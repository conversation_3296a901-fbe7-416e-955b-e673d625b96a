import { Component, Input, OnInit, OnDestroy, Output, EventEmitter, ViewChild, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormsModule, FormArray, FormControl } from '@angular/forms';
import { Subscription, catchError, finalize, of } from 'rxjs';

import { TableComponent } from 'src/app/shared/components/table/table.component';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { ProgressBarModel } from 'src/app/shared/models/configuration-form/progress-bar';

import { FieldModelCreate, TypeField, CatalogCreate, CatalogUpdate } from 'src/app/shared/models/form/form.model';
import { CatalogModel } from 'src/app/shared/models/catalog-setting/catalog.model';
import { TabModuleModel } from 'src/app/shared/models/configuration-form/tab-module';
import { SectionListTableModel } from 'src/app/shared/models/configuration-form/sections';

import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { DpDatePickerModule, IDatePickerDirectiveConfig } from 'ng2-date-picker';

import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';

import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { CalculationFormulasComponent } from 'src/app/shared/components/calculation-formulas/calculation-formulas.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';

import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { TaskTayConfiCreateModel } from 'src/app/shared/models/task-tray-config';
import { ChecksService } from 'src/app/shared/services/checks/checks.service';
import { DataService } from 'src/app/shared/services/data/data.service';
import { ConfigurationModel, HomologationModel } from 'src/app/shared/models/OCR';
import { SelectModel } from 'src/app/shared/models/select';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';

@Component({
  selector: 'app-fields-form',
  standalone: true,
  imports: [CommonModule,
    TableComponent,
    Modal2Component,
    MatFormFieldModule,
    ReactiveFormsModule,
    TranslateModule,
    FormsModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatIconModule,
    MatButtonModule,
    MatSlideToggleModule,
    MatRadioModule,
    MatTooltipModule,
    MatTabsModule,
    MatCardModule,
    MatDatepickerModule,
    MatNativeDateModule,
    CalculationFormulasComponent,
    DpDatePickerModule,
    ValidationInputFileDirective
  ],
  templateUrl: './fields-form.component.html',
  styleUrls: ['./fields-form.component.scss'],
})
export class FieldsFormComponent implements OnInit, OnDestroy {

  private _settingProductSubscription?: Subscription;
  private fieldInheritedSubscription: Subscription | null = null;
  fieldSubs?: Subscription;
  transactionSubs?: Subscription;
  _settingCountryAndCompanySubscription?: Subscription;

  catalogTable: CatalogModel[] = [];
  tabList: TabModuleModel[] = [];
  dataTableSections: SectionListTableModel[] = [];
  progressBarList: ProgressBarModel[] = [];

  dataTableFields: any[] = [];
  fieldTypes: any[] = [];
  fieldGroupers: any[] = [];
  nameColumnsCustomerTable: any[] = [];
  nameColumnsPolicyTable: any[] = [];
  catalogFields: any[] = [];
  allFields: any[] = [];
  catalogs: any[] = [];
  maxTextAreaLength: number = 2000;
  maxHelpTextLength: number = 100;
  maxTextOnlyLength: number = 50;
  allStages: any[] = [];
  allStates: any[] = [];
  allOriginalField: any[] = [];
  formModuleByState: any[] = [];
  maxSizeInMb = 20;
  fileName: string = '';
  isDragOver = false;
  showBtnDelete: boolean = false;

  @Input() idForm: number = 0;
  @Input() LoginClient: boolean = false;



  isEditingField: boolean = false;
  orderList: number[] = []
  idBusinessCountry: number = 0;
  idCountry: number = 0;
  idProduct: number = 0;
  idProcess: number = 0;
  isPosibleSearch: boolean = true;

  typeFormatDate: any = [{ format: "YYYY-MM-DD" }, { format: "DD-MM-YYYY" }, { format: "MM-DD-YYYY" }]
  typeFormatCoin: any = [{ format: "Pesos colombianos" }, { format: "Pesos mexicanos" }, { format: "Peso argentino" }, { format: "Dolar" }, { format: "Pesos chilenos" }, { format: "Colón costarricense" }, { format: "Córdoba de nicaragua" }, { format: "Real de Brasil" }, { format: "Guarani de paraguay " }, { format: "Lempira hondureño" }, { format: "Quetzal de guatemala" }]
  fileTypes: any[] = [];
  typeDateMin: any[] = [];
  typeDateMinFilter: any[] = [];
  typeDateMaxFilter: any[] = [];



  @ViewChild('editNewFieldModal') editNewFieldModal?: TemplateRef<any>;
  @ViewChild('typeUpload') typeUpload?: TemplateRef<any>;
  currentModal: MatDialogRef<any>[] = [];

  idFieldModule: number = 0;
  modeDatePicker: any = 'day';
  clockTypeIcon: boolean = false;
  config: any = {};
  placeholderText: string = '';
  standarField: any[] = [];

  formField: FormGroup = this._fb.group({
    pkIIdFieldModule: 0,
    fkIIdFormModule: [],
    fkIIdProgressBar: [],
    fkIIdTabModule: [],
    fkIIdSectionModule: [],
    iOrder: [null, [Validators.required]],
    bActive: [true],
    vNameField: [null, [Validators.required]],
    fkIIdFieldType: [null, Validators.required],
    vNameFieldDb: [null],
    vDescription: [null, Validators.required],
    bRequired: [false],
    bAllowMultipleUploads: [false],
    bShowYear: [true],
    bShowMonth: [true],
    bShowDay: [true],
    bVisibleToConsultation: [false],
    bShowHour: [false],
    bShowCoin: [false],
    bIsReadonly: [false],
    bisKeyField: [false],
    bIsPolicy: [false],
    bIsTable: [false],
    bIsPerson: [false],
    bIsSearch: [false],
    bIsGrouper: [false],
    bIsUsingJson: [false],
    iMinLength: [null],
    iMaxLength: [null],
    vHelpText: [null],
    vFormat: [null],
    options: [null],
    optionValues: this._fb.array([]),
    bHelpText: [false],
    bIsDependent: [false],
    fkIIdParent: [0],
    selectedFileTypes: [],
    iOptionDependent: [0],
    vEquivalentField: [null],
    fkIGrouperField: [null],
    fkIIdChildrenDependent: [[]],
    iSearchType: [null],
    fkIIdCatalog: [null],
    fkIIdFieldCatalog: [],
    fkIdFieldExistent: [],
    fkIdstageInherited: [],
    fkIdStateInherited: [],
    fkFieldOrigin: [],
    bUsingExistent: [],
    bConfigureWithOCRField:[false],
    readingOCR:[null],
    baseField:[null],
    rowsRequest: this._fb.array([]),
    rowsResponse: this._fb.array([]),
    iTypeRequest: [],
    vEndpoint: [null],
    bIsEmail: [false],
    bIsMaxDateRequerid: [false],
    bIsMinDateRequerid: [false],
    bIsValueMax: [false],
    bIsValueMin: [false],
    dMaxDateRequerid: [null],
    dMinDateRequerid: [null],
    vValueMax: [null],
    vValueMin: [null],
    vTypeDateMin: [null],
    vTypeDateMax: [null],
    vSchemaJson: [null],
    bIsHasDefaultValue: [false],
    vSetDefaultValue: [null],
    iTypeResponseList: [null],
    vNameResponseList: [null],
    bIsInheritedField: [null],
    fkIdFieldInherited: 0,
    bisCheckField: [false],
    bIsExternalLink: [null],
    iLink: [null],
    vExternalLink: [null],
    vExternalPDF: [null],
    vFileName: [null],
    OptionsChecks: [[]],
    bisKeyFieldOCR:[false],
    sendDocumentForOCRReading:[false],
    fkOCRReadingSentDoc:[null],
    keyFieldCreatePolicy:[false],
    bIsInvisible: [false],
    iElementHideField: [],
    bIsGetdate: [false],
    bIsMultiple:[false]
  });

  estructTableFields: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('FormConfiguration.Sections.Order'),
      columnValue: 'iOrder'
    },
    {
      columnLabel: this._translateService.instant('Product.FieldId'),
      columnValue: 'pkIIdFieldModule'
    },
    {
      columnLabel: this._translateService.instant('Product.FieldName'),
      columnValue: 'vNameField'
    },
    {
      columnLabel: this._translateService.instant('Product.FieldType'),
      columnValue: 'fkIIdFieldType',
      functionValue: (item: any) => this.getFieldTypeNameById(item.fkIIdFieldType)
    },
    {
      columnLabel: this._translateService.instant('FormConfiguration.Fields.Section'),
      columnValue: 'sectionName',
    },
    {
      columnLabel: this._translateService.instant('FormConfiguration.Sections.Tab'),
      columnValue: 'tabName',
    },
    {
      columnLabel: this._translateService.instant('FormConfiguration.ProgressBar.Title'),
      columnValue: 'progressName',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  //configuración campos login
  estructTableFieldsLogin: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configLogin.orden'),
      columnValue: 'iOrder'
    },
    {
      columnLabel: this._translateService.instant('configLogin.idcampo'),
      columnValue: 'pkIIdFieldModule'
    },
    {
      columnLabel: this._translateService.instant('configLogin.nameField'),
      columnValue: 'vNameField'
    },
    {
      columnLabel: this._translateService.instant('configLogin.tipocampo'),
      columnValue: 'fkIIdFieldType',
      functionValue: (item: any) => this.getFieldTypeNameById(item.fkIIdFieldType)
    },
    {
      columnLabel: this._translateService.instant('configLogin.obligatorio'),
      columnValue: 'bRequired',
      functionValue: (item: any) => this.utilsSvc.changeRequired(item),

    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  fieldKey:boolean = false;
  optionsChecks:any[]=[];

  //Variables relacionadas con OCR.
  readingsData: ConfigurationModel[] = [];
  homologationData: Partial<HomologationModel>[] = [];
  fielkeyOCRList: SelectModel[]=[]
  keyFieldOCR = 0;

  constructor(
    private _moduleService: ModuleService,
    private _transactionService: TransactionService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public _fieldSvc: FieldService,
    public utilsSvc: UtilsService,
    public modalDialog: MatDialog,
    private _catalogService: CatalogSettingService,
    private _settingService: SettingService,
    private _parametersService: ParametersService,
    private _fb: FormBuilder,
    private _ModuleSettingsService: ModulesSettingService,
    private _taskTayConfigService: TaskTayConfigService,
    private _checkService: ChecksService,
    private _cdr: ChangeDetectorRef,
    private dataService: DataService


  ) { }

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this.getDataFilterForModuleSubscription();
    this.getParameters();
    this.getParametersStandard();
    this.getColumnNamesPolicy();

    this.getTypeFileList();
    
    if (this.idForm !== 0) {
      this.getFieldType();
      this.getFieldsByForm(this.idForm);
      this.formField.get('fkIIdProgressBar')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getFormTabModulesListByIdProgress(value);
        }
      });

      this.formField.get('fkIIdCatalog')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getCatalogFieldByCatalogId();
        }
      });

      this.formField.get('fkIIdTabModule')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getSectionListByIdTab(value);
        }
      });

      this.formField.get('vNameField')?.valueChanges.subscribe((value) => {
        if (value != undefined) {
          const cleanedValue = this.utilsSvc.generateNameDb(value);
          this.formField.get('vNameFieldDb')?.setValue(cleanedValue);
        }
      });

      this.formField.get('bIsPerson')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getFieldsGrouperByForm(this.idForm, true);
          this.togglePersonValidators(value);
        }
      });
      this.formField.get('bIsPolicy')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getFieldsGrouperByForm(this.idForm, false);
        }
      });

      this.formField.get('bIsGrouper')?.valueChanges.subscribe((value) => {
        if (value == true || value === null) {
          this.formField.get('fkIGrouperField')?.clearValidators();
          this.formField.get('fkIGrouperField')?.updateValueAndValidity();
        }
        else if (value == false) {
          this.formField.get('fkIGrouperField')?.setValidators([Validators.required]);
          this.formField.get('fkIGrouperField')?.updateValueAndValidity();
        }
      });

      this.formField.get('fkIdstageInherited')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getAllStatesByStages(value);
        }
      });
      this.formField.get('fkIdStateInherited')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.GetFormModuleByState(value);
        }
      });
      this.initializeFieldInheritedWatcher();
      this.formField.get('fkIdFieldExistent')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getFieldById(value, true);
        }
      });
      this.formField.get('fkIIdParent')?.valueChanges.subscribe((value) => {
        if (value != null) {
          if (this.formField.get('bIsDependent')?.value === false) {
            this.formField.get('iOptionDependent')?.setValue(null);
            this.formField.get('iOptionDependent')?.clearValidators();
            this.formField.get('iOptionDependent')?.updateValueAndValidity();
          }

          if (this.formField.get('bIsDependent')?.value)
            this.getFieldById(value, false);
        }
      });
      this.formField.get('bIsSearch')!.valueChanges.subscribe(value => {
        this.toggleSearchValidators(value);
      });
      this.formField.get('iSearchType')!.valueChanges.subscribe(value => {
        if (this.formField.get('bIsSearch')!.value) {
          this.updateValidatorsBasedOnSearchType(value);
        }
      });

      this.formField.get('fkIIdFieldType')?.valueChanges.subscribe((value) => {
        if (value != null) {
          if (value !== this.typeField.Alphanumeric) {
            this.formField.get('bIsEmail')?.setValue(null);
          }
          if(value != this.typeField.UploadDocuments){
            this.formField.get('sendDocumentForOCRReading')?.setValue(null);
            this.formField.get('fkOCRReadingSentDoc')?.setValue(null);
            this.formField.get('keyFieldCreatePolicy')?.clearValidators();
            this.formField.get('fkOCRReadingSentDoc')?.clearValidators();
            this.formField.get('keyFieldCreatePolicy')?.updateValueAndValidity();
            this.formField.get('fkOCRReadingSentDoc')?.updateValueAndValidity();
          }
        }
      });

      this.formField.get('bIsEmail')?.valueChanges.subscribe((value) => {
        if (value != null) {
          if (value) {
            this.formField.get('bIsValueMax')?.disable();
            this.formField.get('bIsValueMax')?.setValue(false);

            this.formField.get('bIsValueMin')?.disable();
            this.formField.get('bIsValueMin')?.setValue(false);
          } else {
            this.formField.get('bIsValueMax')?.enable();
            this.formField.get('vValueMax')?.enable();

            this.formField.get('bIsValueMin')?.enable();
            this.formField.get('vValueMin')?.enable();
          }
        }
      });

      this.formField.get('bIsValueMax')?.valueChanges.subscribe(value => {
        if (value) {
          this.formField.get('vValueMax')?.enable();
          this.formField.get('vValueMax')?.setValidators(Validators.required);
          this.formField.get('vValueMax')?.updateValueAndValidity();
        } else {
          this.formField.get('vValueMax')?.disable();
          this.formField.get('vValueMax')?.setValue('');
          this.formField.get('vValueMax')?.setValidators(null);
          this.formField.get('vValueMax')?.updateValueAndValidity();
        }

      });

      this.formField.get('bIsValueMin')?.valueChanges.subscribe(value => {
        if (value) {
          this.formField.get('vValueMin')?.enable();
          this.formField.get('vValueMin')?.setValidators(Validators.required);
          this.formField.get('vValueMin')?.updateValueAndValidity();
        } else {
          this.formField.get('vValueMin')?.disable();
          this.formField.get('vValueMin')?.setValue('');
          this.formField.get('vValueMin')?.setValidators(null);
          this.formField.get('vValueMin')?.updateValueAndValidity();
        }
      });

      this.formField.get('bIsMinDateRequerid')?.valueChanges.subscribe(value => {
        if (value) {
          this.formField.get('dMinDateRequerid')?.enable();
          this.formField.get('dMinDateRequerid')?.setValidators(Validators.required);
          this.formField.get('dMinDateRequerid')?.updateValueAndValidity();

          this.formField.get('vTypeDateMin')?.enable();
        } else {
          this.formField.get('vTypeDateMin')?.disable();
          this.formField.get('vTypeDateMin')?.setValue(0);
          this.typeDateMaxFilter = this.typeDateMin;
          this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );
        }
      });

      this.formField.get('bIsMaxDateRequerid')?.valueChanges.subscribe(value => {
        if (value) {
          this.formField.get('dMaxDateRequerid')?.enable();
          this.formField.get('dMaxDateRequerid')?.setValidators(Validators.required);
          this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();

          this.formField.get('vTypeDateMax')?.enable();
        } else {
          this.formField.get('vTypeDateMax')?.disable();
          this.formField.get('vTypeDateMax')?.setValue(0);
          this.formField.get('dMaxDateRequerid')?.disable();
          this.formField.get('dMaxDateRequerid')?.setValue('');
          this.formField.get('dMaxDateRequerid')?.setValidators(null);
          this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();
        }
      });


      this.formField.get('bShowYear')?.valueChanges.subscribe((value) => {
        this.cleanFieldDate();
        if (value) {
          this.validationTypeDate(
            true,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );
        } else {
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );

        }
      });

      this.formField.get('bShowMonth')?.valueChanges.subscribe((value) => {
        this.cleanFieldDate();
        if (value) {
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            true,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );
        } else {
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );

        }
      });

      this.formField.get('bShowDay')?.valueChanges.subscribe((value) => {
        this.cleanFieldDate();
        if (value) {
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            true,
            this.formField.get('bShowHour')?.value
          );
        } else {
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );
        }
      });

      this.formField.get('bShowHour')?.valueChanges.subscribe((value) => {
        this.cleanFieldDate();
        if (value) {
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            true
          );
        } else {
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );
        }
      });

      this.formField.get('dMaxDateRequerid')?.valueChanges.subscribe((value) => {
        if (value != null) this.isDateInvalid(false);
      });

      this.formField.get('dMinDateRequerid')?.valueChanges.subscribe((value) => {
        if (value != null) this.isDateInvalid(true);
      });



      this.formField.get('bIsDependent')!.valueChanges.subscribe(value => {
        if (value === false) {
          this.formField.get('fkIIdParent')?.setValue(0);
          this.formField.get('fkIIdParent')?.clearValidators();
          this.formField.get('fkIIdParent')?.updateValueAndValidity();

          this.formField.get('iOptionDependent')?.setValue(null);
          this.formField.get('iOptionDependent')?.updateValueAndValidity();

        }
      });

      this.formField.get('fkIIdFieldType')?.valueChanges.subscribe((value) => {
        if (value != null) {
          if (
            value !== this.typeField.Alphanumeric &&
            value !== this.typeField.Numeric &&
            value !== this.typeField.Text &&
            value !== this.typeField.Date &&
            value !== this.typeField.DropDownList
          ) {
            this.formField.get('bIsSearch')?.setValue(false);
          }
          if (value === this.typeField.DropDownList) {
            this.formField.get('fkIIdCatalog')?.setValidators([Validators.required]);
            this.formField.get('fkIIdFieldCatalog')?.setValidators([Validators.required]);
            this.formField.get('fkIIdCatalog')?.updateValueAndValidity();
            this.formField.get('fkIIdFieldCatalog')?.updateValueAndValidity();
          }
          else {
            this.formField.get('fkIIdCatalog')?.clearValidators();
            this.formField.get('fkIIdFieldCatalog')?.clearValidators();
            this.formField.get('fkIIdCatalog')?.updateValueAndValidity();
            this.formField.get('fkIIdFieldCatalog')?.updateValueAndValidity();
          }
        }
      });

      this.formField.get('bConfigureWithOCRField')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getConfigurationOCR();
          this.formField.get('readingOCR')?.setValidators([Validators.required]);
          this.formField.get('baseField')?.setValidators([Validators.required]);
          this.formField.get('readingOCR')?.updateValueAndValidity();
          this.formField.get('baseField')?.updateValueAndValidity();
        }
        if(!value){
          this.homologationData = [];
          this.formField.get('readingOCR')?.setValue(null);
          this.formField.get('baseField')?.setValue(null);
          this.formField.get('readingOCR')?.clearValidators();
          this.formField.get('baseField')?.clearValidators();
          this.formField.get('readingOCR')?.updateValueAndValidity();
          this.formField.get('baseField')?.updateValueAndValidity();
        }
      });

      this.formField.get('readingOCR')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.homologationData = [];
          this.formField.get('baseField')?.setValue(null);
          this.getConfigurationOCRById(value);
        }
      });

      this.formField.get('sendDocumentForOCRReading')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getKeyFieldOCR(this.idForm);
          this.getConfigurationOCR();
          // this.formField.get('keyFieldCreatePolicy')?.setValidators([Validators.required]);
          this.formField.get('fkOCRReadingSentDoc')?.setValidators([Validators.required]);
          // this.formField.get('keyFieldCreatePolicy')?.updateValueAndValidity();
          this.formField.get('fkOCRReadingSentDoc')?.updateValueAndValidity();
        }
        if(!value){
          // this.formField.get('keyFieldCreatePolicy')?.setValue(null);
          this.formField.get('fkOCRReadingSentDoc')?.setValue(null);
          // this.formField.get('keyFieldCreatePolicy')?.clearValidators();
          this.formField.get('fkOCRReadingSentDoc')?.clearValidators();
          // this.formField.get('keyFieldCreatePolicy')?.updateValueAndValidity();
          this.formField.get('fkOCRReadingSentDoc')?.updateValueAndValidity();
        }
      });



    }


    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table modules
      this.estructTableFieldsLogin[0].columnLabel = this._translateService.instant(
        'configLogin.orden'
      );
      this.estructTableFieldsLogin[1].columnLabel = this._translateService.instant(
        'configLogin.idcampo'
      );

      this.estructTableFieldsLogin[2].columnLabel = this._translateService.instant(
        'configLogin.nameField'
      );

      this.estructTableFieldsLogin[3].columnLabel = this._translateService.instant(
        'configLogin.tipocampo'
      );

      this.estructTableFieldsLogin[4].columnLabel = this._translateService.instant(
        'configLogin.obligatorio'
      );

      this.estructTableFieldsLogin[5].columnLabel =
        this._translateService.instant('Status');

      this.estructTableFieldsLogin[6].columnLabel =
        this._translateService.instant('Action');
    });

  }

  initializeFieldInheritedWatcher() {
    this.fieldInheritedSubscription = this.formField.get('fkIdFieldInherited')?.valueChanges.subscribe((value) => {
      if (value) {
        if (this.isPosibleSearch) {


          this.formField.get('fkIIdFieldType')?.disable();
          this.formField.get('vNameField')?.disable();
          this.formField.get('vNameFieldDb')?.disable();

          if (!this.isEditingField)
            this.getFieldById(value, true);
        }
      }
    }) || null; // Si subscribe retorna undefined, asignamos null
  }

  ngOnDestroy() {
    this.fieldSubs?.unsubscribe();
    this.transactionSubs?.unsubscribe();
  }

  get valid(): boolean {
    return this.formField.valid;
  }

  public get typeField(): typeof TypeField {
    return TypeField;
  }
  get orderItems() {
    return this.formField.controls['optionValues'] as FormArray;
  }


  getDataFilterForModuleSubscription() {
    this._settingProductSubscription =
      this._ModuleSettingsService.currentFilterFormModulesSetting.subscribe(
        (response) => {

          if (!(Object.keys(response).length === 0)) {

            this.idProduct = response.idProductModule;
            this.idProcess = response?.idProcess || 0;


          }
        }
      );
  }


  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.idCountry = response.enterprise.pkIIdCountry;
            this.Checks();
          }
        }
      );
  }

  updateOptionValue(index: number, value: any) {
    const option = this.formField.get('optionValues')?.get(index.toString()) as FormGroup;
    option.patchValue({
      Id: index,
      Value: value.value
    });

    // Obtener el campo de entrada correspondiente por su índice
    const inputField = document.getElementById(`optionInput-${index}`) as HTMLInputElement;

    // Asignar el valor actualizado al campo de entrada
    if (inputField) {
      inputField.value = option.value.Value;
    }
  }

  EditField(isEditing: boolean, evt?: IconEventClickModel) {

    this.open('editNewFieldModal');
    this.getAllFields();
    this.getAllStages();
    this.getListCatalog(this.idBusinessCountry, this.idCountry);
    this.getCustomerColumnNames();
    this.getColumnNamesPolicy();
    this.getProgressBarModuleByIdFormModule(this.idForm);
    if (isEditing) {
      this.formField.get('fkIIdFieldType')?.disable();
      this.formField.get('vNameField')?.disable();
      this.formField.get('vDescription')?.disable();
      this.setOrder(true);
      this.getFieldById(evt?.value.pkIIdFieldModule, true);
      this.idFieldModule = evt?.value.pkIIdFieldModule;
    }

    this.formField.get('bIsInheritedField')?.enable();
    this.formField.get('fkIdstageInherited')?.enable();
    this.formField.get('fkIdStateInherited')?.enable();
    this.formField.get('fkIdFieldInherited')?.enable();
    this.isEditingField = isEditing;
  }

  setOrder(edit: boolean) {
    if (edit) {
      this.orderList = this.utilsSvc.generarArrayOrderList(
        this.dataTableFields.length
      );
    }
    else {
      this.orderList = this.utilsSvc.generarArrayOrderList(
        this.dataTableFields.length + 1
      );
    }
  }

  getFieldById(idField: number, bIsForPatching: boolean) {
    var tempfkIdstageInherited: number;
    var tempfkIdStateInherited: number;
    var tempfkIdfkIdFieldInherited: number;
    if (this.formField.get('bIsInheritedField')?.value) {
      tempfkIdstageInherited = this.formField.get('fkIdstageInherited')?.value
      tempfkIdfkIdFieldInherited = this.formField.get('fkIdFieldInherited')?.value
      tempfkIdStateInherited = this.formField.get('fkIdStateInherited')?.value
    }

    this.fieldSubs = this._moduleService.getFieldById(idField).pipe(
      catchError((error) => {
        this._msgSvc.messageWaring(this._translateService.instant('Warning'), error.error.message)
        return of([]);
      }),
      finalize(() => {
        //Reasigna valores guardados en memoria sobreescritos cuando se consulta el campo
        if (tempfkIdstageInherited > 0 && tempfkIdStateInherited > 0 && tempfkIdStateInherited > 0) {
            // Desuscribirse temporalmente del valueChanges
            if (!this.isEditingField) {
            this.isPosibleSearch = false;
            this.formField.get('fkIdstageInherited')?.setValue(tempfkIdstageInherited)
            this.formField.get('fkIdStateInherited')?.setValue(tempfkIdStateInherited)
            this.formField.get('fkIdFieldInherited')?.setValue(tempfkIdfkIdFieldInherited)
            this.formField.get('bIsInheritedField')?.setValue(true)
            this.formField.get('fkIIdSectionModule')?.setValue(null);
            this.formField.get('fkIIdProgressBar')?.setValue(null);
            this.formField.get('fkIIdTabModule')?.setValue(null);
            setTimeout(() => { this.isPosibleSearch = true; }, 1000)
          }
        }



      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          var field = resp.result;
          this.fieldKey = resp?.result?.bisKeyField || false;
          if (bIsForPatching) {
            if (field.fkIIdChildrenDependent != null) {
              field.fkIIdChildrenDependent = JSON.parse(field.fkIIdChildrenDependent);
            }
            if (field.fkIIdFieldType === this.typeField.Radio) {
              this.getCatalogById(field, true);

            }
            if (field.rowsRequest != null || field.rowsResponse != null) {
              const rowsRequest = this.formField.get(
                'rowsRequest'
              ) as FormArray;
              const rowsResponse = this.formField.get(
                'rowsResponse'
              ) as FormArray;
              rowsRequest.clear();
              rowsResponse.clear();
              field.rowsRequest.forEach((value: any) => {
                rowsRequest.push(
                  this._fb.group(value)
                );
              })
              field.rowsResponse.forEach((value: any) => {
                rowsResponse.push(
                  this._fb.group(value)
                );
              })

              this.formField.patchValue(field);

              this.formField.setControl('rowsRequest', rowsRequest);
              this.formField.setControl('rowsResponse', rowsResponse);
            }
            else {
              this.formField.patchValue(field);

              if (this.formField.get('fkIIdFieldType')?.value == this.typeField.UploadDocuments) {
                this.formField.get('selectedFileTypes')?.setValue(this.formField.get('vFormat')?.value.replace(' ', '').split(','))
              }

            }


            if (field.bIsInheritedField == false || field.bIsInheritedField == null) {
              this.formField.get('bIsInheritedField')?.disable();
            } else {
              this.formField.get('bIsInheritedField')?.disable();
              this.formField.get('fkIdstageInherited')?.disable();
              this.formField.get('fkIdStateInherited')?.disable();
              this.formField.get('fkIdFieldInherited')?.disable();

            }
          }
          else {
            //When it's not neccesary patch the info just for consulting.
            if (field.fkIIdFieldType == this.typeField.Radio) {
              this.getCatalogById(field, false);
            }
            if (field.fkIIdFieldType == this.typeField.DropDownList) {
              this.getCatalogByIdForDropdown(field)
            }

          }
          if (field.bisCheckField) {
            const selectedValues = field.optionsChecksList.split(',').map((val: string) => parseInt(val.trim(), 10));
            this.formField.get('OptionsChecks')?.setValue(selectedValues);
          }

        }
      }
    });
  }

  open(component: string) {

    this.formField.get('fkIIdFieldType')?.enable();
    this.formField.get('vNameField')?.enable();
    this.formField.get('vNameFieldDb')?.enable();
    var sizeConfiguration = {
      disableClose: false,
      width: '70vw',
      maxHeight: '90vh'
    }
    var modal: TemplateRef<any>;
    switch (component) {
      case "editNewFieldModal":
        this.formField.reset();
        this.isEditingField = false;
        this.formField.patchValue({ bActive: true, selectedFileTypes: [] });
        modal = this.editNewFieldModal!;
        break;
      case "typeUpload":
        modal = this.typeUpload!;
        sizeConfiguration.width = '60vh'
        break;
      default:
        return;
    }

    // Abre el modal y guarda su referencia en la propiedad currentModal
    const modalRef = this.modalDialog.open(modal, sizeConfiguration);
    this.currentModal.push(modalRef);
  }

  deleteField() {
    this._msgSvc.messageConfirmationAndNegation(this._translateService.instant('FormConfiguration.ProgressBar.DeleteMessageConfirm'), '', 'info', this._translateService.instant('Confirm'), this._translateService.instant('Cancel'))
      .then((response) => {
        if (response) {
          this.fieldSubs = this._moduleService
            .deleteField(this.formField.get('pkIIdFieldModule')?.value)
            .pipe(
              catchError((error) => {
                if (
                  error.error.error &&
                  error.error.message === 'hasAssociatedItems'
                ) {
                  this._msgSvc.messageInfo(
                    this._translateService.instant(
                      'FormConfiguration.deletedMessageTitle'
                    ),
                    this._translateService.instant(
                      'FormConfiguration.deletedMessageSubtitle'
                    )
                  );
                } else {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('Warning'),
                    error.error.message
                  );
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
              } else {
                if (resp.error) {
                  this._msgSvc.messageError(
                    this._translateService.instant('ThereWasAError') +
                    resp.message
                  );
                } else {
                  this._msgSvc.messageSuccess(
                    this._translateService.instant(
                      'FormConfiguration.ProgressBar.DeleteMessageSuccess'
                    ),
                    ''
                  );
                  this.getFieldsByForm(this.idForm);
                }
              }
            });
        }
      })

  }

  async saveField() {
    var idCatalog: number = 0
    var caller;
    let validOcr = false;
    if(this.formField.get('sendDocumentForOCRReading')?.value){
      if(this.keyFieldOCR != 0){
        validOcr = true;
      } else {
        validOcr = false;
      }
    } else{
      validOcr = true;
    }
    //Create a field by form
    if (this.valid && validOcr) {
      this.formField.get('fkIIdFormModule')?.setValue(this.idForm);



      var field: FieldModelCreate = this.formField.getRawValue();
      if (this.formField.get('fkIIdChildrenDependent')?.value != null) {
        field.fkIIdChildrenDependent = JSON.stringify(field.fkIIdChildrenDependent);
      }
      if (this.isEditingField) {
        caller = this._moduleService.updateField(field)
        if (this.formField.get('fkIIdFieldType')?.value === TypeField.Radio) {
          await this.updateCatalog(this.formField.get('fkIIdCatalog')?.value);
        }

      } else {
        if (this.LoginClient) {
          field.bLoginClientPortal = this.LoginClient
          field.fkIIdBusinessCountry = this.idBusinessCountry
        }
        caller = this._moduleService.createField(field)
        if (this.formField.get('fkIIdFieldType')?.value === TypeField.Radio) {
          idCatalog = await this.saveCatalog();
          if (idCatalog === 0) {
            return;
          }
          field.fkIIdCatalog = idCatalog;
        }
      }
      this.fieldSubs = caller.pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(this._translateService.instant('Warning'), error.error.message)
          return of([]);
        })
      ).subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.validationTabRulesField(resp.result);


            // ---------------- validar campo llave

            if (resp.result != null && field.bisKeyField && this.fieldKey != true) {

              let payload: TaskTayConfiCreateModel = {
                pkIIdTaskTrayConfig: 0,
                vFieldName: field.vNameField,
                fkIIdField: resp.result,
                iOrder: 1,
                bActive: true,
                bIsFilter: false,
                bIsStandard: false,
                fkIIdBusinessByCountry: this.idBusinessCountry,
                fkIIdProduct: this.idProduct,
                bIsQuote: true,
                fkIIdProcess: this.idProcess
              };

              this._taskTayConfigService
                .createTaskTrayConfig(payload)
                .pipe(
                  catchError((error) => {
                    if (error.error.error) {
                      this._msgSvc.messageError(
                        this._translateService.instant('ThereWasAError') + resp.message
                      );
                    }
                    return of([]);
                  })
                )
                .subscribe((resp: ResponseGlobalModel | never[]) => {
                  if (Array.isArray(resp)) {
                  } else {
                  }
                });

            } else if (resp.result != null && field.bisKeyField == false && this.fieldKey == true) {
              // desactivar/actualizar campo llave

              let payload: TaskTayConfiCreateModel = {
                pkIIdTaskTrayConfig: 0,
                vFieldName: field.vNameField,
                fkIIdField: resp.result,
                iOrder: 1,
                bActive: false,
                bIsFilter: false,
                bIsStandard: false,
                fkIIdBusinessByCountry: this.idBusinessCountry,
                fkIIdProduct: this.idProduct,
                bIsQuote: true,
                fkIIdProcess: this.idProcess
              };

              this._taskTayConfigService
                .updateTaskTrayConfigField(payload)
                .pipe(
                  catchError((error) => {
                    if (error.error.error) {
                      this._msgSvc.messageError(
                        this._translateService.instant('ThereWasAError') + resp.message
                      );
                    }
                    return of([]);
                  })
                )
                .subscribe((resp: ResponseGlobalModel | never[]) => {
                  if (Array.isArray(resp)) {
                  } else {
                  }
                });
            }


          }
        }
      });
    } else {
      this.formField.markAllAsTouched();
    }
  }

  async updateCatalog(idCatalog: number): Promise<number> {
    try {
      const catalog: CatalogUpdate = {
        vJson: JSON.stringify(this.formField.get('optionValues')?.value)
      };

      const resp: ResponseGlobalModel | undefined = await this._fieldSvc.updateCatalog(catalog, idCatalog).toPromise();

      if (!resp || resp.error) {
        this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + ": " + (resp?.message || this._translateService.instant('ThereWasAError')));
        return 0;
      }

      return resp.result;
    } catch (error) {
      return 0;
    }
  }

  async saveCatalog(): Promise<number> {
    try {
      const catalog: CatalogCreate = {
        vDescription: this.formField.get('vDescription')?.value,
        vJson: JSON.stringify(this.formField.get('optionValues')?.value)
      };

      const resp: ResponseGlobalModel | undefined = await this._fieldSvc.createCatalog(catalog).toPromise();

      if (!resp || resp.error) {
        this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + ": " + (resp?.message || this._translateService.instant('ThereWasAError')));
        return 0;
      }

      return resp.result;
    } catch (error) {
      console.error("Ocurrió un error:", error);
      return 0;
    }
  }

  getFieldsByForm(idForm: number) {
    this.fieldSubs = this._moduleService.getFieldsByForm(idForm, this.LoginClient, this.idBusinessCountry).pipe(
      catchError((excepception) => {
        this.orderList = this.utilsSvc.generarArrayOrderList(1);
        if (excepception.error.error) {
          this._msgSvc.messageWaring(this._translateService.instant('Warning'), excepception.error.message)
        }
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.dataTableFields = resp.result
          this.dataService.setFields(resp.result);
          if (resp.result.length > 0) {
            this.orderList = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderList = this.utilsSvc.generarArrayOrderList(1);
          }
          this.modalDialog.closeAll();
        }
      }
    });
  }

  getFieldsGrouperByForm(idForm: number, isPerson: boolean) {
    this.fieldSubs = this._moduleService.getFieldsGrouperByFormId(idForm, isPerson).pipe(
      catchError((excepception) => {
        if (excepception.error.error) {
          this._msgSvc.messageWaring(this._translateService.instant('Warning'), excepception.error.message)
        }
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.fieldGroupers = resp.result
        }
      }
    });
  }

  getFieldType() {
    this.fieldSubs = this._fieldSvc.getFieldType().pipe(
      catchError((error) => {
        this._msgSvc.messageWaring(this._translateService.instant('Warning'), error.error.message)
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.fieldTypes = resp.result
        }
      }
    });
  }

  getCustomerColumnNames() {
    this.transactionSubs = this._transactionService.getColumnNamesCustomer().pipe(
      catchError((error) => {
        this._msgSvc.messageWaring(this._translateService.instant('Warning'), error.error.message)
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.nameColumnsCustomerTable = resp.result
        }
      }
    });
  }

  getColumnNamesPolicy() {
    this.transactionSubs = this._transactionService.getColumnNamesPolicy().pipe(
      catchError((error) => {
        this._msgSvc.messageWaring(this._translateService.instant('Warning'), error.error.message)
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.nameColumnsPolicyTable = resp.result
        }
      }
    });
  }

  getTabModuleListByFormId(idFormModule: number) {
    this._moduleService
      .getTabModuleListByFormId(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            if (response.result.length === 0) {
              this.getSectionModuleByFormId(this.idForm);
              this.formField.get('fkIIdTabModule')?.disable();
              this.formField.get('fkIIdTabModule')?.clearValidators();
              this.formField.get('fkIIdTabModule')?.updateValueAndValidity();
            } else {
              this.tabList = response.result;
              this.formField.get('fkIIdTabModule')?.setValidators(Validators.required);
              this.formField.get('fkIIdTabModule')?.updateValueAndValidity();
              this.formField.get('fkIIdSectionModule')?.disable();

            }
          }
        }
      });
  }

  getSectionModuleByFormId(idFormModule: number) {
    this._moduleService
      .getSectionModuleByFormId(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            if (response.result.length === 0) {
              this.formField.get('fkIIdSectionModule')?.disable();
              this.formField.get('fkIIdSectionModule')?.clearValidators();
              this.formField.get('fkIIdSectionModule')?.updateValueAndValidity();
            } else {
              this.dataTableSections = response.result;
              this.formField.get('fkIIdSectionModule')?.setValidators(Validators.required);
              this.formField.get('fkIIdSectionModule')?.updateValueAndValidity();
            }
          }
        }
      });
  }

  getProgressBarModuleByIdFormModule(idFormModule: number) {
    this._moduleService
      .getProgressBarModuleByIdFormModule(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            if (response.result.length > 0) {
              this.progressBarList = response.result;
              this.formField.get('fkIIdProgressBar')?.enable();
              this.formField.get('fkIIdTabModule')?.disable();
              this.formField.get('fkIIdSectionModule')?.disable();
              this.formField.get('fkIIdProgressBar')?.setValidators(Validators.required);
              this.formField.get('fkIIdProgressBar')?.updateValueAndValidity();
            } else {
              this.formField.get('fkIIdProgressBar')?.disable();
              this.getTabModuleListByFormId(this.idForm);
            }
          }
        }
      });
  }

  getFormTabModulesListByIdProgress(idProgressBar: number) {
    this._moduleService
      .getFormTabModulesListByIdProgress(idProgressBar)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            if (response.result.length === 0) {
              this.formField.get('fkIIdTabModule')?.disable();
              this.formField.get('fkIIdTabModule')?.clearValidators();
              this.formField.get('fkIIdTabModule')?.updateValueAndValidity();
            } else {
              this.tabList = response.result;
              this.formField.get('fkIIdTabModule')?.enable();
              this.formField
                .get('fkIIdTabModule')
                ?.setValidators(Validators.required);
              this.formField.get('fkIIdTabModule')?.updateValueAndValidity();
            }
          }
        }
      });
  }

  getSectionListByIdTab(idTab: number) {
    this._moduleService
      .getSectionListByIdTab(idTab)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            if (response.result.length === 0) {
              this.dataTableSections = [];
              this.formField.get('fkIIdSectionModule')?.disable();
              this.formField.get('fkIIdSectionModule')?.clearValidators();
              this.formField.get('fkIIdSectionModule')?.updateValueAndValidity();
            } else {
              this.dataTableSections = response.result;
              this.formField.get('fkIIdSectionModule')?.enable();
              this.formField
                .get('fkIIdSectionModule')
                ?.setValidators(Validators.required);
              this.formField.get('fkIIdSectionModule')?.updateValueAndValidity();
            }
          }
        }
      });
  }

  getFieldTypeNameById(idFieldType: number) {
    var temp = this.fieldTypes.find(x => x.pkIIdFieldType == idFieldType);
    return temp != undefined ? temp.vName : idFieldType;
  }

  isSelected(fileType: string): boolean {
    const selectedFileTypes = this.formField.get('selectedFileTypes')?.value;

    const fileTypesArray = (Array.isArray(selectedFileTypes)
      ? selectedFileTypes
      : selectedFileTypes?.split(',') || []
    ).map((type: any) => type.trim().toLowerCase());

    return fileTypesArray.includes(fileType.trim().toLowerCase());
  }


  toggleFileType(fileType: string) {
    let selectedFileTypes = this.formField.get('selectedFileTypes')?.value;

    selectedFileTypes = (Array.isArray(selectedFileTypes)
        ? selectedFileTypes
        : selectedFileTypes?.split(',') || []
    ).map((type: any) => type.trim().toLowerCase())
     .filter((type: string) => type); // Elimina elementos vacíos

    const index = selectedFileTypes.indexOf(fileType.trim().toLowerCase());

    if (index >= 0) {
        selectedFileTypes.splice(index, 1);
    } else {
        selectedFileTypes.push(fileType.trim().toLowerCase());
    }

    this.formField.patchValue({
        selectedFileTypes: selectedFileTypes.length > 0 ? selectedFileTypes.join(', ') : ''
    });
  }




  saveInfo() {
    const selectedFileTypes = Array.from(
      new Set(
        (typeof this.formField.get('selectedFileTypes')?.value === 'string'
          ? this.formField.get('selectedFileTypes')?.value.split(',')
          : this.formField.get('selectedFileTypes')?.value || []
        ).map((type: string) => type.trim().toUpperCase())
      )
    );

    const selectedFileTypesDisplay = selectedFileTypes.join(', ');

    this.formField.patchValue({
      selectedFileTypes: selectedFileTypesDisplay,
      vFormat: selectedFileTypesDisplay
    });

    this.closeModal();
  }


  closeModal() {
    if (this.currentModal.length > 0) {
      const modalRef = this.currentModal.pop();  // Obtener la referencia del último modal
      if (modalRef) {

        // Cerrar el modal actual
        modalRef.close();
        this.setOrder(false);
      }

      // Si ya no hay más modales en la pila, se limpia el form
      if (this.currentModal.length === 0) {
        this.formField.reset()
      }
    }
  }

  getListCatalog(idBusinessCountry: number, idCountry: number) {
    this._catalogService
      .getAllCatalogByCountryAndBusiness(idBusinessCountry, idCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.catalogTable = response.result;
          }
        }
      });
  }

  getCatalogFieldByCatalogId() {
    this._catalogService
      .getCatalogBasicInfoFieldByCatalogId(this.formField.get('fkIIdCatalog')?.value)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.catalogFields = response.result;
          }
        }
      });
  }

  get isGrouper() {
    return this.formField.get('bIsGrouper')?.value
  }

  getAllFields() {
    this._moduleService
      .getAllFields()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.allFields = response.result;
          }
        }
      });
  }

  getAllStages() {
    this._moduleService
      .getStageByIdProductModule(this.idProduct)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.allStages = response.result;
          }
        }
      });
  }

  getAllStatesByStages(idStage: number) {
    this._moduleService
      .GetAllStatesbyStage(idStage)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.allStates = response.result;
          }
        }
      });
  }

  GetFormModuleByState(idState: number) {

    var idStateF: number[] = [];
    idStateF.push(idState);
    this._moduleService
      .getFormModuleByIdState(idStateF)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else if (response.result == null) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.formModuleByState = response.result;

            this.GetFieldByFormId(response.result.pkIIdFormModule);
          }
        }
      });
  }


  GetFieldByFormId(idForm: number) {

    this._moduleService
      .getFieldsByForm(idForm, this.LoginClient, this.idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else if (response.result == null) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.allOriginalField = response.result;
          }
        }
      });
  }

  updateOptions() {
    const options = this.formField.get('options')?.value;
    const optionValues = this.formField.get('optionValues') as FormArray;

    // Obtener la cantidad actual de opciones
    const currentOptions = optionValues.length;

    if (options > currentOptions) {
      // Agregar nuevas opciones
      for (let i = currentOptions; i < options; i++) {
        optionValues.push(new FormControl(null, Validators.required));
      }
    } else if (options < currentOptions) {
      // Eliminar opciones excedentes
      for (let i = currentOptions - 1; i >= options; i--) {
        optionValues.removeAt(i);
      }
    }
  }

  getCatalogById(field: any, bIsForPatching: boolean) {
    //Catalogs for radio button.
    this.fieldSubs = this._fieldSvc
      .getCatalogById(field.fkIIdCatalog)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {

        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            if (bIsForPatching) {
              const optionValuesFormArray = this.formField.get('optionValues') as FormArray;
              optionValuesFormArray.clear();
              field.optionValues = JSON.parse(resp.result.vJson);
              field.options = field.optionValues.length
              this.formField.patchValue(field);

              field.optionValues.forEach((value: any) => {
                optionValuesFormArray.push(new FormControl(value, Validators.required));
                setTimeout(() => {
                  const inputField = document.getElementById(`optionInput-${value.Id}`) as HTMLInputElement;
                  // Asignar el valor actualizado al campo de entrada
                  if (inputField) {
                    inputField.value = value.Value;
                  }
                });
              });
              this.formField.setControl('optionValues', optionValuesFormArray);
            }
            else {
              this.catalogs = JSON.parse(resp.result.vJson);
            }
          }
        }
      });
  }

  getCatalogByIdForDropdown(field: any) {
    //Catalogs for dropdown.
    this.fieldSubs = this._catalogService
      .getCatalogFieldById(field.fkIIdFieldCatalog)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {

        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.catalogs = resp.result.vJson;
          }
        }
      });
  }

  //Función que obtiene las unidades de medidas.
  getParameters() {
    this._parametersService
      .getParameters('Type_Date')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.typeDateMin = resp;
          this.typeDateMinFilter = resp;
          this.typeDateMaxFilter = resp;
        } else {
        }
      });
  }

  get rowsRequest() {
    return (this.formField.get('rowsRequest') as FormArray).controls;
  }
  get rowsResponse() {
    return (this.formField.get('rowsResponse') as FormArray).controls;
  }

  generateSchemaRow(flag: boolean): FormGroup {
    return this._fb.group({
      fkIIdField: 0,
      vEquivalentField: '',
      bIsForFieldEndpoint: true,
      iTypeConvertion: 1,
      bIsForResponse: flag,
    });
  }

  addRow(key: string) {
    const rowsArray = this.formField.get(key) as FormArray;
    rowsArray.push(this.generateSchemaRow(key === 'rowsRequest' ? false : true));
  }

  deleteRowForTable(key: string, index: number) {
    const rowsArray = this.formField.get(key) as FormArray;
    if (rowsArray) {
      rowsArray.removeAt(index);
    }
  }

  private toggleSearchValidators(isSearch: boolean) {
    const iSearchTypeControl = this.formField.get('iSearchType')!;

    if (isSearch) {
      iSearchTypeControl.setValidators([Validators.required]);
    } else {
      iSearchTypeControl.clearValidators();
      this.clearAllConditionalValidators();
    }

    iSearchTypeControl.updateValueAndValidity();
  }

  private updateValidatorsBasedOnSearchType(searchType: number) {
    const fkIIdChildrenDependentControl = this.formField.get('fkIIdChildrenDependent')!;
    const vEndpointControl = this.formField.get('vEndpoint')!;
    const iTypeRequestControl = this.formField.get('iTypeRequest')!;

    this.clearAllConditionalValidators();

    if (searchType === 2) {
      fkIIdChildrenDependentControl.setValidators([Validators.required]);
    } else if (searchType === 3) {
      vEndpointControl.setValidators([Validators.required]);
      iTypeRequestControl.setValidators([Validators.required]);
    }

    fkIIdChildrenDependentControl.updateValueAndValidity();
    vEndpointControl.updateValueAndValidity();
    iTypeRequestControl.updateValueAndValidity();
  }

  private togglePersonValidators(IsPerson: boolean) {
    const fkIGrouperField = this.formField.get('fkIGrouperField')!;
    const vEquivalentField = this.formField.get('vEquivalentField')!;

    if (IsPerson) {
      fkIGrouperField.setValidators([Validators.required]);
      vEquivalentField.setValidators([Validators.required]);
    } else {
      fkIGrouperField.clearValidators();
      vEquivalentField.clearValidators();
    }

    fkIGrouperField.updateValueAndValidity();
    vEquivalentField.updateValueAndValidity();
  }

  private clearAllConditionalValidators() {
    const fkIIdChildrenDependentControl = this.formField.get('fkIIdChildrenDependent')!;
    const vEndpointControl = this.formField.get('vEndpoint')!;
    const iTypeRequestControl = this.formField.get('iTypeRequest')!;

    fkIIdChildrenDependentControl.clearValidators();
    vEndpointControl.clearValidators();
    iTypeRequestControl.clearValidators();

    fkIIdChildrenDependentControl.setValue(null);
    vEndpointControl.setValue(null);
    iTypeRequestControl.setValue(null);

    fkIIdChildrenDependentControl.updateValueAndValidity();
    vEndpointControl.updateValueAndValidity();
    iTypeRequestControl.updateValueAndValidity();
  }

  onChangeTypeMinDate(event: any) {
    if (!this.isEditingField) {
      this.formField.get('dMinDateRequerid')?.disable();
    }

    this.formField.get('dMinDateRequerid')?.setValue('');
    this.formField.get('dMinDateRequerid')?.disable();
    this.formField.get('dMaxDateRequerid')?.setValue('');
    this.formField.get('dMaxDateRequerid')?.disable();

    const selectedValue = this.formField.get('vTypeDateMin')?.value;
    this.typeDateMaxFilter = this.typeDateMin.filter(
      (item) => item.Name === selectedValue
    );
    this.typeDateMaxFilter = this.typeDateMaxFilter.slice();

    if (event != null && event != '0' && this.formField.get('bIsMinDateRequerid')) {
      this.formField.get('dMinDateRequerid')?.enable();
      this.formateDate(event);
    }
  }

  onChangeTypeMaxDate(event: any) {
    if (!this.isEditingField) {
      this.formField.get('dMaxDateRequerid')?.setValue('');
      this.formField.get('dMaxDateRequerid')?.disable();
    }
    this.filterFieldTypeDateMax();

    if (event != null && event != 0 && this.formField.get('bIsMaxDateRequerid')) {
      this.formField.get('dMaxDateRequerid')?.enable();
      this.formateDate(event);
    }
  }

  isFieldDisabledTabRuleField(): boolean {
    const fkIIdFieldTypeValue = this.formField.get('fkIIdFieldType')?.value;
    const pkIIdFieldModuleValue = this.formField.get('pkIIdFieldModule')?.value;
    return (
      (fkIIdFieldTypeValue === this.typeField.Alphanumeric ||
        fkIIdFieldTypeValue === this.typeField.Numeric ||
        fkIIdFieldTypeValue === this.typeField.Decimal ||
        fkIIdFieldTypeValue === this.typeField.Text ||
        fkIIdFieldTypeValue === this.typeField.Money) &&
      pkIIdFieldModuleValue > 0
    );
  }

  validationTabRulesField(idField: number) {
    this.formField.get('pkIIdFieldModule')?.setValue(idField);
    if (!this.isFieldDisabledTabRuleField()) {
      this.getFieldsByForm(this.idForm);
      this._msgSvc.messageSuccess('', this._translateService.instant('Saved'));
      return;
    }

    if (this.isEditingField) {
      this.getFieldsByForm(this.idForm);
      this._msgSvc.messageSuccess(
        '',
        this._translateService.instant('Saved')
      );
    } else {
      this._msgSvc
        .messageConfirmationAndNegationReverseButton(
          this._translateService.instant(
            '¿Deseas agregar reglas del campo?'
          ),
          '',
          'warning',
          this._translateService.instant('Cancel'),
          this._translateService.instant('Confirm')
        )
        .then((result) => {
          if (!result) {
            this.getFieldsByForm(this.idForm);
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant('Saved')
            );
          } else {
            this.isFieldDisabledTabRuleField();
            this.idFieldModule = idField;
            this.isEditingField = true;
          }
        });
    }
  }

  /// Function where we validate the selected check and thus filter the array for the field
  validationTypeDate(bShowYear: boolean, bShowMonth: boolean, bShowDay: boolean, bShowHour: boolean) {
    let filters: string[] = [];
    this.typeDateMinFilter = [];
    if (bShowYear) filters.push('año');
    if (bShowMonth) filters.push('mes');
    if (bShowDay) filters.push('día');
    if (bShowHour) filters.push('horas');

    /// A filter is made depending on the check or the marked checks.
    this.typeDateMinFilter = this.typeDateMin.filter(item => {
      return filters.some(prop => item.Name.toLowerCase().includes(prop));
    });

    /// It is validated if the filter array has data if it does not leave the list as it was initially
    if (filters.length == 0) {
      this.typeDateMinFilter = this.typeDateMin;
    }

    /// We refresh the field array
    this.typeDateMinFilter = this.typeDateMinFilter.slice();
    this.typeDateMaxFilter = this.typeDateMinFilter;
    this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
  }

  filterFieldTypeDateMax() {
    if (this.formField.get('vTypeDateMin')?.value != null && this.formField.get('vTypeDateMin')?.value != 0) {
      const selectedValue = this.formField.get('vTypeDateMin')?.value;
      this.typeDateMaxFilter = this.typeDateMin.filter(item => item.Name === selectedValue);
      this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
    }
  }

  /// Function formate date
  formateDate(typeDate: string) {
    const vTypeDateMinValue = typeDate;

    this.config = {
      format: '',
      showMultipleYearsNavigation: false,
      multipleYearsNavigateBy: 1,
      showNearMonthDays: true,
    };

    if (
      (!typeDate || typeDate === '')
    ) {
      console.log('No se realiza la validación porque falta vTypeDateMinValue');
      return;
    }

    if (vTypeDateMinValue.includes('año')) {
      this.modeDatePicker = 'day';
      this.config = {
        format: 'YYYY',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false,
      };
      this.placeholderText = 'YYYY';
    }

    if (vTypeDateMinValue.includes('mes')) {
      this.modeDatePicker = 'month';
      this.config = {
        format: 'MM',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += '-MM';
    }

    if (vTypeDateMinValue.includes('día')) {
      this.modeDatePicker = 'day';
      this.config = {
        format: 'DD',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += '-DD';
    }

    if (vTypeDateMinValue.includes('hora')) {
      this.modeDatePicker = 'time';
      this.clockTypeIcon = true;
      this.config = {
        format: 'hh:mm:ss',
        enableMonthSelector: false, //Desahabilita el selecctor de meses.
        secondsInterval: 1,
        showSeconds: true,
      };
      this.placeholderText += 'hh:mm:ss';
    }

    if (vTypeDateMinValue.toLowerCase().includes('fecha')) {
      this.modeDatePicker = 'day';
      this.clockTypeIcon = false;
      this.config = {
        format: 'YYYY-MM-DD',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += 'YYYY-MM-DD';
    }
  }

  cleanFieldDate() {
    this.formField.get('vTypeDateMin')?.setValue(null)
    this.formField.get('dMaxDateRequerid')?.setValue('');
    this.formField.get('dMaxDateRequerid')?.disable();
    this.formField.get('dMaxDateRequerid')?.setValidators(null);
    this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();

    this.formField.get('vTypeDateMax')?.setValue(null);
    this.formField.get('dMinDateRequerid')?.setValue('');
    this.formField.get('dMinDateRequerid')?.disable();
    this.formField.get('dMinDateRequerid')?.setValidators(null);
    this.formField.get('dMinDateRequerid')?.updateValueAndValidity();
  }

  /// Function that performs validation between dates
  isDateInvalid(isDateMin: boolean) {
    let horaMin: number = 0;
    let horaMax: number = 0;
    let isHour: boolean = false;
    const vTypeDateMinValue = this.formField.get('vTypeDateMin')?.value;
    const minDate = this.formField.get('dMinDateRequerid')?.value;
    const maxDate = this.formField.get('dMaxDateRequerid')?.value;

    /// If the type is time, convert to seconds
    if (
      vTypeDateMinValue != null &&
      vTypeDateMinValue != '' &&
      vTypeDateMinValue != 0
    )
      if (vTypeDateMinValue.includes('hora')) {
        horaMin = this.timeToSeconds(minDate);
        horaMax = this.timeToSeconds(maxDate);
        isHour = true;
      }

    /// If the variable isMindate is true, it is valid for the minimum date field, if not, it is valid for the maximum date.
    if (isDateMin) {
      /// If the time variable is true, it validates by hour, if not, it validates by date, month, day, etc...
      if (isHour) {
        if (horaMin > horaMax) {
          this.formField
            .get('dMinDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMinDateRequerid')?.setErrors(null);
        }
      } else {
        if (Date.parse(minDate) > Date.parse(maxDate)) {
          this.formField
            .get('dMinDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMinDateRequerid')?.setErrors(null);
        }
      }
    } else {
      /// If the time variable is true, it validates by hour, if not, it validates by date, month, day, etc...
      if (isHour) {
        if (horaMax < horaMin) {
          this.formField
            .get('dMaxDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMaxDateRequerid')?.setErrors(null);
        }
      } else {
        if (Date.parse(maxDate) < Date.parse(minDate)) {
          this.formField
            .get('dMaxDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMaxDateRequerid')?.setErrors(null);
        }
      }
    }
  }

  timeToSeconds(time: string): number {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  }

  get maxLengthText() {
    if (this.formField.get('fkIIdFieldType')?.value === this.typeField.TextArea) {
      return this.maxTextAreaLength;
    }
    else if (this.formField.get('fkIIdFieldType')?.value === this.typeField.Text) {
      return this.maxTextOnlyLength;
    }
    else {
      return null;
    }
  }

  validateDecimals(event: any) {
    let input = event.target.value;

    input = input.replace(/,/g, '');

    if (!/^\d*\.?\d*$/.test(input)) {
      event.target.value = input.slice(0, -1);
      return;
    }

    if (input.includes('.')) {
      const [integerPart, decimalPart] = input.split('.');
      if (decimalPart.length > 3) {
        event.target.value = `${integerPart}.${decimalPart.slice(0, 3)}`;
      } else {
        event.target.value = input;
      }
    } else {
      event.target.value = input;
    }
  }

  formatToThreeDecimals(event: any) {
    let input = event.target.value;

    if (input && !input.includes('.')) {
      event.target.value = `${parseFloat(input).toFixed(2)}`;
    }

    if (input.includes('.')) {
      const [integerPart, decimalPart] = input.split('.');
      if (decimalPart.length === 1) {
        event.target.value = `${integerPart}.${decimalPart}000`;
      } else if (decimalPart.length === 2) {
        event.target.value = `${integerPart}.${decimalPart}0`;
      }
    }
  }

  resetFieldSetDefaultValue() {
    this.formField.get('vSetDefaultValue')?.setValue(null);
  }

  onFileDropped(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
    const file = event.dataTransfer?.files[0];
    if (file) {
      if (file.type !== 'application/pdf') {
        this._msgSvc.messageError('Solo se permiten archivos PDF.');
        return;
      } else if (file.size > 20 * 1024 * 1024) {
        this._msgSvc.messageError(
          'El archivo excede el tamaño máximo (20 MB).'
        );
        return;
      }

      // Convierte el archivo PDF a Base64
      this.convertPdfToBase64(file)
        .then((base64: string) => {
          this.formField.get('vExternalPDF')?.setValue(base64);
        })
        .catch((error) => {
          this._msgSvc.messageError(error);
        })
        .finally(() => {
          this.showBtnDelete = true;
          this.fileName = file.name;  // Asigna el nombre del archivo
          this.formField.get('vFileName')?.setValue(file.name);
          this.formField.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessCountry);

        });
    }

  }
  validateAndUpload(file: File) {
    const maxSizeInBytes = this.maxSizeInMb * 1024 * 1024;
    if (file.type !== 'application/pdf') {
      return;
    }
    if (file.size > maxSizeInBytes) {
      return;
    }
  }
  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }
  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      if (file.type !== 'application/pdf') {
        return this._msgSvc.messageError('Solo se permiten archivos PDF.');
      } else if (file.size > 20 * 1024 * 1024) {
        // Límite de 20 MB
        return this._msgSvc.messageError(
          'La imagen excede el peso máximo(100MB)'
        );
      } else {
        const files: FileList = event.target.files;
        const name = event.target.files[0].name;
        // Convierte el archivo PDF a Base64
        this.convertPdfToBase64(file)
          .then((base64: string) => {
            this.formField.get('vExternalPDF')?.setValue(base64);
          })
          .catch((error) => {
            this._msgSvc.messageError(error);
          })
          .finally(() => {
            this.showBtnDelete = true;
            this.fileName = name;
          });
        this.formField.get('vFileName')?.setValue(name);
        this.formField
          .get('fkIIdBusinessByCountry')
          ?.setValue(this.idBusinessCountry);
      }
    }
  }
  convertPdfToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String);
      };
      reader.onerror = (error) => {
        this._msgSvc.messageError('Error al leer el archivo: ' + error);
      };
      reader.readAsDataURL(file);
    });
  }
  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  Checks() {
    this._checkService.getChecks(this.idBusinessCountry, 1).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.optionsChecks = [...resp.result];
          this._cdr.detectChanges()
        }
      })
    })
  }

  //Obtiene todas las lecturas registradas en el sistema.
  getConfigurationOCR() {
    this._parametersService
      .getConfigurationOCR()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.readingsData = resp.result;
          }
        }
      });
  }

  //Obtiene el detalle de una lectura OCR por medio del idConfiguration.
  getConfigurationOCRById(idConfiguration: number) {
    this._parametersService
      .getConfigurationOCRById(idConfiguration)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            if (resp.result.homologations) {
              this.homologationData = resp.result.homologations;
            }
          }
        }
      });
  }

  //obtiene el campo llave OCR del formulario.
  getKeyFieldOCR(idForm: number) {
    this.fieldSubs = this._moduleService.getKeyFieldOCR(idForm).pipe(
      catchError((excepception) => {
        this.orderList = this.utilsSvc.generarArrayOrderList(1);
        if (excepception.error.error) {
          this._msgSvc.messageWaring(this._translateService.instant('Warning'), excepception.error.message)
        }
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {

      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          if(resp.result){
            this.fielkeyOCRList = resp.result;
            if(this.fielkeyOCRList.length > 0){
              this.keyFieldOCR = this.fielkeyOCRList[0].id;
            }
          }
        }
      }
    });
  }

  getParametersStandard() {
    this._parametersService
      .getParameters('StandarField')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.standarField = resp;
        } else {
        }
      });
  }

  /// trae la lista de los tipos de archivos
  getTypeFileList() {
    this._parametersService
      .getParameters('Type_Files_DocumenModules')
      .subscribe((resp: any) => {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.fileTypes = resp;
        }
      });
  }
}
