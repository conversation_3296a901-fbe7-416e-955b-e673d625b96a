import { CommonModule } from '@angular/common';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Router } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription, catchError, of } from 'rxjs';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { FieldTaskTrayConfigModel } from 'src/app/shared/models/field';
import { ProcessModel } from 'src/app/shared/models/menu';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import {
  TaskTayConfiCreateModel,
  TaskTayConfiEditModel,
  TaskTrayConfigListModel,
  TaskTrayConfigModel,
  TaskTrayConfigProductApiProductModel,
  TaskTrayConfigProductApiRoleModel,
} from 'src/app/shared/models/task-tray-config';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-all-task-tray-settings',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    MatInputModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    TableComponent,
    TranslateModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    Modal2Component,
    MatSlideToggleModule,
  ],
  templateUrl: './all-task-tray-settings.component.html',
  styleUrls: ['./all-task-tray-settings.component.scss'],
})
export class AllTaskTraySettingsComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessByCountry: number = 0;
  formFilter: FormGroup = new FormGroup({});
  formCreateEditElement: FormGroup = new FormGroup({});
  processes: ProcessModel[] = [];
  productsApiRole: TaskTrayConfigProductApiRoleModel[] = [];
  productsApiProduct: TaskTrayConfigProductApiProductModel[] = [];
  fields: FieldTaskTrayConfigModel[] = [];
  orderList: number[] = [];
  taskTraySettingDataTable: TaskTrayConfigListModel[] = [];
  titelModal: string = 'Elemento de bandeja de tareas';
  @ViewChild('createEditElementModal')
  createEditElementModal?: TemplateRef<any>;
  estructTaskTraySettingsTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'TaskTraySettings.Table.ItemName'
      ),
      columnValue: 'vFieldName',
    },
    {
      columnLabel: this._translateService.instant(
        'TaskTraySettings.Table.Order'
      ),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant(
        'TaskTraySettings.Table.Type'
      ),
      columnValue: 'vType',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Insurer.Edit'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
  ];

  constructor(
    private _router: Router,
    private _messageService: MessageService,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    private _parametersService: ParametersService,
    public utilsSvc: UtilsService,
    public _matDialog: MatDialog,
    private _taskTayConfigService: TaskTayConfigService,
    private _roleService: RoleService,
    private _productService: ProductService,
    private _moduleService: ModuleService
  ) {}

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this.initFormFilter();
    this.getAllProcess();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table configuración de bandeja de tareas.
      this.estructTaskTraySettingsTable[0].columnLabel =
        this._translateService.instant('TaskTraySettings.Table.ItemName');
      this.estructTaskTraySettingsTable[1].columnLabel =
        this._translateService.instant('TaskTraySettings.Table.Order');

      this.estructTaskTraySettingsTable[2].columnLabel =
        this._translateService.instant('TaskTraySettings.Table.Type');

      this.estructTaskTraySettingsTable[3].columnLabel =
        this._translateService.instant('Status');

      this.estructTaskTraySettingsTable[4].columnLabel =
        this._translateService.instant('Action');
    });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessByCountry =
                response.enterprise.pkIIdBusinessByCountry;
            }
          }
        }
      );
  }

  initFormFilter() {
    this.formFilter = this._fb.group({
      fkIdProcess: [null, [Validators.required]],
      fkIdProduct: [0],
    });
    this.formFilter.get('fkIdProcess')?.valueChanges.subscribe({
      next: (fkIdProcess) => {
        this.formFilter.get('fkIdProduct')?.setValue(null);
        if (fkIdProcess > 0) {
          this.getProductsByIdProcess(fkIdProcess, this.idBusinessByCountry);
        }
      },
    });

    this.formFilter.get('fkIdProduct')?.valueChanges.subscribe({
      next: (fkIdProduct) => {
        if (fkIdProduct > 0) {
          this.getTaskTrayConfigList(
            this.formFilter.get('fkIdProcess')?.value,
            fkIdProduct
          );
        }
      },
    });
  }

  initFormCreateEditElement() {
    this.formCreateEditElement = this._fb.group({
      pkIIdTaskTrayConfig: [0],
      vFieldName: [''],
      fkIIdField: [null],
      iOrder: [null, [Validators.required]],
      bActive: [true, [Validators.required]],
      bIsFilter: [true, [Validators.required]],
      bIsStandard: [false],
      fkIIdBusinessByCountry: [0],
      fkIIdProduct: [0],
      bIsQuote: [false],
      fkIIdProcess: [0],
    });
  }

  //Obtiene todos los procesos regsitrados en el sistema.
  getAllProcess() {
    this._parametersService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.processes = resp.result;
        }
      });
  }

  getProductsByIdProcess(idProcess: number, idBusinessCountry: number) {
    this._roleService
      .getProductsByIdProcess(idProcess, idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.formFilter.get('fkIdProduct')?.disable();
          this._messageService.messageInfo(
            'Advertencia',
            'No se encontró ningun porducto para este proceso'
          );
          this.taskTraySettingDataTable = [];
        } else {
          if (resp.result.length > 0) {
            this.formFilter.get('fkIdProduct')?.enable();
            this.productsApiRole = resp.result;
            this.productsApiProduct = [];
            this.taskTraySettingDataTable = [];
          }
          if (this.hasZeroValue(resp.result)) {
            if (resp.result[0].pkIIdProductModule) {
              if (resp.result.length > 1) {
                this.productsApiRole.forEach(
                  (product: TaskTrayConfigProductApiRoleModel) => {
                    product.vName = `No Aplica - ${product.pkIIdProductModule}`;
                  }
                );
              } else {
                this.productsApiRole[0].vName = `No Aplica - ${resp.result[0].pkIIdProductModule}`;
                this.formFilter
                  .get('fkIdProduct')
                  ?.setValue(this.productsApiRole[0].pkIIdProductModule);
                this.formFilter.get('fkIdProduct')?.disable();
              }
            }
          }
        }
      });
  }

  //Obtiene los productos asociados a un proceso por medio del fkIIdBusinessByCountry.
  getAllProductByBusiness(fkIIdBusinessByCountry: number) {
    this._productService
      .getAllProductByBusiness(fkIIdBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.length > 0) {
            this.formFilter.get('fkIdProduct')?.enable();
            this.productsApiProduct = resp.result;
            this.productsApiRole = [];
          }
        }
      });
  }

  //Obtiene la lista de tareas a mostrar en la tabla
  getTaskTrayConfigList(idProcess: number, idProduct?: number) {
    this._taskTayConfigService
      .getTaskTrayConfigList(this.idBusinessByCountry, idProcess, idProduct)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.taskTraySettingDataTable = [];
        } else {
          // this.order = resp.result.length;
          this.taskTraySettingDataTable = resp.result;
          if (resp.result.length > 0) {
            this.orderList = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderList = this.utilsSvc.generarArrayOrderList(1);
          }
        }
      });
  }

  //Obtine la lista de campos disponibles para un elemento de la bandeja de tarea, asociados por id de producto.
  getFieldListByProductModule(idProductModule: number, isQuote: boolean) {
    this._moduleService
      .getFieldListByProductModule(idProductModule, isQuote)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.fields = resp.result;
        }
      });
  }

  //Busca un elemento de la bandeja de tarea por pkIIdTaskTrayConfig.
  getTaskTrayConfig(pkIIdTaskTrayConfig: number) {
    this._taskTayConfigService
      .getTaskTrayConfig(pkIIdTaskTrayConfig)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.validateForm(resp.result);
          this.formCreateEditElement.patchValue(resp.result);
        }
      });
  }

  //Aplica las validaciones necesarias según la lógica establecida en el requerimiento.
  validateForm(data: TaskTrayConfigModel) {
    if (data.bIsStandard) {
      this.formCreateEditElement.get('fkIIdField')?.disable();
    }
    if (
      data.vFieldName === 'Id de tarea' ||
      data.vFieldName === 'Fecha de creación'
    ) {
      this.formCreateEditElement.get('bActive')?.disable();
    }
    if (
      data.vFieldName === 'Id de tarea' ||
      data.vFieldName === 'Estado' ||
      data.vFieldName === 'Fecha de creación' ||
      data.vFieldName === 'Proceso'
    ) {
      this.formCreateEditElement.get('bIsFilter')?.disable();
    }
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.getTaskTrayConfig(event.value.pkIIdTaskTrayConfig);
        this.openModalCreateEditElement();
        break;
      default:
        break;
    }
  }

  openModalCreateEditElement() {
    this.titelModal = this._translateService.instant(
      'TaskTraySettings.FormCreateEditElement.TitleModal'
    );
    this.initFormCreateEditElement();
      this.getFieldListByProductModule(
        this.formFilter.get('fkIdProduct')?.value,
        false
      );
    this._matDialog.open(this.createEditElementModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  closeModalEvent(event: boolean) {
    this.fields = [];
  }

  get validformFilter(): boolean {
    return this.formFilter.valid;
  }

  get validformCreateEditElement(): boolean {
    return this.formCreateEditElement.valid;
  }

  //Obtine el nombre de un campo (vNameField) pro medio de la llave fkIIdField
  getNameFieldById(fkIIdField: number) {
    const FoundField = this.fields.find(
      (field: FieldTaskTrayConfigModel) => field.pkIIdField === fkIIdField
    );
    return FoundField ? FoundField.vNameField : null;
  }

  complete() {
    if (this.formCreateEditElement.get('pkIIdTaskTrayConfig')?.value > 0) {
      this.editElement();
    } else {
      this.createElement();
    }
  }
  createElement() {
    if (this.formFilter.get('fkIdProcess')?.value === 7) {
      this.formCreateEditElement.get('bIsQuote')?.setValue(true);
    }
    this.formCreateEditElement
      .get('fkIIdProduct')
      ?.setValue(this.formFilter.get('fkIdProduct')?.value);
    this.formCreateEditElement
      .get('fkIIdBusinessByCountry')
      ?.setValue(this.idBusinessByCountry);
    this.formCreateEditElement
      .get('fkIIdProcess')
      ?.setValue(this.formFilter.get('fkIdProcess')?.value);
    this.formCreateEditElement
      .get('vFieldName')
      ?.setValue(
        this.getNameFieldById(
          this.formCreateEditElement.get('fkIIdField')?.value
        )
      );
    if (this.validformCreateEditElement) {
      let payload: TaskTayConfiCreateModel =
        this.formCreateEditElement.getRawValue();
      this._taskTayConfigService
        .createTaskTrayConfig(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            } else {
              if (error.error.message === 'FilterLimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.FilterLimitExceededSubtitle'
                  )
                );
              }
              if (error.error.message === 'LimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededSubtitle'
                  )
                );
              }
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              '',
              this._translateService.instant(
                'ModulesSetting.SuccessfulMessageCreated'
              )
            );
            this.getlistingAfterCreating();
            this._matDialog.closeAll();
          }
        });
    }
  }

  editElement() {
    if (this.validformCreateEditElement) {
      let payload: TaskTayConfiEditModel =
        this.formCreateEditElement.getRawValue();
      this._taskTayConfigService
        .updateTaskTrayConfig(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            } else {
              if (error.error.message === 'FilterLimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.FilterLimitExceededSubtitle'
                  )
                );
              }
              if (error.error.message === 'LimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededSubtitle'
                  )
                );
              }
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              '',
              this._translateService.instant(
                'TaskTraySettings.FormCreateEditElement.Messages.Modified'
              )
            );
            this.getlistingAfterCreating();
            this._matDialog.closeAll();
          }
        });
    }
  }

  getlistingAfterCreating() {
    if (
      this.formFilter.get('fkIdProcess')?.value > 0 &&
      this.formFilter.get('fkIdProduct')?.value > 0
    ) {
      this.getTaskTrayConfigList(
        this.formFilter.get('fkIdProcess')?.value,
        this.formFilter.get('fkIdProduct')?.value
      );
    } else if (this.formFilter.get('fkIdProcess')?.value > 0) {
      this.getTaskTrayConfigList(this.formFilter.get('fkIdProcess')?.value);
    }
  }

  // Función para verificar si algún producto tiene valor 0 en "fkIIdProduct"
  hasZeroValue(array: TaskTrayConfigProductApiRoleModel[]): boolean {
    return array.some(
      (product: TaskTrayConfigProductApiRoleModel) => product.fkIIdProduct === 0
    );
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
