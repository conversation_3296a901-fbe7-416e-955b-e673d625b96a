import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DomSanitizer } from '@angular/platform-browser';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';

import { catchError, filter, of, Subscription } from 'rxjs';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { EmbeddedContent } from 'src/app/shared/models/embeddedResource/embedded.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { ErrorHandlingService } from 'src/app/shared/services/error/errorHandlingService';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import * as powerbi from 'powerbi-client';

@Component({
  standalone: true,
  selector: 'app-metrics',
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    BreadcrumbComponent,
    MatIconModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
  ],
  templateUrl: './metrics.component.html',
  styleUrls: ['./metrics.component.scss'],
})
export class MetricsComponent implements OnInit, OnDestroy {
  form: FormGroup = new FormGroup({});
  @ViewChild('reportContainer', { static: false }) reportContainer!: ElementRef;
  idBusinessByCountry: number = 0;
  elementEmbedded: any;

  embeddedList: EmbeddedContent[] = [];
  pkIIdContent!: number;
  resourceName!: string;

  private _routerSubscription!: Subscription;
  constructor(
    public utilsService: UtilsService,
    private _translateService: TranslateService,
    private _router: Router,
    private _parameterService: ParametersService,
    private _settingService: SettingService,
    private _customRouter: CustomRouterService,
    private _errorHandlingService: ErrorHandlingService,
    private _messageService: MessageService,
    private sanitizer: DomSanitizer
  ) {}

  inicio: string = 'Inicio';
  metricas: string = 'Métricas';

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.metricas, link: '/dashboard/metrics' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant('Inicio');
      this.metricas = this._translateService.instant('Métricas');
      this.sections[0].label = this.inicio;
      this.sections[1].label = this.metricas;
    });
    this.getDataSettingInit();
  }

  ngOnDestroy(): void {}

  checkRoute(url: string): void {
    if (url === '/dashboard/metrics') {
    }
  }

  async getDataSettingInit() {
    let data = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry =
      data == undefined
        ? this._customRouter.getIdbusinessByCountry()
        : data.idBusinessByCountry;
    this.getEmbeddedByBusinessCountryByResourceType(
      'PBI',
      this.idBusinessByCountry
    );
  }

  getEmbeddedByBusinessCountryByResourceType(
    resourceType: string,
    idBusinessByCountry: number
  ) {
    this._parameterService
      .getEmbeddedByBusinessCountryByResourceType(
        idBusinessByCountry,
        resourceType,
        true,
        false
      )
      .pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error, false);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this.embeddedList = resp.result;
          }
        }
      });
  }

  getEmbeddedResource(event: any) {
    const idContent = event.value;

    this._parameterService
      .getEmbeddedReportByIdContent(idContent, true, false)
      .pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error, false);
          return of([]);
        })
      )
      .subscribe(async (resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos no es un string.');
          return;
        }

        if (resp.error) {
          this._messageService.messageError(
            this._translateService.instant('Warning') + resp.message
          );
          return;
        }

        this.elementEmbedded = resp.result;
        
        const embedConfig: powerbi.IEmbedConfiguration = {
          type: 'report',
          id: this.elementEmbedded.reportId,
          embedUrl: this.elementEmbedded.embedUrl,
          accessToken: this.elementEmbedded.embedToken,
          tokenType: powerbi.models.TokenType.Embed,
          settings: {
            panes: {
              filters: { visible: false },
              pageNavigation: { visible: true },
            },
          },
        };

        const powerbiService = new powerbi.service.Service(
          powerbi.factories.hpmFactory,
          powerbi.factories.wpmpFactory,
          powerbi.factories.routerFactory
        );

        powerbiService.reset(this.reportContainer.nativeElement);
        powerbiService.embed(this.reportContainer.nativeElement, embedConfig);
      });
  }
}
