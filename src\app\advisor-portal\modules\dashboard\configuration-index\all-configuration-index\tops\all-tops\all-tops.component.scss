.cont {
    width: 100%;
    height: auto;
    .cont-title-cards {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .cont-options-view {
        .view-not-select {
          color: gainsboro;
        }
        .view-select {
          color: black;
        }
      }
    }
    .cont-cards-view-colunm {
      display: flex;
      width: 100%;
      margin-right: 30px;
      .card-view-colunm {
        width: 360px;
        height: auto;
        padding: 24px 32px;
        margin: 0;
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px !important;
        border: 1px solid rgba(70, 67, 67, 0.116);
        border-radius: 0;
        text-align: center;
        .cont-logo-view-colunm {
          margin-bottom: 12px;
          img {
            width: 124px;
            height: 48px;
          }
        }
        .cont-plan-name-view-colunm {
          border-bottom: 1px solid #d9d9d9;
          margin-bottom: 28px;
          p {
            font-weight: bold;
          }
        }
        .cont-yearly-fee-view-colunm {
          p {
            margin: 0;
          }
          padding-bottom: 24px;
          border-bottom: 1px solid #d9d9d9;
          margin-bottom: 40px;
        }
        .cont-details-view-column {
          .cont-item-detail-view-column {
            display: flex;
            justify-content: initial;
            align-items: center;
            margin-bottom: 16px;
            .color-check {
              color: #007d61;
              margin-right: 4px;
            }
            .color-not-check {
              color: #b1232d;
              margin-right: 4px;
            }
            .font-especial {
              // font-size: small;
            }
          }
        }
        .cont-check-view-colunm {
          .checkbox-section {
            margin: 2px 0;
          }
        }
        .cont-button-view-colunm {
          display: flex;
          align-items: center;
          justify-content: center;
          button {
            width: 136px !important;
          }
        }
      }
      .cont-validity-view-column {
        text-align: center;
        h6 {
          font-weight: bold;
        }
      }
    }
    .cont-cards-view-row {
      .card-view-row {
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
        padding: 24px 16px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .cont-logo-view-row {
          margin-bottom: 12px;
          img {
            width: 124px;
            height: 48px;
          }
          .cont-plan-name-view-row {
            text-align: center;
            .plan-name-view-row {
              font-weight: bold;
            }
          }
        }
        .cont-monthly-premium-view-row {
          // font-size: small;
          p {
            margin: 0;
          }
          h1 {
            margin: 0;
          }
        }
        .cont-yearly-fee-view-row {
          p {
            margin: 0;
          }
          h1 {
            margin: 0;
          }
        }
        .cont-check-and-button {
          text-align: center;
          justify-content: center;
        }
        .cont-button-view-row {
          display: flex;
          flex-direction: column;
          justify-content: center;
          button {
            width: 122px !important;
          }
        }
      }
      .cont-validity-view-row {
        text-align: center;
        h6 {
          font-weight: bold;
        }
      }
    }
  }

span.one {
    background: #e4c685;
     border-radius: 0.8em;
    -moz-border-radius: 0.8em;
    -webkit-border-radius: 0.8em;
    color: #ffffff;
    display: inline-block;
    font-weight: bold;
    line-height: 1.6em;
    margin-right: 15px;
    text-align: center;
    width: 1.6em; 
}

span.two {
    background: gray;
     border-radius: 0.8em;
    -moz-border-radius: 0.8em;
    -webkit-border-radius: 0.8em;
    color: #ffffff;
    display: inline-block;
    font-weight: bold;
    line-height: 1.6em;
    margin-right: 15px;
    text-align: center;
    width: 1.6em; 
}

span.three {
    background: #d09715;
     border-radius: 0.8em;
    -moz-border-radius: 0.8em;
    -webkit-border-radius: 0.8em;
    color: #ffffff;
    display: inline-block;
    font-weight: bold;
    line-height: 1.6em;
    margin-right: 15px;
    text-align: center;
    width: 1.6em; 
}

span.four {
    background: #4c545a;
     border-radius: 0.8em;
    -moz-border-radius: 0.8em;
    -webkit-border-radius: 0.8em;
    color: #ffffff;
    display: inline-block;
    font-weight: bold;
    line-height: 1.6em;
    margin-right: 15px;
    text-align: center;
    width: 1.6em; 
}

span.five {
    background: black;
     border-radius: 0.8em;
    -moz-border-radius: 0.8em;
    -webkit-border-radius: 0.8em;
    color: #ffffff;
    display: inline-block;
    font-weight: bold;
    line-height: 1.6em;
    margin-right: 15px;
    text-align: center;
    width: 1.6em; 
}