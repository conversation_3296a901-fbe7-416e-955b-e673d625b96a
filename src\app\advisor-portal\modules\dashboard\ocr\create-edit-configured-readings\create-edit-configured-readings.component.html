<!-- Nueva lectura-->
<div class="row mt-2 mb-3">
    <h5 class="title-local mb-1">{{ 'OCR.NewReading' | translate }}</h5>
</div>

<!-- Seleccionar tipo lectura-->
<div class="row mt-2 mb-3">
    <mat-radio-group [disabled]="this.idHomologation != 0" [(ngModel)]="iTypeReading">
        <mat-radio-button [value]="1">{{ "OCR.StandardReading" | translate }}</mat-radio-button>
        <mat-radio-button [value]="2">{{ "OCR.DoclmReading" | translate }}</mat-radio-button>
      </mat-radio-group>
</div>

<!-- Nombre de lectura -->
<div class="row">
    <div class="col-6 col-md-6">
        <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
                {{ "OCR.ReadingName" | translate }}
            </mat-label>
            <input matInput PreventionSqlInjector [(ngModel)]="baseName" />
        </mat-form-field>
    </div>
    <div class="col-6 col-md-6">
        <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
                {{ "OCR.AssociatedProduct" | translate }}
            </mat-label>
            <mat-select  [(ngModel)]="idProductAssociated" name="idProductAssociated">
                <mat-option *ngFor="let item of products" [value]="item.id">
                    {{item.vProductName}}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
</div>

<!-- Nueva lectura-->
<div class="row mt-2 mb-3">
    <h6 class="title-local mb-1">{{ 'OCR.Homologation' | translate }}</h6>
</div>

<!-- Tabla Homologaciones -->
<div class="row mt-2">
    <app-table [displayedColumns]="estructHomologationTable" [data]="homologationData"
        (iconClick)="controller($event)"></app-table>
</div>

<!-- Añadir -->
<div class="row">
    <div class="col-md-4">
        <button type="button" mat-raised-button color="primary" (click)="openModal('homologationModal', 0, true)"
            [disabled]="idConfiguration === 0"> {{
            "Add" | translate }} </button>
    </div>
</div>


<div class="d-flex justify-content-center mt-3">
    <!-- Cancelar -->
    <a class="label-button mt-1 mx-3" mat-button (click)="cancel()"><span>{{"Cancel" | translate}}</span>
    </a>
    <!-- Guardar lectura -->
    <button class=" mr-2 mt-1" type="button" color="primary" mat-raised-button
        (click)="createOrUpdateConfigurationOCR()" [disabled]="!(idBusinessByCountry > 0 && baseName)">{{
        "OCR.SaveReading" |
        translate }}</button>

</div>


<!-- modal Crear - Editar Homologación -->
<ng-template #homologationModal>
    <app-modal2 [titleModal]="modalTitle" [showCancelButtonBelow]="false">
        <ng-container body>
            <form [formGroup]="formHomologation">
                <!-- Campo base -->
                <div class="row">
                    <div class="col-4 col-sm-12">
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>
                                {{ "OCR.FiledBase" | translate }}
                            </mat-label>
                            <input matInput formControlName="baseField" PreventionSqlInjector />
                        </mat-form-field>
                    </div>
                </div>

                <!-- Nueva homologación-->
                <div class="row mt-2 mb-3">
                    <h6 class="title-local mb-1">{{ 'OCR.NewReading' | translate }}</h6>
                </div>

                <div class="cont-alert mb-2">
                    <div class="cont-icon-alert">
                        <img src="assets/img/emergency_home.svg" />
                    </div>
                    <div class="cont-message-alert">
                        <span>{{ 'OCR.LabelWrning1'| translate }}</span> <span class="bold-custom">"{{
                            'OCR.LabelWrning2'| translate }}"</span> {{ 'OCR.LabelWrning3'| translate }}
                        <span class="bold-custom">{{ 'OCR.LabelWrning4'| translate }}</span>
                    </div>
                </div>

                <div class="cont-message-blue mb-3">
                    <span>{{ 'OCR.AlertBlue'| translate }}</span>
                </div>

                <div class="row">
                    <!-- Plantilla OCR -->
                    <div *ngIf="iTypeReading == 1" class="col-md-12 mb-2">
                        <mat-form-field appearance="outline" class="select-look m-auto w-100">
                            <mat-label>
                                {{ "OCR.OCRTemplate" | translate }}
                            </mat-label>
                            <mat-select formControlName="ocrTemplate">
                                @for (template of ocrTemplateList; track $index) {
                                <mat-option [value]="getKey(template)">
                                    {{ getValue(template) }}
                                </mat-option>
                                }
                            </mat-select>
                            @if (utilsSvc.isControlHasError(formHomologation, 'ocrTemplate', 'required')) {
                            <mat-error>
                                {{ "ThisFieldIsRequired" | translate }}
                            </mat-error>
                            }
                        </mat-form-field>
                    </div>
                    <div *ngIf="iTypeReading == 2" class="col-md-12 mb-2">
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>
                                {{ "OCR.TemplateDoclm" | translate }}
                            </mat-label>
                            <input matInput formControlName="guidAssesmentDoclm" PreventionSqlInjector />
                        </mat-form-field>
                    </div>
                </div>
               

                <!-- Revalidar plantillas  -->
                <div class="row  mb-3">
                    <div class="col-md-">
                        <button type="button" mat-raised-button color="primary" (click)="revalidateTemplate()"
                            [disabled]="!ocrTemplate && !guidAssesmentDoclm">
                            {{ "OCR.RevalidateTemplates" | translate }}
                            <mat-icon iconPositionEnd fontIcon="refresh"></mat-icon>
                        </button>
                    </div>
                </div>

                <!-- Subir plantilla -->
                <div class="row mb-2">
                    <ng-container *ngIf="showFileUpload">
                        <div class="cont-fieles mt-3">
                            <div class="cont-template" *ngIf="showTemplateUpload">
                                <div class="cont-info-file">
                                    <p class="m-0 p-0">{{fileNameTemplate}}</p>
                                    <span class="description">{{templateSize}}</span>
                                </div>
                                <div class="cont-download-icon">
                                    <span class="material-symbols-outlined click" (click)="deleteTemplate()">
                                        delete
                                    </span>
                                </div>
                            </div>
                            <div class="cont-upload" *ngIf="!showTemplateUpload">
                                <app-drag-drop-upload [fileAccept]="'.PDF'" [isOneFile]="true" [message]="''"
                                    (saveFile)="getFiles($event)"></app-drag-drop-upload>
                            </div>
                        </div>
                    </ng-container>
                </div>

                <!-- Aseguradora -->
                <div *ngIf="iTypeReading == 2" class="row">
                    <!-- Plantilla OCR -->
                    <div  class="col-md-12 mb-2">
                        <mat-form-field appearance="outline" class="select-look m-auto w-100">
                            <mat-label>
                                {{ "OCR.Insurance" | translate }}
                            </mat-label>
                            <mat-select formControlName="insurance">
                                @for (insurance of listOfInsurances; track $index) {
                                <mat-option [value]="insurance.vDescription">
                                    {{ insurance.vDescription }}
                                </mat-option>
                                }
                            </mat-select>
                            @if (utilsSvc.isControlHasError(formHomologation, 'insurance', 'required')) {
                            <mat-error>
                                {{ "ThisFieldIsRequired" | translate }}
                            </mat-error>
                            }
                        </mat-form-field>
                    </div>
                </div>

                <!-- Alerta por aseguradora -->
                <div *ngIf="iTypeReading == 2" class="cont-alert mb-2">
                    <div class="cont-icon-alert">
                        <img src="assets/img/emergency_home.svg" />
                    </div>
                    <div class="cont-message-alert">
                        <span>{{ 'OCR.Insurance1'| translate }}</span> <span class="bold-custom">{{
                            'OCR.Insurance2'| translate }}</span>
                    </div>
                </div>

                <!-- Campo de plantilla desde OCR -->
                <div class="col-md-12 mb-2">
                    <mat-form-field appearance="outline" class="select-look m-auto w-100">
                        <mat-label>
                            {{ "OCR.TemplateFieldOCR" | translate }}
                        </mat-label>
                        <mat-select formControlName="templateField">
                            @for (fiel of fielTemplateList; track $index) {
                            <mat-option [value]="fiel.name">
                                {{ fiel.name }}
                            </mat-option>
                            }
                        </mat-select>
                        @if (utilsSvc.isControlHasError(formHomologation, 'templateField', 'required')) {
                        <mat-error>
                            {{ "ThisFieldIsRequired" | translate }}
                        </mat-error>
                        }
                    </mat-form-field>
                </div>
            </form>

            <!-- Añadir al listado -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <button type="button" mat-raised-button color="primary" (click)="addHomologation()"
                        [disabled]="!(ocrTemplate && templateField ) && !guidAssesmentDoclm"> {{
                        "OCR.AddToList" | translate }} </button>
                </div>
            </div>

            <!--Tabla de homologaciones de campo-->
            <div class="row mt-2">
                <app-table [displayedColumns]="estructFieldHomologationTable" [data]="fieldHomologationData"
                    (iconClick)="controllerCrud($event)"></app-table>
            </div>

        </ng-container>

        <!-- Botones-->
        <ng-container customButtonRight>
            <a class="label-button mt-1 mx-3" mat-button (click)="closeModal()"><span>{{"Close"
                    | translate}}</span>
            </a>
            <button class="w-auto mx-3" type="button" mat-raised-button color="primary" (click)="saveHomologation()"
                [disabled]="(fieldHomologationData.length === 0 || !baseField)">
                {{ "OCR.SaveHomologation" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>