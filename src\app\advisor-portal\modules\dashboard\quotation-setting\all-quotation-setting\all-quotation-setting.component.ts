import { CommonModule } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Router } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { FieldTaskTrayConfigModel } from 'src/app/shared/models/field';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { TaskTrayConfigListModel, TaskTrayConfigModel, TaskTrayConfigProductApiProductModel } from 'src/app/shared/models/task-tray-config';
import { QuotationConfiCreateModel } from 'src/app/shared/models/work-flow/quotation-config-create.models';
import { QuotationConfigListModel } from 'src/app/shared/models/work-flow/quotation-config-list.model';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { WorkFlowService } from 'src/app/shared/services/work-flow/work-flow.service';

@Component({
  standalone : true,
  selector: 'app-all-quotation-setting',
  templateUrl: './all-quotation-setting.component.html',
  styleUrls: ['./all-quotation-setting.component.scss'],
  imports: [
    CommonModule,
    // ChooseCountryAndCompanyComponent,
    MatInputModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    TableComponent,
    TranslateModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    Modal2Component,
    MatSlideToggleModule,
  ]
})
export class AllQuotationSettingComponent implements OnInit {

  private _settingCountryAndCompanySubscription?: Subscription;
  taskTraySettingDataTable: QuotationConfigListModel[] = [];
  estructTaskTraySettingsTable: BodyTableModel[] = [];
  productsApiProduct: TaskTrayConfigProductApiProductModel[] = [];
  fields: FieldTaskTrayConfigModel[] = [];
  formFilter: FormGroup = new FormGroup({});
  formCreateEditElement: FormGroup = new FormGroup({});
  @ViewChild('createEditElementModal')
  createEditElementModal?: TemplateRef<any>;
  idBusinessByCountry: number = 0;
  orderList: number[] = []; 
  titelModal: string = 'Elementos de mis cotizaciones';
  pageSize: number = 10;

  constructor(
    private _fb: FormBuilder,
    public _matDialog: MatDialog,
    private _messageService: MessageService,
    private _moduleService: ModuleService,
    private _productService: ProductService,
    public router: Router,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    public utilsSvc: UtilsService,
    private _workFlowService: WorkFlowService,
  ) { }

  ngOnInit() {
    this.estrucTable();
    this.initFormFilter();

    this._settingCountryAndCompanySubscription =
    this._settingService.currentSettingCountryAndCompany.subscribe(
      (response) => {
        if (this.router.url == response.currentModule) {
          if (!(Object.keys(response).length === 0)) {
            this.productsApiProduct = [];
            this.taskTraySettingDataTable = [];
            this.idBusinessByCountry = response.enterprise.pkIIdBusinessByCountry;
            this.getAllProductByBusiness(
              response.enterprise.pkIIdBusinessByCountry
            );

          }
        }
      }
    );

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table configuración de cotizaciones.
      this.estructTaskTraySettingsTable[0].columnLabel =
        this._translateService.instant('SettingsQuotes.Quotes.ItemName');
      this.estructTaskTraySettingsTable[1].columnLabel =
        this._translateService.instant('SettingsQuotes.Quotes.Order');

      this.estructTaskTraySettingsTable[2].columnLabel =
        this._translateService.instant('SettingsQuotes.Quotes.Type');

      this.estructTaskTraySettingsTable[3].columnLabel =
        this._translateService.instant('SettingsQuotes.Quotes.State');

      this.estructTaskTraySettingsTable[4].columnLabel =
        this._translateService.instant('SettingsQuotes.Quotes.Action');
    });

  }

  // Definición de las columnas
  estrucTable(){
    this.estructTaskTraySettingsTable = [
      { columnLabel: this._translateService.instant('SettingsQuotes.Quotes.ItemName'), columnValue: 'vFieldName',}, // Nombre de item
      { columnLabel: this._translateService.instant('SettingsQuotes.Quotes.Order'), columnValue: 'iOrder',}, // Orden
      { columnLabel: this._translateService.instant('SettingsQuotes.Quotes.Type'), columnValue: 'vType',}, // Tipo
      { columnLabel: this._translateService.instant('SettingsQuotes.Quotes.State'), columnValue: 'bActive', functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),}, // Estado
      { columnLabel: this._translateService.instant('SettingsQuotes.Quotes.Action'), columnValue: 'edit', columnIcon: 'create',}, // Accion
    ];
  }


  initFormFilter() {
    this.formFilter = this._fb.group({
      fkIdProduct: [null, [Validators.required]],
    });


    this.formFilter.get('fkIdProduct')?.valueChanges.subscribe({
      next: (fkIdProduct) => {
        if (fkIdProduct > 0) {
          this.getQuotationConfigList(
            fkIdProduct
          );
        }
      },
    });
  }

  initFormCreateEditElement() {
    this.formCreateEditElement = this._fb.group({
      pkIIdQuotationConfig: [0],
      vFieldName: [''],
      fkIIdField: [null],
      iOrder: [null, [Validators.required]],
      bActive: [true, [Validators.required]],
      bIsFilter: [true, [Validators.required]],
      bIsStandard: [false],
      fkIIdBusinessByCountry: [0],
      fkIIdProduct: [0],
      bIsQuote: [false],
      fkIIdProcess: [0],
    });
  }

  getAllProductByBusiness(fkIIdBusinessByCountry: number) {
    this._productService
      .getAllProductByBusiness(fkIIdBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.length > 0) {
            this.formFilter.get('fkIdProduct')?.enable();
            this.productsApiProduct = resp.result;
          }
        }
      });
  }


  //Obtiene la lista de tareas a mostrar en la tabla
  getQuotationConfigList(idProduct?: number) {
    this._workFlowService
      .getQuotationConfigList(this.idBusinessByCountry, idProduct)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.taskTraySettingDataTable = [];
        } else {
          // this.order = resp.result.length;
          this.taskTraySettingDataTable = resp.result;
          if (resp.result.length > 0) {
            this.orderList = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderList = this.utilsSvc.generarArrayOrderList(1);
          }
        }
      });
  }

  //Obtine la lista de campos disponibles para un elemento de MIS COTIZACIONES, asociados por id de producto.
  getFieldListByProductModule(idProductModule: number, isQuote: boolean) {
    this._moduleService
      .getFieldListByProductModule(idProductModule, isQuote)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.fields = resp.result;
        }
      });
  }

  //Busca un elemento de la configiracion de mi cotizacion por el id.
  getQuotationConfigById(pkIIdQuotationConfig: number) {
    this._workFlowService
      .getQuotationConfigById(pkIIdQuotationConfig)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.validateForm(resp.result);
          this.formCreateEditElement.patchValue(resp.result);
        }
      });
  }


  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.getQuotationConfigById(event.value.pkIIdQuotationConfig);
        this.openModalCreateEditElement();
        break;
      default:
        break;
    }
  }

  openModalCreateEditElement() {
  
    this.initFormCreateEditElement();
    this.getFieldListByProductModule(
      this.formFilter.get('fkIdProduct')?.value,
      true
    );
    this._matDialog.open(this.createEditElementModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  closeModalEvent(event: boolean) {
    this.fields = [];
  }

  complete() {
    if (this.formCreateEditElement.get('pkIIdQuotationConfig')?.value > 0) {
      this.editElement();
    } else {
      this.createElement();
    }
  }

  createElement() {
   
    this.formCreateEditElement
      .get('fkIIdProduct')
      ?.setValue(this.formFilter.get('fkIdProduct')?.value);
    this.formCreateEditElement
      .get('fkIIdBusinessByCountry')
      ?.setValue(this.idBusinessByCountry);
    this.formCreateEditElement
      .get('fkIIdProcess')
      ?.setValue(this.formFilter.get('fkIdProcess')?.value);
    this.formCreateEditElement
      .get('vFieldName')
      ?.setValue(
        this.getNameFieldById(
          this.formCreateEditElement.get('fkIIdField')?.value
        )
      );
    if (this.validformCreateEditElement) {
      let payload: QuotationConfiCreateModel =
        this.formCreateEditElement.getRawValue();
      this._workFlowService
        .createQuotationConfig(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            } else {
              if (error.error.message === 'FilterLimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.FilterLimitExceededSubtitle3'
                  )
                );
              }
              if (error.error.message === 'LimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededSubtitle'
                  )
                );
              }
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              '',
              this._translateService.instant(
                'ModulesSetting.SuccessfulMessageCreated'
              )
            );
            this.getQuotationConfigList(this.formFilter.get('fkIdProduct')?.value);
            this._matDialog.closeAll();
          }
        });
    }
  }

  editElement() {
    if (this.validformCreateEditElement) {
      let payload: QuotationConfiCreateModel =
        this.formCreateEditElement.getRawValue();
      this._workFlowService
        .updateQuotationConfig(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            } else {
              if (error.error.message === 'FilterLimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.FilterLimitExceededSubtitle3'
                  )
                );
              }
              if (error.error.message === 'LimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededSubtitle'
                  )
                );
              }
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              '',
              this._translateService.instant(
                'TaskTraySettings.FormCreateEditElement.Messages.Modified'
              )
            );
            this.getQuotationConfigList(this.formFilter.get('fkIdProduct')?.value);
            this._matDialog.closeAll();
          }
        });
    }
  }

  //Aplica las validaciones necesarias según la lógica establecida en el requerimiento.
  validateForm(data: TaskTrayConfigModel) {
    if (data.bIsStandard) {
      this.formCreateEditElement.get('fkIIdField')?.disable();
      this.formCreateEditElement.get('bIsFilter')?.disable();
    }

  }

  //Obtine el nombre de un campo (vNameField) pro medio de la llave fkIIdField
  getNameFieldById(fkIIdField: number) {
    const FoundField = this.fields.find(
      (field: FieldTaskTrayConfigModel) => field.pkIIdField === fkIIdField
    );
    return FoundField ? FoundField.vNameField : null;
  }

  get validformFilter(): boolean {
    return this.formFilter.valid;
  }

  get validformCreateEditElement(): boolean {
    return this.formCreateEditElement.valid;
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

}
