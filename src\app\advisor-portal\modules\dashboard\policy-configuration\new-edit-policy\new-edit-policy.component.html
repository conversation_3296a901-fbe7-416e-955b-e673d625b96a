<div class="row">
    <div class="col-md-12 col-sm-12">
        <app-company-country-history></app-company-country-history>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-12 col-sm-12">
        <!-- Tipo de pólizas -->
        <h3>{{"PolicyConfiguration.PolicyTypes" | translate}}</h3>
        <section *ngIf="policyTypes.length > 0">
            <mat-checkbox *ngFor="let policy of policyTypes" class="example-margin"
                [checked]="selectedPolicyType === policy.pkIIdPolicyType" [disabled]="idForm > 0 || idPolicy > 0"
                (change)="onPolicyTypeChange(policy.pkIIdPolicyType, $event)">
                {{ policy.vPolicyTypeName }}
            </mat-checkbox>
        </section>
    </div>
</div>
<div class="">    
    <button class="mx-3 mb-2"  type="button" mat-raised-button (click)="openModalcopyExistingParameters()" [disabled]="disabledButton">{{ "PolicyReplicationConfiguration.ButtonTitle" | translate }}
      <mat-icon iconPositionEnd fontIcon="file_copy"></mat-icon>
    </button>
</div>
<div class="row mb-2">
    <div class="col-md-12 col-sm-12">
        <h4>{{"PolicyConfiguration.DataSettings" | translate}}</h4>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <mat-tab-group (selectedTabChange)="onTabChange($event)">
            <mat-tab label="{{ 'PolicyConfiguration.GeneralInformation.Title' | translate }}">
                <app-general-information (formGeneralInfo)="getFormGeneralInfo($event)"
                    (editedFiles)="getEditedFiles($event)" 
                    [isANewPolicy]="newPolicy"></app-general-information>
            </mat-tab>
            <mat-tab label="{{ 'PolicyConfiguration.RiskData.Title' | translate }}" [disabled]="true">
                <app-risk-data></app-risk-data>
            </mat-tab>
            <mat-tab label="{{ 'PolicyConfiguration.Coverage.Title' | translate }}" [disabled]="true">
                <app-coverages></app-coverages>
            </mat-tab>
        </mat-tab-group>
    </div>
</div>
<div class="cont-btn-navigate">
    <a mat-button (click)="cancel()"><span class="label-button">{{'Cancel'| translate}}</span></a>
    <button *ngIf="selectedIndexTab !== 2"
        [disabled]="(!forminformationGeneralValid || !formRiskDataValid) || selectedPolicyType === 0"
        class="w-auto mx-3" type="button" mat-raised-button color="primary" (click)="continue()">
        {{ "Continue" | translate }}
    </button>
    <button *ngIf="selectedIndexTab === 2" class="w-auto mx-3" type="button" mat-raised-button color="primary"
        (click)="continue()">
        {{ "PolicyConfiguration.SavePolicy" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
    </button>
</div>

<!-- Modal Copiar parámetros existentes -->
<ng-template #copyExistingParametersModal>
 
    <app-modal2 [titleModal]="titleModal" (closeModal)="onModalClose()" >       
      <ng-container body>               
        <div  class="row" [formGroup]="modalForm">
          <!-- Tipo Póliza -->
          <div class="col-md-12 mb-2">
            <mat-form-field  appearance="outline" class="select-look w-50 m-auto w-100">
              <mat-label>{{ "PolicyReplicationConfiguration.ModalPolicyTypeLabel" | translate }} </mat-label>                <!-- {{ "FormsConfigurationHistory.Module" | translate }}   [disabled]="selectedPolicyType >0"-->
              
              <mat-select formControlName="policyTypesSelect" >
                <mat-option *ngFor="let item of policyTypes" [value]="item.pkIIdPolicyType" >{{item.vPolicyTypeName}}</mat-option>                                  
              </mat-select>
              <mat-error> {{ "ThisFieldIsRequired" | translate }} </mat-error>
            </mat-form-field>
          </div>
  
          <!-- Producto -->
          <div class="col-md-12 mb-2">
            <mat-form-field appearance="outline"class="select-look w-50 m-auto w-100">
              <mat-label>{{ "PolicyReplicationConfiguration.ModalProductLabel" | translate }} </mat-label>
              <mat-select formControlName="productSelect" (selectionChange)="resetSelects()">
                <mat-option *ngFor="let product of productList" [value]="product.pkIIdProduct">
                    {{ product.vProductName }}
                </mat-option>
              </mat-select>
              <mat-error>{{ "ThisFieldIsRequired" | translate }}</mat-error>
            </mat-form-field>
          </div>
  
          <!-- Aseguradora -->
          <div class="col-md-12 mb-2">
            <mat-form-field appearance="outline" class="select-look w-50 m-auto w-100">
              <mat-label>{{ "PolicyReplicationConfiguration.ModalInsuranceCompanyLabel" | translate }}</mat-label>
              <mat-select formControlName="insurancesSelect" (selectionChange)="getPoliciesId()">
                <mat-option *ngFor="let insurance of insuranceList" [value]="insurance.pkIIdInsuranceCompanies">
                {{ insurance.vName }}
            </mat-option>
              </mat-select>
              <mat-error>{{ "ThisFieldIsRequired" | translate }}</mat-error>
            </mat-form-field>
          </div>
  
          <!-- ID -->
          <div class="col-md-12 mb-2">
            <mat-form-field appearance="outline"class="select-look w-50 m-auto w-100">
              <mat-label>{{ "PolicyReplicationConfiguration.ModalIdLabel" | translate }}                
              </mat-label>
              <mat-select formControlName="policyIdsSelect">
                <mat-option *ngFor="let idWTW of idsPolicies" [value]="idWTW">
                  {{ idWTW }}
              </mat-option>
              </mat-select>
              <mat-error>{{ "ThisFieldIsRequired" | translate }}</mat-error>
            </mat-form-field>
          </div>
  
     
        </div>
      </ng-container>
  
      <ng-container customButtonRight>
        <button type="button"    mat-raised-button color="primary" (click)="clonePolicyById()">
          {{ "PolicyReplicationConfiguration.ButtonTitle" | translate }}
        </button>
      </ng-container>
    </app-modal2>
  
    
  </ng-template>