import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';

import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { catchError, debounceTime, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ProgressBarModel } from 'src/app/shared/models/configuration-form';
import { TabModuleModel } from 'src/app/shared/models/configuration-form/tab-module/tab-module-model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-tabs-form',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    Modal2Component,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    FormsModule,
    MatTooltipModule
  ],
  templateUrl: './tabs-form.component.html',
  styleUrls: ['./tabs-form.component.scss'],
})
export class TabsFormComponent implements OnInit {
  @Input() idForm: number = 0;
  form: FormGroup = new FormGroup({});

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.TabModule.Order'
      ),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.TabModule.NameTabModule'
      ),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.TabModule.DatabaseName'
      ),
      columnValue: 'vNameDb',
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.TabModule.StepProgressBarTabs'
      ),
      columnValue: 'vNameProgress',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'edit',
      columnIcon: 'edit',
    },
  ];

  dataTableTabsForm: TabModuleModel[] = [];
  @ViewChild('editNewTabFormModal')
  editNewTabFormModal?: TemplateRef<any>;
  titelModal: string = this._translateService.instant(
    'FormConfiguration.TabModule.TitleModalCreate'
  );
  orderList: number[] = [];
  action: string = 'create';
  progressBarList: ProgressBarModel[] = [];

  constructor(
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public modalDialog: MatDialog,
    public utilsSvc: UtilsService,
    private _fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getTabModuleListByFormId(this.idForm);
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table modules
      this.estructTable[0].columnLabel = this._translateService.instant(
        'FormConfiguration.TabModule.Order'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'FormConfiguration.TabModule.NameTabModule'
      );

      this.estructTable[2].columnLabel = this._translateService.instant(
        'FormConfiguration.TabModule.DatabaseName'
      );

      this.estructTable[3].columnLabel = this._translateService.instant(
        'FormConfiguration.TabModule.StepProgressBarTabs'
      );

      this.estructTable[4].columnLabel =
        this._translateService.instant('Status');

      this.estructTable[5].columnLabel =
        this._translateService.instant('Action');
    });
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdTabModule: [0],
      vName: ['', [Validators.required]],
      vNameDb: [''],
      iOrder: [[], [Validators.required]],
      bActive: [true],
      fkIIdFormModule: [this.idForm],
      fkIIdProgressBar: [null],
    });

    this.form
      .get('vName')
      ?.valueChanges.pipe(debounceTime(600))
      .subscribe({
        next: (data) => {
          if (data) {
            this.form
              .get('vNameDb')
              ?.setValue(this.utilsSvc.generateNameDb(data));
          } else {
            this.form.get('vNameDb')?.setValue('');
          }
        },
      });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.action = 'edit';
        this.setOrder(true);
        this.getTabModuleById(event.value.pkIIdTabModule);
        this.openModalCreateEditTabFormModule();
        break;
      default:
        break;
    }
  }

  setOrder(edit: boolean){
    if(edit){
      this.orderList = this.utilsSvc.generarArrayOrderList(
        this.dataTableTabsForm.length
      );
    }
    else{
      this.orderList = this.utilsSvc.generarArrayOrderList(
        this.dataTableTabsForm.length + 1
      );
    }
  }

  getProgressBarModuleByIdFormModule(idFormModule: number) {
    this._moduleService
      .getProgressBarModuleByIdFormModule(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if (response.result != undefined) {
              if (response.result.length > 0) {
                this.progressBarList = response.result;
                this.form.get('fkIIdProgressBar')?.enable();
                this.form
                  .get('fkIIdProgressBar')
                  ?.setValidators(Validators.required);
                this.form.get('fkIIdProgressBar')?.updateValueAndValidity();
              } else {
                this.progressBarList = response.result;
                this.form.get('fkIIdProgressBar')?.disable();
                this.form.get('fkIIdProgressBar')?.clearValidators();
                this.form.get('fkIIdProgressBar')?.updateValueAndValidity();
              }
            }
          }
        }
      });
  }

  getTabModuleById(idTabModule: number) {
    this._moduleService
      .getTabModuleById(idTabModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
          } else {
            this.form.patchValue(response.result);
          }
        }
      });
  }

  getTabModuleListByFormId(idFormModule: number) {
    this._moduleService
      .getTabModuleListByFormId(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.dataTableTabsForm = response.result;
            if (response.result.length > 0) {
              this.orderList = this.utilsSvc.generarArrayOrderList(
                response.result.length + 1
              );
            } else {
              this.orderList = this.utilsSvc.generarArrayOrderList(1);
            }
          }
        }
      });
  }

  openModalCreateEditTabFormModule() {
    if (this.action === 'edit') {
      this.titelModal = this._translateService.instant(
        'FormConfiguration.TabModule.TitleModalEdit'
      );
    } else {
      this.titelModal = this._translateService.instant(
        'FormConfiguration.TabModule.TitleModalCreate'
      );
    }
    this.modalDialog.open(this.editNewTabFormModal!, {
      width: '60vw',
      height: 'auto',
    });
    this.getProgressBarModuleByIdFormModule(this.idForm);
  }

  createTabModule() {
    if (this.valid) {
      let payload: TabModuleModel = this.form.getRawValue();
      this._moduleService
        .registerTabModule(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              if (resp.message === 'MessageGenericCreateFormsModules') {
                this._msgSvc.messageInfo(
                  this._translateService.instant(
                    'FormConfiguration.TitleConfirmationMessageCreate'
                  ),
                  this._translateService.instant(
                    'FormConfiguration.SubtitleConfirmationMessageCreate'
                  )
                );
              } else {
                this._msgSvc.messageSuccess(
                  '',
                  this._translateService.instant(
                    'ModulesSetting.SuccessfulMessageCreated'
                  )
                );
              }
              this.getTabModuleListByFormId(this.idForm);
              this.modalDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  editTabModule() {
    if (this.valid) {
      let payload: TabModuleModel = this.form.getRawValue();
      this._moduleService
        .updateTabModule(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageUpdated'
                )
              );
              this.getTabModuleListByFormId(this.idForm);
              this.modalDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  deleteTab() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant(
          'FormConfiguration.TabModule.MessageConfirmationAndNegationFirstPart'
        ),
        this._translateService.instant(
          'FormConfiguration.TabModule.MessageConfirmationAndNegationSecondPart'
        ),
        'info',
        this._translateService.instant(
          'FormConfiguration.TabModule.ButtonmessageConfirmation'
        ),
        this._translateService.instant(
          'FormConfiguration.TabModule.ButtonmessageNegation'
        )
      )
      .then((response) => {
        if (response) {
          this._moduleService
            .deleteTabModule(this.form.get('pkIIdTabModule')?.value)
            .pipe(
              catchError((error) => {
                if (
                  error.error.error &&
                  error.error.message === 'hasAssociatedItems'
                ) {
                  this._msgSvc.messageInfo(
                    this._translateService.instant(
                      'FormConfiguration.deletedMessageTitle'
                    ),
                    this._translateService.instant(
                      'FormConfiguration.deletedMessageSubtitle'
                    )
                  );
                } else {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    error.error.message
                  );
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
                console.log('El tipo de datos devueltos es un array vacío.');
              } else {
                this._msgSvc.messageSuccess(
                  '',
                  this._translateService.instant(
                    'MessageGenericDelete'
                  )
                );
                this.getTabModuleListByFormId(this.idForm);
                this.modalDialog.closeAll();
                this.closeModal(true);
              }
            });
        }
      });
  }

  closeModal(event: boolean) {
    this.form.reset();
    this.form.get('bActive')?.setValue(true);
    this.form.get('pkIIdTabModule')?.setValue(0);
    this.form.get('fkIIdFormModule')?.setValue(this.idForm);
    this.form.get('fkIIdProgressBar')?.setValue(null);
    this.setOrder(false);
    this.action = '';
  }
}
