<form [formGroup]="form" (ngSubmit)="complete()">
  <!-- Estados asociados al formulario -->
  <div class="row mt-5">
    <div class="col-md-4 mb-2 cont-row">
      <mat-form-field
        appearance="outline"
        class="select-look w-50 m-auto w-100"
      >
        <mat-label>
          {{ "FormConfiguration.AssociatedStatesLabel" | translate }}
        </mat-label>
        <mat-select formControlName="stateAsoccied" multiple>
          <mat-option
            *ngFor="let itemS of statusList"
            [value]="itemS.pkIIdStageByState"
            >{{ itemS.vState }}</mat-option
          >
        </mat-select>
        <mat-error
          *ngIf="utilsSvc.isControlHasError(form, 'stateAsoccied', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
     <mat-icon class="click mb-1 ml-1" matTooltipPosition="right" matTooltip="{{ 'Tooltips.FormSelectState' | translate }}">help_outline</mat-icon>
    </div>
  </div>

  <div class="cont-form mt-3 mb-3">
    <div class="cont-title-form mb-3">
      <h3>{{ "FormConfiguration.FormTitle" | translate }}</h3>
    </div>
    <div class="cont-subtitle-form mb-3">
      <h5>{{ "FormConfiguration.FormDataSubtitle" | translate }}</h5>
    </div>

    <div class="form">
      <div class="row">
        <div class="col-md-8 col-sm-12">
          <div class="row">
            <div class="col-md-4">
              <mat-form-field appearance="outline" class="w-100 mb-3">
                <mat-label>
                  {{ "FormConfiguration.FormNameLabel" | translate }}
                </mat-label>
                <input matInput formControlName="vName" />
                <mat-error
                  *ngIf="utilsSvc.isControlHasError(form, 'vName', 'required')"
                >
                  {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-md-4">
              <mat-form-field appearance="outline" class="w-100 mb-3">
                <mat-label>
                  {{ "FormConfiguration.NameInTheSystemLabel" | translate }}
                </mat-label>
                <input matInput formControlName="vNameDb" />
                <mat-error
                  *ngIf="
                    utilsSvc.isControlHasError(form, 'vNameDb', 'required')
                  "
                >
                  {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-md-4">
              <mat-form-field appearance="outline" class="w-100 mb-3">
                <mat-label>
                  {{ "FormConfiguration.NumberOfColumnsLabel" | translate }}
                </mat-label>
                <input matInput formControlName="iAmountColumns" type="number" />
                <mat-error
                  *ngIf="
                    utilsSvc.isControlHasError(
                      form,
                      'iAmountColumns',
                      'required'
                    )
                  "
                >
                  {{ "ThisFieldIsRequired" | translate }}
                </mat-error>

                <mat-error
                  *ngIf="
                    utilsSvc.isControlHasError(
                      form,
                      'iAmountColumns',
                      'pattern'
                    )
                  "
                >
                  {{ "FormConfiguration.ValidateAmountColumns" | translate }}
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <div class="d-flex justify-content-between">
            <div class="">
              <mat-slide-toggle class="mb-3" formControlName="bActive">
                {{ "FormConfiguration.ActiveFormLabel" | translate }}
              </mat-slide-toggle>
            </div>
            <div class="">
              <mat-icon class="click" matTooltipPosition="left" matTooltip="{{ 'Tooltips.FormClone' | translate }}">help_outline</mat-icon>
              <button
                class="mx-3 mb-2"
                type="button"
                mat-raised-button
                [disabled]="!isButtonCopyParameter"
                (click)="openModalcopyExistingParameters()"
              >
                {{ "FormConfiguration.CopyExistingParameters" | translate }}
                <mat-icon iconPositionEnd fontIcon="file_copy"></mat-icon>
              </button>
            </div>
          </div>
        </div>
        <div class="col-md-4 col-sm-12">
          <div class="row cont-btns">
            <div class="col-12">
              <button
                class="width-button"
                type="submit"
                mat-raised-button
                color="primary"
                [disabled]="!valid"
              >
                {{ "FormConfiguration.SaveForm" | translate }}
                <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
              </button>
            </div>

            <div class="col-12">
              <button
                class="mt-2 width-button"
                type="button"
                (click)="preview()"
                mat-raised-button
                color="primary"
                [disabled]="idForm == 0"
              >
                {{ "Product.Preview" | translate }}
                <mat-icon iconPositionEnd fontIcon="remove_red_eye"></mat-icon>
              </button>
            </div>
            <div class="col-12">
              <button
                class="mt-2 download width-button"
                type="button"
                mat-raised-button
                (click)="deleteForm()"
                [disabled]="idForm == 0"
              >
                <strong>{{ "Product.DeleteForm" | translate }}</strong>
                <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="cont-items-form mt-5" *ngIf="idForm !== 0">
      <div class="cont-progress-bar">
        <div class="row mb-2">
          <app-progress-bar-form [idForm]="idForm"></app-progress-bar-form>
        </div>
      </div>
      <div class="cont-tabs">
        <app-tabs-form [idForm]="idForm"></app-tabs-form>
      </div>
      <div class="cont-sections">
        <app-sections-form [idForm]="idForm"></app-sections-form>
      </div>
      <div class="cont-fields">
        <app-fields-form [idForm]="idForm"></app-fields-form>
      </div>
      <div class="cont-pop-up">
        <app-pop-up-form [idForm]="idForm"></app-pop-up-form>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12 d-flex justify-content-center mt-3">
        <button class="mx-3" mat-raised-button type="button" (click)="cancel()">
          {{ "Cancel" | translate }}
        </button>
      </div>
    </div>
  </div>
</form>

<!-- Modal Copiar parámetros existentes -->
<ng-template #copyExistingParametersModal>
  <app-modal2 [titleModal]="titelModal">
    <ng-container body
      ><app-choose-country-and-company
        [viewInRows]="false"
      ></app-choose-country-and-company>
      <div [formGroup]="formFilter" class="row">
        <!-- Modulo -->
        <div class="col-md-12 mb-2">
          <mat-form-field
            appearance="outline"
            class="select-look w-50 m-auto w-100"
          >
            <mat-label>
              {{ "FormsConfigurationHistory.Module" | translate }}
            </mat-label>
            <mat-select formControlName="idModule">
              <mat-option *ngFor="let itemM of moduleList" [value]="itemM">
                {{ itemM.vDescription }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                utilsSvc.isControlHasError(formFilter, 'idModule', 'required')
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Sub modulo -->
        <div class="col-md-12 mb-2">
          <mat-form-field
            appearance="outline"
            class="select-look w-50 m-auto w-100"
          >
            <mat-label>
              {{ "FormsConfigurationHistory.Submodule" | translate }}
            </mat-label>
            <mat-select formControlName="idSubModule">
              <mat-option *ngFor="let itemSM of subModuleList" [value]="itemSM">
                {{ itemSM.vDescription }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                utilsSvc.isControlHasError(
                  formFilter,
                  'idSubModule',
                  'required'
                )
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Producto -->
        <div class="col-md-12 mb-2">
          <mat-form-field
            appearance="outline"
            class="select-look w-50 m-auto w-100"
          >
            <mat-label>
              {{ "FormsConfigurationHistory.Product" | translate }}
            </mat-label>
            <mat-select formControlName="idProductModule">
              <mat-option *ngFor="let itemP of productList" [value]="itemP">
                {{ itemP.productName }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                utilsSvc.isControlHasError(
                  formFilter,
                  'idProductModule',
                  'required'
                )
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Etapa -->
        <div class="col-md-12 mb-2">
          <mat-form-field
            appearance="outline"
            class="select-look w-50 m-auto w-100"
          >
            <mat-label>
              {{ "FormsConfigurationHistory.Stage" | translate }}
            </mat-label>
            <mat-select formControlName="idStage">
              <mat-option *ngFor="let itemST of stageList" [value]="itemST">
                {{ itemST.vNameStage }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                utilsSvc.isControlHasError(formFilter, 'idStage', 'required')
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Estado -->
        <div class="col-md-12 mb-2">
          <mat-form-field
            appearance="outline"
            class="select-look w-50 m-auto w-100"
          >
            <mat-label>
              {{ "FormsConfigurationHistory.State" | translate }}
            </mat-label>
            <mat-select formControlName="idState">
              <mat-option
                *ngFor="let itemS of statusListCopyParameters"
                [value]="itemS.pkIIdStageByState"
                >{{ itemS.vState }}</mat-option
              >
            </mat-select>
            <mat-error
              *ngIf="
                utilsSvc.isControlHasError(formFilter, 'idState', 'required')
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </ng-container>

    <ng-container customButtonRight>
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="cloneFormModuleByIdStateModule()"
      >
        {{ "FormConfiguration.ReplicateConfigurationTitle" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

<!-- Modal Vista Previa -->
<ng-template #previewForm>
  <app-modal2 [titleModal]="titelModal">
    <ng-container body>
      <div class="cont-preview" *ngIf="field.length > 0">
        <app-visualizer-form [data]="field"></app-visualizer-form>
      </div>
    </ng-container>
  </app-modal2>
</ng-template>
