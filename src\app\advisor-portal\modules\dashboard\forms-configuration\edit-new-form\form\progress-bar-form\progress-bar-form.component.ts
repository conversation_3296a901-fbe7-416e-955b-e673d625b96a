import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, debounceTime, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ProgressBarModel } from 'src/app/shared/models/configuration-form';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-progress-bar-form',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    Modal2Component,
    MatSelectModule,
    MatTooltipModule
  ],
  templateUrl: './progress-bar-form.component.html',
  styleUrls: ['./progress-bar-form.component.scss'],
})
export class ProgressBarFormComponent implements OnInit {
  @Input() idForm: number = 0;
  form: FormGroup = new FormGroup({});
  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.ProgressBar.Order'
      ),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.ProgressBar.ProgressBarStepName'
      ),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'edit',
      columnIcon: 'edit',
    },
  ];
  dataTableProgressBar: ProgressBarModel[] = [];
  @ViewChild('editNewProgressBarModal')
  editNewProgressBarModal?: TemplateRef<any>;
  titelModal: string = 'Nuevo paso';
  orderList: number[] = [];
  action: string = 'create';

  constructor(
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public modalDialog: MatDialog,
    public utilsSvc: UtilsService,
    private _fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initForm();
    if (this.idForm !== 0) {
      this.getProgressBarModuleByIdFormModule(this.idForm);
    }
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table modules
      this.estructTable[0].columnLabel = this._translateService.instant(
        'FormConfiguration.ProgressBar.Order'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'FormConfiguration.ProgressBar.ProgressBarStepName'
      );

      this.estructTable[2].columnLabel =
        this._translateService.instant('Status');

      this.estructTable[3].columnLabel =
        this._translateService.instant('Action');
    });
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdProgressBar: [0],
      vName: ['', [Validators.required]],
      vNameDb: { value: '', disabled: true },
      iOrder: [[], [Validators.required]],
      bActive: [true],
      fkIIdFormModule: [this.idForm],
    });

    this.form
      .get('vName')
      ?.valueChanges.pipe(debounceTime(600))
      .subscribe({
        next: (data) => {
          if (data) {
            this.form
              .get('vNameDb')
              ?.setValue(this.utilsSvc.generateNameDb(data));
          } else {
            this.form.get('vNameDb')?.setValue('');
          }
        },
      });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.action = 'edit';
        this.setOrder(true);
        this.getProgressBarModuleById(event.value.pkIIdProgressBar);
        this.openModalCreateEditProgressBar();
        break;
      default:
        break;
    }
  }
  
  setOrder(edit: boolean){
    if(edit){
      this.orderList = this.utilsSvc.generarArrayOrderList(
        this.dataTableProgressBar.length
      );
    }
    else{
      this.orderList = this.utilsSvc.generarArrayOrderList(
        this.dataTableProgressBar.length + 1
      );
    }
  }

  getProgressBarModuleByIdFormModule(idFormModule: number) {
    this._moduleService
      .getProgressBarModuleByIdFormModule(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.dataTableProgressBar = response.result;
            if (response.result.length > 0) {
              this.orderList = this.utilsSvc.generarArrayOrderList(
                response.result.length + 1
              );
            } else {
              this.orderList = this.utilsSvc.generarArrayOrderList(1);
            }
          }
        }
      });
  }

  openModalCreateEditProgressBar() {
    if (this.action === 'edit') {
      this.titelModal = this._translateService.instant(
        'FormConfiguration.ProgressBar.TitleModalEdit'
      );
    } else {
      this.titelModal = this._translateService.instant(
        'FormConfiguration.ProgressBar.TitleModalCreate'
      );
    }
    this.modalDialog.open(this.editNewProgressBarModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  getProgressBarModuleById(pkIIdProgressBar: number) {
    this._moduleService
      .getProgressBarModuleById(pkIIdProgressBar)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.form.patchValue(resp.result);
          }
        }
      });
  }

  createStep() {
    if (this.valid) {
      let payload: ProgressBarModel = this.form.getRawValue();
      this._moduleService
        .registerProgressBarModule(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              if (resp.message === 'MessageGenericCreateFormsModules') {
                this._msgSvc.messageInfo(
                  this._translateService.instant(
                    'FormConfiguration.TitleConfirmationMessageCreate'
                  ),
                  this._translateService.instant(
                    'FormConfiguration.SubtitleConfirmationMessageCreate'
                  )
                );
              } else {
                this._msgSvc.messageSuccess(
                  '',
                  this._translateService.instant(
                    'ModulesSetting.SuccessfulMessageCreated'
                  )
                );
              }
              this.getProgressBarModuleByIdFormModule(this.idForm);
              this.modalDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  editStep() {
    if (this.valid) {
      let payload: ProgressBarModel = this.form.getRawValue();
      this._moduleService
        .updateProgressBarModule(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageUpdated'
                )
              );
              this.getProgressBarModuleByIdFormModule(this.idForm);
              this.modalDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  deleteStep() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant(
          'FormConfiguration.ProgressBar.DeleteMessageConfirm'
        ),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._moduleService
            .deleteProgressBarModule(this.form.get('pkIIdProgressBar')?.value)
            .pipe(
              catchError((error) => {
                if (
                  error.error.error &&
                  error.error.message === 'hasAssociatedItems'
                ) {
                  this._msgSvc.messageInfo(
                    this._translateService.instant(
                      'FormConfiguration.deletedMessageTitle'
                    ),
                    this._translateService.instant(
                      'FormConfiguration.deletedMessageSubtitle'
                    )
                  );
                } else {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    error.error.message
                  );
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
                console.log('El tipo de datos devueltos es un array vacío.');
              } else {
                if (resp.error) {
                  this._msgSvc.messageError(
                    this._translateService.instant('Warning') + resp.message
                  );
                } else {
                  this._msgSvc.messageSuccess(
                    '',
                    this._translateService.instant(
                      'FormConfiguration.ProgressBar.DeleteMessageSuccess'
                    )
                  );
                  this.getProgressBarModuleByIdFormModule(this.idForm);
                  this.modalDialog.closeAll();
                  this.closeModal(true);
                }
              }
            });
        }
      });
  }

  closeModal(event: boolean) {
    this.form.reset();
    this.form.get('bActive')?.setValue(true);
    this.form.get('pkIIdProgressBar')?.setValue(0);
    this.form.get('fkIIdFormModule')?.setValue(this.idForm);
    this.action = '';
    this.setOrder(false);
  }
}
