<form [formGroup]="form" class="mt-3">
  <div class="cont-colors-definition">
    <div class="row">
      <!-- Primary color -->
      <div class="col-md-6">
        <div class="row">
          <div class="col-md-12">
            <h6>{{ "SettingColors.MainColor" | translate }}</h6>
            <p>
              {{ "SettingColors.DescriptionPrimaryColor" | translate }}
            </p>
          </div>

          <div class="row mt-3">
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-4 cont-color">
                  <div class="colors" [style.background]="primaryColor"></div>
                </div>
                <div class="col-md-8">
                  <div class="row">
                    <div class="col-md-12">
                      <h6>HEX</h6>
                    </div>
                    <div class="col-md-12">
                      <mat-form-field appearance="outline"
                                      class="w-100 mb-2 mat-small">
                        <mat-label>
                          {{ "SettingColors.LabelPrimaryColor" | translate }}
                        </mat-label>
                        <input (input)="showColors()"
                               matInput
                               formControlName="primaryColor"
                               PreventionSqlInjector />
                        <mat-error *ngIf="
                            utilsSvc.isControlHasError(
                              form,
                              'primaryColor',
                              'required'
                            )
                          ">
                          {{ "ThisFieldIsRequired" | translate }}
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Secondary color -->
      <div class="col-md-6">
        <div class="row">
          <div class="col-md-12">
            <h6>{{ "SettingColors.SecondaryColor" | translate }}</h6>
            <p>{{ "SettingColors.DescriptionSecondaryColor" | translate }}</p>
          </div>
          <div class="col-md-12">
            <div class="row mt-2">
              <div class="col-md-4 cont-color">
                <div class="colors" [style.background]="secondaryColor"></div>
              </div>
              <div class="col-md-8">
                <div class="row">
                  <div class="col-md-12">
                    <h6>HEX</h6>
                  </div>
                  <div class="col-md-12">
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                      <mat-label>
                        {{ "SettingColors.SecondaryColor" | translate }}
                      </mat-label>
                      <input (input)="showSecondaryColor()"
                             matInput
                             formControlName="secondaryColor"
                             PreventionSqlInjector />
                      <mat-error *ngIf="
                          utilsSvc.isControlHasError(
                            form,
                            'secondaryColor',
                            'required'
                          )
                        ">
                        {{ "ThisFieldIsRequired" | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sombra -->
      <div class="col-md-12 mb-2">
        <div class="row">
          <div class="col-md-12">
            <div class="row mt-2">
              <div class="col-md-4 cont-shade">
                <div class="colors-shade" [style.background]="shadeColor"></div>
              </div>
              <div class="col-md-8">
                <div class="row">
                  <div class="col-md-12 mb-2">
                    <span>{{ "SettingColors.Shade" | translate }}</span>
                  </div>
                  <div class="col-md-12">{{ shadeColor }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tinte -->
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="row mt-2">
              <div class="col-md-4 cont-shade">
                <div class="colors-shade" [style.background]="tintColor"></div>
              </div>
              <div class="col-md-8">
                <div class="row">
                  <div class="col-md-12 mb-2">
                    <span>{{ "SettingColors.Tint" | translate }}</span>
                  </div>
                  <div class="col-md-12">{{ tintColor }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Fuente -->
      <div class="col-md-12">
        <div class="row">
          <h4>{{ "SettingColors.Font" | translate }}</h4>
        </div>
        <div class="row">
          <div class="col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2 mat-small">
              <mat-label>
                {{ "SettingColors.Font" | translate }}
              </mat-label>
              <mat-select formControlName="font" (selectionChange)="fontChange()">
                <mat-option *ngFor="let font of fontList"
                            [value]="font.pkIIdFont">
                  {{ font.vName }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="utilsSvc.isControlHasError(form,'font','required')">
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
