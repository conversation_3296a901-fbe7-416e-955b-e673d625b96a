import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { ActivatedRoute,Router  } from '@angular/router';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ManagementHistoryModel } from 'src/app/shared/models/task';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-management-history-table',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './management-history-table.component.html',
  styleUrls: ['./management-history-table.component.scss'],
})
export class ManagementHistoryTableComponent implements OnInit {
  //idTask obtenido por url
  idTask: number = 0;
  //Estructura de la tabla
  estructTableManagementHistory: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'TaskTray.ManagementHistory.Table.WhoModifies'
      ),
      columnValue: 'vUserName',
      functionValue: (item: any) => this._translateService.instant(item.vUserName),
    },
    {
      columnLabel: this._translateService.instant(
        'TaskTray.ManagementHistory.Table.ModificationDate'
      ),
      columnValue: 'dDateCreation',
    },
    // {
    //   columnLabel: 'Gestión',
    //   columnValue: '3',
    // },
    {
      columnLabel: this._translateService.instant(
        'TaskTray.ManagementHistory.Table.Stage'
      ),
      columnValue: 'vStage',
    },
    // {
    //   columnLabel: this._translateService.instant(
    //     'Observations'
    //   ),
    //   columnValue: 'Observation',
    // },
    {
      columnLabel: this._translateService.instant(
        'TaskTray.ManagementHistory.Table.State'
      ),
      columnValue: 'vState',
    },
    {
      columnLabel: this._translateService.instant(
        'TaskTray.ManagementHistory.Table.SeeDetail'
      ),
      columnValue: 'seeDetails',
      columnIcon: 'search',
    },
  ];
  //Variable que almacena los datos de la tabla.
  @Input() dataTableManagementHistory: ManagementHistoryModel[] = [];

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _translateService: TranslateService,
    private _customRouter: CustomRouterService,

  ) {}

  ngOnInit(): void {
    console.log(this.dataTableManagementHistory)
   // this.getIdTask();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table business
      this.estructTableManagementHistory[0].columnLabel =
        this._translateService.instant(
          'TaskTray.ManagementHistory.Table.WhoModifies'
        );
      this.estructTableManagementHistory[1].columnLabel =
        this._translateService.instant(
          'TaskTray.ManagementHistory.Table.ModificationDate'
        );
      this.estructTableManagementHistory[2].columnLabel =
        this._translateService.instant(
          'TaskTray.ManagementHistory.Table.Stage'
        );
      // this.estructTableManagementHistory[3].columnLabel =
      //   this._translateService.instant(
      //     'Observations'
      //   );
      this.estructTableManagementHistory[4].columnLabel =
        this._translateService.instant(
          'TaskTray.ManagementHistory.Table.State'
        );
      this.estructTableManagementHistory[5].columnLabel =
        this._translateService.instant(
          'TaskTray.ManagementHistory.Table.SeeDetail'
        );
    });
  }




  //Función que permite gestionar las acciones de la tabla
  controller(evt: IconEventClickModel) {
    this._activatedRoute.params.subscribe((params) => {
      if (params['idTaskState']) {
        // Aquí obtienes todos los parámetros actuales
        const currentParams = { ...params };
  
        // Modificas el parámetro 'idTask' según tus necesidades
        currentParams['idTaskState'] = evt.value.pkIIdTaskState;
        currentParams['idStateModule'] = evt.value.fkIIdStateModule;
  
        // Rediriges a la misma ruta con los parámetros actualizados
        this._customRouter.navigate(
          ['/dashboard/task-tray/'+this._activatedRoute.snapshot.routeConfig?.path?.replace(/:([^\/]+)/g, (match, p1) => currentParams[p1])],
          { queryParams: this._activatedRoute.snapshot.queryParams }
        ).then(() => {
          window.location.reload();
        });;
      }
    });
  }
}
