<form [formGroup]="formSection">
  <div class="row">
    <div class="col-12 col-md-6">
      <mat-form-field class="w-100" appearance="fill">
        <mat-label>
          {{ "SectionClientPortal.Title" | translate }}
        </mat-label>
        <input formControlName="vTitle" matInput PreventionSqlInjector />
        <mat-error *ngIf="utilsSvc.isControlHasError(formSection, 'vTitle', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field class="w-100" appearance="fill">
        <mat-label>
          {{ "SectionClientPortal.Subtitle" | translate }}
        </mat-label>
        <input formControlName="vSubtitle" matInput PreventionSqlInjector />
        <mat-error *ngIf="utilsSvc.isControlHasError(formSection, 'vSubtitle', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <div class="w-100">
        <app-upload-image-varies-ext [showUploadedImage]="false" [showBtnDownload]="false"
                                     (changeFile)="changeFile($event)" (deleteFile)="deleteFile($event)">
        </app-upload-image-varies-ext>
      </div>

      <div class="container group-flow">
        <mat-checkbox formControlName="bHasButton" (change)="hasButtonChange($event)">
          {{ "SectionClientPortal.HasButton" | translate}}
        </mat-checkbox>
      </div>

      <ng-container *ngIf="hasButton">
        <mat-form-field class="w-100" appearance="fill">
          <mat-label>
            {{ "SectionClientPortal.ButtonText" | translate }}
          </mat-label>
          <input formControlName="vButtonText" matInput PreventionSqlInjector />
        </mat-form-field>

        <mat-form-field class="w-100" appearance="fill">
          <mat-label>
            {{ "SectionClientPortal.ButtonLink" | translate }}
          </mat-label>
          <input formControlName="vButtonLink" matInput PreventionSqlInjector />
        </mat-form-field>
      </ng-container>
    </div>
  </div>

  <div class="d-flex justify-content-center">
    <div class="mt-3">
      <button class="mx-2" (click)="cancel()" type="button" mat-raised-button>
        {{ "Cancel" | translate }}
      </button>
      <button (click)="saveChanges()" type="button" mat-raised-button color="primary" [disabled]="!formSection.valid">
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "SaveChanges" | translate }}
      </button>
    </div>
  </div>
</form>
