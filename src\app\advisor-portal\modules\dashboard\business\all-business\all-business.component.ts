import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import {
  Component,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatDialog } from '@angular/material/dialog';
import { Subscription, findIndex } from 'rxjs';

import { TableComponent } from 'src/app/shared/components/table/table.component';
import { EditNewBusinessGroupComponent } from '../edit-new-business-group/edit-new-business-group.component';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';

import { BusinessService } from 'src/app/shared/services/business/business.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MessageService } from 'src/app/shared/services/message/message.service';

import { IconEventClickModel } from 'src/app/shared/models/table/icon-event-click.model';
import { BodyTableModel } from 'src/app/shared/models/table';
import {
  BusinessGroupListModel,
  BusinessListModel,
} from 'src/app/shared/models/business';
import { ResponseGlobalModel } from 'src/app/shared/models/response';

import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-all-business',
  standalone: true,
  imports: [
    CommonModule,
    EditNewBusinessGroupComponent,
    TableComponent,
    RouterModule,
    MatInputModule,
    MatIconModule,
    ModalComponent,
    TranslateModule,
    MatButtonModule,
    MatTooltipModule
  ],
  templateUrl: './all-business.component.html',
  styleUrls: [],
})
export class AllBusinessComponent implements OnInit, OnDestroy {
  dataBusinessGroupId: number = 0;
  tittleModalText: string = '';
  notApplicable: string = this._translateService.instant('NotApplicable');
  formSearch: FormGroup = this._fb.group({
    searchBar: [null],
  });

  businessList?: Subscription;
  businessGroupList?: Subscription;

  estructTableBusiness: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Business.Name'),
      columnValue: 'v_Name',
    },
    {
      columnLabel: this._translateService.instant('Business.RegistryNumber'),
      columnValue: 'v_Document',
    },
    {
      columnLabel: this._translateService.instant('Business.BusinessGroup'),
      columnValue: 'v_BusinessGroupName',
    },
    {
      columnLabel: this._translateService.instant('Business.CreationDate'),
      columnValue: 'd_CreationDate',
    },
    {
      columnLabel: this._translateService.instant('Business.Country'),
      columnValue: 'v_CountryName',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'b_Active',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyBusiness',
      columnIcon: 'edit',
    },
  ];

  dataTableBusiness: BusinessListModel[] = [];

  estructTableBusinessGroup: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Business.BusinessGroup'),
      columnValue: 'v_BusinessGroupName',
    },
    {
      columnLabel: this._translateService.instant('Business.BusinessName'),
      columnValue: 'v_BusinessName',
    },
    {
      columnLabel: this._translateService.instant('Business.RegistryNumber'),
      columnValue: 'v_Document',
    },
    {
      columnLabel: this._translateService.instant('Business.CreationDate'),
      columnValue: 'd_CreationDate',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'b_Active',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyBusinessGroup',
      columnIcon: 'edit',
    },
  ];

  dataTableBusinessGruop: BusinessGroupListModel[] = [];

  @ViewChild('editNewBusinessGroupModal')
  editNewBusinessGroupModal?: TemplateRef<any>;

  constructor(
    private _businessService: BusinessService,
    private _messageService: MessageService,
    private _fb: FormBuilder,

    public _router: Router,
    public _editNewBusinessGroupDialog: MatDialog,
    public _utilsSvc: UtilsService,
    public _translateService: TranslateService,
    private _customRouter: CustomRouterService,

  ) {}

  ngOnInit(): void {
    this.getBusinessList();
    this.getBusinessGroup();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //texto no aplica
      this.notApplicable = this._translateService.instant('NotApplicable');
      //traduccion de texto no aplica en tabla business
      for (let item of this.dataTableBusiness) {
        if (
          item.v_BusinessGroupName == undefined ||
          item.v_BusinessGroupName == 'Not applicable' ||
          item.v_BusinessGroupName == 'No aplica'
        ) {
          item.v_BusinessGroupName = this.notApplicable;
        }
      }

      //traduccion data table business
      this.estructTableBusiness[0].columnLabel =
        this._translateService.instant('Business.Name');
      this.estructTableBusiness[1].columnLabel = this._translateService.instant(
        'Business.RegistryNumber'
      );
      this.estructTableBusiness[2].columnLabel = this._translateService.instant(
        'Business.BusinessGroup'
      );
      this.estructTableBusiness[3].columnLabel = this._translateService.instant(
        'Business.CreationDate'
      );
      this.estructTableBusiness[4].columnLabel =
        this._translateService.instant('Business.Country');
      this.estructTableBusiness[5].columnLabel =
        this._translateService.instant('Status');
      this.estructTableBusiness[6].columnLabel =
        this._translateService.instant('Modify');

      //traduccion data table businessGroup
      this.estructTableBusinessGroup[0].columnLabel =
        this._translateService.instant('Business.BusinessGroup');
      this.estructTableBusinessGroup[1].columnLabel =
        this._translateService.instant('Business.BusinessName');
      this.estructTableBusinessGroup[2].columnLabel =
        this._translateService.instant('Business.RegistryNumber');
      this.estructTableBusinessGroup[3].columnLabel =
        this._translateService.instant('Business.CreationDate');
      this.estructTableBusinessGroup[4].columnLabel =
        this._translateService.instant('Status');
      this.estructTableBusinessGroup[5].columnLabel =
        this._translateService.instant('Modify');
    });
  }

  ngOnDestroy(): void {
    this.businessList?.unsubscribe();
    this.businessGroupList?.unsubscribe();
  }

  search(evt: string) {
    this.getBusinessList(evt);
  }

  controllerBusiness(evt: IconEventClickModel) {
    this._customRouter.navigate([
      '/dashboard/business/modify/' + evt.value.pk_i_IdBusiness,
    ]);
  }

  controllerBusinessGruop(evt: IconEventClickModel) {
    this.openEditNewBusinessGroupDialog(evt.value.pk_i_IdBusinessGroup);
    this.tittleModalText = this._translateService.instant(
      'Business.ModifyBusinessGroup'
    );
  }

  getSubmitData(event: ResponseGlobalModel) {
    if (!event.error) {
      this._editNewBusinessGroupDialog.closeAll();
      this.getBusinessGroup();
      this._messageService.messageSuccess(
        this._translateService.instant('Created'),
        this._translateService.instant(
          'Business.YouCanFindItInTheBusinessSettings'
        )
      );
    } else {
      this._messageService.messageError(
        this._translateService.instant('ThereWasAError') + event.message
      );
    }
  }

  openEditNewBusinessGroupDialog(id: number = 0) {
    this.tittleModalText = this._translateService.instant(
      'Business.NewBusinessGroup'
    );
    this.dataBusinessGroupId = id;
    const dialogRef = this._editNewBusinessGroupDialog.open(
      this.editNewBusinessGroupModal!,
      {
        width: '40vw',
        maxHeight: '90vh',
      }
    );
    dialogRef.afterClosed().subscribe((result) => {
      console.log(`Dialog result: ${result}`);
    });
  }

  getBusinessList(keyWord: string = '') {
    //si no se ha ingresado nada en la caja de texto de buscar
    if (keyWord == '') {
      this.businessList = this._businessService
        .getBusinessList()
        .subscribe((resp) => {
          this.dataTableBusiness = resp.result;
          for (let item of this.dataTableBusiness) {
            if (item.v_BusinessGroupName == undefined) {
              item.v_BusinessGroupName = this.notApplicable;
            }
          }
        });
    }
    //al ingresar valores en la caja de texto de buscar
    else {
      this.businessList = this._businessService
        .getBusinessListSearch(keyWord)
        .subscribe((resp) => {
          this.dataTableBusiness = resp.result;
          for (let item of this.dataTableBusiness) {
            if (item.v_BusinessGroupName == undefined) {
              item.v_BusinessGroupName = this.notApplicable;
            }
          }
        });
    }
  }

  getBusinessGroup() {
    this.businessGroupList = this._businessService
      .getBusinessGroup()
      .subscribe((resp) => {
        this.dataTableBusinessGruop = resp.result;
      });
  }

  addBusiness() {
    this._customRouter.navigate([`/dashboard/business/new`]);
  }
}
