<ng-container *ngIf="data">
  <div [formGroup]="formBuild" class="cont-visualizer mt-3" *ngFor="let item of data">
    <!-- <PERSON><PERSON><PERSON> cu<PERSON>do hay pasos -->
    <app-wizard *ngIf="
        item.progressBarModules.length >= 1 &&
        item.progressBarModules[0].pkIIdProgressBar &&
        item
      " [steps]="steps" [currentStep]="currentStep" (currentStepChange)="currentStepChange($event)"
      (next)="next($event)" (back)="back($event)" [stepToCompare]="steps[currentStep]">
      <ng-container stepBody [ngSwitch]="currentStep">
        <div>
          <ng-container *ngFor="
              let progressBar of item.progressBarModules;
              let iProgressBar = index
            ">
            <div *ngSwitchCase="iProgressBar">
              <mat-tab-group class="mt-5 mb-5">
                <ng-container *ngFor="let tab of progressBar.tabModules">
                  <mat-tab *ngIf="tab.pkIIdTabModule" label="{{ tab.vName }}">
                    <ng-container *ngFor="let section of tab.sectionModules">
                      <div class="row" *ngIf="section.pkIIdSectionModule; else notSection">
                        <div class="row mt-3 mb-3">
                          <div class="col-md-12 col-sm-12">
                            <h1>{{ section.vName }}</h1>
                          </div>
                          <ng-container *ngFor="let field of section.fieldModules">
                            <div class="col-md-{{
                                12 / item.iAmountColumns
                              }} mt-3 mb-3" *ngIf="field" [hidden]="field.bIsInvisible">
                              <app-field-generator [isQuote]="false" [field]="field"
                                *ngIf="field.pkIIdFieldModule"></app-field-generator>
                            </div>
                          </ng-container>
                        </div>
                      </div>
                      <ng-template #notSection>
                        <ng-container *ngFor="let field of section.fieldModules">
                          <div class="row mt-3 mb-3" *ngIf="field">
                            <div class="col-{{ 12 / item.iAmountColumns }}" [hidden]="field.bIsInvisible">
                              <app-field-generator [isQuote]="false" [field]="field"
                                *ngIf="field.pkIIdFieldModule"></app-field-generator>
                            </div>
                          </div>
                        </ng-container>
                      </ng-template>
                    </ng-container>
                  </mat-tab>
                </ng-container>
              </mat-tab-group>

              <ng-container *ngIf="progressBar.tabModules[0].pkIIdTabModule === null">
                <ng-container *ngFor="
                    let section of progressBar.tabModules[0].sectionModules
                  ">
                  <div class="row mt-3 mb-3">
                    <div class="col-md-12 col-sm-12">
                      <h1>{{ section.vName }}</h1>
                    </div>
                    <ng-container *ngFor="let field of section.fieldModules">
                      <div class="col-md-{{ 12 / item.iAmountColumns }} mt-3 mb-3" *ngIf="field" [hidden]="field.bIsInvisible">
                        <app-field-generator [isQuote]="false" [field]="field"
                          *ngIf="field.pkIIdFieldModule"></app-field-generator>
                      </div>
                    </ng-container>
                  </div>
                </ng-container>
              </ng-container>
            </div>
          </ng-container>
        </div>
        <div *ngSwitchDefault>Default</div>
      </ng-container>

      <ng-container customButtonInit>
        <button mat-raised-button (click)="goToBack()">
          <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
          Cancelar
        </button>
      </ng-container>
    </app-wizard>

    <!-- Caso cuándo no hay pasos -->
    <ng-container *ngIf="item.progressBarModules[0].pkIIdProgressBar === null">
      <div>
        <div *ngFor="let progressBar of item.progressBarModules">
          <mat-tab-group class="mt-5 mb-5">
            <ng-container *ngFor="let tab of progressBar.tabModules">
              <mat-tab *ngIf="tab.pkIIdTabModule" label="{{ tab.vName }}">
                <ng-container *ngFor="let section of tab.sectionModules">
                  <div class="row" *ngIf="section.pkIIdSectionModule; else notSection">
                    <div class="row mt-3 mb-3">
                      <div class="col-md-12 col-sm-12">
                        <h1>{{ section.vName }}</h1>
                      </div>
                      <ng-container *ngFor="let field of section.fieldModules">
                        <div class="col-md-{{
                            12 / item.iAmountColumns
                          }} mt-3 mb-3" *ngIf="field" [hidden]="field.bIsInvisible">
                          <app-field-generator [isQuote]="false" [field]="field"
                            *ngIf="field.pkIIdFieldModule"></app-field-generator>
                        </div>
                      </ng-container>
                    </div>
                  </div>
                  <ng-template #notSection>
                    <ng-container *ngFor="let field of section.fieldModules">
                      <div class="row mt-3 mb-3" *ngIf="field">
                        <div class="col-{{ 12 / item.iAmountColumns }}" [hidden]="field.bIsInvisible">
                          <app-field-generator [isQuote]="false" [field]="field"
                            *ngIf="field.pkIIdFieldModule"></app-field-generator>
                        </div>
                      </div>
                    </ng-container>
                  </ng-template>
                </ng-container>
              </mat-tab>
            </ng-container>
          </mat-tab-group>

          <ng-container *ngIf="progressBar.tabModules[0].pkIIdTabModule === null">
            <ng-container *ngIf="
                progressBar.tabModules[0].sectionModules[0].pkIIdSectionModule
              ">
              <ng-container *ngFor="let section of progressBar.tabModules[0].sectionModules">
                <div class="row mt-3 mb-3">
                  <div class="col-md-12 col-sm-12">
                    <h1>{{ section.vName }}</h1>
                  </div>
                  <ng-container *ngFor="let field of section.fieldModules">
                    <div class="col-md-{{ 12 / item.iAmountColumns }} mt-3 mb-3" *ngIf="field" [hidden]="field.bIsInvisible">
                      <app-field-generator [isQuote]="false" [field]="field"
                        *ngIf="field.pkIIdFieldModule"></app-field-generator>
                    </div>
                  </ng-container>
                </div>
              </ng-container>
            </ng-container>

            <ng-container *ngIf="
                progressBar.tabModules[0].sectionModules[0]
                  .pkIIdSectionModule === null
              ">
              <ng-container *ngFor="let section of progressBar.tabModules[0].sectionModules">
                <div class="row mt-3 mb-3">
                  <div class="col-md-12 col-sm-12">
                    <h1>{{ section.vName }}</h1>
                  </div>
                  <ng-container *ngFor="let field of section.fieldModules">
                    <div class="col-md-{{ 12 / item.iAmountColumns }} mt-3 mb-3" *ngIf="field" [hidden]="field.bIsInvisible">
                      <app-field-generator [isQuote]="false" [field]="field"
                        *ngIf="field.pkIIdFieldModule"></app-field-generator>
                    </div>
                  </ng-container>
                </div>
              </ng-container>
            </ng-container>
          </ng-container>
        </div>
      </div>
    </ng-container>
  </div>
</ng-container>