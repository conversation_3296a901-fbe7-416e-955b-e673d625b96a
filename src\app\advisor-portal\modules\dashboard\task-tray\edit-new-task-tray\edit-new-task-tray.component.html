<div class="cont-company-country-history">
  <app-forms-configuration-history [arrayIn]="arrayCompanyHistoryOut"></app-forms-configuration-history>
</div>
<mat-tab-group  [selectedIndex]="selectedIndex"  (selectedTabChange)="getStageByState($event)">
  <mat-tab *ngFor="let item of stageList; let idx = index"
           (click)="restartViewing()"
           class="tabs3"
           label="{{ item.vNameStage }}"
           [id]="item.pkIIdStage"
           [disabled]="item.bHasParent || isCreationTask">
    <form [formGroup]="form">
      <!-- estado -->
      <div class="state-select">
        <mat-form-field appearance="outline" class="select-look w-50 mt-3">
          <mat-label>
            {{ "Status" | translate }}
          </mat-label>
          <mat-select formControlName="state">
            <ng-container *ngFor="let item of stateList">
              <mat-option [value]="item.pkIIdStageByState" disabled="{{ item.bHasParent }}" *ngIf="isViewing || (!isViewing && item.bActive)">
                {{ item.vState }}
              </mat-option>
            </ng-container>
          </mat-select>
        </mat-form-field>
      </div>
    </form>

    <form [formGroup]="formQuote" *ngIf="idx == 0 && process == 'Cotización' && idProduct != 0 && idQuotation != 0">
      <mat-form-field class="w-100 field-input">
        <mat-label>Oferta Seleccionada</mat-label>
        <input matInput formControlName="quotePlan" type="text" class="form-control" [(ngModel)]="quotePlan" >
      </mat-form-field>
      <mat-form-field class="w-100 field-input">
        <mat-label>Aseguradora</mat-label>
        <input matInput formControlName="quoteInsurance" type="text" class="form-control" [(ngModel)]="quoteInsurance" >
      </mat-form-field>
      <mat-form-field class="w-100 field-input">
        <mat-label>Número de cotización</mat-label>
        <input matInput formControlName="quotNumber" type="text" class="form-control" [(ngModel)]="quotNumber" >
      </mat-form-field>
    </form>

    <app-quotation-form *ngIf="idx == 0 && process == 'Cotización' && idProduct != 0 && idQuotation != 0"
                        [idProduct]="idProduct"
                        [assignData]="true"
                        [businessByCountry]="idBusinessByCountry"
                        [idQuotation]="idQuotation">
    </app-quotation-form>
    <app-quotation-form *ngIf="
      isStateSelected('ST-EM', 'EM-EM', item.vCode) &&
      process == 'Cotización' && 
      idProduct != 0 && 
      idQuotation != 0 && 
      formEmitId !== 0 && 
      idInsuranceCompany !== 0 && 
      formValueEmitId !== 0"
        [idProduct]="idProduct"
        [assignData]="true"
        [businessByCountry]="idBusinessByCountry"
        [idFormFlow]="formEmitId"
        [idQuotation]="idQuotation"
        [idInsuranceCompany]="idInsuranceCompany"
        [idFormValueEmit]="formValueEmitId">
    </app-quotation-form>
    <app-policy-data *ngIf="
      isStateSelected('ST-PA', 'PA-PA', item.vCode) &&
      process == 'Cotización' && 
      idProduct != 0 && 
      idQuotation != 0"
      [quoteId]="idQuotation"
      >
    </app-policy-data>

    <app-dynamic-component
      *ngIf="this.isConsultingForm"
      [isViewing]="isViewing"
      [isInternal]="false"
      [formComplete]="this.formModel"
      [IdStateByStage]="this.stateSelected"
      [idTaskStateEditing]="this.idTaskState"
      [idTaskEditing]="this.idTask"
      [formulasCalculated]="formulasCalculated"></app-dynamic-component>
  </mat-tab>
</mat-tab-group>
<div *ngIf="dataTableManagementHistory.length>0" class="mt-5 mb-5">
  <app-management-history-table [dataTableManagementHistory]="dataTableManagementHistory"></app-management-history-table>
</div>
