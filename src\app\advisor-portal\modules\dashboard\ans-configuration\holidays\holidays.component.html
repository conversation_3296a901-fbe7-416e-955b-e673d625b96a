<div class="row mt-4">
  <div class="col-md-3">
    <!--Select de paises -->
    <mat-form-field appearance="outline" class="select-look w-100">
      <mat-label> {{ "SelectCountry" | translate }} * </mat-label>
      <mat-select
        [(ngModel)]="countrySelect"
        (ngModelChange)="getHolidaysByIdCountry()"
      >
        <mat-option
          *ngFor="let country of countries"
          [value]="country.pk_i_IdCountry"
        >
          {{ country.v_CountryName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
</div>

<div class="row mt-3 mb-2">
  <h5>{{ "AnsConfiguration.Tabs.Holidays" | translate }}</h5>
</div>

<div class="row mt-2">
  <app-table
    [displayedColumns]="estructHolidaysTable"
    [data]="dataHolidaysTable"
    (iconClick)="controller($event)"
  ></app-table>
</div>

<div class="row">
  <div class="col-md-12">
    <button
      type="button"
      class="w-auto"
      (click)="openModal()"
      mat-raised-button
      color="primary"
      [disabled]="countrySelect <= 0"
    >
      {{ "AnsConfiguration.Holidays.AddHolidayButton" | translate }}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
</div>

<!-- Modal Filtro -->
<ng-template #AddHolidaysModal>
  <app-modal2 [titleModal]="modalTitle" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="form">
        <!-- Activo -->
        <div class="switches mt-3 mb-2">
          <mat-slide-toggle class="mb-3" formControlName="bActive">
            {{ "Active" | translate }}
          </mat-slide-toggle>
        </div>

        <mat-form-field class="w-100">
          <mat-label>
            {{ "MyQuotation.AllQuotation.Date" | translate }}
          </mat-label>
          <input
            matInput
            [matDatepicker]="dDateHolidays"
            formControlName="dDateHolidays"
          />

          <mat-datepicker-toggle
            matIconSuffix
            [for]="dDateHolidays"
          ></mat-datepicker-toggle>
          <mat-datepicker #dDateHolidays></mat-datepicker>
        </mat-form-field>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button
        *ngIf="pkIIdHolidaysAns <= 0"
        (click)="addHolidays()"
        type="button"
        mat-raised-button
        color="primary"
      >
        {{ "AnsConfiguration.Holidays.AddHolidayButton" | translate }}

        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
      <button
        *ngIf="pkIIdHolidaysAns > 0"
        (click)="editHolidays()"
        type="button"
        mat-raised-button
        color="primary"
      >
        {{ "Save" | translate }}

        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
