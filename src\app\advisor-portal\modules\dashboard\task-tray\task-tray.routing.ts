import { Routes } from '@angular/router';
import { TaskTrayComponent } from './task-tray.component';

export default [
  {
    path: '',
    component: TaskTrayComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-task-tray/all-task-tray.component').then(
            (c) => c.AllTaskTrayComponent
          ),
      },
      {
        path: 'new/:idProductModule/:process/:product',
        loadComponent: () =>
          import('./edit-new-task-tray/edit-new-task-tray.component').then(
            (c) => c.EditNewTaskTrayComponent
          ),
      },
      {
        path: 'new/:idProductModule/:process/:product/:idTask/:idTaskState/:idStateModule',
        loadComponent: () =>
          import('./edit-new-task-tray/edit-new-task-tray.component').then(
            (c) => c.EditNewTaskTrayComponent
          ),
      },
      {
        path: 'new/:idProductModule/:process/:product/:idTask/:idTaskState/:idStateModule/:idProduct',
        loadComponent: () =>
          import('./edit-new-task-tray/edit-new-task-tray.component').then(
            (c) => c.EditNewTaskTrayComponent
          ),
      }
    ],
  },
] as Routes;
