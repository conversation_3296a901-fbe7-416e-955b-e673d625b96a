import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { DragDropUploadComponent } from 'src/app/shared/components/drag-drop-upload/drag-drop-upload.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GenericButtonsComponent } from 'src/app/shared/components/generic-buttons/generic-buttons.component';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { catchError, Observable, of, Subscription } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BulkUploadCatalogDto } from 'src/app/shared/models/composite-catalog/BulkUpload.model';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ImageUploadModel } from 'src/app/shared/models/file';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
@Component({
  selector: 'app-upload-composite-catalog',
  standalone: true,
  imports: [
    CommonModule,
    DragDropUploadComponent,
    TranslateModule,
    GenericButtonsComponent,
    MatIconModule,
    MatButtonModule
  ],
  templateUrl: './upload-composite-catalog.component.html',
  styleUrls: ['./upload-composite-catalog.component.scss']
})
export class UploadCompositeCatalogComponent implements OnDestroy, OnInit {


  @Input() idHistoryCatalog: number = 0;
  @Output() setActiveTabCatalog = new EventEmitter<any>();
  private _registerOrUpdateFunction!: (
    payloads: any
  ) => Observable<ResponseGlobalModel>;
  private _settingCompositeCatalogSubscription?: Subscription;

  dataJson: any = {};
  FkIdHistoryCatalog: number = 0;
  files: any;
  attachments: any;
  dataRequest!: BulkUploadCatalogDto;
  jsonExcel: any;

  constructor(
    private _fileService: FileService,
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    private _catalogService: CatalogSettingService,
    public _location: Location,
    public _utilsSvc: UtilsService,
    public modalDialog: MatDialog,
    private _customRouter: CustomRouterService,
    private _messageService: MessageService,
  ) { }

  ngOnInit(): void {
    this.getSettinCompositeCatalogSubscription();
  }

  getSettinCompositeCatalogSubscription() {
    this._settingCompositeCatalogSubscription =
      this._catalogService.currentCompositeSetting.subscribe((response) => {
        if (!(Object.keys(response).length === 0)) {
          this.dataJson = response;
          this.validatedFunctionServices();
        }
      });
  }

  validatedFunctionServices() {
    if (
      this.dataJson !== undefined &&
      this.dataJson !== null &&
      this.dataJson !== ''
    ) {
      if (this.dataJson.pkIIdHistoryCatalog > 0)
        this._registerOrUpdateFunction =
          this._catalogService.updateHistoryCatalog.bind(this._catalogService);
      else
        this._registerOrUpdateFunction =
          this._catalogService.registerHistoryCatalog.bind(
            this._catalogService
          );
    }
  }

  async saveConfigCatalogComposite() {
    if (this.files.length == 0) {
      this._msgSvc.messageWaring('CatalogSetting.MessagesCatalogo.Warning', this._translateService.instant(
        'CatalogSetting.MessagesCatalogo.SelectFile'
      ))
      return
    }

    this.dataJson = this.organizeColumnsById(this.dataJson);
    let payload: any;
    payload = {
      pkIIdHistoryCatalog: this.dataJson.pkIIdHistoryCatalog,
      vValueConfigCatalog: JSON.stringify(this.dataJson),
      fkIIdUploadFile: await this.saveFile()
    };

    this._registerOrUpdateFunction(payload)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageInfo(
              this._translateService.instant('ThereWasAError'),
              resp.message
            );
          } else {
            this.bulkCatalog(resp.result);

          }
        }
      });
  }

  goBack() {
    let dataReturn = {
      value: 3,
      id: 0,
    };
    this.setActiveTabCatalog.emit(dataReturn);
  }

  goBackMain() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant('Out'),
        'CatalogSetting.MessagesCatalogo.UnsavedData',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._location.back();
        }
      });
  }

  ngOnDestroy(): void {
    this._settingCompositeCatalogSubscription?.unsubscribe();
  }

  saveFiles(files: File[]) {
    this.files = files;

    if (this.files.length > 0) {
      this.convertExcelToJSON();
    } else {
      console.warn("No files to process.");
    }
  }

  convertFileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String);
      };
      reader.onerror = (error) => {
        this._messageService.messageError('Error al leer el archivo: ' + error);
      };
      reader.readAsDataURL(file);
    });
  }

  async saveFile(): Promise<number> {
      return new Promise(async (resolve) => {
        let payload: ImageUploadModel = {
          fileName: this.files[0].name,
          fkIIdBusinessByCountry: this._customRouter.getIdbusinessByCountry(),
          imageBase64: (await this.convertFileToBase64(this.files[0])).split(',')[1],
          pkIIdInsuranceCompanies: 0,
          type: 0
        };
        this._fileService
          .uploadImage(payload)
          .pipe(
            catchError((error) => {
              if (error.error.error) {
                this._messageService.messageWaring(
                  this._translateService.instant('ThereWasAError'),
                  error.error.message
                );
              }
              return of([]);
            })
          )
          .subscribe((resp: ResponseGlobalModel | never[]) => {
            if (Array.isArray(resp)) {
            } else {
              if (resp.error) {
              } else {
                resolve(resp.result)
              }
            }
          });
      });
      
    }

  convertExcelToJSON() {

    this._fileService.convertExcelToJSON(this.files[0])
      .pipe(
        catchError(error => {
          console.error('Error converting file:', error);
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error?.error?.message || 'An error occurred'
          );
          return of(null);
        })
      )
      .subscribe((response: ResponseGlobalModel | null) => {
        if (response && !response.error) {
          const jsonData = response.result;
          const responseData = JSON.parse(jsonData);
          this.jsonExcel = responseData;

        } else if (response) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + response.message
          );
        }
      });
  }


  bulkCatalog(pkIIdHistoryCatalog: any) {
    if (this.files.length == 0) {
      this._msgSvc.messageWaring('CatalogSetting.MessagesCatalogo.Warning', this._translateService.instant(
        'CatalogSetting.MessagesCatalogo.SelectFile'
      ))
      return
    }

    const dataRequest: BulkUploadCatalogDto = {
      fkIdHistoryCatalog: typeof pkIIdHistoryCatalog === 'boolean' ? this.dataJson.pkIIdHistoryCatalog : pkIIdHistoryCatalog,
      dataJson: this.jsonExcel
    };

    this._catalogService.bulkUploadCatalog(dataRequest)
      .pipe(
        catchError(error => {
          console.error('Error converting file:', error);
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error?.error?.message || 'An error occurred'
          );
          return of(null);
        })
      )
      .subscribe((response: ResponseGlobalModel | null) => {
        if (response && !response.error) {
          this._msgSvc.messageSuccess(
            '',
            this._translateService.instant(
              'CatalogSetting.MessagesCatalogo.SuccessfulMessageCreated'
            )
          );

          this._location.back();

        } else if (response) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + response.message
          );
        }
      });
  }

  organizeColumnsById(data: any): any {
    const organizedColumns = data.Columns.sort((a:any, b:any) => a.id - b.id);

    return {
        pkIIdHistoryCatalog: data.pkIIdHistoryCatalog,
        Sheets: data.Sheets,
        Columns: organizedColumns,
        catalogs: data.catalogs
    };
}


}
