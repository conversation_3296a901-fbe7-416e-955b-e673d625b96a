import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UploadImageVariesExtComponent } from '../../../../../../../shared/components/upload-image-varies-ext/upload-image-varies-ext.component';
import { ImageModel } from '../../../../../../../shared/models/image-picker';
import { UtilsService } from '../../../../../../../shared/services/utils/utils.service';
import { catchError, of } from 'rxjs';
import { MessageService } from '../../../../../../../shared/services/message/message.service';
import { SectionClientPortalService } from '../../../../../../../shared/services/section-client-portal/section-client-portal.service';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-create-edit-section',
  templateUrl: './create-edit-section.component.html',
  styleUrls: ['./create-edit-section.component.scss'],
  standalone: true,
  imports: [
    CommonModule, MatInputModule, TranslateModule,
    MatCheckboxModule, MatFormFieldModule, FormsModule,
    ReactiveFormsModule, UploadImageVariesExtComponent,
    MatIconModule, MatButtonModule
  ]
})
export class CreateEditSectionComponent implements OnInit {

  @Input() idOrder: number = 0;
  @Input() idSection: number = 0;
  @Input() idBusinessByCountry: number = 0;
  @Output() changeSuccess = new EventEmitter<boolean>();


  formSection: FormGroup = new FormGroup({});
  hasButton: boolean = false;

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    public _modalDialog: MatDialog,
    private _messageService: MessageService,
    public _translateService: TranslateService,
    private _SectionClientPortalService: SectionClientPortalService,
  ) {

  }

  ngOnInit(): void {
    this.initForm();
    this.validEditSection();
  }

  initForm() {
    this.formSection = this._fb.group({
      pkIIdSectionClientPortal: [0],
      vTitle: ['', [Validators.required]],
      vSubtitle: ['', [Validators.required]],
      bHasButton: [false, [Validators.required]],
      vButtonText: [''],
      vButtonLink: [''],
      iOrder: [(this.idOrder + 1)],
      fileName: ['', [Validators.required]],
      imageBase64: ['', [Validators.required]],
      fkIIdUploadFile: [0],
      fkIIdBusinessCountry: [this.idBusinessByCountry]
    });
  }

  validEditSection() {
    if (this.idSection > 0) {
      this._SectionClientPortalService.getSectionById(this.idSection)
        .subscribe((resp) => {
          if (resp.result != null) {
            this.formSection.patchValue({ pkIIdSectionClientPortal: this.idSection });
            this.formSection.patchValue({ vTitle: resp.result.title });
            this.formSection.patchValue({ vSubtitle: resp.result.subTitle });
            this.formSection.patchValue({ bHasButton: resp.result.hasButton });
            this.formSection.patchValue({ vButtonText: resp.result.buttonText });
            this.formSection.patchValue({ vButtonLink: resp.result.buttonLink });
            this.formSection.patchValue({ iOrder: resp.result.order });
            this.formSection.patchValue({ fileName: resp.result.fileName });
            //this.formSection.patchValue({ imageBase64: resp.result.bActive });
            this.formSection.patchValue({ fkIIdUploadFile: resp.result.idUploadFile });
            this.hasButton = resp.result.hasButton;
          }
        });
    }
  }

  changeFile(event: ImageModel) {
    //if (this.operationType === 'edit') {
    //  this.formActiveNews.get('isImageEdit')?.setValue(true);
    //}
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString.split(',')[1];
    this.formSection.get('fileName')?.setValue(`${imageName}.${extension}`);
    this.formSection.get('imageBase64')?.setValue(base64);
  }

  deleteFile(event: boolean) {
    if (event) {
      this.formSection.get('fileName')?.setValue('');
      this.formSection.get('imageBase64')?.setValue('');
    }
  }

  cancel() {
    this.formSection.reset();
    this._modalDialog.closeAll();
  }

  hasButtonChange(evt: any) {
    this.hasButton = evt.checked;
  }

  saveChanges() {
    if (this.formSection.valid) {
      if (this.idSection > 0) {
        this.update();
      } else {
        this.create();
      }
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('Warning'),
        this._translateService.instant('CheckTheFormIsFilledCorrectly')
      );
    }
  }

  create() {
    this._SectionClientPortalService.createNews(this.formSection.value)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message);
          this._modalDialog.closeAll();
          this.changeSuccess.emit(false);
          return of([]);
        })
      )
      .subscribe({
        next: (response) => {
          if (!Array.isArray(response)) {
            if (!response.error) {
              this._messageService.messageSuccess(this._translateService.instant('DataSavedSuccessfully'), "");
              this._modalDialog.closeAll();
              this.changeSuccess.emit(true);
            } else {
              this._messageService.messageInfo(this._translateService.instant('ThereWasAError'), this._translateService.instant(response.message));
            }
          }
        },
      });
  }

  update() {
    this._SectionClientPortalService.updateNews(this.formSection.value)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message);
          this._modalDialog.closeAll();
          this.changeSuccess.emit(false);
          return of([]);
        })
      )
      .subscribe({
        next: (response) => {
          if (!Array.isArray(response)) {
            if (!response.error) {
              this._messageService.messageSuccess(this._translateService.instant('DataSavedSuccessfully'), "");
              this._modalDialog.closeAll();
              this.changeSuccess.emit(true);
            } else {
              this._messageService.messageInfo(this._translateService.instant('ThereWasAError'), this._translateService.instant(response.message));
            }
          }
        },
      });
  }
}
