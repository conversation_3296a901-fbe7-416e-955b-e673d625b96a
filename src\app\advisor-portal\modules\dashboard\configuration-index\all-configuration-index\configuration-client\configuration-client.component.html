
<!--Seccion de configuraciones básicas-->
<div class="mb-12">
  <app-additional-configuration *ngIf="idBusinessByCountry > 0"
    [idBusinessByCountry]="idBusinessByCountry">
  </app-additional-configuration>
</div>

<!--Seccion de coberturas-->
<div class="mb-12">
  <app-coverages *ngIf="idBusinessByCountry > 0"
    [idBusinessByCountry]="idBusinessByCountry">
  </app-coverages>
</div>

<!--Seccion de preguntas-->
<div class="mb-12">
  <app-questions *ngIf="idBusinessByCountry > 0"
    [idBusinessByCountry]="idBusinessByCountry">
  </app-questions>
</div>

<!--Seccion de pasos-->
<div class="mb-12">
  <app-steps *ngIf="idBusinessByCountry > 0"
    [idBusinessByCountry]="idBusinessByCountry">
  </app-steps>
</div>

<!--Secciones de beneficios-->
<div class="mb-12">
  <app-benefits *ngIf="idBusinessByCountry > 0"
    [idBusinessByCountry]="idBusinessByCountry">
  </app-benefits>
</div>

<!--Secciones configurables-->
<div class="mb-12">
  <app-all-section *ngIf="idBusinessByCountry > 0"
                   [idBusinessByCountry]="idBusinessByCountry">
  </app-all-section>
</div>

<!--Secciones de alianzas-->
<div class="mb-12">
  <app-alliances *ngIf="idBusinessByCountry > 0"
                   [idBusinessByCountry]="idBusinessByCountry">
  </app-alliances>
</div>

<!--Seccion de Links-->
<div class="mb-12">
  <app-links *ngIf="idBusinessByCountry > 0"
    [idBusinessByCountry]="idBusinessByCountry">
  </app-links>
</div>


<!--Seccion de noticas-->
<div class="mb-12">
  <app-all-active-news *ngIf="idBusinessByCountry > 0"
                       [idBusinessByCountry]="idBusinessByCountry"
                       [objetActNews]="null" [userType]="2">
  </app-all-active-news>
</div>




<!--Seccion de plantillas-->

<div *ngIf="idBusinessByCountry > 0">
  <div class="row mb-2 mt-5">
    <h5 class="fw-bold mb-2">
      {{ 'Template.TemplateConfiguration' | translate }} <!-- Configuración Plantilla -->
    </h5>
  </div>

  <div class="col-12 col-md-12 mb-3">
    <h4>{{ "Template.TitleTemplate" | translate }}</h4>  <!-- Plantilla -->
    <mat-form-field>
      <mat-label>{{ "Communications.TemplateSelectionToLabel" | translate }}</mat-label> <!-- Selección de plantilla -->
      <mat-select [(value)]="selectedTemplate" (selectionChange)="templatesChange($event)">
        <mat-option *ngFor="let item of templateList" [value]="item.pkIIdTemplates">
          {{ item.vName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>


  <div class="row">
    <div class="col">
      <button mat-raised-button color="primary" type="button" class="" (click)="saveTemplate()">
        {{ "Communications.SaveSettingsButton" | translate }} <!-- Guardar configuración -->
      </button>
    </div>
  </div>

</div>