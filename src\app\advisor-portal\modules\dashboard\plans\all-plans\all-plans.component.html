<app-choose-country-and-company></app-choose-country-and-company>

<div class="row mb-2">
  <h4 class="col-md-12">
    {{ 'Plan.PlansConfiguredByProducts' | translate }}
    <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.PlanConfigurationSubtitle' | translate }}">help_outline</mat-icon>
  </h4>
</div>

<div class="col-md-6 col-sm-12 mb-2">
  <form [formGroup]="formProduct">
    <mat-form-field class="w-100" appearance="fill">
      <mat-label>
        {{ 'Plan.SelectTheProduct' | translate }}
      </mat-label>
      <mat-select formControlName="productSelect">
        <mat-option *ngFor="let product of produts" [value]="product">
          {{ product.vProductName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>
</div>

<div class="row mt-2">
  <app-table
    [displayedColumns]="estructPlansTable"
    [data]="plansData"
    (iconClick)="controller($event)"
  ></app-table>
</div>
<button
  class="mt-2 w-20"
  type="button"
  (click)="gotToCreate()"
  mat-raised-button
  color="primary"
>
{{ 'Plan.AddPlan' | translate }}
<mat-icon iconPositionEnd fontIcon="add"></mat-icon>
</button>
