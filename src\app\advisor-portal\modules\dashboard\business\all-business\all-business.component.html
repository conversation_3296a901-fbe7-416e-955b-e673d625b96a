<!-- buscar empresa -->
<mat-form-field appearance="fill" class="fld col-12">
  <label>
    {{ "Business.SearchBusiness" | translate }}
  </label>
  <input matInput #searchBar (focusout)="search(searchBar.value)" />
  <mat-icon matPrefix class="my-icon">search</mat-icon>
</mat-form-field>

<!-- datatable empresas -->
<div class="d-flex justify-content-start align-items-center flex-wrap mt-4">
  <h3 class="h4 mr-2" style="margin: 0">
    {{ "Business.RegisteredBusiness" | translate }}
  </h3>
</div>
<div class="quotes-list">
  <div class="list">
    <app-table
      [displayedColumns]="estructTableBusiness"
      [data]="dataTableBusiness"
      (iconClick)="controllerBusiness($event)"
    ></app-table>
  </div>

  <div class="row">
    <div class="col">
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="addBusiness()"
      >
        {{ "Business.AddBusiness" | translate }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
  </div>
</div>
<!-- end datatable empresas -->

<!-- datatable grupos empresariales -->
<div class="d-flex justify-content-start align-items-center flex-wrap mt-4">
  <h3 class="h4" style="margin: 0">
    {{ "Business.BusinessGroups" | translate }}
  </h3>
  <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.BusinessGroupsTable' | translate }}">help_outline</mat-icon>
</div>

<div class="quotes-list">
  <div class="list">
    <app-table
      [displayedColumns]="estructTableBusinessGroup"
      [data]="dataTableBusinessGruop"
      (iconClick)="controllerBusinessGruop($event)"
    ></app-table>
  </div>

  <div class="row">
    <div class="col">
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="openEditNewBusinessGroupDialog()"
      >
        {{ "Business.AddBusinessGroup" | translate }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
  </div>
</div>
<!-- end datatable grupos empresariales -->

<!-- modal crear editar grupo empresarial -->
<ng-template #editNewBusinessGroupModal>
  <app-modal [titleModal]="tittleModalText">
    <app-edit-new-business-group
      (submitOut)="getSubmitData($event)"
      [dataBusinessGroupId]="dataBusinessGroupId"
    >
    </app-edit-new-business-group>
  </app-modal>
</ng-template>
<!-- end modal crear editar grupo empresarial -->
