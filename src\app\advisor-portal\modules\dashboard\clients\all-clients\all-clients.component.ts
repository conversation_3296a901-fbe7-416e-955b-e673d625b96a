import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { CommonModule } from '@angular/common';
import { SelectModel } from 'src/app/shared/models/select';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { Subscription, catchError, of } from 'rxjs';
import { ClientService } from 'src/app/shared/services/client/client.service';
import { PageEvent } from '@angular/material/paginator';
import { FilterTaskTrayModel } from 'src/app/shared/models/task';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import {
  AllCustomersOnlyCompanyType,
  AllCustomersOnlyPersonTypeDTO,
} from 'src/app/shared/models/client';
import { MatCardModule } from '@angular/material/card';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatDialog } from '@angular/material/dialog';
import { ModalBulkLoadComponent } from 'src/app/advisor-portal/modules/dashboard/clients/modal-bulk-load/modal-bulk-load.component';
import { SettingService } from 'src/app/shared/services/setting/setting.service';

@Component({
  selector: 'app-all-clients',
  standalone: true,
  imports: [
    TableComponent,
    RouterModule,
    MatInputModule,
    MatIconModule,
    ModalComponent,
    TranslateModule,
    MatButtonModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatCardModule,
    MatOptionModule,
    CommonModule,
    DragDropModule
  ],
  templateUrl: './all-clients.component.html',
  styleUrls: ['./all-clients.component.scss'],
})
export class AllClientsComponent implements OnInit, OnDestroy {
  ClientsList?: Subscription;

  Client?: Subscription;
  form: FormGroup = new FormGroup({});
  isDisabledButton: boolean = true;
  dataTableClients: any[] = [];
  idsCustomerToDownload: number[] = [];
  idsCompanyToDownload: number[] = [];
  customerPersonToDownload: AllCustomersOnlyPersonTypeDTO[] = [];
  customerCompanyToDownload: AllCustomersOnlyCompanyType[] = [];
  downloadAllCustomer: boolean = false;
  headsClients: string[] = [];
  headsCompanys: string[] = [];
  selectionChecked: string = 'NONE';

  filterRequest: FilterTaskTrayModel = {
    idTarea: 0,
    idStateModule: 0,
    idProcess: 0,
    idProduct: 0,
    idRequestingUser: 0,
    jsonString: '',
    startDate: '',
    endDate: '',
    from: 1,
    pageSize: 5,
  };

  //Variables el paginado de la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;

  documentsTypes: SelectModel[] = [];

  estructTableClients: BodyTableModel[] = [
    {
      columnLabel: '',
      columnValue: 'check',
      check: true
    },
    {
      columnLabel: this._translateService.instant('Clients.NameLabel'),
      columnValue: 'vFullName',
    },
    {
      columnLabel: this._translateService.instant('Clients.TypeDocument'),
      columnValue: 'iIdDocumentType',
      functionValue: (item: any) => this.filterTypeDocument(item),
    },
    {
      columnLabel: this._translateService.instant('Clients.ClientDocument'),
      columnValue: 'vDocumentNumber',
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'detailClient',
      columnIcon: 'search',
    },
  ];

  //variables relacionadas con empresa pais.
  idBusinessCountry: number = 0;


  constructor(
    private _fb: FormBuilder,
    private _translateService: TranslateService,
    public _utilsSvc: UtilsService,
    private _parametersService: ParametersService,
    private _clientService: ClientService,
    private _messageService: MessageService,
    private _customRouter: CustomRouterService,
    private _fileService: FileService,
    public dialog: MatDialog,
    private _settingService: SettingService,

  ) {
  }

  ngOnInit(): void {
    this.getDataSettingInit();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table ANS
      this.estructTableClients[1].columnLabel =
        this._translateService.instant('Clients.NameLabel');

      this.estructTableClients[2].columnLabel = this._translateService.instant(
        'Clients.TypeDocument'
      );

      this.estructTableClients[3].columnLabel = this._translateService.instant(
        'Clients.ClientDocument'
      );
      this.estructTableClients[4].columnLabel =
        this._translateService.instant('Action');
    });
    this.initForm();
    this.getListDocumentType();
  }

  ngOnDestroy(): void {
    this.ClientsList?.unsubscribe();
  }

  initForm() {
    this.form = this._fb.group({
      typeDocument: [, [Validators.required]],
      document: [, [Validators.required]],
    });
  }

  openModalBulkLoad(){
    const dialogRefD = this.dialog.open(ModalBulkLoadComponent, {
      width: '800px',
      data: {
      },
    });
  
    dialogRefD.afterClosed().subscribe(
      (result: any) => {
        console.log("result", result);
        
      },
      (error) => {
        console.error('Error opening the confirmation dialog:', error);
      }
    );
  }

  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;

    if (this.pageIndex < 0) {
      this.pageIndex = 1;
    }

    this.filterRequest.from = this.pageIndex + 1;
    this.filterRequest.pageSize = this.pageSize;
    this.getAllCustomer(this.pageIndex + 1, this.pageSize, this.idBusinessCountry);
  }

  getAllCustomer(from: number, pageSize: number, idBusinessByCountry: number) {
    this.ClientsList = this._clientService
      .getAllCustomer(this.pageIndex, this.pageSize, idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp) => {
        if (Array.isArray(resp)) {
          this._messageService.messageWaring(
            this._translateService.instant('WarningMessage'),
            this._translateService.instant('TaskTray.Messages.NoDataProduct')
          );
        } else {
          this.dataTableClients = resp.result;
          if (this.dataTableClients.length > 0){
            this.persistCheckedRecords();
          }
          this.amountRows = resp.rowCount;
        }
      });
  }

  getCustomerByDocument() {
    if (this.form.valid) {
      const { typeDocument, document } = this.form.value;

      this.Client = this._clientService
        .getCustomerByDocument(document, typeDocument)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )

        .subscribe((resp) => {
          if (Array.isArray(resp)) {
            this._messageService.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant('TaskTray.Messages.NoDataProduct')
            );
          } else {
            this.dataTableClients = [resp.result];
            this.amountRows = resp.rowCount;
          }
        });
    }
  }

  filterTypeDocument(idDocument: any) {
    const document: any = this.documentsTypes.find(
      (doc) => doc.id === idDocument.iIdDocumentType
    );
    return document ? document.name : '';
  }

  filterTypeDocumentDownload(idDocument: any) {
    const document: any = this.documentsTypes.find(
      (doc) => doc.id === idDocument
    );
    return document ? document.name : '';
  }

  controllerBusiness(evt: IconEventClickModel) {
    // Check if the clicked column is 'check'
    
    if (evt.column === 'check') {
      if (evt.value === 'NONE') {
        this.selectionChecked = evt.value;
        this.idsCustomerToDownload = [];
        this.isDisabledButton = true;
      } else if (evt.value === 'ALL') {
        this.selectionChecked = evt.value;
        this.idsCustomerToDownload = this.dataTableClients.map(item => item.pkIIdCustomer);
        this.isDisabledButton = false;
      } else if (evt.value.pkIIdCustomer) {
        this.isDisabledButton = false;
        this.selectionChecked = 'MULTI';
      }
    } else if (evt.column === 'detailClient') {
      // Navigate to client detail page
      this._customRouter.navigate([`/dashboard/clients/detail/${evt.value.pkIIdCustomer}`]);
    }

    // Adding or removing users from the list
    if (
      evt &&
      evt.value !== 'ALL' &&
      evt.value !== 'NONE' &&
      evt.value.pkIIdCustomer
    ) {
      this.downloadAllCustomer = false;

      if (evt.value.vTypePerson) {
        // Check if the type of person is 1 (user)
        if (evt.value.vTypePerson === 1) {
          this.addRemoveUserId(evt.value.pkIIdCustomer);
        } else {
          this.addRemoveCompanyId(evt.value.pkIIdCustomer);
        }
      } else if (evt.value.iIdDocumentType === 13) {
        this.addRemoveUserId(evt.value.pkIIdCustomer);
      } else if (evt.value.iIdDocumentType === 31) {
        this.addRemoveCompanyId(evt.value.pkIIdCustomer);
      }
    } else {
      this.downloadAllCustomer = true;
    }
  }

  /**
   * Adds or removes a user ID from the list of customers to download.
   * @param {number} idUsuario - The ID of the user to add or remove.
   */
  addRemoveUserId(idUsuario: number) {
    const index = this.idsCustomerToDownload.indexOf(idUsuario);

    if (index !== -1) {
      // The user ID exists in the array, so remove it
      this.idsCustomerToDownload.splice(index, 1);
    } else {
      // The user ID does not exist in the array, so add it
      this.idsCustomerToDownload.push(idUsuario);
    }

    // Disable the button if both customer and company lists are empty
    this.isDisabledButton =
      this.idsCompanyToDownload.length === 0 &&
      this.idsCustomerToDownload.length === 0;
  }

  /**
   * Adds or removes a company ID from the list of companies to download.
   * @param {number} idUsuario - The ID of the company to add or remove.
   */
  addRemoveCompanyId(idUsuario: number) {
    const index = this.idsCompanyToDownload.indexOf(idUsuario);

    if (index !== -1) {
      // The idUsuario exists in the array, so remove it
      this.idsCompanyToDownload.splice(index, 1);
    } else {
      // The idUsuario does not exist in the array, so add it
      this.idsCompanyToDownload.push(idUsuario);
    }

    // Disable the button if both company and customer lists are empty
    this.isDisabledButton =
      this.idsCompanyToDownload.length === 0 &&
      this.idsCustomerToDownload.length === 0;
  }

  /**
   * Retrieves the list of global document types from the parameters service.
   */
  getListDocumentType() {
    this._parametersService.getListCatalogGlobalDocumentTypes().subscribe({
      next: (response) => {
        if (!response.error) {
          // Assign the response result to the documentsTypes array
          this.documentsTypes = response.result;
        }
      },
      error: (err) => {
        // Optionally handle the error case
        console.error('Error retrieving document types', err);
      },
    });
  }

  goToClientsNew(){
    this._customRouter.navigate([`/dashboard/clients/new`]);
  }
  
  /**
   */
  async downloadCustomer() {
    if (this.downloadAllCustomer) {
      await this.downloadClientsTypeCompany([]);

      // Downloading person-type customers
      await this.downloadClientsTypePerson([]);
    } else {
      if (this.idsCompanyToDownload.length > 0) {
        await this.downloadClientsTypeCompany(this.idsCompanyToDownload);
      }

      if (this.idsCustomerToDownload.length > 0) {
        // Downloading person-type customers based on selected IDs
        await this.downloadClientsTypePerson(this.idsCustomerToDownload);
      }
    }

    if (
      this.customerPersonToDownload.length > 0 ||
      this.customerCompanyToDownload.length > 0
    ) {
      this.downloadExcel();
    }
  }

  /**
   * Downloads company-type customer data based on the provided data.
   * @param data - The data to filter the person-type customers.
   * @returns A Promise that resolves when the download is complete.
   */
  async downloadClientsTypePerson(data: any): Promise<void> {
    try {
      // Downloading company-type customers
      const response = await this._clientService
        .GetAllCustomersOnlyPersonType(data)
        .toPromise();

      if (response.length > 0) {
        const keysArray = Object.keys(response[0]);
        this.headsClients = keysArray;
        this.customerPersonToDownload = response;
      }
    } catch (error) {
      console.error('Error downloading person-type customers', error);
      throw error; // Propagate the error to be handled by the caller
    }
  }

  /**
   * Downloads company-type customer data based on the provided data.
   * @param data - The data to filter the company-type customers.
   * @returns A Promise that resolves when the download is complete.
   */
  async downloadClientsTypeCompany(data: any): Promise<void> {
    try {
      // Downloading company-type customers
      const response = await this._clientService
        .GetAllCustomersOnlyCompanyType(data)
        .toPromise();

      if (response.length > 0) {
        this.customerCompanyToDownload = response;
      }
    } catch (error) {
      console.error('Error downloading company-type customers', error);
      throw error; // Propagate the error to be handled by the caller
    }
  }

  /**
   * Updates the document type for each customer in the provided list.
   * @param customers - The list of customers to be updated.
   * @returns The list of customers with updated document types.
   */
  updateDocumentType(customers: any[]): any[] {
    return customers.map((r) => {
      r.tipoDocumento = this.filterTypeDocumentDownload(r.idTipoDocumento);
      return r;
    });
  }

  /**
   * Updates the document type for each customer in the provided list.
   * @param customers - The list of customers to be updated.
   * @returns The list of customers with updated document types.
   */
  updateDocumentTypeCompany(customers: any[]): any[] {
    return customers.map((r) => {
      r.tipoDeDocumentoRepresentanteLegal = this.filterTypeDocumentDownload(
        r.idTipoDeDocumentoRepresentanteLegal
      );
      return r;
    });
  }

  /**
   * Removes specified properties from each customer object in the list.
   * @param customers - The list of customers to be processed.
   * @param propertiesToRemove - The properties to be removed.
   * @returns A new list of customers with the specified properties removed.
   */
  removeProperties(customers: any[], propertiesToRemove: string[]): any[] {
    return customers.map((obj) => {
      const newObj = { ...obj };
      propertiesToRemove.forEach((prop) => delete newObj[prop]);
      return newObj;
    });
  }

  /**
   * Prepares the data structure for Excel file generation.
   * @param personData - The data for person customers.
   * @param companyData - The data for company customers.
   * @returns The structured data for Excel generation.
   */
  prepareExcelBody(personData: any[], companyData: any[]): any {
    const propertiesToRemove = [
      'idCustomer',
      'idTipoDocumento',
      'idTipoDeDocumentoRepresentanteLegal',
    ];

    var newPersonData = [];

    if (this.customerPersonToDownload.length > 0) {
      newPersonData = this.removeProperties(personData, propertiesToRemove);
    }

    var newCompanyData = [];
    if (this.customerCompanyToDownload.length > 0) {
      newCompanyData = this.removeProperties(companyData, propertiesToRemove);
    }

    var personKeys: any[] = [];

    if (newPersonData.length > 0) {
      personKeys = Object.keys(newPersonData[0]);
    }

    var companyKeys: any[] = [];

    if (newCompanyData.length > 0) {
      companyKeys = Object.keys(newCompanyData[0]);
    }

    this.headsClients = personKeys;
    this.headsCompanys = companyKeys;

    if (this.headsClients.length > 0 && this.headsCompanys.length > 0) {
      return {
        body: [
          {
            SheetName: 'Clientes',
            head: personKeys,
            data: newPersonData,
          },
          {
            SheetName: 'Empresas',
            head: companyKeys,
            data: newCompanyData,
          },
        ],
      };
    } else if (this.headsClients.length > 0) {
      return {
        body: [
          {
            SheetName: 'Clientes',
            head: personKeys,
            data: newPersonData,
          },
        ],
      };
    } else if (this.headsCompanys.length > 0) {
      return {
        body: [
          {
            SheetName: 'Empresas',
            head: companyKeys,
            data: newCompanyData,
          },
        ],
      };
    }
  }
  
  /**
   * Triggers the download of an Excel file with the provided data.
   * @param body - The data to be included in the Excel file.
   * @returns A Promise that resolves when the file download is complete.
   */
  async downloadExcelFile(body: any): Promise<void> {
    try {
      // Convert the Observable to a Promise
      const response: Blob = await new Promise((resolve, reject) => {
        this._fileService.GenerateExcelDynamic(body).subscribe({
          next: (blob: Blob) => resolve(blob),
          error: (err) => reject(err),
        });
      });

      // Create a URL for the Blob object
      const url = window.URL.createObjectURL(response);

      // Create a temporary <a> element
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;

      // Set the file name for download
      a.download = 'filename.xlsx';

      // Append the link to the DOM and simulate a click to trigger the download
      document.body.appendChild(a);
      a.click();

      // Clean up by revoking the object URL and removing the link from the DOM
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading the file:', error);
    }
  }

  /**
   * Processes customer data and triggers the download of an Excel file with the processed data.
   */
  async downloadExcel(): Promise<void> {
    // Update document types
    this.customerPersonToDownload = this.updateDocumentType(
      this.customerPersonToDownload
    );

    this.customerCompanyToDownload = this.updateDocumentType(
      this.customerCompanyToDownload
    );
    this.customerCompanyToDownload = this.updateDocumentTypeCompany(
      this.customerCompanyToDownload
    );

    // Prepare data for Excel
    const excelBody = this.prepareExcelBody(
      this.customerPersonToDownload,
      this.customerCompanyToDownload
    );

    // Download the Excel file
    await this.downloadExcelFile(excelBody);

    //Waiting for it to resolve to reset everything to zero to avoid duplicates when redownloading
    this.headsClients = [];
    this.headsCompanys = [];
    this.customerCompanyToDownload = [];
    this.customerPersonToDownload = [];
  }

  showBulkLoad(){
    const dialogRefD = this.dialog.open(ModalBulkLoadComponent, {
      width: '800px',
      data: {
        isConfirmation: true,
      },
    });

    dialogRefD.afterClosed().subscribe(
      (result: any) => {},
      (error) => {
        console.error('Error opening the confirmation dialog:', error);
      }
    );
  }

    //Get BusinessByCountry Initial Configuration.
    async getDataSettingInit() {
      let data = await this._settingService.getDataSettingInit();
      this.idBusinessCountry = data.idBusinessByCountry;
      this.getAllCustomer(this.pageIndex, this.pageSize, this.idBusinessCountry);
    }
  
  // Keeps records checked when pagination changes
  persistCheckedRecords(){
    switch (this.selectionChecked) {
      case 'ALL':
        this.idsCustomerToDownload = this.dataTableClients.map(item => item.pkIIdCustomer);
        this.dataTableClients.forEach(customer => customer.check = true);
        break;
      case 'NONE':
        this.idsCustomerToDownload = [];
        this.dataTableClients.forEach(customer => customer.check = false);
        break;
      case 'MULTI':
        this.dataTableClients.forEach(customer => {
          if (this.idsCustomerToDownload.some((idCustomer: number) => idCustomer === customer?.pkIIdCustomer)){
            customer.check = true;
          }
          else{
            customer.check = false;
          }
        });
        break;
      default:
        break;
    }
  }
}
