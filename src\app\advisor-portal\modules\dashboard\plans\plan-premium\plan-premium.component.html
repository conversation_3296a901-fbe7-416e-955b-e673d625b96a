<h2 style="text-align: left;">{{ "PlanPremium.RuleCalculationPremium" | translate }}</h2>
<div style="text-align: left;">
   <h4>{{titleModal}}</h4>
</div>
<div class="modal-body d-flex justify-content-center flex-wrap">
    <ng-container body>
        <mat-divider></mat-divider>
        <br>
        <div class="col-md-12 col-sm-12 mb-2">
            <form [formGroup]="formPlanPremium">
                <div *ngIf="this.formPlanPremium.get('bIsCalculated')?.value === 0" class="col-12 col-md-12">
                    <section class="margin-section">
                        <mat-form-field class="w-100 mb-3">
                            <input matInput type="text" formControlName="vSymbol" />
                            <mat-error
                                *ngIf="
                                    _utilsService.isControlHasError(formPlanPremium, 'vSymbol', 'required')
                                "
                                >
                                {{ 'ThisFieldIsRequired' | translate }}
                            </mat-error>
                        </mat-form-field>
                    </section>
                </div>
                <div class="col-12 col-md-12">
                    <section class="margin-section">
                        <mat-radio-group  formControlName="bIsCalculated">
                            <mat-radio-button  [value]="0"
                                class="margin-radioButton" (change)="changeOptionsPremium($event.value)">
                                {{ "PlanPremium.ValueFixed" | translate }}
                            </mat-radio-button>
                            <mat-radio-button  [value]="1"
                                class="margin-radioButton" (change)="changeOptionsPremium($event.value)">
                                {{ "PlanPremium.ValueCalculated" | translate }} </mat-radio-button>
                        </mat-radio-group>
                    </section>
                </div>
                <div *ngIf="this.formPlanPremium.get('bIsCalculated')?.value === 0" class="col-12 col-md-12 mb-3">
                    <mat-form-field class="w-100 mb-3">
                        <mat-label>
                            {{ "PlanPremium.ValuePremium" | translate }}
                        </mat-label>
                        <input matInput type="text" formControlName="vValue" (input)="onInput($event)"/>
                        <mat-error
                            *ngIf="
                                _utilsService.isControlHasError(formPlanPremium, 'vSymbol', 'required')
                            "
                            >
                            {{ 'ThisFieldIsRequired' | translate }}
                        </mat-error>
                    </mat-form-field>

                    <div class="col-12 col-md-12" >
                      <div class="switches">
                        <mat-slide-toggle class="mb-3" formControlName="bIsShowDecimals" (change)="changeDecimalValues()">
                          {{ "ShowValuesWithDecimals" | translate }}
                        </mat-slide-toggle>
                      </div>
                    </div>
                </div>

                <div *ngIf="this.formPlanPremium.get('bIsCalculated')?.value === 1">
                    <div class="row">
                        <div class="listbox-container col-5 col-md-5 mb-3">
                            <mat-label class="listbox-label">
                                {{ "PlanPremium.Field" | translate }} </mat-label>
                            <ul cdkListbox aria-labelledby="zodiac-sign-label" class="listbox">
                                <li *ngFor="let field of fieldsPar" (click)="addFieldforFormula(field)"
                                    [cdkOption]="field" class="option-focus">
                                    {{field.vNameField}}
                                </li>
                            </ul>
                        </div>

                        <div class="chip-container col-5 col-md-5 mb-3">
                            <mat-label class="listbox-label">
                                {{ "PlanPremium.CalculationFormulas" | translate }} </mat-label>
                            <mat-chip-listbox cdkDropList (cdkDropListDropped)="dropField($event)">
                                <mat-chip class="example-box" cdkDrag *ngFor="let formula of formulas"
                                    (removed)="removeFieldsFormulas(formula)" [removable]="true">
                                    {{formula.vNameField}}
                                    <mat-icon matChipRemove>cancel</mat-icon>
                                </mat-chip>
                            </mat-chip-listbox>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6 col-md-6 mb-3">
                            <mat-chip-listbox #chipList>
                                <mat-chip *ngFor="let element of elements" (click)="addElementsforFormula(element)">
                                    {{element.vNameField}}
                                </mat-chip>
                            </mat-chip-listbox>
                        </div>

                        <div class="col-6 col-md-6 mb-3">
                            <mat-form-field style="width: 86%;">
                                <mat-label>
                                    {{ "PlanPremium.Description" | translate }}
                                </mat-label>
                                <input matInput type="text" formControlName="vDescription" PreventionSqlInjector/>
                            </mat-form-field>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-4 col-md-4">
                            <mat-form-field class="w-100">
                                <mat-label>
                                    {{ "PlanPremium.AddText" | translate }}
                                </mat-label>
                                <input matInput type="text" formControlName="vfieldtext" PreventionSqlInjector/>
                            </mat-form-field>
                        </div>
                        <div class="col-2 col-md-2">
                            <button type="button" mat-raised-button (click)="addNewFields(formPlanPremium.value)"
                                color="default">  {{ "PlanPremium.Add" | translate }} </button>
                        </div>

                        <div class="col-6 col-md-6">
                            <button type="button" style="float: left;" mat-raised-button
                                (click)="saveRules()" color="primary">
                                    {{ "PlanPremium.AddRule" | translate }}
                                <mat-icon>add</mat-icon>
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-4 col-md-4">
                            <mat-form-field class="w-100">
                                <mat-label>
                                    {{ "PlanPremium.AddNumber" | translate }}
                                </mat-label>
                                <input matInput type="number" formControlName="vfieldnumber" />
                            </mat-form-field>
                        </div>
                        <div class="col-2 col-md-2">
                            <button type="button" mat-raised-button (click)="addNewFields(formPlanPremium.value)"
                                color="default">  {{ "PlanPremium.Add" | translate }} </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-4 col-md-4">
                            <mat-form-field class="w-100">
                                <mat-label>
                                    {{ "PlanPremium.AddDate" | translate }}
                                </mat-label>
                                <input matInput [matDatepicker]="vfielddate" formControlName="vfielddate" />
                                <mat-datepicker-toggle matIconSuffix [for]="vfielddate"></mat-datepicker-toggle>
                                <mat-datepicker #vfielddate></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div class="col-2 col-md-2">
                            <button type="button" mat-raised-button (click)="addNewFields(formPlanPremium.value)"
                                color="default">  {{ "PlanPremium.Add" | translate }} </button>
                        </div>
                    </div>
                    <div class="row">
                      <div class="col-4 col-md-4" >
                        <div class="switches" class="w-100">
                          <mat-slide-toggle class="mb-3" formControlName="bIsShowDecimals" (change)="changeDecimalValues()">
                            {{ "ShowValuesWithDecimals" | translate }}
                          </mat-slide-toggle>
                        </div>
                      </div>
                    </div>

                </div>
            </form>
            <div *ngIf="this.formPlanPremium.get('bIsCalculated')?.value === 1" class="row mt-2">
                <app-table-drag [displayedColumns]="estructPlansPremiumTable" [data]="listPlanPremium"
                    (iconClick)="controller($event)"
                    (listPlanPremiumToUpdate)="listPlanPremiumToUpdate($event)"></app-table-drag>
            </div>
        </div>
    </ng-container>
    <ng-container  customButtonRight>
        <button  type="button" (click)="savePlanPremiumValues()" mat-raised-button color="primary">
            {{ "Save" | translate }}
            <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        </button>
    </ng-container>
</div>
