<ng-container *ngIf="!showPreview">
  <form [formGroup]="formName">
    <mat-form-field
      class="w-100 field-input"
      *ngIf="
        field.fkIIdFieldType !== TypeField.Checkbox &&
        field.fkIIdFieldType !== TypeField.Radio
      "
      [ngSwitch]="field.fkIIdFieldType"
    >
      <mat-label>{{ field.vNameField }}</mat-label>

      <!--Field Type Numeric-->
      <input
        [formControlName]="field.pkIIdFieldModule + '_' + field.vNameFieldDb"
        matInput
        *ngSwitchCase="TypeField.Numeric"
        type="text"
        class="form-control"
        [disabled]="field.bIsReadonly"
        [id]="field.vNameField"
        [name]="field.vNameField"
        [attr.maxlength]="field.iMaxLength"
        [attr.minlength]="field.iMinLength"
        [required]="field.bRequired"
        [placeholder]="field.vHelpText"
        (keypress)="allowOnlyNumbers($event)"
      />

      <!--Field Type DropDown List-->
      <mat-select
        [formControlName]="field.pkIIdFieldModule + '_' + field.vNameFieldDb"
        [disabled]="field.bIsReadonly"
        matInput
        *ngSwitchCase="TypeField.DropDownList"
        class="form-control"
        [id]="field.vNameField"
        [required]="field.bRequired"
      >
        <mat-option *ngFor="let option of catalogs" [value]="option.Id">{{
          option.Value
        }}</mat-option>
      </mat-select>

      <!--Field Type Files-->
      <ng-container *ngSwitchCase="TypeField.UploadDocuments">
        <div class="file-upload-container">
          <button
            mat-icon-button
            matPrefix
            (click)="fileInput.click()"
            [disabled]="field.bIsReadonly"
          >
            <mat-icon>attach_file</mat-icon>
          </button>
          <input
            type="text"
            readonly
            matInput
            [disabled]="field.bIsReadonly"
            [value]="selectedFileName"
          />
          <label class="file-name-label">{{ selectedFileName }}</label>
          <input
            #fileInput
            (onChangeValidated)="onFileChange($event)"
            [accept]="parseAcceptFormats(field.vFormat)"
            [disabled]="field.bIsReadonly"
            [multiple]="field.bAllowMultipleUploads"
            type="file"
            [required]="field.bRequired"
            style="display: none"
            ValidationInputFile 
            [allowedExtensions]="[field.vFormat]" 
            [maxFileSizeMB]="field.iMaxLength"
          />
        </div>
      </ng-container>

      <!--Field Type Only Text-->
      <input
        [formControlName]="field.pkIIdFieldModule + '_' + field.vNameFieldDb"
        (keypress)="blockNumbers($event)"
        [disabled]="field.bIsReadonly"
        matInput
        *ngSwitchCase="TypeField.Text"
        type="text"
        class="form-control"
        [id]="field.vNameField"
        [name]="field.vNameFieldDb"
        [required]="field.bRequired"
        [attr.maxlength]="field.iMaxLength"
        [attr.minlength]="field.iMinLength"
        [placeholder]="field.vHelpText"
        pattern="[A-Za-záéíóúüñÁÉÍÓÚÜÑ\s]+"
        inputmode="text"
      />

      <!--Field Type Date-->
      <ng-container *ngSwitchCase="TypeField.Date">
        <div style="display: flex">
          <input
            [formControlName]="
              field.pkIIdFieldModule + '_' + field.vNameFieldDb
            "
            class="w-90"
            [disabled]="field.bIsReadonly"
            matInput
            [matDatepicker]="picker"
            [id]="field.vNameField"
            [required]="field.bRequired"
            [placeholder]="field.vHelpText"
          />
          <mat-datepicker-toggle
            matDatepickerToggleIcon
            matIconSuffix
            [for]="picker"
          ></mat-datepicker-toggle>
        </div>
        <mat-datepicker matDatepickerToggle #picker></mat-datepicker>
        <mat-hint>{{ field.vFormat }}</mat-hint>
      </ng-container>

      <!--Field Type Default-->
      <input
        [formControlName]="field.pkIIdFieldModule + '_' + field.vNameFieldDb"
        [disabled]="field.bIsReadonly"
        matInput
        *ngSwitchDefault
        type="text"
        class="form-control"
        [id]="field.vNameField"
        [name]="field.vNameField"
        [required]="field.bRequired"
        [attr.maxlength]="field.iMaxLength"
        [attr.minlength]="field.iMinLength"
        [placeholder]="field.vHelpText"
      />
    </mat-form-field>

    <!--Field Type Radio Multiple Choices-->
    <ng-container *ngIf="field.fkIIdFieldType === TypeField.Radio">
      <fieldset>
        <legend>{{ field.vNameField }}</legend>
        <mat-radio-group
          [formControlName]="field.pkIIdFieldModule + '_' + field.vNameFieldDb"
          aria-label="field.vNameField"
          [required]="field.bRequired"
          [disabled]="field.bIsReadonly"
        >
          <mat-radio-button
            *ngFor="let option of catalogs"
            [value]="option.Id"
            >{{ option.Value }}</mat-radio-button
          >
        </mat-radio-group>
      </fieldset>
    </ng-container>

    <!--Field Type Checkbox-->
    <ng-container *ngIf="field.fkIIdFieldType === TypeField.Checkbox">
      <mat-checkbox
        [formControlName]="field.pkIIdFieldModule + '_' + field.vNameFieldDb"
        [disabled]="field.bIsReadonly"
        [required]="field.bRequired"
        class="form-control"
      >
        {{ field.vNameField }}
      </mat-checkbox>
    </ng-container>
  </form>
</ng-container>

<ng-container *ngIf="showPreview">
  <form [formGroup]="formName">
    <mat-form-field
      class="w-100 field-input"
      *ngIf="
        field.fkIIdFieldType !== TypeField.Checkbox &&
        field.fkIIdFieldType !== TypeField.Radio
      "
      [ngSwitch]="field.fkIIdFieldType"
    >
      <mat-label>{{ field.vNameField }}</mat-label>

      <!--Field Type Numeric-->
      <input
        matInput
        *ngSwitchCase="TypeField.Numeric"
        type="text"
        class="form-control"
        [disabled]="field.bIsReadonly"
        [id]="field.vNameField"
        [name]="field.vNameField"
        [attr.maxlength]="field.iMaxLength"
        [attr.minlength]="field.iMinLength"
        [required]="field.bRequired"
        [placeholder]="field.vHelpText"
        (keypress)="allowOnlyNumbers($event)"
      />

      <!--Field Type DropDown List-->
      <mat-select
        [disabled]="field.bIsReadonly"
        matInput
        *ngSwitchCase="TypeField.DropDownList"
        class="form-control"
        [id]="field.vNameField"
        [required]="field.bRequired"
      >
        <mat-option *ngFor="let option of catalogs" [value]="option.Id">{{
          option.Value
        }}</mat-option>
      </mat-select>

      <!--Field Type Files-->
      <ng-container *ngSwitchCase="TypeField.UploadDocuments">
        <div class="file-upload-container">
          <button
            mat-icon-button
            matPrefix
            (click)="fileInput.click()"
            [disabled]="field.bIsReadonly"
          >
            <mat-icon>attach_file</mat-icon>
          </button>
          <input
            type="text"
            readonly
            matInput
            [disabled]="field.bIsReadonly"
            [value]="selectedFileName"
          />
          <label class="file-name-label">{{ selectedFileName }}</label>
          <input
            #fileInput
            (change)="onFileChange($event)"
            [accept]="parseAcceptFormats(field.vFormat)"
            [disabled]="field.bIsReadonly"
            [multiple]="field.bAllowMultipleUploads"
            type="file"
            [required]="field.bRequired"
            style="display: none"
            ValidationInputFile 
            [allowedExtensions]="[field.vFormat]" 
            [maxFileSizeMB]="field.iMaxLength"
          />
        </div>
      </ng-container>

      <!--Field Type Only Text-->
      <input
        (keypress)="blockNumbers($event)"
        [disabled]="field.bIsReadonly"
        matInput
        *ngSwitchCase="TypeField.Text"
        type="text"
        class="form-control"
        [id]="field.vNameField"
        [name]="field.vNameFieldDb"
        [required]="field.bRequired"
        [attr.maxlength]="field.iMaxLength"
        [attr.minlength]="field.iMinLength"
        [placeholder]="field.vHelpText"
        pattern="[A-Za-záéíóúüñÁÉÍÓÚÜÑ\s]+"
        inputmode="text"
      />

      <!--Field Type Date-->
      <ng-container *ngSwitchCase="TypeField.Date">
        <div style="display: flex">
          <input
            class="w-90"
            [disabled]="field.bIsReadonly"
            matInput
            [matDatepicker]="picker"
            [id]="field.vNameField"
            [required]="field.bRequired"
            [placeholder]="field.vHelpText"
          />
          <mat-datepicker-toggle
            matDatepickerToggleIcon
            matIconSuffix
            [for]="picker"
          ></mat-datepicker-toggle>
        </div>
        <mat-datepicker matDatepickerToggle #picker></mat-datepicker>
        <mat-hint>{{ field.vFormat }}</mat-hint>
      </ng-container>

      <!--Field Type Default-->
      <input
        [disabled]="field.bIsReadonly"
        matInput
        *ngSwitchDefault
        type="text"
        class="form-control"
        [id]="field.vNameField"
        [name]="field.vNameField"
        [required]="field.bRequired"
        [attr.maxlength]="field.iMaxLength"
        [attr.minlength]="field.iMinLength"
        [placeholder]="field.vHelpText"
      />
    </mat-form-field>

    <!--Field Type Radio Multiple Choices-->
    <ng-container *ngIf="field.fkIIdFieldType === TypeField.Radio">
      <fieldset>
        <legend>{{ field.vNameField }}</legend>
        <mat-radio-group
          aria-label="field.vNameField"
          [required]="field.bRequired"
          [disabled]="field.bIsReadonly"
        >
          <mat-radio-button
            *ngFor="let option of catalogs"
            [value]="option.Id"
            >{{ option.Value }}</mat-radio-button
          >
        </mat-radio-group>
      </fieldset>
    </ng-container>

    <!--Field Type Checkbox-->
    <ng-container *ngIf="field.fkIIdFieldType === TypeField.Checkbox">
      <mat-checkbox
        [disabled]="field.bIsReadonly"
        [required]="field.bRequired"
        class="form-control"
      >
        {{ field.vNameField }}
      </mat-checkbox>
    </ng-container>
  </form>
</ng-container>
