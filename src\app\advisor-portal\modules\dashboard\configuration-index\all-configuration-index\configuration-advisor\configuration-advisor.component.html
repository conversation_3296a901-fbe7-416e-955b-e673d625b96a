<div class="col-12 col-md-12 mb-3">
  <h2>{{ "ConfigurationIndex.IndexOrder" | translate }}</h2>
  <p>{{ "ConfigurationIndex.ParagraphOrderIndex" | translate }}</p>
  <div class="row">
    <div class="col-4" *ngIf="listSectionIndex.length > 0">
      <div cdkDropList
           class="listdrop mt-20"
           (cdkDropListDropped)="dropOrderIndex($event)">
        <div class="style-box"
             *ngFor="let item of listSectionIndex; index as i"
             cdkDrag>
          <div class="w-10 drag-icon">
            <i class="material-icons" cdkDragHandle>reorder</i>
          </div>
          <div class="w-100 drag-content">
            {{ item.vName }}
          </div>
          <div class="w-10 delete-icon">
            <button mat-icon-button (click)="activateYDeactivateProducts(item)">
              <mat-icon>{{!item.bActive ? 'visibility_off' : 'visibility'}}</mat-icon>
              <!-- <i *ngIf="item.bActive" class="material-icons">visibility</i>
               <i *ngIf="!item.bActive" class="material-icons">visibility_off</i>-->
            </button>
          </div>
          <div class="w-10 delete-icon">
            <button (click)="editNameSectionIndex(item)" mat-icon-button>
              <i class="material-icons">create</i>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="col-4">
      <button type="button" mat-raised-button color="primary" (click)="updateSectionIndexByBusiness()">
        {{ "Save" | translate }}
      </button>
    </div>
  </div>
</div>

<!--Edit modal name customer products-->
<div>
  <ng-template #editNameCustomerModal>
    <app-modal2 [titleModal]="titelModal" (closeModal)="eventCloseModal($event)">
      <ng-container body>
        <div class="col-12 col-md-12">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>{{ "ConfigurationIndex.Name" | translate }}</mat-label>
            <input matInput [(ngModel)]="nameProduct" type="text" PreventionSqlInjector
                   class="input">
          </mat-form-field>
        </div>
      </ng-container>

      <ng-container customButtonRight>
        <button type="button"
                mat-raised-button
                color="primary"
                [mat-dialog-close]="nameProduct" cdkFocusInitial>
          {{"Add" | translate}}
          <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
        </button>
      </ng-container>
    </app-modal2>
  </ng-template>
</div>

<div class="mb-12">
  <app-all-active-news *ngIf="idBusinessByCountry > 0 && listSectionIndex.length > 0"
                       [idBusinessByCountry]="idBusinessByCountry"
                       [objetActNews]="searchActNewsId()"
                       [userType]="1"></app-all-active-news>
</div>

<div class="mb-12">
  <app-all-tops *ngIf="idBusinessByCountry > 0 && listSectionIndex.length > 0"
                [idBusinessByCountry]="idBusinessByCountry"
                [objetTop]="searchTopId()"></app-all-tops>
</div>

<div class="row">
  <h4>{{ "ConfigurationIndex.OrganizationChart" | translate }}</h4>
  <app-organization-chart *ngIf="listSectionIndex.length > 0"
                          [objetOrganizationChart]="searchOrgChartId()"></app-organization-chart>
</div>
