<form [formGroup]="form" (ngSubmit)="complete()">
  <mat-slide-toggle class="mb-3" formControlName="bActive">
    {{"Insurer.ActiveInsurer" | translate}}
  </mat-slide-toggle>

  <div class="row">
    <div class="col-12 col-md-8">
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ "Insurer.BusinessName" | translate }}
        </mat-label>
        <input matInput formControlName="vName"/>
        <mat-error
          *ngIf="utilsSvc.isControlHasError(form, 'vName', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{"Insurer.RegistryNumber" | translate}}
        </mat-label>
        <input matInput formControlName="vRegistrationNumber" PreventionSqlInjector />
        <mat-error
          *ngIf="
            utilsSvc.isControlHasError(form, 'vRegistrationNumber', 'required')
          "
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ "Insurer.Description" | translate }}
        </mat-label>
        <input matInput formControlName="vDescription" PreventionSqlInjector/>
        <mat-error
          *ngIf="utilsSvc.isControlHasError(form, 'vDescription', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field class="w-100" appearance="fill">
        <mat-label>
          {{"Insurer.GlobalInsurer" | translate}}
        </mat-label>
        <mat-select
          formControlName="fkIIdInsuranceGlobal"
          (openedChange)="clickSelect($event)"
        >
          <mat-option
            *ngFor="let insurer of insurers"
            [value]="insurer.pkIIdInsuranceGlobal"
          >
            {{ insurer.vName }}
          </mat-option>
        </mat-select>
        <mat-error
          *ngIf="
            utilsSvc.isControlHasError(form, 'fkIIdInsuranceGlobal', 'required')
          "
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <app-generic-image-picker
      class="col-12 col-md-4"
      [title]="'Insurer.InsurerLogo' | translate"
      description=""
      (changeFile)="changeImage($event)"
      [imageSrc]="imageSrc"
    >
    </app-generic-image-picker>
  </div>
  <div class="row">
    <div
      class="col-12 col-md-8 d-flex gap-3 flex-column flex-lg-row justify-content-between mt-3"
    >
      <button mat-raised-button type="button" (click)="goBack()">
        <mat-icon fontIcon="arrow_back"></mat-icon>
        {{ "Insurer.BackToInsurers" | translate }}
      </button>
      <div
        class="d-flex gap-3 flex-column flex-lg-row"
        *ngIf="operationType === 'create'"
      >
        <button type="submit" mat-raised-button color="primary">
          {{ "Insurer.CreateInsurer" | translate }}
        </button>
      </div>
      <div
        class="d-flex gap-3 flex-column flex-lg-row"
        *ngIf="operationType === 'edit'"
      >
        <button type="submit" mat-raised-button color="primary">
          <mat-icon iconPositionEnd  fontIcon="save"></mat-icon>
          {{ "SaveChanges" | translate }}
        </button>
      </div>
    </div>
  </div>
</form>
