<div class="title" class = "p-5">

  <div class="row mb-2 mt-2 mb-2">
    <h4 class="col-md-12">{{ 'SettingsQuotes.Management.TitleState' | translate }}</h4> <!-- Estados de gestión de cliente  -->
  </div>

  <div>
    <app-table
      [displayedColumns]="estructStateSettingsTable"
      [data]="stateTable"
      [pageSize]="pageSize"
      (iconClick)="controller($event)"
    ></app-table>
  </div>

  <div class="cont-btn-create">
    <button
      class="mt-2 w-20 mb-2"
      type="button"
      (click)="openModalCreateEditElementState()"
      mat-raised-button
      color="primary"
      [disabled]="!validformFilter"
    >
      {{ 'SettingsQuotes.Management.AddState' | translate }} <!-- Añadir estado  -->

      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>



  <div class="row mb-2 mt-2 mb-2">
    <h4 class="col-md-12">{{ 'SettingsQuotes.Management.TitleMethod' | translate }}</h4> <!-- Métodos de contacto  -->
  </div>

  <div>
    <app-table
      [displayedColumns]="estructMethodSettingsTable"
      [data]="methodTable"
      [pageSize]="pageSize"
      (iconClick)="controller($event)"
    ></app-table>
  </div>

  <div class="cont-btn-create">
    <button
      class="mt-2 w-20"
      type="button"
      (click)="openModalCreateEditElementMethod()"
      mat-raised-button
      color="primary"
      [disabled]="!validformFilter"
    >
      {{ 'SettingsQuotes.Management.AddMethod' | translate }} <!-- Añadir método   -->

      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>

</div>



<!-- modal editar-crear estados -->
<ng-template #createEditElementStateModal>
  <app-modal2 [titleModal]="'SettingsQuotes.Management.TitleState' | translate " (closeModal)="closeModalEvent($event)">
    <ng-container body>
      <form [formGroup]="formCreateEditElementState">
        <div class="row">

          <div class="cont-slide">
            <div class="mx-3">
              <mat-slide-toggle class="mb-3" formControlName="active">
                {{ 'SettingsQuotes.Management.Modal.StateActive' | translate }} <!--  Estado activo   -->
              </mat-slide-toggle>
            </div>

          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ 'SettingsQuotes.Management.Modal.StateName' | translate }}  <!--  Nombre de estado    -->
              </mat-label>
              <input matInput formControlName="value" placeholder="{{ 'SettingsQuotes.Management.Modal.StateNamePlaceholder' | translate }}"  maxlength="100"  />
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    formCreateEditElementState,
                    'value',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ 'SettingsQuotes.Management.Modal.StateColor' | translate }}  <!--  Color de identificación   -->
              </mat-label>
              <mat-select formControlName="vColor">
                <mat-option *ngFor="let item of colorsList" [value]="item.Name">
                  {{ item.Name }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    formCreateEditElementState,
                    'vColor',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

        </div>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="completeState()"
        [disabled]="!validformCreateEditElement"
      >
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "Save" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>





<!-- modal editar-crear Metodos -->
<ng-template #createEditElementMethodModal>
  <app-modal2 [titleModal]="'SettingsQuotes.Management.TitleMethod' | translate " (closeModal)="closeModalEvent($event)">  <!-- Estado activo -->
    <ng-container body>
      <form [formGroup]="formCreateEditElementMethod">
        <div class="row">

          <div class="cont-slide">
            <div class="mx-3">
              <mat-slide-toggle class="mb-3" formControlName="active">
                {{ 'SettingsQuotes.Management.Modal.StateActive' | translate }} <!-- Estado activo -->
              </mat-slide-toggle>
            </div>

          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ 'SettingsQuotes.Management.Modal.MethodName' | translate }}  <!-- Metodo de contacto -->
              </mat-label>
              <input matInput formControlName="value" placeholder="{{ 'SettingsQuotes.Management.Modal.MethodNamePlaceholder' | translate }}"  maxlength="100" />
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    formCreateEditElementMethod,
                    'value',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          

        </div>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="completeMethod()"
        [disabled]="!validformCreateEditElement"
      >
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "Save" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>