import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BodyTableModel } from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { AllPolicyTableComponent } from '../../shared/components/all-policy-table/all-policy-table.component';
import { PolicyModel } from '../../shared/models';
import { PolicyTableService } from '../../shared/service/policy-table.service';
import { ActivatedRoute } from '@angular/router';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-page-all-individual-policy',
  standalone: true,
  imports: [
    CommonModule,
    AllPolicyTableComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './page-all-individual-policy.component.html',
  styleUrls: ['./page-all-individual-policy.component.scss'],
})
export class PageAllIndividualPolicyComponent implements OnInit {
  idPolicyType: number = 1;
  idBusinessByCountry: number = 0;
  estructTableIndividualPolicy: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'ID WTW Plantilla'
      ),
      columnValue: 'idwtwTemplate',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      ),
      columnValue: 'idwtw',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyNumber'
      ),
      columnValue: 'policyNumber',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      ),
      columnValue: 'insurance',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      ),
      columnValue: 'product',
    },
    {
      columnLabel: this._translateService.instant('Policy.Taker'),
      columnValue: 'customer',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.Document'),
      columnValue: 'docNumber',
    },
    {
      columnLabel: this._translateService.instant('Policy.Endorsement'),
      columnValue: 'endorsement',
    },
    {
      columnLabel: this._translateService.instant('Policy.StartOfValidity'),
      columnValue: 'startValidity',
      functionValue: (item: PolicyModel) =>
        this._utilsService.formatDate(item.startValidity, 'DD-MM-YYYY'),
    },
    {
      columnLabel: this._translateService.instant('Policy.EndOfValidity'),
      columnValue: 'endValidity',
      functionValue: (item: PolicyModel) =>
        this._utilsService.formatDate(item.endValidity, 'DD-MM-YYYY'),
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'statePolicy',
      functionValue: (item: PolicyModel) =>
        this._policyTableService.changePolicyStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant(
        'ProceduresViewerSettings.Manage'
      ),
      columnValue: 'manage',
      columnIcon: 'search',
    },
  ];

  constructor(
    private _customeRouter: CustomRouterService,
    private _translateService: TranslateService,
    private _policyTableService: PolicyTableService,
    private _activatedRoute: ActivatedRoute,
    private _utilsService: UtilsService
  ) {
    this.getDataUrl();
  }

  ngOnInit() {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //Traducción de los datos de la tabla que lista las pólizas individuales.
      this.estructTableIndividualPolicy[0].columnLabel =
        this._translateService.instant(
          'PolicyConfiguration.GeneralInformation.IdWTW'
        );
      this.estructTableIndividualPolicy[1].columnLabel =
        this._translateService.instant(
          'PolicyConfiguration.GeneralInformation.PolicyNumber'
        );
      this.estructTableIndividualPolicy[2].columnLabel =
        this._translateService.instant(
          'PolicyConfiguration.GeneralInformation.Insurance'
        );
      this.estructTableIndividualPolicy[3].columnLabel =
        this._translateService.instant(
          'PolicyConfiguration.GeneralInformation.Product'
        );
      this.estructTableIndividualPolicy[4].columnLabel =
        this._translateService.instant('Policy.Taker');
      this.estructTableIndividualPolicy[5].columnLabel =
        this._translateService.instant('MyProfile.Document');
      this.estructTableIndividualPolicy[6].columnLabel =
        this._translateService.instant('Policy.Endorsement');
      this.estructTableIndividualPolicy[7].columnLabel =
        this._translateService.instant('Policy.StartOfValidity');
      this.estructTableIndividualPolicy[8].columnLabel =
        this._translateService.instant('Policy.EndOfValidity');
      this.estructTableIndividualPolicy[9].columnLabel =
        this._translateService.instant('Status');
      this.estructTableIndividualPolicy[10].columnLabel =
        this._translateService.instant('ProceduresViewerSettings.Manage');
    });
  }

  //Obtiene los valores de las variables enviadas por medio de la URL.
  getDataUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
    });
  }

  //Obtiene toda la info del registro de la tabla en la acción Gestionar.
  getActionTable(event: PolicyModel) {
    this._customeRouter.navigate([
      `dashboard/policy/see-details-individual-policy/${event.idwtw}/${1}/${
        event.idPolicyRisk
      }/${event.idParent}`,
    ]);
  }

  //Funcicón que redirecciona al usuario al submódulo de cargue amsivo de pólizas, dentro del módulo de masivos.
  goToMassive() {
    this._customeRouter.navigate([
      `dashboard/massive/mass-creation-policy/${this.idBusinessByCountry}`,
    ]);
  }
}
