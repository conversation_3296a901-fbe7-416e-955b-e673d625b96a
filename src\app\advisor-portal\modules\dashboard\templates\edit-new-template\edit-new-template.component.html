<h3 class="h4">
  {{ titleText }}
</h3>
<form [formGroup]="form">
  <div class="row mt-3" *ngIf="idTemplate == 0">
    <mat-radio-group aria-label="Select an option" formControlName="bIsModule">
      <mat-radio-button
        #radioForProduct
        (change)="radioForChange(radioForProduct.value)"
        value="0"
      >
        {{ "Template.ForProduct" | translate }}
      </mat-radio-button>
      <mat-radio-button
        #radioForModule
        (change)="radioForChange(radioForModule.value)"
        value="1"
      >
        {{ "Template.ForModule" | translate }}
      </mat-radio-button>
    </mat-radio-group>
  </div>

  <div class="row mt-3" *ngIf="idTemplate == 0 && forModules">
    <!-- Modulo -->
    <mat-form-field appearance="outline" class="select-look w-25">
      <mat-label>
        {{ "FormsConfigurationHistory.Module" | translate }}
      </mat-label>
      <mat-select formControlName="module" required>
        <mat-option *ngFor="let item of moduleList" [value]="item">
          {{ item.vDescription }}
        </mat-option>
      </mat-select>
      <mat-error
        *ngIf="_utilsService.isControlHasError(form, 'module', 'required')"
      >
        {{ "ThisFieldIsRequired" | translate }}
      </mat-error>
    </mat-form-field>

    <!-- Sub modulo -->
    <mat-form-field
      appearance="outline"
      class="select-look w-25"
      *ngIf="moduleSelectedHasChildren"
    >
      <mat-label>
        {{ "FormsConfigurationHistory.Submodule" | translate }}
      </mat-label>
      <mat-select formControlName="subModule" #country required>
        <mat-option *ngFor="let item of subModuleList" [value]="item">
          {{ item.vDescription }}
        </mat-option>
      </mat-select>
      <mat-error
        *ngIf="_utilsService.isControlHasError(form, 'subModule', 'required')"
      >
        {{ "ThisFieldIsRequired" | translate }}
      </mat-error>
    </mat-form-field>

    <!-- Etapa -->
    <mat-form-field appearance="outline" class="select-look w-25">
      <mat-label>
        {{ "FormsConfigurationHistory.Stage" | translate }}
      </mat-label>
      <mat-select formControlName="stage" #country required>
        <mat-option *ngFor="let item of stageList" [value]="item">
          {{ item.vNameStage }}
        </mat-option>
      </mat-select>
      <mat-error
        *ngIf="_utilsService.isControlHasError(form, 'stage', 'required')"
      >
        {{ "ThisFieldIsRequired" | translate }}
      </mat-error>
    </mat-form-field>

    <!-- Estado -->
    <mat-form-field appearance="outline" class="select-look w-25">
      <mat-label>
        {{ "Status" | translate }}
      </mat-label>
      <mat-select formControlName="status" #country required>
        <mat-option *ngFor="let item of stageByStateList" [value]="item">
          {{ item.vState }}
        </mat-option>
      </mat-select>
      <mat-error
        *ngIf="_utilsService.isControlHasError(form, 'status', 'required')"
      >
        {{ "ThisFieldIsRequired" | translate }}
      </mat-error>
    </mat-form-field>
  </div>
  <div class="row mt-2" *ngIf="idTemplate == 0 && !forModules">
    <mat-form-field class="w-100 mb-2">
      <mat-label>
        {{ "Template.ProductAssociate" | translate }}
      </mat-label>
      <mat-select formControlName="fkIIdProduct">
        <mat-option
          *ngFor="let item of allProductsList"
          [value]="item.pkIIdProduct"
          >{{ item.vProductName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>



  <!-- Botones de detalle, inclusión y cotización marubeni buttons -->
  <div class="row mt-2" *ngIf="businessComponent?.vActualComponent === 'buttonsMarubeni'">

    <app-cl-marubeni-check-buttons [marubeniForm]="form"></app-cl-marubeni-check-buttons>

  </div>

  <!-- slides de estado plantilla, enmascarar, cc y cco -->
  <div class="row mt-3">
    <h4 class="fw-bold">{{ "Template.TemplateConfiguration" | translate }}</h4>

    <!-- estado plantilla -->
    <div class="switches mt-3 w-25">
      <mat-slide-toggle
        class="mb-3"
        formControlName="bActive"
        (change)="slideStatusChange($event)"
      >
        {{ "Template.ActiveTemplate" | translate }}
      </mat-slide-toggle>
    </div>

    <!-- enmascarar correo -->
    <div class="switches mt-3 w-25">
      <mat-slide-toggle class="mb-3" formControlName="bMaskMail">
        {{ "Template.MaskMail" | translate }}
      </mat-slide-toggle>
    </div>

    <!-- copia -->
    <div class="switches mt-3 w-25">
      <mat-slide-toggle class="mb-3" formControlName="bCopy">
        {{ "Template.Copy" | translate }}
      </mat-slide-toggle>
    </div>

    <!-- copia oculta -->
    <div class="switches mt-3 w-25">
      <mat-slide-toggle class="mb-3" formControlName="bHiddenCopy">
        {{ "Template.HiddenCopy" | translate }}
      </mat-slide-toggle>
    </div>

    <!-- Permitir adjuntar archivo -->
    <div class="switches mt-3 w-25" *ngIf="businessComponent?.vActualComponent === 'buttonsMarubeni'">
      <mat-slide-toggle class="mb-3" formControlName="bIsAllowFileAttachment">
        {{ "Template.AllowFileAttachment" | translate }}
      </mat-slide-toggle>
    </div>

    <!-- Permitir agregar observaciones -->
    <div class="switches mt-3 w-25" *ngIf="businessComponent?.vActualComponent === 'buttonsMarubeni'">
      <mat-slide-toggle class="mb-3" formControlName="bIsAllowComments">
        {{ "Template.AllowComments" | translate }}
      </mat-slide-toggle>
    </div>
  </div>

  <!-- nombre de la plantilla -->
  <mat-form-field appearance="outline" class="w-100 mt-3">
    <mat-label>
      {{ "Template.TemplateName" | translate }}
    </mat-label>
    <input matInput formControlName="vName" required type="text" />
    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vName', 'required')"
    >
      {{ "ThisFieldIsRequired" | translate }}
    </mat-error>
  </mat-form-field>

  <!-- correo que hace el envio -->
  <mat-form-field appearance="outline" class="w-100 mt-3">
    <mat-label>
      {{ "Template.MailFrom" | translate }}
    </mat-label>
    <input matInput formControlName="vSender" required type="email" />
    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vSender', 'required')"
    >
      {{ "ThisFieldIsRequired" | translate }}
    </mat-error>
    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vSender', 'pattern')"
    >
      {{ "ThisEmailIsNotValid" | translate }}
    </mat-error>
  </mat-form-field>

  <!-- correo que recibe la información -->
  <mat-form-field appearance="outline" class="w-100 mt-3" *ngIf="businessComponent?.vActualComponent === 'buttonsMarubeni'">
    <mat-label>
      {{ "Template.EmailReceiveInformation" | translate }}
    </mat-label>
    <input matInput formControlName="vMailSendInfo" (focus)="onFocus('vMailSendInfo')" required type="email" />
    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vMailSendInfo', 'required')"
    >
      {{ "ThisFieldIsRequired" | translate }}
    </mat-error>
    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vMailSendInfo', 'pattern')"
    >
      {{ "ThisEmailIsNotValid" | translate }}
    </mat-error>
  </mat-form-field>

  <!-- correo para enmascarar -->
  <mat-form-field *ngIf="maskMail" appearance="outline" class="w-100 mt-3">
    <mat-label>
      {{ "Template.MaskMail" | translate }}
    </mat-label>
    <input matInput formControlName="vMaskEmail" type="email" />

    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vMaskEmail', 'required')"
    >
      {{ "ThisFieldIsRequired" | translate }}
    </mat-error>
    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vMaskEmail', 'pattern')"
    >
      {{ "ThisEmailIsNotValid" | translate }}
    </mat-error>
  </mat-form-field>
  <span style="color: gray">{{ "Template.MaskMailSpan" | translate }}</span>


  <div class="mt-3" *ngIf="forModules">

    <h4 class="fw-bold">{{ "Template.Recipient" | translate }} </h4> <!-- Destinatario -->
    <div class="row mt-3">
      <!-- enviar al usuario log -->
      <div class="switches mt-2 w-25">
        <mat-slide-toggle class="mb-3" formControlName="bIsSendLogUser">
          {{ "Template.SendUserLog" | translate }}
        </mat-slide-toggle>
      </div>

      <!-- enviar al creador de la tarea -->
      <div class="switches mt-2 w-25">
        <mat-slide-toggle class="mb-3" formControlName="bIsSendTaskCreate">
          {{ "Template.SendTaskCreator" | translate }}
        </mat-slide-toggle>
      </div>

      <!-- enviar al radicar tarea -->
      <div class="switches mt-2 w-25" *ngIf="idTemplate == idSpecialTemplate || idSpecialTemplate == 0">
        <mat-slide-toggle class="mb-3" formControlName="bIsSendFileTask">
          {{ "Template.SendFileTask" | translate }}
        </mat-slide-toggle>
      </div>

      <!-- enviar al usuario asignado -->
      <div class="switches mt-2 w-25" *ngIf="idTemplate == idSpecialTemplate || idSpecialTemplate == 0">
        <mat-slide-toggle class="mb-3" formControlName="bSendToUserAssigned">
          {{ "Template.SendToUserAssigned" | translate }}
        </mat-slide-toggle>
      </div>
    </div>

    <!-- correo que recibe la información se cambia a Destinatario(s) -->
    <mat-form-field appearance="outline" class="w-100 mt-3">
      <mat-label>
        {{ "Template.Recipients" | translate }} <!-- Destinatario(s) -->
      </mat-label>
      <input matInput formControlName="vMailRecipients" (focus)="onFocus('vMailRecipients')" type="text" />

      <mat-error *ngIf="_utilsService.isControlHasError(form, 'vMailRecipients', 'invalidEmails')">
        {{ "Template.RecipientAlert1" | translate }}  <!-- Algunos correos no son válidos o están mal formateados. -->
      </mat-error>

      <mat-error *ngIf="_utilsService.isControlHasError(form, 'vMailRecipients', 'pattern')">
        {{ "Template.RecipientAlert2" | translate }} <!-- El formato de los correos es incorrecto. -->
      </mat-error>


    </mat-form-field>
    <span style="color: gray"> {{ "Template.RecipientsMessage" | translate }}</span> <!--Si desea ingresar multiples destinatarios, separelos con una coma.-->

    <!-- Destinatarios por campo  -->
    <mat-form-field appearance="outline" class="w-100 mt-3">
      <mat-label>
        {{ "Template.RecipientsField" | translate }}
      </mat-label>
      <input matInput formControlName="vMailSendFieldInfo" (focus)="onFocus('vMailSendFieldInfo')" type="text" />
    </mat-form-field>
    <span style="color: gray"> {{ "Template.RecipientsFieldMessage" | translate }}</span>

    <!-- boton Añadir Campo    **email-->
    <div class="row mt-3">
      <button
        class="mx-3 mt-2 col-2"
        type="button"
        (click)="openAddFieldEmailDialog()"
        mat-raised-button
        color="primary"
      >
        {{ "Template.AddField" | translate }}
      </button>
    </div>

  </div>


  <!-- copia -->
  <mat-form-field *ngIf="copy" appearance="outline" class="w-100 mt-3">
    <mat-label>
      {{ "Template.Copy" | translate }}
    </mat-label>
    <input matInput formControlName="vCc" type="email" />
    <mat-error *ngIf="_utilsService.isControlHasError(form, 'vCc', 'required')">
      {{ "ThisFieldIsRequired" | translate }}
    </mat-error>
    <mat-error *ngIf="_utilsService.isControlHasError(form, 'vCc', 'pattern')">
      {{ "ThisEmailIsNotValid" | translate }}
    </mat-error>
  </mat-form-field>

  <!-- copia oculta -->
  <mat-form-field *ngIf="hiddenCopy" appearance="outline" class="w-100 mt-3">
    <mat-label>
      {{ "Template.HiddenCopy" | translate }}
    </mat-label>
    <input matInput formControlName="vCco" required type="email" />
    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vCco', 'required')"
    >
      {{ "ThisFieldIsRequired" | translate }}
    </mat-error>
    <mat-error *ngIf="_utilsService.isControlHasError(form, 'vCco', 'pattern')">
      {{ "ThisEmailIsNotValid" | translate }}
    </mat-error>
  </mat-form-field>

  <!-- asunto -->
  <mat-form-field appearance="outline" class="w-100 mt-3">
    <mat-label>
      {{ "Template.Subject" | translate }}
    </mat-label>
    <input matInput #emailSubjectInput (focus)="onFocus('vEmailSubject')"  formControlName="vEmailSubject" type="text" />
    <mat-error
      *ngIf="_utilsService.isControlHasError(form, 'vEmailSubject', 'required')"
    >
      {{ "ThisFieldIsRequired" | translate }}
    </mat-error>
  </mat-form-field>

  <!-- mensaje -->
  <div id="test3 row" class="w-100 mt-3">
    <div class="label">
      <span>{{ "Template.Message" | translate }}:</span>
    </div>
    <div tabindex="0">
      <quill-editor
        (click)="onFocus('vMessage')"
        #quillEditor
        formControlName="vMessage"
        class="quill-editor mt-3"
      ></quill-editor>
      <div cdkDropList (cdkDropListDropped)="drop($event)">
        <div *ngFor="let campo of campos; cdkDrag">
          {{ campo }}
        </div>
      </div>
    </div>
  </div>
</form>

<!-- boton agregar campo -->
<div class="row mt-3">
  <button
    class="mx-3 mt-2 col-2"
    type="button"
    (click)="openAddFieldDialog()"
    mat-raised-button
    color="primary"
  >
    {{ "Template.AddField" | translate }}
  </button>
</div>

<!-- botones inferiores -->
<div class="row w-auto">
  <!-- boton cancelar -->
  <div class="w-auto m-auto">
    <button
      class="w-auto"
      mat-raised-button
      type="button"
      style="background-color: transparent"
      (click)="goBackClick()"
    >
      <mat-icon style="margin-right: 10px" fontIcon="arrow_back"></mat-icon>
      {{ "Cancel" | translate }}
    </button>
  </div>
  <!-- boton crear, en caso de nuevo -->
  <div class="w-auto m-auto" *ngIf="operationType === 'create'">
    <button
      type="button"
      mat-raised-button
      color="primary"
      (click)="saveTemplateClick()"
    >
      {{ "Template.CreateTemplate" | translate }}
    </button>
  </div>
  <!-- boton guardar, en caso de actualizar -->
  <div class="w-auto m-auto" *ngIf="operationType === 'edit'">
    <button
      type="button"
      mat-raised-button
      color="primary"
      (click)="saveTemplateClick()"
    >
      <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      {{ "SaveChanges" | translate }}
    </button>
  </div>
</div>

<!-- modal agregar campo -->
<ng-template #addFieldModal>
  <app-modal2
    [titleModal]="getTitleForModal()"
    (closeModal)="eventCloseModal($event)"
  >
    <ng-container body>
      <form [formGroup]="fieldForm">
        <!-- Campo -->
        <mat-form-field appearance="outline" class="select-look w-100">
          <mat-label>
            {{ "Template.Field" | translate }}
          </mat-label>

          <mat-select formControlName="idCampo" #field required>
            <mat-option *ngFor="let item of fieldList" [value]="item">
              {{ item.vNameField }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="_utilsService.isControlHasError(form, 'idCampo', 'required')"
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </form>
    </ng-container>
    <ng-container customButtonRight>
      <button
        class="w-auto"
        type="button"
        mat-raised-button
        color="primary"
        (click)="insertContentInQuill(field.value)"
      >
        {{ "Add" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
