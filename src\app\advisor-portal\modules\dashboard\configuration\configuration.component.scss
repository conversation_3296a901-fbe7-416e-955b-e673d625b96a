.card-menu {
  width: 5%;
  text-align: center;
}

.card-menu:hover {
  cursor: pointer;
}

.container-modules{
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

/* Estilo base para el título */
.mat-card-title {
    font-size: 1.125rem; /* 18px en relación con el tamaño de fuente base del documento */
}

.cont-row {
  display: flex;
  align-items: center;
  justify-content: center;
}
.cont-icon {
  margin-bottom: 15px;
}