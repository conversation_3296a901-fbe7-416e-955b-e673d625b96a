<form [formGroup]="formCopyrigth">
  <!-- logo footer -->
    <mat-card-title>Logo footer</mat-card-title>
    <br>
    <mat-card-subtitle>
      Recuerde que este logo debe ser blanco o generar contraste con el footer
    </mat-card-subtitle>
    <div class="row">
      <div class="col">
        <div class="logo-preview-container">
          <img mat-card-image  *ngIf="imageSrc" [src]="imageSrc" alt="" class="logo-preview" />
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <button type="button" mat-raised-button color="primary" (click)="selectLogo()">
          Seleccionar
        </button>
      </div>
    </div>
    <input type="file" (onChangeValidated)="onFileSelected($event)" #fileInput hidden accept=".png,.svg" ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"/>
  <!-- fin logo footer -->
<br>
  <!-- categorias -->
  <h5 class="fw-bold mb-2">Categorias</h5>
  <app-table [displayedColumns]="estructTableCategory" [data]="dataTableCategory"
        (iconClick)="controller($event)"></app-table>
  <div class="row">
          <div class="col">
            <button type="button" mat-raised-button color="primary" (click)="openModalCategory()">
              {{ "Add" | translate }}
            </button>
          </div>
  </div>
  <!-- fin categorias -->
  <br>

  <!-- links -->
  <h5 class="fw-bold mb-2">Links</h5>
  <app-table [displayedColumns]="estructTableLinks" [data]="dataTableLinks"
        (iconClick)="controller($event)"></app-table>
        <div class="row">
          <div class="col">
            <button type="button" mat-raised-button color="primary" (click)="openModalLink()">
              {{ "Add" | translate }}
            </button>
          </div>
  </div>
  <!-- fin links -->
  <br>

  <!-- Aviso de privacidad -->
  <div class="row mb-3">
    <h5 class="fw-bold mb-2">Aviso de privacidad</h5>
    <!-- Título -->
    <mat-form-field appearance="outline" class="w-100 mb-2">
      <mat-label>
        {{'Title' | translate}}
      </mat-label>
      <input matInput [(ngModel)]="titlePrivacyPolices" [ngModelOptions]="{standalone: true}" />
    </mat-form-field>

    <!-- Subtítulo -->
    <mat-form-field appearance="outline" class="w-100 mb-2">
      <mat-label>
        {{'Subtitle' | translate}}
      </mat-label>
      <textarea matInput [(ngModel)]="subtitlePrivacyPolices" [ngModelOptions]="{standalone: true}"></textarea>
    </mat-form-field>

    <div class="row mb-3">
      <div class="col">
        <!-- Guardar -->
        <button type="button" mat-raised-button color="primary" (click)="createUpdatePrivacyPoliciesFooter()" [disabled]="!titlePrivacyPolices">
          {{ "Save" | translate }}
        </button>
      </div>
    </div>

    <!-- Tabla aviso de privacidad -->
    <app-table [displayedColumns]="estructTablePrivacyPolicies" [data]="dataTablePrivacyPolices"
      (iconClick)="controller($event)"></app-table>
    <div class="row">
      <div class="col">
        <!-- Añadir -->
        <button type="button" mat-raised-button color="primary" (click)="openModalPrivacyPolicies()" [disabled]="pkPrivacyPolicies === 0">
          {{ "Add" | translate }}
        </button>
      </div>
    </div>
  </div>
   <!-- fin Aviso de privacidad -->

  <!-- copyright -->
  <h5 class="fw-bold mb-2">
    Copyright
    <!-- {{ 'configurationClientPortal.Pestana' | translate }} -->
  </h5>
  <mat-form-field appearance="outline" class="w-100 mb-2">
    <mat-label>
      {{'SectionClientPortal.Copyright' | translate}}
    </mat-label>
    <input matInput formControlName="vText" />
  </mat-form-field>
  <!-- fin copyright -->
  <br>

  <!-- contacto -->
  <h5 class="fw-bold mb-2">Contacto</h5>
  <app-table [displayedColumns]="estructTableContacto" [data]="dataTableContacto"
        (iconClick)="controller($event)"></app-table>
        <div class="row">
          <div class="col">
            <button type="button" mat-raised-button color="primary" (click)="openModalContact()">
              {{ "Add" | translate }}
            </button>
          </div>
  </div>
  <!-- fin contacto -->

  <br>

  <!-- disclaimer -->
  <h5 class="fw-bold mb-2">Disclaimer</h5>
  <mat-form-field appearance="outline" class="w-100 mb-2">
    <mat-label>
      Disclaimer
      <!-- {{'SectionClientPortal.Copyright' | translate}} -->
    </mat-label>
    <textarea matInput formControlName="vDisclaimer" ></textarea>
  </mat-form-field>
  <!-- fin disclaimer -->
</form>

<div class="row">
  <div class="col">
    <button
      type="button"
      mat-raised-button
      color="primary"
      (click)="save()"
    >
      Guardar Cambios
    </button>
  </div>
</div>


<ng-template #createEditCategory>
  <app-modal2
      [titleModal]="(pkCategoryToModify === 0 ? 'configurationClientPortal.CreateSubPestanaTitle' : 'configurationClientPortal.UpdateSubPestanaTitle') | translate">
      <ng-container body>
          <form [formGroup]="formCategory">
              <mat-form-field appearance="outline" class="w-100 mb-2">
                  <mat-label>{{ 'configurationClientPortal.namePestana' | translate }}</mat-label>
                  <input matInput formControlName="VName" />
                  <mat-error *ngIf="utilsSvc.isControlHasError(formCategory, 'VName', 'required')">
                      Este campo es obligatorio
                  </mat-error>
              </mat-form-field>
          </form>
      </ng-container>
      <ng-container customButtonRight>
          <div class="modal-footer">
              <button mat-raised-button color="primary" type="button" class="" (click)="saveCategory()" >
                  {{ "Save" | translate }}
              </button>
          </div>
      </ng-container>
  </app-modal2>
</ng-template>

<ng-template #createEditLink>
  <app-modal2
      [titleModal]="(pkLinkToModify === 0 ? 'Link Footer' : 'Editar Link Footer') | translate">
      <ng-container body>
          <form [formGroup]="formLink">
            <mat-form-field id="state" class="w-100">
              <mat-label>{{ 'Categoria' | translate }}</mat-label>
              <mat-select formControlName="FkIIdCategory">
                <mat-option *ngFor="let product of dataTableCategory" [value]="product.pkIIdCategory">{{ product.vName
                      }}</mat-option>
              </mat-select>
            </mat-form-field>

              <mat-form-field appearance="outline" class="w-100 mb-2">
                  <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                  <input matInput formControlName="VName" />
                  <mat-error *ngIf="utilsSvc.isControlHasError(formLink, 'VName', 'required')">
                      Este campo es obligatorio
                  </mat-error>
              </mat-form-field>
              <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>Link</mat-label>
                <input matInput formControlName="vLink" />
                <mat-error *ngIf="utilsSvc.isControlHasError(formLink, 'vLink', 'required')">
                    Este campo es obligatorio
                </mat-error>
            </mat-form-field>
          </form>
      </ng-container>
      <ng-container customButtonRight>
          <div class="modal-footer">
              <button mat-raised-button color="primary" type="button" class="" (click)="saveLink()" >
                  {{ "Save" | translate }}
              </button>
          </div>
      </ng-container>
  </app-modal2>
</ng-template>

<ng-template #createEditContact>
  <app-modal2
      [titleModal]="(pkContactToModify === 0 ? 'Contacto' : 'Editar Contacto') | translate">
      <ng-container body>
          <form [formGroup]="formContact">
              <mat-form-field appearance="outline" class="w-100 mb-2">
                  <mat-label>{{ 'texto' | translate }}</mat-label>
                  <input matInput formControlName="VName" />
                  <mat-error *ngIf="utilsSvc.isControlHasError(formContact, 'VName', 'required')">
                      Este campo es obligatorio
                  </mat-error>
              </mat-form-field>
              <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>{{ 'contacto' | translate }}</mat-label>
                <input matInput formControlName="VContact" />
                <mat-error *ngIf="utilsSvc.isControlHasError(formContact, 'VContact', 'required')">
                    Este campo es obligatorio
                </mat-error>
            </mat-form-field>
          </form>
      </ng-container>
      <ng-container customButtonRight>
          <div class="modal-footer">
              <button mat-raised-button color="primary" type="button" class=""  (click)="saveContact()" >
                  {{ "Save" | translate }}
              </button>
          </div>
      </ng-container>
  </app-modal2>
</ng-template>

<ng-template #createEditPrivacyPolicies>
  <app-modal2
      [titleModal]="'Link aviso de privacidad' | translate">
      <ng-container body>
          <form [formGroup]="formLinkPrivacyPolicies">
              <mat-form-field appearance="outline" class="w-100 mb-2">
                  <mat-label>{{ 'Nombre' | translate }}</mat-label>
                  <input matInput formControlName="name" />
                  <mat-error *ngIf="utilsSvc.isControlHasError(formLinkPrivacyPolicies, 'name', 'required')">
                      Este campo es obligatorio
                  </mat-error>
              </mat-form-field>
              <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>{{ 'Link' | translate }}</mat-label>
                <input matInput formControlName="link" />
                <mat-error *ngIf="utilsSvc.isControlHasError(formLinkPrivacyPolicies, 'link', 'required')">
                    Este campo es obligatorio
                </mat-error>
            </mat-form-field>
          </form>
      </ng-container>
      <ng-container customButtonRight>
          <div class="modal-footer">
              <button mat-raised-button color="primary" type="button" class=""  (click)="saveUpdateLinkPrivacyPolicies()" >
                  {{ "Save" | translate }}
              </button>
          </div>
      </ng-container>
  </app-modal2>
</ng-template>