import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  OnDestroy,
  Output,
  EventEmitter,
  ViewChild,
  TemplateRef,
  Input,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormsModule,
  FormArray,
  FormControl,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subscription, catchError, of, debounceTime } from 'rxjs';

import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import {MatTabsModule} from '@angular/material/tabs';
import {MatCardModule} from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { DpDatePickerModule,IDatePickerDirectiveConfig } from 'ng2-date-picker';

import { ActionsToCreateComponent } from 'src/app/shared/components/actions-to-create/actions-to-create.component';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { MatSelectModule } from '@angular/material/select';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { CalculationFormulasComponent } from 'src/app/shared/components/calculation-formulas/calculation-formulas.component';


import { PreviewFormComponent } from '../preview-form/preview-form.component';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import {
  FormModelCreate,
  TabModelCreate,
  SectionModelCreate,
  FieldModelCreate,
  CatalogCreate,
  TypeField,
} from 'src/app/shared/models/form/form.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CatalogModel } from 'src/app/shared/models/catalog-setting/catalog.model';


import { MessageService } from 'src/app/shared/services/message/message.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';


import { AppState } from 'src/app/store/app.reducers';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { BusinessComponentModel } from 'src/app/shared/models/business';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';

@Component({
  selector: 'app-step-form-data',
  standalone: true,
  imports: [
    TableComponent,
    CommonModule,
    MatRadioModule,
    MatInputModule,
    MatFormFieldModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    GenericImagePickerComponent,
    ActionsToCreateComponent,
    ModalComponent,
    MatSelectModule,
    MatCheckboxModule,
    PreviewFormComponent,
    TranslateModule,
    ChooseCountryAndCompanyComponent,
    PreventionSqlInjectorDirective,
    CalculationFormulasComponent,
    MatDatepickerModule,
    Modal2Component,
    MatCardModule,
    MatNativeDateModule,
    DpDatePickerModule,
    ValidationInputFileDirective
  ],
  templateUrl: './step-form-data.component.html',
  styleUrls: ['./step-form-data.component.scss'],
})
export class StepFormDataComponent implements OnInit, OnDestroy {
  //Modals
  @ViewChild('editNewTabModal') editNewTabModal?: TemplateRef<any>;
  @ViewChild('editNewSectionModal') editSectionTabModal?: TemplateRef<any>;
  @ViewChild('editNewFieldModal') editNewFieldModal?: TemplateRef<any>;
  @ViewChild('previewForm') previewForm?: TemplateRef<any>;
  @ViewChild('typeUpload') typeUpload?: TemplateRef<any>;
  @ViewChild('copyParameters') copyParameters?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;

  @Input() idFormInput: number = 0;
  @Input() vNameForm: string = '';
  @Input() bFormActive: boolean = false;
  @Input() vNameFormDb: string = '';

  itsJustFields: boolean = false;
  insuranceList?: Subscription;
  insuranceCompanies: InsuranceCompanyModel[] = [];

  productSubs?: Subscription;
  private _settingCountryAndCompanySubscription?: Subscription;
  formSubs?: Subscription;
  fieldSubs?: Subscription;
  productList?: Subscription;
  transactionSubs?: Subscription;

  nameColumnsCustomerTable: any[] = [];
  dataTableTab: any[] = [];
  dataTableSection: any[] = [];
  dataTableFields: any[] = [];
  products: any = [];
  orderListTab: number[] = [];
  orderListSection: number[] = [];
  orderListFields: number[] = [];
  disableListeners = false;
  fieldGroupers: any[] = [];
  catalogTable: CatalogModel[] = [];
  catalogFields: any[] = [];
  nameColumnsPolicyTable: any[] = [];
  allFields: any[] = [];
  catalogs: any[] = [];
  maxTextAreaLength: number = 2000;
  maxHelpTextLength: number = 100;
  maxTextOnlyLength: number = 50;

  typeFormatDate: any = [
    { format: 'YYYY-MM-DD' },
    { format: 'DD-MM-YYYY' },
    { format: 'MM-DD-YYYY' },
  ];

  typeFormatCoin: any = [
    { format: 'Pesos colombianos' },
    { format: 'Pesos mexicanos' },
    { format: 'Peso argentino' },
    { format: 'Dolar' },
    { format: 'Pesos chilenos' },
    { format: 'Colón costarricense' },
    { format: 'Córdoba de nicaragua' },
    { format: 'Real de Brasil' },
    { format: 'Guarani de paraguay ' },
    { format: 'Lempira hondureño' },
    { format: 'Quetzal de guatemala' },
  ];

  fieldTypes: any[] = [];
  idForm: number = 0;
  idProduct: number = 0;
  idProductReply: number = 0;
  isEditingField: boolean = false;
  isEditingTab: boolean = false;
  isEditingSection: boolean = false;
  selectedTabSections: any[] = [];
  options: number = 0;
  bHelpText: boolean = false;
  selectedFileTypes: string[] = [];
  fileTypes: any[] = [];
  idBusinessCountry: number = 0;
  idCountry: number = 0;
  businessComponent?: BusinessComponentModel;
  idFieldModule: number = 0;
  modeDatePicker: any = 'day';
  clockTypeIcon: boolean = false;
  config: any = {};
  placeholderText: string = '';
  typeDate: any[] = [];
  typeDateMinFilter: any[] = [];
  typeDateMaxFilter: any[] = [];
  maxSizeInMb = 20;
  fileName: string = '';
  showBtnDelete: boolean = false;
  isDragOver = false;
  allFieldsQuote: any[] = [];
  idFormQuote: number = 0;

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Order'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('Product.TabName'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Product.NameInDataBase'),
      columnValue: 'vNameDb',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  estructTableSections: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Order'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('Product.SectionName'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Product.Tab'),
      columnValue: 'fkIIdTab',
      functionValue: (item: any) => this.getTabNameById(item.fkIIdTab),
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  estructTableFields: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Order'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('Product.FieldId'),
      columnValue: 'pkIIdField',
    },
    {
      columnLabel: this._translateService.instant('Product.FieldName'),
      columnValue: 'vNameField',
    },
    {
      columnLabel: this._translateService.instant('Product.NameInDataBase'),
      columnValue: 'vNameFieldDb',
    },
    {
      columnLabel: this._translateService.instant('Product.FieldType'),
      columnValue: 'fkIIdFieldType',
      functionValue: (item: any) =>
        this.getFieldTypeNameById(item.fkIIdFieldType),
    },
    {
      columnLabel: this._translateService.instant('Product.AssignedTab'),
      columnValue: 'fkIIdTab',
      functionValue: (item: any) => this.getTabNameById(item.fkIIdTab),
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  //Forms
  form: FormGroup = this._fb.group({
    pkIIdForm: [0],
    b_Active: [true],
    v_Name: [null, [Validators.required]],
    v_NameDB: [null, Validators.required],
  });

  formTab: FormGroup = this._fb.group({
    pkIIdTab: [0],
    b_Active: [true],
    v_Name: [null, [Validators.required]],
    v_NameDB: [null, Validators.required],
    i_Order: [null, Validators.required],
  });

  formQuoteProduct: FormGroup = this._fb.group({
    v_Title: [null]
  });

  formSection: FormGroup = this._fb.group({
    pkIIdSection: [0],
    b_Active: [true],
    v_Name: [null, [Validators.required]],
    fk_i_idTab: [null, Validators.required],
    i_Order: [null, Validators.required],
  });

  formField: FormGroup = this._fb.group({
    pkIIdField: 0,
    bActive: [true],
    vNameField: [null, [Validators.required]],
    fkIIdFieldType: [null, Validators.required],
    vNameFieldDb: [null, Validators.required],
    vDescription: [null, Validators.required],
    idTab: [null, Validators.required],
    idSection: [null, Validators.required],
    bRequired: [false],
    bIsEncrypted: [false],
    IsPolicy: [false],
    bIsPolicy: [false],
    bIsInPdf: [false],
    bAllowMultipleUploads: [false],
    bShowYear: [true],
    bIsFromUrl: [true],
    bShowMonth: [true],
    bIsSearch: [false],
    bIsPerson: [false],
    bShowDay: [true],
    bIsInheritedField: [false],
    bShowHour: [false],
    bShowCoin: [false],
    bIsGrouper: [false],
    bIsReadonly: [false],
    bIsUsingJson: [false],
    iMinLength: [null],
    iMaxLength: [null],
    vHelpText: [null],
    vFormat: [null],
    options: [null],
    optionValues: this._fb.array([]),
    bHelpText: [false],
    bIsDependent: [false],
    iOrder: [null, [Validators.required]],
    fkIIdCatalog: [0],
    fkIIdFieldCatalog: [],
    selectedFileTypes: [],
    vEquivalentField: [null],
    fkIGrouperField: [null],
    fkIdInheritedField: [null],
    iSearchType: [null],
    iOptionDependent: [0],
    fkIIdParent: [0],
    fkIIdChildrenDependent: [[]],
    fkIdFieldExistent: [],
    bUsingExistent: [],
    fkIIdInsuranceCompany: [],
    bInsuranceCompany: [],
    rowsRequest: this._fb.array([]),
    rowsResponse: this._fb.array([]),
    iTypeRequest: [],
    vEndpoint: [null],
    bIsHasDefaultValue: [false],
    bIsVisibleInQueryModules: [false],
    bisKeyField: [false],
    vSetDefaultValue: [null],
    bIsEmail: [false],
    bIsMaxDateRequerid: [false],
    bIsMinDateRequerid: [false],
    bIsValueMax: [false],
    bIsValueMin: [false],
    dMaxDateRequerid: [null],
    dMinDateRequerid: [null],
    vValueMax: [null],
    vValueMin: [null],
    vTypeDateMin: [null],
    vTypeDateMax: [null],
    vSchemaJson: [null],
    idForm: [null],
    bIsExternalLink: [null],
    iLink: [null],
    vExternalLink: [null],
    vExternalPDF: [null],
    vFileName:[null],
    iTypeResponseList: [null],
    vNameResponseList: [null],
    fkIIdBusinessByCountry: 0,
    iTypeAuthorization: [null],
    vUserAuthorization: [null],
    vPasswordAuthorization: [null],
    bIsMask: [false],
    bIsFullyMasked: [false],
    bIsMaskedLastFourDigits: [false],
    bIsInvisible: [false],
    iElementHideField: [],
    iStepHideField: [],
    bIsInvisibleDependent: [false],
    iFormatText: [],
    bIsGetdate: [false],
    bIsUrlCheck:[false],
    bIsSearchLogin:[false],
    bIsMultiple:[false]
  });

  constructor(
    public router: Router,
    private _productService: ProductService,
    public utilsSvc: UtilsService,
    public _fieldSvc: FieldService,
    public modalDialog: MatDialog,
    private _fb: FormBuilder,
    private _store: Store<AppState>,
    private _msgSvc: MessageService,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _transactionService: TransactionService,
    private _catalogService: CatalogSettingService,
    private _parametersService: ParametersService,
    private _businessService: BusinessService,
    private _insuranceService: InsuranceService,
  ) {}

  ngOnInit(): void {
    this.getParameters();

    this.getTypeFileList();

    this.productSubs = this._store.select('product').subscribe((p) => {
      if (p.error) return this._msgSvc.messageError(p.error);
      this.idProduct = p.Product?.id == undefined ? 0 : p.Product?.id;
      this.getFieldType();
      this._productService.GetProductById(this.idProduct).subscribe({
        next: ((response) => {
          this.formQuoteProduct.get('v_Title')?.patchValue(response.result.vTextQuoteButton)
          this.formQuoteProduct.get('v_Title')?.valueChanges.pipe(debounceTime(1000)).subscribe({
            next: (data => {
              this._productService.updateQuoteTextProduct(this.idProduct, {
                VTextCotization: data
              }).subscribe({})
            })
          })
        })
      })

      //Inicializa el formulario de acuerdo si se recibe el id del formulario o no.
      this.getDataSettingInit();
      this.initializeForm();
      this.getBusinessComponentByIdBusinessCountry();

      //Cambiar en tiempo real el campo v_NameDB limpiado.

      this.formTab.get('v_Name')?.valueChanges.subscribe((value) => {
        const cleanedValue = this.cleanName(value);
        this.formTab.get('v_NameDB')?.setValue(cleanedValue);
      });
      this.formField.get('vNameField')?.valueChanges.subscribe((value) => {
        const cleanedValue = this.cleanName(value);
        this.formField.get('vNameFieldDb')?.setValue(cleanedValue);
      });
      this.formField.get('fkIIdCatalog')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getCatalogFieldByCatalogId();
        }
      });
      this.formField.get('bIsPolicy')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getFieldsGrouperByForm(this.idForm, false);
        }
      });
      this.formField.get('bIsPerson')?.valueChanges.subscribe((value) => {
        if (value != null) {
          this.getCustomerColumnNames();
          this.getFieldsGrouperByForm(this.idForm, true);
        }
        this.togglePersonValidators(value);
      });
      this.formField.get('bUsingExistent')?.valueChanges.subscribe((value) => {
        if (value) {
          this.getAllFields();
        }
      });

      if (this.itsJustFields) {
        this.formField
          .get('bInsuranceCompany')
          ?.valueChanges.subscribe((value) => {
            if (value) {
              this.getInsuranceCompanies();
              this.formField
                .get('fkIIdInsuranceCompany')
                ?.setValidators([Validators.required]);
              this.formField
                .get('fkIIdInsuranceCompany')
                ?.updateValueAndValidity();
            } else {
              this.formField.get('fkIIdInsuranceCompany')?.setValue(null);
              this.formField.get('fkIIdInsuranceCompany')?.clearValidators();
              this.formField
                .get('fkIIdInsuranceCompany')
                ?.updateValueAndValidity();
            }
          });
      }


      this.formField
        .get('fkIdFieldExistent')
        ?.valueChanges.subscribe((value) => {
          if (value != null) {
            this.getFieldById(value, true);
          }
        });

      this.formField.get('bIsDependent')!.valueChanges.subscribe((value) => {
        if (value === false) {
          this.formField.get('fkIIdParent')?.setValue(0);
          this.formField.get('fkIIdParent')?.clearValidators();
          this.formField.get('fkIIdParent')?.updateValueAndValidity();

          this.formField.get('iOptionDependent')?.setValue(0);
          this.formField.get('iOptionDependent')?.updateValueAndValidity();
        }
      });

      this.formField.get('fkIIdParent')?.valueChanges.subscribe((value) => {
        this.catalogs = [];
        if (value != null) {
          if (this.formField.get('bIsDependent')?.value === false) {
            this.formField.get('iOptionDependent')?.setValue(0);
            this.formField.get('iOptionDependent')?.clearValidators();
            this.formField.get('iOptionDependent')?.updateValueAndValidity();
          }

          if (this.formField.get('bIsDependent')?.value)
            this.getFieldById(value, false);
        }
      });
      this.formField.get('bIsSearch')!.valueChanges.subscribe((value) => {
        this.toggleSearchValidators(value);
      });
      this.formField.get('iSearchType')!.valueChanges.subscribe((value) => {
        if (this.formField.get('bIsSearch')!.value) {
          this.updateValidatorsBasedOnSearchType(value);
        }
      });
      this.formField.get('iTypeResponseList')!.valueChanges.subscribe((value) => {
          if (this.formField.get('iTypeResponseList')!.value === 1) {
          this.formField.get('vNameResponseList')?.setValidators([Validators.required]);
        }
        else{
            this.formField.get('vNameResponseList')?.setValue(null);
            this.formField.get('vNameResponseList')?.clearValidators();
            this.formField.get('vNameResponseList')?.updateValueAndValidity();
          }
        });

      this.formField.get('iTypeAuthorization')!.valueChanges.subscribe((value) => {
          if (this.formField.get('iTypeAuthorization')!.value === 1) {
          this.formField.get('vUserAuthorization')?.setValidators([Validators.required]);
          this.formField.get('vPasswordAuthorization')?.setValidators([Validators.required]);
        }
        else{
            this.formField.get('vUserAuthorization')?.setValue(null);
            this.formField.get('vPasswordAuthorization')?.setValue(null);
            this.formField.get('vPasswordAuthorization')?.clearValidators();
            this.formField.get('vUserAuthorization')?.clearValidators();
            this.formField.get('vUserAuthorization')?.updateValueAndValidity();
          this.formField.get('vPasswordAuthorization')?.updateValueAndValidity();
          }
        });
      this.formField.get('bIsGrouper')?.valueChanges.subscribe((value) => {
        if (value == true || value === null) {
          this.formField.get('fkIGrouperField')?.clearValidators();
          this.formField.get('fkIGrouperField')?.updateValueAndValidity();
        } else if (value == false) {
          this.formField
            .get('fkIGrouperField')
            ?.setValidators([Validators.required]);
          this.formField.get('fkIGrouperField')?.updateValueAndValidity();
        }
      });
    });

    this.form.get('v_Name')?.valueChanges.subscribe((value) => {
      // Limpiar y asignar valor al campo v_NameDB
      this.form.get('v_NameDB')?.setValue(this.cleanName(value));
    });

    this.formField.get('fkIIdFieldType')?.valueChanges.subscribe((value) => {
      if (value != null) {
        if (value !== this.typeField.Alphanumeric)
          this.formField.get('bIsEmail')?.setValue(null);

        if (
          value !== this.typeField.Alphanumeric &&
          value !== this.typeField.Numeric &&
          value !== this.typeField.Text &&
          value !== this.typeField.Date &&
          value !== this.typeField.DropDownList
        ) {
          this.formField.get('bIsSearch')?.setValue(false);
        }
        if (value === this.typeField.Money)
          this.formField.get('bIsHasDefaultValue')?.setValue(false);
        if (value === this.typeField.DropDownList) {
          this.formField
            .get('fkIIdCatalog')
            ?.setValidators([Validators.required]);
          this.formField
            .get('fkIIdFieldCatalog')
            ?.setValidators([Validators.required]);
          this.formField.get('fkIIdCatalog')?.updateValueAndValidity();
          this.formField.get('fkIIdFieldCatalog')?.updateValueAndValidity();
        } else {
          this.formField.get('fkIIdCatalog')?.clearValidators();
          this.formField.get('fkIIdFieldCatalog')?.clearValidators();
          this.formField.get('fkIIdCatalog')?.updateValueAndValidity();
          this.formField.get('fkIIdFieldCatalog')?.updateValueAndValidity();
        }
      }
    });

    this.formField.get('bIsEmail')?.valueChanges.subscribe((value) => {
      if (value != null) {
        if (value) {
          this.formField.get('bIsValueMax')?.disable();
          this.formField.get('bIsValueMax')?.setValue(false);

          this.formField.get('bIsValueMin')?.disable();
          this.formField.get('bIsValueMin')?.setValue(false);
        } else {
          this.formField.get('bIsValueMax')?.enable();
          this.formField.get('vValueMax')?.enable();

          this.formField.get('bIsValueMin')?.enable();
          this.formField.get('vValueMin')?.enable();
        }
      }
    });

    this.formField.get('bIsValueMax')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formField.get('vValueMax')?.enable();
        this.formField.get('vValueMax')?.setValidators(Validators.required);
        this.formField.get('vValueMax')?.updateValueAndValidity();
      } else {
        this.formField.get('vValueMax')?.disable();
        this.formField.get('vValueMax')?.setValue('');
        this.formField.get('vValueMax')?.setValidators(null);
        this.formField.get('vValueMax')?.updateValueAndValidity();
      }
    });

    this.formField.get('bIsValueMin')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formField.get('vValueMin')?.enable();
        this.formField.get('vValueMin')?.setValidators(Validators.required);
        this.formField.get('vValueMin')?.updateValueAndValidity();
      } else {
        this.formField.get('vValueMin')?.disable();
        this.formField.get('vValueMin')?.setValue('');
        this.formField.get('vValueMin')?.setValidators(null);
        this.formField.get('vValueMin')?.updateValueAndValidity();
      }
    });

    this.formField
      .get('bIsMinDateRequerid')
      ?.valueChanges.subscribe((value) => {
        if (value) {
          this.formField.get('dMinDateRequerid')?.enable();
          this.formField
            .get('dMinDateRequerid')
            ?.setValidators(Validators.required);
          this.formField.get('dMinDateRequerid')?.updateValueAndValidity();

          this.formField.get('vTypeDateMin')?.enable();
        } else {
          this.formField.get('vTypeDateMin')?.disable();
          this.formField.get('vTypeDateMin')?.setValue(0);
          this.typeDateMaxFilter = this.typeDate;
          this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );
        }
      });

    this.formField
      .get('bIsMaxDateRequerid')
      ?.valueChanges.subscribe((value) => {
        if (value) {
          this.formField.get('dMaxDateRequerid')?.enable();
          this.formField
            .get('dMaxDateRequerid')
            ?.setValidators(Validators.required);
          this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();

          this.formField.get('vTypeDateMax')?.enable();
        } else {
          this.formField.get('vTypeDateMax')?.disable();
          this.formField.get('vTypeDateMax')?.setValue(0);
          this.formField.get('dMaxDateRequerid')?.disable();
          this.formField.get('dMaxDateRequerid')?.setValue('');
          this.formField.get('dMaxDateRequerid')?.setValidators(null);
          this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();
        }
      });

    this.formField.get('bShowYear')?.valueChanges.subscribe((value) => {
      this.cleanFieldDate();
      if (value) {
        this.validationTypeDate(
          true,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      } else {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      }
    });

    this.formField.get('bIsGrouper')?.valueChanges.subscribe((value) => {

      // Forzar la reevaluación de la validez del campo
      if (value === true) {
        this.formField.get('fkIGrouperField')?.clearValidators();
        this.formField.get('fkIGrouperField')?.disable;
        this.formField.get('fkIGrouperField')?.updateValueAndValidity();
      } else if (value === false) {
        this.formField
          .get('fkIGrouperField')
          ?.setValidators([Validators.required]);
        this.formField.get('fkIGrouperField')?.updateValueAndValidity();
      }
    });

    this.formField.get('bShowMonth')?.valueChanges.subscribe((value) => {
      this.cleanFieldDate();
      if (value) {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          true,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      } else {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      }
    });

    this.formField.get('bShowDay')?.valueChanges.subscribe((value) => {
      this.cleanFieldDate();
      if (value) {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          true,
          this.formField.get('bShowHour')?.value
        );
      } else {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      }
    });

    this.formField.get('bShowHour')?.valueChanges.subscribe((value) => {
      this.cleanFieldDate();
      if (value) {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          true
        );
      } else {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      }
    });

    this.formField.get('dMaxDateRequerid')?.valueChanges.subscribe((value) => {
      if (value != null) this.isDateInvalid(false);
    });

    this.formField.get('dMinDateRequerid')?.valueChanges.subscribe((value) => {
      if (value != null) this.isDateInvalid(true);
    });

    this.formField.get('vTypeDateMin')?.valueChanges.subscribe((value) => {
      const selectedValue = this.formField.get('vTypeDateMin')?.value;
      this.typeDateMaxFilter = this.typeDate.filter(
        (item) => item.Name === selectedValue
      );
      this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
    });

    this.formField.get('vTypeDateMax')?.valueChanges.subscribe((value) => {
      this.filterFieldTypeDateMax();
    });
    this.formField.get('bIsInheritedField')?.valueChanges.subscribe((value) => {
      if (value)
        this._getQuoteFieldToInherited();
      else
        this.formField.patchValue({fkIdInheritedField: null})
    });

    this.formField.get('bIsInvisible')?.valueChanges.subscribe((value) => {
      if(value!=null)
      this.toggleInvisibleValidators(value);
    });


    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTable[0].columnLabel =
        this._translateService.instant('Product.Order');
      this.estructTable[1].columnLabel =
        this._translateService.instant('Product.TabName');
      this.estructTable[2].columnLabel = this._translateService.instant(
        'Product.NameInDataBase'
      );
      this.estructTable[3].columnLabel =
        this._translateService.instant('Status');
      this.estructTable[4].columnLabel =
        this._translateService.instant('Modify');

      this.estructTableSections[0].columnLabel =
        this._translateService.instant('Product.Order');
      this.estructTableSections[1].columnLabel = this._translateService.instant(
        'Product.SectionName'
      );
      this.estructTableSections[2].columnLabel =
        this._translateService.instant('Product.Tab');
      this.estructTableSections[3].columnLabel =
        this._translateService.instant('Status');
      this.estructTableSections[4].columnLabel =
        this._translateService.instant('Modify');

      this.estructTableFields[0].columnLabel =
        this._translateService.instant('Product.Order');
      this.estructTableFields[1].columnLabel =
        this._translateService.instant('Product.FieldId');
      this.estructTableFields[2].columnLabel =
        this._translateService.instant('Product.FieldName');
      this.estructTableFields[3].columnLabel = this._translateService.instant(
        'Product.NameInDataBase'
      );
      this.estructTableFields[4].columnLabel =
        this._translateService.instant('Product.FieldType');
      this.estructTableFields[5].columnLabel = this._translateService.instant(
        'Product.AssignedTab'
      );
      this.estructTableFields[6].columnLabel =
        this._translateService.instant('Status');
      this.estructTableFields[7].columnLabel =
        this._translateService.instant('Modify');
    });
  }

  private _getQuoteFieldToInherited(){
    this.fieldSubs = this._fieldSvc
      .getFormByProduct(this.idProduct)
      .pipe(
        catchError((respError) => {
          if (respError.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              respError.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.idFormQuote = resp.result.pkIIdForm;
            this.getFieldsByFormToInherited(this.idFormQuote);
          }
        }
      });
  }

  getFieldsByFormToInherited(idForm: Number) {
    this.fieldSubs = this._fieldSvc
      .getFieldsByForm(idForm)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.allFieldsQuote = resp.result;
          }
        }
      });
  }

  private initializeForm(): void {
    if (this.idFormInput != 0) {

      //Cuando es solo campos para ocultar las demás secciones y mostrar solo la de campos.
      this.itsJustFields = true;

      // Si el idFormInput no es 0, se asigna a la variable idForm
      this.idForm = this.idFormInput;

      // Se actualizan los valores del formulario con los datos proporcionados
      this.form.patchValue({
        pkIIdForm: this.idForm,
        b_Active: this.bFormActive,
        v_Name: this.vNameForm,
        v_NameDB: this.vNameFormDb,
      });

      // Se obtienen las pestañas asociadas al formulario
      this.getTabByForm(this.idForm);

      // Se obtienen las secciones asociadas al formulario
      this.getSectionByForm(this.idForm);

      // Se obtienen los campos asociados al formulario
      this.getFieldsByForm(this.idForm);
    } else {
      // Si idFormInput es 0, se obtiene el formulario por el id del producto
      this.getFormById(this.idProduct);
    }
  }

  /**
   * Lifecycle hook that is called when the component is destroyed.
   *
   * This method checks if there is an active subscription.
   * If the subscription exists, it unsubscribes to prevent memory leaks.
   *
   */
  ngOnDestroy(): void {
    this.productSubs?.unsubscribe();
    this.formSubs?.unsubscribe();
    this.fieldSubs?.unsubscribe();
    this._settingCountryAndCompanySubscription?.unsubscribe();
    this.productList?.unsubscribe();
    this.transactionSubs?.unsubscribe();
  }

  EditField(isEditing: boolean, evt: IconEventClickModel) {

    this.open('editNewFieldModal');
    this.getColumnNamesPolicy();
    if (isEditing) {
      this.getFieldById(evt?.value.pkIIdField, true);
      this.idFieldModule = evt?.value.pkIIdField;
    }
    this.isEditingField = true;
  }
  EditSection(evt: IconEventClickModel) {
    this.open('editNewSectionModal');
    this.isEditingSection = true;
    this.formSection.patchValue({
      pkIIdSection: evt.value.pkIIdSection,
      b_Active: evt.value.bActive,
      v_Name: evt.value.vName,
      fk_i_idTab: evt.value.fkIIdTab,
      i_Order: evt.value.iOrder,
    });
  }

  EditTab(evt: IconEventClickModel) {
    this.open('editNewTabModal');
    this.isEditingTab = true;
    this.formTab.patchValue({
      pkIIdTab: evt.value.pkIIdTab,
      b_Active: evt.value.bActive,
      v_Name: evt.value.vName,
      v_NameDB: evt.value.vNameDb,
      i_Order: evt.value.iOrder,
    });
  }
  async getDataSettingInit() {
    let data = await this._settingService.getDataSettingInit();
    this.idBusinessCountry = data.idBusinessByCountry;
    this.idCountry = data.pkIIdCountry;
    this.getListCatalog(this.idBusinessCountry, this.idCountry);
    this.getBusinessByCountry();
  }

  getBusinessByCountry() {
    //Detectar producto a replicar configuración.
    //Se dejó de usar por perdida de valor, al recargar.
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.idCountry = response.enterprise.pkIIdCountry;

            this.getProductByBusiness(this.idBusinessCountry);
          }
        }
      );
  }

  getTabNameById(idTab: number) {
    var temp = this.dataTableTab.find((x) => x.pkIIdTab == idTab);
    return temp != undefined ? temp.vName : idTab;
  }
  getFieldTypeNameById(idFieldType: number) {
    var temp = this.fieldTypes.find((x) => x.pkIIdFieldType == idFieldType);
    return temp != undefined ? temp.vName : idFieldType;
  }

  getAllFields() {
    this._fieldSvc
      .getAllFields(this.idBusinessCountry)
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
          } else {
            this.allFields = response.result;
          }
        }
      });
  }

  getFormById(IdProduct: number) {
    this.fieldSubs = this._fieldSvc
      .getFormByProduct(IdProduct)
      .pipe(
        catchError((respError) => {
          if (respError.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              respError.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.idForm = resp.result.pkIIdForm;
            this.form.patchValue({
              pkIIdForm: resp.result.pkIIdForm,
              b_Active: resp.result.bActive,
              v_Name: resp.result.vName,
              v_NameDB: resp.result.vNameDb,
            });

            this._productService.isValidForm = this.form.valid;

            this.getTabByForm(this.idForm);
            this.getSectionByForm(this.idForm);
            this.getFieldsByForm(this.idForm);
          }
        }
      });
  }

  getTabByForm(idForm: Number) {
    this.fieldSubs = this._fieldSvc
      .getTabByForm(idForm)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataTableTab = resp.result;
            this.modalDialog.closeAll();
            if (resp.result.length > 0) {
              this.orderListTab = this.utilsSvc.generarArrayOrderList(
                resp.result.length + 1
              );
            } else {
              this.orderListTab = this.utilsSvc.generarArrayOrderList(1);
            }
          }
        }
      });
  }

  getSectionByForm(idForm: Number) {
    this.fieldSubs = this._fieldSvc
      .getSectionByForm(idForm)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataTableSection = resp.result;
            this.modalDialog.closeAll();
            if (resp.result.length > 0) {
              this.orderListSection = this.utilsSvc.generarArrayOrderList(
                resp.result.length + 1
              );
            } else {
              this.orderListSection = this.utilsSvc.generarArrayOrderList(1);
            }
          }
        }
      });
  }

  getFieldsByForm(idForm: Number) {
    this.fieldSubs = this._fieldSvc
      .getFieldsByForm(idForm)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataTableFields = resp.result;
            this.modalDialog.closeAll();
            if (resp.result.length > 0) {
              this.orderListFields = this.utilsSvc.generarArrayOrderList(
                resp.result.length + 1
              );
            } else {
              this.orderListFields = this.utilsSvc.generarArrayOrderList(1);
            }
          }
        }
      });
  }

  getFieldById(idField: Number, bIsForPatching: boolean) {
    this.fieldSubs = this._fieldSvc
      .getFieldById(idField)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            var temp = resp.result;
            if (bIsForPatching) {
              if (temp.fkIIdChildrenDependent != null) {
                temp.fkIIdChildrenDependent = JSON.parse(
                  temp.fkIIdChildrenDependent
                );
              }
              if (temp.fkIIdFieldType === this.typeField.Radio) {
                this.getCatalogById(temp, true);
              }

              if (temp.optionValues) {
                const optionValuesFormArray = this.formField.get(
                  'optionValues'
                ) as FormArray;
                optionValuesFormArray.clear();
                temp.optionValues = JSON.parse(temp.optionValues);
                temp.options = temp.optionValues.length;
                this.formField.patchValue(temp);

                if(this.formField.get('fkIIdInsuranceCompany')?.value){
                  this.formField
                    .get('bInsuranceCompany')
                    ?.setValue(true);
                }

                temp.optionValues.forEach((value: any) => {
                  optionValuesFormArray.push(
                    new FormControl(value, Validators.required)
                  );
                  setTimeout(() => {
                    const inputField = document.getElementById(
                      `optionInput-${value.Id}`
                    ) as HTMLInputElement;
                    // Asignar el valor actualizado al campo de entrada
                    if (inputField) {
                      inputField.value = value.Value;
                    }
                  });
                });
                this.formField.setControl(
                  'optionValues',
                  optionValuesFormArray
                );
              }
              if (temp.rowsRequest != null || temp.rowsResponse != null) {
                const rowsRequest = this.formField.get(
                  'rowsRequest'
                ) as FormArray;
                const rowsResponse = this.formField.get(
                  'rowsResponse'
                ) as FormArray;
                rowsRequest.clear();
                rowsResponse.clear();
                temp.rowsRequest.forEach((value: any) => {
                  rowsRequest.push(this._fb.group(value));
                });
                temp.rowsResponse.forEach((value: any) => {
                  rowsResponse.push(this._fb.group(value));
                });

                this.formField.patchValue(temp);

                if(this.formField.get('fkIIdInsuranceCompany')?.value){
                  this.formField
                    .get('bInsuranceCompany')
                    ?.setValue(true);
                }

                this.formField.setControl('rowsRequest', rowsRequest);
                this.formField.setControl('rowsResponse', rowsResponse);
              } else {
                this.formField.patchValue(temp);

                if(this.formField.get('fkIIdInsuranceCompany')?.value){
                  this.formField
                    .get('bInsuranceCompany')
                    ?.setValue(true);
                }

                if (
                  this.formField.get('fkIIdFieldType')?.value ==
                  this.typeField.UploadDocuments
                ) {
                  this.formField
                    .get('selectedFileTypes')
                    ?.setValue(
                      this.formField
                        .get('vFormat')
                        ?.value.replace(' ', '')
                        .split(',')
                    );
                }
              }
            } else {
              //When it's not neccesary patch the info just for consulting.
              if (temp.fkIIdFieldType == this.typeField.Radio) {
                this.getCatalogById(temp, false);
              }
              if (temp.fkIIdFieldType == this.typeField.DropDownList) {
                this.getCatalogByIdForDropdown(temp);
              }
            }
          }
        }
      });
  }

  getFieldType() {
    this.fieldSubs = this._fieldSvc
      .getFieldType()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.fieldTypes = resp.result;
          }
        }
      });
  }

  //Función que obtiene las unidades de medidas.
  getParameters() {
    this._parametersService
      .getParameters('Type_Date')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.typeDate = resp;
          this.typeDateMinFilter = resp;
          this.typeDateMaxFilter = resp;
        } else {
        }
      });
  }

  saveForm() {
    //Create a form by product
    if (this.form.valid) {
      var caller;
      var form: FormModelCreate = this.form.value;
      form.fk_i_IdProduct = this.idProduct;

      if (this.idForm != 0) {
        caller = this._fieldSvc.updateForm(form);
      } else {
        caller = this._fieldSvc.createForm(form);
      }
      this.fieldSubs = caller
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.idForm = resp.result;
              this.form.patchValue({ pkIIdForm: resp.result });
              this.orderListTab = this.utilsSvc.generarArrayOrderList(1);
              this.orderListSection = this.utilsSvc.generarArrayOrderList(1);
              this.orderListFields = this.utilsSvc.generarArrayOrderList(1);
            }
          }
        });
    }
  }

  deleteForm() {
    if (this.form.valid) {
      this.fieldSubs = this._fieldSvc
        .deleteForm(this.idForm)
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.form.reset();
              this.idForm = 0;
            }
          }
        });
    }
  }

  deleteSection() {
    if (this.formSection.valid) {
      this._msgSvc
        .messageConfirmationAndNegation(
          'Está a punto  de eliminar esta sección',
          'Esto puede afectar el funcionamiento del sistema',
          'info',
          'Confirmar',
          'Cancelar'
        )
        .then((response) => {
          if (response) {
            this.fieldSubs = this._fieldSvc
              .deleteSection(this.formSection.get('pkIIdSection')?.value)
              .pipe(
                catchError((error) => {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('Warning'),
                    error.error.message
                  );
                  return of([]);
                })
              )
              .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                } else {
                  if (resp.error) {
                    this._msgSvc.messageError(
                      this._translateService.instant('ThereWasAError') +
                        resp.message
                    );
                  } else {
                    this._msgSvc.messageSuccess(
                      'Confirmado',
                      'Sección eliminada exitosamente'
                    );
                    this.getSectionByForm(this.idForm);
                  }
                }
              });
          }
        });
    }
  }

  deleteField() {
    if (this.formField.valid) {
      this._msgSvc
        .messageConfirmationAndNegation(
          'Está a punto  de eliminar este campo',
          'Esto puede afectar el funcionamiento del sistema',
          'info',
          'Confirmar',
          'Cancelar'
        )
        .then((response) => {
          if (response) {
            this.fieldSubs = this._fieldSvc
              .deleteField(this.formField.get('pkIIdField')?.value)
              .pipe(
                catchError((error) => {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('Warning'),
                    error.error.message
                  );
                  return of([]);
                })
              )
              .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                } else {
                  if (resp.error) {
                    this._msgSvc.messageError(
                      this._translateService.instant('ThereWasAError') +
                        resp.message
                    );
                  } else {
                    this._msgSvc.messageSuccess(
                      'Confirmado',
                      'Campo eliminado exitosamente'
                    );
                    this.getFieldsByForm(this.idForm);
                  }
                }
              });
          }
        });
    }
  }

  deleteTab() {
    if (this.formTab.valid) {
      this._msgSvc
        .messageConfirmationAndNegation(
          'Está a punto  de eliminar esta pestaña',
          'Esto puede afectar el funcionamiento del sistema',
          'info',
          'Confirmar',
          'Cancelar'
        )
        .then((response) => {
          if (response) {
            this.fieldSubs = this._fieldSvc
              .deleteTab(this.formTab.get('pkIIdTab')?.value)
              .pipe(
                catchError((error) => {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('Warning'),
                    error.error.message
                  );
                  return of([]);
                })
              )
              .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                } else {
                  if (resp.error) {
                    this._msgSvc.messageError(
                      this._translateService.instant('ThereWasAError') +
                        resp.message
                    );
                  } else {
                    this._msgSvc.messageSuccess(
                      'Confirmado',
                      'Pestaña eliminada exitosamente'
                    );
                    this.getTabByForm(this.idForm);
                  }
                }
              });
          }
        });
    }
  }

  open(component: string) {

    this.getColumnNamesPolicy();

    var sizeConfiguration = {
      disableClose: false,
      width: '70vw',
      maxHeight: '90vh',
    };
    var modal: TemplateRef<any>;
    switch (component) {
      case 'editNewTabModal':
        this.formTab.reset();
        this.formTab.patchValue({ b_Active: true });
        modal = this.editNewTabModal!;
        break;
      case 'editNewSectionModal':
        this.formSection.reset();
        this.formSection.patchValue({ b_Active: true });

        modal = this.editSectionTabModal!;
        break;
      case 'editNewFieldModal':
        this.formField.reset();
        this.formField.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessCountry);
        this.clearFormArray('rowsRequest');
        this.clearFormArray('rowsResponse');
        this.isEditingField = false;
        this.formField.patchValue({
          bActive: true,
          selectedFileTypes: [],
          optionValues: [],
        });
        //It's neccesary force clear to optionValues
        var renew = this.formField.get('optionValues') as FormArray;
        renew.clear();
        modal = this.editNewFieldModal!;
        break;
      case 'previewForm':
        modal = this.previewForm!;
        sizeConfiguration.width = '90vw';
        break;
      case 'typeUpload':
        modal = this.typeUpload!;
        sizeConfiguration.width = '50vw';

        break;
      case 'copyParameters':
        modal = this.copyParameters!;
        break;
      default:
        return;
    }

    // Abre el modal y guarda su referencia en la propiedad currentModal
    this.currentModal = this.modalDialog.open(modal, sizeConfiguration);
  }
  closeModal() {
    if (this.currentModal) {
      this.currentModal.close();
      this.currentModal = null; // Limpia la referencia después de cerrar el modal
    }
  }
  saveTab() {
    //Create a tab by form
    if (this.formTab.valid) {
      var caller;
      var tab: TabModelCreate = this.formTab.value;
      tab.fk_i_IdForm = this.idForm;
      tab.i_AmountColumns = 2;
      tab.i_SizeBetweenColumns = 10;

      if (this.isEditingTab) {
        caller = this._fieldSvc.updateTab(tab);
      } else {
        caller = this._fieldSvc.createTab(tab);
      }

      this.fieldSubs = caller
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant('Saved')
              );

              this.getTabByForm(this.idForm);
            }
          }
        });
    }
  }

  saveSection() {
    //Create a tab by form
    if (this.formSection.valid) {
      var caller;
      var section: SectionModelCreate = this.formSection.value;
      if (this.isEditingSection) {
        caller = this._fieldSvc.updateSection(section);
      } else {
        caller = this._fieldSvc.createSection(section);
      }

      this.fieldSubs = caller
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.getSectionByForm(this.idForm);
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant('Saved')
              );
            }
          }
        });
    }
  }

  async saveCatalog(): Promise<number> {
    try {
      const catalog: CatalogCreate = {
        vDescription: this.formField.get('vDescription')?.value,
        vJson: JSON.stringify(this.formField.get('optionValues')?.value),
      };

      const resp: ResponseGlobalModel | undefined = await this._fieldSvc
        .createCatalog(catalog)
        .toPromise();

      if (!resp || resp.error) {
        this._msgSvc.messageError(
          this._translateService.instant('ThereWasAError') +
            ': ' +
            (resp?.message || this._translateService.instant('ThereWasAError'))
        );
        return 0;
      }

      return resp.result;
    } catch (error) {
      console.error('Ocurrió un error:', error);
      return 0;
    }
  }

  /**
   * Validates the masking options in the form.
   *
   * Checks if masking is enabled (`bIsMask` is true), and ensures that only one masking option is selected.
   * If masking is enabled but no option is selected, or if both options are selected, an error message
   * will be displayed and the function will return `true` to indicate a validation error.
   *
   * Additionally, if masking is disabled, both masking options will be reset to false.
   *
   * @returns {boolean} - Returns `true` if there is a validation error, `false` otherwise.
   */
  validateMaskingOptions(): boolean {
    const isMaskSelected = this.formField.get('bIsMask')?.value;
    const isLastFourDigitsMasked = this.formField.get(
      'bIsMaskedLastFourDigits'
    )?.value;
    const isFullyMasked = this.formField.get('bIsFullyMasked')?.value;

    // Check if masking is enabled but no option is selected
    if (isMaskSelected && !isLastFourDigitsMasked && !isFullyMasked) {
      this._msgSvc.messageError(
        this._translateService.instant('ThereWasAError') +
          this._translateService.instant(
            'Product.IfMaskingIsSelectedYouMustSelectAMaskingOption'
          )
      );
      return true; // Validation error: no masking option selected
    }

    // Check if both masking options are selected at the same time
    if (isMaskSelected && isLastFourDigitsMasked && isFullyMasked) {
      this._msgSvc.messageError(
        this._translateService.instant('ThereWasAError') +
          this._translateService.instant(
            'Product.IfMaskingIsSelectedYouCannotSelectMoreThanOneWayToMask'
          )
      );
      return true; // Validation error: both options selected
    }

    // Reset masking options if masking is disabled
    if (!isMaskSelected) {
      this.formField.get('bIsMaskedLastFourDigits')?.setValue(false);
      this.formField.get('bIsFullyMasked')?.setValue(false);
    }

    return false; // No validation errors
  }

  async saveField() {
    var idCatalog: number = 0;
    var caller;
    Object.keys(this.formField.controls).forEach((key) => {
      const control = this.formField.get(key);
      if (control!.invalid) {
      }
    });

    if (this.validateMaskingOptions()) {
      return;
    }

    //Create a field by form
    if (this.formField.valid) {

      this.formField.value.bIsGrouper = this.formField.value.bIsGrouper ?? false;

      var field: FieldModelCreate = this.formField.value;
      field.idForm = this.idForm;
      if (this.isEditingField) {
        caller = this._fieldSvc.updateField(field);
      } else {
        caller = this._fieldSvc.createField(field);
        if (this.formField.get('fkIIdFieldType')?.value === TypeField.Radio) {
          idCatalog = await this.saveCatalog();
          if (idCatalog === 0) {
            return;
          }
          field.fkIIdCatalog = idCatalog;
        }
      }
      this.fieldSubs = caller
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.validationTabRulesField(resp.result);
            }
          }
        });
    } else {
      this.formField.markAllAsTouched();
    }
  }

  updateSectionInfo() {
    //Update sections by tabs;
    this.selectedTabSections = this.dataTableSection.filter(
      (x) => x.fkIIdTab == this.formField.get('idTab')?.value
    );
  }

  updateOptions() {
    const options = this.formField.get('options')?.value;
    const optionValues = this.formField.get('optionValues') as FormArray;

    // Obtener la cantidad actual de opciones
    const currentOptions = optionValues.length;

    if (options > currentOptions) {
      // Agregar nuevas opciones
      for (let i = currentOptions; i < options; i++) {
        optionValues.push(new FormControl(null, Validators.required));
      }
    } else if (options < currentOptions) {
      // Eliminar opciones excedentes
      for (let i = currentOptions - 1; i >= options; i--) {
        optionValues.removeAt(i);
      }
    }
  }

  updateOptionValue(index: number, value: any) {
    const option = this.formField
      .get('optionValues')
      ?.get(index.toString()) as FormGroup;
    option.patchValue({
      Id: index,
      Value: value.value,
    });

    // Obtener el campo de entrada correspondiente por su índice
    const inputField = document.getElementById(
      `optionInput-${index}`
    ) as HTMLInputElement;

    // Asignar el valor actualizado al campo de entrada
    if (inputField) {
      inputField.value = option.value.Value;
    }
  }

  get orderItems() {
    return this.formField.controls['optionValues'] as FormArray;
  }
  public get typeField(): typeof TypeField {
    return TypeField;
  }

  isSelected(fileType: string): boolean {
    const selectedFileTypes = this.formField.get('selectedFileTypes')
      ?.value as string[];
    return selectedFileTypes?.includes(fileType) || false;
  }
  toggleFileType(fileType: string) {
    const selectedFileTypes = this.formField.get('selectedFileTypes')
      ?.value as string[];
    const index = selectedFileTypes?.indexOf(fileType);
    if (index !== undefined && index >= 0) {
      selectedFileTypes.splice(index, 1);
    } else {
      selectedFileTypes.push(fileType);
    }
    this.formField.patchValue({
      selectedFileTypes: selectedFileTypes,
    });
  }

  saveInfo() {
    const selectedFileTypes = this.formField.get('selectedFileTypes')
      ?.value as string[];

    const selectedFileTypesDisplay = selectedFileTypes?.join(', ');
    this.formField.patchValue({
      selectedFileTypes: selectedFileTypesDisplay,
    });

    this.formField.patchValue({
      vFormat: selectedFileTypesDisplay,
    });

    this.closeModal();
  }

  cleanName(name: string): string {
    // Limpiar el nombre: eliminar tildes, espacios, etc.
    let cleanedName = name?.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    cleanedName = cleanedName?.replace(/\s+/g, '');

    // Agregar 'DB' al final del nombre
    cleanedName += 'DB';

    return cleanedName;
  }

  //Busca todos los productos por empresa.
  getProductByBusiness(idBusinessByCountry: number) {
    this.productList = this._productService
      .getAllProductByBusiness(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.products = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.products = resp.result;
          }
        }
      });
  }

  replyProduct() {
    if (this.idProductReply != 0) {
      this.fieldSubs = this._fieldSvc
        .cloneFieldByIdProduct(this.idProductReply, this.idProduct)
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.idForm = resp.result.id;
              this.getFormById(this.idProduct);
            }
          }
        });
    }
  }

  // Método para actualizar el formato de fecha basado en los checkboxes en tiempo real
  updateDateFormat() {
    this.disableListeners = true; // Deshabilita los listeners temporalmente para evitar el ciclo infinito

    let format = '';
    if (this.formField.get('bShowYear')?.value) {
      format += 'YYYY';
    }
    if (this.formField.get('bShowMonth')?.value) {
      format += format.length > 0 ? '-MM' : 'MM';
    }
    if (this.formField.get('bShowDay')?.value) {
      format += format.length > 0 ? '-DD' : 'DD';
    }
    if (this.formField.get('bShowHour')?.value) {
      format += format.length > 0 ? '-h:mm' : 'h:mm';
    }

    this.formField.get('vFormatDate')?.setValue(format);
    this.disableListeners = false; // Vuelve a habilitar los listeners
  }

  // Método para actualizar los checkboxes en tiempo real según el formato seleccionado
  updateCheckboxVisibility(format: string) {
    this.disableListeners = true; // Deshabilita los listeners temporalmente para evitar el ciclo infinito

    this.formField.get('bShowYear')?.setValue(format.includes('YYYY'));
    this.formField.get('bShowMonth')?.setValue(format.includes('MM'));
    this.formField.get('bShowDay')?.setValue(format.includes('DD'));
    this.formField.get('bShowHour')?.setValue(format.includes('h:mm'));
    this.disableListeners = false; // Vuelve a habilitar los listeners
  }

  getFieldsGrouperByForm(idForm: number, isPerson: boolean) {
    this.fieldSubs = this._fieldSvc
      .getFieldsGrouperByFormId(idForm, isPerson)
      .pipe(
        catchError((excepception) => {
          if (excepception.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              excepception.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.fieldGroupers = resp.result;
          }
        }
      });
  }

  getColumnNamesPolicy() {
    this.transactionSubs = this._transactionService
      .getColumnNamesPolicy()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.nameColumnsPolicyTable = resp.result;
          }
        }
      });
  }

  getCustomerColumnNames() {
    this.transactionSubs = this._transactionService
      .getColumnNamesCustomer()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.nameColumnsCustomerTable = resp.result;
          }
        }
      });
  }

  getListCatalog(idBusinessCountry: number, idCountry: number) {
    this._catalogService
      .getAllCatalogByCountryAndBusiness(idBusinessCountry, idCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.catalogTable = response.result;
          }
        }
      });
  }
  getCatalogFieldByCatalogId() {
    this._catalogService
      .getCatalogBasicInfoFieldByCatalogId(
        this.formField.get('fkIIdCatalog')?.value
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.catalogFields = response.result;
          }
        }
      });
  }

  getCatalogById(field: any, bIsForPatching: boolean) {
    //Catalogs for radio button.
    this.fieldSubs = this._fieldSvc
      .getCatalogById(field.fkIIdCatalog)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            if (bIsForPatching) {
              const optionValuesFormArray = this.formField.get(
                'optionValues'
              ) as FormArray;
              optionValuesFormArray.clear();
              field.optionValues = JSON.parse(resp.result.vJson);
              field.options = field.optionValues.length;
              this.formField.patchValue(field);

              field.optionValues.forEach((value: any) => {
                optionValuesFormArray.push(
                  new FormControl(value, Validators.required)
                );
                setTimeout(() => {
                  const inputField = document.getElementById(
                    `optionInput-${value.Id}`
                  ) as HTMLInputElement;
                  // Asignar el valor actualizado al campo de entrada
                  if (inputField) {
                    inputField.value = value.Value;
                  }
                });
              });
              this.formField.setControl('optionValues', optionValuesFormArray);
            } else {
              this.catalogs = JSON.parse(resp.result.vJson.toLowerCase());
            }
          }
        }
      });
  }

  getCatalogByIdForDropdown(field: any) {
    //Catalogs for dropdown.
    this.fieldSubs = this._catalogService
      .getCatalogFieldById(field.fkIIdFieldCatalog)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.catalogs = resp.result.vJson;
          }
        }
      });
  }

  get rowsRequest() {
    return (this.formField.get('rowsRequest') as FormArray).controls;
  }
  get rowsResponse() {
    return (this.formField.get('rowsResponse') as FormArray).controls;
  }

  generateSchemaRow(flag: boolean): FormGroup {
    return this._fb.group({
      fkIIdField: 0,
      vEquivalentField: '',
      bIsForFieldEndpoint: true,
      iTypeConvertion: 1,
      bIsForResponse: flag,
    });
  }

  addRow(key: string) {
    const rowsArray = this.formField.get(key) as FormArray;
    rowsArray.push(
      this.generateSchemaRow(key === 'rowsRequest' ? false : true)
    );
  }

  isFieldDisabledTabRuleField(): boolean {
    const fkIIdFieldTypeValue = this.formField.get('fkIIdFieldType')?.value;
    const pkIIdFieldValue = this.formField.get('pkIIdField')?.value;
    return (
      (fkIIdFieldTypeValue === this.typeField.Alphanumeric ||
        fkIIdFieldTypeValue === this.typeField.Numeric ||
        fkIIdFieldTypeValue === this.typeField.Decimal ||
        fkIIdFieldTypeValue === this.typeField.Text  ||
        fkIIdFieldTypeValue === this.typeField.Money) &&
      pkIIdFieldValue > 0
    );
  }

  validationTabRulesField(idField: number) {
    this.formField.get('pkIIdField')?.setValue(idField);
    if (!this.isFieldDisabledTabRuleField()) {
      this.getFieldsByForm(this.idForm);
      this._msgSvc.messageSuccess('', this._translateService.instant('Saved'));
      return;
    }

    if (this.isEditingField) {
      this.getFieldsByForm(this.idForm);
      this._msgSvc.messageSuccess('', this._translateService.instant('Saved'));
    } else {
      this._msgSvc
        .messageConfirmationAndNegationReverseButton(
          this._translateService.instant('¿Deseas agregar reglas del campo?'),
          '',
          'warning',
          this._translateService.instant('Cancel'),
          this._translateService.instant('Confirm')
        )
        .then((result) => {
          if (!result) {
            this.getFieldsByForm(this.idForm);
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant('Saved')
            );
          } else {
            this.isFieldDisabledTabRuleField();
            this.idFieldModule = idField;
            this.isEditingField = true;
          }
        });
    }
  }

  /// Function where we validate the selected check and thus filter the array for the field
  validationTypeDate(
    bShowYear: boolean,
    bShowMonth: boolean,
    bShowDay: boolean,
    bShowHour: boolean
  ) {
    let filters: string[] = [];
    this.typeDateMinFilter = [];
    if (bShowYear) filters.push('año');
    if (bShowMonth) filters.push('mes');
    if (bShowDay) filters.push('día');
    if (bShowHour) filters.push('horas');

    /// A filter is made depending on the check or the marked checks.
    this.typeDateMinFilter = this.typeDate.filter((item) => {
      return filters.some((prop) => item.Name.toLowerCase().includes(prop));
    });

    /// It is validated if the filter array has data if it does not leave the list as it was initially
    if (filters.length == 0) {
      this.typeDateMinFilter = this.typeDate;
    }

    /// We refresh the field array
    this.typeDateMinFilter = this.typeDateMinFilter.slice();
    this.typeDateMaxFilter = this.typeDateMinFilter;
    this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
  }

  filterFieldTypeDateMax() {
    if (
      this.formField.get('vTypeDateMin')?.value != null &&
      this.formField.get('vTypeDateMin')?.value != 0
    ) {
      const selectedValue = this.formField.get('vTypeDateMin')?.value;
      this.typeDateMaxFilter = this.typeDate.filter(
        (item) => item.Name === selectedValue
      );
      this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
    }
  }

  onChangeTypeMinDate(event: any) {
    if (!this.isEditingField) {
      this.formField.get('dMinDateRequerid')?.disable();
    }

    this.formField.get('dMinDateRequerid')?.setValue('');
    this.formField.get('dMinDateRequerid')?.disable();
    this.formField.get('dMaxDateRequerid')?.setValue('');
    this.formField.get('dMaxDateRequerid')?.disable();

    const selectedValue = this.formField.get('vTypeDateMin')?.value;
    this.typeDateMaxFilter = this.typeDate.filter(
      (item) => item.Name === selectedValue
    );
    this.typeDateMaxFilter = this.typeDateMaxFilter.slice();

    if (
      event != null &&
      event != '0' &&
      this.formField.get('bIsMinDateRequerid')
    ) {
      this.formField.get('dMinDateRequerid')?.enable();
      this.formateDate(event);
    }
  }

  onChangeTypeMaxDate(event: any) {
    if (!this.isEditingField) {
      this.formField.get('dMaxDateRequerid')?.setValue('');
      this.formField.get('dMaxDateRequerid')?.disable();
    }
    this.filterFieldTypeDateMax();

    if (
      event != null &&
      event != 0 &&
      this.formField.get('bIsMaxDateRequerid')
    ) {
      this.formField.get('dMaxDateRequerid')?.enable();
      this.formateDate(event);
    }
  }

  /// Function formate date
  formateDate(typeDate: string) {
    const vTypeDateMinValue = typeDate;

    this.config = {
      format: '',
      showMultipleYearsNavigation: false,
      multipleYearsNavigateBy: 1,
      showNearMonthDays: true,
    };

    if (!typeDate || typeDate === '') {
      console.log('No se realiza la validación porque falta vTypeDateMinValue');
      return;
    }

    if (vTypeDateMinValue.includes('año')) {
      this.modeDatePicker = 'day';
      this.config = {
        format: 'YYYY',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false,
      };
      this.placeholderText = 'YYYY';
    }

    if (vTypeDateMinValue.includes('mes')) {
      this.modeDatePicker = 'month';
      this.config = {
        format: 'MM',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += '-MM';
    }

    if (vTypeDateMinValue.includes('día')) {
      this.modeDatePicker = 'day';
      this.config = {
        format: 'DD',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += '-DD';
    }

    if (vTypeDateMinValue.includes('hora')) {
      this.modeDatePicker = 'time';
      this.clockTypeIcon = true;
      this.config = {
        format: 'hh:mm:ss',
        enableMonthSelector: false, //Desahabilita el selecctor de meses.
        secondsInterval: 1,
        showSeconds: true,
      };
      this.placeholderText += 'hh:mm:ss';
    }

    if (vTypeDateMinValue.toLowerCase().includes('fecha')) {
      this.modeDatePicker = 'day';
      this.clockTypeIcon = false;
      this.config = {
        format: 'YYYY-MM-DD',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += 'YYYY-MM-DD';
    }
  }

  cleanFieldDate() {
    this.formField.get('vTypeDateMin')?.setValue(null);
    this.formField.get('dMaxDateRequerid')?.setValue('');
    this.formField.get('dMaxDateRequerid')?.disable();
    this.formField.get('dMaxDateRequerid')?.setValidators(null);
    this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();

    this.formField.get('vTypeDateMax')?.setValue(null);
    this.formField.get('dMinDateRequerid')?.setValue('');
    this.formField.get('dMinDateRequerid')?.disable();
    this.formField.get('dMinDateRequerid')?.setValidators(null);
    this.formField.get('dMinDateRequerid')?.updateValueAndValidity();
  }

  /// Function that performs validation between dates
  isDateInvalid(isDateMin: boolean) {
    let horaMin: number = 0;
    let horaMax: number = 0;
    let isHour: boolean = false;
    const vTypeDateMinValue = this.formField.get('vTypeDateMin')?.value;
    const minDate = this.formField.get('dMinDateRequerid')?.value;
    const maxDate = this.formField.get('dMaxDateRequerid')?.value;

    /// If the type is time, convert to seconds
    if (
      vTypeDateMinValue != null &&
      vTypeDateMinValue != '' &&
      vTypeDateMinValue != 0
    )
      if (vTypeDateMinValue.includes('hora')) {
        horaMin = this.timeToSeconds(minDate);
        horaMax = this.timeToSeconds(maxDate);
        isHour = true;
      }

    /// If the variable isMindate is true, it is valid for the minimum date field, if not, it is valid for the maximum date.
    if (isDateMin) {
      /// If the time variable is true, it validates by hour, if not, it validates by date, month, day, etc...
      if (isHour) {
        if (horaMin > horaMax) {
          this.formField
            .get('dMinDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMinDateRequerid')?.setErrors(null);
        }
      } else {
        if (Date.parse(minDate) > Date.parse(maxDate)) {
          this.formField
            .get('dMinDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMinDateRequerid')?.setErrors(null);
        }
      }
    } else {
      /// If the time variable is true, it validates by hour, if not, it validates by date, month, day, etc...
      if (isHour) {
        if (horaMax < horaMin) {
          this.formField
            .get('dMaxDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMaxDateRequerid')?.setErrors(null);
        }
      } else {
        if (Date.parse(maxDate) < Date.parse(minDate)) {
          this.formField
            .get('dMaxDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMaxDateRequerid')?.setErrors(null);
        }
      }
    }
  }

  timeToSeconds(time: string): number {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  }

  resetFieldSetDefaultValue() {
    this.formField.get('vSetDefaultValue')?.setValue(null);
  }

  getBusinessComponentByIdBusinessCountry() {
    this._businessService
      .getBusinessComponentByIdBusinessCountry()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (!response.error) {
            this.businessComponent = response.result;
          }
        }
      });
  }

  deleteRowForTable(key: string, index: number) {
    const rowsArray = this.formField.get(key) as FormArray;
    if (rowsArray) {
      rowsArray.removeAt(index);
    }
  }

  private toggleSearchValidators(isSearch: boolean) {
    const iSearchTypeControl = this.formField.get('iSearchType')!;

    if (isSearch) {
      iSearchTypeControl.setValidators([Validators.required]);
    } else {
      iSearchTypeControl.clearValidators();
      this.clearAllConditionalValidators();
    }

    iSearchTypeControl.updateValueAndValidity();
  }

  private togglePersonValidators(IsPerson: boolean) {
    const fkIGrouperField = this.formField.get('fkIGrouperField')!;
    const vEquivalentField = this.formField.get('vEquivalentField')!;

    if (IsPerson) {
      fkIGrouperField.setValidators([Validators.required]);
      vEquivalentField.setValidators([Validators.required]);
    } else {
      fkIGrouperField.clearValidators();
      vEquivalentField.clearValidators();
    }

    fkIGrouperField.updateValueAndValidity();
    vEquivalentField.updateValueAndValidity();
  }

  private updateValidatorsBasedOnSearchType(searchType: number) {
    const fkIIdChildrenDependentControl = this.formField.get(
      'fkIIdChildrenDependent'
    )!;
    const vEndpointControl = this.formField.get('vEndpoint')!;
    const iTypeRequestControl = this.formField.get('iTypeRequest')!;

    this.clearAllConditionalValidators();

    if (searchType === 2) {
      fkIIdChildrenDependentControl.setValidators([Validators.required]);
    } else if (searchType === 3) {
      vEndpointControl.setValidators([Validators.required]);
      iTypeRequestControl.setValidators([Validators.required]);
    }

    fkIIdChildrenDependentControl.updateValueAndValidity();
    vEndpointControl.updateValueAndValidity();
    iTypeRequestControl.updateValueAndValidity();
  }

  private clearAllConditionalValidators() {
    const fkIIdChildrenDependentControl = this.formField.get(
      'fkIIdChildrenDependent'
    )!;
    const vEndpointControl = this.formField.get('vEndpoint')!;
    const iTypeRequestControl = this.formField.get('iTypeRequest')!;

    fkIIdChildrenDependentControl.clearValidators();
    vEndpointControl.clearValidators();
    iTypeRequestControl.clearValidators();

    fkIIdChildrenDependentControl.setValue(null);
    vEndpointControl.setValue(null);
    iTypeRequestControl.setValue(null);

    fkIIdChildrenDependentControl.updateValueAndValidity();
    vEndpointControl.updateValueAndValidity();
    iTypeRequestControl.updateValueAndValidity();
  }

  clearFormArray(key: string) {
    const formArray = this.formField.get(key) as FormArray;
    formArray.clear();
  }

  get maxLengthText() {
    if (
      this.formField.get('fkIIdFieldType')?.value === this.typeField.TextArea
    ) {
      return this.maxTextAreaLength;
    } else if (
      this.formField.get('fkIIdFieldType')?.value === this.typeField.Text
    ) {
      return this.maxTextOnlyLength;
    } else {
      return null;
    }
  }

  validateDecimals(event: any) {
    let input = event.target.value;

    input = input.replace(/,/g, '');

    if (!/^\d*\.?\d*$/.test(input)) {
      event.target.value = input.slice(0, -1);
      return;
    }

    if (input.includes('.')) {
      const [integerPart, decimalPart] = input.split('.');
      if (decimalPart.length > 3) {
        event.target.value = `${integerPart}.${decimalPart.slice(0, 3)}`;
      } else {
        event.target.value = input;
      }
    } else {
      event.target.value = input;
    }
  }

  formatToThreeDecimals(event: any) {
    let input = event.target.value;

    if (input && !input.includes('.')) {
      event.target.value = `${parseFloat(input).toFixed(2)}`;
    }

    if (input.includes('.')) {
      const [integerPart, decimalPart] = input.split('.');
      if (decimalPart.length === 1) {
        event.target.value = `${integerPart}.${decimalPart}000`;
      } else if (decimalPart.length === 2) {
        event.target.value = `${integerPart}.${decimalPart}0`;
      }
    }
  }

  onFileDropped(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
    const file = event.dataTransfer?.files[0];
    if (file) {
      if (file.type !== 'application/pdf') {
        this._msgSvc.messageError('Solo se permiten archivos PDF.');
        return;
      } else if (file.size > 20 * 1024 * 1024) {
        this._msgSvc.messageError(
          'El archivo excede el tamaño máximo (20 MB).'
        );
        return;
      }

      // Convierte el archivo PDF a Base64
      this.convertPdfToBase64(file)
        .then((base64: string) => {
          this.formField.get('vExternalPDF')?.setValue(base64);
        })
        .catch((error) => {
          this._msgSvc.messageError(error);
        })
        .finally(() => {
          this.showBtnDelete = true;
          this.fileName = file.name;  // Asigna el nombre del archivo
          this.formField.get('vFileName')?.setValue(file.name);
          this.formField.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessCountry);

        });
    }

  }
  validateAndUpload(file: File) {
    const maxSizeInBytes = this.maxSizeInMb * 1024 * 1024;
    if (file.type !== 'application/pdf') {
      return;
    }
    if (file.size > maxSizeInBytes) {
      return;
    }
  }
  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }
  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      if (file.type !== 'application/pdf') {
        return this._msgSvc.messageError('Solo se permiten archivos PDF.');
      } else if (file.size > 20 * 1024 * 1024) {
        // Límite de 20 MB
        return this._msgSvc.messageError(
          'La imagen excede el peso máximo(100MB)'
        );
      } else {
        const files: FileList = event.target.files;
        const name = event.target.files[0].name;
        // Convierte el archivo PDF a Base64
        this.convertPdfToBase64(file)
          .then((base64: string) => {
            this.formField.get('vExternalPDF')?.setValue(base64);
          })
          .catch((error) => {
            this._msgSvc.messageError(error);
          })
          .finally(() => {
            this.showBtnDelete = true;
            this.fileName = name;
          });
        this.formField.get('vFileName')?.setValue(name);
        this.formField
          .get('fkIIdBusinessByCountry')
          ?.setValue(this.idBusinessCountry);
      }
    }
  }
  convertPdfToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String);
      };
      reader.onerror = (error) => {
        this._msgSvc.messageError('Error al leer el archivo: ' + error);
      };
      reader.readAsDataURL(file);
    });
  }
  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  getInsuranceCompanies(): void {
    this.insuranceList = this._insuranceService
    .getInsuranceCompanyByIdBusinessByCountry(this.idBusinessCountry)
    .subscribe({
      next: (response) => {
        this.insuranceCompanies = response.result;
        console.log(this.insuranceCompanies);
      },
    });
  }

  private toggleInvisibleValidators(IsInvisible: boolean) {
    const iElementTypeControl = this.formField.get('iElementHideField')!;
    const iSearchTypeControl = this.formField.get('iStepHideField')!;

    if (IsInvisible) {
      iSearchTypeControl.setValidators([Validators.required]);
      iElementTypeControl.setValidators([Validators.required]);
    } else {
      iElementTypeControl.clearValidators();
      iSearchTypeControl.clearValidators();
      this.formField.get('iElementHideField')?.setValue(null);
      this.formField.get('iStepHideField')?.setValue(null);
      this.clearAllConditionalValidators();
    }

    iElementTypeControl.updateValueAndValidity();
    iSearchTypeControl.updateValueAndValidity();
  }

    /// trae la lista de los tipos de archivos
  getTypeFileList() {
    this._parametersService
      .getParameters('Type_Files_DocumenModules')
      .subscribe((resp: any) => {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.fileTypes = resp;
        }
      });
  }
}
