<div class="row">
  <!-- input de busqueda en la tabla -->
  <mat-form-field class="w-75">
    <mat-label>
      {{ "Search" | translate }}
    </mat-label>
    <input
      (keyup.enter)="search(keyword)"
      [(ngModel)]="keyword"
      matInput
      type="text"
      class="form-control"
      placeholder="{{ 'Search' | translate }}"
    />
    <mat-icon class="hand click" (click)="search(keyword)" matSuffix
      >search</mat-icon
    >
  </mat-form-field>

  <!-- boton que abre el modal de filtrar -->
  <div class="w-auto">
    <button
      class="w-auto mr-2"
      type="button"
      color="primary"
      (click)="openFilterDialog()"
      mat-raised-button
    >
      {{ "Filter" | translate }}
      <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
    </button>
  </div>

  <!-- boton que abre el modal de busqueda externa -->
  <div class="w-auto">
    <button
      class="w-auto mr-2"
      type="button"
      color="primary"
      (click)="openSearchDialog()"
      mat-raised-button
    >
      {{ "ProceduresViewerSettings.ExternalSearch" | translate }}
      <mat-icon iconPositionEnd fontIcon="search"></mat-icon>
    </button>
  </div>
</div>

<!-- datatable tramites -->
<div class="row mt-2">
  <app-table *ngIf="estructTableProcedures.length > 0"
    [displayedColumns]="estructTableProcedures"
    [data]="dataTableProcedures"
    [IsStatic]="false"
    [pageIndex]="pageIndex"
    [pageSize]="pageSize"
    [amountRows]="amountRows"
    (pageChanged)="onPageChange($event)"
    (iconClick)="controller($event)"
  ></app-table>
</div>

<!-- modal filtros -->
<ng-template #filtersModal>
  <app-modal2 [titleModal]="'Filter' | translate">
    <ng-container body>
      <form [formGroup]="formFilter">
        <!-- alerta -->
        <!-- <div class="row mt-3">
          <h4>{{ "ProceduresViewerSettings.Alert" | translate }}</h4>
          <ul *ngFor="let alert of alertList">
            <div class="form-check">
              <input
                type="checkbox"
                class="form-check-input"
                value="{{ alert.id }}"
                id="CheckAction{{ alert.id }}"
                [checked]="alert.checked"
              />
              <label class="form-check-label" for="CheckAction{{ alert.id }}">
                {{ alert.name | translate }}
              </label>
            </div>
          </ul>
        </div> -->

        <!-- fechas -->
        <div class="row mt-3">
          <h4>{{ "TaskTraySettings.DateRange" | translate }}</h4>
          <!-- fecha inicio -->
          <div class="row">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>
                {{ "MyQuotation.AllQuotation.StartDate" | translate }}
              </mat-label>
              <input
                matInput
                formControlName="startDate"
                [matDatepicker]="picker"
              />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
          <!-- fecha fin -->
          <div class="row">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>
                {{ "MyQuotation.AllQuotation.FinalDate" | translate }}
              </mat-label>
              <input
                matInput
                formControlName="endDate"
                [matDatepicker]="picker1"
              />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="picker1"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker1></mat-datepicker>
            </mat-form-field>
          </div>
        </div>

        <!-- estado -->
        <div class="row mt-3">
          <h4>{{ "Status" | translate }}</h4>
          <ul *ngFor="let estado of statusList">
            <div class="form-check">
              <input
                type="checkbox"
                class="form-check-input"
                value="{{ estado.id }}"
                id="CheckAction{{ estado.id }}"
                [checked]="estado.checked"
                (change)="checkedChange(estado.name)"
              />
              <label
                class="form-check-label"
                for="CheckAction{{ estado.name }}"
              >
                {{ estado.name | translate }}
              </label>
            </div>
          </ul>
        </div>
      </form>
    </ng-container>

    <!-- botones limpiar/aplicar filtros -->
    <ng-container customButtonCenter>
      <button
        class="w-auto mr-2"
        type="button"
        mat-raised-button
        (click)="cleanFilterForm(true)"
      >
        {{ "MyQuotation.AllQuotation.Clean" | translate }}
      </button>
    </ng-container>
    <!-- botones limpiar/aplicar filtros -->
    <ng-container customButtonRight>
      <button
        class="w-auto"
        type="button"
        mat-raised-button
        color="primary"
        (click)="applyFilters()"
      >
        {{ "Apply" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
<!-- end modal filtros -->

<!-- modal busqueda externa -->
<ng-template #searchModal>
  <app-modal2
    [titleModal]="'ProceduresViewerSettings.GeneralSearch' | translate"
  >
    <ng-container body>
      <form [formGroup]="formSearch">
        <div class="row mt-3">
          <!-- numero de documento -->
          <!-- <mat-form-field appearance="outline" class="w-50">
            <mat-label>
              {{ "MyProfile.DocumentNumber" | translate }}
            </mat-label>
            <input matInput formControlName="vDocumentNumber" type="number" />
          </mat-form-field> -->

          <!-- id de la tarea -->
          <mat-form-field appearance="outline" class="w-50">
            <mat-label>
              {{ "TaskTraySettings.TaskId" | translate }}
            </mat-label>
            <input matInput formControlName="idTask" type="number" />
          </mat-form-field>
        </div>

        <div class="row mt-3">
          <!-- fecha inicio -->
          <mat-form-field appearance="outline" class="w-50">
            <mat-label>
              {{ "MyQuotation.AllQuotation.StartDate" | translate }}
            </mat-label>
            <input
              matInput
              formControlName="startDate"
              [matDatepicker]="picker"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="picker"
            ></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>

          <!-- fecha fin -->
          <mat-form-field appearance="outline" class="w-50">
            <mat-label>
              {{ "MyQuotation.AllQuotation.FinalDate" | translate }}
            </mat-label>
            <input
              matInput
              formControlName="endDate"
              [matDatepicker]="picker1"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="picker1"
            ></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>
        </div>
      </form>
    </ng-container>

    <!-- boton limpiar -->
    <ng-container customButtonCenter>
      <button
        class="w-auto mr-2"
        type="button"
        mat-raised-button
        (click)="cleanFilterFormSearch()"
      >
        {{ "MyQuotation.AllQuotation.Clean" | translate }}
      </button>
    </ng-container>
    <!-- boton buscar -->
    <ng-container customButtonRight>
      <button
        class="w-auto"
        type="button"
        mat-raised-button
        color="primary"
        (click)="externalSearch()"
      >
        {{ "Search" | translate }}
        <mat-icon matSuffix>search</mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
<!-- end modal busqueda externa -->

<!-- modal detail task -->
<ng-template #seeDetailsTaskModal>
  <app-modal2
    [titleModal]="'ProceduresViewerSettings.TaskDetail' | translate"
  >
    <ng-container body>
      <app-detail-task-procedure *ngIf="disabledDetailTask === true" 
      [dataDetailTask]="dataDetailTask"></app-detail-task-procedure>
    </ng-container>
  </app-modal2>
</ng-template>
