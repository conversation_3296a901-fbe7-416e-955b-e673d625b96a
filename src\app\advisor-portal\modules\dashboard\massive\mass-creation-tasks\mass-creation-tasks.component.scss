.title {
  font-weight: 700;
}
.label-button {
  font-size: 1.2rem !important;
  font-weight: bold;
  text-decoration: none;
}


.cont-fieles {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.cont-template,
.cont-upload {
  width: 100%; /* Ajusta según tus necesidades */
  max-width: 30dvw; /* Tamaño máximo para el contenido */
  margin-bottom: 20px; /* Espacio entre los divs */
}

.cont-template {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid #c9cdd0;
}

.cont-upload {
  margin-bottom: 0;
}

.cont-info-file {
  flex-grow: 1; /* Ocupa todo el espacio disponible en el contenedor */
  margin-right: 1rem; /* Espacio entre el texto y el icono */
  overflow: hidden;
}

.cont-info-file p {
  margin: 0;
  padding: 0;
  white-space: nowrap; /* No permitir el salto de línea */
  overflow: hidden; /* Ocultar el texto que se desborde */
  text-overflow: ellipsis; /* Mostrar los tres puntos (...) cuando el texto es muy largo */
  max-width: 100%; /* Asegura que no se desborde */
}

.cont-info-file .description {
  font-size: 0.8rem;
  color: #666;
}

.cont-download-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.material-symbols-outlined {
  font-size: 24px;
}

::ng-deep .swal2-cancel {
  background-color: #808080 !important;
}
