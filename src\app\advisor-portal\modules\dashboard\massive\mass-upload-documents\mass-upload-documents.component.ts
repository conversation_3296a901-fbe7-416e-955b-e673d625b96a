import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { DragDropUploadComponent } from 'src/app/shared/components/drag-drop-upload/drag-drop-upload.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  TypeOfManagementModel,
  UploadTemplateModel,
} from 'src/app/shared/models/massive';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-mass-upload-documents',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    DragDropUploadComponent,
  ],
  templateUrl: './mass-upload-documents.component.html',
  styleUrls: ['./mass-upload-documents.component.scss'],
})
export class MassUploadDocumentsComponent implements OnInit {
  typeOfLoad: TypeOfManagementModel[] = [];
  loadingTemplate: UploadTemplateModel[] = [];
  dataTableLoad: any[] = [];
  estructTableLoad: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'UploadDocuments.DocumentName'
      ),
      columnValue: 'name',
    },
    {
      columnLabel: this._translateService.instant('Product.TypeFile'),
      columnValue: 'type',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  templateName = this._translateService.instant('UploadDocuments.TemplateName');
  filesName = 'files';
  filesSize = 0;
  templateSize = 0;
  showFileUpload: boolean = false;
  showTemplateUpload: boolean = false;
  template!: File;
  completedTemplate!: File;
  typeOfManagementSelected: number = 0;
  templateSelected!: UploadTemplateModel;
  files: any[] = [];
  fileNameTemplate: string = '';
  idBusinessByCountry: number = 0;
  idUser: number = 0;
  fileAccept = '';

  constructor(
    private _messageService: MessageService,
    private _transactionService: TransactionService,
    private _fieldService: FieldService,
    private _fileService: FileService,
    private _utilsSvc: UtilsService,
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    private _activatedRoute: ActivatedRoute,
    private _customeRouter: CustomRouterService,
    private _userService: UserService
  ) {
    this.getAllTypesOfManagement();
  }

  async ngOnInit() {
    this.getBusinessByCountry();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table business
      this.estructTableLoad[0].columnLabel = this._translateService.instant(
        'UploadDocuments.DocumentName'
      );
      this.estructTableLoad[1].columnLabel =
        this._translateService.instant('Product.TypeFile');
      this.estructTableLoad[2].columnLabel =
        this._translateService.instant('Delete');
    });
    this.idUser = await this._userService.getUserIdSesion();
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
    });
  }

  //Función que redirecciona al módulo de configuración de plantillas.
  configureUploadTemplate() {
    this._customeRouter.navigate([
      `dashboard/massive/configure-upload-template/${this.idBusinessByCountry}`,
    ]);
  }

  //Detecta los cambios del select de tipo de carga.
  onTypeLoadDocument(event: any) {
    this.template = new File([], '');
    this.files = [];
    if (event.source._value) {
      this.typeOfManagementSelected = event.source._value.pkIIdManagementType;
      this.getUploadTemplates(this.typeOfManagementSelected);
    }
  }

  //Detecta los cambios del select de plantillas.
  onUploadTemplate(event: any) {
    this.template = new File([], '');
    this.files = [];
    if (event.source._value) {
      this.templateSelected = event.source._value;
      if (this.templateSelected.format) {
        this.fileAccept = this.templateSelected.format
                          .split(',')
                          .map((ext) => `.${ext.trim() === 'XLS' ? 'XLSX' : ext.trim()}`)
                          .join(', ');
      }
      event.source._value.idUploadTemplate > 0
        ? (this.showFileUpload = true)
        : (this.showFileUpload = false);
      this.downloadTemplate(false);
    }
  }

  getFilterOption(): TypeOfManagementModel[] {
    return this.typeOfLoad.filter(
      (option: TypeOfManagementModel) => option.fkIIdBulkLoadType === 4
    );
  }

  //Controlador de las acciones configuradas en la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'delete':
        this.dataTableLoad.splice(event.index, 1);
        this.dataTableLoad = [...this.dataTableLoad];
        break;
      default:
        break;
    }
  }
  deleteAll() {
    this.files = [];
    this.dataTableLoad = [];
  }

  //Función que se encarga de llamar a la función que devuelve el base64 con el template según el tipo de gestión.
  downloadTemplate(download?: boolean) {
    switch (this.typeOfManagementSelected) {
      case 7:
        this.generateUploadTemplateJSONByIdFieldTable(
          this.templateSelected.idFieldModule ?? 0,
          this.templateSelected.idTableModule ?? 0,
          this.templateSelected.idStateModule ?? 0,
          download
        );
        break;
      case 8:
        if (this.templateSelected.idPolicyParent)
          this.generateUploadTemplateJSONByIdPolicy(
            this.templateSelected.idPolicyParent,
            this.templateSelected.idFieldPolicy,
            download
          );
        break;

      default:
        break;
    }
  }

  //Detecta los eventos del componente de cargue de archivos para las plantillas caragadas.
  getFiles(file: File[]) {
    if (this._utilsSvc.validateFileSize(file[0].size, 25)) {
      if (file.length > 0) {
        this.showTemplateUpload = true;
        this.fileNameTemplate = file[0].name;
        this.templateSize = this._utilsSvc.parseBytesToMb(file[0].size);
        this.completedTemplate = file[0];
      } else {
        this.showTemplateUpload = false;
      }
    } else {
      this._messageService.messageWaring(
        this._translateService.instant('UploadDocuments.ExceededSize'),
        this._translateService.instant('UploadDocuments.ExceededSizeWtw')
      );
    }
  }

  //Detecta los eventos del componente de cargue de archivos para los documentos a cargar.
  getFilesToLoad(files: File[]) {
    let totalSize = 0;
    const nameFileList: string[] = [];
    if (this.templateSelected) {
      this.files = files;
      if (this.files.length > 0) {
        this.files.forEach((element) => {
          const file = {
            type: element.name.split('.')[1],
            name: element.name,
          };
          this.dataTableLoad.push(file);
          totalSize = totalSize + element.size;
          nameFileList.push(element.name);
        });
      }
    }
    if (this._utilsSvc.validateFileSize(totalSize, 25)) {
      this.filesName = nameFileList.join(',');
      this.dataTableLoad = [...this.dataTableLoad];
      this.filesSize = this._utilsSvc.parseBytesToMb(totalSize);
    } else {
      this._messageService.messageWaring(
        this._translateService.instant('UploadDocuments.ExceededSize'),
        this._translateService.instant(
          'UploadDocuments.ExceededSizeDescription'
        )
      );
      this.deleteFile();
      // this.files = [];
      // this.dataTableLoad = [];
    }
  }

  //Obtiene los tipos de gestiones registrados en el sistema.
  getAllTypesOfManagement() {
    this._fileService
      .getAllTypesOfManagement()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.typeOfLoad = resp.result;
          this.typeOfLoad = this.getFilterOption();
        }
      });
  }

  //Obtiene la lista de plantillas de carga por tipo de gestión.
  getUploadTemplates(idManagementType: number) {
    this._fileService
      .getUploadTemplates(idManagementType)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.loadingTemplate = resp.result;
        }
      });
  }

  //Obtiene la plantilla de una póliza por id de póliza.
  generateUploadTemplateJSONByIdPolicy(
    idPolicy: number,
    idFieldPolicy: number,
    dowloandFile: boolean = true
  ) {
    this._fieldService
      .generateUploadTemplateJSONByIdPolicy(idPolicy, idFieldPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (dowloandFile) {
            this._utilsSvc.downloadTemplateByBase64(
              resp.result,
              this._translateService.instant('TemplatePolicy')
            );
          } else {
            this.template =
              this._utilsSvc.base64ToFile(resp.result, 'template') ||
              new File([], '');
          }
        }
      });
  }

  //Genera plantilla carga documentos tareas en base64.
  generateUploadTemplateJSONByIdFieldTable(
    idFieldModule: number,
    idTableModule: number,
    idStateModule: number,
    dowloandFile: boolean = true
  ) {
    this._moduleService
      .generateUploadTemplateJSONByIdFieldTable(
        idFieldModule,
        idTableModule,
        idStateModule
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (dowloandFile) {
            this._utilsSvc.downloadTemplateByBase64(
              resp.result,
              this._translateService.instant('TemplateTask')
            );
          } else {
            this.template =
              this._utilsSvc.base64ToFile(resp.result, 'template') ||
              new File([], '');
          }
        }
      });
  }

  //Compara 2 archivos excel para verificar si las cabeceras son iguales.
  comparerExcelFiles(files: FormData) {
    this._fileService
      .comparerExcelFiles(files)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.length > 0) {
            this._messageService.messageErrorCustomer(
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadTitle'
              ),
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadSubtitle'
              )
            );
          } else {
            let jsonAditional = {
              idUploadTemplate: this.templateSelected.idUploadTemplate,
            };

            const payload = new FormData();
            payload.append(
              `IdManagementType`,
              this.typeOfManagementSelected.toString()
            );
            payload.append(`IdUser`, this.idUser.toString());
            payload.append(`File`, this.completedTemplate);
            payload.append(`JsonAditional`, JSON.stringify(jsonAditional));
            this.saveLoad(payload);
          }
        }
      });
  }

  //Guarda la carga de documentos.
  saveLoad(request: FormData) {
    this._fileService
      .uploadMassivePolicyTask(request)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            const payload = new FormData();

            payload.append(`IdBulkLoad`, resp.result.toString());
            const addFiles: File[] = this.files;
            if (addFiles && addFiles.length > 0) {
              addFiles.forEach((file) => {
                const fileObject = file;
                payload.append(`Files`, fileObject); // Agregar cada archivo al FormData
              });
            }
            this.processBulkLoadDocuments(payload);

            //Limpiamos todo el formulario.
            this.files = [];
            this.dataTableLoad = [];
            this.completedTemplate = new File([], '');
            this.templateName = this._translateService.instant(
              'UploadDocuments.TemplateName'
            );
            this.templateSize = 0;
            this.showTemplateUpload = false;

            this._messageService
              .messageConfirmatioCustomer(
                `${this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadTitle'
                )} ${resp.result}`,
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadSubtitle'
                ),
                'success',
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadBtnLeft'
                ),
                this._translateService.instant('Continuar')
              )
              .then((result) => {
                if (result) {
                  this._customeRouter.navigate([
                    `dashboard/massive/load-viewer/${this.idBusinessByCountry}`,
                  ]);
                }
              });
          }
        }
      });
  }

  //Procesa la carga de documentos subida, en segundo plano.
  processBulkLoadDocuments(request: FormData) {
    this._fileService
      .processBulkLoadDocuments(request)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            console.log(error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
        }
      });
  }

  //Elimina el archivo agregado en la opciónd e cargue de plantilla.
  deleteTemplate() {
    this.completedTemplate = new File([], '');
    this.templateName = this._translateService.instant(
      'UploadDocuments.TemplateName'
    );
    this.templateSize = 0;
    this.showTemplateUpload = false;
  }

  //Elimina las cargas subidas.
  deleteFile() {
    this.files = [];
    this.dataTableLoad = [];
    // this.fileName = '';
  }

  goBackMassive() {
    this._customeRouter.navigate([`dashboard/massive`]);
  }

  startUploadingDocuments() {
    if (this.files.length >= 1) {
      this._messageService
        .messageConfirmationAndNegation(
          this._translateService.instant('BulkUpload.PopUp.ValidateLoadTitle'),
          '',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            const files: File[] = [this.template, this.completedTemplate];
            const payload = new FormData();
            files.forEach((element) => {
              payload.append(`Files`, element);
            });

            this.comparerExcelFiles(payload);
          }
        });
    }
  }
}
