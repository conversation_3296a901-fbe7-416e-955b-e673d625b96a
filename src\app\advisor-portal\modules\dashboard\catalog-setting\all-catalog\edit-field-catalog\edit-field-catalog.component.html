<form [formGroup]="form" (ngSubmit)="saveCatalogField()">
  <div class="row">
    <div class="mb-2">
      <h3 *ngIf="!isEditing" class="col-md-12">{{ "CatalogSetting.AddField" | translate }}</h3>
      <h3 *ngIf="isEditing" class="col-md-12">{{ "Product.UpdateField" | translate }}</h3>
    </div>
    <div class="col-md-8">
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label> {{ "CatalogSetting.FieldName" | translate }} </mat-label>
        <input matInput formControlName="vName" />
        <mat-error
          *ngIf="_utilsSvc.isControlHasError(form, 'vName', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
    </div>
    <div class="col-md-4">
      <mat-slide-toggle
        class="w-100 mb-3"
        formControlName="bIsDependent"
        (change)="changeIsDependent($event)"
      >
        {{ "CatalogSetting.IsDependent" | translate }}</mat-slide-toggle
      >
    </div>
  </div>

  <br />
  <div class="row" *ngIf="form.value.bIsDependent">
    <div class="mb-2">
      <h3 class="col-md-12">{{ "CatalogSetting.Dependence" | translate }}</h3>
    </div>
    <div class="col-md-6">
      <mat-form-field appearance="outline" class="select-look w-100">
        <mat-label>{{
          "CatalogSetting.FieldWhichIsDependent" | translate
        }}</mat-label>
        <mat-select
          formControlName="fkIIdCatalogField"
          (opened)="onSelectOpened()"
        >
          <mat-option
            *ngFor="let items of fieldDependent"
            [value]="items.pkIIdCatalogField"
            >{{ items.vName }}</mat-option
          >
        </mat-select>
      </mat-form-field>
    </div>

    <div class="col-md-6">
      <mat-form-field appearance="outline" class="select-look w-100">
        <mat-label>{{
          "CatalogSetting.OptionWhichBelongs" | translate
        }}</mat-label>
        <mat-select formControlName="vIdJson">
          <mat-option
            *ngFor="let items of optionCatalogFields"
            [value]="items.id"
            >{{ items.name }}</mat-option
          >
        </mat-select>
      </mat-form-field>
    </div>
  </div>
  <br />
  <div class="row">
    <div class="mb-2">
      <h3 class="col-md-12">{{ "CatalogSetting.OptionField" | translate }}
        <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.OptionFieldSubtitle' | translate }}">help_outline</mat-icon>
      </h3>
    </div>
    <div class="col-md-12">
      <app-table
        [displayedColumns]="estructTableOption"
        [data]="optionTable"
        (iconClick)="controller($event)"
      ></app-table>
    </div>
    <div class="cont-btn-create">
      <button
        class="mx-1 mt-2 col-2"
        type="button"
        mat-raised-button
        color="primary"
        [disabled]="optionToWhichItBelongs <= 0"
        (click)="open('modalLoadOption')"
      >
        {{ "CatalogSetting.LoadOptions" | translate }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
      <button
        class="mx-1 mt-2 col-2"
        type="button"
        mat-raised-button
        color="default"
        [disabled]="optionToWhichItBelongs <= 0"
        (click)="open('modalOptionField')"
      >
        {{ "CatalogSetting.AddOption" | translate }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
  </div>

  <div class="d-flex justify-content-center">
    <div class="mt-3">
      <button class="mx-2" (click)="goBack()" type="button" mat-raised-button>
        {{ "Cancel" | translate }}
      </button>
      <button
        class="mx-2"
        *ngIf="this.form.get('pkIIdCatalogField')?.value > 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="deleteCatalogField()"
      >
        <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
        {{ "CatalogSetting.DeleteField" | translate }}
      </button>
      <button
        *ngIf="this.form.get('pkIIdCatalogField')?.value === 0"
        type="button"
        mat-raised-button
        color="primary"
        [disabled]="!valid"
        (click)="dataAssignmentFieldJson('create')"
      >
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "CatalogSetting.Save" | translate }}
      </button>
      <button
        *ngIf="this.form.get('pkIIdCatalogField')?.value > 0"
        type="button"
        mat-raised-button
        color="primary"
        [disabled]="!valid"
        (click)="dataAssignmentFieldJson('edit')"
      >
        {{ "CatalogSetting.Update" | translate }}
        <mat-icon fontIcon="save"></mat-icon>
      </button>
    </div>
  </div>
</form>

<!-- modal option field  -->
<ng-template #editOptionFieldModal>
  <app-modal2 [titleModal]="titelModal" [showTooltip]="true" [tooltipDescription]="'Tooltips.AddOptions' | translate" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="formJsonOption">
        <div class="row mt-5">
          <div class="col-6 col-md-6">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "CatalogSetting.OptionName" | translate }}
              </mat-label>
              <input matInput formControlName="name" />
              <mat-error
                *ngIf="_utilsSvc.isControlHasError(form, 'name', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-6 col-md-6">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label> {{ "CatalogSetting.Id" | translate }} </mat-label>
              <input matInput formControlName="id"  type="number"/>
            </mat-form-field>
          </div>
        </div>
      </form>
    </ng-container>
    <ng-container customButtonRight>
      <button
        type="button"
        mat-raised-button
        color="primary"
        [disabled]="!validOptionField"
        (click)="addOption()"
      >
        {{ "CatalogSetting.Add" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

<!-- modal load option  -->
<ng-template #editLoadOptionModal>
  <app-modal2
    [titleModal]="titleLoadOptionModal"
    (closeModal)="closeModal($event)"
    [showTooltip]="true" [tooltipDescription]="'Tooltips.LoadOptions' | translate"
  >
    <ng-container body>
      <div class="row mt-5">
        <mat-form-field class="example-full-width">
          <mat-label>  {{ "MyProfile.Document" | translate }} </mat-label>     
          <input matInput  [(ngModel)]="fileName"  [ngModelOptions]="{ standalone: true }" readonly>
        </mat-form-field>

        <div class="col-md-4 mb-2">
          <button
            mat-raised-button
            color="primary"
            (click)="getCatalogTemplate()"
          >
            <mat-icon iconPositionEnd fontIcon="get_app"></mat-icon>
            {{ "CatalogSetting.DownloadTemplate" | translate }}
          </button>
        </div>
        <div class="col-md-5 mb-2" style="text-align: center;">
          <input type="file" (onChangeValidated)="selectFile($event)" hidden #fileInput ValidationInputFile [allowedExtensions]="allowedExtensions"/>
          <button mat-raised-button color="primary" (click)="fileInput.click()">
            <mat-icon iconPositionEnd fontIcon="upgrade"></mat-icon>
            {{ "CatalogSetting.MasiveLoadModalUploadButtonText" | translate }}
          </button>
        </div>
        <div class="col-md-3 mb-2" style="text-align: end;" *ngIf="showBtnDelete">
          <button mat-raised-button color="primary" (click)="deleteFile()">
            <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
            {{ "CatalogSetting.Delete" | translate }}
          </button>
        </div>
      </div>
    </ng-container>
    <ng-container customButtonRight>
      <button
        type="button"
        (click)="massiveCatalog()"
        mat-raised-button
        color="primary"
      >
        {{ "CatalogSetting.Add" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
