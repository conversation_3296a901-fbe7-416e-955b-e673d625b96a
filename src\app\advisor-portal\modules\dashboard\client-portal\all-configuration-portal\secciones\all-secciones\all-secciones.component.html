<h5 class="fw-bold mb-2">
    {{ 'configurationClientPortal.seccion' | translate }}
</h5>

<!-- Pestaña -->
<mat-form-field appearance="outline" class="w-100 mb-2">
    <mat-label>{{ 'configurationClientPortal.pestana' | translate }}</mat-label>
    <mat-select formControlName="iOrder" (selectionChange)="onPestanaChange($event.value)">
        <mat-option *ngFor="let pt of pestana" [value]="{ id: pt.pkIIdMenu, description: pt.vDescription }">
        {{ pt.vDescription }}
        </mat-option>
    </mat-select>
</mat-form-field>

<!-- Subpestaña -->
<mat-form-field appearance="outline" class="w-100 mb-2">
    <mat-label>{{ 'configurationClientPortal.SubPestana' | translate }}</mat-label>
    <mat-select formControlName="iOrder" [disabled]="opaco" (selectionChange)="onSubPestanaChange($event.value)">
        <mat-option *ngFor="let pt of subpestana" [value]="pt.pkIIdMenu">
        {{ pt.vDescription }}
        </mat-option>
    </mat-select>
</mat-form-field>

<!-- tabla de secciones por pestaña o subpestañas -->
<app-table [displayedColumns]="estructTableSecciones" [data]="dataTableSeccionesLanding"
        (iconClick)="controller($event)"></app-table>
    <div class="row">
        <div class="col">
            <button type="button" mat-raised-button color="primary" (click)="openModalSeccion();pkSeccionToModify=0 ">
                {{ "configurationClientPortal.AddPestana" | translate }}
            </button>
        </div>
    </div>


    <ng-template #createEditSeccion>
        <app-modal2
            [titleModal]="(pkSeccionToModify === 0 ? 'configurationClientPortal.newSeccion' : 'configurationClientPortal.UpdateSeccion') | translate">
            <ng-container body>
                <form [formGroup]="formLandingSecciones">
                    <!-- Pestaña Activa -->
                    <mat-slide-toggle formControlName="bActiveSeccion" class="mb-2">
                        {{ 'configurationClientPortal.SeccionActiva' | translate }}
                    </mat-slide-toggle>
                    <!-- tipo de seccion -->
                    <p></p>
                    <mat-label>{{ 'configurationClientPortal.typeseccion' | translate }}</mat-label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.typeseccion' | translate }}</mat-label>
                        <mat-select formControlName="iTypeSeccion"
                        (selectionChange)="onTypeSeccionChange($event.value)">
                            <mat-option *ngFor="let seccion of TypeSeccion" [value]="seccion.id">
                                {{ seccion.name }}
                              </mat-option>
                        </mat-select>
                    </mat-form-field>

                    <!-- orden de seccion -->
                    <p></p>
                    <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                        <mat-select formControlName="iOrderSeccion">
                            <mat-option *ngFor="let order of orderSeccion" [value]="order">
                              {{ order }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>

                    <!-- tipo de tarjeta -->
                    <div *ngIf="tipoSeccion == 5">
                        <p></p>
                        <mat-label>{{ 'configurationClientPortal.typeTarjet' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                            <mat-select formControlName="iTypeCards">
                                <mat-option *ngFor="let order of TypeCards" [value]="order.id">
                                {{ order.name }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>

                     <!-- tipo de noticia -->
                     <div *ngIf="tipoSeccion == 9">
                        <p></p>
                        <mat-label>{{ 'configurationClientPortal.typeNotice' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.typeNotice' | translate }}</mat-label>
                            <mat-select formControlName="FkIIdSizeNew">
                                <mat-option *ngFor="let order of typeNew" [value]="order.pkIIdSizeNew">
                                {{ order.vSize }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <!-- Titulo -->
                    <div  *ngIf="tipoSeccion == 1 || tipoSeccion == 2 || tipoSeccion == 3 || tipoSeccion == 5
                    || tipoSeccion == 6 || tipoSeccion == 9 || tipoSeccion == 10">
                        <p></p>
                        <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                            <input matInput formControlName="vTitle" />                          
                        </mat-form-field>
                    </div>

                    <!-- texto -->
                    <div  *ngIf="tipoSeccion == 1 || tipoSeccion == 3 || tipoSeccion == 5 || tipoSeccion == 6
                    || tipoSeccion == 9 || tipoSeccion == 10">
                        <p></p>
                        <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                            <input matInput formControlName="vText" />                          
                        </mat-form-field>
                    </div>

                    <!-- Imagen PC -->
                    <div *ngIf="tipoSeccion == 1 || tipoSeccion == 2 || tipoSeccion == 8 || tipoSeccion == 10">
                        <mat-label>{{ 'configurationClientPortal.imgPC' | translate }}</mat-label>
                        <div class="upload-container" matTooltip="Solo se permiten archivos PNG, JPG, SVG">
                        <div
                            class="upload-area"
                            [ngClass]="{ 'dragover': isDragOver }"
                            (drop)="onFileDropped($event)"
                            (dragover)="onDragOver($event)"
                            (dragleave)="isDragOver = false"
                        >
                            <input
                            type="file"
                            (onChangeValidated)="onFileSelected($event)"
                            id="fileUploadPC"
                            accept="image/png,image/jpeg,image/svg+xml"
                            hidden
                            ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"
                            />
                            <label
                            for="fileUploadPC"
                            class="upload-label"
                            [ngClass]="{ 'uploaded': showBtnDelete }"
                            >
                            <mat-icon
                                class="mb-5"
                                [style.color]="showBtnDelete ? 'green' : 'inherit'"
                                style="overflow: visible; margin-right: 23px"
                            >
                                {{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}
                            </mat-icon>
                            <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>
                            <p *ngIf="!showBtnDelete && !fileName">
                                Haga clic para cargar o arrastre y suelte aquí los archivos
                            </p>
                            <span>PNG, JPG, SVG</span>
                            </label>
                        </div>
                        </div>
                    </div>
                    
                    <!-- Imagen móvil -->
                    <div *ngIf="tipoSeccion == 1 || tipoSeccion == 8">
                        <mat-label>{{ 'configurationClientPortal.imgMovil' | translate }}</mat-label>
                        <div class="upload-container" matTooltip="Solo se permiten archivos PNG, JPG, SVG">
                        <div
                            class="upload-area"
                            [ngClass]="{ 'dragover': isDragOverMovil }"
                            (drop)="onFileDroppedMovil($event)"
                            (dragover)="onDragOverMovil($event)"
                            (dragleave)="isDragOverMovil = false"
                        >
                            <input
                            type="file"
                            (onChangeValidated)="onFileSelectedMovil($event)"
                            id="fileUploadMovil"
                            accept="image/png,image/jpeg,image/svg+xml"
                            hidden
                            ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"
                            />
                            <label
                            for="fileUploadMovil"
                            class="upload-label"
                            [ngClass]="{ 'uploaded': showBtnDeleteMovil }"
                            >
                            <mat-icon
                                class="mb-5"
                                [style.color]="showBtnDeleteMovil ? 'green' : 'inherit'"
                                style="overflow: visible; margin-right: 23px"
                            >
                                {{ showBtnDeleteMovil || fileNameMovil ? 'check_circle' : 'cloud_upload' }}
                            </mat-icon>
                            <p *ngIf="showBtnDeleteMovil || fileNameMovil">{{ fileNameMovil }}</p>
                            <p *ngIf="!showBtnDeleteMovil && !fileNameMovil">
                                Haga clic para cargar o arrastre y suelte aquí los archivos
                            </p>
                            <span>PNG, JPG, SVG</span>
                            </label>
                        </div>
                        </div>
                    </div>

                <!-- button for still images -->
                <div  *ngIf="tipoSeccion == 8">
                    <label class="d-block subtitle-section">{{ 'configurationClientPortal.button' | translate }}</label>
                    <mat-checkbox formControlName="bActiveButtonImg" class="my-4">
                        {{ "configurationClientPortal.ActiveButton" | translate }}
                    </mat-checkbox>
                    <p></p>
                    <label >{{ 'configurationClientPortal.textbuttom' | translate }}</label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                        <input matInput formControlName="vTextButton" />                          
                    </mat-form-field>
                    <div class="mb-3">
                        <label class="d-block subtitle-section">{{ 'configurationClientPortal.position' | translate }}</label>
                        <mat-radio-group formControlName="iPosition" class="d-flex gap-3">
                            <mat-radio-button [value]="2">{{ 'configurationClientPortal.left' | translate
                                }}</mat-radio-button>
                            <mat-radio-button [value]="1">{{ 'configurationClientPortal.right' | translate
                                }}</mat-radio-button>                            
                        </mat-radio-group>
                    </div>
                    <div class="mb-3">
                        <label class="d-block">{{ 'configurationClientPortal.action' | translate }}</label>
                        <mat-radio-group formControlName="iActionButton" class="d-flex gap-3">
                            <mat-radio-button [value]="1">{{ 'configurationClientPortal.goproduct' | translate
                                }}</mat-radio-button>
                            <mat-radio-button [value]="2">{{ 'configurationClientPortal.gotab' | translate
                                }}</mat-radio-button>  
                            <mat-radio-button [value]="3">{{ 'configurationClientPortal.link' | translate
                                }}</mat-radio-button>                            
                        </mat-radio-group>
                    </div>
                    <div *ngIf="formLandingSecciones.get('iActionButton')?.value == 1">
                        <mat-label>{{ 'configurationClientPortal.product' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.ProductVinc' | translate }}</mat-label>
                            <mat-select formControlName="iIdProduct">
                                <mat-option *ngFor="let product of Listproducts" [value]="product.pkIIdProduct">{{ product.vProductName
                                }}</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div *ngIf="formLandingSecciones.get('iActionButton')?.value == 2">
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.pestana' | translate }}</mat-label>
                            <mat-select formControlName="iPestana" (selectionChange)="onPestanaChangeButton($event.value)">
                                <mat-option *ngFor="let pt of pestana" [value]="pt.pkIIdMenu">
                                {{ pt.vDescription }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="w-100 mb-2" [ngClass]="{'disabled-select': disable}">
                            <mat-label>{{ 'configurationClientPortal.SubPestana' | translate }}</mat-label>
                            <mat-select formControlName="iSubPestana" [disabled]="disable" (selectionChange)="onSubPestanaChangeButton($event.value)">
                                <mat-option *ngFor="let pt of subpestanaButton" [value]="pt.pkIIdMenu">
                                {{ pt.vDescription }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                
                    <div *ngIf="formLandingSecciones.get('iActionButton')?.value == 3">
                        <label>{{ 'configurationClientPortal.link' | translate }}</label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.link' | translate }}</mat-label>
                            <input matInput formControlName="vLinkRedirection" />                          
                        </mat-form-field>
                        <div class="mb-3">
                            <mat-radio-group formControlName="iOpenLink" class="d-flex gap-3">
                                <mat-radio-button [value]="1">{{ 'configurationClientPortal.opentab' | translate
                                    }}</mat-radio-button>
                                <mat-radio-button [value]="2">{{ 'configurationClientPortal.opennewtab' | translate
                                    }}</mat-radio-button>                            
                            </mat-radio-group>
                        </div>
                    </div>                
                </div>

                <div  *ngIf="tipoSeccion == 8">
                    <label class="d-block subtitle-section">{{ 'configurationClientPortal.text' | translate }}</label>
                    <mat-checkbox formControlName="bActiveTexts" class="my-4">
                        {{ "configurationClientPortal.ActiveText" | translate }}
                    </mat-checkbox>
                    <p></p>
                    <label>{{ 'configurationClientPortal.tittle' | translate }}</label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                        <input matInput formControlName="vTitle" />                          
                    </mat-form-field>
                    <label>{{ 'configurationClientPortal.text' | translate }}</label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                        <input matInput formControlName="vText" />                          
                    </mat-form-field>
                </div>
                    <!-- posisicion imagen -->
                    <div  *ngIf="tipoSeccion == 1">
                        <div class="mb-3">
                            <label class="d-block">{{ 'configurationClientPortal.postitionIMG' | translate }}</label>
                            <mat-radio-group formControlName="iPosition" class="d-flex gap-3">
                                <mat-radio-button [value]="1">{{ 'configurationClientPortal.left' | translate
                                    }}</mat-radio-button>
                                <mat-radio-button [value]="2">{{ 'configurationClientPortal.right' | translate
                                    }}</mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>

                    <!-- Boton -->
                    <div *ngIf="tipoSeccion == 1">
                        <mat-slide-toggle formControlName="bActiveButton" class="mb-2">
                            {{ 'configurationClientPortal.button' | translate }}
                        </mat-slide-toggle>
                    </div>

                    <!-- texto del boton -->
                    <div *ngIf="tipoSeccion == 1 || tipoSeccion == 3">
                        <p></p>
                        <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                            <input matInput formControlName="vTextButton" />                          
                        </mat-form-field>
                    </div>

                    <div *ngIf="tipoSeccion == 1 || tipoSeccion == 3">
                        <div class="mb-3">
                            <mat-radio-group formControlName="iOpenLink" class="d-flex gap-3">
                                <mat-radio-button [value]="1">{{ 'configurationClientPortal.opentab' | translate
                                    }}</mat-radio-button>
                                <mat-radio-button [value]="2">{{ 'configurationClientPortal.opennewtab' | translate
                                    }}</mat-radio-button>                            
                            </mat-radio-group>
                        </div>
                    </div>
                    <!-- link de redireccioanmeinto -->
                    <div *ngIf="tipoSeccion == 1  || tipoSeccion == 3">
                        <p></p>
                        <mat-label>{{ 'configurationClientPortal.redirectionlink' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.redirectionlink' | translate }}</mat-label>
                            <input matInput formControlName="vLinkRedirection" />                          
                        </mat-form-field>
                    </div>

                    <div *ngIf="tipoSeccion == 1">
                        <mat-label>{{ 'configurationClientPortal.colorBanner' | translate }}</mat-label>
                        <div class="color-picker-container">
                                <div class="color-box" [style.background-color]="background"></div>                            
                                <mat-label>{{ 'configurationClientPortal.background' | translate }}</mat-label>                          
                                <mat-form-field appearance="outline" class="color-input">
                                <input matInput formControlName="vPrimaryColor" (input)="onInputChangeBakground($event)"
                                    placeholder="#000000" maxlength="7"/>
                                </mat-form-field>
                        </div>
                        <div class="color-picker-container">
                            <div class="color-box" [style.background-color]="colortittle"></div>                            
                            <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>                          
                            <mat-form-field appearance="outline" class="color-input">
                            <input matInput formControlName="vSecondaryColor" (input)="onInputChangeTittle($event)"
                                placeholder="#ffffff" maxlength="7"/>
                            </mat-form-field>
                        </div>
                        <div class="color-picker-container">
                            <div class="color-box" [style.background-color]="colortext"></div>                            
                            <mat-label>{{ 'configurationClientPortal.background' | translate }}</mat-label>                          
                            <mat-form-field appearance="outline" class="color-input">
                            <input matInput formControlName="vTertiaryColor" (input)="onInputChangeText($event)"
                                placeholder="#cccccc" maxlength="7"/>
                            </mat-form-field>
                        </div>

                        <mat-label>{{ 'configurationClientPortal.colorButton' | translate }}</mat-label>
                        <div class="color-picker-container">
                            <div class="color-box" [style.background-color]="Bbackground"></div>                            
                            <mat-label>{{ 'configurationClientPortal.background' | translate }}</mat-label>                          
                            <mat-form-field appearance="outline" class="color-input">
                            <input matInput formControlName="VQuaternaryColor" (input)="onInputChangeBBackground($event)"
                                placeholder="#ffffff" maxlength="7"/>
                            </mat-form-field>
                        </div>
                        <div class="color-picker-container">
                            <div class="color-box" [style.background-color]="Bcolortittle"></div>                            
                            <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>                          
                            <mat-form-field appearance="outline" class="color-input">
                            <input matInput formControlName="VQuinaryColor" (input)="onInputChangeBTittle($event)"
                                placeholder="#000000" maxlength="7"/>
                            </mat-form-field>
                        </div>
                    </div>

                     <!-- beneficio-->
                     <div *ngIf="tipoSeccion == 2 ">
                        <p></p>
                        <mat-label>{{ 'configurationClientPortal.benefic' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.benefic' | translate }}</mat-label>
                            <input matInput formControlName="vBenefic" />                          
                        </mat-form-field>
                        <button mat-raised-button color="primary" type="button" class="" (click)="saveBenefic()">
                            {{ editingItem ? ('configurationClientPortal.EditBenefic' | translate) : 
                                             ('configurationClientPortal.SaveBenefic' | translate) }}
                        </button>
                        <p></p>               
                        <mat-label>{{ 'configurationClientPortal.beneficRegister' | translate }}</mat-label>
                        <app-table [displayedColumns]="estructTBenefic" [data]="dataTableBeneficios"
                        (iconClick)="controllerModal($event)"></app-table>
                    </div>

                    <div *ngIf="tipoSeccion == 4">
                        <mat-label>{{ 'configurationClientPortal.imgRegister' | translate }}</mat-label>
                        <p></p>  
                        <app-table [displayedColumns]="estructTCarruselIMG" [data]="dataTableCarruselImg"
                        (iconClick)="controllerModal($event)"></app-table>
                        <button mat-raised-button color="primary" type="button" class="" (click)="openModalIMG()">
                            {{ "configurationClientPortal.addIMG" | translate }}
                        </button>
                    </div>

                    <div *ngIf="tipoSeccion == 5">
                        <mat-label>{{ 'configurationClientPortal.tarjetRegister' | translate }}</mat-label>
                        <p></p>  
                        <app-table [displayedColumns]="estructTTarjetas" [data]="dataTableTarjetas"
                        (iconClick)="controllerModal($event)"></app-table>
                        <button mat-raised-button color="primary" type="button" class="" (click)="openModalTarjet()">
                            {{ "configurationClientPortal.addTarjet" | translate }}
                        </button>
                    </div>
                    <div *ngIf="tipoSeccion == 6">
                        <mat-label>{{ 'configurationClientPortal.producRegister' | translate }}</mat-label>
                        <p></p>  
                        <app-table [displayedColumns]="estructTCarruselProducto" [data]="dataTableCarruselProducto"
                        (iconClick)="controllerModal($event)"></app-table>
                        <button mat-raised-button color="primary" type="button" class="" (click)="openModalProduct()">
                            {{ "configurationClientPortal.addProduct" | translate }}
                        </button>
                    </div>
                      <!-- formulario -->
                      <div *ngIf="tipoSeccion == 7">
                        <p></p>
                        <mat-label>{{ 'configurationClientPortal.form' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.form' | translate }}</mat-label>
                            <mat-select formControlName="iIdForm">
                                <mat-option *ngFor="let order of form" [value]="order.idProductModule ">
                                {{ order.vname }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>

                    <div *ngIf="tipoSeccion == 9">
                        <mat-label>{{ 'configurationClientPortal.NoticeRegister' | translate }}</mat-label>
                        <p></p>  
                        <app-table [displayedColumns]="estructTNews" [data]="dataTableNews"
                        (iconClick)="controllerModal($event)"></app-table>
                        <button mat-raised-button color="primary" type="button" class="" (click)="openModalNotice()">
                            {{ "configurationClientPortal.addNotice" | translate }}
                        </button>
                    </div>

                    <div *ngIf="tipoSeccion == 10">
                        <mat-label>{{ 'configurationClientPortal.QuestionRegister' | translate }}</mat-label>
                        <p></p>  
                        <app-table [displayedColumns]="estructTPreguntas" [data]="dataTableQuestion"
                        (iconClick)="controllerModal($event)"></app-table>
                        <button mat-raised-button color="primary" type="button" class="" (click)="openModalQuestion()">
                            {{ "configurationClientPortal.addQuestion" | translate }}
                        </button>
                    </div>

                    <div *ngIf="tipoSeccion == 11">
                        <mat-label>{{ 'configurationClientPortal.productRegister' | translate }}</mat-label>
                        <p></p>  
                        <app-table [displayedColumns]="estructTSecciones" [data]="dataTableProduct"
                        (iconClick)="controllerModal($event)"></app-table>
                        <button mat-raised-button color="primary" type="button" class="" (click)="openModaladdProduct()">
                            {{ "configurationClientPortal.addProduct" | translate }}
                        </button>
                    </div>
                                   
                </form>
                
            </ng-container>
            <ng-container customButtonRight>
                <div class="modal-footer">
                    <button mat-raised-button color="primary" type="button" class="" (click)="saveSeccion()">
                        {{ "configurationClientPortal.SaveSeccion" | translate }}
                    </button>
                </div>
            </ng-container>
        </app-modal2>
    </ng-template>






    <!-- imagen -->
    <ng-template #createEditIMG>
        <app-modal2
            [titleModal]="(pkSeccionProductoToModify === 0 ? 'configurationClientPortal.newIMG' : 'configurationClientPortal.UpdateIMG') | translate">
            <ng-container body>
                <form [formGroup]="formCarruselImg">
                      
                    <!-- orden  -->
                    <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>

                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                        <mat-select formControlName="iOrder">
                            <mat-option *ngFor="let order of orderimagen" [value]="order">
                              {{ order }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>

                     <!-- Imagen PC -->
                        <mat-label>{{ 'configurationClientPortal.imgPC' | translate }}</mat-label>
                        <div class="upload-container" matTooltip="Solo se permiten archivos PNG, JPG, SVG">
                        <div
                            class="upload-area"
                            [ngClass]="{ 'dragover': isDragOver }"
                            (drop)="onFileDropped($event)"
                            (dragover)="onDragOver($event)"
                            (dragleave)="isDragOver = false"
                        >
                            <input
                            type="file"
                            (onChangeValidated)="onFileSelected($event)"
                            id="fileUploadPC"
                            accept="image/png,image/jpeg,image/svg+xml"
                            hidden
                            ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"
                            />
                            <label
                            for="fileUploadPC"
                            class="upload-label"
                            [ngClass]="{ 'uploaded': showBtnDelete }"
                            >
                            <mat-icon
                                class="mb-5"
                                [style.color]="showBtnDelete ? 'green' : 'inherit'"
                                style="overflow: visible; margin-right: 23px"
                            >
                                {{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}
                            </mat-icon>
                            <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>
                            <p *ngIf="!showBtnDelete && !fileName">
                                Haga clic para cargar o arrastre y suelte aquí los archivos
                            </p>
                            <span>PNG, JPG, SVG</span>
                            </label>
                        </div>
                        </div>
                    
                    <!-- Imagen móvil -->
                        <mat-label>{{ 'configurationClientPortal.imgMovil' | translate }}</mat-label>
                        <div class="upload-container" matTooltip="Solo se permiten archivos PNG, JPG, SVG">
                        <div
                            class="upload-area"
                            [ngClass]="{ 'dragover': isDragOverMovil }"
                            (drop)="onFileDroppedMovil($event)"
                            (dragover)="onDragOverMovil($event)"
                            (dragleave)="isDragOverMovil = false"
                        >
                            <input
                            type="file"
                            (onChangeValidated)="onFileSelectedMovil($event)"
                            id="fileUploadMovil"
                            accept="image/png,image/jpeg,image/svg+xml"
                            hidden
                            ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"
                            />
                            <label
                            for="fileUploadMovil"
                            class="upload-label"
                            [ngClass]="{ 'uploaded': showBtnDeleteMovil }"
                            >
                            <mat-icon
                                class="mb-5"
                                [style.color]="showBtnDeleteMovil ? 'green' : 'inherit'"
                                style="overflow: visible; margin-right: 23px"
                            >
                                {{ showBtnDeleteMovil || fileNameMovil ? 'check_circle' : 'cloud_upload' }}
                            </mat-icon>
                            <p *ngIf="showBtnDeleteMovil || fileNameMovil">{{ fileNameMovil }}</p>
                            <p *ngIf="!showBtnDeleteMovil && !fileNameMovil">
                                Haga clic para cargar o arrastre y suelte aquí los archivos
                            </p>
                            <span>PNG, JPG, SVG</span>
                            </label>
                        </div>
                        </div>

                </form>
            </ng-container>
            <ng-container customButtonRight>
                <div class="modal-footer">
                    <button mat-raised-button color="primary" type="button" class="" (click)="saveCarruselIMG()">
                        {{ "Save" | translate }}
                    </button>
                </div>
            </ng-container>
        </app-modal2>
    </ng-template>

    
    <!-- tarjeta-->
    <ng-template #createEditTarjet>
        <app-modal2
            [titleModal]="(pkSeccionCardsToModify === 0 ? 'configurationClientPortal.newTarjet' : 'configurationClientPortal.UpdateTarjet') | translate">
            <ng-container body>
                <form [formGroup]="formTarjetas">
                    <!-- orden  -->
                    <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                        <mat-select formControlName="iOrder">
                            <mat-option *ngFor="let order of orderTarjetas" [value]="order">
                              {{ order }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>

                    <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                            <input matInput formControlName="vDescription" />                          
                        </mat-form-field>
                    
                    <mat-label>{{ 'configurationClientPortal.category' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.category' | translate }}</mat-label>
                            <input matInput formControlName="vCategory" />                          
                        </mat-form-field>
                    
                        <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                            <input matInput formControlName="vText" />                          
                        </mat-form-field>

                        <mat-label>{{ 'configurationClientPortal.imgPC' | translate }}</mat-label>
                        <div class="upload-container" matTooltip="Solo se permiten archivos PNG, JPG, SVG">
                            <div
                            class="upload-area"
                            [ngClass]="{ 'dragover': isDragOver }"
                            (drop)="onFileDropped($event)"
                            (dragover)="onDragOver($event)"
                            (dragleave)="isDragOver = false"
                            >
                            <input
                            type="file"
                            (onChangeValidated)="onFileSelected($event)"
                            id="fileUploadPC"
                            accept="image/png,image/jpeg,image/svg+xml"
                            hidden
                            ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"
                            />
                            <label
                            for="fileUploadPC"
                            class="upload-label"
                            [ngClass]="{ 'uploaded': showBtnDelete }"
                            >
                            <mat-icon
                                class="mb-5"
                                [style.color]="showBtnDelete ? 'green' : 'inherit'"
                                style="overflow: visible; margin-right: 23px"
                            >
                                {{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}
                            </mat-icon>
                            <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>
                            <p *ngIf="!showBtnDelete && !fileName">
                                Haga clic para cargar o arrastre y suelte aquí los archivos
                            </p>
                            <span>PNG, JPG, SVG</span>
                            </label>
                            </div>
                        </div>

                          <!-- boton -->
                            <mat-slide-toggle formControlName="bActiveButton" class="mb-2">
                                {{ 'configurationClientPortal.button' | translate }}
                            </mat-slide-toggle>
    
                        <!-- texto del boton -->
                            <p></p>
                            <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                                <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                                <input matInput formControlName="vTextButton" />                          
                            </mat-form-field>

                        <!-- redireccion ink -->
                        <div class="mb-3">
                            <mat-radio-group formControlName="iOpenLink" class="d-flex gap-3">
                                <mat-radio-button [value]="1">{{ 'configurationClientPortal.opentab' | translate
                                    }}</mat-radio-button>
                                <mat-radio-button [value]="2">{{ 'configurationClientPortal.opennewtab' | translate
                                    }}</mat-radio-button>                            
                            </mat-radio-group>
                        </div>
    
                        <!-- link de redireccioanmeinto -->
                            <p></p>
                            <mat-label>{{ 'configurationClientPortal.redirectionlink' | translate }}</mat-label>
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                                <mat-label>{{ 'configurationClientPortal.redirectionlink' | translate }}</mat-label>
                                <input matInput formControlName="vLinkRedirection" />                          
                            </mat-form-field>

                    
                </form>
            </ng-container>
            <ng-container customButtonRight>
                <div class="modal-footer">
                    <button mat-raised-button color="primary" type="button" class="" (click)="saveTarjetas()">
                        {{ "Save" | translate }}
                    </button>
                </div>
            </ng-container>
        </app-modal2>
    </ng-template>

     <!-- producto-->
     <ng-template #createEditProduct>
        <app-modal2
            [titleModal]="(pkSeccionCorosuelProductoToModify === 0 ? 'configurationClientPortal.newProduct' : 'configurationClientPortal.UpdateProduct') | translate">
            <ng-container body>
                <form [formGroup]="formCarruselProducto">
                      
                    <!-- orden  -->
                    <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>

                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                        <mat-select formControlName="iOrder">
                            <mat-option *ngFor="let order of orderCarruselproducto" [value]="order">
                              {{ order }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>


                    <!-- producto vinculado-->
                    <mat-label>{{ 'configurationClientPortal.ProductVinc' | translate }}</mat-label>

                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.ProductVinc' | translate }}</mat-label>
                        <mat-select formControlName="iIdProduct">
                            <mat-option *ngFor="let product of Listproducts" [value]="product.pkIIdProduct">{{ product.vProductName
                            }}</mat-option>
                        </mat-select>
                    </mat-form-field>


                    <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                            <input matInput formControlName="vTitle" />                          
                        </mat-form-field>
                    
                        <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                            <input matInput formControlName="vText" />                          
                        </mat-form-field>
               
                        <mat-label>{{ 'configurationClientPortal.imgPC' | translate }}</mat-label>
                        <div class="upload-container" matTooltip="Solo se permiten archivos PNG, JPG, SVG">
                        <div
                            class="upload-area"
                            [ngClass]="{ 'dragover': isDragOver }"
                            (drop)="onFileDropped($event)"
                            (dragover)="onDragOver($event)"
                            (dragleave)="isDragOver = false"
                        >
                            <input
                            type="file"
                            (onChangeValidated)="onFileSelected($event)"
                            id="fileUploadPC"
                            accept="image/png,image/jpeg,image/svg+xml"
                            hidden
                            ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"
                            />
                            <label
                            for="fileUploadPC"
                            class="upload-label"
                            [ngClass]="{ 'uploaded': showBtnDelete }"
                            >
                            <mat-icon
                                class="mb-5"
                                [style.color]="showBtnDelete ? 'green' : 'inherit'"
                                style="overflow: visible; margin-right: 23px"
                            >
                                {{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}
                            </mat-icon>
                            <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>
                            <p *ngIf="!showBtnDelete && !fileName">
                                Haga clic para cargar o arrastre y suelte aquí los archivos
                            </p>
                            <span>PNG, JPG, SVG</span>
                            </label>
                        </div>
                        </div>

                          

                    
                </form>
            </ng-container>
            <ng-container customButtonRight>
                <div class="modal-footer">
                    <button mat-raised-button color="primary" type="button" class="" (click)="saveCarruselProducto()">
                        {{ "configurationClientPortal.addTarjet" | translate }}
                    </button>
                </div>
            </ng-container>
        </app-modal2>
    </ng-template>

    <!-- noticias-->
    <ng-template #createEditNotice>
            <app-modal2
                [titleModal]="(pkSeccionNewToModify === 0 ? 'configurationClientPortal.newNotice' : 'configurationClientPortal.UpdateNotice') | translate">
                <ng-container body>
                    <form [formGroup]="formNews">
                          
                        <!-- orden  -->
                        <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                            <mat-select formControlName="IOrder">
                                <mat-option *ngFor="let order of ordernoticias" [value]="order">
                                  {{ order }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
    
    
                        <!-- categoria-->
                        <mat-label>{{ 'configurationClientPortal.category' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.category' | translate }}</mat-label>
                            <input matInput formControlName="VCategory" />                          
                        </mat-form-field>

                        <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                                <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                                <input matInput formControlName="VTitle" />                          
                            </mat-form-field>
                     
                            <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                                <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                                <input matInput formControlName="VDescription" />                          
                            </mat-form-field>
                   
                        <!-- Boton -->
                            <mat-slide-toggle formControlName="BActiveButton" class="mb-2">
                                {{ 'configurationClientPortal.button' | translate }}
                            </mat-slide-toggle>

                        <!-- texto del boton -->
                            <p></p>
                            <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                                <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                                <input matInput formControlName="VTextButton" />                          
                            </mat-form-field>
                            <!-- redireccion ink -->
                            <div class="mb-3">
                                <mat-radio-group formControlName="iOpenLink" class="d-flex gap-3">
                                    <mat-radio-button [value]="1">{{ 'configurationClientPortal.opentab' | translate
                                        }}</mat-radio-button>
                                    <mat-radio-button [value]="2">{{ 'configurationClientPortal.opennewtab' | translate
                                        }}</mat-radio-button>                            
                                </mat-radio-group>
                            </div>

                        <!-- link de redireccioanmeinto -->
                            <p></p>
                            <mat-label>{{ 'configurationClientPortal.redirectionlink' | translate }}</mat-label>
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                                <mat-label>{{ 'configurationClientPortal.redirectionlink' | translate }}</mat-label>
                                <input matInput formControlName="VLinkRedirection" />                          
                            </mat-form-field>
                           
                            <p></p>
                            <mat-label>{{ 'configurationClientPortal.date' | translate }}</mat-label>
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                                <input matInput [matDatepicker]="picker" formControlName="DEndDate" placeholder="MM/DD/YYYY">
                                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                                <mat-datepicker #picker></mat-datepicker>
                              </mat-form-field>


                            <mat-label>{{ 'configurationClientPortal.imgPC' | translate }}</mat-label>
                        <div class="upload-container" matTooltip="Solo se permiten archivos PNG, JPG, SVG">
                        <div
                            class="upload-area"
                            [ngClass]="{ 'dragover': isDragOver }"
                            (drop)="onFileDropped($event)"
                            (dragover)="onDragOver($event)"
                            (dragleave)="isDragOver = false"
                        >
                            <input
                            type="file"
                            (onChangeValidated)="onFileSelected($event)"
                            id="fileUploadPC"
                            accept="image/png,image/jpeg,image/svg+xml"
                            hidden
                            ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"
                            />
                            <label
                            for="fileUploadPC"
                            class="upload-label"
                            [ngClass]="{ 'uploaded': showBtnDelete }"
                            >
                            <mat-icon
                                class="mb-5"
                                [style.color]="showBtnDelete ? 'green' : 'inherit'"
                                style="overflow: visible; margin-right: 23px"
                            >
                                {{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}
                            </mat-icon>
                            <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>
                            <p *ngIf="!showBtnDelete && !fileName">
                                Haga clic para cargar o arrastre y suelte aquí los archivos
                            </p>
                            <span>PNG, JPG, SVG</span>
                            </label>
                        </div>
                        </div>
    
                              
    
                        
                    </form>
                </ng-container>
                <ng-container customButtonRight>
                    <div class="modal-footer">
                        <button mat-raised-button color="primary" type="button" class="" (click)="saveNews()">
                            {{ "configurationClientPortal.addNotice" | translate }}
                        </button>
                    </div>
                </ng-container>
            </app-modal2>
    </ng-template>

    <!-- preguntas-->
    <ng-template #createEditQuestion>
        <app-modal2
            [titleModal]="(pkSeccionQuestionToModify === 0 ? 'configurationClientPortal.newQuestion' : 'configurationClientPortal.UpdateQuestion') | translate">
            <ng-container body>
                <form [formGroup]="formQuestion">
                      
                    <!-- orden  -->
                    <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                        <mat-select formControlName="iOrder">
                            <mat-option *ngFor="let order of orderpreguntas" [value]="order">
                              {{ order }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>


                    <!-- pregunta-->
                    <mat-label>{{ 'configurationClientPortal.question' | translate }}</mat-label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.question' | translate }}</mat-label>
                        <input matInput formControlName="vQuestion" />                          
                    </mat-form-field>

                    <mat-label>{{ 'configurationClientPortal.answer' | translate }}</mat-label>
                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.answer' | translate }}</mat-label>
                        <input matInput formControlName="vAnswer" />                          
                    </mat-form-field>                                    
                </form>
            </ng-container>
            <ng-container customButtonRight>
                <div class="modal-footer">
                    <button mat-raised-button color="primary" type="button" class="" (click)="saveQuestion()">
                        {{ "configurationClientPortal.addQuestion" | translate }}
                    </button>
                </div>
            </ng-container>
        </app-modal2>
    </ng-template>

    <!-- producto-->
    <ng-template #createEditaddProduct>
            <app-modal2
                [titleModal]="(pkSeccionProductToModify === 0 ? 'configurationClientPortal.newProduct' : 'configurationClientPortal.UpdateProduct') | translate">
                <ng-container body>
                    <form [formGroup]="formProduct">
                          
                        <!-- orden  -->
                        <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.Orden' | translate }}</mat-label>
                            <mat-select formControlName="iOrder">
                                <mat-option *ngFor="let order of orderproducto" [value]="order">
                                  {{ order }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>

                        <mat-label>{{ 'configurationClientPortal.category' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.category' | translate }}</mat-label>
                            <input matInput formControlName="vCategory" />                          
                        </mat-form-field>
    
                        <!-- producto vinculado-->
                    <mat-label>{{ 'configurationClientPortal.ProductVinc' | translate }}</mat-label>

                    <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>{{ 'configurationClientPortal.ProductVinc' | translate }}</mat-label>
                        <mat-select formControlName="fkIIdProduct" (selectionChange)="GetCoverage($event.value)">
                            <mat-option *ngFor="let product of Listproducts" [value]="product.pkIIdProduct">{{ product.vProductName
                            }}</mat-option>
                        </mat-select>
                    </mat-form-field>
    
                        <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.tittle' | translate }}</mat-label>
                            <input matInput formControlName="vTitle" />                          
                        </mat-form-field>
    
                        <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                                <mat-label>{{ 'configurationClientPortal.text' | translate }}</mat-label>
                                <input matInput formControlName="vText" />                          
                            </mat-form-field>
                            <mat-label>{{ 'configurationClientPortal.imgPC' | translate }}</mat-label>
                            <div class="upload-container" matTooltip="Solo se permiten archivos PNG, JPG, SVG">
                            <div
                                class="upload-area"
                                [ngClass]="{ 'dragover': isDragOver }"
                                (drop)="onFileDropped($event)"
                                (dragover)="onDragOver($event)"
                                (dragleave)="isDragOver = false"
                            >
                                <input
                                type="file"
                                (onChangeValidated)="onFileSelected($event)"
                                id="fileUploadPC"
                                accept="image/png,image/jpeg,image/svg+xml"
                                hidden
                                ValidationInputFile [allowedExtensions]="allowedExtensions" [maxFileSizeMB]="maxFileSizeMB"
                                />
                                <label
                                for="fileUploadPC"
                                class="upload-label"
                                [ngClass]="{ 'uploaded': showBtnDelete }"
                                >
                                <mat-icon
                                    class="mb-5"
                                    [style.color]="showBtnDelete ? 'green' : 'inherit'"
                                    style="overflow: visible; margin-right: 23px"
                                >
                                    {{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}
                                </mat-icon>
                                <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>
                                <p *ngIf="!showBtnDelete && !fileName">
                                    Haga clic para cargar o arrastre y suelte aquí los archivos
                                </p>
                                <span>PNG, JPG, SVG</span>
                                </label>
                            </div>
                            </div>
                     
                        <mat-label>{{ 'configurationClientPortal.colors' | translate }}</mat-label>
                        <div class="color-picker-container">
                            <div class="color-box" [style.background-color]="colorprimary"></div>  
                            
                            <mat-label>{{ 'configurationClientPortal.primary' | translate }}</mat-label>                          
                            <mat-form-field appearance="outline" class="color-input">
                              <input matInput formControlName="vPrimaryColor" (input)="onInputChange($event)"
                                placeholder="#000000" maxlength="7"
                              />
                            </mat-form-field>

                        </div>
                        <div class="color-picker-container">
                            <div class="color-box" [style.background-color]="colorsecundary"></div>
                            <mat-label>{{ 'configurationClientPortal.secundary' | translate }}</mat-label>                          
                            <mat-form-field appearance="outline" class="color-input">
                              <input matInput formControlName="vSecondaryColor" (input)="onInputChangeS($event)"
                                placeholder="#000000" maxlength="7"
                              />
                            </mat-form-field>

                        </div>
                        <div class="color-picker-container">
                            <div class="color-box" [style.background-color]="colortertiary"></div>

                            <mat-label>{{ 'configurationClientPortal.tertiary' | translate }}</mat-label>                          
                            <mat-form-field appearance="outline" class="color-input">
                              <input matInput formControlName="vTertiaryColor" (input)="onInputChangeT($event)"
                                placeholder="#000000" maxlength="7"
                              />
                            </mat-form-field>

                        </div>
                          
                           

                    <!-- posisicion imagen -->
                        <div class="mb-3">
                            <label class="d-block">{{ 'configurationClientPortal.postitionIMG' | translate }}</label>
                            <mat-radio-group formControlName="iPosition" class="d-flex gap-3">
                                <mat-radio-button [value]="1">{{ 'configurationClientPortal.right' | translate
                                    }}</mat-radio-button>
                                <mat-radio-button [value]="2">{{ 'configurationClientPortal.left' | translate
                                    }}</mat-radio-button>
                            </mat-radio-group>
                        </div>

                        <!-- Boton -->
                        <mat-slide-toggle formControlName="bActiveKnowMore" class="mb-2">
                            {{ 'configurationClientPortal.buttonmore' | translate }}
                        </mat-slide-toggle>
                        <mat-slide-toggle formControlName="bActiveQuote" class="mb-2">
                            {{ 'configurationClientPortal.buttonquote' | translate }}
                        </mat-slide-toggle>
                        <p></p>


                         <!-- redireccion ink -->
                         <div class="mb-3">
                            <mat-radio-group formControlName="iOpenLink" class="d-flex gap-3">
                                <mat-radio-button [value]="1">{{ 'configurationClientPortal.opentab' | translate
                                    }}</mat-radio-button>
                                <mat-radio-button [value]="2">{{ 'configurationClientPortal.opennewtab' | translate
                                    }}</mat-radio-button>                            
                            </mat-radio-group>
                        </div>

                        <mat-label>{{ 'configurationClientPortal.redirectionlink' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.redirectionlink' | translate }}</mat-label>
                            <input matInput formControlName="vLinkRedirection" />                          
                        </mat-form-field>
                              

                        <mat-label>{{ 'configurationClientPortal.selectCobertura' | translate }}</mat-label>
                        <p></p>
                        <div class="d-flex align-items-center justify-content-between">
                            <form [formGroup]="formCoverage">
                                <!-- Selector de Cobertura -->
                                <mat-form-field appearance="outline" class="flex-grow-1 me-2">
                                    <mat-select formControlName="iCoverage" (selectionChange)="onCoverage($event.value)" [compareWith]="compareCoverage">
                                      <mat-option *ngFor="let pt of ListCoverage" [value]="pt">
                                        {{ pt.vName }}
                                      </mat-option>
                                    </mat-select>
                                </mat-form-field>                                  
                              
                                <!-- Botón para Guardar Cobertura -->
                                <button 
                                  mat-raised-button 
                                  color="primary" 
                                  type="button" 
                                  class="btn-add" 
                                  (click)="saveCoverage()">
                                  {{ "configurationClientPortal.addProduct" | translate }}
                                </button>
                              </form>
                        </div>

                        <app-table [displayedColumns]="estructTableProductos" [data]="dataTableCoverage"
                            (iconClick)="controllerModal($event)"></app-table>
    
                        
                    </form>
                </ng-container>
                <ng-container customButtonRight>
                    <div class="modal-footer">
                        <button mat-raised-button color="primary" type="button" class="" (click)="saveProduct()">
                            {{ "configurationClientPortal.addQuestion" | translate }}
                        </button>
                    </div>
                </ng-container>
            </app-modal2>
    </ng-template>