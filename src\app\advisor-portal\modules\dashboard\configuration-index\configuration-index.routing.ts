import { Routes } from '@angular/router';
import { ConfigurationIndexComponent } from './configuration-index.component'

export default [
    {
        path: '',
        component: ConfigurationIndexComponent,
        children: [
            {
                path: '',
                loadComponent: () =>
                    import('./all-configuration-index/all-configuration-index.component').then(
                        (c) => c.AllConfigurationIndexComponent
                    ),
            },
            {
                path: 'all-tops',
                loadComponent: () =>
                    import('./all-configuration-index/tops/all-tops/all-tops.component').then(
                        (c) => c.AllTopsComponent
                    ),
            },
            {
                path: 'new',
                loadComponent: () =>
                    import('./all-configuration-index/active-news/edit-active-news/edit-active-news.component').then(
                        (c) => c.EditActiveNewsComponent
                    ),
            },
            {
                path: 'edit/:pkIIdNews',
                loadComponent: () =>
                    import('./all-configuration-index/active-news/edit-active-news/edit-active-news.component').then(
                        (c) => c.EditActiveNewsComponent
                    ),
            },
        ]
    },
] as Routes;