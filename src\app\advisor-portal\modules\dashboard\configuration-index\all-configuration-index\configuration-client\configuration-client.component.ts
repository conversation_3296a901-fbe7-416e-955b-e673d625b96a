import { Component, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { AllActiveNewsComponent } from '../active-news/all-active-news/all-active-news.component';
import { catchError, of, Subscription } from 'rxjs';
import { SettingService } from '../../../../../../shared/services/setting/setting.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AllSectionComponent } from '../section-client-portal/all-section/all-section.component';
import { BenefitsComponent } from '../section-client-portal/benefits/benefits.component';
import { CoveragesComponent } from '../section-client-portal/coverages/coverages.component';
import { QuestionsComponent } from '../section-client-portal/questions/questions.component';
import { AdditionalConfigurationComponent } from '../section-client-portal/additional-configuration/additional-configuration.component';
import { AlliancesComponent } from '../section-client-portal/alliances/alliances.component';
import { StepsComponent } from '../section-client-portal/steps/steps.component';
import { LinksComponent } from '../section-client-portal/links/links.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-configuration-client',
  templateUrl: './configuration-client.component.html',
  styleUrls: ['./configuration-client.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    AllActiveNewsComponent,
    AllSectionComponent,
    BenefitsComponent,
    QuestionsComponent,
    CoveragesComponent,
    AdditionalConfigurationComponent,
    AlliancesComponent,
    StepsComponent,
    LinksComponent,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    TranslateModule
  ],
})
export class ConfigurationClientComponent implements OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;

  idBusinessByCountry: number = 0;
  templateList: any[] = [];
  dataTemplatesBusinessByCountry: any[] = [];
  selectedTemplate: number | null = null;
  existsRecord: boolean = false;

  constructor(
    private _router: Router,
    private _settingService: SettingService,
    private _businessService: BusinessService,
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    private _messageService: MessageService
  ) {
    this.getSettingCountryAndCompanySubscription();
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessByCountry =
                response.enterprise.pkIIdBusinessByCountry;
              this.getInitData();
            }
          }
        }
      );
  }

  getInitData() {
    this.getTemplates();
    this.getTemplatesBusinessCountry();
  }

  getTemplates() {
    this._businessService
      .getTemplates()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.templateList = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.templateList = resp.result;
          }
        }
      });
  }

  templatesChange(evt: any) {
    this.selectedTemplate = evt.value;
  }

  public saveTemplate() {

    if (
      this.idBusinessByCountry == null ||
      this.idBusinessByCountry == 0 ||
      this.selectedTemplate == null ||
      this.selectedTemplate == 0
    ) {
      this._msgSvc.messageWaring(
        this._translateService.instant('ThereWasAError'),
        'No esta la información completa para realizar el guardado.'
      );
      return;
    }

    if (this.existsRecord){
      this.modifyTemplateBusinessCountry();
      return;
    }

    this._businessService
      .createTemplateBusinessCountry({
        fkIIdBusinessByCountry: this.idBusinessByCountry,
        fkIIdTemplates: this.selectedTemplate,
      })
      .subscribe({
        next: (resp) => {
          if (!resp.error) {

            this.existsRecord = true;
            this._messageService.messageSuccess(
              this._translateService.instant('ClientPortal.Confirmation'),
              ''
            );
          }
        },
        error: (err) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            'No fue posible registrar la información.'
          );
        }
      });
  }

  private getTemplatesBusinessCountry() {
    this._businessService
      .getTemplatesBusinessCountry(this.idBusinessByCountry)
      .subscribe({
        next: (resp) => {
          if (!resp.error) {
            const savedTemplate = resp.result;
            if (savedTemplate != null) {
              this.selectedTemplate = savedTemplate.pkIIdTemplatesBusinessByCountry;
              this.existsRecord = true;
            } else {
              this.selectedTemplate = null;
              this.existsRecord = false;
            }
          } else {
            this.existsRecord = false;
          }
        },
      });
  }

  public modifyTemplateBusinessCountry() {
    if (
      this.idBusinessByCountry == null ||
      this.idBusinessByCountry == 0 ||
      this.selectedTemplate == null ||
      this.selectedTemplate == 0
    ) {
      this._msgSvc.messageWaring(
        this._translateService.instant('ThereWasAError'),
        'No esta la información completa para realizar la actualización.'
      );
      return;
    }

    this._businessService
      .modifyTemplateBusinessCountry(
        {
          fkIIdBusinessByCountry: this.idBusinessByCountry,
          fkIIdTemplates: this.selectedTemplate,
        },
        this.idBusinessByCountry
      )
      .subscribe({
        next: (resp) => {
          this._messageService.messageSuccess(
            this._translateService.instant('ClientPortal.Confirmation'),
            ''
          );
        },
      });
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
