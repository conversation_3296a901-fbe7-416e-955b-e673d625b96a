import {
  Component,
  TemplateRef,
  ViewChild,
  Output,
  EventEmitter,
  OnDestroy,
  OnInit,
  Input,
} from '@angular/core';
import { CommonModule, Location } from '@angular/common';

import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { ResponseGlobalModel } from 'src/app/shared/models/response';

import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';

import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import {
  TranslateModule,
  TranslateService,
  LangChangeEvent,
} from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { SheetModel } from 'src/app/shared/models/composite-catalog/sheet.model';
import { catchError, of, Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { ColumnsModel } from 'src/app/shared/models/composite-catalog/column.model';

@Component({
  selector: 'app-edit-catalog-column',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    TableComponent,
    Modal2Component,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  templateUrl: './edit-catalog-column.component.html',
  styleUrls: ['./edit-catalog-column.component.scss'],
})
export class EditCatalogColumnComponent implements OnInit, OnDestroy {
  @Input() idCatalog: number = 0;
  @Input() idHistoryCatalog: number = 0;
  @Output() setActiveTabCatalog = new EventEmitter<any>();

  @ViewChild('AddColumnModal') AddColumnModal?: TemplateRef<any>;
  private _settingCompositeCatalogSubscription?: Subscription;

  form: FormGroup = new FormGroup({});

  modalTitle: string = this._translateService.instant(
    'CatalogSetting.AddColumn'
  );
  dataJson: any = {};

  dataColumn: any = {};

  listSheets: SheetModel[] = [];
  dataColumnTable: any[] = [];
  originalColumnArray: any[] = [];
  dataChildrenRelation: any[] = [];
  originalChildrenRelationArray: any[] = [];
  listColumn: any[] = [];
  listColumnRelation: any[] = [];
  listSheetParent: any[] = [];

  isEdit: boolean = false;
  isEditRelation: boolean = false;

  /// Estruct table columns
  estructColumnsTable: BodyTableModel[] = [
    {
      columnLabel: 'ID',
      columnValue: 'id',
    },
    {
      columnLabel: this._translateService.instant(
        'CatalogSetting.AssociatedField'
      ),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('CatalogSetting.CatalogName'),
      columnValue: 'vNameColumn',
    },
    {
      columnLabel: this._translateService.instant('CatalogSetting.Dependent'),
      columnValue: 'bIsDependent',
      functionValue: (item: any) => this.parserConditionsDependentTable(item),
    },
    {
      columnLabel: this._translateService.instant(
        'CatalogSetting.DependentSheet'
      ),
      columnValue: 'nameSheetparent',
      hidenColumn: true,
    },
    {
      columnLabel: '',
      columnValue: 'fkIIdSheetParent',
      hidenColumn: true,
    },
    {
      columnLabel: '',
      columnValue: 'fkIIdColumn',
      hidenColumn: true,
    },
    {
      columnLabel: this._translateService.instant(
        'CatalogSetting.Relationships'
      ),
      columnValue: 'bIsChildren',
      functionValue: (item: any) => this.parserConditionsChildrenTable(item),
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  //// Estruct table relation for columns
  estructChildrenRelationTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('CatalogSetting.Sheet'),
      columnValue: 'nameSheetChildren',
    },
    {
      columnLabel: this._translateService.instant('CatalogSetting.Column'),
      columnValue: 'nameColumnRelation',
    },
    {
      columnLabel: '',
      columnValue: 'fkIIdSheet',
      hidenColumn: true,
    },
    {
      columnLabel: '',
      columnValue: 'fkIIdColumn',
      hidenColumn: true,
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  constructor(
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _catalogService: CatalogSettingService,
    private _msgSvc: MessageService,
    public _utilsSvc: UtilsService,
    public _location: Location,
    public modalDialog: MatDialog,
    private _fb: FormBuilder,
    public _router: Router
  ) {}

  ngOnInit(): void {
    this.getSettinCompositeCatalogSubscription();
    this.getColumnByIdHistoryCatalog(this.idCatalog);
    this.initForm();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      /// traduction titles modals
      this.modalTitle = this._translateService.instant(
        'CatalogSetting.AddColumn'
      );

      //traduccion data table column
      this.estructColumnsTable[1].columnLabel = this._translateService.instant(
        'CatalogSetting.AssociatedField'
      );
      this.estructColumnsTable[1].columnLabel = this._translateService.instant(
        'CatalogSetting.CatalogName'
      );
      this.estructColumnsTable[2].columnLabel = this._translateService.instant(
        'CatalogSetting.Dependent'
      );
      this.estructColumnsTable[3].columnLabel = this._translateService.instant(
        'CatalogSetting.DependentSheet'
      );
      this.estructColumnsTable[7].columnLabel = this._translateService.instant(
        'CatalogSetting.Relationships'
      );

      this.estructColumnsTable[8].columnLabel =
        this._translateService.instant('Delete');
      this.estructColumnsTable[9].columnLabel =
        this._translateService.instant('Modify');

      //traduccion data table relation children
      this.estructChildrenRelationTable[0].columnLabel =
        this._translateService.instant('CatalogSetting.Sheet');
      this.estructChildrenRelationTable[1].columnLabel =
        this._translateService.instant('CatalogSetting.Column');
      this.estructChildrenRelationTable[4].columnLabel =
        this._translateService.instant('Delete');
      this.estructChildrenRelationTable[5].columnLabel =
        this._translateService.instant('Modify');
    });

    this.form.get('bIsDependent')?.valueChanges.subscribe((value) => {
      if (!value) {
        this.form.get('fkIIdSheetParent')?.setValue(0);
        this.form.get('fkIIdColumn')?.setValue(0);
      }
    });

    this.form.get('bIsChildren')?.valueChanges.subscribe((value) => {
      if (!value) {
        if (this.dataChildrenRelation.length == 0) {
          this.form.get('fkIIdSheetRelation')?.setValue(0);
          this.form.get('fkIIdColumnRelation')?.setValue(0);
        } else {
          if (this.idHistoryCatalog > 0) {
            this.form.get('bIsChildren')?.setValue(true);
            this._msgSvc.messageInfo(
              this._translateService.instant(
                'CatalogSetting.MessagesCatalogo.Warning'
              ),
              this._translateService.instant(
                'CatalogSetting.MessagesCatalogo.CannotBeDeleted'
              )
            );
          } else {
            this.form.get('fkIIdSheetRelation')?.setValue(0);
            this.form.get('fkIIdColumnRelation')?.setValue(0);
            this.dataChildrenRelation = [];
          }
        }
      }
    });
  }

  initForm() {
    this.form = this._fb.group({
      id: [0],
      vName: ['', Validators.required],
      vNameColumn: ['', Validators.required],
      fkIIdSheet: [0, Validators.required],
      bIsRemoveDuplicated: [false],
      bIsDependent: [false],
      bIsChildren: [{ value: false, disabled: true }],
      fkIIdSheetParent: [0],
      fkIIdColumn: [0],
      fkIIdSheetRelation: [0],
      fkIIdColumnRelation: [0],
    });
    this.dataListDropdownSheets();
  }

  get valid(): boolean {
    return this.form.valid;
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'delete':
        this._msgSvc
          .messageConfirmationAndNegation(
            this._translateService.instant(
              'CatalogSetting.MessagesCatalogo.DeleteItem'
            ),
            this._translateService.instant(
              'CatalogSetting.MessagesCatalogo.FunctionalityCatalog'
            ),
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.deleteColumnArrayOriginal(event.value.id);
            }
          });
        break;
      case 'modify':
        this.modalTitle = this._translateService.instant(
          'CatalogSetting.EditColumn'
        );
        this.isEdit = true;
        this.openModal();
        this.form.patchValue(event.value);
        this.form.get('bIsChildren')?.enable();
        this.getChildrenRelation(event.value.children);
        this.getDatalistColumnFilter(
          'D',
          this.form.get('fkIIdSheetParent')?.value
        );
        this.getDatalistColumnFilter(
          'R',
          this.form.get('fkIIdSheetRelation')?.value
        );
        break;
      default:
        break;
    }
  }

  controllerChildrenRelation(event: IconEventClickModel) {
    switch (event.column) {
      case 'delete':
        this.deleteRelationArrayOriginal(
          event.value.fkIIdSheet,
          event.value.fkIIdColumn
        );
        break;
      case 'modify':
        this.isEditRelation = true;
        this.form.get('fkIIdSheetRelation')?.setValue(event.value.fkIIdSheet);
        this.form.get('fkIIdColumnRelation')?.setValue(event.value.fkIIdColumn);
        break;
      default:
        break;
    }
  }

  //Abre el modal para Añadir un festivo.
  openModal() {
    if (!this.isEdit) {
      this.modalTitle = this._translateService.instant(
        'CatalogSetting.AddColumn'
      );
    }

    this.modalDialog.open(this.AddColumnModal!, {
      width: '720px',
    });
  }

  dataListDropdownSheets() {
    this.listSheets = this.dataJson.Sheets;
  }

  getSettinCompositeCatalogSubscription() {
    this._settingCompositeCatalogSubscription =
      this._catalogService.currentCompositeSetting.subscribe((response) => {
        if (!(Object.keys(response).length === 0)) {
          this.dataJson = response;
        }
      });
  }

  getSheetByIdHistoryCatalog(idCatalog: number) {
    this._catalogService
      .getSheetByIdHistoryCatalog(idCatalog)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.listSheetParent = response.result;
          }
        }
      });
  }

  getColumnByIdHistoryCatalog(idCatalog: number) {
    this._catalogService
      .getColumnByIdHistoryCatalog(idCatalog)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.dataColumnTable = response.result;
            this.originalColumnArray = response.result;
            if (this.dataJson.Columns !== undefined) {
              this.dataColumnTable = this.dataJson.Columns.concat(
                response.result
              );
              this.dataJson.Columns.includes(this.dataJson.Columns);
            } else {
              this.saveCurrentDataCatalogComposite();
            }
            this.listColumn = this.dataColumnTable;
            this.deleteDuplicateJSON();
            this.onDataTableJoin();
            this.onDataRelationTableJoin();
          }
        }
      });
  }

  getChildrenRelation(arrayChildren: any) {
    if (arrayChildren !== null) {
      this.dataChildrenRelation = arrayChildren;
      this.originalChildrenRelationArray = arrayChildren;
    }
    this.onDataRelationTableJoin();
  }

  getDatalistColumnFilter(type: string, value: number) {
    if (type === 'R') {
      this.listColumnRelation = this.dataColumnTable.filter(
        (a) => a.fkIIdSheet === value && a.id !== this.form.get('id')?.value
      );
      this.listColumnRelation = this.listColumnRelation.slice();
    } else {
      this.listColumn = this.dataColumnTable.filter(
        (a) => a.fkIIdSheet === value && a.id !== this.form.get('id')?.value
      );
      this.listColumn = this.listColumn.slice();
      this.form.get('fkIIdSheetParent')?.setValue(value);
    }
  }

  generateNewId(): number {
    return this.dataColumnTable.length + 1; // Genera un ID basado en el tamaño del array
  }

  saveColumn() {
    if (this.valid) {
      this.dataColumn = {
        id: this.generateNewId(),
        fkIIdSheet: this.form.get('fkIIdSheet')?.value,
        vName: this.form.get('vName')?.value,
        vNameColumn: this.form.get('vNameColumn')?.value,
        bIsDependent: this.form.get('bIsDependent')?.value,
        bIsRemoveDuplicated: this.form.get('bIsRemoveDuplicated')?.value,
        fkIIdSheetParent: this.form.get('fkIIdSheetParent')?.value,
        fkIIdColumn: this.form.get('fkIIdColumn')?.value,
        children: this.form.get('children')?.value,
        bIsChildren: this.form.get('bIsChildren')?.value,
      };

      this.form.get('bIsChildren')?.enable();
    }
  }

  /// Add information table relation
  addRelation() {
    let objectRelation: any = {
      fkIIdColumn: 0,
      fkIIdSheet: 0,
    };
    if (this.isEditRelation) {
      objectRelation = this.dataChildrenRelation.find(
        (b: { fkIIdSheet: EventListener }) =>
          b.fkIIdSheet === this.form.get('fkIIdSheetRelation')?.value
      );
      this.deleteRelationArrayOriginal(
        objectRelation.fkIIdSheet,
        objectRelation.fkIIdColumn
      );
      objectRelation.fkIIdColumn = this.form.get('fkIIdColumnRelation')?.value;
      if (this.dataColumn.id === undefined) this.updateColumn();
    } else {
      objectRelation.fkIIdColumn = this.form.get('fkIIdColumnRelation')?.value;
      objectRelation.fkIIdSheet = this.form.get('fkIIdSheetRelation')?.value;
    }

    this.dataChildrenRelation.push(objectRelation);
    this.dataChildrenRelation = this.dataChildrenRelation.slice();
    this.originalChildrenRelationArray.push(objectRelation);
    this.clearRelation();
    this.onDataRelationTableJoin();
  }

  save() {
    if (this.dataColumn.id > 0) {
      this.dataColumn.children = this.dataChildrenRelation;
      if (this.dataChildrenRelation.length > 0)
        this.dataColumn.bIsChildren = this.form.get('bIsChildren')?.value;

      let newOptionField: any = this.dataColumn;
      if (this.isEdit)
        this.deleteColumnArrayOriginal(this.form.get('id')?.value);

      if (this.idCatalog > 0) {
        this.dataColumnTable.push(newOptionField);
        this.dataColumnTable = this.dataColumnTable.slice();

        this.originalColumnArray.push(newOptionField);
        this.originalColumnArray = this.originalColumnArray.slice();
      } else {
        this.dataColumnTable.push(newOptionField);
        this.dataColumnTable = this.dataColumnTable.slice();
        this.originalColumnArray.push(newOptionField);
      }
      this.form.get('bIsChildren')?.enable();
      this.saveCurrentDataCatalogComposite();
    }
    this.closeModal(false);
  }

  updateColumn() {
    if (this.valid) {
      this.dataColumn = this.dataJson.Columns.find(
        (b: { id: EventListener }) => b.id === this.form.get('id')?.value
      );

      this.dataColumn.fkIIdSheet = this.form.get('fkIIdSheet')?.value;
      this.dataColumn.vName = this.form.get('vName')?.value;
      this.dataColumn.vNameColumn = this.form.get('vNameColumn')?.value;
      this.dataColumn.bIsDependent = this.form.get('bIsDependent')?.value;
      this.dataColumn.bIsRemoveDuplicated = this.form.get(
        'bIsRemoveDuplicated'
      )?.value;
      this.dataColumn.fkIIdSheetParent =
        this.form.get('fkIIdSheetParent')?.value;
      this.dataColumn.fkIIdColumn = this.form.get('fkIIdColumn')?.value;
      this.dataColumn.children = this.dataChildrenRelation;
      this.dataColumn.bIsChildren = this.form.get('bIsChildren')?.value;
    }
  }

  onDataTableJoin() {
    this.dataColumnTable = this.dataColumnTable
      .flatMap((a) =>
        this.dataJson.Sheets.filter(
          (sheet: { id: any }) => sheet.id === a.fkIIdSheet
        ).map((sheet: any) => ({
          nameSheetparent: sheet.nameSheet,
          bIsChildren: a.bIsChildren,
          bIsDependent: a.bIsDependent,
          bIsRemoveDuplicated: a.bIsRemoveDuplicated,
          children: a.children,
          fkIIdColumn: a.fkIIdColumn,
          fkIIdSheet: a.fkIIdSheet,
          fkIIdSheetParent: a.fkIIdSheetParent,
          id: a.id,
          vName: a.vName,
          vNameColumn: a.vNameColumn,
        }))
      )
      .sort((a, b) => a.id - b.id);
  }

  onDataRelationTableJoin() {
    this.dataChildrenRelation = this.dataChildrenRelation.flatMap((a) =>
      this.dataJson.Columns.filter(
        (column: { fkIIdSheet: any; id: any }) =>
          column.id === a.fkIIdColumn && column.fkIIdSheet === a.fkIIdSheet
      ).map((column: any) => ({
        nameSheetChildren: column.nameSheetparent,
        nameColumnRelation: column.vName,
        fkIIdSheet: a.fkIIdSheet,
        fkIIdColumn: a.fkIIdColumn,
      }))
    );
  }

  saveCurrentDataCatalogComposite() {
    let payload: any = {
      Columns: this.dataColumnTable,
    };
    Object.assign(this.dataJson, payload);
    this._catalogService.setCurrentEditNewCompositeSetting(this.dataJson);

    /// Update data temporal of table with inner join between arrays
    this.onDataTableJoin();
  }

  next() {
    let dataReturn = {
      value: 4,
      id: 0,
    };
    this.setActiveTabCatalog.emit(dataReturn);
  }

  deleteColumnArrayOriginal(id: number) {
    this.originalColumnArray = this.originalColumnArray.filter(
      (objeto) => objeto.id !== id
    );

    const index = this.dataColumnTable.findIndex((item) => item.id === id);

    this.dataColumnTable.splice(index, 1);
    this.dataColumnTable = this.dataColumnTable.slice();

    if (!this.isEdit) {
      // Reordenar los 'order' del array
      this.dataColumnTable.forEach((column, idx) => {
        column.id = idx + 1; // El nuevo 'order' será el índice + 1
      });
    }
    this.saveCurrentDataCatalogComposite();
  }

  deleteRelationArrayOriginal(idSheet: number, idColumn: number) {
    this.originalChildrenRelationArray =
      this.originalChildrenRelationArray.filter(
        (objeto) =>
          objeto.fkIIdSheet !== idSheet && objeto.fkIIdColumn !== idColumn
      );

    const index = this.dataChildrenRelation.findIndex(
      (item) =>
        item.fkIIdSheetRelation === idSheet &&
        item.fkIIdColumnRelation === idColumn
    );

    this.dataChildrenRelation.splice(index, 1);
    this.dataChildrenRelation = this.dataChildrenRelation.slice();
  }

  deleteDuplicateJSON() {
    this.dataColumnTable = this.dataColumnTable.filter(
      (item: { id: any }, index: any, self: any[]) =>
        index === self.findIndex((t) => t.id === item.id)
    );

    this.saveCurrentDataCatalogComposite();
  }

  //Cambia el valor del campo de la tabla (Condiciones) según sea el valor de llave bIsDependent
  parserConditionsDependentTable(value: any) {
    if (value.bIsDependent) {
      return 'Si';
    } else {
      return 'No';
    }
  }

  //Cambia el valor del campo de la tabla (Condiciones) según sea el valor de llave bIsChildren
  parserConditionsChildrenTable(value: any) {
    if (value.bIsChildren) {
      return this._translateService.instant(
        'CatalogSetting.MessagesCatalogo.Yes'
      );
    } else {
      return this._translateService.instant(
        'CatalogSetting.MessagesCatalogo.No'
      );
    }
  }

  clearRelation() {
    this.form.get('fkIIdColumnRelation')?.setValue(0);
    this.form.get('fkIIdSheetRelation')?.setValue(0);
    this.isEditRelation = false;
  }

  goBack() {
    this.form.reset();
    let dataReturn = {
      value: 2,
      id: 0,
    };
    this.setActiveTabCatalog.emit(dataReturn);
  }

  goBackMain() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant('Out'),
        this._translateService.instant('CatalogSetting.MessagesCatalogo.UnsavedData'),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._location.back();
          this.form.reset();
        }
      });
  }

  closeModal(event: boolean) {
    this.isEdit = false;
    this.isEditRelation = false;
    this.form.reset();
    this.modalDialog.closeAll();
  }

  ngOnDestroy(): void {
    this._settingCompositeCatalogSubscription?.unsubscribe();
  }
}
