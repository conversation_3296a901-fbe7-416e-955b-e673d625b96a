import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, firstValueFrom, of } from 'rxjs';
import {
  ClientModel,
  CompanyClient,
  ICustomerForService,
  PersonClient,
} from 'src/app/shared/models/client';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SelectModel } from 'src/app/shared/models/select';
import { ClientService } from 'src/app/shared/services/client/client.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import * as XLSX from 'xlsx';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';

@Component({
  selector: 'app-modal-bulk-load',
  standalone: true,
  imports: [CommonModule, MatIconModule,MatCardModule,TranslateModule,ValidationInputFileDirective],
  templateUrl: './modal-bulk-load.component.html',
  styleUrls: ['./modal-bulk-load.component.scss'],
})
export class ModalBulkLoadComponent implements OnInit {
  items = ['Item 1', 'Item 2', 'Item 3', 'Item 4'];
  persons: PersonClient[] = [];
  companies: CompanyClient[] = [];
  errorMessage: string | null = '';
  isDragOver = false;
  fileName: string | null = null;
  fileCharged: File | null = null;
  costumerPersonForService: ICustomerForService[] = [];
  costumerCompanyForService: ClientModel[] = [];
  documentsTypes: SelectModel[] = [];

  constructor(
    private _translateService: TranslateService,
    public dialogRef: MatDialogRef<ModalBulkLoadComponent>,
    private _messageService: MessageService,
    private _parametersService: ParametersService,
    private _transactionService: TransactionService,
    private _router: Router
  ) {}
  ngOnInit(): void {
    this.getListDocumentType();
  }

  /**
   * Handles the event when files are dropped onto the drop zone.
   * @param {any} event - The drop event containing the files to be uploaded.
   */
  onFileDropped(event: any) {
    event.preventDefault();
    event.stopPropagation();
    if (event.dataTransfer && event.dataTransfer.files) {
      const files = event.dataTransfer.files;
      this.uploadFiles(files);
    }
  }

  /**
   * Handles the event when a file is dragged over the drop zone.
   * @param {DragEvent} event - The drag event to prevent default behavior and mark the drop zone as active.
   */
  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  /**
   * Handles the event when a dragged file leaves the drop zone.
   * @param {DragEvent} event - The drag event to prevent default behavior and mark the drop zone as inactive.
   */
  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  /**
   * Handles the event when files are selected through the file input.
   * @param {Event} event - The change event from the file input containing the selected files.
   */
  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files?.length > 0) {
      this.uploadFiles(input.files);
    }
  }

  /**
   * Uploads the selected files.
   * @param {FileList} files - The list of files to be uploaded.
   */
  private uploadFiles(files: FileList) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      this.fileName = file.name;
      if (!this.checkAcceptedType(file.type)) {
        this.errorMessage = this._translateService.instant(
          'Client.InvalidFileType'
        );
      } else {
        this.fileCharged = file;
        this.errorMessage = null;
      }
    }
  }

  /**
   * Checks if the file type is accepted.
   * @param {string} type - The MIME type of the file.
   * @returns {boolean} - Returns true if the file type is accepted, otherwise false.
   */
  checkAcceptedType(type: string): boolean {
    return (
      type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
  }

  /**
   * Initiates the process to read the Excel file when the file input is clicked.
   */
  processFileClick() {
    this.readExcelFile(this.fileCharged);
  }

  /**
   * Processes the given file by reading its content.
   * @param {File} file - The file to be processed.
   */
  processFile(file: File) {
    this.readExcelFile(file);
  }

  /**
   * Reads and processes an Excel file.
   * @param {File | null} file - The file to read, which can be a File object or null.
   */
  private readExcelFile(file: File | null) {
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e: any) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const usersSheet = workbook.Sheets['Persona'];
      const companiesSheet = workbook.Sheets['Empresa'];
      const paramatersSheet = workbook.Sheets['No tocar'];
      if (!usersSheet || !companiesSheet || !paramatersSheet){
        this.closeWithError('Client.SystemTemplateOnly');
        return;
      }
      if (usersSheet) {
        const jsonDataUsers = XLSX.utils.sheet_to_json(usersSheet, {
          header: 1,
        });
        await this.processUsersData(
          jsonDataUsers.filter((r: any) => r.length > 0)
        );
      }

      if (companiesSheet) {
        const jsonDataCompanies = XLSX.utils.sheet_to_json(companiesSheet, {
          header: 1,
        });
        await this.processCompaniesData(
          jsonDataCompanies.filter((r: any) => r.length > 0)
        );
      }

      if (this.costumerPersonForService.length > 0) {
        await this.createCostumer(this.costumerPersonForService);
      }

      if (this.costumerCompanyForService.length > 0) {
        await this.createCompany(this.costumerCompanyForService);
      }
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    };
    reader.readAsArrayBuffer(file);
  }

  /**
   * Processes an array of user data, performing validations and adding valid entries to the persons list.
   * @param {any[]} data - The array of user data, where each item represents a row of data.
   */
  private async processUsersData(data: any[]): Promise<void> {
    this.persons = [];
    data.forEach((row: any[], index: number) => {
      if (index > 0 && row.length) {
        const [
          tipoDocumento,
          numeroDocumento,
          primerNombre,
          segundoNombre,
          primerApellido,
          segundoApellido,
          correoElectronico,
          celular,
          tipoCliente,
        ] = row;
        
        // Create and log an error message based on available user data
        const message = this.createErrorMessage(
          primerNombre,
          numeroDocumento,
          correoElectronico
        );
        if (!message) {
          this.close();
          return this._messageService.messageError(
            this._translateService.instant(
              'Client.YouMustRegisterAllMandatoryFieldsMarkedWithAsterisk'
            )
          );
        }

        // Validate the document type
        if (!this.isValidDocumentType(tipoDocumento)) {
          this.closeWithError('Client.TypeDocumentIsRequired', message);
          return;
        }
        
        // Validate the document number
        if (!this.isValidDocumentNumberHyphen(numeroDocumento)) {
          this.closeWithError(
            'Client.TheDocumentNumberMustBeNumerical',
            message
          );
          return;
        }

        // Validate the document number
        if (!this.isValidDocumentNumber(numeroDocumento)) {
          this.closeWithError('Client.DocumentNumberLengthIsInvalid', message);
          return;
        }

        // Validate the first name
        if (
          !this.isValidName(primerNombre, 'Client.FirstNameLengthIsInvalid')
        ) {
          this.closeWithError('Client.FirstNameIsRequired', message);
          return;
        }

        // Validate the surname
        if (
          !this.isValidName(primerApellido, 'Client.SurnameLengthIsInvalid')
        ) {
          this.closeWithError('Client.SurnameIsRequired', message);
          return;
        }

        // Validate the email
        if (!this.isValidEmailField(correoElectronico)) {
          this.closeWithError('Client.EmailIsRequired', message);
          return;
        }

        // Validate the phone number
        if (!this.isValidPhoneNumber(celular)) {
          this.closeWithError('Client.PhoneNumberIsRequired', message);
          return;
        }

        // Validate the client type
        if (!this.isValidClientType(tipoCliente)) {
          this.closeWithError('Client.TypeClientIsRequired', message);
          return;
        }

        var idTipoDocumento =
          this.assignItemTypeIdWithDescription(tipoDocumento);

        if (!this.isNumeric(idTipoDocumento.toString())) {
          this.closeWithError('Client.TypeDocumentIsRequired', message);
          return;
        }

        // Create and add the person object
        this.addPerson(
          idTipoDocumento,
          numeroDocumento,
          primerNombre,
          segundoNombre,
          primerApellido,
          segundoApellido,
          correoElectronico,
          celular,
          tipoCliente
        );
      }
    });

    if (this.persons.length > 0) {
      this.mapUsersTypePersonToTypeService();
    }
  }

  /**
   * Creates a customer by sending the provided data to the backend service.
   * Displays success or error messages based on the response.
   *
   * @param data - Array of customer data to be sent for creation.
   */
  async createCostumer(data: any[]): Promise<void> {
    try {
      const resp: ResponseGlobalModel | any = await firstValueFrom(
        this._transactionService.createPeopleDinamically(data).pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
      );

      if (!resp.error && !(Array.isArray(resp) && resp.length === 0)) {
        this.costumerPersonForService = [];
        this._messageService.messageSuccess(
          this._translateService.instant(
            'Client.CustomersCreatedCorrectly'
          ),
          ''
        );
      } else {
        this.costumerPersonForService = [];
        this._messageService.messageError(
          this._translateService.instant('ThereWasAError') + ': ' + resp.message
        );
      }
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * Creates a company by sending the provided data to the backend service.
   * Displays success or error messages based on the response.
   *
   * @param data - Array of company data to be sent for creation.
   */
  async createCompany(data: any[]): Promise<void> {
    try {
      const resp: ResponseGlobalModel | any = await firstValueFrom(
        this._transactionService.createCompanyDinamically(data).pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
      );
  
      if (!resp.error && !(Array.isArray(resp) && resp.length === 0)) {
        this.costumerCompanyForService = [];
        this._messageService.messageSuccess(
          this._translateService.instant('Clients.Client.CompanyCreatedCorrectly'),
          ''
        );
      } else {
        this.costumerCompanyForService = [];
        this._messageService.messageError(
          this._translateService.instant('ThereWasAError') + ': ' + resp.message
        );
      }
    } catch (error) {
      console.error(error);
    }
  
  }

  /**
   * Creates an error message based on available user data.
   * @param {string} primerNombre - The first name of the user.
   * @param {string} numeroDocumento - The document number of the user.
   * @param {string} correoElectronico - The email of the user.
   * @returns {string} - The constructed error message.
   */
  private createErrorMessage(
    primerNombre: string,
    numeroDocumento: string,
    correoElectronico: string
  ): string {
    if (primerNombre) {
      return (
        this._translateService.instant('Client.TheFirstName') +
        ': ' +
        primerNombre
      );
    } else if (numeroDocumento) {
      return (
        this._translateService.instant('Client.TheDocumentNumber') +
        ' ' +
        numeroDocumento
      );
    } else if (correoElectronico) {
      return (
        this._translateService.instant('Client.TheEmail') +
        ' ' +
        correoElectronico
      );
    } else {
      return '';
    }
  }

  /**
   * Validates the document type.
   * @param {string} tipoDocumento - The document type of the user.
   * @returns {boolean} - True if the document type is valid, false otherwise.
   */
  private isValidDocumentType(tipoDocumento: string): boolean {
    const validDocumentTypes = [
      'céduladeciudadanía',
      'pasaporte',
      'céduladeextranjería',
    ];
    let isValidDocumentType: boolean =  validDocumentTypes.some(str => str === tipoDocumento.toLowerCase().replace(/\s+/g, ''));
    return tipoDocumento ? isValidDocumentType : false;
  }

  /**
   * Validates the document type company.
   * @param {string} tipoDocumento - The document type of the user.
   * @returns {boolean} - True if the document type is valid, false otherwise.
   */
  private isValidDocumentTypeCompany(tipoDocumento: string): boolean {
    const validDocumentTypes = ['nit'];
    return tipoDocumento
      ? validDocumentTypes.includes(
          tipoDocumento.toLowerCase().replace(/\s+/g, '')
        )
      : false;
  }

  /**
   * Validates the document number.
   * @param {string} numeroDocumento - The document number of the user.
   * @returns {boolean} - True if the document number is valid, false otherwise.
   */
  private isValidDocumentNumber(numeroDocumento: string): boolean {
    return numeroDocumento
      ? numeroDocumento.toString().length >= 5 &&
          numeroDocumento.toString().length < 12
      : false;
  }

  /**
   * Validates the name field.
   * @param {string} name - The name to validate.
   * @param {string} errorMessageKey - The key for the error message if the name is invalid.
   * @returns {boolean} - True if the name is valid, false otherwise.
   */
  private isValidName(name: string, errorMessageKey: string): boolean {
    if (name) {
      if (name.toString().length < 3 || name.toString().length >= 20) {
        this.fileCharged = null;
        this.fileName = null;
        this.close();
        this._messageService.messageError(
          this._translateService.instant(errorMessageKey)
        );
      }
    } else {
      this.fileCharged = null;
      this.fileName = null;
      return false;
    }
    return true;
  }

  /**
   * Validates the email field.
   * @param {string} correoElectronico - The email of the user.
   * @returns {boolean} - True if the email is valid, false otherwise.
   */
  private isValidEmailField(correoElectronico: string): boolean {
    if (correoElectronico) {
      if (
        correoElectronico.toString().length < 7 ||
        correoElectronico.toString().length >= 30
      ) {
        this.fileCharged = null;
        this.fileName = null;
        this.close();
        this._messageService.messageError(
          this._translateService.instant('Client.EmailLengthIsInvalid')
        );
      }

      if (!this.isValidEmail(correoElectronico)) {
        this.close();
        this._messageService.messageError(
          this._translateService.instant('Client.InvalidEmailFormat')
        );
      }
    } else {
      this.fileCharged = null;
      this.fileName = null;
      return false;
    }
    return true;
  }

  /**
   * Validates the phone number.
   * @param {string} celular - The phone number of the user.
   * @returns {boolean} - True if the phone number is valid, false otherwise.
   */
  private isValidPhoneNumber(celular: string): boolean {
    if (celular) {
      if (celular.toString().length < 5 || celular.toString().length >= 14) {
        this.fileCharged = null;
        this.fileName = null;
        this._messageService.messageError(
          this._translateService.instant('Client.PhoneNumberLengthIsInvalid')
        );
        this.close();
      }
    } else {
      this.fileCharged = null;
      this.fileName = null;
      return false;
    }
    return true;
  }

  /**
   * Validates the client type.
   * @param {string} tipoCliente - The client type.
   * @returns {boolean} - True if the client type is valid, false otherwise.
   */
  private isValidClientType(tipoCliente: string): boolean {
    const validClientTypes = ['abierto', 'cerrado'];
    if (tipoCliente) {
      if (!validClientTypes.includes(tipoCliente.toLowerCase())) {
        this.fileCharged = null;
        this.fileName = null;
        this._messageService.messageError(
          this._translateService.instant('Client.TypeClientIsInvalid')
        );
        this.close();
      }
    } else {
      this.fileCharged = null;
      this.fileName = null;
      return false;
    }
    return true;
  }

  /**
   * Closes the current process with an error message.
   * @param {string} errorKey - The key for the error message.
   * @param {string} [message] - Additional message to include in the error.
   */
  private closeWithError(errorKey: string, message?: string): void {
    this.fileCharged = null;
    this.fileName = null;

    this._messageService.messageError(
      this._translateService.instant(errorKey) +
        ' ' +
        (message
          ? this._translateService.instant('Client.InUserRegistrationWith') +
            ' ' +
            message
          : '')
    );
    this.close();
  }

  /**
   * Adds a person to the persons array.
   * @param {string} tipoDocumento - The document type of the user.
   * @param {string} numeroDocumento - The document number of the user.
   * @param {string} primerNombre - The first name of the user.
   * @param {string} [segundoNombre] - The second name of the user.
   * @param {string} primerApellido - The surname of the user.
   * @param {string} [segundoApellido] - The second surname of the user.
   * @param {string} correoElectronico - The email of the user.
   * @param {string} celular - The phone number of the user.
   * @param {string} tipoCliente - The client type.
   */
  private addPerson(
    idTipoDocumento: number,
    numeroDocumento: string,
    primerNombre: string,
    segundoNombre: string | null,
    primerApellido: string,
    segundoApellido: string | null,
    correoElectronico: string,
    celular: string,
    tipoCliente: string
  ): void {
    const personObj: PersonClient = {
      idTipoDocumento,
      numeroDocumento,
      primerNombre,
      segundoNombre: segundoNombre ?? null,
      primerApellido,
      segundoApellido: segundoApellido ?? null,
      correoElectronico,
      celular,
      tipoCliente,
    };
    this.persons.push(personObj);
  }

  isValidEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }

  private async processCompaniesData(data: any[]): Promise<void> {
    this.companies = [];

    data.forEach((row: any[], index: number) => {
      if (index > 0 && row.length) {
        const [
          tipoDocumento,
          numeroDocumento,
          digitoVerificacion,
          razonSocial,
          correoElectronico,
          celularContacto,
          representanteLegal,
          tipoDocumento2,
          numeroDocumento2,
          primerNombre,
          segundoNombre,
          primerApellido,
          segundoApellido,
          correoElectronico2,
          celular,
          tipoCliente,
        ] = row;

        //validations
        // Create and log an error message based on available user data
        const message = this.createErrorMessage(
          primerNombre,
          numeroDocumento,
          correoElectronico
        );
        if (!message) {
          this.close();
          return this._messageService.messageError(
            this._translateService.instant(
              'Client.YouMustRegisterAllMandatoryFieldsMarkedWithAsterisk'
            )
          );
        }

        // Validate the document type
        if (!this.isValidDocumentTypeCompany(tipoDocumento)) {
          this.closeWithError('Client.TypeDocumentIsRequired', message);
          return;
        }

        // Validate the document number
        if (!this.isValidDocumentNumber(numeroDocumento)) {
          this.closeWithError('Client.DocumentNumberLengthIsInvalid', message);
          return;
        }

        // Validate the client type
        if (!this.isValidClientType(tipoCliente)) {
          this.closeWithError('Client.TypeClientIsRequired', message);
          return;
        }

        var idTipoDocumento =
          this.assignItemTypeIdWithDescription(tipoDocumento);

        if (!this.isNumeric(idTipoDocumento.toString())) {
          this.closeWithError('Client.TypeDocumentIsRequired', message);
          return;
        }

        var idTipoDocumento2 = null;
        if (tipoDocumento2) {
          idTipoDocumento2 =
            this.assignItemTypeIdWithDescription(tipoDocumento);

          if (!this.isNumeric(idTipoDocumento2.toString())) {
            this.closeWithError('Client.TypeDocumentIsRequired', message);
            return;
          }
        }

        this.companies.push({
          idTipoDocumento,
          tipoDocumento,
          numeroDocumento,
          digitoVerificacion: digitoVerificacion ?? null,
          razonSocial,
          correoElectronico: correoElectronico ?? null,
          celularContacto: celularContacto ?? null,
          representanteLegal: representanteLegal ?? 'Representante legal',
          idTipoDocumento2: idTipoDocumento2 ?? null,
          tipoDocumento2: tipoDocumento2 ?? null,
          numeroDocumento2: numeroDocumento2 ?? null,
          primerNombre: primerNombre ?? null,
          segundoNombre: segundoNombre ?? null,
          primerApellido: primerApellido ?? null,
          segundoApellido: segundoApellido ?? null,
          correoElectronico2: correoElectronico2 ?? null,
          celular: celular ?? null,
          tipoCliente,
        });
      }
    });

    if (this.companies.length > 0) {
      this.mapUsersTypeCompanyToTypeService();
    }
  }

  CreateUsersTypePerson() {
    if (this.persons.length > 0) {
    }
  }

  /**
   * Maps the user type person data to the service type format.
   * Iterates through the `persons` array and transforms each entry to the format required by the service.
   * The transformed data is pushed to the `costumerPersonForService` array.
   */
  mapUsersTypePersonToTypeService(): void {
    this.persons.forEach((res) => {
      // Transform each person entry to the required service format
      this.costumerPersonForService.push({
        IIdDocumentType: res.idTipoDocumento,
        VDocumentNumber: res.numeroDocumento,
        VFirstName: res.primerNombre,
        VSecondName: res.segundoNombre,
        VSurname: res.primerApellido,
        VSecondSurname: res.segundoApellido,
        VEmailUser: res.correoElectronico,
        VCellPhone: res.celular,
        BIsRestricted: res.tipoCliente == 'Cerrado' ? 1 : 0,
        VTypePerson: 1,
        BActive: 1,
        DBirthDate: null,
        PkIIdCustomer: null,
        VFullName: null,
        VGender: null,
        VPhone: null,
      });
    });
  }

  mapUsersTypeCompanyToTypeService(): void {
    this.companies.forEach((res) => {
      // Transform each company entry to the required service format
      this.costumerCompanyForService.push({
        IIdDocumentType: res.idTipoDocumento,
        VDocumentNumber: res.numeroDocumento,
        VFirstName: res.razonSocial,
        VSecondName: null,
        VSurname: null,
        VSecondSurname: null,
        VEmailUser: res.correoElectronico,
        VCellPhone: res.celularContacto,
        BIsRestricted: res.tipoCliente == 'Cerrado' ? 1 : 0,
        VTypePerson: 2,
        BActive: 1,
        DBirthDate: null,
        PkIIdCustomer: 0,
        VFullName: null,
        VGender: null,
        VPhone: null,
        contactCustomer: {
          pkIIdTypePerson: 0,
          vEmailUser: res.correoElectronico2,
          bActive: 1,
          vPhone: res.celular,
          fkIIdCustomer: 0,
          iIdDocumentType: res.idTipoDocumento2,
          vDocumentNumber: res.numeroDocumento2,
          vFirstName: res.primerNombre,
          vSecondName: res.segundoNombre,
          vSurname: res.primerApellido,
          vSecondSurname: res.segundoApellido,
          vRole: null,
        },
      });
    });
  }

  assignItemTypeIdWithDescription(description: string): number {
    const document: any = this.documentsTypes.find(
      (doc) =>
        doc.name.toLowerCase().replace(/\s+/g, '') ===
        description.toLowerCase().replace(/\s+/g, '')
    );
    return document?.id;
  }

  /**
   * Retrieves the list of global document types from the parameters service.
   */
  getListDocumentType() {
    this._parametersService.getListCatalogGlobalDocumentTypes().subscribe({
      next: (response) => {
        if (!response.error) {
          // Assign the response result to the documentsTypes array
          this.documentsTypes = response.result;
        }
      },
      error: (err) => {
        // Optionally handle the error case
        console.error('Error retrieving document types', err);
      },
    });
  }

  /**
   * Checks if a value is a number.
   * @param value - The value to check.
   * @returns True if the value is a number, false otherwise.
   */
  isNumeric(value: string): boolean {
    return !isNaN(Number(value));
  }

  /**
   * Initiates the download of a template file.
   * Creates a temporary link element to trigger the download of the file.
   */
  downloadTemplate(): void {
    const link = document.createElement('a');
    link.href = 'assets/pdfs/Plantilla_Carga_Masiva_Usuarios.xlsx';
    link.download = 'Plantilla_Carga_Masiva_Usuarios.xlsx';
    link.click();
  }

  /**
   * Closes the current dialog reference.
   * This method is typically used to close a modal or dialog box.
   */
  close(): void {
    this.dialogRef.close();
  }

  /**
   * Checks if a value is valid for document number.
   * It should contain only numbers and hyphens.
   * @param value - The value to check.
   * @returns True if the value is valid, false otherwise.
   */
  isValidDocumentNumberHyphen(value: string): boolean {
    const pattern = /^[0-9-]*$/; // Permite solo números y guiones medios
    return pattern.test(value);
  }

}
