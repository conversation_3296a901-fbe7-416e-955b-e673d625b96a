<form [formGroup]="form" (submit)="next()">
  <div class="row mt-5">
    <div class="col-md-12 col-sm-12">
       <h3>{{ 'CatalogSetting.CatalogData' | translate }}</h3>
    </div>

    <div class="col-12 col-md-12">
      <mat-form-field class="w-100 mb-5" appearance="fill">
        <mat-label>
          {{ 'CatalogSetting.CatalogName' | translate }}
        </mat-label>
        <input matInput type="text" formControlName="vName" />
        <mat-error
          *ngIf="_utilsSvc.isControlHasError(form, 'vName', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="cont-btns">
      <button class="mx-3" mat-raised-button type="button" (click)="goBack()">
        {{ 'CatalogSetting.ExitCatalogs' | translate }}
      </button>

      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="next()"
      >
      {{ 'CatalogSetting.NextCatalog' | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </div>

  </div>
</form>
