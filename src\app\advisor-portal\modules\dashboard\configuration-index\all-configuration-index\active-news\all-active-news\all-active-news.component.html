<div class="row mt-2">
  <div class="row mb-2">
    <h4 class="col-md-12">{{ "News.ActiveNews" | translate }}</h4>
  </div>

  <form [formGroup]="formConfigurations">
    <div class="row">
        <mat-form-field appearance="outline" class="col-md-6 col-sm-12 mb-2">
            <mat-label>
              {{'News.Title' | translate}}
            </mat-label>
            <input matInput formControlName="vTitleLanding" PreventionSqlInjector/>
            <mat-error *ngIf="utilsSvc.isControlHasError(formConfigurations, 'vTitleLanding', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="col-md-6 col-sm-12 mb-2">
            <mat-label>
              {{'News.SubTitle' | translate}}
            </mat-label>
            <textarea matInput formControlName="vSubTitleLanding" PreventionSqlInjector cdkTextareaAutosize cdkAutosizeMinRows="1" cdkAutosizeMaxRows="10"></textarea>
            <mat-error *ngIf="utilsSvc.isControlHasError(formConfigurations, 'vSubTitleLanding', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
    </div>
  </form>

  <app-table [displayedColumns]="estructActiveNewsTable" [data]="activeNewsDataTable"
             (iconClick)="controller($event)"></app-table>
</div>
<div class="row">
  <div class="col">
    <button type="button" mat-raised-button color="primary" (click)="gotToCreate()">
      {{ "News.AddNews" | translate }}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
</div>

<ng-template #editActiveNewsModal>
  <app-modal2 [titleModal]="titelModal" [showCancelButton]="false" [showCancelButtonTOP]="true" [showTooltip]="true" [tooltipDescription]="'Tooltips.AddNewModalTitle' | translate" (closeModal)="eventCloseModal($event)">
    <ng-container body>
      <app-edit-active-news [newsId]="newsId"
                            [pkIIdBusinessByCountry]="idBusinessByCountry"
                            (getNewsByIdBusinessCountry)="getNewsByIdBusinessCountry(idBusinessByCountry)"
                            [objetActNews]="objetActNews"
                            [userType]="userType"></app-edit-active-news>
    </ng-container>
  </app-modal2>
</ng-template>
