<div class="modal-body d-flex justify-content-center flex-wrap">
  <form action="">
    <form [formGroup]="form">
      <!-- nombre grupo empresarial -->
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ "Business.BusinessGroupName" | translate }}
        </mat-label>
        <input
          matInput
          formControlName="v_BusinessGroupName"
          required
          type="text"
          PreventionSqlInjector
        />
        <mat-error
          *ngIf="
            _utilsService.isControlHasError(
              form,
              'v_BusinessGroupName',
              'required'
            )
          "
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <!-- razon Social -->
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ "Business.BusinessName" | translate }}
        </mat-label>
        <input matInput formControlName="v_BusinessName" required type="text" PreventionSqlInjector/>
        <mat-error
          *ngIf="
            _utilsService.isControlHasError(form, 'v_BusinessName', 'required')
          "
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <!-- nit -->
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ "Business.RegistryNumber" | translate }}
        </mat-label>
        <input matInput formControlName="v_Document" required type="text" PreventionSqlInjector/>
        <mat-error
          *ngIf="
            _utilsService.isControlHasError(form, 'v_Document', 'required')
          "
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <!-- grupo activo -->
      <div class="row">
        <mat-slide-toggle class="w-100 mb-2" formControlName="b_Active">
          {{ "Business.ActiveBusinessGroup" | translate }}
          <mat-icon class="click icon-tooltip" matTooltipPosition="right" matTooltip=" {{'Tooltips.ActiveBusinessGroup' | translate}}">help_outline</mat-icon>
        </mat-slide-toggle>
      </div>
    </form>
  </form>
</div>

<!-- save button -->
<div class="d-flex buttonAlign alignSubmit">
  <button
    type="button"
    mat-raised-button
    color="primary"
    (click)="btnSubmint_Click()"
  >
    {{ saveButtonText }}
  </button>
</div>
