<section>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>
</section>
<section>
    <mat-tab-group>
        <mat-tab class="mb-2"  label="{{ 'Reports.Utils.GenerateReports' | translate }}">
            <app-reports-generated></app-reports-generated>
        </mat-tab>
        <!-- <mat-tab class="mb-2" label="{{ 'Reports.Utils.SettingReports' | translate }}"> -->
        <mat-tab class="mb-2" label="Configurar reporte módulos">
            <app-report-configuration></app-report-configuration>
        </mat-tab>

        <mat-tab class="mb-2" label="Configurar reporte pólizas">
            <!-- <app-report-configuration-policy></app-report-configuration-policy> -->
            <app-report-configuration [tipoReporte]="1"></app-report-configuration>
        </mat-tab>
    </mat-tab-group>
</section>