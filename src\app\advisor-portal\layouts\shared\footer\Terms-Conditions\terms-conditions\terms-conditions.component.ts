import { Component, Input, NgModule, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-terms-conditions',
  standalone: true,
  imports:[
    CommonModule,
    TranslateModule

  ],
  templateUrl: './terms-conditions.component.html',
  styleUrls: ['./terms-conditions.component.scss'],

})
export class TermsConditionsComponent implements OnInit, OnDestroy{
  @Input() dataIdTermCondition: number =0;
  ngOnInit(): void {
  }
  ngOnDestroy(): void {
    
  }
}
