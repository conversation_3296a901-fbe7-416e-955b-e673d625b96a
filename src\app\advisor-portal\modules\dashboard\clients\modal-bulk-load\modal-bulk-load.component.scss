.close-button {
    position: absolute;
    right: 10px;
    top: 10px;
  }
/* file-upload.component.css */
.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.download-card {
  width: 100%;
  max-width: 500px;
  margin: 20px;
  padding: 20px;
  margin-top: 0px;
  border: 2px solid grey;
  cursor: pointer;
}

.upload-card {
  width: 100%;
  max-width: 500px;
  margin: 20px;
  margin-top: 0px;
  padding: 20px;
  text-align: center;
  border: 2px dashed #3f51b5;
  cursor: pointer;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-content mat-icon {
  font-size: 48px;
  color: #3f51b5;
}

.upload-content p {
  margin: 5px 0;
}

.file-details {
  margin: 10px 0;
}

.error {
  color: red;
}
