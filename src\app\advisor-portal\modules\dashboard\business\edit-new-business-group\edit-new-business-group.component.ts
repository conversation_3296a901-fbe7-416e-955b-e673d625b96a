import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatInputModule } from '@angular/material/input';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';


import { BusinessService } from 'src/app/shared/services/business/business.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MessageService } from 'src/app/shared/services/message/message.service';

import { BusinessGroupEditNewModel } from 'src/app/shared/models/business';
import { ResponseGlobalModel } from 'src/app/shared/models/response';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-edit-new-business-group',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSlideToggleModule,
    TranslateModule,
    MatButtonModule,
    PreventionSqlInjectorDirective,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './edit-new-business-group.component.html',
  styleUrls: ['./edit-new-business-group.component.scss']
})
export class EditNewBusinessGroupComponent implements OnInit{
  
  private businessGroupModel?: BusinessGroupEditNewModel;  
  
  saveButtonText: string = '';
  form: FormGroup = new FormGroup({});

  @Input() dataBusinessGroupId: number = 0;
  @Output() submitOut = new EventEmitter<ResponseGlobalModel>();

  constructor(
    private _businessService: BusinessService,
    private _fb: FormBuilder,

    public _utilsService: UtilsService,
    public _messageService: MessageService,
    public _translateService: TranslateService
  ){}

  ngOnInit(): void {
    if(this.dataBusinessGroupId == 0)
    {
      this.saveButtonText = this._translateService.instant("Create");
    }
    else
    {
      this.saveButtonText = this._translateService.instant("Save");
      this.getByBusinessGroupId(this.dataBusinessGroupId);   
    }

    this.initForm()
  }

  initForm() {
    this.form = this._fb.group({
      v_BusinessGroupName: ['', [Validators.required]],
      v_BusinessName: ['', [Validators.required]],
      v_Document: ['', [Validators.required]],
      b_Active: [true]
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  btnSubmint_Click()
  {
    if (this.saveButtonText == this._translateService.instant("Create"))
    {
      this.postBusinessGroup();
    }  
    else if (this.saveButtonText == this._translateService.instant("Save"))
    {
      this.putBusinessGruop();
    } 
  }

  getByBusinessGroupId(id : number)
  {
    if(id)
    {
      this.saveButtonText = this._translateService.instant("Save");
      this._businessService.getByBusinessGroupId(id).subscribe((resp) => {

        this.form = this._fb.group({
          pk_i_IdBusinessGroup: [resp.result.pk_i_IdBusinessGroup],
          v_BusinessGroupName: [resp.result.v_BusinessGroupName, [Validators.required]],
          v_BusinessName: [resp.result.v_BusinessName, [Validators.required]],
          v_Document: [resp.result.v_Document, [Validators.required]], 
          b_Active: [resp.result.b_Active, [Validators.required]]
        }); 

      });
    }
    else
    {
      this.saveButtonText = this._translateService.instant("Create");;
    }
  }

  postBusinessGroup() {
    
    if (this.valid) {

      this.businessGroupModel =
      {
        pk_i_IdBusinessGroup: 0,
        v_BusinessGroupName:this.form.value.v_BusinessGroupName,
        v_BusinessName:this.form.value.v_BusinessName,
        i_IdDocumentType:1,
        v_Document:this.form.value.v_Document,
        d_CreationDate:new Date(),
        b_Active:this.form.value.b_Active
      }
      this._businessService.postBusinessGroup(this.businessGroupModel).subscribe(
        (resp) => {    
          this.submitOut.emit(resp);    
        }
      );
    }     
    else
    {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm')
      );
    }
  }

  putBusinessGruop()
  {
    
    if (this.valid) {  

      this.businessGroupModel =
      {
        pk_i_IdBusinessGroup: this.form.value.pk_i_IdBusinessGroup,
        v_BusinessGroupName:this.form.value.v_BusinessGroupName,
        v_BusinessName:this.form.value.v_BusinessName,
        i_IdDocumentType:1,
        v_Document:this.form.value.v_Document,
        d_CreationDate:new Date(),
        b_Active:this.form.value.b_Active
      }   

      this._businessService.putBusinessGroup(this.businessGroupModel, this.businessGroupModel.pk_i_IdBusinessGroup).subscribe(
        (resp) => {
          this.submitOut.emit(resp);
        }
      );
    }
    else
    {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm')
      );
    }
  }

}
