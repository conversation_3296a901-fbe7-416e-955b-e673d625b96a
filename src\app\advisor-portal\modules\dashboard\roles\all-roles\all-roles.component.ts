import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';

import { EditNewRoleComponent } from '../edit-new-role/edit-new-role.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { ReplicateRoleComponent } from '../replicate-role/replicate-role.component';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';

import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RolesListComponent } from '../advisor-portal-roles/advisor-portal-roles.component';
import { MatTabsModule } from '@angular/material/tabs';


@Component({
  selector: 'app-all-roles',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    RolesListComponent,
    TableComponent,
    EditNewRoleComponent,
    ReplicateRoleComponent,
    ModalComponent,
    RouterModule,
    MatIconModule,
    TranslateModule,
    MatButtonModule,
    MatTooltipModule,
    MatTabsModule
  ],
  templateUrl: './all-roles.component.html',
  styleUrls: []
})

export class AllRolesComponent {

  
  defaultTabIndex: number = 0;
  activeTab: number = 0;
  onTabChanged(index: number) {
    this.activeTab = index;
  }


}
