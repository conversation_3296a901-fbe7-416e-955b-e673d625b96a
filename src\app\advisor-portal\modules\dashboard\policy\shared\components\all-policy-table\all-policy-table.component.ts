import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { PageEvent } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SelectModel } from 'src/app/shared/models/select';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { PolicyModel, RequesFilterPolicyTableModel } from '../../models';

@Component({
  selector: 'app-all-policy-table',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    Modal2Component,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
  ],
  templateUrl: './all-policy-table.component.html',
  styleUrls: ['./all-policy-table.component.scss'],
})
export class AllPolicyTableComponent {
  //Variables para compartir información entre componentes.
  @Input() estructTable: BodyTableModel[] = [];
  @Input() idtypePolicy: number = 0;
  @Output() actionTable = new EventEmitter<PolicyModel>();

  //Variables relacionadas con los modales.
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;

  //Variables para filtrar la data de la tabla.
  keyword: string = '';

  formValid: boolean = false;
  idBusinessByCountry: number = 0;
  idCountry: number = 0;

  //Variables relacioandas con la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;
  currentPosition: number = 0;
  dataTablePolicy: PolicyModel[] = [];

  //variable formulario.
  formFilter: FormGroup = new FormGroup({});
  listSelectedStates: number[] = [];
  listSelectedPolicyType: number[] = [];
  productList: SelectModel[] = [];
  insuranceList: SelectModel[] = [];
  policyStatus: SelectModel[] = [];
  endorsementList = [{ value: 'Sí' }, { value: 'No' }];
  requestFilterPolicy: RequesFilterPolicyTableModel = {
    idBusinessCountry: 0,
    endorsement: '',
    idInsurance: 0,
    keyword: '',
    idProduct: 0,
    statePolicyId: 0,
    order: 'desc',
    page: 0,
    pageSize: 5,
    idtypePolicy: 0,
  };

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    public matDialog: MatDialog,
    private _transactionService: TransactionService,
    private _customeRouter: CustomRouterService,
    private _activatedRoute: ActivatedRoute
  ) {
    this.getIdBusinessByCountry();
  }

  ngOnInit(): void {
    this.requestFilterPolicy.idtypePolicy = this.idtypePolicy;
    this.filterGeneralPolicyData(this.requestFilterPolicy);
  }

  //Obtiene el idBusinessByCountry por medio d ela url.
  getIdBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
        this.requestFilterPolicy.idBusinessCountry = this.idBusinessByCountry;
      }
    });
  }

  //Función que se ejecuta al dar enter o click en el icono de busqueda, del input buscar.
  search(keyword: string) {
    this.requestFilterPolicy.keyword = keyword;
    this.requestFilterPolicy.page = 0;
    this.filterGeneralPolicyData(this.requestFilterPolicy);
  }

  //Función que abre el modal para filtrar la data.
  openFilterDialog() {
    this.initFilterForm();
    this.getProductByPolicies();
    this.getInsuranceByPolicies();
    this.getStatePolicies();
    this.matDialog.open(this.filtersModal!, {
      width: '45vw',
      maxHeight: '90vh',
    });
    this.formFilter
      .get('endorsement')
      ?.setValue(this.requestFilterPolicy.endorsement);
    this.formFilter
      .get('statePolicyId')
      ?.setValue(this.requestFilterPolicy.statePolicyId);
  }

  //Delcaración del formulario formFilter.
  initFilterForm() {
    this.formFilter = this._fb.group({
      idProduct: this.requestFilterPolicy.idProduct,
      idInsurance: this.requestFilterPolicy.idInsurance,
      endorsement: '',
      statePolicyId: 0,
      idtypePolicy: this.idtypePolicy,
      idBusinessCountry: this.idBusinessByCountry,
      page: this.pageIndex,
      pageSize: 5,
      order: 'desc',
    });
  }

  //Controlador de las acciones configuradas en la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'manage':
        this.actionTable.emit(event.value);
        break;
      default:
        break;
    }
  }

  //Obtiene todos los productos que esten asociados a pólizas por idBusinessCountry.
  getProductByPolicies() {
    this._transactionService
      .getProductByPolicies(this.idtypePolicy)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.productList = resp.result;
        }
      });
  }

  //Obtiene todas las aseguradoras que esten asociadas a pólizas por idBusinessCountry.
  getInsuranceByPolicies() {
    this._transactionService
      .getInsuranceByPolicies(this.idtypePolicy)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.insuranceList = resp.result;
        }
      });
  }

  //Obtiene todas los estados de pólizas por idBusinessCountry.
  getStatePolicies() {
    this._transactionService
      .getStatePolicies()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          let stateIdValid = [1, 2, 3, 4, 5];
          if (this.idtypePolicy === 1) {
            stateIdValid = [1, 2, 4];
          }
          if (this.idtypePolicy !== 1) {
            stateIdValid = [1, 2, 3];
          }
          this.policyStatus = resp.result.filter((x: SelectModel) =>
            stateIdValid.includes(x.id)
          );
        }
      });
  }

  //Obtiene las pólizas registradas en el sistema por medio de un filtro.
  filterGeneralPolicyData(model: RequesFilterPolicyTableModel) {
    this._transactionService
      .getPolicyFilter(model)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          this.dataTablePolicy = [];
          this.matDialog.closeAll();
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTablePolicy = [];
          this.matDialog.closeAll();
        } else {
          this.dataTablePolicy = resp.result.item1;
          this.amountRows = resp.result.item2;
          this.matDialog.closeAll();
        }
      });
  }

  //Detecta los cambios en la paginación de la tabla.
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.currentPosition = this.pageIndex * this.pageSize;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 0;
    }
    this.requestFilterPolicy.page = this.currentPosition;
    this.requestFilterPolicy.pageSize = this.pageSize;

    let payload: RequesFilterPolicyTableModel = this.requestFilterPolicy;
    this.filterGeneralPolicyData(payload);
  }

  //Función que ordena los datos de la tabla.
  onSortClick() {
    if (this.requestFilterPolicy.order === 'asc') {
      this.requestFilterPolicy.order = 'desc';
      this.filterGeneralPolicyData(this.requestFilterPolicy);
    } else {
      this.requestFilterPolicy.order = 'asc';
      this.filterGeneralPolicyData(this.requestFilterPolicy);
    }
  }

  //Función que redirige al componente de edición y creación de pólizas.
  policyConfiguration() {
    this._customeRouter.navigate([
      `dashboard/massive/mass-creation-policy/${this.idBusinessByCountry}`,
    ]);
  }

  checkedStatus(statusId: number): boolean {
    return this.listSelectedStates.some((l) => l == statusId);
  }

  checkedType(policyTypeId: number): boolean {
    return this.listSelectedPolicyType.some((l) => l == policyTypeId);
  }

  //Fubnción que borra los filtros aplicados a el formulario formFilter.
  cleanFilterForm() {
    this.formFilter.reset();
    this.listSelectedPolicyType = [];
    this.listSelectedStates = [];
    this.requestFilterPolicy = {
      idBusinessCountry: this.idBusinessByCountry,
      endorsement: '',
      idInsurance: 0,
      keyword: '',
      idProduct: 0,
      statePolicyId: 0,
      order: 'desc',
      page: 0,
      pageSize: 5,
      idtypePolicy: this.idtypePolicy,
    };
    this.filterGeneralPolicyData(this.requestFilterPolicy);
  }

  //Función que aplica los filtros seleccionados en el modal de filtros generales.
  applyFilters() {
    this.pageIndex = 0;
    let payload: RequesFilterPolicyTableModel = this.formFilter.value;
    console.log(this.formFilter.value);

    payload.idBusinessCountry = this.idBusinessByCountry;
    payload.page = 0;
    payload.idtypePolicy = this.idtypePolicy;
    this.requestFilterPolicy = payload;
    this.filterGeneralPolicyData(payload);
  }

  ngOnDestroy(): void {}
}
