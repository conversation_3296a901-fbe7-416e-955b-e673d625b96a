<ng-container *ngIf="tops.length > 0">
  <div class="cont-title mb-3">
    <h4 *ngIf="topsObject.vName; else notName">{{ topsObject.vName }}</h4>
    <ng-template #notName>
      <h4>{{ "HomeAdvisors.Tops.Title" | translate }}</h4>
    </ng-template>
  </div>
  <div class="cont-tops" *ngIf="tops.length > 0">
    <div class="cont-cards" *ngFor="let top of tops">
      <div class="cont-header">
        <h3>{{ top.vTitle }}</h3>
      </div>
      <div class="cont-body">
        <div
          class="cont-item-tops"
          *ngFor="let item of top.vJsonElements; let i = index"
        >
          <div class="top-number" id="first-number">
            <span>{{ i + 1 }}</span>
          </div>
          <div>
            <span>{{ item }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>
