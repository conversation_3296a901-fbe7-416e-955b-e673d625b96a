import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { SectionService } from 'src/app/shared/services/section/section.service';
import { ActivatedRoute, Router } from '@angular/router';
import { SectionModel } from 'src/app/shared/models/product-sections';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Location } from '@angular/common';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-all-characteristics',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './all-characteristics.component.html',
  styleUrls: ['./all-characteristics.component.scss'],
})
export class AllCharacteristicsComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  dataTableSection: SectionModel[] = [];
  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('ProductSection.CharacteristicName'),
      columnValue: 'vDescription'
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item)
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];
  enterprise: string = '';
  country: string = '';
  product: string = '';
  producId: number = 0;

  constructor(
    private _sectionService: SectionService,
    private _activatedRoute: ActivatedRoute,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _utilsSvc: UtilsService,
    private _location: Location,
    public router: Router,
    private _customRouter: CustomRouterService,

  ) {}

  ngOnInit(): void {
    this.getsettingCountryAndCompanySubscription();
    this.getIdProduct();

    this._translateService.onLangChange.subscribe(
      (event: LangChangeEvent) => {
        this.estructTable[0].columnLabel = this._translateService.instant('ProductSection.CharacteristicName')
        this.estructTable[1].columnLabel = this._translateService.instant('Status')
        this.estructTable[2].columnLabel = this._translateService.instant('Modify')
      }
    );
  }

  getsettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.enterprise = response.enterprise.vName;
            this.country = response.country.vCountryName;
          }
        }
      );
  }

  getSectionPlanList(id: number) {
    this._sectionService.getSectionPlanList(id).subscribe({
      next: (response) => {
        this.dataTableSection = response.result;
      },
    });
  }

  getIdProduct() {
    this._activatedRoute.params.subscribe((params: any) => {
      this.getSectionPlanList(params.id);
      this.producId = params.id;
      this.product = params.product;
    });
  }

  controller(event: IconEventClickModel) {
    if (event.column === 'modify') {
      this._customRouter.navigate([
        `dashboard/product-sections/edit-characteristics/${this.producId}/${this.product}/${event.value.pkIIdSectionPlanProduct}`,
      ]);
    }
  }

  goToCreateCharacteristics() {
    this._customRouter.navigate([
      `dashboard/product-sections/create-characteristics/${this.producId}/${this.product}`,
    ]);
  }

  goBack() {
    this._location.back();
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
