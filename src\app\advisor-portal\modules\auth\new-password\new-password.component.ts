import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { AssignPasswordModel } from 'src/app/shared/models/user';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { ToastComponent } from 'src/app/shared/components/toast/toast.component';
import { ToastModel } from 'src/app/shared/models/toast';
import { debounceTime } from 'rxjs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { REGEX_PATTERNS } from 'src/app/shared/services/constants';

@Component({
  selector: 'app-new-password',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    ToastComponent,
    TranslateModule,
  ],
  templateUrl: './new-password.component.html',
  styleUrls: ['../auth.scss'],
})
export class NewPasswordComponent implements OnInit {

  toastConfiguration: ToastModel = {
    title: 'La contraseña no cumple con lo requerido',
    contBody:  'Contener mínimo 8 caracteres, letras mayúsculas y minúsculas, números y caracteres especiales',
    hour: '',
    showToast: false,
    type: 'warning',
    iconClass: 'warning',
  };
  
  constructor(
    private messageService: MessageService,
    private userService: UserService,
    private fb: FormBuilder,
    public utilsService: UtilsService,
    private activatedRoute: ActivatedRoute,
    private _customRouter: CustomRouterService,
    private _router: Router,
    private _translateService: TranslateService,
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    const parts = this._router.url.split('/');
    this._customRouter.setNavigationData(parts[1], parts[2]);
  }

  form: FormGroup = new FormGroup({});

  initForm() {
    this.form = this.fb.group({
      token: [''],
      password: [
        '',
        [
          Validators.required,
          Validators.pattern(REGEX_PATTERNS.PASSWORD),
        ],
      ],
      confirmPassword: [
        '',
        [
          Validators.required,
          Validators.pattern(REGEX_PATTERNS.PASSWORD),
        ],
      ],
    });

    this.form.get('password')?.valueChanges.pipe(debounceTime(500)).subscribe({
      next:(data=>{
        if(data){
          if(!this.form.get('password')?.valid){
            const currentDate = new Date();
            this._translateService.instant('Auth.TitleToastInvalidPassword')
            this._translateService.instant('Auth.SubtitleToastInvalidPassword')
            this.toastConfiguration.showToast = true;
            this.toastConfiguration.hour = `${currentDate.getHours()}:${currentDate.getMinutes()}:${currentDate.getSeconds()}`
          } else{
            this.toastConfiguration.showToast = false;
          }
        }
      })
    })

    this.form.get('confirmPassword')?.valueChanges.pipe(debounceTime(500)).subscribe({
      next:(data=>{
        if(data){
          if(!this.form.valid){
            const currentDate = new Date();
            this.toastConfiguration.showToast = true;
            this.toastConfiguration.hour = `${currentDate.getHours()}:${currentDate.getMinutes()}:${currentDate.getSeconds()}`
          } else{
            this.toastConfiguration.showToast = false;
          }
        }
      })
    })
  }

  get valid(): boolean {
    return this.form.valid;
  }

  recoverPassword() {
    let payload: AssignPasswordModel;
    let token: string = '';
    this.activatedRoute.params.subscribe((params: any) => {
      token = params.token;
    });
    if (this.valid && this.validatePass()) {
      payload = {
        password: this.form.get('password')?.value,
        token: token,
      };
      this.userService.assignPassword(payload).subscribe({
        next: (response) => {
          if (!response.error) {
            this.messageService.messageSuccess(
              'Contraseña creada exitosamente',
              'Ya puede ingresar a su cuenta'
            );
            this._customRouter.navigate(['/auth/login'])
          } else {
            this.messageService.messageInfo(
              'Error',
              `${response.message} Por favor vuelva a generar una nueva solicitud.`
            );
          }
        },
        error: (err) => {
          console.log(err);
        },
      });
    } else if (!this.validatePass()) {
      this.messageService.messageInfo(
        'Error',
        'Las contraseñas ingresadas no son iguales'
      );
    }
  }

  validatePass(): boolean {
    if (
      this.form.get('password')?.value ===
      this.form.get('confirmPassword')?.value
    ) {
      return true;
    } else {
      return false;
    }
  }
}
