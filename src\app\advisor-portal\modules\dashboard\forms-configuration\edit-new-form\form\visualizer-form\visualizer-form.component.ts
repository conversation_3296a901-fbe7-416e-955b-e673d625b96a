import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FieldComponent } from 'src/app/shared/components/field-generator/field.component';

@Component({
  selector: 'app-visualizer-form',
  standalone: true,
  imports: [
    CommonModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatIconModule,
    MatSelectModule,
    MatButtonModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    MatRadioModule,
    MatNativeDateModule,
    WizardComponent,
    MatTabsModule,
    FormsModule,
    MatSlideToggleModule,
    FieldComponent
  ],
  templateUrl: './visualizer-form.component.html',
  styleUrls: ['./visualizer-form.component.scss'],
})
export class VisualizerFormComponent implements OnInit, OnDestroy {
  @Input() data: any = [];
  steps: string[] = [];
  currentStep: number = 0;

  //Se agrega esta variable para evitar error por falta de instanciar un form en el componente field.
  formBuild: FormGroup = new FormGroup([]);

  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['data']) {
      this.steps = this.getNameProgressBar(this.data);
    }
  }

  ngOnDestroy(): void {}

  next(event: string) {}

  back(event: string) {}

  currentStepChange(event: number) {
    this.currentStep = event;
  }

  goToBack() {}

  // Función para obtener los valores de la key "vName" dentro de "progressBarModules"
  getNameProgressBar(data: any): string[] {
    const steps: string[] = [];

    // Itera sobre cada objeto en el array
    data.forEach((objeto: any) => {
      // Verifica si existe la propiedad "progressBarModules"
      if (
        objeto.progressBarModules &&
        Array.isArray(objeto.progressBarModules)
      ) {
        // Itera sobre cada objeto dentro de "progressBarModules"
        objeto.progressBarModules.forEach((progressBar: any) => {
          // Verifica si existe la propiedad "vName" y agrega el valor al array de nombres
          if (progressBar.vName) {
            steps.push(progressBar.vName);
          }
        });
      }
    });
    return steps;
  }
}
