<h3 class="col-md-12"> {{ "ValidationForm.Title" | translate }} </h3>

<div class="row mt-2">
    <app-table [displayedColumns]="estructValidationTable"
               [data]="dataValidationTable"
               (iconClick)="controller($event)">
  </app-table> 
</div>

<div class="row">
    <div class="col-md-12">
      <button type="button" class="w-auto"(click)="openModal()" mat-raised-button color="primary">
         {{ "ValidationForm.AddValidation" | translate }} 
         
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
  </div>

    <!-- Modal Filtro -->
<ng-template #AddValidationModal>
    <app-modal2 [titleModal]="modalTitle" (closeModal)="closeModal($event)">
      <ng-container body>
        <form [formGroup]="modalFormAddValidation">

          <div class="col-md-6 col-sm-12">
            <mat-slide-toggle class="mb-3" style="margin-top: 10px;" formControlName="validationStatus" (change)="onToggleChange($event)">
                 {{"ValidationForm.ToggleStatus" | translate}} 
            </mat-slide-toggle>
          </div>        

          <div class="row mt-5">
            <div class="col-12 col-md-12">
              <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label> {{"ValidationForm.NameLabel" | translate}} </mat-label>
                <input matInput formControlName="validationName" />
                <mat-error *ngIf="_utilsService.isControlHasError(modalFormAddValidation, 'validationName', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="col-12 col-md-12">

              <mat-form-field class="w-100 mb-2" appearance="outline">
                <mat-label>{{ "FormConfiguration.ProgressBar.StepOrderLabel" | translate }}</mat-label>
                <mat-select formControlName="validationOrder">
                    <mat-option *ngFor="let order of orderList" [value]="order"> {{ order }}</mat-option>
                </mat-select>
                <mat-error *ngIf="_utilsService.isControlHasError(modalFormAddValidation, 'validationOrder', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
            </div>

            <div class="col-12 col-md-12">              
                <mat-label>{{"ValidationForm.ConditionsLabel" | translate}}</mat-label>
                <h5>{{"ValidationForm.ConditionsText" | translate}}</h5>
              
            </div><br>

            <div class="col-12 col-md-12">
                <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{"ValidationForm.ActionDependentFieldLabel" | translate}}</mat-label>
                    <mat-select [required]="true" formControlName="actionDependFieldList"  >
                      <mat-option *ngFor="let item of dependentFieldsList" [value]="item.pkIIdFieldModule" >
                        {{ item.vNameField }}
                    </mat-option>
                    </mat-select>
                    <mat-error *ngIf="_utilsService.isControlHasError(modalFormAddValidation, 'actionDependFieldList', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error> 
                </mat-form-field>
            </div>

            <div class="col-12 col-md-12">
                <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{"ValidationForm.ValidationTypeLabel" | translate}}</mat-label>
                    <mat-select [required]="true" formControlName="validationTypeList" (selectionChange)="validationTypeSelectChange($event.value)" >                    
                    <mat-option *ngFor="let item of typeOfValidationList" [value]="item.value" >
                      {{ item.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="_utilsService.isControlHasError(modalFormAddValidation, 'validationTypeList', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error> 
                </mat-form-field>
            </div>

            <div class="col-12 col-md-12">
                <mat-form-field appearance="outline" class="w-100 mb-2" *ngIf="!isDefinedInputShowed">
                    <mat-label>{{"ValidationForm.DateTypeLabel" | translate}}</mat-label>
                    <mat-select [required]="true" formControlName="dateTypeList" (selectionChange)="onFormatChange($event.value)"  >
                      <mat-option *ngFor="let item of typeOfDateList" [value]="item.value" >
                        {{ item.name }}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="_utilsService.isControlHasError(modalFormAddValidation, 'dateTypeList', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error> 
                </mat-form-field>
            </div>

            <div class="col-12 col-md-12">
                <mat-form-field class="w-100" *ngIf="!isDefinedInputShowed">
                    <mat-label>{{"ValidationForm.DateLabel" | translate}}</mat-label>
                    <input  matInput  [matDatepicker]="dDateHolidays" formControlName="dateFormat" />          
                    <mat-datepicker-toggle  matIconSuffix [for]="dDateHolidays"></mat-datepicker-toggle>
                    <mat-datepicker #dDateHolidays></mat-datepicker>
                </mat-form-field>
            </div>
        

            <div class="col-12 col-md-12">
                <mat-form-field appearance="outline" class="w-100 mb-2" *ngIf="isDefinedInputShowed">
                    <mat-label>{{"ValidationForm.DefinedValueLabel" | translate}}</mat-label>
                    <input  PreventionSqlInjector  matInput type="text"  formControlName="definedValue" >           
                   <mat-error *ngIf="_utilsService.isControlHasError(modalFormAddValidation, 'definedValue', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error> 
                </mat-form-field>
            </div>
      
            
            <div class="col-12 col-md-12">               
                <mat-label>{{"ValidationForm.ActionToBeTakenLabel" | translate}}</mat-label>
                <h5>{{"ValidationForm.ActionToBeTakenText" | translate}}</h5>                           
            </div>
            <div class="col-12 col-md-12">
                <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>{{"ValidationForm.ActionTypeLabel" | translate}}</mat-label>
                <mat-select [required]="true" formControlName="actionTypeList"  >
                  <mat-option *ngFor="let item of typeOfActionList" [value]="item.value" >
                    {{ item.name }}
                </mat-option>
                  </mat-select>
                  <mat-error *ngIf="_utilsService.isControlHasError(modalFormAddValidation, 'actionTypeList', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error> 
                 </mat-form-field>
            </div>
       
            <div class="col-12 col-md-12">
                <mat-form-field appearance="outline" class="w-100">
                   <mat-label>{{"ValidationForm.StatusToWhichTaskChangesLabel" | translate}}</mat-label>
                    <mat-select [required]="true" formControlName="stateTaskChangeList"  >
                      <mat-option *ngFor="let stageByStateChildren of stageByStateChildrenList" [value]="stageByStateChildren.pkIIdStageByState">                                          
                        {{ stageByStateChildren.vState }}
                    </mat-option>                
                    </mat-select>
                  <mat-error *ngIf="_utilsService.isControlHasError(modalFormAddValidation, 'stateTaskList', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error> 
                  </mat-form-field>
            </div>                     
          </div>
        </form>      
      </ng-container>
  
      <ng-container customButtonRight>
        <button
          *ngIf="idContent <= 0"        
          type="button"
          mat-raised-button
          color="primary"
          (click)="addValidation()"
        >  {{"ValidationForm.ValidationSave" | translate}} 
            
          <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
        </button>

        <button *ngIf="idContent > 0" type="button" mat-raised-button  color="primary"(click)="ediValidation()">{{ "Modify" | translate }} 
          <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        </button>
      </ng-container>
      
    </app-modal2>
  </ng-template>