import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Subscription, catchError, of } from 'rxjs';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { PlansModel } from 'src/app/shared/models/plans';
import { Router } from '@angular/router';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MatSelectModule } from '@angular/material/select';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { PlansService } from 'src/app/shared/services/plans/plans.service';
import { PorductListModel } from 'src/app/shared/models/product';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';
@Component({
  selector: 'app-all-plans',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TableComponent,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MatTooltipModule
  ],
  templateUrl: './all-plans.component.html',
  styleUrls: ['./all-plans.component.scss'],
})
export class AllPlansComponent implements OnInit, OnDestroy {
  formProduct: FormGroup = new FormGroup({});
  private _settingCountryAndCompanySubscription?: Subscription;
  productList?: Subscription;
  product?: Subscription;
  skipOffer: boolean = false;
  plansData: PlansModel[] = [];
  pkIIdProduct: number = 0;
  estructPlansTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Plan.Plan'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Plan.Insurer'),
      columnValue: 'vInsuranceCompany',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
  ];
  produts: PorductListModel[] = [];
  private localStorageService = inject(LocalStorageService);

  constructor(
    private _router: Router,
    private _messageService: MessageService,
    private _productService: ProductService,
    private _settingService: SettingService,
    private _plansService: PlansService,
    private _fb: FormBuilder,
    private _translateService: TranslateService,
    private _utilsSvc: UtilsService,
    private _customRouter: CustomRouterService,

  ) {}

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this.initForm();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table business
      this.estructPlansTable[0].columnLabel =
        this._translateService.instant('Plan.Plan');
      this.estructPlansTable[1].columnLabel =
        this._translateService.instant('Plan.Insurer');
      this.estructPlansTable[2].columnLabel =
        this._translateService.instant('Status');
      this.estructPlansTable[3].columnLabel =
        this._translateService.instant('Modify');
    });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.localStorageService.setItem(
                'businessByCountryPlan',
                String(response.enterprise.pkIIdBusinessByCountry)
              );
              this.getProductsList(response.enterprise.pkIIdBusinessByCountry);
            }
          }
        }
      );
  }

  getProductsList(pkIIdBusinessByCountry: number) {
    this.productList = this._productService
      .getAllProductByBusiness(pkIIdBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.produts = [];
          this.plansData = [];
          this.formProduct.get('productSelect')?.setValue([]);
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.produts = resp.result;
          }
        }
      });
  }

  initForm() {
    this.formProduct = this._fb.group({
      productSelect: [[], [Validators.required]],
    });

    this.formProduct.valueChanges.subscribe({
      next: (response) => {
        this.pkIIdProduct = response.productSelect.pkIIdProduct;
        this.getPlansByProductId(response.productSelect.pkIIdProduct);
      },
    });
  }

  getPlansByProductId(pkIIdProduct: number) {
    this._plansService.getPlanList(pkIIdProduct).subscribe({
      next: (response) => {
        this.plansData = response.result;
      },
    });
    this.product = this._productService.GetProductById(pkIIdProduct)
    .subscribe((resp: ResponseGlobalModel) => {
      if (resp.error) {
      } else {
        this.skipOffer = resp.result.bSkipOffers;
      }
    });
  }

  controller(event: IconEventClickModel) {
    if (event.column === 'edit') {
      this._customRouter.navigate([
        `/dashboard/plans/edit/${this.pkIIdProduct}/${event.value.pkIIdPlan}`,
      ]);
    }
  }

  get formValid(): boolean {
    return this.formProduct.valid;
  }

  gotToCreate() {
    console.log("this is the plan length",this.plansData.length);
    if(this.plansData && this.plansData.length > 0 && this.skipOffer){
      this._messageService.messageInfo(
        this._translateService.instant('InvalidAction'),
          this._translateService.instant('YouCannotProceedWithMultiplePlansAndSkipOffer')
      );
      return;
    }
    else{
      if (this.formValid && this.produts.length > 0) {
        this._customRouter.navigate([`/dashboard/plans/new/${this.pkIIdProduct}`]);
      } else {
        this._messageService.messageInfo(
          this._translateService.instant('InvalidForm'),
          this._translateService.instant(
            'YouMustSelectACountryABusinessAndAProduct'
          )
        );
      }
    }
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
    this.productList?.unsubscribe();
    this.product?.unsubscribe();
  }
}
