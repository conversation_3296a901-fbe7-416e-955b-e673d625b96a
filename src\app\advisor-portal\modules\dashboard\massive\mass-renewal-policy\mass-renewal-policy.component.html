<!-- Titulo Renovación masiva de polizas-->
<div class="row">
  <h5 class="title mb-1 fw-bold">
    {{ "RenewalPolicy.Subtitle" | translate }}
  </h5>
  <p class="description">
    {{ "RenewalPolicy.SubtitleDescription" | translate }}
  </p>
</div>

<!-- Formulario lista desplegable -->
<div class="row mt-6">
  <div class="col-md-12 col-sm-12 mb-3">
    <form [formGroup]="form">
      <div class="row">
        <!-- Producto  -->
        <div class="col-md-12 col-sm-12 mb-3">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.Product" | translate }}</mat-label>
            <mat-select formControlName="fkIIdProduct">
              <mat-option *ngFor="let option of listProduct" [value]="option.id">{{ option.name }}</mat-option>
            </mat-select>
            <mat-error *ngIf="
                utilsService.isControlHasError(form, 'fkIIdProduct', 'required')
              ">
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <ng-container *ngIf="form.value.fkIIdProduct">
        <div class="cont-fieles mt-3">
          <div class="cont-template">
            <div class="cont-info-file">
              <p class="m-0 p-0">{{ templateName }}</p>
              <span class="description">25 MB</span>
            </div>
            <div class="cont-download-icon">
              <span (click)="downloadTemplate()" class="material-symbols-outlined click">
                download
              </span>
            </div>
          </div>

          <div class="cont-template" *ngIf="uploadedFile.length > 0">
            <div class="cont-info-file">
              <p class="m-0 p-0">{{ fileName }}</p>
              <span class="description">25 MB</span>
            </div>
            <div class="cont-download-icon">
              <span class="material-symbols-outlined click" (click)="deleteFile()"> delete </span>
            </div>
          </div>
          <div class="cont-upload" *ngIf="uploadedFile.length === 0">
            <app-drag-drop-upload [fileAccept]="'.xlsx'" [isOneFile]="true" [message]="''"
              (saveFile)="getFiles($event)"></app-drag-drop-upload>
          </div>
        </div>
      </ng-container>

      <div class="d-flex justify-content-center mt-6 gap-4">
        <a class="label-button" mat-button (click)="goBackMassive()"><span>{{
            "Close" | translate
            }}</span>
          <mat-icon fontIcon="arrow_back"></mat-icon>
        </a>
        <button *ngIf="form.value.fkIIdProduct" type="button" class="w-auto" [disabled]="
          uploadedFile.length === 0 ||
          !form.valid
        " (click)="loadMassive()" mat-raised-button color="primary">
          {{ "RenewalPolicy.UploadPolicies" | translate }}
        </button>

      </div>
    </form>
  </div>
</div>