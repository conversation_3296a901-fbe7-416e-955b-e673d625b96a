import { CommonModule } from '@angular/common';
import {
  <PERSON>mpo<PERSON>,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { PageEvent } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  FilterPolicyModel,
  InsurancePolicyModel,
  PolicyModel,
  PolicyTypeModel,
  ProductPolicyModel,
  SharedInformationPolicymodel,
} from 'src/app/shared/models/policy';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { GlobalSelectModel } from 'src/app/shared/models/shared';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-all-policy',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    Modal2Component,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
  ],
  templateUrl: './all-policy.component.html',
  styleUrls: ['./all-policy.component.scss'],
})
export class AllPolicyComponent implements OnInit, OnDestroy {
  //Variables relacionadas con los modales.
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;

  //Variables para filtrar la data de la tabla.
  keyword: string = '';

  //Variables relacioandas con la suscripción de empresa país.
  private _settingCountryAndCompanySubscription?: Subscription;
  private _policyDataSubscription?: Subscription;
  formValid: boolean = false;
  idBusinessByCountry: number = 0;
  idCountry: number = 0;

  //Variables relacioandas con la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;
  currentPosition: number = 0;
  dataTablePolicy: PolicyModel[] = [];
  estructTablePolicy: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyNumber'
      ),
      columnValue: 'policyNumber',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      ),
      columnValue: 'idPolicy',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      ),
      columnValue: 'productName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      ),
      columnValue: 'insuranceName',
    },
    {
      columnLabel: this._translateService.instant(
        'validityTypeName'
      ),
      columnValue: 'validityName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.Table.Type'
      ),
      columnValue: 'policyTypeName',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'active',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  //variable formulario.
  formFilter: FormGroup = new FormGroup({});
  listSelectedStates: number[] = [];
  listSelectedPolicyType: number[] = [];
  productList: ProductPolicyModel[] = [];
  insuranceList: InsurancePolicyModel[] = [];
  requestFilterPolicy: FilterPolicyModel = {
    active: null,
    idBusinessCountry: 0,
    idInsurance: null,
    idPolicyType: null,
    idProduct: null,
    keyword: null,
    order: 'desc',
    page: 0,
    pageSize: 5,
  };

  //listas para los checks
  statusList = [
    { id: 1, checked: false, name: this._translateService.instant('Active') },
    { id: 2, checked: false, name: this._translateService.instant('Inactive') },
  ];
  policyTypes: PolicyTypeModel[] = [];
  typeOfValidity: GlobalSelectModel[] = [];

  constructor(
    private _fb: FormBuilder,
    private _router: Router,
    private _settingService: SettingService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _utilsSvc: UtilsService,
    public matDialog: MatDialog,
    private _transactionService: TransactionService,
    private _customeRouter: CustomRouterService,
    private _policyService: PolicyService,
    private _parametersService: ParametersService,
  ) { }

  ngOnInit(): void {
    this.getAllValidyOfType();
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              if (response.enterprise?.pkIIdBusinessByCountry) {
                this.idBusinessByCountry =
                  response.enterprise?.pkIIdBusinessByCountry;
                this.idCountry = response.enterprise.pkIIdCountry;
              }
              this.requestFilterPolicy.idBusinessCountry =
                this.idBusinessByCountry;
              this.filterGeneralPolicyData(this.requestFilterPolicy);
            }
          }
        }
      );
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTablePolicy[0].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyNumber'
      );
      this.estructTablePolicy[1].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      );
      this.estructTablePolicy[2].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      );
      this.estructTablePolicy[3].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      );
      this.estructTablePolicy[4].columnLabel = this._translateService.instant(
        'PolicyConfiguration.Table.Type'
      );
      this.estructTablePolicy[5].columnLabel =
        this._translateService.instant('Status');
      this.estructTablePolicy[6].columnLabel =
        this._translateService.instant('Modify');
    });
    const payload: SharedInformationPolicymodel = {
      idForm: 0,
      idPolicyType: 0,
      idPolicy: 0,
      filesCreated: [],
    };
    this._policyService.setCurrentPolicyData(payload);
  }

  //Evento que valida si el formulario de empresa país es valido.
  validForm(event: boolean) {
    this.formValid = event;
  }

  //Función que se ejecuta al dar enter o click en el icono de busqueda, del input buscar.
  search(keyword: string) {
    this.requestFilterPolicy.keyword = keyword;
    this.requestFilterPolicy.page = 0;
    this.filterGeneralPolicyData(this.requestFilterPolicy);
  }

  //Función que abre el modal para filtrar de filtros.
  openFilterDialog() {
    this.getAllPolicyType();
    this.getProductsWithAssociatedPolicies();
    this.getInsurancesWithAssociatedPolicies();
    const dialogRef = this.matDialog.open(this.filtersModal!, {
      width: '30vw',
      maxHeight: '90vh',
    });
    this.initFilterForm();
    this.formFilter
      .get('idProduct')
      ?.setValue(this.requestFilterPolicy.idProduct);
    this.formFilter
      .get('idInsurance')
      ?.setValue(this.requestFilterPolicy.idInsurance);
    this.formFilter
      .get('idPolicyType')
      ?.setValue(this.requestFilterPolicy.idPolicyType);
  }

  //Delcaración del formulario formFilter.
  initFilterForm() {
    this.formFilter = this._fb.group({
      idProduct: null,
      idInsurance: null,
      active: null,
      idPolicyType: [],
      idBusinessCountry: this.idBusinessByCountry,
      page: this.pageIndex,
      pageSize: 5,
      order: 'desc',
    });
  }

  //Controlador de las acciones configuradas en la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'modify':
        this._customeRouter.navigate([
          `dashboard/policy-configuration/modify/${this.idBusinessByCountry}/${event.value.idPolicy}/${this.idCountry}`,
        ]);
        break;
      default:
        break;
    }
  }

  //Obtiene los tramites filtrados por empresa pais y otros filtros opcionales.
  getAllPolicyType() {
    this._transactionService
      .getAllPolicyType()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.policyTypes = resp.result;
        }
      });
  }

  //Obtiene todos los productos que esten asociados a pólizas por idBusinessCountry.
  getProductsWithAssociatedPolicies() {
    this._transactionService
      .getProductsWithAssociatedPolicies(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.productList = resp.result;
        }
      });
  }

  //Obtiene todas las aseguradoras que esten asociadas a pólizas por idBusinessCountry.
  getInsurancesWithAssociatedPolicies() {
    this._transactionService
      .getInsurancesWithAssociatedPolicies(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.insuranceList = resp.result;
        }
      });
  }

  //Obtiene las pólizas registradas en el sistema por medio de un filtro.
  filterGeneralPolicyData(model: FilterPolicyModel) {
    this._transactionService
      .filterGeneralPolicyData(model)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          this.dataTablePolicy = [];
          this.matDialog.closeAll();
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTablePolicy = [];
          this.matDialog.closeAll();
        } else {
          this.dataTablePolicy = resp.result;
          this.amountRows = resp.rowCount;
          this.formatData();
          this.matDialog.closeAll();
        }
      });
  }

  //Detecta los cambios en la paginación de la tabla.
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.currentPosition = this.pageIndex * this.pageSize;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 0;
    }
    this.requestFilterPolicy.page = this.currentPosition;
    this.requestFilterPolicy.pageSize = this.pageSize;

    let payload: FilterPolicyModel = this.requestFilterPolicy;
    this.filterGeneralPolicyData(payload);
  }

  //Función que redirige al componente de edición y creación de pólizas.
  policyConfiguration() {
    this._customeRouter.navigate([
      `dashboard/policy-configuration/new/${this.idBusinessByCountry}/${this.idCountry}`,
    ]);
  }

  //Verifica el estado checkeado y lo setea en el formulario.
  checkedChangeStatus(statusId: number) {
    //Cambiamos el valor de la propiedad checked en el array original para que al momento de borrar el filtro, pueda verse reflejado el cambio a nivel visual.
    let statusFound = this.statusList.find((status) => status.id === statusId);

    if (statusFound) statusFound.checked = !statusFound.checked;

    //Comprobamos si el tipo de póliza ya esta agg, si lo está, lo elimina, sino lo agg.
    if (statusId) {
      const index = this.listSelectedStates.indexOf(statusId);
      if (index !== -1) {
        this.listSelectedStates.splice(index, 1);
      } else {
        this.listSelectedStates.push(statusId);
      }
      if (this.listSelectedStates.length === 2) {
        this.formFilter.get('active')?.setValue(null);
      } else {
        if (this.listSelectedStates[0] === 1) {
          this.formFilter.get('active')?.setValue(true);
        } else if (this.listSelectedStates[0] === 2) {
          this.formFilter.get('active')?.setValue(false);
        } else {
          this.formFilter.get('active')?.setValue(null);
        }
      }
    }
  }

  checkedStatus(statusId: number): boolean {
    return this.listSelectedStates.some((l) => l == statusId);
  }

  checkedType(policyTypeId: number): boolean {
    return this.listSelectedPolicyType.some((l) => l == policyTypeId);
  }

  checkedChangeType(policyTypeId: number) {
    //Cambiamos el valor de la propiedad checked en el array original para que al momento de borrar el filtro, pueda verse reflejado el cambio a nivel visual.
    let policyTypeFound = this.policyTypes.find(
      (policyType: PolicyTypeModel) =>
        policyType.pkIIdPolicyType === policyTypeId
    );

    if (policyTypeFound) policyTypeFound.checked = !policyTypeFound.checked;

    //Comprobamos si el tipo de póliza ya esta agg, si lo está, lo elimina, sino lo agg.
    if (policyTypeId) {
      const index = this.listSelectedPolicyType.indexOf(policyTypeId);
      if (index !== -1) {
        this.listSelectedPolicyType.splice(index, 1);
      } else {
        this.listSelectedPolicyType.push(policyTypeId);
      }
      this.formFilter
        .get('idPolicyType')
        ?.setValue(this.listSelectedPolicyType);
    }
  }

  //Fubnción que borra los filtros aplicados a el formulario formFilter.
  cleanFilterForm() {
    this.formFilter.reset();
    this.listSelectedPolicyType = [];
    this.listSelectedStates = [];
    this.requestFilterPolicy = {
      active: null,
      idBusinessCountry: this.idBusinessByCountry,
      idInsurance: null,
      idPolicyType: null,
      idProduct: null,
      keyword: null,
      order: 'desc',
      page: 0,
      pageSize: 5,
    };
    this.filterGeneralPolicyData(this.requestFilterPolicy);
  }

  //Función que aplica los filtros seleccionados en el modal de filtros generales.
  applyFilters() {
    this.pageIndex = 0;
    let payload: FilterPolicyModel = this.formFilter.value;
    payload.idBusinessCountry = this.idBusinessByCountry;
    payload.page = 0;
    this.requestFilterPolicy = payload;
    this.filterGeneralPolicyData(payload);
  }

  clearCheckStatusList() {
    this.statusList.forEach((element) => {
      element.checked = false;
    });
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
    this._policyDataSubscription?.unsubscribe();
  }

  //Obtiene todos los tipos de vigencia registrados en el sistema.
  getAllValidyOfType() {
    this._parametersService
      .getParameters('Type_Of_Validity')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.typeOfValidity = resp;
        } else {
        }
      });
  }

  formatData(){
    const updatedPolicies = this.dataTablePolicy.map(policy => {
      const validity = this.typeOfValidity.find(type => type.value === policy.fkIIdValidityType);
      return {
        ...policy,
        validityName: validity ? validity.name : "No aplica"
      };
    });

    this.dataTablePolicy = updatedPolicies;
  }
}
