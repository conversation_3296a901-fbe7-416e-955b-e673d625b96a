<app-company-country-history></app-company-country-history>
<app-forms-configuration-history></app-forms-configuration-history>
<mat-tab-group>
  <mat-tab label="Formulario">
    <app-form (sendIdForm)="getIdForm($event)" (sendDataForm)="getDataForm($event)" (reloadTable)="reloadTable($event)"></app-form>
  </mat-tab>
  <mat-tab label="Tablas">
    <app-tables [idForm]="idForm"></app-tables>
  </mat-tab>
  <mat-tab label="Documentos">
    <app-documents [idForm]="idForm"></app-documents>
  </mat-tab>
  <mat-tab label="Alertas">
    <app-alerts [idForm]="idForm"></app-alerts>
  </mat-tab>
  <mat-tab label="Comunicaciones">
    <app-comunications *ngIf="idForm > 0" [idForm]="idForm" [dataForm]="dataForm"></app-comunications>
  </mat-tab>
  <mat-tab label="{{'ValidationForm.ValidationTabTitle' | translate }}">
    <app-validations  *ngIf="idForm > 0" [idForm]="idForm"></app-validations>
  </mat-tab>

  <mat-tab label="{{'MappingForm.MappingTabTitle' | translate }}">
    <app-policies *ngIf="idForm > 0" [idForm]="idForm"></app-policies>
  </mat-tab>
</mat-tab-group>
