import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { <PERSON><PERSON><PERSON>er, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatDialog } from '@angular/material/dialog';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { Subscription, catchError, of } from 'rxjs';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Router } from '@angular/router';
import { TaskTrayConfigModel } from 'src/app/shared/models/task-tray-config';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

@Component({
  selector: 'app-all-formalities-tray-settings',
  standalone: true,
  imports: [
    CommonModule, 
    ChooseCountryAndCompanyComponent, 
    TranslateModule,
    TableComponent,
    Modal2Component,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatSlideToggleModule
  ],
  templateUrl: './all-formalities-tray-settings.component.html',
  styleUrls: ['./all-formalities-tray-settings.component.scss']
})
export class AllFormalitiesTraySettingsComponent implements OnInit, OnDestroy{

  //variables para obtener el idBusinessCountry seleccionado
  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessByCountry: number = 0;
  canDeactive: boolean = true;

  //variable para dataTable
  proceduresViewerSettingsDataTable: any[] = []; //cambiar tipo de variable a modelo correcto
  estructProceduresViewerSettingsTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'TaskTraySettings.Table.ItemName'
      ),
      columnValue: 'vFieldName',
    },
    {
      columnLabel: this._translateService.instant(
        'TaskTraySettings.Table.Order'
      ),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant(
        'TaskTraySettings.Table.Type'
      ),
      columnValue: 'vType',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
  ];

  //modal editar elimento
  @ViewChild('editElementModal')editElementModal?: TemplateRef<any>;

  //formulario modificar item
  form: FormGroup = new FormGroup({});

  //lista que llena el select de orden en tabla
  orderList: any[]=[];

  
  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _router: Router,
    private _settingService: SettingService,
    private _taskTayConfigService: TaskTayConfigService,
    private _translateService: TranslateService,
    public matDialog: MatDialog,
    public utilsSvc: UtilsService,
  ){}
 

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table configuración de bandeja de tareas.
      this.estructProceduresViewerSettingsTable[0].columnLabel =
        this._translateService.instant('TaskTraySettings.Table.ItemName');
      this.estructProceduresViewerSettingsTable[1].columnLabel =
        this._translateService.instant('TaskTraySettings.Table.Order');

      this.estructProceduresViewerSettingsTable[2].columnLabel =
        this._translateService.instant('TaskTraySettings.Table.Type');

      this.estructProceduresViewerSettingsTable[3].columnLabel =
        this._translateService.instant('Status');

      this.estructProceduresViewerSettingsTable[4].columnLabel =
        this._translateService.instant('Modify');
    });
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  //evento al presionar boton en modificar un item
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.openModalEditElement(event.value)
        break;
      default:
        break;
    }
  }

  openModalEditElement(value: TaskTrayConfigModel) {
    this.initForm(value);
    this.matDialog.open(this.editElementModal!, {
      width: '60vw',
      height: 'auto',
    }); 
    if (!value.bCanDeactive) {
      this.form.get('bActive')?.disable();
    } else {
      this.form.get('bActive')?.enable();
    }
  }

  //inicia el formulario de actualizar item
  initForm(value: TaskTrayConfigModel) {
    this.form = this._fb.group({
      pkIIdTaskTrayConfig: [value.pkIIdTaskTrayConfig],
      vFieldName: [value.vFieldName],
      iOrder: [value.iOrder],
      bActive: [value.bActive],
    });
  }

  //Completa el formulario de actualizar item
  complete(){
      const formData = this.form.getRawValue();
      this._taskTayConfigService
        .updateTaskTrayConfig(formData)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            } else {
              if (error.error.message === 'FilterLimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.FilterLimitExceededSubtitle'
                  )
                );
              }
              if (error.error.message === 'LimitExceeded') {
                this._messageService.messageInfo(
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededTitle'
                  ),
                  this._translateService.instant(
                    'TaskTraySettings.FormCreateEditElement.Messages.LimitExceededSubtitle'
                  )
                );
              }
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              '',
              this._translateService.instant(
                'TaskTraySettings.FormCreateEditElement.Messages.Modified'
              )
            );
            this.getProceduresViewerSettingList(this.idBusinessByCountry);
            this.matDialog.closeAll();
          }
        });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessByCountry =
                response.enterprise.pkIIdBusinessByCountry;                
                this.getProceduresViewerSettingList(this.idBusinessByCountry);
            }
          }
        }
      );
  }

  //Obtiene la lista de tramites a mostrar en la tabla
  getProceduresViewerSettingList(idBusinessByCountry: number) {
    this._taskTayConfigService
      .getProceduresViewerSettingsItemsList(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.proceduresViewerSettingsDataTable = [];
        } else {
          this.proceduresViewerSettingsDataTable = resp.result;
          if (resp.result.length > 0) {
            this.orderList = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderList = this.utilsSvc.generarArrayOrderList(1);
          }
        }
      });
  }
}
