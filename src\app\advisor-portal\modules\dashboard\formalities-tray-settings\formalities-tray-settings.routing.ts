import { Routes } from '@angular/router';
import { FormalitiesTraySettingsComponent } from './formalities-tray-settings.component';

export default [
  {
    path: '',
    component: FormalitiesTraySettingsComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './all-formalities-tray-settings/all-formalities-tray-settings.component'
          ).then((c) => c.AllFormalitiesTraySettingsComponent),
      },
      //     {path: 'all', loadComponent:()=> import('./all-forms/all-forms.component').then(c=>c.AllFormsComponent)},
      //     {path: 'new', loadComponent:()=> import('./edit-new-form/edit-new-form.component').then(c=>c.EditNewFormComponent)},
      //     {path: 'modify/:id', loadComponent:()=> import('./edit-new-form/edit-new-form.component').then(c=>c.EditNewFormComponent)},
    ],
  },
] as Routes;
