<div class="row">
    <div class="col-md-10">
        <!-- input de busqueda en la tabla -->
        <mat-form-field class="w-100">
            <mat-label>
                {{ "Search" | translate }}
            </mat-label>
            <input (keyup.enter)="search(keyword)" [(ngModel)]="keyword" matInput type="text" class="form-control"
                placeholder="{{ 'Search' | translate }}" />
            <mat-icon class="hand click" (click)="search(keyword)" matSuffix>search</mat-icon>
        </mat-form-field>
    </div>
    <div class="d-flex col-md-2">
        <div class="mx-3">
            <!-- boton que abre el modal de filtrar -->
            <button class="" type="button" color="primary" (click)="openFilterDialog()" mat-raised-button>
                {{ "Filter" | translate }}
                <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
            </button>
        </div>
        <div class="">
            <button (click)="onSortClick()" class="" type="button" mat-raised-button color="primary">
                {{ "Order" | translate }}
                <mat-icon fontIcon="sort_by_alpha"></mat-icon>
            </button>
        </div>
    </div>
</div>

<!-- datatable Pólizas individuales-->
<div class="row mt-2">
    <app-table *ngIf="dataTablePolicy.length > 0" [displayedColumns]="estructTable" [data]="dataTablePolicy"
        [IsStatic]="false" [pageIndex]="pageIndex" [pageSize]="pageSize" [amountRows]="amountRows"
        (pageChanged)="onPageChange($event)" (iconClick)="controller($event)"></app-table>
</div>

<!-- modal filtros -->
<ng-template #filtersModal>
    <app-modal2 [titleModal]="'Filter' | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <form [formGroup]="formFilter">
                <div class="row">
                    <!-- Producto -->
                    <div class="col-md-6 mb-2">
                        <mat-form-field appearance="outline" class="select-look w-50 m-auto w-100">
                            <mat-label>
                                {{ "FormsConfigurationHistory.Product" | translate }}
                            </mat-label>
                            <mat-select formControlName="idProduct">
                                <mat-option *ngFor="let product of productList" [value]="product.id">
                                    {{ product.name }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>

                    <!-- Aseguradora -->
                    <div class="col-md-6 mb-2">
                        <mat-form-field class="w-100" appearance="fill">
                            <mat-label>
                                {{"ReportLogCarrier.InsuranceLabel" | translate}}
                            </mat-label>
                            <mat-select formControlName="idInsurance">
                                <mat-option *ngFor="let insurance of insuranceList"
                                    [value]="insurance.id">
                                    {{ insurance.name }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>

                    <!-- Endoso -->
                    <div class="col-md-6 mb-2">
                        <mat-form-field class="w-100" appearance="fill">
                            <mat-label>
                                {{"Policy.Endorsement" | translate}}
                            </mat-label>
                            <mat-select formControlName="endorsement">
                                <mat-option *ngFor="let endorsement of endorsementList" [value]="endorsement.value">
                                    {{ endorsement.value }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>

                    <!-- estado -->
                    <div class="col-md-6 mb-2">
                        <mat-form-field class="w-100" appearance="fill">
                            <mat-label>
                                {{"Status" | translate}}
                            </mat-label>
                            <mat-select formControlName="statePolicyId">
                                <mat-option *ngFor="let status of policyStatus" [value]="status.id">
                                    {{ status.name }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
            </form>
        </ng-container>

        <!-- Botón Borrar filtros -->
        <ng-container customButtonCenter>
            <button (click)="cleanFilterForm()" class="btn-custom w-100" type="button" mat-raised-button>
                <strong>{{ "PolicyConfiguration.Filter.DeleteFilters" | translate }}</strong>
            </button>
        </ng-container>
        <!-- Botón Aplicar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary" (click)="applyFilters()">
                {{ "Apply" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>