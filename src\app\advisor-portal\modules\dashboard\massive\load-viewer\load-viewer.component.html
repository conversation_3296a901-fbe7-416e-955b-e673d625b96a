<!-- Sección de Cargas en proceso -->
<ng-container *ngIf="showTableInProcess">
    <!-- <PERSON>ti<PERSON>lo <PERSON>ólizas configuradas-->
    <div class="row mt-2 mb-3">
        <h5 class="title-local mb-1">{{ 'BulkUpload.LoadViewer.LoadsInProcess.Title' | translate }}</h5>
        <p class="description">{{ 'BulkUpload.LoadViewer.LoadsInProcess.Subtitle' | translate }}</p>
    </div>
    <div class="row">
        <div class="col-md-11">
            <!-- input de busqueda en la tabla Cargas en proceso  -->
            <mat-form-field class="w-100">
                <mat-label>
                    {{ "BulkUpload.LoadViewer.LoadsInProcess.SearchForLoad" | translate }}
                </mat-label>
                <input (keyup.enter)="search(keywordInProgress, true)" [(ngModel)]="keywordInProgress" matInput
                    type="text" class="form-control"
                    placeholder="{{ 'BulkUpload.LoadViewer.LoadsInProcess.SearchForLoad' | translate }}" />
                <mat-icon class="hand click" (click)="search(keywordInProgress, true)" matSuffix>search</mat-icon>
            </mat-form-field>
        </div>
        <div class="col-md-1">
            <!-- boton que abre el modal de filtrar -->
            <button class=" mr-2 mt-1" type="button" color="primary" (click)="openFilterInProcess(true)"
                mat-raised-button>
                {{ "Filter" | translate }}
                <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
            </button>
        </div>
    </div>

    <ng-container *ngIf="this.dataTableLoadViewerInProcess.length > 0">
        <!-- datatable Cargas en proceso  -->
        <div class="row mt-2">
            <app-table [displayedColumns]="estructTableLoadViewerPolicyInProcess" [data]="dataTableLoadViewerInProcess"
                [IsStatic]="false" [pageIndex]="pageIndexInProcess" [pageSize]="pageSizeInProcess"
                [amountRows]="amountRowsInProcess" (pageChanged)="onPageChangeInProcess($event)"></app-table>
        </div>

        <!-- Conteo de procesos en tabla Cargas en proceso -->
        <div class="row mt-2">
            <h6 class="title-local mb-1">{{ 'BulkUpload.LoadViewer.LoadsInProcess.OngoingProcesses' | translate }}
                {{ongoingProcesses}} {{ 'BulkUpload.LoadViewer.LoadsInProcess.TotalProcesses' | translate
                }}{{amountRowsInProcess}}
            </h6>
        </div>
    </ng-container>

</ng-container>

<!-- Sección de Cargas finalizadas -->
<ng-container *ngIf="showTableFinished">
    <!-- Subtitulo Pólizas configuradas-->
    <div class="row mt-5 mb-3">
        <h5 class="title-local mb-1">{{ 'BulkUpload.LoadViewer.LoadsCompleted.Title' | translate }}</h5>
        <p class="description">{{ 'BulkUpload.LoadViewer.LoadsCompleted.Subtitle' | translate }}</p>
    </div>
    <div class="row">
        <div class="col-md-11">
            <!-- input de busqueda en la tabla Cargas finalizadas  -->
            <mat-form-field class="w-100">
                <mat-label>
                    {{ "BulkUpload.LoadViewer.LoadsInProcess.SearchForLoad" | translate }}
                </mat-label>
                <input (keyup.enter)="search(keywordFinished, false)" [(ngModel)]="keywordFinished" matInput type="text"
                    class="form-control"
                    placeholder="{{ 'BulkUpload.LoadViewer.LoadsInProcess.SearchForLoad' | translate }}" />
                <mat-icon class="hand click" (click)="search(keywordFinished, false)" matSuffix>search</mat-icon>
            </mat-form-field>
        </div>
        <div class="col-md-1">
            <!-- boton que abre el modal de filtrar -->
            <button class=" mr-2 mt-1" type="button" color="primary" (click)="openFilterInProcess(false)"
                mat-raised-button>
                {{ "Filter" | translate }}
                <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
            </button>
        </div>
    </div>

    <ng-container *ngIf="dataTableLoadViewerFinished.length > 0">
        <!-- datatable Cargas finalizadas  -->
        <div class="row mt-2">
            <app-table [displayedColumns]="estructTableLoadViewerPolicyFinished" [data]="dataTableLoadViewerFinished"
                [IsStatic]="false" [pageIndex]="pageIndexFinished" [pageSize]="pageSizeFinished"
                [amountRows]="amountRowsFinished" (pageChanged)="onPageChangeFinished($event)"
                (iconClick)="controllerLoadViewerFinished($event)"></app-table>
        </div>

        <!-- Conteo de procesos en tabla Cargas finalizadas -->
        <div class="row mt-2">
            <h6 class="title-local mb-1">{{ 'BulkUpload.LoadViewer.LoadsCompleted.CompletedProcesses' | translate }}
                {{amountRowsFinished}}
            </h6>
        </div>
    </ng-container>

</ng-container>

<div class="d-flex justify-content-center mt-3">
    <a class="label-button" mat-button
        (click)="goBackMassive()"><span>{{'BulkUpload.Massives.MassCreationPolicy.GoOutToMass'| translate}}</span>
        <mat-icon fontIcon="arrow_back"></mat-icon>
    </a>
</div>

<!-- modal filtros -->
<ng-template #filtersModal>
    <app-modal2 [titleModal]="'Filter' | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <form [formGroup]="formFilter">
                <!-- Tipo de gestión -->
                <div class="row mt-3 mb-2">
                    <div class="col-md-12">
                        <mat-form-field class="w-100" appearance="fill">
                            <mat-label>{{"BulkUpload.LoadViewer.Table.ManagementOfType" | translate}}</mat-label>
                            <mat-select formControlName="typeManagement">
                                <mat-option *ngFor="let item of bulkLoadType" [value]="item.id">
                                    {{ item.name }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>

                <!-- Usuario responsable -->
                <div class="col-md-12 mb-2">
                    <mat-form-field class="w-100" appearance="fill">
                        <mat-label>
                            {{"BulkUpload.LoadViewer.Table.ResponsibleUser" | translate}}
                        </mat-label>
                        <mat-select formControlName="userId">
                            <mat-option *ngFor="let user of userList" [value]="user.id">
                                {{ user.name }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- Estado -->
                <div class="col-md-12 mb-2">
                    <mat-form-field class="w-100" appearance="fill">
                        <mat-label>
                            {{"Status" | translate}}
                        </mat-label>
                        <mat-select formControlName="typeState" multiple>
                            <mat-option *ngFor="let user of typeBulkUploadProcess" [value]="user.value">
                                {{ user.name }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </form>
        </ng-container>

        <!-- Botón Borrar filtros -->
        <ng-container customButtonCenter>
            <button (click)="cleanFilterForm()" class="btn-custom w-100" type="button" mat-raised-button>
                <strong>{{ "PolicyConfiguration.Filter.DeleteFilters" | translate }}</strong>
            </button>
        </ng-container>
        <!-- Botón Aplicar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary" (click)="applyFilters()">
                {{ "Apply" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>