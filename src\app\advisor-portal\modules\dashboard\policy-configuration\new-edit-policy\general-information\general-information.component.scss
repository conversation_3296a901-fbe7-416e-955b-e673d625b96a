.downloand-color {
  background: #f5f5f5;
}

.prevent-disabled-icon {
  pointer-events: auto;
}

.cont-items-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cont-btn-name-file {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 85%;
}

.truncate-input {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.description {
  margin-bottom: 2rem;
  opacity: 0.6;
}

::ng-deep .custom-input .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
  padding: 0 !important;
}

