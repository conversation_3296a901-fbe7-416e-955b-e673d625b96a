<div class="row mt-5">
    <!-- pestañas -->
    <h5 class="fw-bold mb-2">
        {{ 'configurationClientPortal.Pestana' | translate }}
    </h5>
    <app-table [displayedColumns]="estructTablePestana" [data]="dataTablePestana"
        (iconClick)="controller($event)"></app-table>
    <div class="row">
        <div class="col">
            <button type="button" mat-raised-button color="primary" (click)="openModal()">
                {{ "configurationClientPortal.AddPestana" | translate }}
            </button>
        </div>
    </div>

<p></p>

    

    <!-- subpestañas -->
    <h5 class="fw-bold mb-2">
        {{ 'configurationClientPortal.SubPestana' | translate }}
    </h5>

    <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>{{ 'configurationClientPortal.pestana' | translate }}</mat-label>
        <mat-select formControlName="iOrder" (selectionChange)="onPestanaChange($event.value)">
            <mat-option *ngFor="let pt of pestana" [value]="pt.pkIIdMenu">
            {{ pt.vDescription }}
            </mat-option>
        </mat-select>
    </mat-form-field>

    <app-table [displayedColumns]="estructTableSubPestana" [data]="dataTableSubPestana"
        (iconClick)="controller($event)"></app-table>
    <div class="row">
        <div class="col">
            <button type="button" mat-raised-button color="primary" (click)="openSubModal()">
                {{ "configurationClientPortal.AddPestana" | translate }}
            </button>
        </div>
    </div>
</div>

<!-- pestaña -->
<ng-template #createEditPestana>
    <app-modal2
     [titleModal]="titleModalPestana">
        <ng-container body>
            <form [formGroup]="formPestana">

             <div *ngIf="formPestana.get('iOrder')?.value !== ''">
                <!-- Pestaña Activa -->
                <mat-slide-toggle formControlName="bActive" class="mb-2">
                    {{ 'configurationClientPortal.PestanaActiva' | translate }}
                </mat-slide-toggle>

                <!-- Asociado a -->
                <div class="mb-3">
                    <label class="d-block">{{ 'configurationClientPortal.associatedwith' | translate }}</label>
                    <mat-radio-group formControlName="iAssociated" class="d-flex gap-3">
                        <mat-radio-button [value]="1">{{ 'configurationClientPortal.landing' | translate
                            }}</mat-radio-button>
                        <mat-radio-button [value]="2">{{ 'configurationClientPortal.modulo' | translate
                            }}</mat-radio-button>
                        <mat-radio-button [value]="3">{{ 'configurationClientPortal.link' | translate
                            }}</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
                <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{ 'configurationClientPortal.namePestana' | translate }}</mat-label>
                    <input matInput formControlName="vDescription" />
                    <mat-error *ngIf="utilsSvc.isControlHasError(formPestana, 'vDescription', 'required')">
                        Este campo es obligatorio
                    </mat-error>
                </mat-form-field>
              <div *ngIf="formPestana.get('iOrder')?.value !== ''">
                <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{ 'configurationClientPortal.ordenPestana' | translate }}</mat-label>
                    <mat-select formControlName="iOrder">
                        <mat-option *ngFor="let order of orderList" [value]="order">
                          {{ order }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>

               
                <mat-form-field  *ngIf="formPestana.get('iAssociated')?.value === 1" id="state" class="w-100">
                    <mat-label>{{ 'configurationClientPortal.producto' | translate }}</mat-label>
                    <mat-select formControlName="FkIIdProduct">
                        <mat-option *ngFor="let product of Listproducts" [value]="product.pkIIdProduct">{{ product.vProductName
                            }}</mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field *ngIf="formPestana.get('iAssociated')?.value === 2" appearance="outline" class="w-100 mb-2">
                    <mat-label>Módulo</mat-label>
                    <mat-select formControlName="FkIIdModule">
                        <mat-option *ngFor="let module of getAvailableModules()" [value]="module.pkIIdMenu">{{ module.vDescription
                            }}</mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field *ngIf="formPestana.get('iAssociated')?.value === 3" appearance="outline" class="w-100 mb-2">
                    <mat-label>Link de redirección</mat-label>
                    <input matInput formControlName="vLink" />
                </mat-form-field>


            </form>
        </ng-container>
        <ng-container customButtonRight>
            <div class="modal-footer">
                <button mat-raised-button color="primary" type="button" class="" (click)="savePestana()">
                    {{ "Save" | translate }}
                </button>
            </div>
        </ng-container>
    </app-modal2>
</ng-template>


<ng-template #createEditSubPestana>
    <app-modal2
    [titleModal]="titleModalSubPestana">
        <ng-container body>
            <form [formGroup]="formSubPestana">
                <!-- Pestaña Activa -->
                <mat-slide-toggle formControlName="bActive" class="mb-2">
                    {{ 'configurationClientPortal.PestanaActiva' | translate }}
                </mat-slide-toggle>

                <!-- Asociado a -->
                <div class="mb-3">
                    <label class="d-block">{{ 'configurationClientPortal.associatedwith' | translate }}</label>
                    <mat-radio-group formControlName="iAssociated" class="d-flex gap-3">
                        <mat-radio-button [value]="1">{{ 'configurationClientPortal.landing' | translate
                            }}</mat-radio-button>
                        <mat-radio-button [value]="2">{{ 'configurationClientPortal.link' | translate
                            }}</mat-radio-button>
                    </mat-radio-group>
                </div>

                <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{ 'configurationClientPortal.pestana' | translate }}</mat-label>
                    <mat-select formControlName="iParent" (selectionChange)="onPestanaChange($event.value)">
                        <mat-option *ngFor="let pt of pestana" [value]="pt.pkIIdMenu">
                        {{ pt.vDescription }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                
                <!-- descripcion -->
                <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{ 'configurationClientPortal.namePestana' | translate }}</mat-label>
                    <input matInput formControlName="vDescription" />
                    <mat-error *ngIf="utilsSvc.isControlHasError(formPestana, 'vDescription', 'required')">
                        Este campo es obligatorio
                    </mat-error>
                </mat-form-field>

                <!-- orden subpestaña -->
                <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{ 'configurationClientPortal.ordenPestana' | translate }}</mat-label>
                    <mat-select formControlName="iOrder">
                        <mat-option *ngFor="let order of ordersubList" [value]="order">
                          {{ order }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>



                <mat-form-field  *ngIf="formSubPestana.get('iAssociated')?.value === 1" id="state" class="w-100">
                    <mat-label>{{ 'configurationClientPortal.producto' | translate }}</mat-label>
                    <mat-select formControlName="FkIIdProduct">
                        <mat-option *ngFor="let product of Listproducts" [value]="product.pkIIdProduct">{{ product.vProductName
                            }}</mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field *ngIf="formSubPestana.get('iAssociated')?.value === 2" appearance="outline" class="w-100 mb-2">
                    <mat-label>Link de redirección</mat-label>
                    <input matInput formControlName="vLink" />
                </mat-form-field>



            </form>
        </ng-container>
        <ng-container customButtonRight>
            <div class="modal-footer">
                <button mat-raised-button color="primary" type="button" class="" (click)="saveSubPestana()">
                    {{ "Save" | translate }}
                </button>
            </div>
        </ng-container>
    </app-modal2>
</ng-template>