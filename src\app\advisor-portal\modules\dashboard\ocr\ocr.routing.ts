import { Routes } from '@angular/router';
import { OCRComponent } from './ocr.component';

export default [
  {
    path: '',
    component: OCRComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './all-configured-readings/all-configured-readings.component'
          ).then((c) => c.AllConfiguredReadingsComponent),
      },
      {
        path: 'new',
        loadComponent: () =>
          import(
            './create-edit-configured-readings/create-edit-configured-readings.component'
          ).then((c) => c.CreateEditConfiguredReadingsComponent),
      },
      {
        path: 'edit/:idConfiguration',
        loadComponent: () =>
          import(
            './create-edit-configured-readings/create-edit-configured-readings.component'
          ).then((c) => c.CreateEditConfiguredReadingsComponent),
      },
    ],
  },
] as Routes;
