<div class="col-12 col-md-12">
    <div class="row mb-2">
      <h3 class="col-md-12">  {{ "DocumentModule.TitleDocument" | translate }}</h3>
    </div>
    <app-table
      [displayedColumns]="estructTable"
      [data]="dataTableDocumentForm"
      (iconClick)="controller($event)"
    ></app-table>
    <button
      class="mb-2"
      type="button"
      mat-raised-button
      color="primary"
      (click)="openModalCreateEditDocumentModule()"
    >
      {{ "Add" | translate }}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
  
  <!-- modal editar-crear pestaña  -->
  <ng-template #editNewDocumentModuleModal>
    <app-modal2 [showCancelButtonBelow]="false" [titleModal]="titelModal"  >
      <ng-container body>
        <app-edit-documents
          [idForm]="idForm"
          [idTable]="idTable"
          [idBusinessCountry]="idBusinessCountry"
          (getDocumentListByFormIdModule)="getDocumentListByFormIdModule(idForm)"
          (cancelFormPrincipal)="cancelFormPrincipal($event)"
        ></app-edit-documents>
      </ng-container>
    </app-modal2>
  </ng-template>
  