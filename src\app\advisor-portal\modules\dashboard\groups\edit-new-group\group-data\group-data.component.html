<div class="title-customer">
  <h3 class="h3">
    {{'Group.NewGroup' | translate}}
  </h3>
  <span> 
    {{'Group.AllChangesWillBeSavedAutomatically' | translate}}
  </span>  
</div>
<form [formGroup]="form"  class="mt-3" >
  <!-- Grupo Activo -->
  <div class="cont-mat-slide-toggle mb-2">
      <div class="mt-2">
          <mat-slide-toggle class="mb-3" formControlName="bActive">
            {{ 'Group.ActiveGroup' | translate }}
          </mat-slide-toggle>
          </div>
      <div>
          <mat-icon class="click icon-tooltip" matTooltipPosition="right"
            matTooltip="{{ 'Tooltips.GroupActive' | translate }}">help_outline</mat-icon>
          </div>
      </div>
  <div class="row">
    <div class="col-12 col-md-12">
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ 'Group.GroupName' | translate }}
        </mat-label>
        <input matInput formControlName="vGroupName" PreventionSqlInjector/>
        <mat-error
          *ngIf="utilsSvc.isControlHasError(form, 'vGroupName', 'required')"
        >
        {{ 'ThisFieldIsRequired' | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ 'Group.Description' | translate }}
        </mat-label>
        <input matInput formControlName="vDescription" PreventionSqlInjector/>
        <mat-error
          *ngIf="utilsSvc.isControlHasError(form, 'vDescription', 'required')"
        >
        {{ 'ThisFieldIsRequired' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
  </div>
</form>
