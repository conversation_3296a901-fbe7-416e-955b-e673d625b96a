<div class="title m-0" *ngIf="!isClientPortal">
  <h2 class="h3">
    <img class="img-rounded" src="{{image}}" alt="" />
    {{ title }}
  </h2>
</div>
<app-breadcrumb *ngIf="!isClientPortal" [breadcrumbSections]="sections"></app-breadcrumb>
<div [ngClass]="{'p-5': isClientPortal}">
  <mat-form-field [hidden]="isSelected" appearance="outline" class="select-look w-50 m-auto">
    <mat-label>
      {{ "FormsConfigurationHistory.Product" | translate }}
    </mat-label>
    <mat-select  (selectionChange)="onProductSelected($event)">
      <mat-option *ngFor="let itemP of productList" [value]="itemP">
        {{ itemP.productName }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <router-outlet></router-outlet>
</div>