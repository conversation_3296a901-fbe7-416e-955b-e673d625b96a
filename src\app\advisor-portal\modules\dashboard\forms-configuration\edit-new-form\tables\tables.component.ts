import {
  Component,
  TemplateRef,
  ViewChild,
  Input,
  OnDestroy,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { Router } from '@angular/router';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatDialog } from '@angular/material/dialog';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { Subscription, catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { EditNewTableComponent } from './edit-new-table/edit-new-table.component';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TableModel } from 'src/app/shared/models/form-tables';

@Component({
  selector: 'app-tables',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatSelectModule,
    MatSlideToggleModule,
    CommonModule,
    TableComponent,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    Modal2Component,
    EditNewTableComponent,
  ],
  templateUrl: './tables.component.html',
  styleUrls: ['./tables.component.scss'],
})
export class TablesComponent implements OnInit, OnDestroy {
  //id form que esta seleccionado
  @Input() idForm: number = 0;
  // modal crear/editar tabla
  @ViewChild('editNewTableModal') editNewTableModal?: TemplateRef<any>;
  //suscripcion a empresa pais
  private _settingCountryAndCompanySubscription?: Subscription;
  //variable que valida si el formulario de crear/editar tabla es valido
  isFormValid: boolean = false;
  //variable que guarda los datos del fromulario de crear editar tabla
  tableModel?: TableModel;
  //idTable que se envia al modal en caso de editar
  idTable: number = 0;
  //idBusinessCountry activo
  idBusinessCountry: number = 0;
  showButtons: boolean = false;

  //data table de tablas
  estructTableTables: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Table.TableName'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Table.ProgressBar'),
      columnValue: 'vProgrssBarName',
    },
    {
      columnLabel: this._translateService.instant('Table.Tab'),
      columnValue: 'vTabName',
    },
    {
      columnLabel: this._translateService.instant('Table.Section'),
      columnValue: 'vSectionName',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsService.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];
  dataTableTables: TableModel[] = [];

  constructor(
    private _fb: FormBuilder,
    private _moduleService: ModuleService,
    private _modulesSettingService: ModulesSettingService,
    private _router: Router,
    private _editNewTableDialog: MatDialog,
    private _addFieldDialog: MatDialog,
    private _translateService: TranslateService,
    private _settingService: SettingService,
    private _messageService: MessageService,
    public utilsService: UtilsService
  ) {}

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTableTables[0].columnLabel =
        this._translateService.instant('Table.TableName');
      this.estructTableTables[1].columnLabel =
        this._translateService.instant('Table.ProgressBar');
      this.estructTableTables[2].columnLabel =
        this._translateService.instant('Table.Tab');
      this.estructTableTables[3].columnLabel =
        this._translateService.instant('Table.Section');
      this.estructTableTables[4].columnLabel =
        this._translateService.instant('Status');
      this.estructTableTables[5].columnLabel =
        this._translateService.instant('Action');
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['idForm']) {
      this.idForm = changes['idForm'].currentValue;
      this.idForm != 0 ? this.getTables(this.idForm) : '';
    }
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
          }
        }
      );
  }

  controller(evt: IconEventClickModel) {
    this.openEditNewTableModal(evt.value.pkIIdTableModule);
  }

  openEditNewTableModal(idTable: number) {
    this.idTable = idTable;
    this.idTable === 0 ? (this.showButtons = false) : (this.showButtons = true);
    //this.modulesList = [];
    const dialogRef = this._editNewTableDialog.open(this.editNewTableModal!, {
      disableClose: true,
      width: '150vh',
      maxHeight: '90vh',
    });
  }

  closeModal(){
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Out?'),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._editNewTableDialog.closeAll();
        }
      });
  }

  getFormResult(event: any) {
    if (event > 0) {
      this.idTable = event;
      this.showButtons = true;
      this.getTables(this.idForm);
    } else {
      if (event == 'invalid') {
        this.isFormValid = false;
      } else {
        this.tableModel = event;
        this.isFormValid = true;
      }
    }
  }

  getTables(idForm: number) {
    this.idTable = 0;
    this._moduleService
      .getTableListByFormId(idForm)
      .pipe(
        catchError((error) => {
          console.log('No existen etapas asociadas');
          this.dataTableTables = [];
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.dataTableTables = [];
        } else {
          if (resp.error) {
            console.log(resp.message);
          } else {
            this.dataTableTables = resp.result;
          }
        }
      });
  }

  updateTable() {
    if (this.isFormValid) {
      this._moduleService
        .updateTable(this.tableModel!)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageError(error.error.message);
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              ''
            );
            this.getTables(this.idForm);
            this._editNewTableDialog.closeAll();
          }
        });
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant(
          'PleaseCompleteAllTheInformationOnTheForm'
        )
      );
    }
  }

  deleteTable() {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Delete') + '?',
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._moduleService
            .deleteTable(this.idTable)
            .pipe(
              catchError((error) => {
                if (error.error.error) {
                  this._messageService.messageError(error.error.message);
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
              } else {
                this._messageService.messageConfirmatio(
                  this._translateService.instant('Deleted'),
                  '',
                  'success',
                  this._translateService.instant('Continue')
                );
                this.getTables(this.idForm);
                this._editNewTableDialog.closeAll();
                this.idTable = 0;
              }
            });
        }
      });
  }
}
