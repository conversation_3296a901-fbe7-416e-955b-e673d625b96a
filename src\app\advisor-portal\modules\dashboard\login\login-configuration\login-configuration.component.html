<app-choose-country-and-company (valueForm)="validForm($event)">
</app-choose-country-and-company>

<!-- Características -->
<div [formGroup]="formLogin" >
    <h3>{{ "configLogin.caracter" | translate }}</h3>
    <mat-checkbox formControlName="bAllow" (change)="onPermitirRegistroChange($event)">{{ "configLogin.record" | translate }}</mat-checkbox>
</div>
<!-- Rol de registro -->
<div class="col-12 col-md-12 mb-3" >
    <h4>{{ "ConfigurationIndex.Index" | translate }}</h4>
    <mat-form-field>
      <mat-label>{{ "ConfigurationIndex.Select" | translate }}</mat-label>
      <mat-select (selectionChange)="userTypeChange($event)" formControlName="fkIIDRol" >
        <mat-option *ngFor="let item of userType" [value]="item.pkIIdUserType">
          {{ item.vName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
<!-- Campos adicionales de registro -->
<app-registrationfields [idBusinessCountry]="idBusinessCountry" [idUserType]="idUserType" *ngIf="idUserType > 0 && formLogin.get('bAllow')?.value"></app-registrationfields>

<!-- Dominios permitidos -->
<app-domains [idBusinessCountry]="idBusinessCountry" [idUserType]="idUserType" *ngIf="idUserType > 0 && formLogin.get('bAllow')?.value"></app-domains>
<!-- Token de activacion y contraseña -->
<div [formGroup]="formLogin" *ngIf="idUserType > 0 && formLogin.get('bAllow')?.value">
    <div class="spaced-section">
        <h3>{{ "configLogin.tokenActivation" | translate }}</h3>
        <p>{{ "configLogin.tokenActivationtext" | translate }}</p>

        <mat-form-field class="full-width w-50" appearance="outline">
            <mat-label>{{ "configLogin.durationToken" | translate }} </mat-label>
            <input matInput placeholder="24" type="number" formControlName="iTokenDuration">
            <mat-hint>{{ "configLogin.definitivehours" | translate }}</mat-hint>
        </mat-form-field>
    </div>
    <!-- Bloqueo por inactividad -->
    <div class="spaced-section">
        <h3>{{ "configLogin.inactivitylock" | translate }}</h3>
        <p>{{ "configLogin.inactivitylockText" | translate }}</p>

        <div class="form-group">
            <mat-form-field class="number-input" appearance="outline">
                <mat-label>{{ "configLogin.number" | translate }}</mat-label>
                <input matInput placeholder="24" type="number" formControlName="iNumberBlock">
            </mat-form-field>

            <mat-form-field class="time-input" appearance="outline">
                <mat-label>{{ "configLogin.time" | translate }}</mat-label>
                <mat-select formControlName="iTimeBlock">
                    <mat-option [value]="1">{{ "configLogin.hour" | translate }}</mat-option>
                    <mat-option [value]="2">{{ "configLogin.day" | translate }}</mat-option>
                    <mat-option [value]="3">{{ "configLogin.months" | translate }}</mat-option>
                </mat-select>
                <mat-hint>{{ "configLogin.times" | translate }}</mat-hint>
            </mat-form-field>
        </div>
    </div>
</div>

<!-- checks de validacion -->
<app-check-validation [idBusinessCountry]="idBusinessCountry" [idUserType]="idUserType" *ngIf="idUserType > 0 && formLogin.get('bAllow')?.value"></app-check-validation>

<ng-container>
    <p>
    <div class="row">
        <div class="col">
            <button type="button" mat-raised-button color="primary" (click)="saveLogin()">
                {{ "Save" | translate }}
            </button>
        </div>
    </div>
</ng-container>