import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatInputModule } from '@angular/material/input';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatDialog } from '@angular/material/dialog';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { EditClientComponent } from '../edit-client/edit-client.component';
import { ClientService } from 'src/app/shared/services/client/client.service';
import { Subscription, catchError, of } from 'rxjs';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SelectModel } from 'src/app/shared/models/select';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import {
  CustomerFilterPruduct,
  CustomerGenericForSelect,
  CustomerProduct,
  CustomerQuote,
} from 'src/app/shared/models/client';
import { AppState } from 'src/app/store/reducers';
import { Store } from '@ngrx/store';
import {
  changeEdit,
  changeStageQuote,
  saveIdQuote,
} from 'src/app/store/actions';
import { QuoteStage } from 'src/app/shared/models/my-quotation/myQuotation.model';
import { FilterModalClientsComponent } from 'src/app/advisor-portal/modules/dashboard/clients/filter-modal-clients/filter-modal-clients.component';
import { ModalSeeDetailsComponent } from 'src/app/advisor-portal/modules/dashboard/clients/modal-see-details/modal-see-details.component';

@Component({
  selector: 'app-get-client',
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatTabsModule,
    RouterModule,
    MatInputModule,
    TranslateModule,
    TableComponent,
    ModalComponent,
    EditClientComponent,
  ],
  templateUrl: './get-client.component.html',
  styleUrls: ['./get-client.component.scss'],
})
export class GetClientComponent implements OnInit, OnDestroy {
  @ViewChild('editClientModal') editClientModal?: TemplateRef<any>;

  tittleModalText: string = '';

  client?: Subscription;
  quotes?: Subscription;

  clientResponse: any;

  onlyProducts: CustomerGenericForSelect[] = [];
  onlyProductsOfQuotes: CustomerGenericForSelect[] = [];
  onlyProcessOfProcedures: CustomerGenericForSelect[] = [];
  onlyProductOfProcedures: CustomerGenericForSelect[] = [];
  onlyInsurer: CustomerGenericForSelect[] = [];

  documentsTypes: SelectModel[] = [];
  idClient: number = 0;

  dataTableProducts: CustomerProduct[] = [];
  dataTableProductsInit: CustomerProduct[] = [];

  dataTableQuotes: CustomerQuote[] = [];
  dataTableQuotesInit: any[] = [];

  dataTableProcedures: any[] = [];
  dataTableProceduresInit: any[] = [];

  estructTableClients: BodyTableModel[] = [];

  estructTableProducts: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Client.Product.ProductName'),
      columnValue: 'productName',
    },
    {
      columnLabel: this._translateService.instant(
        'Client.Product.PremiumValue'
      ),
      columnValue: 'premiumValue',
    },
    {
      columnLabel: this._translateService.instant('Client.Product.PremiumType'),
      columnValue: 'premiumType',
    },
    {
      columnLabel: this._translateService.instant(
        'Client.Product.ProductState'
      ),
      columnValue: 'productState',
    },
    {
      columnLabel: this._translateService.instant(
        'Client.Product.InsuranceCarrierName'
      ),
      columnValue: 'insuranceCarrierName',
    },
    {
      columnLabel: this._translateService.instant('Actions'),
      columnValue: 'viewProduct',
      columnIcon: 'search',
    },
  ];

  estructTableClientsQuotes: BodyTableModel[] = [
    {
      columnLabel:
        'N° ' + this._translateService.instant('Client.Quote.QuoteNumber'),
      columnValue: 'iQuoteNumber',
    },
    {
      columnLabel: this._translateService.instant('Client.Quote.QuoteDate'),
      columnValue: 'dQuoteDate',
    },
    {
      columnLabel: this._translateService.instant('Client.Quote.ProductName'),
      columnValue: 'vProductName',
    },
    {
      columnLabel: this._translateService.instant('Client.Quote.QuoteState'),
      columnValue: 'vQuoteState',
    },
    {
      columnLabel: '',
      columnValue: 'vColorState',
    },
    {
      columnLabel: this._translateService.instant('Actions'),
      columnValue: 'viewQuote',
      columnIcon: 'search',
    },
  ];

  estructTableClientsProcedures: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'Client.Procedure.vProcedureName'
      ),
      columnValue: 'vProcedureName',
    },
    {
      columnLabel: this._translateService.instant(
        'Client.Procedure.ProductName'
      ),
      columnValue: 'vProductName',
    },
    {
      columnLabel: this._translateService.instant(
        'Client.Procedure.ProcedureState'
      ),
      columnValue: 'vProcedureState',
    },
    { columnLabel: '', columnValue: 'vColorState' },
    {
      columnLabel: this._translateService.instant(
        'Client.Procedure.ProcedureDate'
      ),
      columnValue: 'dProcedureDate',
    },
    {
      columnLabel: this._translateService.instant('Actions'),
      columnValue: 'viewProcedure',
      columnIcon: 'search',
    },
  ];

  withoutEmitString: string = 'SIN EMITIR';
  issuedString: string = 'EMITIDO';
  activeString: string = 'ACTIVO';
  inactiveString: string = 'INACTIVO';
  openString: string = 'ABIERTO';
  closedString: string = 'CERRADO';
  inCourseString: string = 'EN CURSO';

  constructor(
    private _router: Router,
    private _store: Store<AppState>,
    public _utilsSvc: UtilsService,
    public _translateService: TranslateService,
    public _editClientDialog: MatDialog,
    private _clientService: ClientService,
    private _activatedRoute: ActivatedRoute,
    private _messageService: MessageService,
    private _parametersService: ParametersService,
    private _customRouter: CustomRouterService,
    public dialog: MatDialog
  ) {
  }
 

  /**
   * Initializes the component by performing various data retrieval operations
   * and setting up subscriptions. This method is called once the component is
   * initialized.
   */
  ngOnInit(): void {
    // Retrieves the list of document types and assigns it to a local variable or state
    this.getListDocumentType();

    // Fetches the customers associated with a specific ID and their contact information
    this.getCostumersWithContactByIdCustomer();

    // Loads the list of products, possibly for display or processing within the component
    this.loadProducts();

    // Retrieves quotes data, which might be used for display or further processing
    this.loadQuotes();

    // Loads procedures data which might be used within the component for various functionalities
    this.loadProcedures();

    // Subscribes to language change events to update the component's language settings
    this.subscribeToLangChange();
  }

  /**
   * Cleanup logic for when the component is destroyed.
   * This method is called automatically by Angular when the component is removed.
   */
  ngOnDestroy(): void {
    // Unsubscribe from observables to prevent memory leaks
    this.client?.unsubscribe();
    this.quotes?.unsubscribe();

    // Notify the service that the client detail view is no longer active
    this._clientService.sendIsClientTrue({
      idClient: this.idClient,
      isClientDetailProduyct: false,
      isDetailClient: false,
    });
  }

  /**
   * Subscribes to language change events to update table columns accordingly.
   */
  private subscribeToLangChange(): void {
    // Listen for language change events from the translation service
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      // Update table columns when the language changes
      this.updateTableColumns();
    });
  }

  /**
   * Updates the table columns based on the current language.
   * Uses a timeout to ensure Angular detects the change in reference.
   */
  updateTableColumns() {
    this.estructTableClientsQuotes[0].columnLabel =
      'N° ' + this._translateService.instant('Client.Quote.QuoteNumber');
    this.estructTableClientsQuotes[1].columnLabel =
      this._translateService.instant('Client.Quote.QuoteDate');
    this.estructTableClientsQuotes[2].columnLabel =
      this._translateService.instant('Client.Quote.ProductName');
    this.estructTableClientsQuotes[3].columnLabel =
      this._translateService.instant('Client.Quote.QuoteState');
    this.estructTableClientsQuotes[5].columnLabel =
      this._translateService.instant('Actions');

    this.estructTableClientsProcedures[0].columnLabel =
      this._translateService.instant('Client.Procedure.vProcedureName');

    this.estructTableClientsProcedures[1].columnLabel =
      this._translateService.instant('Client.Procedure.ProductName');

    this.estructTableClientsProcedures[2].columnLabel =
      this._translateService.instant('Client.Procedure.ProcedureState');

    this.estructTableClientsProcedures[4].columnLabel =
      this._translateService.instant('Client.Procedure.ProcedureDate');

    this.estructTableClientsProcedures[5].columnLabel =
      this._translateService.instant('Actions');

    this.estructTableProducts[0].columnLabel = this._translateService.instant(
      'Client.Product.ProductName'
    );

    this.estructTableProducts[1].columnLabel = this._translateService.instant(
      'Client.Product.PremiumValue'
    );

    this.estructTableProducts[2].columnLabel = this._translateService.instant(
      'Client.Product.PremiumType'
    );

    this.estructTableProducts[3].columnLabel = this._translateService.instant(
      'Client.Product.ProductState'
    );

    this.estructTableProducts[4].columnLabel = this._translateService.instant(
      'Client.Product.InsuranceCarrierName'
    );

    this.estructTableProducts[5].columnLabel =
      this._translateService.instant('Accions');
  }

  /**
   * Retrieves customer details by their ID and handles potential errors.
   */
  getCostumersWithContactByIdCustomer() {
    // Subscribe to route parameters to get the customer ID
    this._activatedRoute.params.subscribe((params: any) => {
      const { id } = params;

      this.idClient = id;

      // Notify the service that we're viewing details of a specific client
      if (this.idClient) {
        this._clientService.sendIsClientTrue({
          idClient: this.idClient ?? 0,
          isClientDetailProduyct: false,
          isDetailClient: true,
        });
      }

      // Fetch customer details using the client service
      this.client = this._clientService
        .getCostumersWithContactByIdCustomer(parseInt(id))
        .pipe(
          catchError((error) => {
            // Handle and display error messages if the request fails
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((response) => {
          if (Array.isArray(response)) {
            // Display warning if no data is found
            this._messageService.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant('TaskTray.Messages.NoDataProduct')
            );
          } else {
            // Assign and log the successful response
            this.clientResponse = response.result;
          }
        });
    });
  }

  /**
   * Opens a dialog for editing client details.
   */
  openEditClientDialog() {
    // Set the title of the modal dialog
    this.tittleModalText = this._translateService.instant('Modificar datos');

    // Open the dialog with the specified component and configuration
    const dialogRef = this._editClientDialog.open(this.editClientModal!, {
      // Set the width of the dialog
      width: '60vw',

      // Set the maximum height of the dialog
      maxHeight: '90vh',

      // Pass client data to the dialog
      data: { client: this.clientResponse },
    });

    // Handle actions after the dialog is closed
    dialogRef.afterClosed().subscribe((result) => {});
  }

  /**
   * Fetches the list of global document types from the catalog.
   */
  getListDocumentType() {
    this._parametersService.getListCatalogGlobalDocumentTypes().subscribe({
      next: (response) => {
        // Check if the response contains an error
        if (!response.error) {
          this.documentsTypes = response.result;
        }
      },
      error: (err) => {
        // handle errors here if needed
        console.error('Error fetching document types:', err);
      },
    });
  }

  /**
   * Filters and retrieves the document type name based on the provided document ID.
   * @param idDocument - The ID of the document type to be retrieved.
   * @returns The name of the document type if found; otherwise, `undefined`.
   */
  filterTypeDocument(idDocument: any): string | undefined {
    // Search for the document type in the list by matching the provided ID
    const document = this.documentsTypes.find((doc) => doc.id === idDocument);

    // Return the name of the document type if it exists, otherwise return `undefined`
    return document?.name;
  }

  /**
   * Updates the customer details and handles success or error responses.
   * @param client - The customer object containing updated details.
   */
  editClient(client: any) {
    this._clientService
      .updateCostumerWithContact(client)
      .pipe(
        catchError((error) => {
          // Display a warning message if an error occurs
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          // Return an empty observable to keep the stream alive
          return of([]);
        })
      )
      .subscribe((resp: any) => {
        if (!resp.error) {
          // Show a success message and refresh customer details
          this._messageService.messageSuccess(
            this._translateService.instant('Saved'),
            ''
          );
          this.getCostumersWithContactByIdCustomer();
          this._editClientDialog.closeAll();
        } else {
          // Show an error message if the response contains an error
          this._messageService.messageError(
            `${this._translateService.instant('ThereWasAError')}: ${
              resp.message
            }`
          );
        }
      });
  }
  goToClient(){
    this._customRouter.navigate(["/dashboard/clients"]);
  }
  /**
   * Loads the products associated with the client and initializes related properties.
   */
  loadProducts() {
    this.quotes = this._clientService
      .getAllProdctsByClientId(this.idClient)
      .subscribe((products) => {
        // Assign the response to the dataTableProducts property
        this.dataTableProducts = products;

        if (this.dataTableProducts.length > 0) {
          // Format premium values and create mappings for product names and insurers
          this.dataTableProductsInit = this.dataTableProducts =
            this.dataTableProducts.map((product) => {
              return {
                ...product,
                premiumValue: this.formatCurrency(
                  parseFloat(product.premiumValue)
                ),
              };
            });

          // Create a list of products with name and ID
          this.onlyProducts = this.dataTableProducts.map((product) => {
            return {
              name: product.productName,
              id: product.productId,
            };
          });

          // Create a list of insurers with name and ID
          this.onlyInsurer = this.dataTableProducts.map((product) => {
            return {
              name: product.insuranceCarrierName,
              id: product.idInsuranceCarrier,
            };
          });

          // Initialize the dataTableProductsInit property
          this.dataTableProductsInit = this.dataTableProducts;
        }
      });
  }

  /**
   * Loads the quotes associated with the client and initializes related properties.
   */
  loadQuotes() {
    this.quotes = this._clientService
      .getAllQuotesByClientId(this.idClient)
      .subscribe((quotes) => {
        // Assign the response to the dataTableQuotes property
        this.dataTableQuotes = quotes;

        // Create a list of products from the quotes with name and ID
        this.onlyProductsOfQuotes = this.dataTableQuotes.map((quote) => {
          return {
            name: quote.vProductName,
            id: quote.iProductId,
          };
        });

        // Initialize the dataTableQuotesInit property
        this.dataTableQuotesInit = this.dataTableQuotes;
      });
  }

  /**
   * Loads the procedures associated with the client and initializes related properties.
   */
  loadProcedures() {
    this.quotes = this._clientService
      .getAllProceduresByClientId(this.idClient)
      .subscribe((procedures) => {
        // Assign the response to the dataTableProcedures property
        this.dataTableProcedures = procedures;

        // Initialize the dataTableProceduresInit property
        this.dataTableProceduresInit = this.dataTableProcedures;

        // Create a list of products from the procedures with name and ID
        this.onlyProductOfProcedures = this.dataTableProcedures.map(
          (procedure) => {
            return {
              name: procedure.vProductName,
              id: procedure.iProductId,
            };
          }
        );

        // Create a list of processes from the procedures with name and ID
        this.onlyProcessOfProcedures = this.dataTableProcedures.map(
          (procedure) => {
            return {
              name: procedure.vProcedureName,
              id: procedure.iProcessId,
            };
          }
        );
      });
  }

  /**
   * Formats a numeric value as a currency string.
   *
   * @param value - The numeric value to be formatted.
   * @returns A string representing the formatted currency.
   */
  formatCurrency(value: number): string {
    return value.toLocaleString('en-US', {
      style: 'currency',
      currency: 'USD',
    });
  }

  /**
   * Opens the filter dialog for products and applies selected filters.
   *
   * This method resets the products to their initial state, opens a dialog for filtering products,
   * and applies the filters based on the user's selection when the dialog is closed.
   */
  openFilterDialog(): void {
    // Reset products to their initial state before applying filters
    this.dataTableProducts = [...this.dataTableProductsInit];

    // Configure and open the filter dialog
    const dialogRef = this.dialog.open(FilterModalClientsComponent, {
      width: '400px',
      data: {
        isProduct: true,
        products: this.removeDuplicate(this.onlyProducts),
        insurers: this.removeDuplicate(this.onlyInsurer),
        states: this.armingStateObjectProduct()
      },
    });

    // Apply selected filters when the dialog is closed
    dialogRef.afterClosed().subscribe(
      (result: CustomerFilterPruduct) => {
        if (result) {
          this.applyFiltersToProducts(result);
        }
      },
      (error) => {
        // Error handling for opening the filter dialog
        console.error('Error opening the filter dialog:', error);
      }
    );
  }

  /**
   * Removes duplicate entries from an array based on the unique identifier (id).
   *
   * @param registers - The array of objects to be filtered for duplicates.
   * @returns An array of objects with unique ids.
   */
  removeDuplicate(
    registers: CustomerGenericForSelect[]
  ): CustomerGenericForSelect[] {
    const uniqueRegisterIds = new Set<number>();

    return registers.filter((register) => {
      if (uniqueRegisterIds.has(register.id)) {
        return false;
      } else {
        uniqueRegisterIds.add(register.id);
        return true;
      }
    });
  }

  /**
   * Creates an array of unique states for products from the list of products.
   *
   * @returns An array of unique product states.
   */
  armingStateObjectProduct(): Array<string> {
    // Extract all unique product states
    const allStates = this.dataTableProducts.map((r) => r.productState);

    // Use a Set to remove duplicates and convert it back to an array
    return Array.from(new Set(allStates));
  }

  /**
   * Applies filters to the list of products based on the provided filter criteria.
   *
   * @param result - The filter criteria including product, insurer, and product state.
   */
  applyFiltersToProducts(result: CustomerFilterPruduct) {
    // Filter by product if specified
    if (result.product.id) {
      this.dataTableProducts = this.dataTableProducts.filter(
        (res) => res.productId === result.product.id
      );
    }

    // Filter by insurer if specified
    if (result.insurer.id) {
      this.dataTableProducts = this.dataTableProducts.filter(
        (res) => res.idInsuranceCarrier === result.insurer.id
      );
    }

    // Apply filters by state
    if (result.active && result.inactive) {
      this.dataTableProducts = this.dataTableProducts.filter(
        (res) =>
          res.productState === 'Active' || res.productState === 'Inactive'
      );
    } else {
      if (result.active) {
        this.dataTableProducts = this.dataTableProducts.filter(
          (res) => res.productState === 'Active'
        );
      }

      if (result.inactive) {
        this.dataTableProducts = this.dataTableProducts.filter(
          (res) => res.productState === 'Inactive'
        );
      }
    }
  }

  /**
   * Opens the filter dialog for quotes and applies selected filters.
   *
   * This method resets the quotes to their initial state, prepares the filter options,
   * and opens a dialog for filtering quotes. It applies the filters based on the user's selection
   * when the dialog is closed.
   */
  openFilterDialogQuote(): void {
    // Reset quotes to their initial state before applying filters
    this.dataTableQuotes = [...this.dataTableQuotesInit];

    // Prepare the filter options including unique products, quotes, and states
    this.armingStateObject();

    // Configure and open the filter dialog
    const dialogRef = this.dialog.open(FilterModalClientsComponent, {
      width: '400px',
      data: {
        isQuote: true,
        products: this.removeDuplicate(this.onlyProductsOfQuotes),
        quotes: this.dataTableQuotes.map((r) => r.iCustomerId),
        states: this.armingStateObject(),
      },
    });

    // Apply selected filters when the dialog is closed
    dialogRef.afterClosed().subscribe(
      (result: CustomerFilterPruduct) => {
        if (result) {
          this.applyFiltersToQuotes(result);
        }
      },
      (error) => {
        // Error handling for opening the filter dialog
        console.error('Error opening the filter dialog:', error);
      }
    );
  }

  /**
   * Opens the filter dialog for procedures and applies selected filters.
   *
   * This method resets the procedures to their initial state, prepares the filter options,
   * and opens a dialog for filtering procedures. Filters are applied based on user selection
   * when the dialog is closed.
   */
  openFilterDialogProcedures(): void {
    // Reset the procedures data to its initial state before applying any filters
    this.dataTableProcedures = [...this.dataTableProceduresInit];

    // Prepare the states object for filtering
    this.armingStateObjectProcedures();

    // Open the filter dialog with appropriate options
    const dialogRef = this.dialog.open(FilterModalClientsComponent, {
      width: '400px',
      data: {
        isProcedure: true,
        // Remove duplicate procedures from the list for filtering
        procedure: this.removeDuplicate(this.onlyProcessOfProcedures),
        // Remove duplicate products from the list for filtering
        products: this.removeDuplicate(this.onlyProductOfProcedures),
        // Provide the states object for filtering
        states: this.armingStateObjectProcedures(),
      },
    });

    // After the dialog is closed, apply the selected filters
    dialogRef.afterClosed().subscribe(
      (result: CustomerFilterPruduct) => {
        if (result) {
          // Apply filters to procedures based on the user's selection
          this.applyFiltersToProcedures(result);
        }
      },
      (error) => {
        // Log any error that occurs while opening the filter dialog
        console.error('Error opening the filter dialog:', error);
      }
    );
  }

  /**
   * Applies filters to the quotes based on the selected criteria.
   *
   * This method filters the quotes data according to the provided filtering options, such as
   * product ID, quote number, and various quote states. The filtered results are updated in
   * the `dataTableQuotes` property.
   *
   * @param result - The object containing filtering criteria.
   */
  applyFiltersToQuotes(result: CustomerFilterPruduct) {
    // Filter by product ID
    if (result.product.id) {
      this.dataTableQuotes = this.dataTableQuotes.filter((res) => {
        return res.iProductId === result.product.id;
      });
    }

    // Filter by quote number
    if (result.quoteNumber !== 0) {
      this.dataTableQuotes = this.dataTableQuotes.filter((res) => {
        return res.iCustomerId === result.quoteNumber;
      });
    }

    // Filter by active status
    if (result.active) {
      this.dataTableQuotes = this.dataTableQuotes.filter((res) => {
        return res.vQuoteState.toUpperCase() === this.activeString;
      });
    }

    // Filter by inactive status
    if (result.inactive) {
      this.dataTableQuotes = this.dataTableQuotes.filter((res) => {
        return res.vQuoteState.toUpperCase() === this.inactiveString;
      });
    }

    // Filter by issued status
    if (result.emitida) {
      this.dataTableQuotes = this.dataTableQuotes.filter((res) => {
        return res.vQuoteState.toUpperCase() === this.issuedString;
      });
    }

    // Filter by not issued status
    if (result.sinEmitir) {
      this.dataTableQuotes = this.dataTableQuotes.filter((res) => {
        return res.vQuoteState.toUpperCase() === this.withoutEmitString;
      });
    }
  }

  /**
   * Applies filters to the procedures based on the selected criteria.
   *
   * This method filters the procedures data according to the provided filtering options, such as
   * product ID, procedure ID, and various procedure states. The filtered results are updated in
   * the `dataTableProcedures` property.
   *
   * @param result - The object containing filtering criteria.
   */
  applyFiltersToProcedures(result: CustomerFilterPruduct) {
    // Filter by product ID if specified
    if (result.product.id) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.iProductId === result.product.id;
      });
    }

    // Filter by procedure ID if specified
    if (result.procedure.id) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.iProcessId === result.procedure.id;
      });
    }

    // Filter by open status if specified
    if (result.abierto) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.vProcedureState.toUpperCase() === this.openString;
      });
    }

    // Filter by closed status if specified
    if (result.cerrado) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.vProcedureState.toUpperCase() === this.closedString;
      });
    }

    // Filter by in-course status if specified
    if (result.enCurso) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.vProcedureState.toUpperCase() === this.inCourseString;
      });
    }

    // Filter by active status if specified
    if (result.active) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.vProcedureState.toUpperCase() === this.activeString;
      });
    }

    // Filter by inactive status if specified
    if (result.inactive) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.vProcedureState.toUpperCase() === this.inactiveString;
      });
    }

    // Filter by issued status if specified
    if (result.emitida) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.vProcedureState.toUpperCase() === this.issuedString;
      });
    }

    // Filter by not issued status if specified
    if (result.sinEmitir) {
      this.dataTableProcedures = this.dataTableProcedures.filter((res) => {
        return res.vProcedureState.toUpperCase() === this.withoutEmitString;
      });
    }
  }

  /**
   * Assembles an array of unique procedure states from the procedures data.
   *
   * This method extracts all unique procedure states from the `dataTableProcedures` array,
   * eliminates duplicate states, and returns them as an array. This is achieved by using a
   * `Set` to remove duplicates and then converting it back to an array.
   *
   * @returns An array of unique procedure states.
   */
  armingStateObjectProcedures(): Array<string> {
    // Extracting all procedure states from the procedures data
    const allStates = this.dataTableProcedures.map((r) => r.vProcedureState);

    // Using a Set to remove duplicates and convert it back to an array
    return Array.from(new Set(allStates));
  }

  /**
   * Assembles an array of unique quote states from the quotes data.
   *
   * This method extracts all unique quote states from the `dataTableQuotes` array,
   * eliminates duplicate states, and returns them as an array. This is achieved by using a
   * `Set` to remove duplicates and then converting it back to an array.
   *
   * @returns An array of unique quote states.
   */
  armingStateObject(): Array<string> {
    // Extracting all quote states from the quotes data
    const allStates = this.dataTableQuotes.map((r) => r.vQuoteState);

    // Using a Set to remove duplicates and convert it back to an array
    return Array.from(new Set(allStates));
  }

  /**
   * Handles the click event to view product details.
   *
   * This method processes the click event to view details of a specific product. 
   *
   * @param evt - The event object containing information about the clicked product.
   */
  onClickSeeProduct(evt: IconEventClickModel) {
    
    evt.value.email = this.clientResponse?.vEmailUser ?? null;
    // Dispatching an event to set the product details in the client service
    this._clientService.setDataDetailProduct(evt.value);

    // Navigating to the route for viewing the product details
    this._customRouter.navigate([
      `dashboard/clients/detail/${this.idClient}/see-detail-product/${evt.value.productId}/`,
    ]);
  }

  /**
   * Handles the click event to view procedure details.
   *
   * This method processes the click event to view details of a specific procedure. 
   * applies any filters based on the user's input in the modal.
   *
   * @param evt - The event object containing information about the clicked procedure.
   */
  onClickSeeProcedure(evt: IconEventClickModel) {
    // Opening a modal dialog to show details of the selected procedure
    const dialogRef = this.dialog.open(ModalSeeDetailsComponent, {
      width: '60%',
      data: {
        isProcedure: true,
        iTask: evt.value.iTask,
        allData: evt.value,
      },
    });

    // Subscribing to the event when the dialog is closed
    // If the user provides filtering criteria, apply those filters to the procedures
    dialogRef.afterClosed().subscribe((result: CustomerFilterPruduct) => {
      if (result) {
        this.applyFiltersToProcedures(result);
      }
    });
  }

  /**
   * Handles the click event to view quote details.
   *
   * This method processes the click event to navigate to the quote detail view. It also dispatches
   * actions to update the application state, including the quote stage, edit mode, and selected quote ID.
   *
   * @param event - The event object containing information about the clicked quote.
   */
  onClickSeeQuote(event: IconEventClickModel) {
    // Navigating to the quote detail view with the specified client ID and quote ID
    this._customRouter.navigate([
      `dashboard/clients/detail/${this.idClient}/see-detail-quote/${event.value.iProductId}/1`,
    ]);

    // Dispatching an action to update the quote stage to 'Data'
    // This indicates that the current stage of the quote is data-related
    this._store.dispatch(changeStageQuote({ stage: QuoteStage.Data }));

    // Dispatching an action to set the edit mode to true
    // This indicates that the quote is being edited or viewed in edit mode
    this._store.dispatch(changeEdit({ isEdit: true }));

    // Dispatching an action to save the selected quote ID in the application state
    // This allows other parts of the application to access the currently selected quote ID
    this._store.dispatch(saveIdQuote({ idQuote: event.value.iQuoteNumber }));

  }
}
