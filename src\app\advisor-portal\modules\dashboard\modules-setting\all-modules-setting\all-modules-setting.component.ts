import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription, catchError, of } from 'rxjs';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { AllMenuModel } from 'src/app/shared/models/menu/all-menu.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-all-modules-setting',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    ChooseCountryAndCompanyComponent,
  ],
  templateUrl: './all-modules-setting.component.html',
  styleUrls: ['./all-modules-setting.component.scss'],
})
export class AllModulesSettingComponent implements OnInit, OnDestroy {
  modulesDataTable: AllMenuModel[] = [];
  estructModulesSettingTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'ModulesSetting.Modules.ModuleName'
      ),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant(
        'ModulesSetting.Modules.ModuleDate'
      ),
      columnValue: 'dCreationDate',
      functionValue: (item: any) => this._utilsSvc.formatDate(item.dCreationDate, 'DD-MM-YYYY')
    },
    {
      columnLabel: this._translateService.instant('Insurer.Edit'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
  ];
  formValid: boolean = false;
  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessCountry: number = 0;
  constructor(
    private _router: Router,
    private _settingService: SettingService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _modulesSettingService: ModulesSettingService,
    public _utilsSvc: UtilsService,
    private _customRouter: CustomRouterService

  ) {}

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table modules
      this.estructModulesSettingTable[0].columnLabel =
        this._translateService.instant('ModulesSetting.Modules.ModuleName');
      this.estructModulesSettingTable[1].columnLabel =
        this._translateService.instant('Insurer.Edit');
    });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessCountry =
                response.enterprise.pkIIdBusinessByCountry;
              this.modulesDataTable = [];
              this.getMenuList();
            }
          }
        }
      );
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this._customRouter.navigate([
          `/dashboard/modules-setting/edit/${event.value.pkIIdMenu}`,
        ]);
        break;
      default:
        break;
    }
  }

  getMenuList() {
    this._modulesSettingService
      .getAllParentMenuFromModules(this.idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.modulesDataTable = resp.result;
          }
        }
      });
  }

  gotToCreate() {
    this._customRouter.navigate(['/dashboard/modules-setting/new']);
  }

  validForm(event: boolean) {
    this.formValid = event;
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
