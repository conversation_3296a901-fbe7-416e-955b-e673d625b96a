import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { Subscription, filter } from 'rxjs';

@Component({
  selector: 'app-product-sections',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent, MatTooltipModule, MatIconModule],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" /> 
        {{"ProductSection.PlansSections" | translate}}
        <mat-icon *ngIf="isSpecialRoute" class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.PlansSectionTitle' | translate }}">help_outline</mat-icon>
        <mat-icon *ngIf="!isSpecialRoute" class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.PlansSectionTitle2' | translate }}">help_outline</mat-icon>
      </h2>
    </div>
     <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class ProductSectionsComponent implements OnInit, OnDestroy {
  constructor(
    private _translateService: TranslateService,
    private _router: Router
  ) {}
  
  inicio: string = this._translateService.instant("Inicio")  
  seccionesPlanes: string = this._translateService.instant("Secciones de planes")
  isSpecialRoute: boolean = true;
  private _routerSubscription!: Subscription;

  sections: {label: string, link: string}[]=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.seccionesPlanes, link: '/dashboard/product-sections'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Inicio")
      this.seccionesPlanes = this._translateService.instant("Secciones de planes")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.seccionesPlanes
    });

    this._routerSubscription = this._router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      const navEndEvent = event as NavigationEnd;
      this.checkRoute(navEndEvent.urlAfterRedirects);
    });
  }

  ngOnDestroy(): void {
    if (this._routerSubscription) {
      this._routerSubscription.unsubscribe();
    }
  }

  checkRoute(url: string): void {
    if (url === '/dashboard/product-sections' || url.includes('all-characteristics')) { 
      this.isSpecialRoute = true;
    } else {
      this.isSpecialRoute = false;
    }
  }
}
