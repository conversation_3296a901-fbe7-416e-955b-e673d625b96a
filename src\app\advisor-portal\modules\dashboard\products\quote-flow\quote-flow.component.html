<div class="container mt-5">
  <h4>
    <strong>{{ "QuoteFlow.QuoteFlowStages" | translate }}</strong>
  </h4>
  <p class="subtitle-text">
    {{
      "QuoteFlow.AllowsYouToDefineWhichStagesWillBeAvailableForTheQuoteFlow"
        | translate
    }}
  </p>

  <form [formGroup]="formSkip">
    <div class="mt-2">
      <div class="form-check mb-3">
        <input
          (change)="onSkipOffer($event, 'BSkipOffers')"
          class="form-check-input"
          type="checkbox"
          id="bSkipOffers"
          formControlName="bSkipOffers"
        />
        <label class="form-check-label" for="bSkipOffers">
          {{ "QuoteFlow.SkipOffer" | translate }}
        </label>
      </div>
    </div>
  </form>

  <form class="container-flow p-4 mt-2 mb-2" [formGroup]="formQuoteCommunication">
    <h6 class="mt-2">{{ "QuoteFlow.QuoteCommunicationQuote" | translate }}</h6>
    <div class="form-check mb-3">
      <input
        (click)="onUpdateEmailTemplate('bActive', 'Quote', $event)"
        class="form-check-input"
        type="checkbox"
        id="bActiveQuoteCommunication"
        formControlName="bActive"
      />
      <label class="form-check-label" for="bActiveQuoteCommunication">
        {{ "QuoteFlow.ActiveCommunicationQuote" | translate }}
      </label>
    </div>
    <div class="mt-2" *ngIf="formQuoteCommunication.get('bActive')?.value">
      <h6>{{ "QuoteFlow.SelectedTemplateQuote" | translate }}</h6>
      <select
          (change)="onUpdateEmailTemplate('FkIdTemplate', 'Quote', $event)"
          class="form-select"
          id="bPayQuoteFlowDataListSupplier"
          formControlName="FkIdTemplate"
      >
          <option *ngFor="let email of emailsTemplates" [value]="email.pkIIdTemplate">{{email.vName}}</option>
      </select>
  </div>
  </form>

  <div class="container-flow p-4">
    <form [formGroup]="form">
      <!-- Emission -->
      <div class="mt-2">
        <div class="form-check mb-3">
          <input
            (click)="onClickFlow('BQuoteFlowStageActive')"
            class="form-check-input"
            type="checkbox"
            id="bQuoteFlowStageActive"
            formControlName="bQuoteFlowStageActive"
          />
          <label class="form-check-label" for="bQuoteFlowStageActive">
            {{ "QuoteFlow.Emission" | translate }}
          </label>
        </div>
      </div>
      <hr class="mb-2">

      <div *ngIf="isbQuoteFlowStageActive">
        <!-- Visibility -->
        <div class="mt-2">
          <h6 class="mt-2">{{ "QuoteFlow.Visibility" | translate }}</h6>
          <div class="form-check mb-3">
            <input
              class="form-check-input"
              type="checkbox"
              id="bQuoteFlowVisibleClients"
              formControlName="bQuoteFlowVisibleClients"
              (click)="onClickFlow('BQuoteFlowVisibleClients')"
            />
            <label class="form-check-label" for="bQuoteFlowVisibleClients">
              {{ "QuoteFlow.VisibleClients" | translate }}
            </label>

            <input
              (click)="onClickFlow('BQuoteFlowVisibleAdvisors')"
              class="form-check-input ml-5"
              type="checkbox"
              id="bQuoteFlowVisibleAdvisors"
              formControlName="bQuoteFlowVisibleAdvisors"
            />
            <label class="form-check-label" for="bQuoteFlowVisibleAdvisors">
              {{ "QuoteFlow.VisibleAdvisors" | translate }}
            </label>
          </div>
        </div>
        <hr class="mb-2">

        <!-- Required login -->
        <div class="mt-2">
          <h6 class="mt-2">
            {{ "QuoteFlow.CustomerPortalLogin" | translate }}
            <span class="span"
              >({{
                "QuoteFlow.OnlyOneOfTheStagesOfTheFlowCanHaveLoginValidation"
                  | translate
              }})</span
            >
          </h6>
          <div class="form-check mb-3">
            <input
              (click)="onClickFlow('BQuoteFlowRequiredLogin')"
              class="form-check-input"
              type="checkbox"
              id="bQuoteFlowRequiredLogin"
              formControlName="bQuoteFlowRequiredLogin"
            />
            <label class="form-check-label" for="bQuoteFlowRequiredLogin">
              {{ "QuoteFlow.RequiresLogin" | translate }}
            </label>
          </div>
        </div>
        <hr class="mb-2">

        <!-- Data -->
        <div class="mt-2">
          <h6 class="mb-2">{{ "QuoteFlow.Integration" | translate }}</h6>
          <mat-radio-group formControlName="bQuoteFlowData">
            <!-- NOTA:  En el control "bQuoteFlowData" se guardará el valor true si la opción del radio seleccionada es "Manual" y false si la opción seleccionada es "Integración"-->
            <mat-radio-button class="margin-radioButton" [value]="true" (click)="onClickFlow('BQuoteFlowManualData')" >{{ "QuoteFlow.Manual" | translate }}</mat-radio-button>
            <mat-radio-button class="margin-radioButton" [value]="false" (click)="onClickFlow('BQuoteFlowDataIntegration')">{{ "QuoteFlow.Integration" | translate }}</mat-radio-button>
          </mat-radio-group>
          <div class="mt-2">
            @if(form.get('bQuoteFlowManualData')?.value || form.get('bQuoteFlowDataIntegration')?.value){
              <ng-container >
                  <h6>{{ "QuoteFlow.EmissionMessage" | translate }}</h6>
                  <!-- Título -->
                  <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{ "QuoteFlow.EmissionMessageTitle" | translate }}</mat-label>
                    <input matInput formControlName="vEmissionTitle" PreventionSqlInjector />
                  </mat-form-field>
                  <!-- Subtítulo -->
                  <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>{{ "QuoteFlow.EmissionMessageSubtitle" | translate }}</mat-label>
                    <input matInput formControlName="vEmissionSubtitle" PreventionSqlInjector />
                  </mat-form-field>
                  <!-- Incluir número de proceso en emisión -->
                   @if(form.get('bQuoteFlowManualData')?.value){
                    <ng-container >
                      <div class="form-check mb-3">
                        <input
                        class="form-check-input"
                        type="checkbox"
                        id="includeProcessNumber"
                        formControlName="includeProcessNumber"
                        (click)="onClickFlow('BIncludeProcessNumber')"
                        />
                        <label class="form-check-label" for="includeProcessNumber">
                        {{ "QuoteFlow.EmissionIncludeProcessNumber" | translate }}
                        </label>
                      </div>
                    </ng-container>
                  }
              </ng-container>
            }
          </div>
        </div>
        <hr class="mb-2">
        <h6 class="mt-2">{{ "QuoteFlow.QuoteCommunicationEmission" | translate }}</h6>
        <div class="form-check mb-3">
          <input
            (click)="onUpdateEmailTemplate('bActive', 'Emission', $event)"
            class="form-check-input"
            type="checkbox"
            id="bQuoteFlowCommunicationActive"
            formControlName="bQuoteFlowCommunicationActive"
          />
          <label class="form-check-label" for="bQuoteFlowCommunicationActive">
            {{ "QuoteFlow.ActiveCommunicationEmission" | translate }}
          </label>
        </div>
        <div class="mt-2" *ngIf="form.get('bQuoteFlowCommunicationActive')?.value">
          <h6>{{ "QuoteFlow.SelectedTemplateEmission" | translate }}</h6>
          <select
              (change)="onUpdateEmailTemplate('FkIdTemplate', 'Emission', $event)"
              class="form-select"
              id="bPayQuoteFlowDataListSupplier"
              formControlName="FkIdTemplate"
          >
              <option *ngFor="let email of emailsTemplates" [value]="email.pkIIdTemplate">{{email.vName}}</option>
          </select>
      </div>
      <hr class="mb-2">
      <div class="form-check mb-3">
        <input
              (click)="onClickFlow('BIsUrlCheck')"
              class="form-check-input"
              type="checkbox"
              id="bIsUrlCheck"
              formControlName="bIsUrlCheck"
            />
            <label class="form-check-label" for="bIsUrlCheck">
              {{ "QuoteFlow.UrlCheck" | translate }}
            </label>
      </div>
      <hr class="mb-2">

        <!-- Table -->
        <div class="row mb-2">
          <div class="col-12 col-md-12">
            <div class="row mb-2 mt-4">
              <h3 class="col-md-12">
                {{ "Product.Fields" | translate }}
              </h3>
            </div>

            <div style="background-color: white !important">

              <!-- Se llama conponente de formulario, modificado para ser reutilizado -->
              <app-step-form-data *ngIf="idForm != 0"
              [idFormInput]="idForm"
              [bFormActive]="form.get('b_Active')?.value"
              [vNameForm]="form.get('v_Name')?.value"
              [vNameFormDb]="this.form.get('v_NameDB')?.value">
            </app-step-form-data>
            </div>


          </div>
        </div>

        <div class="mt-2">
          <h6>{{ "QuoteFlow.IssueValidity" | translate }}</h6>
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{ "QuoteFlow.IssueValidity" | translate }}
            </mat-label>
            <input matInput formControlName="name" PreventionSqlInjector />
            <mat-hint>
              {{ "QuoteFlow.ValueInDays" | translate }}
            </mat-hint>
          </mat-form-field>
        </div>
      </div>
    </form>
  </div>

  <div class="container-flow mt-2 p-4">
    <form [formGroup]="formPagos">
      <!-- Payment -->
      <div class="mt-2">
        <div class="form-check mb-3">
          <input
            (click)="onClickPayment('bIsActive')"
            class="form-check-input"
            type="checkbox"
            id="bPayQuoteFlowStageActive"
            formControlName="bIsActive"
          />
          <label class="form-check-label" for="bPayQuoteFlowStageActive">
            {{ "QuoteFlow.Payment" | translate }}
          </label>
        </div>
      </div>


      <div *ngIf="isbPayQuoteFlowStageActive">
        <!-- Visibility -->
        <div class="mt-2">
          <h6>{{ "QuoteFlow.Visibility" | translate }}</h6>
          <div class="form-check mb-3">
            <input
              class="form-check-input"
              type="checkbox"
              id="bPayQuoteFlowVisibleClients"
              formControlName="bVisibleClients"
              (click)="onClickPayment('bVisibleClients')"
            />
            <label class="form-check-label" for="bPayQuoteFlowVisibleClients">
              {{ "QuoteFlow.VisibleClients" | translate }}
            </label>

            <input
              (click)="onClickPayment('bVisibleAdvisors')"
              class="form-check-input ml-5"
              type="checkbox"
              id="bPayQuoteFlowVisibleAdvisors"
              formControlName="bVisibleAdvisors"
            />
            <label class="form-check-label" for="bPayQuoteFlowVisibleAdvisors">
              {{ "QuoteFlow.VisibleAdvisors" | translate }}
            </label>
          </div>
        </div>

        <!-- Required login -->
        <div class="mt-2">
          <h6>
            {{ "QuoteFlow.CustomerPortalLogin" | translate }}
            <span class="span"
              >({{
                "QuoteFlow.OnlyOneOfTheStagesOfTheFlowCanHaveLoginValidation"
                  | translate
              }})</span
            >
          </h6>
          <div class="form-check mb-3">
            <input
              (click)="onClickPayment('bRequiredLogin')"
              class="form-check-input"
              type="checkbox"
              id="bPayQuoteFlowRequiredLogin"
              formControlName="bRequiredLogin"
            />
            <label class="form-check-label" for="bPayQuoteFlowRequiredLogin">
              {{ "QuoteFlow.RequiresLogin" | translate }}
            </label>
          </div>
        </div>

        <!-- Data -->
        <div class="mt-2">
          <h6>{{ "QuoteFlow.Data" | translate }}</h6>

          <div class="col-12 col-md-12">
            <mat-radio-group formControlName="bManualData" (change)="onClickPayment('bManualData')" class="mb-2">
              <mat-radio-button class="margin-radioButton" [value]="false">{{'QuoteFlow.Manual' | translate}}</mat-radio-button>
              <mat-radio-button class="margin-radioButton" [value]="true">{{'QuoteFlow.Automatic' | translate}}</mat-radio-button>
            </mat-radio-group>
          </div>
          <div class="col-12 col-md-12">
            <mat-radio-group formControlName="bEmbeddedGateway" (change)="onClickPayment('bEmbeddedGateway')" class="mb-2">
              <mat-radio-button class="margin-radioButton" [value]="false">{{'QuoteFlow.ExternalGateway' | translate}}</mat-radio-button>
              <!--<mat-radio-button class="margin-radioButton" [value]="true">{{'QuoteFlow.EmbeddedGateway'}}</mat-radio-button>-->
            </mat-radio-group>
          </div>
        </div>


        <!-- Proveedor -->
        <div class="mt-2" *ngIf="!formPagos.get('bEmbeddedGateway')?.value">
          <h6>Proveedor</h6>
          <select
              (change)="onClickPayment('fkIIdGatewayConfig')"
              class="form-select"
              id="bPayQuoteFlowDataListSupplier"
              formControlName="fkIIdGatewayConfig"
          >
              <option *ngFor="let config of gatewayConfigs" [value]="config.pkGatewayConfig">{{config.vName}}</option>
          </select>
      </div>

      <div class="mt-2">
        <h6>{{ "QuoteFlow.Button" | translate }}</h6>
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{ "QuoteFlow.ButtonText" | translate }}
          </mat-label>
          <input matInput formControlName="vButtonText" PreventionSqlInjector />
        </mat-form-field>
      </div>
      <div class="mt-2">
        <h6>{{ "QuoteFlow.Disclaimer" | translate }}</h6>
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{ "QuoteFlow.DisclaimerText" | translate }}
          </mat-label>
          <input matInput formControlName="vDisclaimerText" PreventionSqlInjector />
        </mat-form-field>
      </div>

      <div class="form-check mb-3">
        <input
          (click)="onClickPayment('bActiveDisclaimerText')"
          class="form-check-input"
          type="checkbox"
          id="bActiveDisclaimerText"
          formControlName="bActiveDisclaimerText"
        />
        <label class="form-check-label" for="bActiveDisclaimerText">
          {{ "QuoteFlow.ActiveDisclaimerLink" | translate }}
        </label>
      </div>

      <div class="form-check" *ngIf="formPagos.get('bActiveDisclaimerText')?.value">
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{ "QuoteFlow.TextDisclaimerLink" | translate }}
          </mat-label>
          <input matInput formControlName="vTextDisclaimerLink" PreventionSqlInjector />
        </mat-form-field>
      </div>

      <div class="form-check mb-3" *ngIf="formPagos.get('bActiveDisclaimerText')?.value">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{ "QuoteFlow.DisclaimerLink" | translate }}
            </mat-label>
            <input matInput formControlName="vLinkDisclaimer" />
          </mat-form-field>
      </div>

      <div class="form-check mb-3">
        <input
          (click)="onClickPayment('bActiveProductCertificate')"
          class="form-check-input"
          type="checkbox"
          id="bActiveProductCertificate"
          formControlName="bActiveProductCertificate"
        />
        <label class="form-check-label" for="bActiveProductCertificate">
          {{ "QuoteFlow.ActiveProductCertificate" | translate }}
        </label>
      </div>

      <h6 class="mt-2">{{ "QuoteFlow.QuoteCommunicationPayment" | translate }}</h6>
        <div class="form-check mb-3">
          <input
            (click)="onUpdateEmailTemplate('bActive', 'Payment', $event)"
            class="form-check-input"
            type="checkbox"
            id="bActiveCommunicationPayment"
            formControlName="bPaymentCommunicationActive"
          />
          <label class="form-check-label" for="bActiveCommunicationPayment">
            {{ "QuoteFlow.ActiveCommunicationPayment" | translate }}
          </label>
        </div>
        <div class="mt-2" *ngIf="formPagos.get('bPaymentCommunicationActive')?.value">
          <h6>{{ "QuoteFlow.SelectedTemplatePayment" | translate }}</h6>
          <select
              (change)="onUpdateEmailTemplate('FkIdTemplate', 'Payment', $event)"
              class="form-select"
              id="FkTemplatePayment"
              formControlName="fkIIdEmailTemplateCommunicationPayment"
          >
              <option *ngFor="let email of emailsTemplates" [value]="email.pkIIdTemplate">{{email.vName}}</option>
          </select>
        </div>
        <div class="mt-4">
          <h6>{{ "QuoteFlow.CallBack" | translate }}</h6>

          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{ "QuoteFlow.header" | translate }}
            </mat-label>
            <input matInput formControlName="vHeader" />
          </mat-form-field>
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{ "QuoteFlow.tittle" | translate }}
            </mat-label>
            <input matInput formControlName="vTittle" />
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{ "QuoteFlow.subtittle" | translate }}
            </mat-label>
            <input matInput formControlName="vSubTittle" />
          </mat-form-field>

          <div class="form-check mb-3">
            <input
            (click)="onClickPayment('bActiveCoverageCertificate')"
              class="form-check-input"
              type="checkbox"
              id="bActiveCoverageCertificate"
              formControlName="bActiveCoverageCertificate"
            />
            <label class="form-check-label" for="bActiveCoverageCertificate">
              {{ "QuoteFlow.ActiveCoverageCertificate" | translate }}
            </label>
          </div>

          <div class="form-check mb-3">
            <input
            (click)="onClickPayment('bActivePolicyDowmload')"
              class="form-check-input"
              type="checkbox"
              id="bActivePolicyDowmload"
              formControlName="bActivePolicyDowmload"
            />
            <label class="form-check-label" for="bActivePolicyDowmload">
              {{ "QuoteFlow.ActivePolicyDowmload" | translate }}
            </label>
          </div>

          <div class="form-check mb-3">
            <input
            (click)="onClickPayment('bActiveContinueShopping')"
              class="form-check-input"
              type="checkbox"
              id="bActiveContinueShopping"
              formControlName="bActiveContinueShopping"
            />
            <label class="form-check-label" for="bActiveContinueShopping">
              {{ "QuoteFlow.ActiveContinueShopping" | translate }}
            </label>
          </div>

          <div class="form-check mb-3">
            <input
            (click)="onClickPayment('bActiveCoverageVisualization')"
              class="form-check-input"
              type="checkbox"
              id="bActiveCoverageVisualization"
              formControlName="bActiveCoverageVisualization"
            />
            <label class="form-check-label" for="bActiveCoverageVisualization">
              {{ "QuoteFlow.ActiveCoverageVisualization" | translate }}
            </label>
          </div>

          <div class="d-flex gap-2">
            <div class="form-check mb-3">
              <input
              (click)="onClickPayment('bActiveStartValidity')"
                class="form-check-input"
                type="checkbox"
                id="bActiveStartValidity"
                formControlName="bActiveStartValidity"
              />
              <label class="form-check-label" for="bActiveStartValidity">
                {{ "QuoteFlow.ActiveStartValidity" | translate }}
              </label>
            </div>
  
            <div class="form-check mb-3">
              <input
              (click)="onClickPayment('bActiveEndValidity')"
                class="form-check-input"
                type="checkbox"
                id="bActiveEndValidity"
                formControlName="bActiveEndValidity"
              />
              <label class="form-check-label" for="bActiveEndValidity">
                {{ "QuoteFlow.ActiveEndValidity" | translate }}
              </label>
            </div>
          </div>

        </div>
      </div>
    </form>
  </div>
</div>
