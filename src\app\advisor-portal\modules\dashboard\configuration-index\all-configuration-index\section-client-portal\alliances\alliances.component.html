<div class="row mt-5">
    <h5 class="fw-bold mb-2">
        {{ 'SectionClientPortal.AllianceTitle' | translate }}
      </h5>
      <mat-label>
        {{ 'SectionClientPortal.AllianceText' | translate }}
      </mat-label>
    <div class="col-4" *ngFor="let alliance of aliances">
        <app-generic-image-picker
            class="col-12 col-md-4"
            [title]="'SectionClientPortal.AllianceIconTitle'| translate"
            [description]="'SectionClientPortal.AllianceIconText'| translate"
            (changeFile)="changeImage($event)"
            [disabled]="true"
            [imageSrc]="alliance.imageSrc">
        </app-generic-image-picker>
        <div class="d-flex justify-content-center">
          <button mat-raised-button color="primary" type="button" class="" (click)="deleteAlliance(alliance.pkIdAlliance)">
            {{ "Delete" | translate }}
          </button>
        </div>
    </div>
    <div class="row mt-5">
      <div class="col">
        <button
          type="button"
          mat-raised-button
          color="primary"
          (click)="openModal()"
        >
          {{ "SectionClientPortal.AddAlliance" | translate }}
        </button>
      </div>
    </div>
</div>

<ng-template #createEditAlliance>
  <app-modal2 [titleModal]="(pkAllianceToModify === 0 ? 'SectionClientPortal.CreateAllianceTitle' : 'SectionClientPortal.UpdateAllianceTitle') | translate">
    <ng-container body>
      <form [formGroup]="formAlliance">
        <app-generic-image-picker
          class="col-12 col-md-4"
          [title]="'SectionClientPortal.AllianceIconTitle'| translate"
          [description]="'SectionClientPortal.AllianceIconText'| translate"
          (changeFile)="changeImage($event)"
          [imageSrc]="imageSrc">
      </app-generic-image-picker>

      </form>
    </ng-container>
    <ng-container customButtonRight>
      <div class="modal-footer">
        <button mat-raised-button color="primary" type="button" class="" (click)="saveAlliance()">
          {{ "Save" | translate }}
        </button>
      </div>
    </ng-container>
  </app-modal2>
</ng-template>