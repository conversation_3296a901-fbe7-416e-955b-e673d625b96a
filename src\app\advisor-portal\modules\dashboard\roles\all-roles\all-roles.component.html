<div class="mb-5">
  <mat-tab-group class="custom-tab-group" [selectedIndex]="defaultTabIndex" (selectedIndexChange)="onTabChanged($event)">
    <mat-tab label="{{ 'Role.AdvisorPortalTitle' | translate }}">
      <ng-container *ngIf="activeTab === 0">
        <app-portal-roles
          [clientPortal]="false">
        </app-portal-roles>
      </ng-container>
    </mat-tab>
    <mat-tab label="{{ 'Role.ClientPortalRitle' | translate }}">
      <ng-container *ngIf="activeTab === 1">
        <app-portal-roles
          [clientPortal]="true">
        </app-portal-roles>
      </ng-container>
    </mat-tab>
  </mat-tab-group>
</div>
