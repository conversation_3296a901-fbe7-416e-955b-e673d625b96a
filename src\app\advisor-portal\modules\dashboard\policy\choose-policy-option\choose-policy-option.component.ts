import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { OptionListMassiveModel } from 'src/app/shared/models/massive';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';

@Component({
  selector: 'app-choose-policy-option',
  standalone: true,
  imports: [CommonModule, TranslateModule, MatIconModule],
  templateUrl: './choose-policy-option.component.html',
  styleUrls: ['./choose-policy-option.component.scss'],
})
export class ChoosePolicyOptionComponent {
  idBusinessByCountry: number = 0;
  optionList: OptionListMassiveModel[] = [
    {
      name: this._translateService.instant('Policy.PolicyAlerts'),
      icon: 'notification_important',
      id: 3,
      bActive: true,
    },
  ];

  constructor(
    private _settingService: SettingService,
    private _customeRouter: CustomRouterService,
    private _translateService: TranslateService,
    private _transactionService: TransactionService
  ) {}

  async ngOnInit() {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      // Mapa de traducción
      const translationMap: { [key: number]: string } = {
        1: 'Policy.IndividualPolicies',
        2: 'Policy.collectiveAndIndividualCollectivePolicies',
        3: 'Policy.PolicyAlerts',
      };

      // Traducción del array de opciones.
      this.optionList.forEach((option) => {
        if (option && translationMap[option.id]) {
          option.name = this._translateService.instant(
            translationMap[option.id]
          );
        }
      });
    });
    await this.getDataSettingInit();
  }

  //Obtiene la información de empresa páis seleccionada despúes del inicio de sesión.
  async getDataSettingInit() {
    let data: any = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = data.idBusinessByCountry;
    this.getIsHasPolicies();
  }

  //Función que dependiendo el tipo de opción escogida, redirige según sea el caso.
  goToOption(optionId: number) {
    switch (optionId) {
      case 1:
        this._customeRouter.navigate([
          `dashboard/policy/individual-policy/${this.idBusinessByCountry}`,
        ]);
        break;
      case 2:
        this._customeRouter.navigate([
          `dashboard/policy/colective-individual-policy/${this.idBusinessByCountry}`,
        ]);
        break;
      case 3:
        break;
      default:
        break;
    }
  }

  //Verifica si existen pólizas individuales, colectivas o individuales colectivas para una empresa páis.
  getIsHasPolicies() {
    this._transactionService
      .getIsHasPolicies()
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          const optionIndividualPolicy = {
            name: this._translateService.instant('Policy.IndividualPolicies'),
            icon: 'text_snippet',
            id: 1,
            bActive: true,
          };

          const optionIndividualColectivePolicy = {
            name: this._translateService.instant(
              'Policy.collectiveAndIndividualCollectivePolicies'
            ),
            icon: 'file_copy',
            id: 2,
            bActive: true,
          };
          if (resp.result.isHasInvidual) {
            //TODO: Si hay pólizas individuales, añadimos esta opción en el menú.
            this.optionList.push(optionIndividualPolicy);
          }

          //TODO: Si hay pólizas colectivas o inidivuduales colectivas, añadimos esta opción en el menú.
          if (resp.result.isHasColective) {
            this.optionList.push(optionIndividualColectivePolicy);
          }
          //Ordenamos por id, para que las opciones, se muestren en el orden especificado en el REQ.
          this.optionList.sort((a, b) => a.id - b.id);
        }
      });
  }
}
