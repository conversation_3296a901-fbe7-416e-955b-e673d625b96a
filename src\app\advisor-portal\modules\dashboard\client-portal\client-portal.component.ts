import { Component,OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
@Component({
  selector: 'app-client-portal',
  standalone: true,
  imports:[CommonModule, RouterModule,TranslateModule,BreadcrumbComponent, MatIconModule, MatTooltipModule],
  template: `
  <div class="title">
    <h2 class="h3">
      <img src="assets/img/layouts/config_ico.svg" alt="" /> 
      {{"configLogin.LoginSettings" | translate}}
      <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.HomeConfiguration' | translate }}">help_outline</mat-icon>
    </h2>
  </div>
  <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

  <router-outlet></router-outlet>
`,
  styleUrls: []
})
export class ClientPortalComponent  implements OnInit{
  constructor(
    private _translateService: TranslateService,
  ) {}
   //busca el valor segun el idioma para el breadcrum
   inicio: string = this._translateService.instant("Inicio");
   configuracionInicio: string = this._translateService.instant("configLogin.LoginSettings")
 
   //llena el breadcrum
   sections: {label: string, link: string}[]=[
     {label: this.inicio, link: '/dashboard'},
     {label: this.configuracionInicio, link: '/dashboard/configurationClientPortal'}
   ];
   ngOnInit(): void {
    //traduce en caso de cambiar el idioma ya dentro de la pantalla
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Inicio")
      this.configuracionInicio = this._translateService.instant("configLogin.LoginSettings")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.configuracionInicio
    });
  }
}
