import { Routes } from '@angular/router';
import { ClientPortalComponent } from './client-portal.component'

export default [
    {
        path: '',
        component: ClientPortalComponent,
        children: [
            {
                path: '',
                loadComponent: () =>
                    import('./all-configuration-portal/all-configuration-portal.component').then(
                        (c) => c.AllConfigurationPortalComponent
                    ),
            },
            // {
            //     path: 'all-tops',
            //     loadComponent: () =>
            //         import('./all-configuration-index/tops/all-tops/all-tops.component').then(
            //             (c) => c.AllTopsComponent
            //         ),
            // },
            // {
            //     path: 'new',
            //     loadComponent: () =>
            //         import('./all-configuration-index/active-news/edit-active-news/edit-active-news.component').then(
            //             (c) => c.EditActiveNewsComponent
            //         ),
            // },
            // {
            //     path: 'edit/:pkIIdNews',
            //     loadComponent: () =>
            //         import('./all-configuration-index/active-news/edit-active-news/edit-active-news.component').then(
            //             (c) => c.EditActiveNewsComponent
            //         ),
            // },
        ]
    },
] as Routes;