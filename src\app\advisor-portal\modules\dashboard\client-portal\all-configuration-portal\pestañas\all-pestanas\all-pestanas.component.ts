import { CommonModule } from '@angular/common';
import { Component,OnInit,Input,ChangeDetectorRef,TemplateRef,ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { TranslateModule, TranslateService, LangChangeEvent } from '@ngx-translate/core';
import { PestanaService } from 'src/app/shared/services/pestana/pestana.service';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonModule } from '@angular/material/button';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { lastValueFrom, Subscription,of,catchError } from 'rxjs';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';


@Component({
  selector: 'app-all-pestanas',
  standalone: true,
  imports: [ 
    MatInputModule,
    MatSelectModule,
    TableComponent,
    TranslateModule,
    Modal2Component,
    ReactiveFormsModule,
    MatRadioModule,
    CommonModule,
    MatButtonModule,
    PreventionSqlInjectorDirective,
    MatSlideToggleModule,

  ],
  templateUrl: './all-pestanas.component.html',
  styleUrls: ['./all-pestanas.component.scss']
})
export class AllPestanasComponent implements OnInit, OnChanges{
  @Input() idBusinessByCountry: number = 0;
  _currentModal: MatDialogRef<any> | null = null;

  dataTablePestana: any[] = [];
  dataTableSubPestana: any[] = [];
  orderList: any[]=[];
  ordersubList: any[]=[];

  pestana: any[]= [];
  selectedPestana: any = null;
  pkPestanaToModify: number = 0;
  pkSubPestanaToModify: number = 0;

  titleModalPestana: string = this._translateService.instant('configurationClientPortal.CreatePestanaTitle');
  titleModalSubPestana: string = this._translateService.instant('configurationClientPortal.CreateSubPestanaTitle');

  formPestana: FormGroup = this._fb.group({
    bActive: [true],
    iAssociated: [0, Validators.required],
    vDescription: ['', Validators.required],
    iOrder: [0, Validators.required],
    FkIIdProduct: [0],
    FkIIdModule: [0],
    vLink: ['', Validators.pattern('https?://.+')],
    IdMenu: [0],
    vTag: [],
    fkIdProcess: [],
    IdFile: [],
    binAdvisorPortal: [false],
    binCustomerPortal: [false],
    bLoginRequired: [false],
  })
  formSubPestana: FormGroup = this._fb.group({
    bActive: [true],
    iAssociated: [null],
    vDescription: ['', Validators.required],
    iOrder: [null, Validators.required],
    iParent: [null, Validators.required],
    FkIIdProduct: [null],
    vLink: [null, Validators.pattern('https?://.+')],
    bIsTab: true

  })
  imageSrc: string = ''
  @ViewChild('createEditPestana') createEditPestana?: TemplateRef<any>;
  @ViewChild('createEditSubPestana') createEditSubPestana?: TemplateRef<any>;

  formSubs?: Subscription;
  formSubsp?: Subscription;


  constructor(
    private _pestanaService: PestanaService,
    private _cdr: ChangeDetectorRef,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _fileService: FileService,
    private _fb: FormBuilder,
    private _modalDialog: MatDialog,
    public utilsSvc: UtilsService,
    private _spinnerService: SpinnerService,
    private _productServices: ProductService,
    private _settingsService: ModulesSettingService
  ){
  }
  estructTablePestana: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.NamePestana'),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Active'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
      hidenIcon: (row: any) => row.iOrder == 0
    },
  ];
  estructTableSubPestana: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.NameSubPestana'),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Active'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),

    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifySub',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteSub',
      columnIcon: 'delete',
    },
  ];
  Listproducts: any[] = []
  ListModulos: any[] = []

  selectedService: any = null;
  showQuotation: boolean = true;


  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      /// traduction title modal
      this.titleModalPestana = this._translateService.instant(
        'configurationClientPortal.CreatePestanaTitle'
      );
      this.titleModalSubPestana = this._translateService.instant(
        'configurationClientPortal.CreateSubPestanaTitle'
      );

      //traduction data table pestañas
      this.estructTablePestana[0].columnLabel = this._translateService.instant(
        'configurationClientPortal.Orden'
      );
      this.estructTablePestana[1].columnLabel = this._translateService.instant(
        'configurationClientPortal.NamePestana'
      );
      this.estructTablePestana[2].columnLabel = this._translateService.instant(
        'configurationClientPortal.Active'
      );
      this.estructTablePestana[3].columnLabel =
        this._translateService.instant('Modify');
      this.estructTablePestana[4].columnLabel =
        this._translateService.instant('Delete');

      //traduction data table pestañas
      this.estructTableSubPestana[0].columnLabel =
        this._translateService.instant('configurationClientPortal.Orden');
      this.estructTableSubPestana[1].columnLabel =
        this._translateService.instant(
          'configurationClientPortal.NameSubPestana'
        );
      this.estructTableSubPestana[2].columnLabel =
        this._translateService.instant('configurationClientPortal.Active');
      this.estructTableSubPestana[3].columnLabel =
        this._translateService.instant('Modify');
      this.estructTableSubPestana[4].columnLabel =
        this._translateService.instant('Delete');
    });

    this._loadPestanas();
    this.GetProductsByBusinessCountryHome(this.idBusinessByCountry);
    this.GetModulsByBusinessCountryHome(this.idBusinessByCountry);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if(changes['idBusinessByCountry']){
      this.GetProductsByBusinessCountryHome(this.idBusinessByCountry);
      this.GetModulsByBusinessCountryHome(this.idBusinessByCountry);
    }
  }

  public getAvailableModules(){
    return this.ListModulos.filter(m => m.bIsToClientPortal == true)
  }
  
  private _loadPestanas() {
    this._pestanaService.getPestana(this.idBusinessByCountry).subscribe({
      next: (resp) => {
        if (!resp.error) {
          this.dataTablePestana = resp.result.filter((item : any) => item.iParent === null);
  
          if (this.dataTablePestana.length === 0 || !this.dataTablePestana.some(item => item.vTag === '/home')) {
            this._crearPestanaInicio();
          }
          this.dataTablePestana = this.dataTablePestana.map(item => ({
            ...item,
            iOrder: item.vTag === '/home' ? '' : item.iOrder
          }));
  
          this.dataTablePestana.sort((a: any, b: any) => {
            return (a.iOrder === '' || a.iOrder === 0 ? -1 : 1) - (b.iOrder === '' || b.iOrder === 0 ? -1 : 1);
          });
  
          const filteredResult = this.dataTablePestana.filter(item => item.vTag !== '/home');
          this.orderList = this.utilsSvc.generarArrayOrderList(filteredResult.length > 0 ? filteredResult.length + 1 : 1);
  
          this.pestana = resp.result.filter((item: any) => item.vTag !== '/home' && item.iParent === null);
          this.pkPestanaToModify = 0;
          this._cdr.detectChanges();
        }
      }
    });
  }
  private _crearPestanaInicio() {
    this.formPestana.patchValue({
      bActive: true,
      vDescription: 'Inicio',
      iOrder: 0,
      vLink: null,
      vTag: '/home'
    });
    this.pkPestanaToModify = 0;
    this.savePestana();
  }
  loadSubPestana(selectedValue: number) {
    this._pestanaService.getSubPestana(selectedValue).subscribe({
      next: (resp) => {
        if (!resp.error) {
          this.dataTableSubPestana = [...resp.result];
          if (resp.result.length > 0) {
            this.ordersubList = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.ordersubList = this.utilsSvc.generarArrayOrderList(1);
          }
          this._cdr.detectChanges();
        } else {
          this.dataTableSubPestana = [];
        }
      },
      error: (err) => {
        this.dataTableSubPestana = []; 
      }
    });
  }
  controller(evt: IconEventClickModel) {
    if (evt.column === 'delete')
        {
          this.validatedExistSubPestaña(evt.value.pkIIdMenu);
        }
        if (evt.column === 'deleteSub')
          {
            this._pestanaService.deletePestana(evt.value.pkIIdMenu).subscribe({
                next: (response) => {
                  if (!response.error) {
                    this.clearSubPestañas();
                    this.loadSubPestana(this.selectedPestana);                   
                    this._messageService.messageSuccess(
                      this._translateService.instant('MessageGenericDelete'),
                      ""
                    );
                  }
                },
                error: (error) => {
                  this._messageService.messageInfo(
                    this._translateService.instant('ThereWasAError'),
                    error
                  );
                },
              })
          }
    if (evt.column === 'modify')
      {
        this.pkPestanaToModify = evt.value.pkIIdMenu
        this.formPestana.patchValue({
          bActive: evt.value.bActive,
          vDescription: evt.value.vDescription,
          iOrder: evt.value.iOrder,
          FkIIdProduct: evt.value.fkIIdProduct,
          FkIIdModule: evt.value.fkIIdModuls,
          iModule: evt.value.pkIIdMenu,
          vLink: evt.value.vLink,
          iAssociated: evt.value.iAssociated,
          vTag: evt.value.vTag,
          binAdvisorPortal: evt.value.bIsToAdvisorPortal,
          binCustomerPortal: evt.value.bIsToClientPortal,
          bLoginRequired: evt.value.bNeedsLogin,
          fkIdProcess: evt.value.fkIdProcess,
          IdFile: evt.value.fkIIdFile
        })    
        this.openModal()
      }   
      if (evt.column === 'modifySub')
        {
          this.pkSubPestanaToModify = evt.value.pkIIdMenu
          this.formSubPestana.patchValue({
            bActive: evt.value.bActive,
            vDescription: evt.value.vDescription,
            iOrder: evt.value.iOrder,
            iParent: evt.value.iParent,
            FkIIdProduct: evt.value.fkIIdProduct,
            vLink: evt.value.vLink,
            iAssociated: evt.value.iAssociated  
          })
          this.openSubModal()
        } 
  }
  openModal(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    if (this.pkPestanaToModify == 0)
      this.titleModalPestana = this._translateService.instant(
        'configurationClientPortal.CreatePestanaTitle'
      );
    else
      this.titleModalPestana = this._translateService.instant(
        'configurationClientPortal.UpdatePestanaTitle'
      );

    this._currentModal = this._modalDialog.open(this.createEditPestana!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.clearPestañas();
      })
    })
  }

  openSubModal(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };

    if (this.pkSubPestanaToModify == 0)
      this.titleModalSubPestana = this._translateService.instant(
        'configurationClientPortal.CreateSubPestanaTitle'
      );
    else
      this.titleModalSubPestana = this._translateService.instant(
        'configurationClientPortal.UpdateSubPestanaTitle'
      );

    this._currentModal = this._modalDialog.open(this.createEditSubPestana!, sizeConfiguration);
    this.formSubsp = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
       this.clearSubPestañas();
      })
    })
  }

  async savePestana(){
    if(this.formPestana.value.vTag != "/home"){
      if (this.formPestana.invalid)
        return this.formPestana.markAllAsTouched()
    }else{
      this.formPestana.value.iOrder = 0
    }
    let data = {
      ...this.formPestana.value,
      IdBusinessCountry: this.idBusinessByCountry,
      bIsTab: true,
    }
    if (this.pkPestanaToModify === 0)
      this._pestanaService.createPestana(data).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadPestanas()
            this._messageService.messageSuccessSimple(
              this._translateService.instant('DataSavedSuccessfully'),
            );
          }else{

          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );      
        }
      })
    else{
      data.IdMenu = this.pkPestanaToModify
      this._pestanaService.modifyPestana(
        this.pkPestanaToModify, 
        data
      ).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadPestanas()
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              ""
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    }
    this.closeModal()
  }
  async saveSubPestana(){
    if (this.formSubPestana.invalid)
      return this.formSubPestana.markAllAsTouched()
    let data = {
      ...this.formSubPestana.value,
      IdBusinessCountry: this.idBusinessByCountry,
      iParent:  this.selectedPestana,
    }
    if (this.pkSubPestanaToModify === 0)
      this._pestanaService.createPestana(data).subscribe({
        next: (response) => {
          if (!response.error) {
            this.loadSubPestana(this.selectedPestana)
            this._messageService.messageSuccessSimple(
              this._translateService.instant('DataSavedSuccessfully'),
            );
          }else{
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );      
        }
      })
    else{
      data.IdMenu = this.pkSubPestanaToModify
      this._pestanaService.modifyPestana(
        this.pkSubPestanaToModify, 
        data
      ).subscribe({
        next: (response) => {
          if (!response.error) {
            this.loadSubPestana(this.selectedPestana)
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              ""
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    }
    this.closeModal()
  }
  closeModal() {
    if (this._currentModal) {
      this._currentModal.close();
      this._currentModal = null;
      this.formSubs?.unsubscribe();
    }
  }
  onPestanaChange(value: any): void {
    this.selectedPestana = value;
    if (value) {
      this.loadSubPestana(this.selectedPestana)
    }
  }
  async GetProductsByBusinessCountryHome(empresa: number) {
    this._spinnerService.showChanges(true);
    await lastValueFrom(
      this._productServices.GetProductsByBusinessCountryHomeCP(empresa)
    )
      .then((res) => {
        this.Listproducts = res.result;
      })
      .catch((r) => {
        this._spinnerService.showChanges(false);
      });

    this.Listproducts = this.Listproducts.map((r) => {
      if (r.vFilePath) {
        let arr = r.vFilePath.split('.');
        r.newSrc = 'data:image/' + arr[1] + ';base64,' + r.imageBase64;
      }
      return r;
    });

    if (this.Listproducts.length > 0) {
      this.selectproductService(this.Listproducts[0]);
    }

    this._spinnerService.showChanges(false);

    if (this.Listproducts.length > 0) {
      this.selectproductService(this.Listproducts[0]);
    }
  }
  async GetModulsByBusinessCountryHome(empresa: number) {
    this._settingsService
    .getAllParentMenuFromModules(empresa)
    .pipe(
      catchError((error) => {
        if (error.error.error) {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
        }
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
      } else {
        if (resp.error) {
        } else {
          this.ListModulos = resp.result;

        }
      }
    });
  }
  selectproductService(service: any) {
    this.selectedService = service;
    this.showQuotation = false;
    setTimeout(() => {
      this.showQuotation = true;
    }, 1);
  }
  isDeletable(item: any): boolean {
    return item.vDescription !== 'Inicio'; 
  }


  validatedExistSubPestaña(idMenu:number){
    this._pestanaService.getSubPestana(idMenu).subscribe({
      next: (resp) => {
        if (!resp.error) {          
          this.dataTableSubPestana = [...resp.result];
          if (resp.result.length > 0) {
            return this._messageService.messageInfo(
              this._translateService.instant(
                'FormConfiguration.deletedMessageTitle'
              ),
              this._translateService.instant(
                'FormConfiguration.deletedMessageSubtitle'
              )
            );
          } else {
            this.deletePestañas(idMenu);
          }
          this._cdr.detectChanges();
        } 
      },
      error: (err) => {
        return of([]);
      }
    });    
  }

  deletePestañas(idMenu:number){
    this._pestanaService.deletePestana(idMenu).subscribe({
      next: (response) => {
        if (!response.error) {
          this._loadPestanas()
          this._messageService.messageSuccess(
            this._translateService.instant('MessageGenericDelete'),
            ""
          );
        }
      },
      error: (error) => {
        this._messageService.messageInfo(
          this._translateService.instant('ThereWasAError'),
          error
        );
      },
    })
  }


  clearSubPestañas(){
    this.formSubPestana.reset();
    this.formSubPestana.get('bActive')?.setValue(true);
    this.pkSubPestanaToModify = 0;
  }

  clearPestañas(){
    this.formPestana.reset();
    this.formPestana.get('IdMenu')?.setValue(0);
    this.formPestana.get('bActive')?.setValue(true);
    this.formPestana.get('bLoginRequired')?.setValue(false);
    this.formPestana.get('binAdvisorPortal')?.setValue(false);
    this.formPestana.get('binCustomerPortal')?.setValue(false);
    this.formPestana.get('iAssociated')?.setValue(0);
    this.pkPestanaToModify = 0; 
  }
  
}
