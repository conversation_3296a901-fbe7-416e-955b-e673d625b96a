<form [formGroup]="category">
    <div *ngIf="true" class="row mb-2">
        <div class="col-12 col-md-12">
          <div class="row mb-2">
            <h3 class="col-md-12">
              {{ "Plan.Category" | translate }}
            </h3>
          </div>
          <app-table [displayedColumns]="estructTable" [data]="dataTablecategory" (iconClick)="controller($event)"></app-table>
          <button class="mx-3 mb-2" type="button" mat-raised-button color="primary"
            (click)="open('editNewTabModal'); isEditingcategory = false">
            {{ "Add" | translate }} {{" "}} {{ "Category" | translate }}
          </button>
        </div>
      </div>
</form>

<ng-template #editNewTabModal>
    <app-modal2 [showCancelButton]="true" [titleModal]="
      isEditingcategory 
      ?('Modify' | translate) + ' ' + ('Category' | translate)
      : ('AddCategory' | translate)">
      <ng-container body>

        <form [formGroup]="formcategory">     
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "MyProfile.Name" | translate }}
              </mat-label>
              <input matInput formControlName="vName" PreventionSqlInjector/>
              <mat-error *ngIf="formcategory.get('vName')?.hasError('required')">{{ "ThisFieldIsRequired" | translate }}</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>
                  {{ "Group.Description" | translate }}
                </mat-label>
                <input matInput formControlName="vDescription" PreventionSqlInjector/>
                <mat-error *ngIf="formcategory.get('vName')?.hasError('required')">{{ "ThisFieldIsRequired" | translate }}</mat-error>
              </mat-form-field>      
          </form>
        
      </ng-container>
       <!-- botones -->
       <ng-container customButtonRight>
  
   
        <button class="mx-3 mb-2" type="button" mat-raised-button color="primary" (click)="saveCategory()">
          {{
          isEditingcategory
          ? ["Save" | translate]
          : ["Add" | translate]
          }}
          <mat-icon *ngIf="!isEditingcategory" iconPositionEnd fontIcon="add"></mat-icon>

        </button>
      </ng-container>
    </app-modal2>
  </ng-template>