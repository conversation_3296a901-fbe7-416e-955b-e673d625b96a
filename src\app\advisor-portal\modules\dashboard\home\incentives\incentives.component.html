<ng-container >
  <div class="cont-title mb-3">        
      <h4>Incentivos</h4>    
  </div>
  <div class="cont-tops" *ngIf="carouselIncentives.length > -1">
    <div class="container">        
      <div class="carousel">
        <mat-card class="incentive-card" *ngFor="let incentive of carouselIncentives">
          
          <img mat-card-image [src]="incentive.src" alt="Incentivo" class="scaled-image" />
          <mat-card-content>
            <div class="overlay-title">{{ incentive.vCampainName }}</div>
            <p class="valid-date">V<PERSON><PERSON><PERSON> hasta: {{ incentive.dEndCampain | date: 'dd-MM-yyyy' }}</p>
            <p class="description">{{ incentive.vDescription }}</p>
          </mat-card-content>
          <mat-card-actions>
            <button mat-raised-button color="primary" class="" (click)="openPDF(incentive.fileConditionsPath)">Ver condiciones
              <mat-icon>launch</mat-icon>
            </button>
          </mat-card-actions>
        </mat-card>
  
        <button mat-icon-button class="nav-button left" (click)="prev()">
          <mat-icon>chevron_left</mat-icon>
        </button>
        <button mat-icon-button class="nav-button right" (click)="next()">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
  
      <button mat-raised-button color="primary" class="mt-5"  [routerLink]="['incentives-index']">Ver más incentivos</button>
    </div>

  </div>
</ng-container>



 


 
  