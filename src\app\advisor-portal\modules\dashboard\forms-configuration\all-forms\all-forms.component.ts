import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  Input,
  AfterViewInit,
  AfterContentInit,
  OnDestroy,
  SimpleChanges,
} from '@angular/core';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { Subscription, catchError, of } from 'rxjs';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { FormModuleModel } from 'src/app/shared/models/configuration-form/form-module/form-module-table.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { WizardFilterFormModuleSettingModel } from 'src/app/shared/models/modules-setting/wizard-filter-form-module-setting';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
@Component({
  selector: 'app-all-forms',
  standalone: true,
  imports: [CommonModule, TableComponent, MatIconModule, MatButtonModule,TranslateModule,MatTooltipModule],
  templateUrl: './all-forms.component.html',
  styleUrls: ['./all-forms.component.scss'],
})
export class AllFormsComponent implements OnInit, OnDestroy {
  @Input() idStage: number = 0;
  @Input()
  dataFilterFormModuleSetting!: WizardFilterFormModuleSettingModel;

  private _moduleSub?: Subscription;

  formModuleDataTable: FormModuleModel[] = [];
  estructFormModuleTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('FormConfiguration.FormTitle'),
      columnValue: 'nameForm',
    },
    {
      columnLabel:  this._translateService.instant('FormConfiguration.AssignedStates'),
      columnValue: 'nameState',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  constructor(
    public _translateService: TranslateService,
    private _msgSvc: MessageService,
    private _moduleService: ModuleService,
    public _utilsSvc: UtilsService,
    private _modulesSettingService: ModulesSettingService,
    private _customRouter: CustomRouterService

  ) {}

  ngOnInit(): void {
    this.getListFormModuleByIdStage();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table modules
      this.estructFormModuleTable[0].columnLabel = this._translateService.instant(
        'FormConfiguration.FormTitle'
      );
      this.estructFormModuleTable[1].columnLabel = this._translateService.instant(
        'FormConfiguration.AssignedStates'
      );

      this.estructFormModuleTable[2].columnLabel =
        this._translateService.instant('Status');

      this.estructFormModuleTable[3].columnLabel =
        this._translateService.instant('Action');
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['idStage']) {
      this.getListFormModuleByIdStage();
    }
  }

  ngOnDestroy(): void {
    this._moduleSub?.unsubscribe();
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'modify':
        this.editButtonClick(event.value.idFormModule);
        break;
      default:
        break;
    }
  }

  getListFormModuleByIdStage() {
    this._moduleSub = this._moduleService
      .getListFormModuleByIdStage(this.idStage)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.formModuleDataTable = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.formModuleDataTable = resp.result;
            this.formModuleDataTable.forEach(function (item) {
              if (item.listState.length > 0) {
                item.nameState = '';

                item.listState.forEach(function (itemState) {
                  item.nameState +=
                    item.nameState != ''
                      ? (item.nameState = itemState.vState)
                      : (item.nameState = itemState.vState + ', ');
                });
              } else {
                item.nameState = '';
              }
            });
          }
        }
      });
  }

  addButtonClick() {
    this._moduleSub = this._moduleService
      .getStagByStateNoAssociateToForm(this.idStage)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this.dataFilterFormModuleSetting.states = [];
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataFilterFormModuleSetting.states = resp.result;
            this.saveurrentEditFilterFormNewModulesSetting();
            this._customRouter.navigate([`/dashboard/forms/new`]);
          }
        }
      });
  }

  editButtonClick(idForm: number) {
    this._moduleSub = this._moduleService
      .getStagByStateByIdFormModule(this.idStage, idForm)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this.dataFilterFormModuleSetting.states = [];
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataFilterFormModuleSetting.states = resp.result;
            this.saveurrentEditFilterFormNewModulesSetting();
            this._customRouter.navigate([`/dashboard/forms/modify/1`]);
          }
        }
      });
  }

  saveurrentEditFilterFormNewModulesSetting() {
    this._modulesSettingService.setCurrentEditFilterFormNewModulesSetting(
      this.dataFilterFormModuleSetting
    );
  }
}
