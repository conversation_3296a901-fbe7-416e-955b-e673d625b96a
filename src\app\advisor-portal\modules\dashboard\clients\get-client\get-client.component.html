<!-- Bo<PERSON><PERSON> para regresar al listado de clientes -->
<a
  mat-button
  class="fw-bold text-decoration-none"
  (click)="goToClient()"
  [routerLink]="['#']"
>
  <mat-icon>keyboard_backspace</mat-icon>
  {{ 'Client.GotoList' | translate }}
</a>

<div class="row mt-1 mb-2">
  <div class="col-md-12 col-sm-12 mb-2">
    <mat-card class="example-card">
      <mat-card-header class="card-header">
        <div class="header-content">
          <!-- Título del card mostrando el nombre completo del cliente -->
          <mat-card-title>
            <div class="row mb-2">
              <div class="col-md-8">
                <div class="row">
                  <div class="col-md-12">
                    <span class="label-client">
                      {{ "Client.Client" | translate }}
                    </span>
                  </div>
                  <div class="col-md-12">
                    <strong class="text-client">
                      {{ clientResponse?.vFullName }}
                    </strong>
                    <b></b>
                  </div>
                </div>
              </div>
            </div>
          </mat-card-title>
        </div>
        <!-- Botón para abrir el diálogo de edición del cliente -->
        <mat-card-actions>
          <mat-card-actions>
            <button color="primary" mat-raised-button type="button" (click)="openEditClientDialog()">
              {{ 'Client.UpdateData' | translate }}
            </button>
          </mat-card-actions>
        </mat-card-actions>
      </mat-card-header>
      <mat-card-content>
        <!-- Información detallada del cliente -->
        <div class="row mt-2">
          <div class="col-md-2">
            <p>
              <strong>{{ 'Client.Products' | translate }}</strong>
            </p>
            <p>{{ clientResponse?.vProducts }}</p>
          </div>
          <div class="col-md-3">
            <p>
              <strong>{{ 'Client.TypeDocument' | translate }}</strong>
            </p>
            <p>{{ filterTypeDocument(clientResponse?.iIdDocumentType) }}</p>
          </div>
          <div class="col-md-3">
            <p>
              <strong>{{ 'Client.DocumentNumber' | translate }}</strong>
            </p>
            <p>{{ clientResponse?.vDocumentNumber }}</p>
          </div>
          <div class="col-md-2">
            <p>
              <strong>{{ 'Client.Email' | translate }}</strong>
            </p>
            <p>{{ clientResponse?.vEmailUser }}</p>
          </div>
          <div class="col-md-2">
            <p>
              <strong>{{ 'Client.CellPhone' | translate }}</strong>
            </p>
            <p>{{ clientResponse?.vCellPhone }}</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<!-- Plantilla para el modal de edición del cliente -->
<ng-template #editClientModal>
  <app-modal [titleModal]="tittleModalText">
    <app-edit-client (editClient)="editClient($event)" [clientInput]="clientResponse"></app-edit-client>
  </app-modal>
</ng-template>

<!-- Grupo de pestañas para productos, trámites y cotizaciones del cliente -->
<mat-tab-group>

  <!-- Pestaña de productos -->
  <mat-tab label="{{ 'Client.Products' | translate }}">
    <div class="mr-2 ml-2 mt-4">
      <br>
      <h3>{{ 'Client.Products' | translate }}</h3>
      <h4 class="mt-3">{{ "Search" | translate }}</h4>

      <!-- Búsqueda de productos -->
      <div class="d-flex">
        <div class="w-100">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ "Search" | translate }}</mat-label>
            <input matInput #searchBar />
            <mat-icon matSuffix class="my-icon" iconPositionEnd>search</mat-icon>
          </mat-form-field>
        </div>
        <div class="mt-1 mx-4">
          <button (click)="openFilterDialog()"  type="button" mat-raised-button color="primary">
            <mat-icon class="hand" iconPositionEnd>filter_list</mat-icon>
            {{ "Filter" | translate }}
          </button>
        </div>
      </div>

      <!-- Tabla de productos del cliente -->
      <div class="auto">
        <app-table
          [displayedColumns]="estructTableProducts"
          [data]="dataTableProducts"
          (iconClick)="onClickSeeProduct($event)"
        ></app-table>
      </div>
    </div>
  </mat-tab>

  <!-- Pestaña de trámites -->
  <mat-tab label="{{ 'Client.Procedure.Title' | translate }}">
    <div class="mr-2 ml-2 mt-4">
      <br>
      <h3>{{ 'Client.Procedure.Title' | translate }}</h3>
      <h4 class="mt-3">{{ "Search" | translate }}</h4>

      <!-- Búsqueda de trámites -->
      <div class="row">
        <div class="col-10">
          <mat-form-field appearance="fill" class="fld col-12">
            <mat-label>{{ "Search" | translate }}</mat-label>
            <input matInput #searchBar />
            <mat-icon matSuffix class="my-icon">search</mat-icon>
          </mat-form-field>
        </div>
        <div class="col-2 filter-centered-button">
          <button (click)="openFilterDialogProcedures()" class="filter-custom-button" type="button" mat-raised-button color="primary">
            <mat-icon class="hand">filter_list</mat-icon>
            {{ "Filter" | translate }}
          </button>
        </div>
      </div>

      <!-- Tabla de trámites del cliente -->
      <div class="auto">
        <app-table
          [displayedColumns]="estructTableClientsProcedures"
          [data]="dataTableProcedures"
          (iconClick)="onClickSeeProcedure($event)"
        ></app-table>
      </div>
    </div>
  </mat-tab>

  <!-- Pestaña de cotizaciones -->
  <mat-tab label="{{ 'Client.Quotes' | translate }}">
    <div class="mr-2 ml-2 mt-4">
      <br>
      <h3>{{ 'Client.Quotes' | translate }}</h3>
      <h4 class="mt-3">{{ "Search" | translate }}</h4>

      <!-- Búsqueda de cotizaciones -->
      <div class="row">
        <div class="col-10">
          <mat-form-field appearance="fill" class="fld col-12">
            <mat-label>{{ "Search" | translate }}</mat-label>
            <input matInput #searchBar />
            <mat-icon matSuffix class="my-icon" iconPositionEnd>search</mat-icon>
          </mat-form-field>
        </div>
        <div class="col-2 filter-centered-button">
          <button (click)="openFilterDialogQuote()" class="filter-custom-button" type="button" mat-raised-button color="primary">
            <mat-icon class="hand" iconPositionEnd>filter_list</mat-icon>
            {{ "Filter" | translate }}
          </button>
        </div>
      </div>

      <!-- Tabla de cotizaciones del cliente -->
      <div class="auto">
        <app-table
        [IsStatic]="false"
          [displayedColumns]="estructTableClientsQuotes"
          [data]="dataTableQuotes"
          (iconClick)="onClickSeeQuote($event)"
        ></app-table>
      </div>
    </div>
  </mat-tab>

</mat-tab-group>
