<div class="cont-subtitle mt-3">
  <h3>{{ "ModulesSetting.Submodules.MainSubtitle" | translate }}</h3>
</div>

<div class="cont-table">
  <app-table
    [displayedColumns]="estructModulesSettingTable"
    [data]="subModulesDataTable"
    (iconClick)="controller($event)"
  ></app-table>
</div>

<div class="cont-btn">
  <button
    class="mt-2 w-20"
    type="button"
    (click)="openModalCreateEditSubmodule()"
    mat-raised-button
    color="primary"
  >
    {{ "ModulesSetting.Submodules.AddSubmodule" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
  <mat-icon class="click mt-2" matTooltipPosition="right" matTooltip="{{ 'Tooltips.AddSubmoduleButton' | translate }}">help_outline</mat-icon>
</div>


<!-- modal editar-crear submodulos -->
<ng-template #createSubmoduleModal>
  <app-modal2 [titleModal]="titelModal" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="form" (ngSubmit)="complete()">
        <div class="row mt-5">
          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "ModulesSetting.Submodules.SubmoduleName" | translate }}
              </mat-label>
              <input matInput formControlName="vDescription"  PreventionSqlInjector/>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(form, 'vDescription', 'required')
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ "ModulesSetting.AssociatedProcess" | translate }}
              </mat-label>
              <mat-select formControlName="fkIdProcess">
                <mat-option
                  *ngFor="let process of processes"
                  [value]="process.pkIIdProcessFamily"
                >
                  {{ process.vNameProcess }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(form, 'fkIdProcess', 'required')
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2">
              <mat-label>
                {{
                  "ModulesSetting.Submodules.ProductsLabel" | translate
                }}</mat-label
              >
              <mat-select formControlName="idProducts" multiple>
                <mat-option
                  *ngFor="let product of products"
                  [value]="product.pkIIdProduct"
                  >{{ product.vProductName }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>

          <div class="col-md-12 col-sm-12">
            <mat-slide-toggle class="mb-3" formControlName="bActive">
              {{ "ModulesSetting.Submodules.ActiveSubmodule" | translate }}
            </mat-slide-toggle>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button
        *ngIf="this.form.get('idMenu')?.value === 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="createSubmodule()"
        [disabled]="!valid"
      >
        {{ "ModulesSetting.Submodules.Modal.CreateAction" | translate }}
      </button>
      <button
        *ngIf="this.form.get('idMenu')?.value > 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="editSubmodule()"
        [disabled]="!valid"
      >
        {{ "ModulesSetting.Submodules.Modal.EditAction" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
