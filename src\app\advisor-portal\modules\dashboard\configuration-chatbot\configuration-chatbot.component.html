<div class="title m-0">
  <h2 class="h3 m-0">
    <img src="assets/img/layouts/config_ico.svg" alt="" />
    {{ "SettingChatbot.Title" | translate }}
  </h2>
</div>
<app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>
<app-choose-country-and-company></app-choose-country-and-company>

<div class="ng-container" > 
  <mat-tab-group  (selectedTabChange)="onTabChange($event)">
    <mat-tab label="Chatbot">       
      <form [formGroup]="form">
        <div class="form-group">
          <h3 class="col-md-12">
            {{ "SettingChatbot.Subtitle" | translate }}
          </h3>
          <mat-slide-toggle formControlName="bActive">
            {{ "SettingChatbot.Active" | translate }}
          </mat-slide-toggle>
        </div>    
        <div
          *ngIf="!isPortalClienteActive"
          class="text-custom-dark border border-danger p-3 d-flex align-items-center"
        >
          <mat-icon matTooltipPosition="right" class="mr-2">error</mat-icon>
          {{ "SettingChatbot.Warning" | translate }}
        </div>
    
        <ng-container *ngIf="isPortalClienteActive">
          <div class="form-group">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>{{
                "SettingChatbot.ScriptChatbot" | translate
              }}</mat-label>
              <textarea
                [disabled]="!form.get('bActive')?.value"
                rows="4"
                matInput
                formControlName="tEmbedCode"
              ></textarea>
              <mat-error *ngIf="form.get('scriptChatbot')?.hasError('required')">
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </ng-container>
    
        <div class="form-group mt-3">
          <button
            [disabled]="!form.get('bActive')?.value"
            mat-raised-button
            (click)="createEmbbededResourceByBusiness()"
            color="primary"
            type="submit"
          >
            {{ "Save" | translate }}
          </button>
        </div>
      </form>
    </mat-tab>

    <mat-tab [label]="'MetricsSettings.title' | translate" > sub      <h3 class="col-md-12">
        {{ "MetricsSettings.moduleSettings.sectionTitle" | translate }}
      </h3>

      <div class="row mt-5">       
        <div class="col-9">
          <mat-form-field class="w-100">
            <mat-label>{{ 'Search' | translate }}</mat-label>
            <input  PreventionSqlInjector   matInput   #searchBar
              (keyup.enter)="search(searchBar.value)"
              (focusout)="search(searchBar.value)"
              type="text"
              class="form-control"
              placeholder="{{ 'Search' | translate }}">
            <mat-icon (click)="search(searchBar.value)" matSuffix> search </mat-icon>
          </mat-form-field>
        </div>
           
      
        <!-- boton ordenar -->
        <div class="col-2">
          <button
            type="button"
            mat-raised-button
            color="primary"
            (click)="order()">
              {{ "Order" | translate }} <mat-icon fontIcon="sort_by_alpha"></mat-icon>
          </button>
        </div>
      </div>

      <div class="row mt-2">
        <app-table        
        [displayedColumns]="estructPowerBITable"
        [data]="dataPowerBITable"
        (iconClick)="controller($event)">
      </app-table> 
      </div>

      <div class="row">
        <div class="col-md-12">
          <button
            type="button"
            class="w-auto"
            (click)="openModal()"
            mat-raised-button
            color="primary"            
          >
             {{ "MetricsSettings.addMetricsButton" | translate }} 
             
            <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
          </button>
        </div>
      </div>
      
    </mat-tab>
  
  </mat-tab-group>

  <!-- Modal Filtro -->
<ng-template #AddEmbeddedModal>
  <app-modal2 [titleModal]="modalTitle" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="modalFormBI">
        <div class="form-group">

          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{"MetricsSettings.moduleSettings.name" | translate}}</mat-label>
            <input matInput formControlName="vName"  />
            <mat-error >{{getErrorFieldName()}}</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{"MetricsSettings.moduleSettings.addScriptLabel" | translate}}</mat-label>
            <textarea rows="4" matInput formControlName="tEmbedScript"></textarea>            
            <mat-error >{{getErrorFieldScript()}}</mat-error>                         
          </mat-form-field>
    
        </div>
      </form>      
    </ng-container>

    <ng-container customButtonRight>
      <button
        *ngIf="idContent <= 0"        
        type="button"
        mat-raised-button
        color="primary"
        (click)="addMetrics()"
      >
      {{"MetricsSettings.moduleSettings.addScriptButton" | translate}}
          
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
      <button
        *ngIf="idContent > 0"        
        type="button"
        mat-raised-button
        color="primary"
        (click)="ediMetrics()"
      >
        {{ "Save" | translate }}
          

        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
    
  </app-modal2>
</ng-template>
</div>

