import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';

import { TablesComponent } from './tables/tables.component';
import { DocumentsComponent } from './documents/documents.component';
import { AlertsComponent } from './alerts/alerts.component';
import { ComunicationsComponent } from './comunications/comunications.component';
import { FormComponent } from './form/form.component';
import { FormsConfigurationHistoryComponent } from 'src/app/shared/components/forms-configuration-history/forms-configuration-history.component';
import { CompanyCountryHistoryComponent } from 'src/app/shared/components/company-country-history/company-country-history.component';
import { DataFormModel } from 'src/app/shared/models/module/data-form.model';
import { ValidationsComponent } from './validations/validations.component';
import { PoliciesComponent } from "./policies/policies.component";
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-edit-new-form',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    FormComponent,
    TablesComponent,
    DocumentsComponent,
    AlertsComponent,
    ComunicationsComponent,
    CompanyCountryHistoryComponent,
    FormsConfigurationHistoryComponent,
    ValidationsComponent,
    PoliciesComponent,
    TranslateModule,
],
  templateUrl: './edit-new-form.component.html',
  styleUrls: ['./edit-new-form.component.scss'],
})
export class EditNewFormComponent implements OnInit {
  idForm: number = 0;
  dataForm: DataFormModel = {};
  @ViewChild(TablesComponent) TableComponent!: TablesComponent;
  @ViewChild(DocumentsComponent) DocumentComponent!: DocumentsComponent;
  @ViewChild(AlertsComponent) AlertComponent!: AlertsComponent;
  @ViewChild(ValidationsComponent) ValidationsComponent!: ValidationsComponent;
  @ViewChild(PoliciesComponent) PoliciesComponent!: PoliciesComponent;

  
  constructor() {}

  ngOnInit(): void {}

  getIdForm(event: number) {
    this.idForm = event;
  }

  getDataForm(event: DataFormModel) {
    this.dataForm = event;
  }

  reloadTable(event: number){
    this.TableComponent.getTables(event);
    this.DocumentComponent.getDocumentListByFormIdModule(event);
    this.AlertComponent.getWarnings(event);
  }
}
