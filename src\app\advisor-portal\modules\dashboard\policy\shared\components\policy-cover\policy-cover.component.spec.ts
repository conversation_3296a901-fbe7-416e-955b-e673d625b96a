import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PolicyCoverComponent } from './policy-cover.component';

describe('PolicyCoverComponent', () => {
  let component: PolicyCoverComponent;
  let fixture: ComponentFixture<PolicyCoverComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ PolicyCoverComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PolicyCoverComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
