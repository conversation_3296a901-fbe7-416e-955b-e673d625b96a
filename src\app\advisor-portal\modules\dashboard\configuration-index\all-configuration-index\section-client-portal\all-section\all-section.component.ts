import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { Modal2Component } from '../../../../../../../shared/components/modal2/modal2.component';
import { ResponseGlobalModel } from '../../../../../../../shared/models/response';
import { SectionListClientPortalModel } from '../../../../../../../shared/models/section-client-portal/section-client-portal.model';
import { FileService } from '../../../../../../../shared/services/file/file.service';
import { MessageService } from '../../../../../../../shared/services/message/message.service';
import { SectionClientPortalService } from '../../../../../../../shared/services/section-client-portal/section-client-portal.service';
import { CreateEditSectionComponent } from '../create-edit-section/create-edit-section.component';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-all-section',
  templateUrl: './all-section.component.html',
  styleUrls: ['./all-section.component.scss'],
  standalone: true,
  imports: [
    CommonModule, MatIconModule, TranslateModule,
    MatExpansionModule, MatFormFieldModule, MatInputModule,
    MatCheckboxModule, Modal2Component, CreateEditSectionComponent,
    MatButtonModule
  ]
})
export class AllSectionComponent implements OnInit {
  @ViewChild('sectionClientPortalModal') sectionClientPortalModal?: TemplateRef<any>;

  @Input() idBusinessByCountry: number = 0;

  idOrder: number = 0;
  sectionId: number = 0;
  titelModal: string = '';
  listSections: SectionListClientPortalModel[] = [];

  constructor(
    public _modalDialog: MatDialog,
    private _msgSvc: MessageService,
    private _sanitizer: DomSanitizer,
    private _fileService: FileService,
    public _translateService: TranslateService,
    private _SectionClientPortalService: SectionClientPortalService,
  ) {
  }

  ngOnInit(): void {
    this.getSections();

  }

  getSections() {
    this._SectionClientPortalService.getSectionByIdBusinessCountry(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.listSections = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message);
            this.listSections = [];
            this.idOrder = 0;
          }
          else {
            this.listSections = resp.result;
            this.idOrder = this.listSections.reduce((max, item) => {
              const itemValue = item.order !== undefined ? item.order : -Infinity;
              return (max !== undefined && itemValue > max) ? itemValue : max;
            }, -Infinity);
            console.log(this.idOrder);
            var infoImage = [];

            for (var i = 0; i < this.listSections.length; i++) {
              infoImage.push({ idSection: this.listSections[i].idSection, filePath: this.listSections[i].filePath });
            }

            this._fileService.getImageUrls(infoImage).subscribe(image => {
              if (image.length > 0) {
                for (var i = 0; i < image.length; i++) {
                  let img = image[i];
                  let exist = this.listSections.find(x => x.idSection == img.idSection);

                  if (exist) {
                    exist.blob = this._sanitizer.bypassSecurityTrustUrl(image[i].src) as SafeUrl;
                  }
                }
              }
            });
          }
        }
      });
  }

  gotToCreate() {
    this.sectionId = 0;
    this.titelModal = this._translateService.instant('SectionClientPortal.AddSection');
    this.openModal();
  }

  openModal() {
    this._modalDialog.open(this.sectionClientPortalModal!, {
      width: '80vw',
      height: '80vh',
    });
  }

  eventCloseModal(event: any) {
    this.sectionId = 0;
  }

  changeSuccess(evt: boolean) {
    if (evt == true) {
      this.getSections();
    }
    this.sectionId = 0;
  }

  modify(id?: any) {
    this.sectionId = id ?? 0;
    this.titelModal = this._translateService.instant('SectionClientPortal.ModifySection');
    this.openModal();
  }

  delete(id: any) {
    this._msgSvc.messageConfirmationAndNegation(
      this._translateService.instant('SectionClientPortal.DeleteMessage'),
      '',
      'warning',
      this._translateService.instant('Confirm'),
      this._translateService.instant('Cancel')
    )
      .then((result) => {
        if (result) {
          this._SectionClientPortalService.deleteSectionClientPortal(id)
            .pipe(
              catchError((error) => {
                this._msgSvc.messageWaring(
                  this._translateService.instant('ThereWasAError'),
                  error.error.message
                );
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (!Array.isArray(resp)) {
                if (!resp.error) {
                  this._msgSvc.messageSuccess('', this._translateService.instant(resp.message));
                  this.getSections();
                }
                else {
                  this._msgSvc.messageError(this._translateService.instant(resp.message));
                }
              }
            });
        }
      });
  }
}
