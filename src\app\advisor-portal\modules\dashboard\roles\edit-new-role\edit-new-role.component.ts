import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCardModule } from '@angular/material/card';
import { CdkAccordionModule } from '@angular/cdk/accordion';
import { Router } from '@angular/router';
import { MatSelectModule } from '@angular/material/select';

import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { MessageService } from 'src/app/shared/services/message/message.service';

import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { 
  ActionListModel, 
  ActionMenus, 
  MenuListModel, 
  PActions,
  RoleEditNewModel, 
  RolesStandarListModel 
} from 'src/app/shared/models/role';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
export interface Test {
  idParentMenu: number;
  parentDescription: string;
  parentLevel: number;
  image: string;
  parentUrl: string;
  idSonMenu: number;
  sonDescription: string;
  sonLevel: number;
  parent: number;
  order: number;
  sonUrl: string;
  submenus?: any;
}
@Component({
  selector: 'app-edit-new-role',
  standalone: true,
  imports: [
    CommonModule,   
    ReactiveFormsModule,
    //MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    MatCardModule,
    CdkAccordionModule,
    MatSelectModule,
    TranslateModule,
    MatButtonModule,
    PreventionSqlInjectorDirective
  ],
  templateUrl: './edit-new-role.component.html',
  styleUrls: ['./edit-new-role.component.scss']
})


export class EditNewRoleComponent implements OnInit, OnDestroy {
  
  //idRole en caso de que se edte editando un rol
  private idRole: number = 0;
  //idRole que se asigna al cambiar el select de un rol estandar/plantilla
  private idStandarMenu: number = 0;
  //el id del menu seleccionado acctualmente cuando se esta editando menus y funcionalidades
  private idSelectedMenu: number = 0;

  //listas de menus y acciones que ya estan selccionadas para un id rol ya creado
  private menusSelectedList: Array<MenuListModel> = new Array<MenuListModel>();
  private actionsSelectedList: Array<ActionListModel> = new Array<ActionListModel>();

  //varibales que cambian un valor en html
  selectAll:boolean = false;
  accordionExpanded: boolean = false;
  actionCheck: boolean = false;
  funcionalidadesText: string = this._translateService.instant('Role.SelectModuleToModifyFunctionalities');
  saveButtonText: string = ''; 

  //modelos
  //modelo general de rol
  private roleModel?: RoleEditNewModel;
  //modelo de los modulos/acciones seleccionados
  private actionMenuListModel: Array<ActionMenus> = new Array<ActionMenus>();
  //modelo de las accciones por modulo
  private pActionListModel: Array<PActions> = new Array<PActions>();
  //modelo de todos los modulos
  private menuLModel: Array<MenuListModel> = new Array<MenuListModel>();
  //modelo de los menu padre
  private parentMenuList: Array<MenuListModel> = new Array<MenuListModel>();

  //Listas de roles que llena el select
  roleStandarList: Array<RolesStandarListModel> = new Array<RolesStandarListModel>();
  //lista de acciones que se muestran en el html
  actionsList: Array<ActionListModel>=[]; 
  //lista modulos que se muestra en el html
  moduleList = [
    {
      idModule: 0,
      description: '',
      subModules: this.menuLModel
    }
  ]; 
  menu: MenuListModel[] = [];
  //formulario
  formEditNewRole: FormGroup = new FormGroup({});

  @Input() data = {
    idBusinessCountry: 0,
    idRole: 0,
    toClientPortal: false
  } 
  @Output() submitOut = new EventEmitter<ResponseGlobalModel>();
  actionMenuConfiguration:any = [];
  idMenuConfiguration:number = 3;

  constructor(

    private fb: FormBuilder,
    //private _router:Router,
    private _messageService: MessageService,

    public _roleService: RoleService,
    public _utilsService: UtilsService,
    public _translateService: TranslateService
    
  ){}

  ngOnInit(): void {
    this.moduleList=[];
    this.getRoleStandarList();
    this.getMenus();
    this.getActionsByMenuId();
    this.initForm();
    this.getConfigurationRol();
    if(this.data.idRole == 0)
    {
      this.saveButtonText = this._translateService.instant('Role.CreateRole')
      this.formEditNewRole.controls['roleStandarId'].enable();
    }
    else
    {
      this.formEditNewRole.controls['roleStandarId'].disable();
      this.saveButtonText = this._translateService.instant('Role.EditRole');
      this.idRole = this.data.idRole;
      this.getByRoleId(this.data.idRole);     
    }
  }

  ngOnDestroy(): void {
    
  }

  initForm() {
    this.formEditNewRole = this.fb.group({
      roleStandarId: [null],
      vRoleName: [null, [Validators.required]],
      vDescription: [null, [Validators.required]],
      bActive: [true, []],
      bEdit: [false, []],
      bClientPortal: [this.data.toClientPortal]
    });
  }

  get valid(): boolean {
    return this.formEditNewRole.valid;
  }

  //llena la lista de modulos y acciones segun la parametrizacion obtenida de la api
  fullModulesList(menuList: MenuListModel[], idrol:Number = 0)
  {

    if(idrol != 0)
    {
      for(let item of this.moduleList)
      {
        const indice = this.moduleList.indexOf(item);
        for(let item2 of item.subModules)
        { 
          const indice2 = this.moduleList[indice].subModules.indexOf(item2);
          this.moduleList[indice].subModules[indice2].checked = false; 
          for(let item3 of menuList)
          {      
            if(item2.idSonMenu == item3.idSonMenu)
            {
              this.moduleList[indice].subModules[indice2].checked = true
 
            }
          }
          
        }
      }
      //this.selectAll = true;
      this.accordionExpanded = true;
    }
    else
    {
      for(let item of menuList)
      {
        if(item.order <= 1)
        {
          this.parentMenuList.push(item)
        }      
      }
      for(let itemParent of this.parentMenuList)
      {      
        const sonMenuList = menuList.filter(menu => menu.parent == itemParent.idParentMenu)
        const moduleAct =
        {
          idModule: itemParent.idParentMenu,
          description: itemParent.parentDescription,
          subModules: sonMenuList
        }
        this.moduleList.push(moduleAct)
      }
    }
  }

  //Asigna el modelo segun los cambios que se hagan en el html
  setActionMenuListModel(idRole:number, idMenu:number, checked:boolean, actionSelected?: ActionListModel)
  {
    //si el rol es distinto a 0 trae la configuracion menu/accion que ya existe para ese rol y lo asigna al modelo. 
    if(idRole != 0)
    {
      this.actionMenuListModel = [];
      for(let item of this.menusSelectedList)
      {        
        //comenzar a asignar las acciones por cada menu desde la api
        this._roleService.getActionByMenuId(item.idSonMenu,idRole).subscribe((resp)=>{
          this.pActionListModel=[]
          for (let item2 of resp.result)
          {
            const pAction=         
            {
              pkIIdAction:item2.pk_i_IdAction,
              vName:item2.v_Name,
              bActive:item2.b_Active, 
            }
            this.pActionListModel.push(pAction)
          }
          const actionMenu =
          {
            pkIIdMenuSon: item.idSonMenu,
            pActions: this.pActionListModel
          }
          this.actionMenuListModel.push(actionMenu)
        })
      }
    }
    //asigna o desasigna acciones al dar check o un check en el modulo
    else
    {
      
      if(actionSelected)
      {
        
        if(checked)
        {

          if(this.actionMenuListModel.filter(menu => menu.pkIIdMenuSon == idMenu).length > 0)
          {
            this.actionMenuListModel.filter(menu => menu.pkIIdMenuSon == idMenu)[0].pActions.push(
              {
                pkIIdAction : actionSelected.pk_i_IdAction,
                vName : actionSelected.v_Name,
                bActive: actionSelected.b_Active              
              }
            )
          }
          else
          {
            this.pActionListModel = [];
            const pAction=         
            {
              pkIIdAction : actionSelected.pk_i_IdAction,
              vName : actionSelected.v_Name,
              bActive: actionSelected.b_Active              
            }
            this.pActionListModel.push(pAction)

            const actionMenu =
            {
              pkIIdMenuSon: idMenu,
              pActions: this.pActionListModel
            }
            this.actionMenuListModel.push(actionMenu)
            
            for (let item of this.moduleList)
            {
              if(item.subModules.filter(sm => sm.idSonMenu == idMenu).length > 0)
              {
                item.subModules.filter(sm => sm.idSonMenu == idMenu)[0].checked = true
              }
            }
          }
          
        } 
        else {
          let index = this.pActionListModel.indexOf(this.pActionListModel.filter(action => action.pkIIdAction == actionSelected.pk_i_IdAction)[0]) 
          this.actionMenuListModel.filter(menu => menu.pkIIdMenuSon == idMenu)[0].pActions.splice(index, 1)
          
          //valida si no quedo ninguna accion seleccionada y en ese caso quita el menu vacio del modelo y des selecciona el menu
          if(this.actionMenuListModel.filter(menu => menu.pkIIdMenuSon == idMenu)[0].pActions.length < 1)
          {
            this.setActionMenuListModel(0, idMenu, false);
            
            for (let item of this.moduleList)
            {
              if(item.subModules.filter(sm => sm.idSonMenu == idMenu).length > 0)
              {
                item.subModules.filter(sm => sm.idSonMenu == idMenu)[0].checked = false
              }
              
            }
          }
          else{
            for (let item of this.moduleList)
            {
              if(item.subModules.filter(sm => sm.idSonMenu == idMenu).length > 0)
              {
                item.subModules.filter(sm => sm.idSonMenu == idMenu)[0].checked = true
              }
            }
          }
          
        }
      }
      else{
        if(idMenu!= 0)
        {
          if(checked)
          {
            //busca las funcionalidades disponibles para ese modulo y las asigna todas en principio
            this._roleService.getActionByMenuId(idMenu,0).subscribe((resp)=>{
              this.actionsList=resp.result;           
              this.funcionalidadesText = '';
              this.pActionListModel=[]
              for (let item of resp.result)
              {
                const indice = this.actionsList.indexOf(item);
                this.actionsList[indice].checked = true
                
                const pAction=         
                {
                  pkIIdAction:item.pk_i_IdAction,
                  vName:item.v_Name,
                  bActive:item.b_Active, 
                }
                this.pActionListModel.push(pAction)
              }
              const actionMenu =
              {
                pkIIdMenuSon: idMenu,
                pActions: this.pActionListModel
              }
              this.actionMenuListModel.push(actionMenu)
              
            })
          }
          else
          { 
            this.actionsList=[];
            this.funcionalidadesText = this._translateService.instant('Role.SelectModuleToModifyFunctionalities');
            this.actionMenuListModel = this.actionMenuListModel.filter(menu => menu.pkIIdMenuSon != idMenu)
          }
        }
      }
    }
  }

  //guarda el formulario editando o creando un rol segun corresponda
  complete() {

    if (this.valid) {
      if (this.idRole != 0) {
        this.putRole();
      } else {
        this.postRole();
      }
    }
    else{
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm'),
      );
    }
    
  }

  //#region -- eventos desde el HTML

    //evento a seleccionar un rol standar como plantilla
    selectRoleStandar_Change(roleStandar: RolesStandarListModel)
    {
      this.idStandarMenu = roleStandar.pk_i_IdRole;
      //this.idRole = roleStandar.pk_i_IdRole;
      this.formEditNewRole = this.fb.group({
        vRoleName: [roleStandar.v_RoleName, [Validators.required]],
        vDescription: [roleStandar.v_Description, [Validators.required]],
        bActive: [roleStandar.b_Active, []],
        bEdit: [roleStandar.b_Edit, []],     
        vShortName:[roleStandar.v_ShortName,[]]
      });
  
      this.getMenusByRoleId(roleStandar.pk_i_IdRole);
    }

    //ENVENTO AL DAR CHECK O UNCHECK A UN MODULO
    checkSubModule_Change(moduleSelected: MenuListModel, event: Event) {
      this.idSelectedMenu = moduleSelected.idSonMenu;
      const ischecked = (<HTMLInputElement>event.target).checked
      this.setActionMenuListModel(0, moduleSelected.idSonMenu,ischecked)
    }

    //EVENTO AL DAR CLICK EN EL NOMBRE DE UN MODULO PARA VER LAS ACCIONES QUE TIENE ASIGNADAS SIN CAMBIAR EL CHECK
    labelCheckSubModule_Click(idSonMenu:number)
    {
      this.idSelectedMenu = idSonMenu;
      this.getActionsByMenuId(idSonMenu, true)
    }

    //EVENTO AL DAR CHECK O UNCHEK A UNA FUNCIONALIDAD
    CheckAction_Change(actionSelected: ActionListModel, event: Event) {
      const ischecked = (<HTMLInputElement>event.target).checked
      this.setActionMenuListModel(0, this.idSelectedMenu, ischecked, actionSelected)
    }
    
    //EVENTO AL DAR CLICK EN SELECCIONAR TODOS LOS MODULOS
    selectAll_Click()
    {
      
      for(let itemM of this.moduleList)
      {
        const indiceM = this.moduleList.indexOf(itemM);
        for(let itemSM of itemM.subModules)
        {
          const indiceSM = this.moduleList[indiceM].subModules.indexOf(itemSM)
          this.moduleList[indiceM].subModules[indiceSM].checked=true
          this.setActionMenuListModel(0, itemSM.idSonMenu, true)
        }
      }
      this.accordionExpanded = true
      this.selectAll = true
    }
  //#endregion

  //#region  --metodos que consumen el api
    getByRoleId(idRole:number)
    {
      if(idRole)
      {
        this.saveButtonText = this._translateService.instant('Role.EditRole');
        this._roleService.getByRoleId(idRole).subscribe((resp) => {


          this.formEditNewRole = this.fb.group({
            vRoleName: [resp.result.v_RoleName, [Validators.required]],
            vDescription: [resp.result.v_Description, [Validators.required]],
            bActive: [resp.result.b_Active, []],
            bEdit: [resp.result.b_Edit, []],   
            vShortName:[resp.result.v_ShortName,[]]  
          });

          this.getMenusByRoleId(resp.result.pk_i_IdRole)
          this.roleModel = resp.result

        });
      }
      else
      {
        this.saveButtonText = this._translateService.instant('Role.CreateRole');
      }
      
    }

    getRoleStandarList()
    {
      this._roleService.getRoleStandarList().subscribe(
        (resp)=>{     
          this.roleStandarList = resp.result

          this.roleStandarList.forEach((List, index)=>{
            this.roleStandarList[index].v_RoleName = this._translateService.instant(List.v_RoleName);
            const ItemMenu = this.getTraslateItem(List.v_RoleName);
            this.roleStandarList[index].v_RoleName = ItemMenu              
                        
          })

        }

      )
    }

    getTraslateItem(key: string): string{
      const translation = this._translateService.instant(key);
      return translation ? translation :key;
    }

    getMenus()
    {
      this._roleService.getMenu(this.data.idBusinessCountry, this.data.toClientPortal).subscribe(
        (resp)=>{
        
          this.menu = resp.result
          this.menu.forEach((List, index)=>{
            this.menu[index].sonDescription = this._translateService.instant(List.sonDescription);
            const ItemMenu = this.getTraslateItem(List.sonDescription);
            this.menu[index].sonDescription = ItemMenu   
          })
          this.fullModulesList(this.menu);
        }
      )
    }

    getMenusByRoleId(idRole:number)
    {
      this._roleService.getMenuByRoleId(idRole).subscribe(
        (resp)=>{
          this.menusSelectedList = resp.result;
          this.fullModulesList(this.menusSelectedList, idRole);
          this.setActionMenuListModel(idRole,0,false)
        }
      )
    }

    getActionsByMenuId(idMenu:number = 0, fromClickEvent: boolean = false)
    {
      if(idMenu != 0)
      {
        this._roleService.getActionByMenuId(idMenu,0).subscribe((resp)=>{
          this.actionsList=resp.result;
          this.funcionalidadesText = '';
          for(let item of this.actionsList)
          {
            item.checked=this.selectAll 
          }
          if(this.idRole != 0 && !fromClickEvent)
          {
            this._roleService.getActionByMenuId(idMenu,this.idRole).subscribe((resp)=>{
              this.actionsSelectedList = resp.result;
             
              for(let item of this.actionsList)
              {
                const indice = this.actionsList.indexOf(item);
                this.actionsList[indice].checked = false;
                for(let item2 of this.actionsSelectedList)
                { 
                  if(item.pk_i_IdAction == item2.pk_i_IdAction)
                  {
                    this.actionsList[indice].checked = true
                  }
                }
              }   
            })
          }
          else
          {
            for(let item of this.actionsList)
            {
              const indice = this.actionsList.indexOf(item);
              this.actionsList[indice].checked = false;
              for(let item2 of this.actionMenuListModel.filter(menu => menu.pkIIdMenuSon == idMenu)[0].pActions)
              { 
                if(item.pk_i_IdAction == item2.pkIIdAction)
                {
                  this.actionsList[indice].checked = true
                }
              }
            }
          }
        })
      }
      else{
        this.actionsList=[];
        this.funcionalidadesText = this._translateService.instant('Role.SelectModuleToModifyFunctionalities');
      }
    }

    postRole()
    {    
      
      // validamos si existe menu padre configuracion
      const exists = this.actionMenuListModel.some(x => x.pkIIdMenuSon == this.idMenuConfiguration);

      if (!exists) {
        const sonMenuSet = new Set(this.menu.filter(x => x.isHub).map(x => x.idSonMenu));
        // vrificamos si hay menús hijos configurados
        const existSon = this.actionMenuListModel.some(item => sonMenuSet.has(item.pkIIdMenuSon));
        if (existSon) {
          this.actionMenuListModel.push(this.actionMenuConfiguration);
        }
      }

      this.roleModel =
      {
        pkIIdRole : 0,
        vRoleName : this.formEditNewRole.value.vRoleName,
        vDescription: this.formEditNewRole.value.vDescription,
        vShortName: this.formEditNewRole.value.vShortName,
        dCreationDate: new Date(),
        bActive: this.formEditNewRole.value.bActive,
        bEdit: this.formEditNewRole.value.bEdit,
        actionMenus: this.actionMenuListModel,
        bToClientPortal: this.data.toClientPortal,
      }
      this._roleService.postRole(this.data.idBusinessCountry, this.roleModel).subscribe(
        (resp) => {
          this.submitOut.emit(resp);  
        }
      );       
    }

    putRole()
    {
      this.roleModel =
      {
        pkIIdRole: this.data.idRole,
        vRoleName: this.formEditNewRole.value.vRoleName,
        vDescription: this.formEditNewRole.value.vDescription,
        vShortName: this.formEditNewRole.value.vShortName,
        dCreationDate: new Date(),
        bActive: this.formEditNewRole.value.bActive,
        bEdit: this.formEditNewRole.value.bEdit,
        actionMenus: this.actionMenuListModel,
        bToClientPortal: this.data.toClientPortal,
      }

      this._roleService.putRole(this.roleModel).subscribe(
        (resp) => {
          this.submitOut.emit(resp);
        }
      ); 
    }

    getConfigurationRol() {

      this._roleService.getActionByMenuId(this.idMenuConfiguration,0).subscribe((resp)=>{
        this.pActionListModel=[]
        for (let item2 of resp.result)
        {
          const pAction=         
          {
            pkIIdAction:item2.pk_i_IdAction,
            vName:item2.v_Name,
            bActive:item2.b_Active, 
          }
          this.pActionListModel.push(pAction)
        }
        const actionMenuC =
        {
          pkIIdMenuSon: this.idMenuConfiguration,
          pActions: this.pActionListModel
        }
        this.actionMenuConfiguration = actionMenuC;
      })
    }
  //#endregion
 
}
