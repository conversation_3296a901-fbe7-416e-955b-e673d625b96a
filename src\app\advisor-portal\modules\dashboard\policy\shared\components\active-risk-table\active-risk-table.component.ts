import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BodyTableModel } from 'src/app/shared/models/table';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { RiskTableDataModel } from '../../models';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { PageEvent } from '@angular/material/paginator';
import { PolicyTableService } from '../../service/policy-table.service';

@Component({
  selector: 'app-active-risk-table',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    TranslateModule,
  ],
  templateUrl: './active-risk-table.component.html',
  styleUrls: ['./active-risk-table.component.scss'],
})
export class ActiveRiskTableComponent {
  @Input() estructTable: BodyTableModel[] = [];
  @Input() idPolicy: number = 0;
  @Input() title: string = this._translateService.instant(
    'Policy.PolicyMovementHistory'
  );
  @Input() idPolicyType: number = 0;
  @Output() actionTable = new EventEmitter<any>();
  //Variables relacioandas con la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;
  currentPosition: number = 0;
  dataTableRisk: RiskTableDataModel[] = [];
  keyField: string = "";

  constructor(
    private _translateService: TranslateService,
    private _transactionService: TransactionService,
    private _messageService: MessageService,
    private _policyTableService: PolicyTableService,
  ) { }

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.title = this._translateService.instant(
        'Policy.PolicyMovementHistory'
      );
    });
    this.getIsuredListByIdPolicy();
  }

  getIsuredListByIdPolicy() {
    this._transactionService
      .getIsuredListByIdPolicy(this.idPolicy, this.currentPosition, this.pageSize)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.dataTableRisk = resp.result.item1;
          this.amountRows = resp.result.item2;
          this.keyField = this.dataTableRisk[0].keyField;
          this.chageStructureTable();
        }
      });
  }

  //Detecta los cambios en la paginación de la tabla.
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.currentPosition = this.pageIndex * this.pageSize;
    if (this.pageIndex < 0) {
      this.pageIndex = 0;
    }
    this.getIsuredListByIdPolicy();
  }

  // ARREGLAR ESTO PARA LAS POLIZAS
  chageStructureTable() {
    if (this.idPolicyType === 2) {
      if (this.keyField) {
        this.estructTable = [
          {
            columnLabel: this.keyField,
            columnValue: 'value',
          },
          {
            columnLabel: this._translateService.instant('Policy.NameSecured'),
            columnValue: 'insuredFullName',
          },
          {
            columnLabel: this._translateService.instant('MyProfile.DocumentType'),
            columnValue: 'insuredDocumentType',
          },
          {
            columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
            columnValue: 'insuredDocumentNumber',
          },
          {
            columnLabel: this._translateService.instant('Status'),
            columnValue: 'state',
            functionValue: (item: any) =>
              this._policyTableService.changeRiskStatusValue(item),
          },
        ];
      } else {
        this.estructTable = [

          {
            columnLabel: this._translateService.instant('Policy.NameSecured'),
            columnValue: 'insuredFullName',
          },
          {
            columnLabel: this._translateService.instant('MyProfile.DocumentType'),
            columnValue: 'insuredDocumentType',
          },
          {
            columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
            columnValue: 'insuredDocumentNumber',
          },
          {
            columnLabel: this._translateService.instant('Status'),
            columnValue: 'state',
            functionValue: (item: any) =>
              this._policyTableService.changeRiskStatusValue(item),
          },
        ];
      }

    } else {
      this.estructTable = [
        {
          columnLabel: this._translateService.instant('PolicyConfiguration.GeneralInformation.PolicyNumber'),
          columnValue: 'policyNumber',
        },
        {
          columnLabel: this._translateService.instant('Policy.NameSecured'),
          columnValue: 'insuredFullName',
        },
        {
          columnLabel: this._translateService.instant('MyProfile.DocumentType'),
          columnValue: 'insuredDocumentType',
        },
        {
          columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
          columnValue: 'insuredDocumentNumber',
        },
        {
          columnLabel: this._translateService.instant('Status'),
          columnValue: 'state',
          functionValue: (item: any) =>
            this._policyTableService.changeRiskStatusValue(item),
        },
      ];
    }
  }
}
