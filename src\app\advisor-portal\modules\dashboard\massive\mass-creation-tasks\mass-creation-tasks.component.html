<!-- Titulo Creación masiva de tareas-->
<div class="row">
    <h5 class="title mb-1">{{ 'BulkUpload.Massives.MassCreationTasks.BulkCreationOfTasks' | translate }}</h5>
    <p class="description">{{ 'BulkUpload.Massives.MassCreationTasks.BulkCreationOfTasksD' | translate }}</p>
</div>

<form [formGroup]="formFilter">
    <div class="row">
        <!-- Proceso  -->
        <div class="col-md-3 col-sm-12 mb-1">
            <mat-form-field class="w-100">
                <mat-label>{{ "Reports.NewReport.Process" | translate }}</mat-label>
                <mat-select formControlName="fkIIdprocess">
                    <mat-option *ngFor="let option of listProcess" [value]="option.pkIIdProcessFamily">{{
                        option.vNameProcess
                        }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <!-- Producto -->
        <div class="col-md-3 col-sm-12 mb-1">
            <mat-form-field class="w-100">
                <mat-label>{{ "Reports.NewReport.Product" | translate }}</mat-label>
                <mat-select formControlName="fkIIdProduct">
                    <mat-option *ngFor="let option of listProduct" [value]="option.pkIIdProductModule">{{ option.vName
                        }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <!-- Etapa -->
        <div class="col-md-3 col-sm-12 mb-1">
            <mat-form-field class="w-100">
                <mat-label>{{ "Reports.NewReport.Stage" | translate }}</mat-label>
                <mat-select formControlName="fkIdStage">
                    <mat-option *ngFor="let stage of stageList" [value]="stage.pkIIdStage">{{ stage.vNameStage
                        }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <!-- Estado -->
        <div class="col-md-3 col-sm-12 mb-1">
            <mat-form-field class="w-100">
                <mat-label>{{ "Reports.NewReport.State" | translate }}</mat-label>
                <mat-select formControlName="fkIdState">
                    <mat-option *ngFor="let status of statusList" [value]="status.pkIIdStageByState">{{
                        status.vState }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 p-0">
          <mat-checkbox formControlName="isDisabledCommunications">
            {{ "BulkUpload.Massives.MassCreationStates.DisableCommunications" | translate }}
          </mat-checkbox>
        </div>
      </div>
</form>

<ng-container>
    <div class="cont-fieles mt-3">
        <!-- Subtitulo Plantilla de tarea-->
        <div class="">
            <h5 class="title mb-1">{{ 'BulkUpload.Massives.MassCreationTasks.TaskTemplate' | translate }}</h5>
            <p class="description">{{ 'BulkUpload.Massives.MassCreationTasks.TaskTemplateD' | translate }}</p>
        </div>
        <div class="cont-template">
            <div class="cont-info-file">
                <p class="m-0 p-0">{{templateName}}</p>
                <span class="description">25 MB</span>
            </div>
            <div class="cont-download-icon">
                <span (click)="downloadTemplate()" class="material-symbols-outlined click">
                    download
                </span>
            </div>
        </div>
        <!-- Subtitulo Carga de plantilla-->
        <div class="mt-3">
            <h5 class="title mb-1">{{ 'BulkUpload.Massives.MassCreationTasks.LoadingTemplate' | translate }}</h5>
            <p class="description">{{ 'BulkUpload.Massives.MassCreationTasks.LoadingTemplateD' | translate }}</p>
        </div>
        <div class="cont-template" *ngIf="uploadedFile.length > 0">
            <div class="cont-info-file">
                <p class="m-0 p-0">{{fileName}}</p>
                <span class="description">25 MB</span>
            </div>
            <div class="cont-download-icon">
                <span class="material-symbols-outlined click" (click)="deleteFile()">
                    delete
                </span>
            </div>
        </div>
        <div class="cont-upload" *ngIf="uploadedFile.length === 0">
            <app-drag-drop-upload [fileAccept]="'.xlsx'" [isOneFile]="true" [message]="''"
                (saveFile)="getFiles($event)"></app-drag-drop-upload>
        </div>
    </div>
</ng-container>

<div class="d-flex justify-content-center mt-3">
    <a class="label-button" mat-button (click)="goBackMassive()"><span>{{"BulkUpload.Massives.MassCreationPolicy.GoOutToMass" | translate}}</span>
        <mat-icon fontIcon="arrow_back"></mat-icon>
    </a>
    <button type="button" class="w-auto" [disabled]="uploadedFile.length < 1 || !template" (click)="loadTask()"
        mat-raised-button color="primary">
        {{ "BulkUpload.Massives.MassCreationTasks.ConfirmUploadBtn" | translate }}
        <mat-icon iconPositionEnd class="material-symbols-outlined">
            check
        </mat-icon>

    </button>
</div>
