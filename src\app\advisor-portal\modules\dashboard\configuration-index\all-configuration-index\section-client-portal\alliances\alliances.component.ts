import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, map, of, Subscription } from 'rxjs';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { ImageUploadModel } from 'src/app/shared/models/file';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { AllianceService } from 'src/app/shared/services/alliances/alliance.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-alliances',
  standalone: true,
  imports: [
    GenericImagePickerComponent,
    Modal2Component,
    TranslateModule,
    CommonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatButtonModule,
  ],
  templateUrl: './alliances.component.html',
  styleUrls: ['./alliances.component.scss']
})
export class AlliancesComponent implements OnInit{
  @ViewChild('createEditAlliance') createEditAlliance?: TemplateRef<any>;
  _currentModal: MatDialogRef<any> | null = null;
  formSubs?: Subscription;

  @Input() idBusinessByCountry: number = 0;
  formAlliance: FormGroup = this._fb.group({
    vLogo: [null],
  })
  aliances: any[] = []
  pkAllianceToModify: number = 0;
  imageSrc: string = '';
  constructor(
    private _allianceService: AllianceService,
    public utilsSvc: UtilsService,
    private _translateService: TranslateService,
    private _fileService: FileService,
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _modalDialog: MatDialog,

  ){}
  ngOnInit(): void {
    this._loadAlliances()
  }

  changeImage(event: any) {
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString;
    this.formAlliance.get('vLogo')?.setValue({ base64, imageName:`${imageName}.${extension}` });
    this.imageSrc = base64;
  }

  openModal(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this._currentModal = this._modalDialog.open(this.createEditAlliance!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formAlliance.reset();
      })
    })
  }

  async _getImage(pkFile: number): Promise<string> {
    let extension: string = '';
    return new Promise((resolve) => {
      this._fileService.getUploadFileById(pkFile).subscribe(
        (response) => {
          if (response.result.vFileName && response.result.imageBase64) {
            extension = response.result.vFileName.split('.');
            extension =
              extension[response.result.vFileName.split('.').length - 1];
            resolve(`data:image/${extension};base64,${response.result.imageBase64}`);
          }
          resolve('');
        },
        (error) => {
          // Log the error if the update fails
          console.error('Error updating the image:', error);
          resolve('');
        }
      );
    });
    
  }

  private _loadAlliances(){
    this._allianceService.getAlliances(this.idBusinessByCountry).subscribe({
      next: (resp => {
        this.aliances = resp.result
        this.aliances.forEach(async alliance => {
          alliance.imageSrc = await this._getImage(alliance.fkIIdUploadedFile)
        })
      }),
      error: (err => {
        this.aliances = []
      })
    })
  }

  async saveImage(): Promise<number> {
    return new Promise((resolve) => {
      let payload: ImageUploadModel = {
        fileName: this.formAlliance.get('vLogo')?.value.imageName,
        fkIIdBusinessByCountry: this.idBusinessByCountry,
        imageBase64: this.formAlliance.get('vLogo')?.value.base64.split(',')[1],
        pkIIdInsuranceCompanies: 0,
        type: 0
      };
      this._fileService
        .uploadImage(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              resolve(resp.result)
            }
          }
        });
    });
    
  }

  public deleteAlliance(pkAlliance: number){
    this._allianceService.deleteAlliance(pkAlliance).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadAlliances()
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.AllianceDeletedSucessfully')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
    })
  }

  async saveAlliance(){
    if (this.formAlliance.invalid)
      return this.formAlliance.markAllAsTouched()
    this._allianceService.createAlliance({
      FkIIdBusinessByCountry: this.idBusinessByCountry,
      FkIIdUploadFile: await this.saveImage()
    }).subscribe({
      next: (response) => {
        if (!response.error) {
          this._loadAlliances();
          this.closeModal()
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.AllianceCreatedSucessfully')
            );
        }
      },
      error: (error) => {
        this._messageService.messageInfo(
          this._translateService.instant('ThereWasAError'),
          error
        );
      }
    })
  }
  closeModal() {
    if (this._currentModal) {
      this._currentModal.close();
      this._currentModal = null;
      this.formSubs?.unsubscribe();
    }
  }

}
