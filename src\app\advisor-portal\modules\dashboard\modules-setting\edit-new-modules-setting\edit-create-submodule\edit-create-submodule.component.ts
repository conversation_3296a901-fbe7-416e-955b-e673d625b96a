import { CommonModule } from '@angular/common';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription, catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { ProcessModel } from 'src/app/shared/models/menu';
import { AllMenuModel } from 'src/app/shared/models/menu/all-menu.model';
import { ModulesSettingModel } from 'src/app/shared/models/menu/modules-setting-model';
import { PorductModulesModel } from 'src/app/shared/models/product/product-modules.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-edit-create-submodule',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    Modal2Component,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSelectModule,
    PreventionSqlInjectorDirective,
    MatTooltipModule
  ],
  templateUrl: './edit-create-submodule.component.html',
  styleUrls: ['./edit-create-submodule.component.scss'],
})
export class EditCreateSubmoduleComponent implements OnInit, OnDestroy {
  subModulesDataTable: AllMenuModel[] = [];
  products: PorductModulesModel[] = [];
  estructModulesSettingTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'ModulesSetting.Submodules.SubmoduleName'
      ),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant('Insurer.Edit'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
  ];
  @ViewChild('createSubmoduleModal') createSubmoduleModal?: TemplateRef<any>;
  titelModal: string = 'Crear submódulo';
  private _editNewModulesSettingSubscription?: Subscription;
  pkIIdMenu: number = 0;
  form: FormGroup = new FormGroup({});
  processes: ProcessModel[] = [];
  idBusinessCountry: number = 0;
  private _settingCountryAndCompanySubscription?: Subscription;

  constructor(
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _modulesSettingService: ModulesSettingService,
    public _matDialog: MatDialog,
    public utilsSvc: UtilsService,
    private _fb: FormBuilder,
    private _parametersService: ParametersService,
    private _settingService: SettingService,
    private _productService: ProductService
  ) {}

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table Submodulos
      this.estructModulesSettingTable[0].columnLabel =
        this._translateService.instant(
          'ModulesSetting.Submodules.SubmoduleName'
        );
      this.estructModulesSettingTable[1].columnLabel =
        this._translateService.instant('Insurer.Edit');
    });
    this.getDataModulesSettingSubscription();
    this.initForm();
    this.getSettingCountryAndCompanySubscription();
  }

  getDataModulesSettingSubscription() {
    this._editNewModulesSettingSubscription =
      this._modulesSettingService.currentModulesSetting.subscribe({
        next: (response) => {
          if (!(Object.keys(response).length === 0)) {
            if (this.pkIIdMenu === 0) {
              this.pkIIdMenu = response.pkIdMenu;
              this.getSubmoduleList();
            }
          }
        },
      });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.form
              .get('idBusinessCountry')
              ?.setValue(response.enterprise.pkIIdBusinessByCountry);
          }
        }
      );
  }

  initForm() {
    this.form = this._fb.group({
      idMenu: [0],
      vDescription: ['', [Validators.required]],
      vTag: [''],
      fkIdProcess: [null, [Validators.required]],
      vImage: [''],
      iParent: [this.pkIIdMenu],
      bActive: [true, [Validators.required]],
      hasChildren: [false],
      isModule: [false],
      idBusinessCountry: [0],
      idProducts: [''],
    });
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.getByMenuId(event.value.pkIIdMenu);
        this.form.get('idMenu')?.setValue(event.value.pkIIdMenu);
        this.openModalCreateEditSubmodule();
        break;
      default:
        break;
    }
  }

  setForm(submodule: ModulesSettingModel) {
    let products = [];
    products = JSON.parse(submodule.idProducts);
    this.form.get('idMenu')?.setValue(submodule.idMenu);
    this.form.get('vDescription')?.setValue(submodule.vDescription);
    this.form.get('vTag')?.setValue(submodule.vTag);
    this.form.get('vImage')?.setValue(submodule.vImage);
    this.form.get('fkIdProcess')?.setValue(submodule.fkIdProcess);
    this.form.get('bActive')?.setValue(submodule.bActive);
    this.form.get('iParent')?.setValue(submodule.iParent);
    this.form.get('idProducts')?.setValue(products);
  }

  getSubmoduleList() {
    this._modulesSettingService
      .getAllChildrenMenuFromModules(this.pkIIdMenu)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.subModulesDataTable = resp.result;
          }
        }
      });
  }

  getAllProcess() {
    this._parametersService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.processes = resp.result;
          }
        }
      });
  }

  getAllProducts() {
    this._productService
      .getAllProductByBusiness(this.idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.products = resp.result;
          }
        }
      });
  }

  openModalCreateEditSubmodule() {
    if (this.form.get('idMenu')?.value > 0) {
      this.titelModal = this._translateService.instant(
        'ModulesSetting.Submodules.Modal.ModalTitleEdit'
      );
    } else {
      this.titelModal = this._translateService.instant(
        'ModulesSetting.Submodules.Modal.ModalTitleCreate'
      );
    }
    this.getAllProcess();
    this.getAllProducts();
    this._matDialog.open(this.createSubmoduleModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  complete() {}

  closeModal(event: boolean) {
    this.form.reset();
    this.form.get('iParent')?.setValue(this.pkIIdMenu);
    this.form.get('bActive')?.setValue(true);
    this.form.get('idMenu')?.setValue(0);
    this.form.get('isModule')?.setValue(false);
    this.form.get('hasChildren')?.setValue(false);
    this.form.get('idBusinessCountry')?.setValue(this.idBusinessCountry);
  }

  get valid(): boolean {
    return this.form.valid;
  }

  createSubmodule() {
    let payload: ModulesSettingModel = this.form.value;
    payload.vTag = this.generateSubmodulePath(payload.vDescription);
    if (this.form.get('idProducts')?.value) {
      payload.idProducts = JSON.stringify(this.form.get('idProducts')?.value);
    } else {
      payload.idProducts = '[]';
    }
    if (this.valid) {
      this._modulesSettingService
        .createMenu(payload)
        .pipe(
          catchError((error) => {
            if (error.error.message === 'ConflictAlreadyExists') {
              this._messageService.messageWaring(
                '',
                this._translateService.instant(
                  'ModulesSetting.ConflictAlreadyExists'
                )
              );
            }
            else {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._messageService.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageCreated'
                )
              );
              this.getSubmoduleList();
              this._matDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  editSubmodule() {
    let payload: ModulesSettingModel = this.form.value;
    payload.vTag = this.generateSubmodulePath(payload.vDescription);
    if (this.form.get('idProducts')?.value) {
      payload.idProducts = JSON.stringify(this.form.get('idProducts')?.value);
    } else {
      payload.idProducts = '[]';
    }
    if (this.valid) {
      this._modulesSettingService
        .updateMenu(payload)
        .pipe(
          catchError((error) => {
            if (error.error.message === 'ConflictAlreadyExists') {
              this._messageService.messageWaring(
                '',
                this._translateService.instant(
                  'ModulesSetting.ConflictAlreadyExists'
                )
              );
            }
            else {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._messageService.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageUpdated'
                )
              );
              this.getSubmoduleList();
              this._matDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  getByMenuId(pkIIdMenu: number) {
    this._modulesSettingService
      .getByMenuId(pkIIdMenu)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.setForm(resp.result);
            this.form.get('isModule')?.setValue(false);
          }
        }
      });
  }

  generateSubmodulePath(submoduleName: string): string {
    const lowerCaseSubmoduleName = submoduleName.toLowerCase();
    const submoduleNameWithHyphens = lowerCaseSubmoduleName.replace(/\s/g, '-');
    const submodulePath = `modules/${submoduleNameWithHyphens}`;
    return submodulePath;
  }

  ngOnDestroy(): void {
    this._editNewModulesSettingSubscription?.unsubscribe();
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
