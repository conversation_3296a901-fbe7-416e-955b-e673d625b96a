import { CommonModule } from '@angular/common';
import {
  <PERSON>mpo<PERSON>,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { PageEvent } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Router } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription, catchError, of } from 'rxjs';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { AnsService } from 'src/app/shared/services/ans/ans.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { WorkFlowService } from 'src/app/shared/services/work-flow/work-flow.service';
import { EditNewAnsComponent } from '../edit-new-ans/edit-new-ans.component';
import { GeneralConditionsComponent } from '../general-conditions/general-conditions.component';
import {
  AnsTableModel,
  FilterCustomerAnsModel,
  FilterDataSource,
  FilteredProcesses,
  FilteredProducts,
  FilteredStages,
  FilteredStates,
  TypeDayEnum,
  UpdateGeneralConditionsMassiveModel,
} from 'src/app/shared/models/ans';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-all-ans',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TranslateModule,
    MatInputModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    TableComponent,
    Modal2Component,
    MatCheckboxModule,
    MatSelectModule,
    EditNewAnsComponent,
    MatSlideToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    GeneralConditionsComponent,
  ],
  templateUrl: './all-ans.component.html',
  styleUrls: ['./all-ans.component.scss'],
})
export class AllAnsComponent implements OnInit, OnDestroy {
  //Variables para manejo de información entre componentes.
  actionAddAns: boolean = false;
  pkIIdGeneralAns: number = 0;
  massiveGeneralConditionsForm: any;

  //Variables relacionadas con la subscription
  private _settingCountryAndCompanySubscription?: Subscription;
  formValidBusinessByCountry: boolean = false;

  //Variables relacionadas con el formulario.
  formFilter: FormGroup = this._fb.group({});
  validFormAddAns: boolean = false;
  idBusinessCountry: number = 0;
  showBtnEdit: boolean = false;
  validateFormGeneralConditions: boolean = false;
  applyFilterModal: boolean = false;
  modifyAll: boolean = false;

  //Variables relacionadas con el buscador.
  keyword: string | any = null;

  //Variables para los select
  processes: FilteredProcesses[] = [];
  products: FilteredProducts[] = [];
  stage: FilteredStages[] = [];
  state: FilteredStates[] = [];

  filterDataSource: FilterDataSource[] = [];
  uniqueProducts: FilteredProducts[] = [];
  uniqueStages: FilteredStages[] = [];
  uniqueStates: FilteredStates[] = [];

  //Variables para modal.
  @ViewChild('filterModal') filterModal?: TemplateRef<any>;
  @ViewChild('AddAnsModal') AddAnsModal?: TemplateRef<any>;
  @ViewChild('generalConditions') generalConditions?: TemplateRef<any>;
  titleModal: string = this._translateService.instant(
    'AnsConfiguration.ModalAns.GeneralsTab.ModalTitleAdd'
  );

  //Variables relacionadas con la tabla.
  dataAnsTable: AnsTableModel[] = [];
  estructAnsTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Process'
      ),
      columnValue: 'nameProcess',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Product'
      ),
      columnValue: 'nameProduct',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Stage'
      ),
      columnValue: 'nameStage',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.TaskStatus'
      ),
      columnValue: 'nameState',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Conditions'
      ),
      columnValue: 'bSpecificCondition',
      functionValue: (item: any) => this.parserConditionsTable(item),
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.MaximumResponseTime'
      ),
      columnValue: 'vUnitMeasurementInstructor',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Goaltime'
      ),
      columnValue: 'vUnitMeasurementGoal',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.AnsStatus'
      ),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsService.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Modify'
      ),
      columnValue: 'Modify',
      columnIcon: 'create',
    },
  ];

  //Variables el paginado de la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 10;
  order: string = 'asc';

  constructor(
    private _router: Router,
    private _settingService: SettingService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _utilsService: UtilsService,
    private _fb: FormBuilder,
    private _ansService: AnsService,
    private _workFlowService: WorkFlowService,
    private _moduleService: ModuleService,
    private _parametersService: ParametersService,
    private _roleService: RoleService,
    public modalDialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getDataBusinnesByCountry();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.titleModal = this._translateService.instant(
        'AnsConfiguration.ModalAns.GeneralsTab.ModalTitleAdd'
      );
      //traduccion data table ANS
      this.estructAnsTable[0].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Process'
      );
      this.estructAnsTable[1].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Product'
      );
      this.estructAnsTable[2].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Stage'
      );
      this.estructAnsTable[3].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.TaskStatus'
      );
      this.estructAnsTable[4].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Conditions'
      );
      this.estructAnsTable[5].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.MaximumResponseTime'
      );
      this.estructAnsTable[6].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Goaltime'
      );
      this.estructAnsTable[7].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.AnsStatus'
      );
      this.estructAnsTable[8].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Modify'
      );
    });
  }

  //Funciónq que obtiene la información de empresa país cada vezs que esta cambia.
  getDataBusinnesByCountry() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.dataAnsTable = [];
              this.idBusinessCountry =
                response.enterprise.pkIIdBusinessByCountry;
              this.getGeneralANSByIdBusineesCountry(
                this.idBusinessCountry,
                this.pageIndex + 1
              );
              this.getDataForANSFilter(this.idBusinessCountry);
            }
          }
        }
      );
  }

  //Obtiene las ANS paginadas, filtrando por idBusinessCountry.
  getGeneralANSByIdBusineesCountry(
    idBusinessCountry: number,
    pageIndex: number
  ) {
    this._workFlowService
      .getGeneralANSByIdBusineesCountry(
        idBusinessCountry,
        pageIndex,
        this.pageSize,
        this.order
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.dataAnsTable = resp.result;
          this.amountRows = resp.rowCount;
        }
      });
  }

  //Verifica si hay ANS  configuradas con condiciones generales particulares.
  checkExistParticularCondition(idBusinessCountry: number) {
    this._workFlowService
      .checkExistParticularCondition(idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            this.popUpConfirm(
              this._translateService.instant(
                'AnsConfiguration.ModalAns.PopUp.Title'
              ),
              this._translateService.instant(
                'AnsConfiguration.ModalAns.PopUp.Subtitle'
              ),
              this._translateService.instant(
                'AnsConfiguration.ModalAns.PopUp.FirstButton'
              ),
              this._translateService.instant(
                'AnsConfiguration.ModalAns.PopUp.SecondButton'
              )
            );
          } else {
            this.updateGeneralConditionsMassive();
          }
        }
      });
  }

  //Obtiene la lista de ANS por medio del filtro especifico aplicado.
  filterGeneralANSData(model: FilterCustomerAnsModel) {
    this._workFlowService
      .filterGeneralANSData(model)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this.dataAnsTable = [];
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataAnsTable = [];
        } else {
          this.dataAnsTable = [];
          this.dataAnsTable = resp.result;
          this.modalDialog.closeAll();
        }
      });
  }

  //Actualiza masivamente las condiciones generales de las ANS.
  updateGeneralConditionsMassive() {
    const payload: UpdateGeneralConditionsMassiveModel = {
      bIncludeHolidays: this.massiveGeneralConditionsForm.bIncludeHolidays,
      bIncludeRestDay: this.massiveGeneralConditionsForm.bIncludeRestDay,
      iTimeZone: this.massiveGeneralConditionsForm.iTimeZone,
      iTypeDays: this.parseTypeDaysValue(
        this.massiveGeneralConditionsForm.iTypeDays
      ),
      fkIIdCountryHoliday:
        this.massiveGeneralConditionsForm.fkIIdCountryHoliday,
      vJson: JSON.stringify(this.massiveGeneralConditionsForm.daysOfWeek),
      bSpecificCondition: this.modifyAll,
    };
    this._workFlowService
      .updateGeneralConditionMassive(payload, this.idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess(
            this._translateService.instant(
              'AnsConfiguration.ModalAns.PopUp.successfulMassiveGeneralModification'
            ),
            ''
          );
          this.modalDialog.closeAll();
          this.getGeneralANSByIdBusineesCountry(this.idBusinessCountry, 1);
        }
      });
  }
  //Función que inicializa el formulario.
  initFormFilter() {
    this.formFilter = this._fb.group({
      active: [false],
      inactive: [false],
      idProcess: [0],
      idProduct: [0],
      idStage: [0],
      idState: [0],
    });

    this.formFilter.get('idProcess')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.formFilter.get('idProduct')?.setValue(null);
          this.formFilter.get('idStage')?.setValue(null);
          this.formFilter.get('idState')?.setValue(null);
          this.products = this.uniqueProducts.filter(product => this.filterDataSource.some(item => item.pkIIdProductModule === product.pkIIdProductModule && item.pkIIdProcessFamily === data));
          this.stage = this.uniqueStages.filter(stage => this.filterDataSource.some(item => item.pkIIdStage === stage.pkIIdStage && item.pkIIdProcessFamily === data));
          this.state = [];
        }
      },
    });

    this.formFilter.get('idProduct')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.formFilter.get('idStage')?.setValue(null);
          this.formFilter.get('idState')?.setValue(null);
          let idProcess = this.formFilter.get('idProcess')?.value;
          this.stage = this.uniqueStages.filter(stage => this.filterDataSource.some(item => item.pkIIdStage === stage.pkIIdStage && item.pkIIdProductModule === data && (idProcess === 0 || item.pkIIdProcessFamily === idProcess)));
          this.state = [];
        }
      },
    });

    this.formFilter.get('idStage')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.formFilter.get('idState')?.setValue(null);
          this.state = this.uniqueStates.filter(state => this.filterDataSource.some(item => item.pkIIdStageByState === state.pkIIdStageByState && item.pkIIdStage === data))
        }
      },
    });
  }

  //Controlador para las acciones de la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'Modify':
        this.titleModal = this._translateService.instant(
          'AnsConfiguration.ModalAns.GeneralsTab.ModalTitleEdit'
        );
        this.pkIIdGeneralAns = event.value.pkIIdGeneralAns;
        this.openModalAddAns();
        break;
      default:
        break;
    }
  }

  //Función que obtiene la validez del formulario del filtro empresa país.
  validFormBusinessByCounty(event: boolean) {
    this.formValidBusinessByCountry = event;
  }

  //Función que se ejecuta al rpesionar enter o dar click en el icono de buscar.
  searchAns() {
    const payload: FilterCustomerAnsModel = {
      idBusinessCountry: this.idBusinessCountry,
      idProcess: this.formFilter.get('idProcess')?.value,
      idProduct: this.formFilter.get('idProduct')?.value,
      idStage: this.formFilter.get('idStage')?.value,
      idState: this.formFilter.get('idState')?.value,
      order: this.order,
      page: 0,
      pageSize: 10,
      bActive: this.getAnsStatus(
        this.formFilter.get('active')?.value,
        this.formFilter.get('inactive')?.value
      ),
      keyword: this.keyword,
    };
    this.filterGeneralANSData(payload);
  }

  //Detecta los cambios en el input de filtrar por.
  onInputChange(value: string) {
    if (value === '') {
      this.getGeneralANSByIdBusineesCountry(this.idBusinessCountry, 1);
    }
    // Haz algo con el nuevo valor, como actualizar otra variable en tu componente
  }

  //Función que ordena la tabla de ANS.
  orderTable(order: number) {
    switch (order) {
      case 0:
        this.order = 'asc';
        this.pageIndex = 0;
        this.pageSize = 10;
        if (this.applyFilterModal) {
          const payload: FilterCustomerAnsModel = {
            idBusinessCountry: this.idBusinessCountry,
            idProcess: this.formFilter.get('idProcess')?.value,
            idProduct: this.formFilter.get('idProduct')?.value,
            idStage: this.formFilter.get('idStage')?.value,
            idState: this.formFilter.get('idState')?.value,
            order: this.order,
            page: 0,
            pageSize: 10,
            bActive: this.getAnsStatus(
              this.formFilter.get('active')?.value,
              this.formFilter.get('inactive')?.value
            ),
            keyword: this.keyword,
          };
          this.filterGeneralANSData(payload);
        } else {
          this.getGeneralANSByIdBusineesCountry(
            this.idBusinessCountry,
            this.pageIndex
          );
        }

        break;
      case 1:
        this.order = 'desc';
        this.pageIndex = 0;
        this.pageSize = 10;
        if (this.applyFilterModal) {
          const payload: FilterCustomerAnsModel = {
            idBusinessCountry: this.idBusinessCountry,
            idProcess: this.formFilter.get('idProcess')?.value,
            idProduct: this.formFilter.get('idProduct')?.value,
            idStage: this.formFilter.get('idStage')?.value,
            idState: this.formFilter.get('idState')?.value,
            order: this.order,
            page: 0,
            pageSize: 10,
            bActive: this.getAnsStatus(
              this.formFilter.get('active')?.value,
              this.formFilter.get('inactive')?.value
            ),
            keyword: this.keyword,
          };
          this.filterGeneralANSData(payload);
        } else {
          this.getGeneralANSByIdBusineesCountry(
            this.idBusinessCountry,
            this.pageIndex
          );
        }

        break;
      default:
        break;
    }
  }

  //Función que abre el modal de filtrar.
  openModalFilter() {
    this.initFormFilter();
    this.resetFilterLists();
    this.applyFilterModal = true;
    this.modalDialog.open(this.filterModal!, {
      width: '720px',
    });
  }

  //Función que abre el modal de añadir ANS.
  openModalAddAns() {
    this.modalDialog.open(this.AddAnsModal!);
  }

  //Función que abre el modal de añadir ANS.
  openModalGeneralConditions() {
    this.modalDialog.open(this.generalConditions!);
  }

  //ventana para confirmar si se aplica la actualizacvión de condiciones generales, a todas las ANS o no.
  popUpConfirm(
    title: string,
    message: string,
    firstButton: string,
    secondButton: string
  ) {
    Swal.fire({
      title: title,
      html: message,
      icon: 'warning',
      showDenyButton: true,
      showCancelButton: true,
      confirmButtonText: firstButton,
      denyButtonText: secondButton,
      confirmButtonColor: 'black',
      denyButtonColor: 'black',
      reverseButtons: true,
    }).then((result) => {
      /* Read more about isConfirmed, isDenied below */
      if (result.isConfirmed) {
        this.modifyAll = false;
        this.updateGeneralConditionsMassive();
      } else if (result.isDenied) {
        this.modifyAll = true;
        this.updateGeneralConditionsMassive();
      }
    });
  }

  //Función que se ejecuta al presionar el boton Añadir ANS dentro del modalde AÑADIR ANS.
  addAns() {
    let payload = {
      saveAns: true,
      editAns: false,
    };
    this._ansService.setCurrentFormAns(payload);
  }

  //Función que se ejecuta al presionar el boton Guardar cambios dentro del modal.
  editAns() {
    let payload = {
      saveAns: false,
      editAns: true,
    };
    this._ansService.setCurrentFormAns(payload);
  }

  //Limpia los filtros del modal filtrar.
  resetSearch() {
    this.formFilter.reset();
    this.applyFilterModal = false;
    this.resetFilterLists();
    this.applyFilter();
  }

  //Función que aplica el los filtros a la tabla de ANS.
  applyFilter() {
    const payload: FilterCustomerAnsModel = {
      idBusinessCountry: this.idBusinessCountry,
      idProcess: this.formFilter.get('idProcess')?.value,
      idProduct: this.formFilter.get('idProduct')?.value,
      idStage: this.formFilter.get('idStage')?.value,
      idState: this.formFilter.get('idState')?.value,
      order: this.order,
      page: 0,
      pageSize: 10,
      bActive: this.getAnsStatus(
        this.formFilter.get('active')?.value,
        this.formFilter.get('inactive')?.value
      ),
      keyword: this.keyword,
    };
    this.filterGeneralANSData(payload);
  }

  //Función que obtiene el evento de validez del formulario de Añadir ANS.
  getValidFormAddAns(event: boolean) {
    this.validFormAddAns = event;
  }

  //Evento que obtiene la acción a realizar en el modal ( create o edit).
  getModalAction(event: any) {
    if (event === 'edit') {
      this.showBtnEdit = true;
    } else {
      this.showBtnEdit = false;
    }
  }

  //Evento que detecta cuando se ha cerrado el modal.
  modalCloseEvent(event: boolean) {
    this.pkIIdGeneralAns = 0;
    this.titleModal = this._translateService.instant(
      'AnsConfiguration.ModalAns.GeneralsTab.ModalTitleAdd'
    );
  }

  //Obtiene la acción que indica si se cierra el modal o no.
  getCloseModal(event: boolean) {
    this.modalDialog.closeAll();
    this.getGeneralANSByIdBusineesCountry(
      this.idBusinessCountry,
      this.pageIndex + 1
    );
  }

  //Obtiene los eventos de paginación de la tabla.
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex + 1;
    this.pageIndex = this.pageIndex - 1;
    this.pageSize = event.pageSize;
    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 1;
    }

    if (this.applyFilterModal) {
      const payload: FilterCustomerAnsModel = {
        idBusinessCountry: this.idBusinessCountry,
        idProcess: this.formFilter.get('idProcess')?.value,
        idProduct: this.formFilter.get('idProduct')?.value,
        idStage: this.formFilter.get('idStage')?.value,
        idState: this.formFilter.get('idState')?.value,
        order: this.order,
        page: this.pageIndex,
        pageSize: this.pageSize,
        bActive: this.getAnsStatus(
          this.formFilter.get('active')?.value,
          this.formFilter.get('inactive')?.value
        ),
        keyword: this.keyword,
      };
      this.filterGeneralANSData(payload);
    } else {
      this.getGeneralANSByIdBusineesCountry(
        this.idBusinessCountry,
        this.pageIndex + 1
      );
    }
  }

  //Obtiene la información del formulario Modificar condiciones generales.
  getCurrentFormValue(event: any) {
    this.massiveGeneralConditionsForm = event.formValue;
    this.validateFormGeneralConditions = event.formValid;
  }

  //Valida si la actualización de condiciones generales, se aplican a todas las ANS o no.
  validateUpdateGeneralConditionMassive() {
    this.checkExistParticularCondition(this.idBusinessCountry);
  }

  //Cambia el valor de tipo de días a un numero.
  parseTypeDaysValue(value: string): number {
    switch (value) {
      case 'Habiles':
        return TypeDayEnum.Habiles;
      case 'Calendario':
        return TypeDayEnum.Calendario;

      default:
        return TypeDayEnum.Default;
    }
  }

  //Función que depende los check seleccioando devuelve el valor esperado por el back.
  getAnsStatus(active: boolean, inactive: boolean) {
    if (active && inactive) {
      return null;
    }
    if (!active && !inactive) {
      return null;
    }
    if (active && !inactive) {
      return true;
    }
    if (!active && inactive) {
      return false;
    }

    return null;
  }

  //Cambia el valor del campo de la tabla (Condiciones) según sea el valor de llave bSpecificCondition
  parserConditionsTable(value: any) {
    if (value.bSpecificCondition) {
      return 'Particulares';
    } else {
      return 'Generales';
    }
  }

  //Acciones que se ejecutan cuándo se destuye el componete actual.
  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  // Gets ANS without pagination by BusinessCountry
  getDataForANSFilter(idBusinessCountry: number,){
    this._workFlowService
      .GetFilterGeneralANSByIdBusineesCountry(
        idBusinessCountry
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.filterDataSource = resp.result?.filterDataSource;
          this.processes = resp.result?.uniqueProcesses;
          this.uniqueProducts = resp.result?.uniqueProducts;
          this.products = this.uniqueProducts;
          this.uniqueStages = resp.result?.uniqueStages;
          this.uniqueStates = resp.result?.uniqueStates;
        }
      });
  }

  resetFilterLists(){
    this.products = this.uniqueProducts;
    this.stage = [];
    this.state = [];
  }
}
