<div class="row mt-5">
    <h5 class="fw-bold mb-2">
      {{ 'SectionClientPortal.BenefitsTitle' | translate }}
    </h5>
    <mat-label>
      {{ 'SectionClientPortal.BenefitsText' | translate }}
    </mat-label>

    <app-table
      [displayedColumns]="estructTableBenefits"
      [data]="dataTableBenefits"
      (iconClick)="controller($event)"
    ></app-table>

    <div class="row" *ngIf="dataTableBenefits.length < maxItems">
      <div class="col">
        <button
          type="button"
          mat-raised-button
          color="primary"
          (click)="openModal()"
        >
          {{ "SectionClientPortal.AddBenefit" | translate }}
        </button>
      </div>
    </div>
  </div>


  <ng-template #createEditBenefit>
    <app-modal2 [titleModal]="(formBenefit.get('id')?.value ? 'SectionClientPortal.UpdateBenefitTitle' : 'SectionClientPortal.AddBenefitTitle') | translate">
      <ng-container body>
        <form [formGroup]="formBenefit">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'SectionClientPortal.BenefitText' | translate}}
            </mat-label>
            <input matInput formControlName="vName" PreventionSqlInjector/>
            <mat-error *ngIf="utilsSvc.isControlHasError(formBenefit, 'vName', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
  
        </form>
      </ng-container>
      <ng-container customButtonRight>
        <div class="modal-footer">
          <button mat-raised-button color="primary" type="button" class="" (click)="saveBenefit()">
            {{ "Save" | translate }}
          </button>
        </div>
      </ng-container>
    </app-modal2>
  </ng-template>