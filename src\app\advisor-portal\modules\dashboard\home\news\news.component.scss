.cont-title {
  h4 {
    font-weight: 600 !important;
  }
}

.cont-new {
  display: flex;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  align-content: center;
  gap: 48px;
  align-self: stretch;
  flex-wrap: wrap;
}
.new {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 2px 8px 0px #ccc;
  width: 300px;
  height: 430px;
}
.cont-img {
  display: flex;
  align-items: flex-start;
  flex: 1 0 0;
  align-self: stretch;
  height: 170px;
  width: max-content;
  text-align: center;
  img {
    width: 300px;
    height: 170px;
  }
}
.img-new {
  flex: 1 0 0;
  align-self: stretch;
  border-radius: var(--Sharp, 0px);
  background: lightgray 50% / cover no-repeat, #d9d9d9;
}
.cont-info {
  p {
    margin: 0px !important;
  }
  display: flex;
  padding: 16px 24px 8px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}
.cont-title-new {
  height: 65px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: start;
}

.title-new {
  h5 {
    margin: 0px !important;
  }
  //   height: 47.5px;
  align-self: stretch;
  color: var(--wtw-brand-brand-wtw-onyx, #2f2c31);
  font-family: Inter;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 28px; /* 140% */
  letter-spacing: -0.4px;
}

.paragraph-info {
  align-self: stretch;
  color: var(--wtw-brand-brand-wtw-onyx, #2f2c31);
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  letter-spacing: -0.32px;
  height: 100px;
  overflow: hidden;
}

.keep-reading {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}
.btn-keep-reading {
  font-weight: 500;
}
