import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatTooltipModule } from '@angular/material/tooltip';


@Component({
  selector: 'app-details-policy',
  standalone: true,
  imports: [CommonModule, TranslateModule, MatTooltipModule],
  templateUrl: './details-policy.component.html',
  styleUrls: ['./details-policy.component.scss'],
})
export class DetailsPolicyComponent implements OnInit {
  @Input() detailsPolicyData: any[] = [];
  @Input() policyNumber: string = '';
  @Input() idWtw: number = 0;
  @Input() idPolicyType: number = 0;
  @Input() policyStatus: string = '';
  @Input() fieldKeyName: string = '';
  @Input() fieldKeyValue: string = '';
  @Input() isRisk: boolean = false;
  @Input() motive: string = '';

  constructor(private _translateService: TranslateService) {}

  ngOnInit(): void {}

  // Función para obtener la clave del objeto de la sección actual
  getSectionKey(section: any): string {
    return Object.keys(section)[0];
  }

  //Función que cambia el valor a poner en el pipe de traducción según sea el caso.
  changeStatusPolicy(status: string): string {
    switch (status) {
      case 'Activa':
        return 'Activa';
      case 'Inactiva':
        return 'Inactiva';
      case 'Vencida':
        return 'Expired';
      case 'Cancelación':
        return 'Cancellation';
      case 'Activo':
        return 'Activo';
      case 'Vencido':
        return 'Vencido';

      default:
        return status;
    }
  }
}
