import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { catchError, debounceTime, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { GlobalSelectModel } from 'src/app/shared/models/shared';
import { MeasurementModel, TypeDayEnum } from 'src/app/shared/models/ans';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-general-conditions',
  standalone: true,
  imports: [
    CommonModule,
    MatSelectModule,
    MatSlideToggleModule,
    TranslateModule,
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatInputModule,
    MatCheckboxModule,
  ],
  templateUrl: './general-conditions.component.html',
  styleUrls: ['./general-conditions.component.scss'],
})
export class GeneralConditionsComponent implements OnInit, OnChanges {
  //Variables para manejo de información entre componentes.
  @Output() currentFormValue = new EventEmitter<any>();
  @Input() ansToEdit: any = {};

  //Variables relacionadas con el formulario.
  form: FormGroup = this._fb.group({});
  showRest: boolean = false;
  showCountryHoliday: boolean = true;

  //Variables relacionadas con los select.
  typeDays: MeasurementModel[] = [];
  timeZone: GlobalSelectModel[] = [];
  startOfTheDay: MeasurementModel[] = [];
  endOfTDay: MeasurementModel[] = [];
  restStart: MeasurementModel[] = [];
  endOfRest: MeasurementModel[] = [];
  countries: any = [];
  daysOfWeek: string[] = [
    this._translateService.instant(
      'AnsConfiguration.ModalAns.GeneralsTab.Sunday'
    ),
    this._translateService.instant(
      'AnsConfiguration.ModalAns.GeneralsTab.Monday'
    ),
    this._translateService.instant(
      'AnsConfiguration.ModalAns.GeneralsTab.Tuesday'
    ),
    this._translateService.instant(
      'AnsConfiguration.ModalAns.GeneralsTab.Wednesday'
    ),
    this._translateService.instant(
      'AnsConfiguration.ModalAns.GeneralsTab.Thursday'
    ),
    this._translateService.instant(
      'AnsConfiguration.ModalAns.GeneralsTab.Friday'
    ),
    this._translateService.instant(
      'AnsConfiguration.ModalAns.GeneralsTab.Saturday'
    ),
  ];

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _parametersService: ParametersService,
    public utilsService: UtilsService
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.getTipeOfDays();
    this.getTimeZone();
    this.getHoursOfWork();
    this.getCountriesByHoliday();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['ansToEdit']) {
      // this.form.patchValue(changes['ansToEdit'].currentValue);
      if (!(Object.keys(changes['ansToEdit'].currentValue).length === 0)) {
        this.assignValues(this.ansToEdit);
      }
    }
  }

  //Declaración del formulario.
  initForm() {
    this.form = this._fb.group({
      bIncludeRestDay: [true],
      bIncludeHolidays: [true],
      iTimeZone: [null, [Validators.required]],
      iTypeDays: [null, [Validators.required]],
      daysOfWeek: this._fb.array([]),
      fkIIdCountryHoliday: [null, [Validators.required]],
    });
    this.daysOfWeek.forEach(() => this.addDayOfWeek());

    this.form.get('bIncludeHolidays')?.valueChanges.subscribe({
      next: (currentValue) => {
        if (currentValue) {
          this.showCountryHoliday = true;
          this.form
            .get('fkIIdCountryHoliday')
            ?.setValidators(Validators.required);
          this.form.get('fkIIdCountryHoliday')?.updateValueAndValidity();
        } else {
          this.form.get('fkIIdCountryHoliday')?.setValue(null);
          this.form.get('fkIIdCountryHoliday')?.clearValidators();
          this.form.get('fkIIdCountryHoliday')?.updateValueAndValidity();
          this.showCountryHoliday = false;
        }
      },
    });

    this.form.get('bIncludeRestDay')?.valueChanges.subscribe({
      next: (currentValue) => {
        if (currentValue) {
          this.showRest = false;
        } else {
          this.showRest = true;
        }
      },
    });

    this.form?.valueChanges.pipe(debounceTime(600)).subscribe({
      next: (data) => {
        if (data) {
          let payload = {
            formValue: data,
            formValid: this.form.valid,
          };
          this.currentFormValue.emit(payload);
        }
      },
    });
  }

  //Obtiene el valor del control daysOfWeek.
  get daysOfWeekFormArray() {
    return this.form.get('daysOfWeek') as FormArray;
  }

  //Función que valida el día de la semana que está chekeado.
  isDayOfWeekChecked(index: number) {
    return this.daysOfWeekFormArray.at(index).get('isChecked')?.value;
  }

  //Crea un control con los 4 select para un día de la semana.
  addDayOfWeek() {
    const dayOfWeekGroup = this._fb.group({
      day: this.getDayName(this.daysOfWeekFormArray.length),
      startOfTheDay: [''],
      endOfTDay: [''],
      restStart: '',
      endOfRest: '',
      isChecked: [false],
    });

    dayOfWeekGroup.get('isChecked')?.valueChanges.subscribe({
      next: (isChecked) => {
        const startOfTheDayControl = dayOfWeekGroup.get('startOfTheDay');
        const endOfTDayControl = dayOfWeekGroup.get('endOfTDay');
        if (isChecked) {
          startOfTheDayControl?.setValidators(Validators.required);
          endOfTDayControl?.setValidators(Validators.required);
        } else {
          startOfTheDayControl?.clearValidators();
          endOfTDayControl?.clearValidators();
          startOfTheDayControl?.updateValueAndValidity();
          endOfTDayControl?.updateValueAndValidity();
        }
      },
    });

    this.daysOfWeekFormArray.push(dayOfWeekGroup);
  }

  //Obtiene el nombre del día de la semana.
  getDayName(index: number) {
    return this.daysOfWeek[index];
  }

  //Función que obtiene los horaios de trabajo.
  getHoursOfWork() {
    this._parametersService
      .getParameters('Hours_Of_Work')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.startOfTheDay = resp;
          this.endOfTDay = resp;
          this.restStart = resp;
          this.endOfRest = resp;
        } else {
        }
      });
  }

  //Función que obtiene las zonas horarias.
  getTimeZone() {
    this._parametersService
      .getParameters('Time_Zone')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.timeZone = resp;
        } else {
        }
      });
  }

  //Función que obtiene los tipos de días.
  getTipeOfDays() {
    this._parametersService
      .getParameters('Type_Days')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.typeDays = resp;
        } else {
        }
      });
  }

  //Obtiene los paises que tiene festivos asignados.
  getCountriesByHoliday() {
    this._parametersService
      .getCountriesByHoliday()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.countries = resp.result;
        }
      });
  }

  //Función que asigna valores al formulario para la acción de editar.
  assignValues(data: any) {
    this.form.patchValue(data);
    this.form
      .get('iTypeDays')
      ?.setValue(this.parseTypeDaysValue(data.iTypeDays));
    this.form.get('daysOfWeek')?.patchValue(JSON.parse(data.vJson));
  }

  //Cambia el valor de tipo de días a una cadena.
  parseTypeDaysValue(value: number): string {
    switch (value) {
      case TypeDayEnum.Habiles:
        return 'Habiles';
      case TypeDayEnum.Calendario:
        return 'Calendario';

      default:
        return '';
    }
  }
}
