<div class="mt-3">
    <app-choose-country-and-company
        (valueForm)="getValueForm($event)"></app-choose-country-and-company>
</div>

<div class="cont-subtitle">
    <h2>{{"AnsConfiguration.Reports.ReportTitle" | translate}}</h2>
</div>

<form [formGroup]="formReportAns">
    <div class="row">
        <!-- Fecha inicial -->
        <div class="col-md-5">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{"AnsConfiguration.Reports.StartDateLabel" | translate}}
                </mat-label>
                <input matInput [matDatepicker]="initialDate" formControlName="startDate" />
                <mat-datepicker-toggle matIconSuffix [for]="initialDate"></mat-datepicker-toggle>
                <mat-datepicker #initialDate></mat-datepicker>
            </mat-form-field>
        </div>
        <!-- Fecha final -->
        <div class="col-md-5">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{"AnsConfiguration.Reports.EndDateLabel" | translate}}
                </mat-label>
                <input matInput [matDatepicker]="finalalDate" formControlName="endDate" />
                <mat-datepicker-toggle matIconSuffix [for]="finalalDate"></mat-datepicker-toggle>
                <mat-datepicker #finalalDate></mat-datepicker>
            </mat-form-field>
        </div>
        <!-- Seleccionar ANS -->
        <div class="col-md-2">
            <button [disabled]="!disableANSselect" type="button" mat-raised-button class="btn-custom"
                (click)="openModal('selectAns')">
                <strong> {{"AnsConfiguration.Reports.SelectANS" | translate}}</strong>
            </button>
        </div>
    </div>

    <div class="d-flex justify-content-initial align-items-center mt-3">
        <!-- Filtrar por usuario -->
        <div class="">
            <mat-slide-toggle formControlName="filterByUser">
                {{"AnsConfiguration.Reports.FilterByUserLabel" | translate}}
            </mat-slide-toggle>
        </div>
        <!-- Seleccionar usuario -->
        <div class="mt-2 mx-5" *ngIf="filterByUser">
            <mat-form-field class="w-100">
                <mat-label> {{"AnsConfiguration.Reports.SelectUserLabel" | translate}}</mat-label>
                <mat-select formControlName="idUser">
                    <mat-option *ngFor="let item of users" [value]="item.pkIIdUser">{{ item.vPersonName}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
    </div>

    <!-- Generar Reporte -->
    <div class="d-flex justify-content-center align-items-center mb-2">
        <div class="">
            <button type="button" class="w-auto" [disabled]="!formReportValid" (click)="downloadANSReport()"
                mat-raised-button color="primary">
                {{"AnsConfiguration.Reports.GenerateReportButton" | translate}}
                <mat-icon iconPositionEnd class="material-symbols-outlined">
                    lab_profile
                </mat-icon>

            </button>
        </div>
    </div>
</form>

<div class="cont-subtitle mt-3">
    <h2>{{"AnsConfiguration.Reports.PreviousReportsTitle" | translate}}</h2>
</div>

<div class="row mt-4">
    <app-table [displayedColumns]="estructTableReport" [data]="dataTableReport" [IsStatic]="false"
        [pageIndex]="pageIndexReport" [pageSize]="pageSizeReport" [amountRows]="amountRowsReport"
        (pageChanged)="onPageChangeReport($event)" (iconClick)="controllerReport($event)"></app-table>
</div>

<!-- Modal Seleccionar ANS -->
<ng-template #selectAns>
    <app-modal2 [titleModal]="'AnsConfiguration.Subtitle' | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <div class="row mt-2">
                <div class="col-md-9">
                    <mat-form-field class="w-100">
                        <mat-label>
                            {{ "AnsConfiguration.FilterByLabel" | translate }}
                        </mat-label>
                        <input (keyup.enter)="searchAns()" [(ngModel)]="keyword" (ngModelChange)="onInputChange($event)"
                            matInput type="text" class="form-control" placeholder="Filtrar por:" />
                        <mat-icon class="hand click" (click)="searchAns()" matSuffix>search</mat-icon>
                    </mat-form-field>
                </div>
                <div class="col-md-3">
                    <div class="d-flex">
                        <button type="button" class="mx-3" (click)="openModal('filterModal')" mat-raised-button
                            color="primary">
                            {{ "Filter" | translate }}
                            <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
                        </button>
                        <div class="btn-group dropup">
                            <button type="button" id="orderAns" data-bs-toggle="dropdown" aria-expanded="false"
                                mat-raised-button color="primary" class="dropdown-toggle">
                                {{ "Quotation.SorButton" | translate }}
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="orderAns">
                                <li>
                                    <a class="dropdown-item" (click)="orderTable(0)">
                                        {{ "AnsConfiguration.MoreRecent" | translate }}</a>
                                </li>
                                <li>
                                    <a class="dropdown-item" (click)="orderTable(1)">{{
                                        "AnsConfiguration.Oldest" | translate
                                        }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-2">
                <app-table [displayedColumns]="estructTableAns" [data]="dataTableAns" [IsStatic]="false"
                    [pageIndex]="pageIndexAns" [pageSize]="pageSizeAns" [amountRows]="amountRowsAns"
                    (pageChanged)="onPageChangeAns($event)" (iconClick)="controllerAns($event)"></app-table>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <button type="button" class="w-auto" [disabled]="ansIdReporting.length == 0" (click)="addAns()"
                        mat-raised-button color="primary">
                        {{ "AnsConfiguration.AddAnsButton" | translate }}
                        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
                    </button>
                </div>
            </div>
        </ng-container>
    </app-modal2>
</ng-template>

<!-- Modal Filtro -->
<ng-template #filterModal>
    <app-modal2 [titleModal]="'AnsConfiguration.Modal.Title' | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <form [formGroup]="formFilterAns">
                <div class="row">
                    <h5>{{ "AnsConfiguration.Modal.Subtitle" | translate }}</h5>
                </div>
                <div class="row">
                    <div class="col-md-12 p-0 mb-2">
                        <mat-checkbox class="example-margin" formControlName="active">{{
                            "AnsConfiguration.Modal.Active" | translate
                            }}</mat-checkbox>
                        <mat-checkbox class="example-margin" formControlName="inactive">{{
                            "AnsConfiguration.Modal.Inactive" | translate
                            }}</mat-checkbox>
                    </div>
                </div>
                <div class="row">
                    <!-- Select de Proceso -->
                    <div class="col-md-6">
                        <mat-form-field id="processes" class="w-100">
                            <mat-label>
                                {{ "AnsConfiguration.Table.Process" | translate }}
                            </mat-label>
                            <mat-select formControlName="idProcess">
                                <mat-option *ngFor="let item of processes" [value]="item.pkIIdProcessFamily">{{
                                    item.vNameProcess }}</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <!-- Select de Producto -->
                    <div class="col-md-6">
                        <mat-form-field id="product" class="w-100">
                            <mat-label>
                                {{ "AnsConfiguration.Table.Product" | translate }}
                            </mat-label>
                            <mat-select formControlName="idProduct">
                                <mat-option *ngFor="let item of products" [value]="item.pkIIdProductModule">{{
                                    item.vName }}</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>

                <div class="row">
                    <!-- Select de Etapa -->
                    <div class="col-md-6">
                        <mat-form-field id="stage" class="w-100">
                            <mat-label>
                                {{ "AnsConfiguration.Table.Stage" | translate }}
                            </mat-label>
                            <mat-select formControlName="idStage">
                                <mat-option *ngFor="let item of stage" [value]="item.pkIIdStage">{{ item.vNameStage
                                    }}</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <p class="description">
                            {{ "AnsConfiguration.Modal.DescriptionStage" | translate }}
                        </p>
                    </div>
                    <!-- Select de Estado -->
                    <div class="col-md-6">
                        <mat-form-field id="product" class="w-100">
                            <mat-label> {{ "Status" | translate }} </mat-label>
                            <mat-select formControlName="idState">
                                <mat-option *ngFor="let item of state" [value]="item.pkIIdStageByState">{{ item.vState
                                    }}</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <p class="description">
                            {{ "AnsConfiguration.Modal.DescriptionState" | translate }}
                        </p>
                    </div>
                </div>
            </form>
        </ng-container>

        <ng-container customButtonCenter>
            <button (click)="resetSearch()" type="button" mat-raised-button class="btn-reset">
                <strong> {{ "MyQuotation.AllQuotation.Clean" | translate }}</strong>
            </button>
        </ng-container>

        <ng-container customButtonRight>
            <button (click)="applyFilter()" type="button" mat-raised-button color="primary">
                {{ "Apply" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>