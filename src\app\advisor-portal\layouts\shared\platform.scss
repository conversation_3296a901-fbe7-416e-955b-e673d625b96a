.platform {
  display: flex;
  width: 100vw;
  align-items: stretch;
  justify-content: start;
  overflow: hidden;

  main {
    display: inline-block;
    width: 100%;
    section.content-no-navbar {
			padding: 40px 32px;
			width: 100%;
      overflow-y: auto;
		}
		section.content {
			padding: 40px 32px;
			width: 95%;
      margin-left: 60px;
      margin-top: 70px;
      overflow-y: auto;
		}
    section.content-expanded {
			padding: 40px 32px;
			width: auto;
      margin-left: 220px;
      margin-top: 70px;
      overflow-y: auto;
		}
    .cont-footer{
      margin-top: 25%;
      display: flex;
      align-items: end;
      justify-content: center;
    }
  }
}