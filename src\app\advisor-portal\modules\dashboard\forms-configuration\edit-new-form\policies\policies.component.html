<div class="col-12 col-md-12">
  <form [formGroup]="createPolicyForm">
      <div class="col-md-6 col-sm-12">
          <mat-slide-toggle   class="mb-3" style="margin-top: 10px;" formControlName="active" (change)="onToggleChange($event)">
            <span color="primary"> {{"MappingForm.PolicyActiveText" | translate}} </span>
          </mat-slide-toggle>
          <mat-error *ngIf="utilsService.isControlHasError(createPolicyForm, 'active', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error>


      </div>

      <div class="col-12 col-md-12">
          <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>{{"MappingForm.PolicyStatusLabel" | translate}}</mat-label>
              <mat-select [required]="true" formControlName="statePolicyList"  >
                <mat-option *ngFor="let state of listOfPolicyStates" [value]="state.pkIIdStageByState">{{ state.vState }}</mat-option>
              </mat-select>
          <mat-error *ngIf="utilsService.isControlHasError(createPolicyForm, 'statePolicyList', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error>
          </mat-form-field>
      </div>

      <div class="col-12 col-md-12">
          <strong><mat-label>{{"MappingForm.MappingTabTitle" | translate}}</mat-label></strong>
      </div>
      <div class="col-12 col-md-12">
        <span color="primary">{{"MappingForm.PolicyCreationText" | translate}}</span>
      </div>

      <div class="col-12 col-md-12" style="margin-top: 25px;">

      <!-- Aseguradora -->
       <div class="col-12 col-md-12">
        <mat-form-field class="w-100" appearance="fill">
         <mat-label>{{"PolicyConfiguration.GeneralInformation.Insurance" | translate}}</mat-label>
         <mat-select formControlName="insurancesList" (selectionChange)="getAllProductsByInsurance()">
                <mat-option *ngFor="let insurance of listOfInsurances" [value]="insurance.pkIIdInsuranceCompanies">{{ insurance.vName }}</mat-option>
            </mat-select>
            <mat-error *ngIf="utilsService.isControlHasError(createPolicyForm, 'insurancesList', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
        </mat-form-field>
    </div>
    <!-- Producto -->
    <div class="col-12 col-md-12">
        <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ "PolicyConfiguration.GeneralInformation.Product" | translate }}</mat-label>
            <mat-select formControlName="productList" (selectionChange)="getPoliciesId()">
                <mat-option *ngFor="let product of listOfProducts" [value]="product.pkIIdProduct">{{ product.vProductName }}</mat-option>
            </mat-select>
            <mat-error *ngIf="utilsService.isControlHasError(createPolicyForm, 'productList', 'required')">
                {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
        </mat-form-field>
    </div>

     <!-- ID -->
     <div class="col-12 col-md-12">
        <mat-form-field appearance="outline"class="select-look w-50 m-auto w-100">
           <mat-label> {{ "PolicyReplicationConfiguration.ModalIdLabel" | translate }} </mat-label>
          <mat-select formControlName="WTWIdList" (selectionChange)="getAllDataPoliciesId()">
            <mat-option *ngFor="let idWTW of listOfWTWIds" [value]="idWTW.idPolicy">
              {{ idWTW.idPolicy }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="utilsService.isControlHasError(createPolicyForm, 'WTWIdList', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error>
        </mat-form-field>
     </div>
      </div>

  </form>

  <div class="ng-container" *ngIf="showTable">
    <div class="col-12 col-md-12">
       <strong><mat-label >{{"MappingForm.MappingElementsForInsuranceLabel" | translate}}</mat-label></strong>
    </div>
    <div class="col-12 col-md-12" style="margin-top: 25px;">
      <mat-tab-group  (selectedTabChange)="onTabChange($event)">
        <mat-tab label="{{'MappingForm.PolicyDataLabel' | translate }}" *ngIf="showPolicyTab">
           <app-table [displayedColumns]="estructMappingTable"   [data]="policyDataMappingTable "   (iconClick)="controller($event)">
           </app-table>
       </mat-tab>

       <mat-tab label="{{'MappingForm.InsuredLabel' | translate }}" *ngIf="showInsuredTab">
         <app-table [displayedColumns]="estructMappingTable"   [data]="insuredMappingTable "   (iconClick)="controller($event)">
         </app-table>
       </mat-tab>

       <mat-tab label="{{'MappingForm.TakerLabel' | translate }}" *ngIf="showTakerTab">
         <app-table [displayedColumns]="estructMappingTable"   [data]="takerMappingTable "   (iconClick)="controller($event)">
         </app-table>
       </mat-tab>
       <mat-tab label="{{'MappingForm.BeneficiaryLabel' | translate }}" *ngIf="showBeneficiaryTab && !this.allSinAsignar">
         <app-table [displayedColumns]="estructMappingBeneficiaryTable"   [data]="beneficiaryMappingTable "   (iconClick)="controller($event)">
         </app-table>

       </mat-tab>

       <mat-tab label="{{ this.tabName | translate }}" *ngIf="showOthersTab">
         <app-table [displayedColumns]="estructMappingTable"   [data]="othersMappingTable "   (iconClick)="controller($event)">
         </app-table>
     </mat-tab>
      </mat-tab-group>
      <div class="row">
        <div class="col-md-12">
          <button type="button" class="w-auto" mat-raised-button color="primary" (click)="checkFields()">
              {{ "MappingForm.ModalSaveMappingLabel" | translate }}

            <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
          </button>
        </div>
      </div>
    </div>

  </div>



</div>

<!-- Modal mapeo de campos -->
<ng-template #MapElementModal>
  <app-modal2 [titleModal]="modalTitle" (closeModal)="onModalClose()">
    <ng-container body>
      <form [formGroup]="mappingModalForm">
        <div class="form-group">

          <mat-form-field appearance="outline" class="w-100">
           <!-- <mat-label>{{'MappingForm.ModalPolicyFieldLabel' | translate }}</mat-label> -->
           <mat-label>{{this.modalLabelTextPolicy}}</mat-label>
            <input matInput formControlName="policyField"  />
          </mat-form-field>

          <mat-form-field appearance="outline"class="select-look w-50 m-auto w-100">
            <!-- <mat-label> {{'MappingForm.ModalFormFieldLabel' | translate }}</mat-label> -->
            <mat-label> {{this.modalLabelTextForm}}</mat-label>
           <mat-select formControlName="fieldFormList">
              <mat-option *ngFor="let formField of listOfFormFields" [value]="formField.pkIIdFieldModule">
                  {{ formField.vNameField }}
              </mat-option>
           </mat-select>
           <mat-error *ngIf="utilsService.isControlHasError(mappingModalForm, 'fieldFormList', 'required')">{{ "ThisFieldIsRequired" | translate }}</mat-error>
         </mat-form-field>

        </div>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button type="button" mat-raised-button color="primary" (click)="changesAssigment()" > {{ "Save" | translate }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </ng-container>

  </app-modal2>
</ng-template>

