import { Routes } from '@angular/router';
import { AuthGuard } from 'src/app/shared/guard/auth/auth-guard.guard';
import { LoginGuard } from 'src/app/shared/guard/auth/login.guard';



export default [
  {
    path: 'login',
    canActivate: [LoginGuard ],
    loadComponent: () =>
      import('./login/login.component').then((c) => c.LoginComponent),
  },
  {
    path: 'new-password',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./new-password/new-password.component').then(
        (c) => c.NewPasswordComponent
      ),
  },
  {
    path: 'Assign-Password/:token',
    loadComponent: () =>
      import('./new-password/new-password.component').then(
        (c) => c.NewPasswordComponent
      ),
  },
  {
    path: 'recover-password',
    loadComponent: () =>
      import('./recover-password/recover-password.component').then(
        (c) => c.RecoverPasswordComponent
      ),
  },
  {
    path: 'register-external-user/:idBusinessByCountry/:idRoleBusiness/:idSsesion/:token',
    loadComponent: () =>
      import('../dashboard/users/register-external-user/register-external-user.component').then(
        (c) => c.RegisterExternalUserComponent
      ),
  }
] as Routes;
