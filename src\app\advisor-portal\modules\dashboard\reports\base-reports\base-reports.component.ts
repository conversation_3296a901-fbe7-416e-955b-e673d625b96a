import { Component, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { ReportsGeneratedComponent } from '../reports-generated/reports-generated.component';
import { Router } from '@angular/router';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MatTabsModule } from '@angular/material/tabs';
import { ReportConfigurationComponent } from '../reports-configurations/report-configuration/report-configuration.component';
import { ReportConfigurationPolicyComponent } from "../report-configuration-policy/report-configuration-policy.component";
@Component({
  selector: 'app-base-reports',
  standalone: true,
  templateUrl: './base-reports.component.html',
  styleUrls: ['./base-reports.component.scss'],
  imports: [
    ReportsGeneratedComponent,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    BreadcrumbComponent,
    MatTabsModule,
    ReportConfigurationComponent,
    ReportConfigurationPolicyComponent
],
})
export class BaseReportsComponent implements OnInit {
  inicio: string = this._translateService.instant('Inicio');
  reports: string = this._translateService.instant('Reports.Title');

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.reports, link: '/dashboard/reports' },
  ];

  constructor(
    private _translateService: TranslateService,
    private _customRouter: CustomRouterService

  ) {}

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant('Inicio');
      this.reports = this._translateService.instant('Reports.Title');
      this.sections[0].label = this.inicio;
      this.sections[1].label = this.reports;
    });
  }
  redirectToRoute() {
    this._customRouter.navigateByUrl('dashboard/reports/new-report/0/0');
  }
}
