import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy, Output, EventEmitter, ViewChild, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subscription, catchError, of } from 'rxjs';
import { ActionsToCreateComponent } from 'src/app/shared/components/actions-to-create/actions-to-create.component';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';

import { AppState } from 'src/app/store/app.reducers';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CoverageProductModel, FrequentQuestionProductModel, ProductParent } from 'src/app/shared/models/product/product.model';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ImageUploadModel } from 'src/app/shared/models/file';
import { FileService } from 'src/app/shared/services/file/file.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { PolicyTypeByBusinessCountryDto } from 'src/app/shared/models/policy';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';


@Component({
  selector: 'app-step-product-data',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    MatInputModule,
    MatFormFieldModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    GenericImagePickerComponent,
    ActionsToCreateComponent,
    TranslateModule,
    PreventionSqlInjectorDirective,
    Modal2Component,
    MatDatepickerModule,
    MatNativeDateModule,
  ],
  templateUrl: './step-product-data.component.html',
  styleUrls: ['./step-product-data.component.scss']
})
export class StepProductDataComponent implements OnInit, OnDestroy {
  @ViewChild('createEditCoverage') createEditCoverage?: TemplateRef<any>;
  @ViewChild('createEditQuestion') createEditQuestion?: TemplateRef<any>;

  _currentModal: MatDialogRef<any> | null = null;


  productSubs?: Subscription;
  productParentSubs?: Subscription;
  formSubs?: Subscription;
  formQuestionSubs?: Subscription;
  productsParent: Array<ProductParent> = [];
  policyTypes: PolicyTypeByBusinessCountryDto[] = [];
  maxItems: number = 6;
  minItems: number = 1;
  idBusinessByCountry: number = 0;
  imageSrc: string = ''

  @Output() formChange = new EventEmitter<any>();
  form: FormGroup = this._fb.group({
    id:[0],
      showCustomerPortal: [false, [Validators.required]],
      active: [true, [Validators.required]],
      name: [null, [Validators.required]],
      description: [null, Validators.required],
      logo: [null, [Validators.required]],
      logoCustomerPortal: [null],
      IdProductParent: [null, [Validators.required]],
      inCustomerPortal: [false, [Validators.required]],
      inAdvisorPortal: [true, [Validators.required]],
      listCoverages: [],
      comments: [null],
      fkIIdPolicyType: [null, [Validators.required]],
      idPolicyCode: [null],
      dValidityStart: [null],
      dValidityEnd: [null],
      coverages: [[]],
    questions: [[]]
    },
    {
    validators: this.portalCheckerValidator()
    }
  );

  formCoverage: FormGroup = this._fb.group({
    name: [null, Validators.required],
    logo: [''],
    index: [null]
  })

  formQuestion: FormGroup = this._fb.group({
    question: [null, Validators.required],
    answer: [null, Validators.required],
    index: [null]
  })

  estructTableCoverages: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Coverage'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyCoverage',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteCoverage',
      columnIcon: 'delete',
    },
  ];

  estructTableQuestions: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Question'),
      columnValue: 'vQuestion',
    },
    {
      columnLabel: this._translateService.instant('Product.Answer'),
      columnValue: 'vAnswer',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyQuestion',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteQuestion',
      columnIcon: 'delete',
    },
  ];

  get dataTableCoverages(){
    return this.form.get('coverages')?.value
  }

  set updateCoverages(newCoverages: Array<CoverageProductModel>){
    this.form.patchValue({coverages: newCoverages})
  }

  get dataTableQuestions(){
    return this.form.get('questions')?.value
  }

  set updateQuestions(newQuestions: Array<FrequentQuestionProductModel>){
    this.form.patchValue({questions: newQuestions})
  }

  portalCheckerValidator(): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      const controlCustomerPortal = formGroup.get('inCustomerPortal')?.value;
      const controlAdvisorPortal = formGroup.get('inAdvisorPortal')?.value;

      return (controlCustomerPortal === true || controlAdvisorPortal === true) ? null : { portalNotChecked: true };
    };
  }

  isCollectivePolicy: boolean = false;

  constructor(
    public router: Router,
    public utilsSvc: UtilsService,
    private _fb: FormBuilder,
    private _store: Store<AppState>,
    private _msgSvc: MessageService,
    private _parameterService: ParametersService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _modalDialog: MatDialog,
    private _fileService: FileService,
    private _cdr: ChangeDetectorRef,
    private _settingService: SettingService,
    private _policyService: PolicyService
  ) {}

  ngOnInit(): void {
    this.getDataSettingInit()
    this.productSubs = this._store.select('product').subscribe(p => {
      if (p.error) return this._msgSvc.messageError(p.error);
      this.form.patchValue(p.Product!);
      if (Number.isNaN(p.Product?.id))
        this.form.patchValue({inAdvisorPortal: true})

      if(p.Product?.comments === null ||  p.Product?.comments === undefined || p.Product.comments === ''){

        this.form.get('comments')?.patchValue(this._translateService.instant('Quotation.QuotationValidityMessage'));
      }
    });
    this.getProductParent();
    this.formSubs = this.form.valueChanges.subscribe(value => this.formChange.emit({...value}))

    this.getPolicyTypesForConfigurationProduct();

    // Listen for changes in the select value
    this.form.get('fkIIdPolicyType')?.valueChanges.subscribe((value) => {
      this.onPolicyTypeChange(value);
    });

    this.validateInitialValuePolicyType();
  }

/**
 * Validates the initial value of the policy type in the form.
 * 
 * - If an initial policy code (`idPolicyCode`) exists, it sets this value after a timeout
 *   and checks if it is "COL" (collective policy). If so, sets `isCollectivePolicy` to true.
 * - If no initial code is found but there is a selected policy type ID (`fkIIdPolicyType`),
 *   it retrieves the corresponding code and sets `idPolicyCode` in the form.
 * 
 * @returns {void}
 */
validateInitialValuePolicyType(): void {
  const valueInitial = this.form.get('idPolicyCode')?.value;
  const fkIIdPolicyType = this.form.get('fkIIdPolicyType')?.value;

  if (valueInitial) {
      setTimeout(() => {
          this.form.get('idPolicyCode')?.setValue(valueInitial);
          if (valueInitial === 'COL') {
              this.isCollectivePolicy = true;
              return;
          }
      }, 1000);
  } else if (this.policyTypes.length > 0 && fkIIdPolicyType) {
      const policyTypeCodeSelected = this.policyTypes.filter(
          (r) => r.pkIIdPolicyType === fkIIdPolicyType
      )[0]?.vCode;

      setTimeout(() => {
          this.form.get('idPolicyCode')?.setValue(policyTypeCodeSelected);
      }, 1000);

      if (policyTypeCodeSelected === 'COL') {
          this.isCollectivePolicy = true;
          return;
      }
  }
}

/**
* Handles changes to the policy type selection in the form.
* 
* - Sets the `idPolicyCode` based on the selected policy type ID (`value`).
* - If the selected code is "COL", it marks `isCollectivePolicy` as true.
* - Resets validity start and end dates if the selected type is not "COL".
* 
* @param {number} value - Selected policy type ID.
* @returns {void}
*/
onPolicyTypeChange(value: number): void {
  const policyTypeCodeSelected = this.policyTypes.filter(
      (r) => r.pkIIdPolicyType === value
  )[0]?.vCode;

  setTimeout(() => {
      this.form.get('idPolicyCode')?.setValue(policyTypeCodeSelected);
  }, 1000);

  if (policyTypeCodeSelected === 'COL') {
      this.isCollectivePolicy = true;
      return;
  }

  // Resetting validity dates if not a collective policy
  this.form.get('dValidityStart')?.setValue(null);
  this.form.get('dValidityEnd')?.setValue(null);

  this.isCollectivePolicy = false;
}


  updateValidators(status: boolean) {
    if (status){
      this.form.get('logoCustomerPortal')?.setValidators(Validators.required);
      this.form.get('listCoverages')?.setValidators([Validators.required, Validators.minLength(1)]);
    }
    else {
      this.form.get('logoCustomerPortal')?.clearValidators();
      this.form.get('listCoverages')?.clearValidators();
    }
    this.form.updateValueAndValidity();
  }

  async getDataSettingInit() {
    let data = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = data.idBusinessByCountry;
  }

  controller(evt: IconEventClickModel) {
    let data = []
    if (evt.column === 'deleteQuestion')
        {
          data = [...this.dataTableQuestions]
          data.splice(evt.index, 1)
          this.updateQuestions = data
    }
    if (evt.column === 'deleteCoverage')
        {
          data = [...this.dataTableCoverages]
          data.splice(evt.index, 1)
          this.updateCoverages = data
    }
    if (evt.column === 'modifyQuestion')
      {
      this.formQuestion.patchValue({
        index: evt.index,
        answer: evt.value.vAnswer,
          question: evt.value.vQuestion
        })
        this.openModal('question')
    }
    if (evt.column === 'modifyCoverage')
      {
      this.formCoverage.patchValue({
        name: evt.value.vName,
          index: evt.index
        })
        this._fileService.getUploadFileById(evt.value.FkIIdUploadFile).subscribe({
          next: (response => {
            if (response.result.vFileName && response.result.imageBase64) {
              let extension = response.result.vFileName.split('.');
              extension = extension[response.result.vFileName.split('.').length - 1];
              this.formCoverage.get('logo')?.setValue(`data:image/${extension};base64,${response.result.imageBase64}`);
              this.imageSrc = `data:image/${extension};base64,${response.result.imageBase64}`;
            }
          })
        })
        this.openModal('coverage')
    }
    this._cdr.detectChanges();
  }


  saveQuestion(){
    if (this.formQuestion.invalid)
      return this.formQuestion.markAllAsTouched()
    let data = [...this.dataTableQuestions];

    if (this.formQuestion.get('index')?.value !== null)
      data[this.formQuestion.get('index')?.value] = {
        vQuestion: this.formQuestion.get('question')?.value,
        vAnswer: this.formQuestion.get('answer')?.value
    }
    else
      data.push({
        vQuestion: this.formQuestion.get('question')?.value,
        vAnswer: this.formQuestion.get('answer')?.value
      })
    this.updateQuestions = data
    this._cdr.detectChanges();
    this.closeModal()
  }

  async saveImage(): Promise<number> {
    return new Promise((resolve) => {
      let payload: ImageUploadModel = {
        fileName: this.formCoverage.get('logo')?.value.imageName,
        fkIIdBusinessByCountry: this.idBusinessByCountry,
        imageBase64: this.formCoverage.get('logo')?.value.base64.split(',')[1],
        pkIIdInsuranceCompanies: 0,
        type: 0
      };
      this._fileService
        .uploadImage(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              resolve(resp.result)
            }
          }
        });
    });
    
  }

  async saveCoverage(){
    if (this.formCoverage.invalid)
      return this.formCoverage.markAllAsTouched()

    let data = [...this.dataTableCoverages];

    if (this.formCoverage.get('index')?.value !== null)
      data[this.formCoverage.get('index')?.value] = {
        vName: this.formCoverage.get('name')?.value,
        FkIIdUploadFile: await this.saveImage()
    }
    else
      data.push({
        vName: this.formCoverage.get('name')?.value,
        FkIIdUploadFile: await this.saveImage()
      })
    this.updateCoverages = data
    this._cdr.detectChanges();
    this.closeModal()
  }

  openModal(component: string){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    var modal: TemplateRef<any>;
    switch(component) {
      case 'coverage':
        modal = this.createEditCoverage!;
        break;
      case 'question':
        modal = this.createEditQuestion!;
        break;
      default:
        return;
    }
    this._currentModal = this._modalDialog.open(modal, sizeConfiguration);
    this.formQuestionSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formQuestion.reset();
        this.formCoverage.reset()
      })
    })
  }

  closeModal() {
    if (this._currentModal) {
      this._currentModal.close();
      this._currentModal = null;
      this.formQuestionSubs?.unsubscribe();
    }
  }

  getProductParent() {
    this.productParentSubs = this._parameterService.getProductGlobal().pipe(
        catchError((error) => {
        this._messageService.messageWaring(this._translateService.instant('Warning'), error.error.message)
          return of([]);
        })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.productsParent = [];
        } else {
          if (resp.error) {
          this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
            this.productsParent = resp.result;
          }
        }
      });
  }

/**
 * Retrieves policy types for product configuration based on the business-country.
 * 
 * - Calls the `getAllPolicyTypeByBusinessCountry` service method to get policy types.
 * - Handles errors by displaying a warning message if the request fails.
 * - If successful, filters policy types to only include those visible in product configuration.
 * - Finally, calls `validateInitialValuePolicyType` to check and set initial values if needed.
 * 
 * @returns {void}
 */
getPolicyTypesForConfigurationProduct(): void {
  this.productParentSubs = this._policyService
    .getAllPolicyTypeByBusinessCountry()
    .pipe(
      catchError((error) => {
        // Display a warning message if there's an error in the service call
        this._messageService.messageWaring(
          this._translateService.instant('Warning'),
          error.error.message
        );
        return of([]); // Return an empty array in case of error
      })
    )
    .subscribe((resp) => {
      // Check if response is an array, indicating an empty result
      if (Array.isArray(resp)) {
        this.policyTypes = [];
      } else {
        // Handle potential errors in the response
        if (resp.error) {
          this._messageService.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          console.log('resp ==> ', resp.result);
          this.policyTypes = resp.result;

          // Filter to include only policy types visible in product configuration
          this.policyTypes = this.policyTypes.filter(
            (r) => r.bVisibleInProductConfig
          );

          // Validate and set initial policy type value if necessary
          this.validateInitialValuePolicyType();
        }
      }
    });
}


  changeImage(event: any, formControlName: string = 'logo') {
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString;
    this.form.get(formControlName)?.setValue({ base64, imageName:`${imageName}.${extension}` });
    this.imageSrc = `data:image/${extension};base64,${base64}`;
  }

  changeImageCoverage(event: any) {
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString;
    this.formCoverage.get('logo')?.setValue({ base64, imageName:`${imageName}.${extension}` });
  }

  ngOnDestroy(): void {
    this.productSubs?.unsubscribe();
    this.productParentSubs?.unsubscribe();
    this.formSubs?.unsubscribe();
  }
}
