import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-task-tray-settings',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
        {{ 'TaskTraySettings.Title' | translate }}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class TaskTraySettingsComponent implements OnInit {
  constructor(private _translateService: TranslateService) {}

  // inicio: string = this._translateService.instant('Inicio');
  // taskTray: string = this._translateService.instant('Forms');
  inicio: string = 'Inicio';
  taskTray: string = 'Bandeja de tareas';

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.taskTray, link: '/dashboard/task-tray-setting' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant('Inicio');
      this.taskTray = this._translateService.instant('TaskTraySettings.Title');
      this.sections[0].label = this.inicio;
      this.sections[1].label = this.taskTray;
    });
  }
}
