import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ConfigureUploadTemplateComponent } from './configure-upload-template.component';

describe('ConfigureUploadTemplateComponent', () => {
  let component: ConfigureUploadTemplateComponent;
  let fixture: ComponentFixture<ConfigureUploadTemplateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ ConfigureUploadTemplateComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ConfigureUploadTemplateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
