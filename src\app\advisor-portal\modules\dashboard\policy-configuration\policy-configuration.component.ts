import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-policy-configuration',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    BreadcrumbComponent,
    MatTooltipModule,
    MatIconModule,
  ],
  template: `
    <div class="title">
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
        {{"PolicyConfiguration.Title" | translate}}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>
    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class PolicyConfigurationComponent implements OnInit {
  constructor(private _translateService: TranslateService) {}

  start: string = this._translateService.instant("Inicio")  
  policyConfiguration: string = this._translateService.instant('PolicyConfiguration.Title');

  sections: { label: string; link: string }[] = [
    { label: this.start, link: '/dashboard' },
    { label: this.policyConfiguration, link: '/dashboard/policy-configuration' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.start = this._translateService.instant("Inicio")
      this.policyConfiguration = this._translateService.instant('PolicyConfiguration.Title');
      this.sections[0].label = this.start;
      this.sections[1].label = this.policyConfiguration;
    });
  }
}
