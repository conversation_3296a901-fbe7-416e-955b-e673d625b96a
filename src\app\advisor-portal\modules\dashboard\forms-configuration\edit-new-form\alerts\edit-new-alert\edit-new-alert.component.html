<form action="" id="business" [formGroup]="form">
    <!-- slide estado alerta -->
    <div class="row mt-3">        
        <mat-slide-toggle
            class="w-100"
            formControlName="bActive">
            {{ 'Warnings.ActiveWarning' | translate }}
        </mat-slide-toggle> 
    </div>
    <!-- nombre alerta -->
    <div class="row mt-3">        
        <mat-form-field appearance="outline" class="w-100">
            <mat-label>
                {{'Warnings.WarningName'|translate}}
            </mat-label>
            <input matInput formControlName="vName"/>
            <mat-error *ngIf="utilsService.isControlHasError(form, 'vName', 'required')" >
              {{'ThisFieldIsRequired'|translate}}
            </mat-error>
        </mat-form-field>
    </div>
    <!--campo para calcular-->
    <div class="row mt-3">        
        <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
                {{ "Warnings.FieldToCalculateWarning" | translate }}
            </mat-label>
            <mat-select
              formControlName="fkIIdFieldModule" 
              required>
              <mat-option 
                *ngFor="let item of fieldList" 
                [value]="item.pkIIdFieldModule"
              >
                {{ item.vNameField }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="utilsService.isControlHasError(form, 'fkIIdFieldModule', 'required')" >
                {{'ThisFieldIsRequired'|translate}}
            </mat-error>
        </mat-form-field>
    </div>
    <!--calcular desde/hasta-->
    <div class="row mt-3">        
        <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
                {{ "Warnings.CalculateFrom/To" | translate }}
            </mat-label>
            <mat-select
              formControlName="isBegin" 
              required>
              <mat-option 
                *ngFor="let item of calculatedFromList" 
                [value]="item.value"
              >
                {{ item.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="utilsService.isControlHasError(form, 'isBegin', 'required')" >
                {{'ThisFieldIsRequired'|translate}}
            </mat-error>
        </mat-form-field>
        <span class="span">{{'Warnings.CalculateFrom/ToSpan' | translate}}</span>
    </div>
    <!--tiempo calculado en-->
    <div class="row mt-3">        
        <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
                {{ "Warnings.TimeCalculatedIn" | translate }}
            </mat-label>
            <mat-select
              formControlName="isDay" 
              required>
              <mat-option 
                *ngFor="let item of calculatedInList" 
                [value]="item.value"
              >
                {{ item.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="utilsService.isControlHasError(form, 'isDay', 'required')" >
                {{'ThisFieldIsRequired'|translate}}
            </mat-error>
        </mat-form-field>
    </div>
    <!-- cantidad dias/horas segun corresponda -- cuadrar titulo dias/horas -->
    <div class="row mt-3">
        <mat-form-field appearance="outline" class="w-100">
            <mat-label>
                {{iValueTittleText}}
            </mat-label>
            <input matInput type="number" formControlName="iValue"/>
            <mat-error *ngIf="utilsService.isControlHasError(form, 'iValue', 'required')" >
              {{'ThisFieldIsRequired'|translate}}
            </mat-error>
        </mat-form-field>
    </div>    


</form>

