<!-- <PERSON><PERSON><PERSON><PERSON> de la tabla -->
<div class="row mt-3 mb-2">
    <h4 class="bold">{{'Policy.Risks' | translate}}</h4>
</div>

<div class="cont-filter">
    <div class="w-75 mt-2">
        <!-- input de busqueda en la tabla -->
        <mat-form-field class="w-100">
            <mat-label>
                {{ "Search" | translate }}
            </mat-label>
            <input (keyup.enter)="search(keyword)" [(ngModel)]="keyword" matInput type="text" class="form-control"
                placeholder="{{ 'Search' | translate }}" />
            <mat-icon class="hand click" (click)="search(keyword)" matSuffix>search</mat-icon>
        </mat-form-field>
    </div>
    <div class="cont-btns-filter">
        <div class="mx-1">
            <!-- Filtrar -->
            <button class="" type="button" color="primary" (click)="openFilterDialog()" mat-raised-button>
                {{ "Filter" | translate }}
                <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
            </button>
        </div>
        <!-- Ordenar -->
        <div class="mx-1">
            <button (click)="onSortClick()" class="" type="button" mat-raised-button color="primary">
                {{ "Order" | translate }}
                <mat-icon iconPositionEnd fontIcon="sort_by_alpha"></mat-icon>
            </button>
        </div>
        <!-- Descargar -->
        <div class="mx-1">
            <button (click)="generateRiskReport(idPolicy, idHistoryPolicy)" class="" type="button" mat-raised-button color="primary">
                {{ "Download" | translate }}
                <mat-icon iconPositionEnd fontIcon="download"></mat-icon>
            </button>
        </div>
    </div>
</div>

<!-- datatable Pólizas colectivas e individuales colectivas-->
<div class="row mt-2">
    <app-table [displayedColumns]="estructTable" [data]="dataTableRisk" [IsStatic]="false" [pageIndex]="pageIndex"
        [pageSize]="pageSize" [amountRows]="amountRows" (pageChanged)="onPageChange($event)"
        (iconClick)="controller($event)"></app-table>
</div>


<!-- modal filtros -->
<ng-template #filtersModal>
    <app-modal2 [titleModal]="'Filter' | translate" [showCancelButtonBelow]="false">
        <ng-container body>

            <div class="row">
                <!-- estado -->
                <div class="col-md-12 mb-2">
                    <mat-form-field class="w-100" appearance="fill">
                        <mat-label>
                            {{"Status" | translate}}
                        </mat-label>
                        <mat-select [(ngModel)]="policyStatusValue">
                            <mat-option *ngFor="let status of policyStatus" [value]="status.id">
                                {{ status.name }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
        </ng-container>

        <!-- Botón Borrar filtros -->
        <ng-container customButtonCenter>
            <button (click)="cleanFilterForm()" class="btn-custom w-100" type="button" mat-raised-button>
                <strong>{{ "PolicyConfiguration.Filter.DeleteFilters" | translate }}</strong>
            </button>
        </ng-container>
        <!-- Botón Aplicar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary" (click)="applyFilters()">
                {{ "Apply" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>