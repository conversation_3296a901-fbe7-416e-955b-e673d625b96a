import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { QuestionService } from 'src/app/shared/services/question/question.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-questions',
  standalone: true,
  imports: [
    MatInputModule,
    Modal2Component,
    PreventionSqlInjectorDirective,
    TranslateModule,
    ReactiveFormsModule,
    CommonModule,
    TableComponent,
    MatButtonModule
  ],
  templateUrl: './questions.component.html',
  styleUrls: ['./questions.component.scss']
})
export class QuestionsComponent implements OnInit{
  @Input() idBusinessByCountry: number = 0;
  @ViewChild('createEditQuestion') createEditQuestion?: TemplateRef<any>;
  _currentModal: MatDialogRef<any> | null = null;

  formSubs?: Subscription;
  
  dataTableQuestions: any[] = [];
  maxItems: number = 6;
  pkQuestionToModify: number = 0;
  estructTableQuestions: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Question'),
      columnValue: 'vQuestion',
    },
    {
      columnLabel: this._translateService.instant('Product.Answer'),
      columnValue: 'vAnswer',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyQuestion',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteQuestion',
      columnIcon: 'delete',
    },
  ];

  constructor(
    public utilsSvc: UtilsService,
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    private _modalDialog: MatDialog,
    private _cdr: ChangeDetectorRef,
    private _questionService: QuestionService,
    private _messageService: MessageService,
  ){

  }
  ngOnInit(): void {
    this._loadQuestions()
  }

  formQuestion: FormGroup = this._fb.group({
    vQuestion: [null, Validators.required],
    vAnswer: [null, Validators.required],
  })

  private _loadQuestions(){
    this._questionService.getQuestions(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error)
          {
            this.dataTableQuestions = [...resp.result];
            this.pkQuestionToModify = 0;
            this._cdr.detectChanges()
          }
      })
    })
  }

  openModal(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this._currentModal = this._modalDialog.open(this.createEditQuestion!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formQuestion.reset();
      })
    })
  }

  saveQuestion(){
    if (this.formQuestion.invalid)
      return this.formQuestion.markAllAsTouched()

    if (this.pkQuestionToModify === 0)
      this._questionService.createQuestion({
        ...this.formQuestion.value,
        FkIIdBusinessByCountry: this.idBusinessByCountry
      }).subscribe({
          next: (response) => {
            if (!response.error) {
              this._loadQuestions()
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.QuestionCreatedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          }
      })
    else
      this._questionService.modifyQuestion(
        this.pkQuestionToModify, 
        {...this.formQuestion.value}).subscribe({
          next: (response) => {
            if (!response.error) {
              this._loadQuestions()
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.QuestionModifiedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          }
      })
    this.closeModal()
  }

  closeModal() {
    if (this._currentModal) {
      this._currentModal.close();
      this._currentModal = null;
      this.formSubs?.unsubscribe();
    }
  }


  controller(evt: IconEventClickModel) {
    if (evt.column === 'deleteQuestion')
        {
          this._questionService.deleteQuestion(evt.value.pkIIdQuestion).subscribe({
            next: (response) => {
              if (!response.error) {
                this._loadQuestions()
                this._messageService.messageSuccess(
                  this._translateService.instant('DataSavedSuccessfully'),
                  this._translateService.instant('SectionClientPortal.QuestionDeletedSucessfully')
                );
              }
            },
            error: (error) => {
              this._messageService.messageInfo(
                this._translateService.instant('ThereWasAError'),
                error
              );
            }
          })
        }
    if (evt.column === 'modifyQuestion')
      {
        this.pkQuestionToModify = evt.value.pkIIdQuestion
        this.formQuestion.patchValue({
          vQuestion: evt.value.vQuestion,
          vAnswer: evt.value.vAnswer
        })
        this.openModal()
      }
    
  }

}
