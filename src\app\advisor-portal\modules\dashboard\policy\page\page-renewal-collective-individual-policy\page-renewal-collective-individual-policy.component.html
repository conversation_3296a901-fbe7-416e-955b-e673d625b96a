<form [formGroup]="form">
  <div class="row">
      <!-- ID WTW -->
      <div class="col-md-6 col-sm-12 mb-2">
          <mat-form-field appearance="outline" class="w-100">
              <mat-label>
                  {{ "PolicyConfiguration.GeneralInformation.IdWTW" | translate }}
              </mat-label>
              <input matInput formControlName="IdPolicy" PreventionSqlInjector type="number" />
          </mat-form-field>
      </div>
      <!-- Producto -->
      <div class="col-md-6 col-sm-12 mb-2">
          <mat-form-field appearance="outline" class="w-100">
              <mat-label>
                  {{ "PolicyConfiguration.GeneralInformation.Product" | translate }}
              </mat-label>
              <input matInput formControlName="ProductName" PreventionSqlInjector type="text" />
          </mat-form-field>
      </div>
  </div>

  <div class="row mb-5">
      <div class="col-md-12">
          <mat-tab-group #tabGroup (selectedTabChange)="onTabChange($event)">
              <mat-tab label="{{ 'Policy.PolicyData' | translate }}">
                  <div class="row mt-5 mb-5">
                      <div class="col-md-12">
                          <h4 class="bold">{{"Policy.PolicyData" | translate}}</h4>
                      </div>
                  </div>
                  <!-- Prorroga  póliza -->
                  <div class="row mt-4 mb-2">
                      <div class="col-md-6 col-sm-12">
                          <mat-slide-toggle class="mb-3" formControlName="Extension">
                              {{"Policy.Extension" | translate}}
                          </mat-slide-toggle>
                      </div>
                  </div>
                  <div class="row">
                      <!-- Número de póliza -->
                      <ng-container *ngIf="idPolicyType === 2">
                          <div class="col-md col-sm-12 mb-2">
                              <mat-form-field appearance="outline" class="w-100">
                                  <mat-label>
                                      {{ "PolicyConfiguration.GeneralInformation.PolicyNumber" | translate }}
                                  </mat-label>
                                  <input matInput formControlName="PolicyNumber" PreventionSqlInjector />
                                  <mat-error *ngIf="_utilService.isControlHasError(form, 'PolicyNumber', 'required')">
                                      {{ "ThisFieldIsRequired" | translate }}
                                  </mat-error>
                              </mat-form-field>
                          </div>
                      </ng-container>
                      <!-- Nombre póliza -->
                      <div class="col-md col-sm-12 mb-2">
                          <mat-form-field appearance="outline" class="w-100">
                              <mat-label>
                                  {{ "PolicyConfiguration.GeneralInformation.PolicyName" | translate }}
                              </mat-label>
                              <input matInput formControlName="PolicyName" PreventionSqlInjector />
                              <mat-error *ngIf="_utilService.isControlHasError(form, 'PolicyName', 'required')">
                                  {{ "ThisFieldIsRequired" | translate }}
                              </mat-error>
                          </mat-form-field>
                      </div>
                  </div>
                  <div class="row">
                      <!-- Aseguradora -->
                      <div class="col-md-6 col-sm-12 mb-2">
                          <mat-form-field class="w-100" appearance="fill">
                              <mat-label>
                                  {{"PolicyConfiguration.GeneralInformation.Insurance" | translate}}
                              </mat-label>
                              <input matInput formControlName="InsuranceName" PreventionSqlInjector type="text" />
                          </mat-form-field>
                      </div>
                      <!-- Inicio de vigencia -->
                      <div class="col-md-6 col-sm-12 mb-2">
                          <mat-form-field appearance="outline" class="w-100">
                              <mat-label>
                                  {{"PolicyConfiguration.GeneralInformation.StartValidity" | translate}}
                              </mat-label>
                              <input matInput [matDatepicker]="StartValidity" formControlName="StartValidity" />
                              <mat-datepicker-toggle matIconSuffix [for]="StartValidity"></mat-datepicker-toggle>
                              <mat-datepicker #StartValidity></mat-datepicker>
                              <mat-error *ngIf="_utilService.isControlHasError(form, 'StartValidity', 'required')">
                                  {{ "ThisFieldIsRequired" | translate }}
                              </mat-error>
                          </mat-form-field>
                      </div>
                  </div>
                  <div class="row">
                      <!-- Fin de vigencia -->
                      <div class="col-md-6 col-sm-12 mb-2">
                          <mat-form-field appearance="outline" class="w-100">
                              <mat-label>
                                  {{"PolicyConfiguration.GeneralInformation.EndValidity" | translate}}
                              </mat-label>
                              <input matInput [matDatepicker]="EndValidity" formControlName="EndValidity" [min]="this.form.get('StartValidity')?.value"/>
                              <mat-datepicker-toggle matIconSuffix [for]="EndValidity"></mat-datepicker-toggle>
                              <mat-datepicker #EndValidity></mat-datepicker>
                              <mat-error *ngIf="_utilService.isControlHasError(form, 'EndValidity', 'required')">
                                  {{ "ThisFieldIsRequired" | translate }}
                              </mat-error>
                          </mat-form-field>
                      </div>
                      <!-- Documentos soporte -->
                      <div class="col-md-6 col-sm-12 mb-1">
                          <mat-form-field class="w-100 custom-input">
                              <mat-label>
                                  <mat-icon matTooltip="file" class="click" mat-icon-button matSuffix>attach_file</mat-icon>
                                  {{"PolicyConfiguration.GeneralInformation.SupportingDocuments" | translate}}
                              </mat-label>
                              <input #fileInput (onChangeValidated)="onFileChange($event)" [multiple]="true" type="file"
                                  style="display: none;" ValidationInputFile  [allowedExtensions]="fileTypes" [maxFileSizeMB]="maxFileSizeMB"/>
                              <div class="cont-items-file mt-2">
                                  <div class="cont-btn-name-file">
                                      <input formControlName="Files" type="text" readonly matInput
                                          [value]="selectedFileName" class="truncate-input">
                                  </div>
                                  <div>
                                      <button *ngIf="!showBtnDownload && !showBtnDelete" (click)="fileInput.click()"
                                          class="downloand-color click prevent-disabled-icon">
                                          <mat-icon class="material-symbols-outlined" mat-icon-button
                                              matSuffix>upload</mat-icon>
                                      </button>
                                      <button *ngIf="showBtnDownload" (click)="downloadDocument()"
                                          class="downloand-color click prevent-disabled-icon">
                                          <mat-icon class="material-symbols-outlined" mat-icon-button
                                              matSuffix>download</mat-icon>
                                      </button>
                                      <button *ngIf="showBtnDelete" (click)="deleteDocument()"
                                          class="downloand-color click prevent-disabled-icon mx-2">
                                          <mat-icon class="material-symbols-outlined" mat-icon-button
                                              matSuffix>delete</mat-icon>
                                      </button>
                                  </div>
                              </div>
                          </mat-form-field>
                          <p class="description">
                              {{"PolicyConfiguration.GeneralInformation.Files"| translate }} PDF, XLS, SVG, PNG, JPEG,
                              {{"PolicyConfiguration.GeneralInformation.MaximumSize"| translate }} 20 MB
                          </p>
                      </div>
                  </div>

              </mat-tab>

              <mat-tab label="{{ 'Policy.Risk' | translate }}">
                  <!-- Tabla de riesgos activos -->
                  <app-active-risk-table [title]="titleRiskTable"
                      [estructTable]="estructTableActiveRisk" [idPolicy]="idWtw" [idPolicyType]="idPolicyType"></app-active-risk-table>
              </mat-tab>
              <mat-tab label="{{ 'PolicyConfiguration.Coverage.Title' | translate }}">
                  <!-- Coberturas -->
                  <app-coverages [idPolicy]="idWtw" [isConfig]="false" (datatableChanged)="getCoverages($event)"></app-coverages>
              </mat-tab>
          </mat-tab-group>
      </div>
  </div>
  <div class="d-flex justify-content-center mt-5">
      <a class="label-button mx-3" mat-button (click)="cancel()"><span>{{"Cancel" | translate}}</span>
          <mat-icon fontIcon="arrow_back"></mat-icon>
      </a>
      <!-- Botón Continuar -->
      <button *ngIf="!isLastTab" type="button" mat-raised-button color="primary" (click)="goToNextTab()">
          <mat-icon iconPositionEnd fontIcon="arrow_forward"></mat-icon>
          {{ "Continue" | translate }}
      </button>

      <!-- Botón Guardar cambios -->
      <button *ngIf="isLastTab" type="button" mat-raised-button color="primary" (click)="saveChange()">
          <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
          {{ "SaveChanges" | translate }}
      </button>
  </div>

</form>
