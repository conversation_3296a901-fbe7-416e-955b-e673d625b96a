import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription, catchError, debounceTime, of } from 'rxjs';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { FormCreateEditModel } from 'src/app/shared/models/configuration-form/form-module';
import { AllMenuModel } from 'src/app/shared/models/menu/all-menu.model';
import { StageModel } from 'src/app/shared/models/module';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { FieldsFormComponent } from './fields-form/fields-form.component';
import { PopUpFormComponent } from './pop-up-form/pop-up-form.component';
import { ProgressBarFormComponent } from './progress-bar-form/progress-bar-form.component';
import { SectionsFormComponent } from './sections-form/sections-form.component';
import { TabsFormComponent } from './tabs-form/tabs-form.component';
import { VisualizerFormComponent } from './visualizer-form/visualizer-form.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { DataFormModel } from 'src/app/shared/models/module/data-form.model';
import { DataService } from 'src/app/shared/services/data/data.service';

@Component({
  selector: 'app-form',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    FieldsFormComponent,
    PopUpFormComponent,
    ProgressBarFormComponent,
    SectionsFormComponent,
    TabsFormComponent,
    Modal2Component,
    ChooseCountryAndCompanyComponent,
    VisualizerFormComponent,
    MatTooltipModule
  ],
  templateUrl: './form.component.html',
  styleUrls: ['./form.component.scss'],
})
export class FormComponent implements OnInit, OnDestroy {
  form: FormGroup = new FormGroup({});
  formFilter: FormGroup = new FormGroup({});
  private _formConfigurationData?: Subscription;
  statesId: number[] = [];
  idForm: number = 0;
  moduleList: AllMenuModel[] = [];
  subModuleList: AllMenuModel[] = [];
  productList: any[] = [];
  stageList: StageModel[] = [];
  statusList: any[] = [];
  statusListCopyParameters: any[] = [];
  @ViewChild('copyExistingParametersModal')
  copyExistingParametersModal?: TemplateRef<any>;
  titelModal: string = 'Replicar configuración';
  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessCountry: number = 0;
  idProductModule: number = 0;
  isButtonCopyParameter: boolean = false;
  formExist: boolean = false;
  @Output() sendIdForm = new EventEmitter<number>();
  @Output() reloadTable = new EventEmitter<number>();
  @Output() sendDataForm = new EventEmitter<DataFormModel>();

  field: any[] = [];
  idStage: number = 0;
  @ViewChild('previewForm')
  previewForm?: TemplateRef<any>;
  @ViewChild(ProgressBarFormComponent) ProgressBarComponent!: ProgressBarFormComponent;
  @ViewChild(TabsFormComponent) TabComponent!: TabsFormComponent;
  @ViewChild(SectionsFormComponent) SectionComponent!: SectionsFormComponent;
  @ViewChild(FieldsFormComponent) FieldComponent!: FieldsFormComponent;
  @ViewChild(PopUpFormComponent) PopUpComponent!: PopUpFormComponent;

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    private _modulesSettingService: ModulesSettingService,
    private _router: Router,
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public modalDialog: MatDialog,
    private _roleService: RoleService,
    private _settingService: SettingService,
    private _customRouter: CustomRouterService,
    private dataService: DataService

  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getFormConfigurationData();
  }

  getFormConfigurationData() {
    this._formConfigurationData =
      this._modulesSettingService.currentFilterFormModulesSetting.subscribe({
        next: (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idStage = response.idStage;
            this.statusList = response.states;
            this.statesId = response.states;

            this.dataService.setStates(response.states);

            let dataForm: DataFormModel = {
              idProductModule: response.idProductModule,
              idStage: this.idStage
            }
            this.sendDataForm.emit(dataForm);

            if (this.statesId.length > 0) {
              if (this.getStageIds(this.statesId).length > 0) {
                this.getFormModuleByIdState(this.getStageIds(this.statesId));
                this.formExist = true;
                this.form
                  .get('stateAsoccied')
                  ?.setValue(this.getStageIds(this.statesId));
              }
              else {
                this.formExist = false;
              }
            }
          } else {
            this._customRouter.navigate(['/dashboard/forms']);
          }
        },
      });
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdFormModule: [0],
      vName: ['', [Validators.required]],
      vNameDb: { value: '', disabled: true },
      iAmountColumns: [
        '',
        [Validators.required, Validators.pattern('^[1-4]$')],
      ],
      bActive: [true],
      stateAsoccied: [[], [Validators.required]],
    });
    this.form
      .get('vName')
      ?.valueChanges.pipe(debounceTime(600))
      .subscribe({
        next: (data) => {
          if (data) {
            this.form.get('vNameDb')?.setValue(this.generateFormNameDb(data));
          } else {
            this.form.get('vNameDb')?.setValue('');
          }
        },
      });

    this.form.get('stateAsoccied')?.valueChanges.subscribe((data) => {
      if (data != undefined) {
        if (data.length > 0 && this.formExist == true) {
          this.isButtonCopyParameter = true;
        } else {
          this.isButtonCopyParameter = false;
        }
      } else {
        this.isButtonCopyParameter = false;
      }
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  getFormModuleByIdState(idState: number[]) {
    this._moduleService
      .getFormModuleByIdState(idState)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if (response.result) {
              this.idForm = response.result.pkIIdFormModule;
              this.sendIdForm.emit(this.idForm);
            }
            if (this.idForm > 0) {
              this.getFormModuleById(this.idForm);
            }
          }
        }
      });
  }

  getFormModuleById(idFormModule: number) {
    this._moduleService
      .getFormModuleById(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.form.patchValue(response.result);
          }
        }
      });
  }

  getStageIds(data: any[]): number[] {
    const filteredStageIds = data
      .filter((obj) => obj.fkIdFormModule > 0)
      .map((obj) => obj.pkIIdStageByState);
    return filteredStageIds;
  }

  openModalcopyExistingParameters() {
    this.getSettingCountryAndCompanySubscription();
    this.initFormFilter();
    this.titelModal = this._translateService.instant(
      'FormConfiguration.ReplicateConfigurationTitle'
    );
    this.modalDialog.open(this.copyExistingParametersModal!, {
      width: '50vw',
      height: 'auto',
    });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessCountry =
                response.enterprise.pkIIdBusinessByCountry;
              this.moduleList = [];
              this.getMenuList();
            }
          }
        }
      );
  }

  initFormFilter() {
    this.formFilter = this._fb.group({
      idModule: [0, [Validators.required]],
      idSubModule: [0, [Validators.required]],
      idProductModule: [0, [Validators.required]],
      idStage: [0, [Validators.required]],
      idState: [0, [Validators.required]],
    });

    this.formFilter.get('idModule')?.valueChanges.subscribe((data) => {
      this.formFilter.get('idSubModule')?.setValue(0);
      this.formFilter.get('idProductModule')?.setValue(0);
      this.subModuleList = [];
      this.productList = [];
      this.stageList = [];
      this.formFilter.get('idSubModule')?.disable();
      this.formFilter.get('idProductModule')?.disable();
      this.formFilter.get('idStage')?.disable();
      this.formFilter.get('idState')?.disable();
      this.formFilter.get('idSubModule')?.clearValidators();
      this.formFilter.get('idProductModule')?.clearValidators();
      this.formFilter.get('idStage')?.clearValidators();
      this.formFilter.get('idState')?.clearValidators();
      this.formFilter.get('idSubModule')?.updateValueAndValidity();
      this.formFilter.get('idProductModule')?.updateValueAndValidity();
      this.formFilter.get('idStage')?.updateValueAndValidity();
      this.formFilter.get('idState')?.clearValidators();
      this.getSubmoduleByIdMenu(data.pkIIdMenu);
    });

    this.formFilter.get('idSubModule')?.valueChanges.subscribe((data) => {
      this.formFilter.get('idProductModule')?.setValue(0);
      this.formFilter.get('idProductModule')?.disable();
      this.formFilter.get('idProductModule')?.clearValidators();
      this.formFilter.get('idProductModule')?.updateValueAndValidity();
      this.productList = [];
      this.stageList = [];
      this.getProductByIdMenu(data.pkIIdMenu);
    });

    this.formFilter.get('idProductModule')?.valueChanges.subscribe((data) => {
      this.formFilter.get('idStage')?.setValue(0);
      this.formFilter.get('idStage')?.disable();
      this.formFilter.get('idStage')?.clearValidators();
      this.formFilter.get('idStage')?.updateValueAndValidity();
      this.stageList = [];
      this.getStageByIdProductModule(data.idProductModule);
    });

    this.formFilter.get('idStage')?.valueChanges.subscribe((data) => {
      this.formFilter.get('idState')?.setValue(0);
      this.formFilter.get('idState')?.disable();
      this.formFilter.get('idState')?.clearValidators();
      this.formFilter.get('idState')?.updateValueAndValidity();
      this.statusListCopyParameters = [];
      this.getStagByStateAssociateToFormByIdStage(data.pkIIdStage);
    });
  }

  getMenuList() {
    this._modulesSettingService
      .getAllParentMenuFromModules(this.idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.moduleList = resp.result;
          }
        }
      });
  }

  getSubmoduleByIdMenu(idModule: number) {
    if (idModule > 0) {
      this._modulesSettingService
        .getAllChildrenMenuFromModules(idModule)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            this.getProductByIdMenu(idModule);
          } else {
            if (resp.error) {
            } else {
              if (resp.result != undefined) {
                if (resp.result.length > 0) {
                  this.subModuleList = resp.result;
                  this.formFilter.get('idSubModule')?.enable();
                  this.formFilter.get('idSubModule')?.updateValueAndValidity();
                } else this.getProductByIdMenu(idModule);
              }
            }
          }
        });
    }
  }

  //obtiene la lista de productos asignados a un menu y llena el dropdwon de prodictos
  getProductByIdMenu(idMenu: number) {
    if (idMenu > 0) {
      this._roleService
        .getProductByIdMenu(idMenu)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            } else {
              this.productList = resp.result;
              var dataIdProductModule = 0;
              if (this.productList != undefined) {
                this.productList.filter((element) => {
                  this.idProductModule = element.idProductModule;
                  if (element.idProduct === null || element.idProduct === '')
                    dataIdProductModule = 1;
                  else dataIdProductModule = 0;
                });

                if (dataIdProductModule === 1) {
                  this.getStageByIdProductModule(this.idProductModule);
                } else {
                  this.formFilter.get('idProductModule')?.enable();
                  this.formFilter
                    .get('idProductModule')
                    ?.updateValueAndValidity();
                }
              }
            }
          }
        });
    }
  }

  //obtiene la lista de etapas asignados a un prodcut y llena el dropdwon de etapas
  getStageByIdProductModule(idProductModule: number) {
    if (idProductModule > 0) {
      this._moduleService
        .getStageByIdProductModule(idProductModule)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            } else {
              if (resp.result != undefined) {
                if (resp.result.length > 0) {
                  this.stageList = resp.result;
                  this.formFilter.get('idStage')?.enable();
                  this.formFilter.get('idStage')?.updateValueAndValidity();
                }
              }
            }
          }
        });
    }
  }

  //obtiene la lista de estados asignados a una etapa y llena el dropdwon de estados
  getStagByStateAssociateToFormByIdStage(idStage: number) {
    if (idStage > 0) {
      this._moduleService
        .getStagByStateAssociateToFormByIdStage(idStage)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            } else {
              if (resp.result != undefined) {
                if (resp.result.length > 0) {
                  this.statusListCopyParameters = resp.result;
                  this.formFilter.get('idState')?.enable();
                  this.formFilter.get('idState')?.updateValueAndValidity();
                }
              }
            }
          }
        });
    }
  }

  complete() {
    let payload: FormCreateEditModel = {
      bActive: this.form.get('bActive')?.value,
      iAmountColumns: Number(this.form.get('iAmountColumns')?.value),
      listState: this.form.get('stateAsoccied')?.value,
      pkIIdFormModule: this.idForm,
      vName: this.form.get('vName')?.value,
      vNameDb: this.form.get('vNameDb')?.value,
    };

    if (this.idForm > 0) {
      this.editFormModule(payload);
    } else {
      this.registerFormModule(payload);
    }
  }

  registerFormModule(payload: FormCreateEditModel) {
    this._moduleService
      .registerFormModule(payload)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant('Created')
            );
            this.idForm = resp.result;
            this.formExist = true;
            this.isButtonCopyParameter = true;
            this.sendIdForm.emit(this.idForm);
          }
        }
      });
  }

  editFormModule(payload: FormCreateEditModel) {
    this._moduleService
      .updateFormModule(payload)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant('Modified')
            );
            this.idForm = resp.result;
            this.sendIdForm.emit(this.idForm);
          }
        }
      });
  }

  deleteForm() {
    if (this.idForm > 0) {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('FormConfiguration.DeleteFormTitle'),
          this._translateService.instant(
            'FormConfiguration.DeleteFormSubtitle'
          ),
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            this._moduleService
              .deleteFormModule(this.idForm)
              .pipe(
                catchError((error) => {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('Warning'),
                    error.error.message
                  );
                  return of([]);
                })
              )
              .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                  console.log('El tipo de datos devueltos es un array vacío.');
                } else {
                  if (resp.error) {
                    this._msgSvc.messageError(
                      this._translateService.instant('Warning') + resp.message
                    );
                  } else {
                    this._msgSvc.messageSuccess('', resp.message);
                    this.form.reset();
                    this.form.get('bActive')?.setValue(true);
                    this.form.get('stateAsoccied')?.setValue([]);
                    this.idForm = 0;
                    this.sendIdForm.emit(this.idForm);
                  }
                }
              });
          }
        });
    }
  }

  cloneFormModuleByIdStateModule() {
    this._moduleService
      .cloneFormModuleByIdStateModule(
        this.form.get('stateAsoccied')?.value,
        this.formFilter.get('idState')?.value
      )
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant('FormConfiguration.Replicated')
            );
            this.modalDialog.closeAll();          
            this.ProgressBarComponent.getProgressBarModuleByIdFormModule(this.idForm);
            this.TabComponent.getTabModuleListByFormId(this.idForm);
            this.SectionComponent.getSectionModuleByFormId(this.idForm);
            this.FieldComponent.getFieldsByForm(this.idForm);
            this.PopUpComponent.getPopUpList(this.idForm);
            this.reloadTable.emit(this.idForm);
          }
        }
      });
  }

  preview() {
    this.getCompleteFormByStage();
    this.titelModal = this._translateService.instant('Product.Preview');
    this.modalDialog.open(this.previewForm!);
  }

  cancel() {
    this._msgSvc
    .messageConfirmationAndNegation(
      this._translateService.instant("Out?"),
      this._translateService.instant("UnsavedSettingsWillBeLost"),
      "info",
      this._translateService.instant("Continue"),
      this._translateService.instant("Out")
    )
    .then((response) => {
      if (response) {
        this._customRouter.navigate(['/dashboard/forms']);
      }
    });
  }

  generateFormNameDb(formName: string): string {
    // Limpiar el nombre: eliminar tildes, espacios, etc.
    let cleanedName = formName
      ?.normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
    cleanedName = cleanedName?.replace(/\s+/g, '');
    // Agregar 'DB' al final del nombre
    cleanedName += 'DB';
    return cleanedName;
  }

  ngOnDestroy(): void {
    this._formConfigurationData?.unsubscribe();
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  getCompleteFormByStage() {
    this._moduleService
      .getCompleteFormByStage(this.statusList[0].pkIIdStageByState, false)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }

          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this.field = resp.result;
          }
        }
      });
  }
}
