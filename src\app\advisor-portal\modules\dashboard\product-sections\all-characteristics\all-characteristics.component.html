<div class="flex">
  <div class="box">
    <span class="title">
      {{'ProductSection.Business'| translate}}
    </span>
    <h4 class="subtitle">{{enterprise}}</h4>
  </div>
  <div class="box title-customer">
    <span class="title">
      {{'ProductSection.Country'| translate}}
    </span>
    <h4 class="subtitle">{{country}}</h4>
  </div>
  <div class="box title-customer">
    <span class="title">
      {{'ProductSection.Product'| translate}}
    </span>
    <h4 class="subtitle">{{product}}</h4>
  </div>
</div>
<app-table
  [displayedColumns]="estructTable"
  [data]="dataTableSection"
  (iconClick)="controller($event)"
></app-table>
<button class="mt-2" type="button" (click)="goToCreateCharacteristics()" mat-raised-button color="primary">
  {{'Add'| translate}} 
  <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
</button>


<div class="d-flex justify-content-center mt-3">
  <button mat-raised-button (click)="goBack()">
    <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
    {{'Back'| translate}}
  </button>
</div>
