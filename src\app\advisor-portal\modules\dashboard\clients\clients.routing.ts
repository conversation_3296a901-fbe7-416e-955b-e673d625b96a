import { Routes } from '@angular/router';
import { ClientsComponent } from './clients.component';

/**
 * Routes configuration for the clients module.
 * 
 * This configuration sets up the routes and their associated components for the
 * Clients module. It defines the child routes for various client-related views,
 * including viewing all clients, client details, creating a new client, and 
 * viewing details for quotes and products. Each route uses lazy loading to
 * load components only when needed, improving application performance.
 * 
 * @constant {Routes} - Array of route definitions for the Clients module.
 */
export default [
  {
    path: '',
    component: ClientsComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-clients/all-clients.component').then(
            (c) => c.AllClientsComponent
          ),
      },
      {
        path: 'detail/:id',
        loadComponent: () =>
          import('./get-client/get-client.component').then(
            (c) => c.GetClientComponent
          ),
      },
      {
        path: 'new',
        loadComponent: () =>
          import('./new-client/new-client.component').then(
            (c) => c.NewClientComponent
          ),
      },
      {
        path: 'detail/:id/:idQuote',
        loadComponent: () =>
          import('./see-details-quote/see-details-quote.component').then(
            (c) => c.SeeDetailsQuoteComponent
          ),
      },
      {
        path: 'detail/:idClient/see-detail-product/:idProduct',
        loadComponent: () =>
          import('./see-details-product/see-details-product.component').then(
            (c) => c.SeeDetailsProductComponent
          ),
      },
      {
        path: 'detail/:idClient/see-detail-quote/:idProduct/:comesFromClient',
        loadComponent: () =>
          import('./see-details-quote/see-details-quote.component').then(
            (c) => c.SeeDetailsQuoteComponent
          ),
      },
    ],
  },
] as Routes;
