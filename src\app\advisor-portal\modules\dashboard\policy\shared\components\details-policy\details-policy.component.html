<div class="cont-detail" *ngIf="detailsPolicyData.length > 0">
  <div class="cont-header-detail">
    <div class="cont-data">
      <ng-container *ngIf="idPolicyType === 1 && !isRisk">
        <span>{{
          "PolicyConfiguration.GeneralInformation.PolicyNumber" | translate
        }}</span>
        <span class="number-customer">{{ policyNumber }}</span>
        <span>{{ "Id WTW" | translate }}</span>
        <span class="number-customer">{{ idWtw }}</span>
      </ng-container>
      <ng-container *ngIf="idPolicyType === 2 && !isRisk">
        <span>{{ "Id WTW" | translate }}</span>
        <span class="number-customer">{{ idWtw }}</span>
        <span>{{
          "PolicyConfiguration.GeneralInformation.PolicyNumber" | translate
        }}</span>
        <span class="number-customer">{{ policyNumber }}</span>
      </ng-container>
      <ng-container *ngIf="idPolicyType === 3 && !isRisk">
        <span>{{ "Id WTW" | translate }}</span>
        <span class="number-customer">{{ idWtw }}</span>
      </ng-container>
      <ng-container *ngIf="isRisk && idPolicyType === 2">
        <span>{{ "Id WTW" | translate }}</span>
        <span class="number-customer">{{ idWtw }}</span>
        <ng-container *ngIf="fieldKeyValue">
          <span>{{ fieldKeyName | translate }}</span>
          <span class="number-customer">{{ fieldKeyValue }}</span>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="isRisk && idPolicyType === 3">
        <span>{{
          "PolicyConfiguration.GeneralInformation.PolicyNumber" | translate
        }}</span>
        <span class="number-customer">{{ policyNumber }}</span>
      </ng-container>
    </div>
    <div class="cont-data">
      <span>{{ "Status" | translate }}</span>
      <span
        class="status-container"
        [ngClass]="{
          active: policyStatus === 'Activa',
          activo: policyStatus === 'Activo',
          expired: policyStatus === 'Vencida',
          vencido: policyStatus === 'Vencido',
          cancelled: policyStatus === 'Cancelada',
          excluded: policyStatus === 'Excluido'
        }"
      >
        {{ changeStatusPolicy(policyStatus) | translate }}

        <!-- Tooltip rojo solo si es Cancelada o Excluido -->
        <span
          class="fixed-tooltip"
          *ngIf="policyStatus === 'Cancelada' || policyStatus === 'Excluido'"
        >
          {{ motive }}
          <span class="tooltip-arrow"></span>
        </span>
      </span>
    </div>
  </div>
  <div class="cont-policy-data">
    <!-- Iterar sobre cada sección (policy, takers, insurances, others) en el array 'detailsPolicyData' -->
    <div
      *ngFor="let section of detailsPolicyData; let i = index; let last = last"
      class="cont-policy-data"
      [ngClass]="{ 'border-custom': !last }"
    >
      <div *ngFor="let subSection of section[getSectionKey(section)]">
        <div class="row mb-2">
          <!-- Mostrar el nombre de la sección -->
          <h4 class="label">{{ subSection?.nameSection }}</h4>
        </div>

        <!-- Contenedor para los campos dentro de la sección actual -->
        <div class="row mb-4">
          <div *ngFor="let field of subSection?.fields" class="col-md-4 mb-3">
            <!-- Mostrar el nombre del campo -->
            <p class="label">
              <ng-container [ngSwitch]="field.name">
                <span *ngSwitchCase="'yearRenewal'">
                  {{ "Policy.YearRenewal" | translate }}
                </span>
                <span *ngSwitchCase="'dNoveltyDate'">
                  <span *ngIf="idPolicyType == 1; else datee">
                    {{ "Fecha de cancelación" | translate }}
                  </span>
                  <ng-template #datee>
                    {{ "Fecha de exclusión" | translate }}
                  </ng-template>
                </span>
                <span *ngSwitchDefault>
                  {{ field.name | translate }}
                </span>
              </ng-container>
            </p>
            <!-- Mostrar el valor del campo -->
            <p>{{ field.value | translate }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
