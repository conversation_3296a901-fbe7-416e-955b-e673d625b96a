<div class="flex">
  <div class="box">
    <span class="title"> {{'Group.BusinessGroup' | translate}} </span>
    <h4 class="subtitle">{{ businessGroup }}</h4>
  </div>
  <div class="box title-customer">
    <span class="title"> {{'Group.Business' | translate}} </span>
    <h4 class="subtitle">{{ enterprise }}</h4>
  </div>
  <div class="box title-customer">
    <span class="title"> {{'Group.Country' | translate}} </span>
    <h4 class="subtitle">{{ country }}</h4>
  </div>
</div>

<app-wizard
  [steps]="steps"
  [currentStep]="currentStep"
  [buttonNextDisabled]="buttonNextDisabled"
  [buttonBackDisabled]="buttonBackDisabled"
  (currentStepChange)="currentStepChange($event)"
  (next)="next($event)"
  (back)="back($event)"
>
  <ng-container stepBody [ngSwitch]="currentStep">
    <div *ngSwitchCase="0">
      <app-group-data [action]="action" [idGroup]="idGroup"></app-group-data>
    </div>
    <div *ngSwitchCase="1">
      <app-levels
        [action]="action"
        [idGroup]="idGroup"
        [gruopState]="gruopState"
        [groupData]="groupData"
      ></app-levels>
    </div>
    <div *ngSwitchCase="2">
      <app-users
        (listUserToAdd)="listUserToAdd($event)"
        (idUserToDelete)="idUserToDelete($event)"
        [action]="action"
        [idGroup]="idGroup"
      ></app-users>
    </div>
    <div *ngSwitchDefault>output2</div>
  </ng-container>
  <ng-container customButtonInit>
    <button class="" mat-raised-button (click)="cancel()">
      <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
      {{ "Group.BackToGroups" | translate }}
    </button>
  </ng-container>
  <ng-container customButtonFinal>
    <button class="" mat-raised-button (click)="completeGroup()">
      {{saveButtonText}}
    </button>
  </ng-container>
</app-wizard>
