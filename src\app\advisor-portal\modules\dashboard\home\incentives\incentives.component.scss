.container {
    text-align: center;
  }

  .carousel {
    display: flex;
    overflow: hidden;
    position: relative;
    gap: 16px;
    justify-content: center;
  }

  .incentive-card {
    min-width: 300px;
    max-width: 320px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }

  .left {
    left: 0;
  }

  .right {
    right: 0;
  }

  .more-button {
    margin-top: 20px;
  }

  // 
  @import "/src/assets/styles/variables";
.cont-title {
  h4 {
    font-weight: 600 !important;
  }
}

.cont-cards {
  display: flex;
  min-width: 300px;
  max-width: 500px;
  height: 360px;
  flex-direction: column;
  align-items: center;
  flex: 1 0 0;
  border-radius: var(--Sharp, 0px);
  border: 1px solid var(--Black05, #f2f2f2);
  background: var(--wtw-brand-brand-wtw-white, #fff);
  box-shadow: 0px 2px 8px 0px #ccc;
}

.cont-tops {
  width: 100%;
  display: flex;
  padding: 16px 64px 64px 64px;
  justify-content: center;
  align-items: center;
  align-content: center;
  gap: 32px;
  align-self: stretch;
  flex-wrap: wrap;
}
.cont-header {
  display: flex;
  padding: 16px 32px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-bottom: 1px solid #f2f3f6;
}

.cont-item-tops {
  display: flex;
  padding: 10px 64px;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}
.top-number {
  display: flex;
  width: 32px;
  height: 32px;
  padding: var(--Subtle, 4px) 10px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  border-radius: 50px;
  color: var(--wtw-brand-brand-wtw-white, #fff);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.cont-body .cont-item-tops:nth-child(-n + 3) > div:nth-child(2) span {
  color: var(--wtw-brand-brand-wtw-onyx, #2f2c31);
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.216px;
}

.cont-body .cont-item-tops:nth-last-child(-n + 2) > div:first-child {
  background: var(--wtw-brand-brand-wtw-onyx, #2f2c31);
}

.cont-body .cont-item-tops:nth-child(3) > div:first-child {
  background: #a16811;
}

.cont-body .cont-item-tops:nth-child(2) > div:first-child {
  background: #a0a0a0;
}

.cont-body .cont-item-tops:first-child > div:first-child {
  background: #edaf06;
}

// copy
.overlay-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* Centra el título en el centro de la imagen */
  color: white;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7); /* Opcional: Agrega sombra para que el texto se vea mejor sobre fondos claros */
}


.valid-date, .description {
  margin: 0; /* Evita márgenes innecesarios */
  padding: 4px 0; /* Espaciado vertical entre los párrafos */
}

.description{
  font-weight: bolder;
  color:black;
}

.btn-conditions {
  margin-top: 12px;
  // background-color: #9c27b0;
  color: white;
}

.scaled-image {
  width: 100%;
  height: auto;
  object-fit: contain;
}

mat-card-actions {
  display: flex;
  justify-content: center; /* Centra horizontalmente */
  align-items: center; /* Centra verticalmente (si es necesario) */
}