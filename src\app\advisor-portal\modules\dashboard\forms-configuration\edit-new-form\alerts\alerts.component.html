<!-- datatable alertas -->
<div class="row mt-2">
    <app-table
      [displayedColumns]="estructTableWarnings"
      [data]="dataTableWarnings"
      (iconClick)="controller($event)"
    ></app-table>
  </div>
  
  <!-- botones añadir table-->
  <div class="row">
    <button
      class="mx-3 mt-2 col-2"
      type="button"
      (click)="openEditNewAlertModal(0)"
      mat-raised-button
      color="primary"
    >
      {{'Warnings.AddWarning' | translate }}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>

  <!-- modal editar/crear alerta -->
<ng-template #editNewWarningModal>
    <app-modal2 [titleModal]="tiitleText">
      <ng-container body>
          <app-edit-new-alert
            [idFormIn]="idForm"
            [idWarningIn]="idWarning"
            (resultOut)="getFormResult($event)"
          >
          </app-edit-new-alert>
      </ng-container>
  
      <ng-container customButtonRight>
        <button
          class="w-auto mr-3"
          type="button"
          (click)="saveWarningClick()"
          mat-raised-button
          color="primary"
        >
            {{ saveButtonText }} 
            <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        </button>
      </ng-container>
    </app-modal2>
  </ng-template>