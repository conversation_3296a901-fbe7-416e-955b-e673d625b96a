import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChooseCountryAndCompanyComponent } from '../../../../../shared/components/choose-country-and-company/choose-country-and-company.component';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from '../../../../../shared/models/response';
import { RoleService } from '../../../../../shared/services/role/role.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MessageService } from '../../../../../shared/services/message/message.service';
import { ConfigurationAdvisorComponent } from './configuration-advisor/configuration-advisor.component';
import { ConfigurationClientComponent } from './configuration-client/configuration-client.component';


@Component({
  selector: 'app-all-configuration-index',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    MatInputModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    TranslateModule,
    ConfigurationAdvisorComponent,
    ConfigurationClientComponent
  ],
  templateUrl: './all-configuration-index.component.html',
  styleUrls: ['./all-configuration-index.component.scss'],
})
export class AllConfigurationIndexComponent implements OnInit {
  userType: any[] = [];
  idUserType: number = 1;

  constructor(
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    private _roleService: RoleService
  ) {}

  ngOnInit(): void {
    this.getAllTypeUser();
  }

  getAllTypeUser() {
    this._roleService
      .getAllTypeUser()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.userType = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.userType = resp.result;
          }
        }
      });
  }

  userTypeChange(evt: any) {
    this.idUserType = evt.value;
  }
}
