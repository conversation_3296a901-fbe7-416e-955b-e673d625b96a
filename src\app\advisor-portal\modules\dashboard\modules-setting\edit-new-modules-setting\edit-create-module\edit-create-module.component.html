<form [formGroup]="form" (ngSubmit)="complete()">
  <div class="row mt-5">
    <div class="col-md-8">
      <div class="col-12 col-md-12">
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{ "ModulesSetting.Modules.ModuleName" | translate }}
          </mat-label>
          <input matInput formControlName="vDescription" PreventionSqlInjector/>
          <mat-error
            *ngIf="utilsSvc.isControlHasError(form, 'vDescription', 'required')"
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
      <div class="col-12 col-md-12" *ngIf="!form.get('hasChildren')?.value">
        <mat-form-field class="w-100 mb-2" appearance="fill">
          <mat-label>
            {{ "ModulesSetting.AssociatedProcess" | translate }}
          </mat-label>
          <mat-select formControlName="fkIdProcess">
            <mat-option
              *ngFor="let process of processes"
              [value]="process.pkIIdProcessFamily"
            >
              {{ process.vNameProcess }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="utilsSvc.isControlHasError(form, 'fkIdProcess', 'required')"
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="col-md-6 col-sm-12">
        <mat-slide-toggle class="mb-3" formControlName="bActive">
          {{ "ModulesSetting.Modules.ActiveModule" | translate }}
        </mat-slide-toggle>
      </div>

      <!-- Tiene submódulos -->
      <div class="cont-mat-slide-toggle mb-2">
        <div class="mt-2">
          <mat-slide-toggle class="mb-3" formControlName="hasChildren">
            {{ "ModulesSetting.Modules.ItHasSubmodules" | translate }}
          </mat-slide-toggle>
        </div>
        <div class="mx-1">
          <mat-icon class="click" matTooltipPosition="right"
            matTooltip=" {{'Tooltips.HasModuleCheck' | translate}}">help_outline</mat-icon>
        </div>
      </div>

      <div
        class="col-md-12 col-sm-12 mt-2"
        *ngIf="!form.get('hasChildren')?.value"
      >
        <mat-form-field class="w-100 mb-2">
          <mat-label>{{
            "ModulesSetting.Modules.ProductsLabel" | translate
          }}</mat-label>
          <mat-select formControlName="idProducts" multiple>
            <mat-option
              *ngFor="let product of products"
              [value]="product.pkIIdProduct"
              >{{ product.vProductName }}</mat-option
            >
          </mat-select>
        </mat-form-field>
      </div>
      <div>
        <h5 class="fw-bold mb-2">
          {{ 'ModulesSetting.Visibility' | translate }}
        </h5>
        <div class="mt-2 d-flex" style="gap:1rem">
          <div class="form-check mb-3">
            <input class="form-check-input" 
              type="checkbox" 
              id="checkAdvisorPortal"
              formControlName="binAdvisorPortal"
              />
            <label class="form-check-label" for="checkAdvisorPortal">
              {{'ModulesSetting.OptionAdvisorPortal' | translate}}
            </label>
          </div>
          <div class="form-check mb-3">
            <input class="form-check-input" 
              type="checkbox" 
              id="checkCustomerPortal"
              formControlName="binCustomerPortal"
              (change)="form.get('bLoginRequired')?.setValue(false)"
              />
            <label class="form-check-label" for="checkCustomerPortal">
              {{'ModulesSetting.OptionCustomerPortal' | translate}}
            </label>
          </div>
          <div class="form-check mb-3" *ngIf="form.get('binCustomerPortal')?.value">
            <input class="form-check-input" 
              type="checkbox" 
              id="checkLoginRequired"
              formControlName="bLoginRequired"
              />
            <label class="form-check-label" for="checkLoginRequired">
              {{'ModulesSetting.LoginRequired' | translate}}
            </label>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <app-generic-image-picker
        class="col-12 col-md-4"
        [title]="titleUploaderImage"
        [description]="descriptionUploaderImage"
        description=""
        (changeFile)="changeImage($event)"
        [imageSrc]="imageSrc"
      >
      </app-generic-image-picker>
    </div>
  </div>
</form>
