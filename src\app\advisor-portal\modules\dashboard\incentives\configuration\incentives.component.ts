import { CommonModule } from '@angular/common';
import { Component, ElementRef, Inject, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ReactiveFormsModule, FormsModule, FormGroup, FormBuilder, Validators, AbstractControl, FormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { DragDropUploadComponent } from 'src/app/shared/components/drag-drop-upload/drag-drop-upload.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { UploadCompositeCatalogComponent } from "../../catalog-setting/all-catalog/composite-catalog/catalog-management/upload-composite-catalog/upload-composite-catalog.component";
import { GenericButtonsComponent } from 'src/app/shared/components/generic-buttons/generic-buttons.component';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { NotMonetaryIncentivesTableModel } from 'src/app/shared/models/incentives/not-monetary-incentives-table.model';
import { MonetaryIncentivesModel } from 'src/app/shared/models/incentives/monetary.incentives.model';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';

import * as XLSX from 'xlsx';
import { FileService } from 'src/app/shared/services/file/file.service';
import { SalesModel } from 'src/app/shared/models/incentives/sales.model';
import { EXCEL_SALES_HEADERS } from 'src/app/shared/models/incentives/excel-sales.headers';
import { IncentivesService } from 'src/app/shared/services/incentives/incentives.service';
import { catchError,of, Subscription } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { EXCEL_CANCELLATIONS_HEADERS } from 'src/app/shared/models/incentives/excel-cancellations.headers';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import Swal from 'sweetalert2';
import { RouterModule } from '@angular/router';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';


@Component({
  selector: 'app-incentives',
  standalone: true,
  imports: [ReactiveFormsModule,CommonModule,TranslateModule,ChooseCountryAndCompanyComponent,MatSlideToggleModule,BreadcrumbComponent,MatButtonModule,FormsModule,MatFormFieldModule,MatInputModule,MatIconModule,MatTabsModule,
    TableComponent,Modal2Component,MatCardModule,MatSelectModule, GenericButtonsComponent,MatInputModule,MatDatepickerModule,MatNativeDateModule,RouterModule
    ,MatSelectModule,MatFormFieldModule,MatTooltipModule, ValidationInputFileDirective
  ],
  templateUrl: './incentives.component.html',
  styleUrl: './incentives.component.scss'
})
export class IncentivesComponent implements OnInit {


  @ViewChild('NotMonetaryIncentiveModal') NotMonetaryIncentiveModal?: TemplateRef<any>;
  @ViewChild('fileInput', { static: false }) fileInput?: ElementRef;
  @ViewChild('imageInput', { static: false }) imageInput?: ElementRef;
  @ViewChild('pdfInput', { static: false }) pdfInput?: ElementRef;
  
  @ViewChild('ShowIncentiveNotMonetaryModal') ShowIncentiveNotMonetaryModal?: TemplateRef<any>;

  private _settingCountryAndCompanySubscription?: Subscription;
  
  inicio: string = 'Inicio';
  colors: string = 'Incentivos';

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.colors, link: '/dashboard/incentivos' },
  ];

  filePath:string="assets/excels/";
  templateFileName="Plantilla_Estandard_Carga_Incentivos_Ventas.xlsx";

  form:FormGroup = new FormGroup({});

  years: number[] = [];
  disposableMonths: { name: string; value: number }[] = [];
  selectedYear!: number;
  selectedMonth!: number;
  selectedFile: File | null = null;
  fileName: string = '';

  structureIncentivesTable: BodyTableModel[] = [
    {
      columnLabel: '#',
      columnValue: 'pkiIdIncentiveNonMonetary',

      
    },
    {
      columnLabel: this._translateService.instant('IncentivesForm.NotMonetaryCampaignNameColumn'),
      columnValue: 'vCampainName',       
    },
    {
      columnLabel: this._translateService.instant('IncentivesForm.NotMonetaryTypeColumn'),
      columnValue: 'vCampainType',       
    },
    {
      columnLabel: this._translateService.instant('IncentivesForm.NotMonetaryEffectiveDateColumn'),
      columnValue: 'dStartCampain',       
    },
    {
      columnLabel: this._translateService.instant('IncentivesForm.NotMonetaryEffectiveEndDateColumn'),
      columnValue: 'dEndCampain',       
    },
    {
      columnLabel: this._translateService.instant('IncentivesForm.NotMonetaryStatusColumn'),
      columnValue: 'bActive',  
      functionValue: (item: any) => this._utilsService.changeStatusValue(item)     
    },
    {
      columnLabel: this._translateService.instant('IncentivesForm.NotMonetarySeeColumn'),
      columnValue: 'See',
      columnIcon: 'visibility ',      
    },    
    {
      columnLabel: this._translateService.instant('ValidationForm.StructTableValidationModifyColumn'),
      columnValue: 'Modify',
      columnIcon: 'create',
    },
    {
      columnLabel: this._translateService.instant('ValidationForm.StructTableValidationDeleteColumn'),
      columnValue: 'Delete',
      columnIcon: 'delete',
    }  

  ];   

  NotMonetaryIncentivesTable: NotMonetaryIncentivesTableModel[]=[]

  titleModal:string =this._translateService.instant('IncentivesForm.AddNotMonetaryButton');
  
  notMonetaryIncentiveModalForm:FormGroup=new FormGroup({});

  salesModel!: SalesModel;
  errores: string[] = [];
  isExcelFileValid:boolean=false;
  submitted: boolean=false;
  fkIIdBusinessByCountry:number=0;
  idBusinessCountry: number = 0;
  uploadFileById: number=0;
  fileUrl!: SafeResourceUrl; 
  idMonetaryIncentive:number=0;
  imageFile: File | null = null;
  imageName!:string;
  pdfFile: File | null = null;
  pdfName!:string;
  imageSrc!:string
  idContent:number=0;
  imageFileId:number = 0;
  pdfFileId:number = 0;
  idIncentiveNotMonetary:number=0;


  popCampaingName:string='';
  popValidUntil:string='Válido hasta el: ';
  popDescription:string='';
  popImagePath:string ='';
  popPdfPath:string ='';
  imageBase64!: string;
  imageSrcPath!:string;
  srcImage!:string;

  constructor( 
    private _messageService: MessageService,private _translateService: TranslateService,
    public _utilsService: UtilsService,private _formBuilder: FormBuilder,public modalDialog: MatDialog,
    private _fileService: FileService, public _incentivesService: IncentivesService,
    private _settingService: SettingService,private sanitizer: DomSanitizer,  @Inject(MAT_DIALOG_DATA) public data:any
  ){}

    
  ngOnInit(): void {    
    this.initForm();
    this.getYears();
    this.getBusinessCountry();
   
  }

  getBusinessCountry() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry; 
            this.getAllIncentivesNonMonetarys();                       
          }
        }
      );
  }

  initForm() {   
    this.form = this._formBuilder.group({
     active:[false],
     showSales:[false],
     showCancellations:[false],
     year:['',[Validators.required]],
     month:['',[Validators.required]],     
    },
    { validators: this.atLeastOneCheckedValidator },    
  );
  }

  getYears(): void {
    const anioActual = new Date().getFullYear();
    this.years = Array.from({ length: anioActual - 1999 }, (_, i) => anioActual - i);
  }

  getMonths(selectedYear: number): void {     
    const months = [
      { month: 'Enero', value: 1 },
      { month: 'Febrero', value: 2 },
      { month: 'Marzo', value: 3 },
      { month: 'Abril', value: 4 },
      { month: 'Mayo', value: 5 },
      { month: 'Junio', value: 6 },
      { month: 'Julio', value: 7 },
      { month: 'Agosto', value: 8 },
      { month: 'Septiembre', value: 9 },
      { month: 'Octubre', value: 10 },
      { month: 'Noviembre', value: 11 },
      { month: 'Diciembre', value: 12 },
    ];
  
    const anioActual = new Date().getFullYear();
    const mesActual = new Date().getMonth() + 1; 
      
    const limiteMes = selectedYear === anioActual ? mesActual : 12;      
    this.disposableMonths = months
      .slice(0, limiteMes) 
      .map((name, value) => ({ name:name.month, value }));
  }

  getSelectedYear() {
    this.selectedYear=this.form.get('year')?.value;
    this.getMonths(this.selectedYear);
  }  

  downloadFile() { 
    const fileUrl = `${this.filePath}${this.templateFileName}`; 
    const link = document.createElement('a');
    link.href = fileUrl;  
    link.download = this._translateService.instant('IncentivesForm.TabMonetaryFileName'); 
    link.click();
  }
 

  selectFile(event: any) {
    this.errores=[];          
    this.fileName = event.target.files[0].name;
    this.selectedFile = event.target.files[0];      
   
    this.readExcelFile(this.selectedFile);   
       
  }

  triggerfileInput(): void {
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';      
    }
  }

  readExcelFile(file: File | null) {
    if (!file) return;
  
    let validandoDiv = document.getElementById('validando') as HTMLDivElement;
    validandoDiv.style.display = 'block';

    const reader = new FileReader();    
    reader.onload = async (e: any) => {         
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });            
        for(const sheet of workbook.SheetNames){        

          if (sheet!=='Ventas' && sheet!=='Cancelaciones') {
            this.fileValidationMessages('SheetsNotMatch');
            this.isExcelFileValid=false;
            this.triggerfileInput();
            return;
          } 

          if (!sheet) {
            this.fileValidationMessages('FileNoValid');
          }
          const excelData = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], {header: 1 });
          if (excelData.length>1) {
            this.isExcelFileValid = this.fileValidation(excelData,sheet);
          }               
        }
      
        if (this.isExcelFileValid) {
          this.fileValidationMessages('FileValidationOk'); 
        }
      } catch (error) {
        console.error('Error reading Excel file:', error);
      }finally {       
        validandoDiv.style.display = 'none';
      }
      
      
    };
   
    reader.readAsArrayBuffer(file);  
    
  }

  validateUploadedFileExists() {       
    let year= this.form.get('year')?.value;
    let month= this.form.get('month')?.value+1;      
    this._incentivesService.getValidateUploadedFileExists(year,month,this.idBusinessCountry).subscribe({
      next:(response)=>{
        debugger
        if(response.result){     
          this.fileValidationMessages('FileExistsAlready');                          
          this.uploadFileById = response.result.fkIIdUploadFile;
          this.idMonetaryIncentive=response.result.pkIIdIncentiveMonetary
          this.downloadExistsFile(false);                   
          this.form.get('fileName')?.setValue(response.result.filename); // filename         
        }else{
          this.resetFileFields();
        }
      },
      error:(err)=> {                        
       console.log(err.error.message);       
      }
    })
  }

  downloadExistsFile(download : boolean) {
    this._fileService.getUploadFileById(this.uploadFileById).subscribe({      
      next: (fileData) => {
        this.fileName =  fileData.result.vFileName
        const filePath = fileData.result?.vFilePath;
        if (filePath && download) {
          this.downloadFileByFilePath(filePath, fileData.result.vFileName);
        } else {
          if(download)
            this._messageService.messageWaring('','vFilePath no está disponible en la respuesta:')         
        }
      },
      error: (err) => {
        this._messageService.messageWaring('','Error retrieving file data:' + err)
      }    
    });
  }
 
  downloadFileByFilePath(filePath: string, title: string) {
    this._fileService.downloadFileByFilePath(filePath).subscribe({
      next: (blob) => {
        const link = document.createElement('a');
        const objectUrl = URL.createObjectURL(blob);
        this.fileUrl = this.sanitizer.bypassSecurityTrustResourceUrl(objectUrl);       
         link.href = objectUrl;
         link.download = title;
         link.click();
         window.URL.revokeObjectURL(objectUrl);
      },
      error: (err) => {
        this._messageService.messageWaring('','Error al descargar el archivo')
      }
    });
  }


  deleteIncentiveMonetary(typeOfIncentive: string){        
    Swal.fire({
      title: this._translateService.instant('IncentivesForm.TabMonetaryValidationModalDeleteFileTitle'),
      html: this._translateService.instant('IncentivesForm.TabMonetaryValidationModalDeleteFileText'),
      icon: 'warning',        
      showCancelButton: true,
      denyButtonText: this._translateService.instant('Cancel'),
      confirmButtonText: this._translateService.instant('IncentivesForm.TabMonetaryValidationModalDeleteButtonText'),        
      confirmButtonColor: 'blue',
      denyButtonColor: 'black',
      reverseButtons: true,
    }).then((result) => {        
      if (result.isConfirmed) {
        let idIncentive=0;
        switch (typeOfIncentive) {
          case 'NotMonetary':
              idIncentive=this.idIncentiveNotMonetary;
            break;        
          default:
            idIncentive=this.idMonetaryIncentive;
            break;
        }
        if (!idIncentive || idIncentive <= 0) {
          this.resetFileFields();
          this.fileValidationMessages('Deleted');  
          return;
        }
        this._incentivesService.deleteIncentive(idIncentive,typeOfIncentive).subscribe({
          next:(response)=>{
            if(response){              
              this.fileValidationMessages('Deleted');   
              this.resetFileFields();
              this.idMonetaryIncentive = 0
            }
          },
          error:(err)=> {                        
            this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), err.error.message);
            
          },
        })
      } else if (result.isDenied) {        
        return 
      }
    });
  }
  private resetFileFields(): void {
    this.fileName = '';
    this.selectedFile = null;
    this.errores = [];
  }

  fileValidation(excelData: any[],sheetName:string): boolean {
   
    const encabezadosArchivo = excelData[0].map((header: any) => header.toString().trim());
    let earlyExit = false;
    if (!this.validateHeadersFile(encabezadosArchivo,sheetName)) {
      this.fileValidationMessages('HeadersNotMatch');      
      return this.isExcelFileValid;
    }

    if (excelData[1].length===0 && this.errores.length===0) {
      this.fileValidationMessages('NotData');
      return this.isExcelFileValid;
    }

    switch (sheetName) {
    case 'Cancelaciones':
      excelData.slice(1).forEach((row,index)=>{      
        if (earlyExit) return; 
        if (row.length === 0) {
          earlyExit = true;
          return;
        }  
        this.validateCancellationsRow(row,index+2,sheetName);     
      })
      break;
    
    default:
     
      excelData.slice(1).forEach((row,index)=>{  
          if (earlyExit) return; 

          if (row.length === 0) {
            earlyExit = true;
            return;
          }  
          this.validateSalesRow(row,index+2,sheetName);                       
      })     
    break;
    } 
    

    if (this.errores.length===null) {
      this.isExcelFileValid=true;
    }else{
       this.triggerfileInput();
    }     
   
    return this.isExcelFileValid;       
  }

  validateHeadersFile(encabezadosArchivo: string[],sheetName:string): boolean {
    
    switch (sheetName) {
      case 'Cancelaciones':
        return JSON.stringify(encabezadosArchivo) === JSON.stringify(EXCEL_CANCELLATIONS_HEADERS);
        break;
    
      default:
        return JSON.stringify(encabezadosArchivo) === JSON.stringify(EXCEL_SALES_HEADERS);
        break;
    }

   
  }

  validateSalesRow(fila: any[], filaIndex: number,sheetName:string): void {    
          
    const [idPoliza, idTomador, tomador, email, idAsesor,  fechaInicio, fechaFin, numeroMeses, valorPrima,  valorTotal, aseguradora, producto, incentivo, accion ] = fila;      

    const yearSelected = this.form.get('year')?.value;   
    const monthSelected = this.form.get('month')?.value + 1;

    if (isNaN(idPoliza)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[0]}: debe ser un número.`);
    }
  
    if (isNaN(idTomador)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[1]}: debe ser un número.`);
    }
  
    if (typeof tomador !== 'string' || tomador.trim() === '') {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[2]}: es requerido.`);
    }  
    if (!this._utilsService.isAValidEmail(email)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[3]}: no es un correo válido.`);
    }
    
    if (isNaN(idAsesor)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[4]}: debe ser un número.`);
    }
  
    if (!this._utilsService.isAValidDate(fechaInicio)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[5]}: no es una fecha válida.`);
    }
      
    if (!this._utilsService.isAValidDate(fechaFin)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[6]}: no es una fecha válida.`);
    }

    if (!this._utilsService.isDateOfExcelBetweenRangeOfDateSelected(fechaInicio,yearSelected,monthSelected)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[5]}: la fecha de inicio de vigencia no corresponde a la fecha seleccionada.`);
    }
              
    if (isNaN(numeroMeses) || numeroMeses <= 0) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[7]}: debe ser un número positivo.`);
    }
    if (isNaN(valorPrima) || valorPrima < 0) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[8]}: debe ser un número positivo.`);
    }

    if (isNaN(valorTotal) || valorTotal < 0) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[9]}: debe ser un número positivo.`);
    }
    if(accion.toString().toLowerCase()!=='venta' ){
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_SALES_HEADERS[13]}: la acción deber ser una venta.`);
    }

                    
    if (this.errores.length > 0) {      
      this.isExcelFileValid=false;
      this.errores.forEach(error => console.info(error));
    }else{
      this.isExcelFileValid=true;
    }

    
  }

  validateCancellationsRow(fila: any[], filaIndex: number,sheetName:string): void {    
          
    const [idPoliza, idTomador, tomador, email, idAsesor,  fecha_cancelacion, valorPrima,  valorTotal, aseguradora, producto, incentivo, accion ] = fila;          
    const yearSelected = this.form.get('year')?.value;   
    const monthSelected = this.form.get('month')?.value + 1;

    if (isNaN(idPoliza)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[0]}: debe ser un número.`);
    }
  
    if (isNaN(idTomador)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[1]}: debe ser un número.`);
    }
  
    if (typeof tomador !== 'string' || tomador.trim() === '') {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[2]}: es requerido.`);
    }  
    if (!this._utilsService.isAValidEmail(email)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[3]}: no es un correo válido.`);
    }
    
    if (isNaN(idAsesor)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[4]}: debe ser un número.`);
    }
  
    if (!this._utilsService.isAValidDate(fecha_cancelacion)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[5]}: no es una fecha válida.`);
    }         

    if (!this._utilsService.isDateOfExcelBetweenRangeOfDateSelected(fecha_cancelacion,yearSelected,monthSelected)) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[5]}: la fecha de inicio de vigencia no corresponde a la fecha seleccionada.`);
    }
 
             
    if (isNaN(valorPrima) || valorPrima < 0) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[6]}: debe ser un número positivo.`);
    }

    if (isNaN(valorTotal) || valorTotal < 0) {
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[7]}: debe ser un número positivo.`);
    }

    if(accion.toString().toLowerCase()!=='cancelación' ){
      this.errores.push(`Hoja ${sheetName} - Fila ${filaIndex} - ${EXCEL_CANCELLATIONS_HEADERS[11]}: la acción deber ser una cancelación.`);
    }

                    
    if (this.errores.length > 0) {      
      this.isExcelFileValid=false;
      this.errores.forEach(error => console.info(error));
    }else{
      this.isExcelFileValid=true;
    }

    
  }
    

  fileValidationMessages(key: string){
    switch (key) {
      case 'Ok':
        this._messageService.modalMessage('',
              this._translateService.instant('IncentivesForm.TabMonetaryValidationModalValidFileTitle'),
              "success",
              this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
        break;
        case 'FileNoValid':
          this._messageService.modalMessage(
                this._translateService.instant('IncentivesForm.TabMonetaryValidationModalNoValidFileTitle'),
                this._translateService.instant('IncentivesForm.TabMonetaryValidationModalNoValidFileText'),
                "error",
                this._translateService.instant('IncentivesForm.TabMonetaryValidationModalGetItButtonText'), );  
        break;      
        case 'Deleted':
          this._messageService.modalMessage('',
                this._translateService.instant('IncentivesForm.TabMonetaryValidationModalFileDeletedText'),
                "success",
                this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
        break;
        case 'FileExistsAlready':
                this._messageService.modalMessage('',
                this._translateService.instant('IncentivesForm.TabMonetaryValidationModalIncentiveExistsAlreadyText'),
                "warning",
                this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
        break;

        case 'HeadersNotMatch':
          this._messageService.modalMessage('Encabezados no validos',
            'Los encabezados no coinciden con la plantilla.',
          // this._translateService.instant('IncentivesForm.TabMonetaryValidationModalIncentiveExistsAlreadyText'),
          "error",
           this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'),
          
           );  
        break;
        case 'NotData':
          this._messageService.modalMessage('Archivo Vacio',
            'El archivo cargado no tiene datos.',
          // this._translateService.instant('IncentivesForm.TabMonetaryValidationModalIncentiveExistsAlreadyText'),
          "error",
           this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'),
          
           );  
        break;
        case 'SheetsNotMatch':
          this._messageService.modalMessage('Nombre de hojas',
            'El nombre de las hojas del archivo excel, no corresponden con la plantilla',
          // this._translateService.instant('IncentivesForm.TabMonetaryValidationModalIncentiveExistsAlreadyText'),
          "error",
           this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'),
          
           );  
        break;

        case 'FileValidationOk':
          this._messageService.modalMessage('Validación',
            'El archivo excel ha superado la validación de datos',
          // this._translateService.instant('IncentivesForm.TabMonetaryValidationModalIncentiveExistsAlreadyText'),
          "success",
           this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'),          
           );  
        break;
      default:
        this._messageService.modalMessage('Validación',
          'Ningún archivo ha sido cargado para validar',
        // this._translateService.instant('IncentivesForm.TabMonetaryValidationModalIncentiveExistsAlreadyText'),
        "error",
         this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'),          
         );  
        break;
     }
  }
  


  atLeastOneCheckedValidator(group: AbstractControl) {
    const showSales = group.get('showSales')?.value;
    const showCancellations = group.get('showCancellations')?.value;

    return showSales || showCancellations ? null : { atLeastOneRequired: true };
  }
  
  hasError(): boolean {
    return this.submitted &&this.form.hasError('atLeastOneRequired');
  }
 
  saveMonetaryIncentives() {  
    this.submitted = true;
    if(this.selectedFile===null){
     this.fileValidationMessages('')
     return;
    }

    if(!this.form.valid){
      return this.form.markAllAsTouched()      
    }
   
    if (this.isExcelFileValid) {
      const incentiveMonetary:  MonetaryIncentivesModel= {
        pkIIdIncentiveMonetary:0,      
        bActive   :        this.form.get('active')?.value,
        bShowSales :       this.form.get('showSales')?.value,
        bShowCancelations: this.form.get('showCancellations')?.value,        
        iYear:             this.form.get('year')?.value,
        vMonth:            this.form.get('month')?.value + 1,
        fkIIdBusinessByCountry:this.idBusinessCountry,
        fkIIdUploadFile:0,
        ExcelFile:         this.selectedFile,
	     
      }

      if(this.idMonetaryIncentive > 0){
        incentiveMonetary.pkIIdIncentiveMonetary = this.idMonetaryIncentive 
        incentiveMonetary.fkIIdUploadFile =  this.uploadFileById
        this._incentivesService.updateMonetaryIncentives(incentiveMonetary).pipe(
          catchError((error)=>{
            this._messageService.messageWaring('', error.error.message);      
            return of([]);
          })
        ).subscribe((resp:ResponseGlobalModel | never[])=>{
          if (Array.isArray(resp)) {
            this._messageService.modalMessage('',
              'Hubo un error al tratar de guardar los incentivos.',
              "error",
              this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
          }else{
            this._messageService.messageSaveMapping(this._translateService.instant('IncentivesForm.TabMonetaryValidationModalValidFileTitle'),"", "success" );  
            this.resetFileFields()
            this.form.reset();
          }                
        });
      }else{
        this._incentivesService.saveMonetaryIncentives(incentiveMonetary).pipe(
          catchError((error)=>{
            this._messageService.messageWaring('', error.error.message);      
            return of([]);
          })
        ).subscribe((resp:ResponseGlobalModel | never[])=>{
          if (Array.isArray(resp)) {
            this._messageService.modalMessage('',
              'Hubo un error al tratar de guardar los incentivos.',
              "error",
              this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
          }else{
            this._messageService.messageSaveMapping(this._translateService.instant('IncentivesForm.TabMonetaryValidationModalValidFileTitle'),"", "success" );  
            this.resetFileFields()
            this.form.reset();
          }                
        });
      }
    }
           
  }
 

  // no monetarios
  setStructureIncentivesTable(){
    this.structureIncentivesTable = [
      {columnLabel: this._translateService.instant('IncentivesForm.NotMonetaryCampaignNameColumn'), columnValue: 'vFormField' },
      {columnLabel: this._translateService.instant('IncentivesForm.NotMonetaryTypeColumn'),columnValue: 'vPolicyField'},    
      {columnLabel: this._translateService.instant('IncentivesForm.NotMonetaryEffectiveDateColumn'),columnValue: 'bState',functionValue: (item: any) => this._utilsService.changeAssignedValue(item)},  
      {columnLabel:  this._translateService.instant('IncentivesForm.NotMonetaryEffectiveEndDateColumn'),columnValue: 'bMandatory'},   
      {columnLabel:  this._translateService.instant('IncentivesForm.NotMonetaryStatusColumn'),columnValue: 'bMandatory'},   
      {columnLabel:  this._translateService.instant('IncentivesForm.NotMonetarySeeColumn'),columnValue: 'bMandatory'},   
      {columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.delete'),columnValue: 'Delete',columnIcon: 'delete'},
      {columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.modify'),columnValue: 'Modify',columnIcon: 'create'},
      
    ];   
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'Modify':                
          this.openModal();
          this.idContent=event.value.pkiIdIncentiveNonMonetary;                              
          const dataOfIncentiveNoMonetary = this.findInTableIncentiveNotMonetary(event.value.pkiIdIncentiveNonMonetary)                    
          this.mapIncentiveToForm(dataOfIncentiveNoMonetary);
          
              
        break;
    
      case 'Delete':
      this.idIncentiveNotMonetary=event.value.pkiIdIncentiveNonMonetary;
      this.deleteIncentiveMonetary('NotMonetary')
      this.getAllIncentivesNonMonetarys();
      break;
      default:
        this.showIncentiveNotMonetary(event.value.pkiIdIncentiveNonMonetary);
        break;
    }
  }


  findInTableIncentiveNotMonetary(idIncentiveNonMonetary: any) {
    return this.NotMonetaryIncentivesTable.find(item => item.pkiIdIncentiveNonMonetary === idIncentiveNonMonetary);
  }

  mapIncentiveToForm(noMonetaryIncentive: any): void {
    this.notMonetaryIncentiveModalForm.patchValue({
      active: noMonetaryIncentive.bActive,
      type: noMonetaryIncentive.vCampainType,
      campaignName: noMonetaryIncentive.vCampainName,
      startDate: new Date(noMonetaryIncentive.dStartCampain),
      endDate: new Date(noMonetaryIncentive.dEndCampain),
      description: noMonetaryIncentive.vDescription,
      imagePath: this.getFileName(noMonetaryIncentive.imagePath),
      fileConditionsPath: this.getFileName(noMonetaryIncentive.fileConditionsPath)
    });

    this.imageFileId = noMonetaryIncentive.fkIIdUploadImage;    
    const imgPath=this.getFileName(noMonetaryIncentive.imagePath);
    this.imageName= this.extractFileName(imgPath);

    this.pdfFileId = noMonetaryIncentive.fkiIdUploadFileConditions; 
    const pdfPath=this.getFileName(noMonetaryIncentive.fileConditionsPath);
    this.pdfName= this.extractFileName(pdfPath);   
    
  }

  getFileName = (path: string): string => {
    const match = path.match(/[^\\]+$/);
    return match ? match[0] : '';
  };

  extractFileName = (path: string): string => {    
    return path.replace(/^\d{2}-\d{2}-\d{4}_[a-f0-9]+_/, '');
  };
   

  showIncentiveNotMonetary(id: number){
    this.NotMonetaryIncentivesTable.forEach(element => {
      if (element.pkiIdIncentiveNonMonetary===id) { 
        this.popCampaingName=element.vCampainName;             
        this.popValidUntil= element.dEndCampain;
        this.popDescription=element.vDescription; 
        this.popImagePath=element.imagePath;                          
        this.popPdfPath=element.fileConditionsPath;

        this.getImagesIncentives(element.fkIIdUploadImage) 
      }

      
    });
    this.modalDialog.open(this.ShowIncentiveNotMonetaryModal!, {
      width: '720px',
    })
  }

  getImagesIncentives(idUploadImage: number){    
    this._fileService.getUploadFileById(idUploadImage).pipe(
      catchError((error)=>{
        this._messageService.messageWaring('', error.error.message);      
        return of([]);
      })
    ).subscribe((resp:ResponseGlobalModel | never[])=>{
      if (Array.isArray(resp)) {
        this._messageService.modalMessage('','Hubo un error al tratar de obtener todos los incentivos.',          "error",
          this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
      }else{    
            
            this.imageSrcPath=resp.result.vFilePath;           
            this.imageBase64=resp.result.imageBase64;                                     
            this.getBitsImage();         
      }                
    });;
    
    
  }


  getBitsImage() {
    let src = '';    
    const fileName = this.imageSrcPath.split('\\').pop() || '';      
    const extension = fileName.split('.').pop() || 'png';      
    src = `data:image/${extension};base64,${this.imageBase64}`;
    this.srcImage=src;     
      
          
  }
  
  initNotMonetaryModalForm() {
    this.notMonetaryIncentiveModalForm = this._formBuilder.group({      
        active: new FormControl<boolean>(false, { nonNullable: true }),
        type: new FormControl<string>('', { nonNullable: true, validators: [Validators.required] }),
        campaignName: new FormControl<string>('', { nonNullable: true, validators: [Validators.required] }),
        startDate: new FormControl<Date>(new Date(), { nonNullable: true, validators: [Validators.required] }),
        endDate: new FormControl<Date>(new Date(), { nonNullable: true, validators: [Validators.required] }),
        description: new FormControl<string>('', { nonNullable: true, validators: [Validators.required] }),
        notMonetaryImageName: new FormControl<string>(''),
        conditionsFileName: new FormControl<string>(''),
      },
      { validators: this.dateRangeValidator }
   )
  }

  openModal() {        
    this.initNotMonetaryModalForm();
    this.modalDialog.open(this.NotMonetaryIncentiveModal!, {width: '720px'});
  }  

  dateRangeValidator(group: FormGroup) {
    const startDate = group.get('startDate')?.value;
    const endDate = group.get('endDate')?.value;
    
    if (startDate && endDate && endDate < startDate) {
      return { dateRangeInvalid: true };
    }
    return null;
  }

  isDateRangeInvalid(): boolean {
    return this.notMonetaryIncentiveModalForm.hasError('dateRangeInvalid');
  }

  addNoMonetaryIncentive() {
    this.openModal(); 
  }


  closeModal() {
    this.modalDialog.closeAll();
  }
 
  selectImage(event: any) {
    this.imageName = event.target.files[0].name;
    this.imageFile = event.target.files[0];     
  }

  selectPdf(event: any) {
    this.pdfName = event.target.files[0].name;
    this.pdfFile = event.target.files[0];      
  }

  setDataForForm(){
    const initDate = this.formatearFecha(this.notMonetaryIncentiveModalForm.get('startDate')!.value)
    const finishDate = this.formatearFecha(this.notMonetaryIncentiveModalForm.get('endDate')!.value)    

    const incentiveNotMonetary:  NotMonetaryIncentivesTableModel= {
      pkiIdIncentiveNonMonetary:this.idContent > 0 ? this.idContent : 0,            
      fkIIdUploadImage:this.imageFileId > 0 ?  this.imageFileId : 0,          
      fKiIdUploadFileConditions: this.pdfFileId > 0 ? this.pdfFileId : 0,

      fkIIdBusinessByCountry:this.idBusinessCountry,  
      bActive:               this.notMonetaryIncentiveModalForm.get('active')?.value,      
      vCampainType:          this.notMonetaryIncentiveModalForm.get('type')?.value,
      vCampainName:          this.notMonetaryIncentiveModalForm.get('campaignName')?.value,
      dStartCampain:         initDate,
      dEndCampain:           finishDate,
      bState:                true,
      vDescription:          this.notMonetaryIncentiveModalForm.get('description')?.value,
      vImageName:            this.notMonetaryIncentiveModalForm.get('imageName')?.value,	          
      fileImage:             this.imageFile!,
      fileCondition:         this.pdfFile!,
      imagePath:'',
      pdfPath:'',
      fileConditionsPath:'',
    }
    
    return incentiveNotMonetary;
  }

  saveIncentiveNotMonetary(){
    if(!this.notMonetaryIncentiveModalForm.valid){
      return this.notMonetaryIncentiveModalForm.markAllAsTouched()      
    }   
    const newIncentiveNotMonetary = this.setDataForForm();    
    this._incentivesService.saveNotMonetaryIncentives(newIncentiveNotMonetary).pipe(
      catchError((error)=>{
        this._messageService.messageWaring('', error.error.message);      
        return of([]);
      })
    ).subscribe((resp:ResponseGlobalModel | never[])=>{
      if (Array.isArray(resp)) {
        this._messageService.modalMessage('',
          'Hubo un error al tratar de guardar los incentivos.',
          "error",
          this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
          
      }else{
        this._messageService.messageSaveMapping(this._translateService.instant('IncentivesForm.TabMonetaryValidationModalValidFileTitle'),"", "success" );  
        this.getAllIncentivesNonMonetarys()
        this.closeModal();
      }                
    });
  }

  formatearFecha(fecha: Date): string {
    const dia = fecha.getDate().toString().padStart(2, '0');
    const mes = (fecha.getMonth() + 1).toString().padStart(2, '0');
    const anio = fecha.getFullYear();
    return `${dia}/${mes}/${anio}`;
  }
  

  getAllIncentivesNonMonetarys(){
    this._incentivesService.getAllIncentivesNonMonetarys(this.idBusinessCountry).pipe(
      catchError((error)=>{
        this._messageService.messageWaring('', error.error.message);      
        return of([]);
      })
    ).subscribe((resp:ResponseGlobalModel | never[])=>{
      if (Array.isArray(resp)) {
        this._messageService.modalMessage('',
          'Hubo un error al tratar de obtener todos los incentivos.',
          "error",
          this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
      }else{                   
          resp.result.forEach((element:NotMonetaryIncentivesTableModel) => {
          element.dStartCampain = element.dStartCampain.replace("T00:00:00", '');
          element.dEndCampain= element.dEndCampain.replace("T00:00:00", '');
        });
        this.NotMonetaryIncentivesTable=resp.result                         
      }                
    });
  }

  editIncentiveNotMonetary(){
    if(!this.notMonetaryIncentiveModalForm.valid){
      return this.notMonetaryIncentiveModalForm.markAllAsTouched()      
    }

    const incentiveNotMonetaryEdited=this.setDataForForm();
    this._incentivesService.editIncentiveNotMonetary(incentiveNotMonetaryEdited).pipe(
      catchError((error)=>{
        this._messageService.messageWaring('', error.error.message);      
        return of([]);
      })
    ).subscribe((resp:ResponseGlobalModel | never[])=>{
      if (Array.isArray(resp)) {
        this._messageService.modalMessage('',
          'Hubo un error al tratar de guardar los incentivos.',
          "error",
          this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
      }else{
        this.getAllIncentivesNonMonetarys()
        this._messageService.messageSaveMapping(this._translateService.instant('IncentivesForm.TabMonetaryValidationModalValidFileTitle'),"Editado", "success" );  
        
        this.closeModal();
      }                
    });

  }


  deleteIncentiveNotMonetary(idIncentiveNonMonetary: any) {
    throw new Error('Method not implemented.');
  }


  openPDF(pdfUrl: string|undefined) {
    if(pdfUrl != undefined){
      const date = new Date().toISOString().split('T')[0];
      this.downloadFileByFilePath(pdfUrl, date );
    }
  }  
}
