<!-- [showSteps]="false" -->
<div class="cont-company-country-history">
  <app-company-country-history></app-company-country-history>
</div>
<app-wizard
  [steps]="steps"
  [currentStep]="currentStep"
  (currentStepChange)="currentStepChange($event)"
  (next)="next($event)"
  (back)="back($event)"
  [buttonNextDisabled]="buttonNextDisabled"
  [stepToCompare]="steps[currentStep]"
>
  <ng-container stepBody [ngSwitch]="currentStep">
    <div *ngSwitchCase="0">
      <app-edit-create-module
        (hasSubmodules)="hasSubmodules($event)"
        (objetModule)="getObjetModule($event)"
        (objetImageModule)="getObjetImageModule($event)"
        (isImagenEdit)="getIsImagenEdit($event)"
        [pkIIdMenu]="pkIIdMenu"
      ></app-edit-create-module>
    </div>
    <div *ngSwitchCase="1">
      <div *ngIf="steps.length === 2; else notSubmodule">
        <app-edit-create-stages
          [idMenuIn]="pkIIdMenu"
          [objetModule]="formModuleValue"
        ></app-edit-create-stages>
      </div>
      <ng-template #notSubmodule>
        <app-edit-create-submodule></app-edit-create-submodule>
      </ng-template>
    </div>
    <div *ngSwitchCase="2">
      <app-edit-create-stages
        [idMenuIn]="pkIIdMenu"
        [objetModule]="formModuleValue"
      ></app-edit-create-stages>
    </div>
    <div *ngSwitchDefault>output2</div>
  </ng-container>

  <ng-container customButtonInit>
    <button mat-raised-button (click)="goToBack()">
      <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
      {{ "ModulesSetting.Steps.GoBack" | translate }}
    </button>
  </ng-container>

  <ng-container customButton *ngIf="currentStep === 0">
    <button
      *ngIf="pkIIdMenu === 0"
      mat-raised-button
      color="primary"
      (click)="saveImage()"
      [disabled]="buttonNextDisabled"
    >
      <mat-icon fontIcon="save" iconPositionStart></mat-icon>
      {{ "ModulesSetting.Modules.SaveChanges" | translate }}
    </button>
    <button
      *ngIf="pkIIdMenu > 0"
      mat-raised-button
      color="primary"
      (click)="editImage()"
      [disabled]="buttonNextDisabled"
    >
      <mat-icon fontIcon="save" iconPositionStart></mat-icon>
      {{ "ModulesSetting.Modules.Update" | translate }}
    </button>
  </ng-container>
</app-wizard>
