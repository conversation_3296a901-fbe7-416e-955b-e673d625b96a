.mat-mdc-card-header {
  background-color: #F0F0F0;
}

.label-client {
  font-weight: 500;
  color: gray;
  letter-spacing: 1.1px;
}

.text-client {
  letter-spacing: 1.3px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex-grow: 1;
}

.w-130 {
  width: 130%;
}

.icon::before {
  content: "\e2c4"; /* Código Unicode del icono "get_app" */
  margin-right: 0.5em; /* Espacio entre el icono y el texto */
}


.filter-centered-button {
  margin-top: 15px;
  text-align: center;
}

