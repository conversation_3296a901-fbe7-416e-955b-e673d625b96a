//Modules
import { NgModule, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule,
  FormControl,
} from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { Subscription, catchError, of } from 'rxjs';

//Services
import { ErrorHandlingService } from 'src/app/shared/services/error/errorHandlingService';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { BusinessService } from 'src/app/shared/services/business/business.service';

//Components
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';

//Models
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { EmbeddedContent } from 'src/app/shared/models/embeddedResource';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { BodyTableModel } from 'src/app/shared/models/table/body-table.model';
import { MatDialog } from '@angular/material/dialog';
import { IconEventClickModel } from 'src/app/shared/models/table/icon-event-click.model';
import { MatCardModule } from '@angular/material/card';


@Component({
  selector: 'app-configuration-chatbot',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    TranslateModule,
    ChooseCountryAndCompanyComponent,
    MatSlideToggleModule,
    BreadcrumbComponent,
    MatButtonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTabsModule,
    TableComponent,
    Modal2Component,
    MatCardModule
  ],
  templateUrl: './configuration-chatbot.component.html',
  styleUrls: ['./configuration-chatbot.component.scss'],
})
export class ConfigurationChatbotComponent implements OnInit, OnDestroy {



  private _settingCountryAndCompanySubscription?: Subscription;

  idBusinessCountry: number = 0;
  idBusiness: number = 0;
  inicio: string = 'Inicio';
  colors: string = 'Configurar Chatbot';
  form: FormGroup = new FormGroup({});
  modalFormBI: FormGroup = new FormGroup({});
  isPortalClienteActive: boolean = false;

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.colors, link: '/dashboard/configuration-chatbot' },
  ];

    //Variables para modal
    @ViewChild('AddEmbeddedModal') AddEmbeddedModal?: TemplateRef<any>;
    modalTitle: string = 'PowerBI';
    
    //Variables relacionadas con la tabla.    
    tempEmbeddedList: EmbeddedContent[] = [];
    orderBy: string = 'Asc';
    idContent:number=0;
    resourceTypeName: string = "CHAT";
    dataPowerBITable: EmbeddedContent[] = [];
    estructPowerBITable: BodyTableModel[] = [     
      {
        columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.name'),
        columnValue: 'pkIIdContent', 
        hidenColumn:true       
      },
      {
        columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.name'),
        columnValue: 'vName',        
      },      
      {
        columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.modify'),
        columnValue: 'Modify',
        columnIcon: 'create',
      },
      {
        columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.delete'),
        columnValue: 'Delete',
        columnIcon: 'delete',
      }
    ];

    

  ngOnInit(): void {
    this.initForm();
    this.getBusinessCountry();    
    this.estructPowerBITable[1].columnLabel=this._translateService.instant('MetricsSettings.moduleSettings.name');
    this.estructPowerBITable[2].columnLabel = this._translateService.instant('MetricsSettings.moduleSettings.modify');
    this.estructPowerBITable[3].columnLabel = this._translateService.instant('MetricsSettings.moduleSettings.delete');    

  }

  onTabChange(event: MatTabChangeEvent) {  
    if (event.index === 0 ? this.resourceTypeName="CHAT" :this.resourceTypeName="PBI")
    console.log(this.resourceTypeName);
    
  }

  getEmbeddedPowerBI() {    
    this.getEmbeddedByBusinessCountryByResourceType('PBI');    
  }

    //Controlador de la tabla.
  controller(event: IconEventClickModel) {
      switch (event.column) {
        case 'Modify':
           this.modalTitle = this._translateService.instant('MetricsSettings.moduleSettings.updateScriptLabel');          

          this.openModal();          
          this.modalFormBI.patchValue({ vName:event.value.vName,tEmbedScript:event.value.tEmbedCode});                       
          this.idContent = event.value.pkIIdContent;
          console.log(this.idContent);
                                      
          break;
        case 'Delete':          
          this.deleteEmbeddedResource(event.value.pkIIdContent);
          break;
        default:
          break;
      }
  }

  constructor(
    private _fb: FormBuilder,
    private _errorHandlingService: ErrorHandlingService,
    private _parameterService: ParametersService,
    private _settingService: SettingService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _businessService: BusinessService,
    public modalDialog: MatDialog,
  ) {}

  getBusinessCountry() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.idBusiness = response.enterprise.pkIIdBusiness;
            this.getBusinessCountryByBusinessId();     
            this.getEmbeddedPowerBI();
          }
        }
      );
  }

  initForm() {
    this.form = this._fb.group({
      bActive: [false, [Validators.required]],
      tEmbedCode: ['', [Validators.required]],
      
    });
  }

  getErrorFieldName(){
    const field = this.modalFormBI.controls['vName'];
      if(field.hasError('required')){
        return this._translateService.instant('ThisFieldIsRequired' );    
      }
     return ''
    }

  getErrorFieldScript(){
  const field = this.modalFormBI.controls['tEmbedScript'];
    if(field.hasError('required')){
      return this._translateService.instant('ThisFieldIsRequired' );    
    }
   return ''
  }

  initFormPBIModal() {
    this.modalFormBI = this._fb.group({
      pkIIdContent:[0],
      vName: ['', [Validators.required]],
      tEmbedScript: new FormControl<string | null>(null, [Validators.required]),
    });
  }
 
  save() {
    this.form.markAsTouched();
  }

  getBusinessCountryByBusinessId() {
    this._businessService
      .getBusinessCountryByBusinessId(this.idBusiness)
      .pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this.isPortalClienteActive=resp.result[0].bEnableClientPortal
          }
        }
      });
  }

  getEmbeddedByBusinessCountry(resourceType: string) {
    this._parameterService
      .getEmbeddedByBusinessCountry(this.idBusinessCountry, resourceType, false)
      .pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error, false);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            console.log(resp.result)
            this.form.patchValue(resp.result);
          }
        }
      });
  }

  getEmbeddedByBusinessCountryByResourceType(resourceType: string) {
    this._parameterService.getEmbeddedByBusinessCountryByResourceType(this.idBusinessCountry, resourceType, false)
      .pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error, false);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {                                   
            this.dataPowerBITable=resp.result;  
            this.tempEmbeddedList = this.dataPowerBITable                    
          }
        }
      });
  }

  createEmbbededResourceByBusiness() {
    var resource: EmbeddedContent = {
      fkIIdBusinessCountry: this.idBusinessCountry,
      bActive: true,
      tEmbedCode: this.form.get('tEmbedCode')?.value,
      vResourceType: this.resourceTypeName,
      vName:  'NA',
    };
        
    this._parameterService
      .createEmbbededResourceByBusiness(resource)
      .pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant(resp.message),
              this._translateService.instant(resp.message)
            );
          }
        }
      });
  }

  ngOnDestroy(): void {}

  //Abre el modal para Añadir un PBI.
  openModal() {    
    this.initFormPBIModal();
     this.modalDialog.open(this.AddEmbeddedModal!, {
       width: '720px',
     });
  }

  //Función que dececta cada vez qaue se cierra el modal.
  closeModal(event: boolean) {
    this.idContent=0;
    this.modalTitle = this._translateService.instant('MetricsSettings.moduleSettings.subtitle' );  
  }

 addMetrics() {        
    if(!this.modalFormBI.valid){
      return          
    }
    else{
      var resource: EmbeddedContent = {
        fkIIdBusinessCountry: this.idBusinessCountry,
        vName:this.modalFormBI.get('vName')?.value,
        bActive: true,
        tEmbedCode: this.modalFormBI.get('tEmbedScript')?.value,
        vResourceType: this.resourceTypeName,
        
      };
      console.log( JSON.stringify(resource, null, 2));
      this._parameterService.createEmbbededResourceByBusiness(resource).pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error);
          return of([]);
        })
      ).subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant(resp.message),
              this._translateService.instant(resp.message)
            );
          }
        }
      });
    }
 
  }

  ediMetrics() {    
    if(!this.modalFormBI.valid){
      return          
    }else{
      var resource: EmbeddedContent = {
        pkIIdContent:this.idContent,
        fkIIdBusinessCountry: this.idBusinessCountry,
        vName:this.modalFormBI.get('vName')?.value,
        bActive: true,
        tEmbedCode: this.modalFormBI.get('tEmbedScript')?.value,
        vResourceType:  this.resourceTypeName
        
      };      
      this._parameterService.updateEmbbededResourceByIdContent(resource)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess(
            '',
            this._translateService.instant('Modified')
          );
          this.modalDialog.closeAll();
          this.getEmbeddedByBusinessCountryByResourceType('PBI');
        }
      });
    }
  }
  
  deleteEmbeddedResource(idContent: number) {
    this._messageService
      .messageConfirmationAndNegation(this._translateService.instant('FormConfiguration.ProgressBar.DeleteMessageConfirm' ),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._parameterService.deleteEmbbededResourceByIdContent(idContent)
            .pipe(
              catchError((error) => {
                if (error.error.error) {
                  this._messageService.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    error.error.message
                  );
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
              } else {
                this._messageService.messageSuccess(
                  '',
                  this._translateService.instant('FormConfiguration.ProgressBar.DeleteMessageSuccess')
                );
                this.getEmbeddedByBusinessCountryByResourceType('PBI');
              }
            });
        }
      });
  }

  order() {
    if (this.modalFormBI) {
      if (this.orderBy == 'Asc') {
        this.dataPowerBITable = this.dataPowerBITable.sort((a, b) => a.vName < b.vName ? 1 : -1)
        this.orderBy = 'Desc'
        this.dataPowerBITable = this.dataPowerBITable.filter(u => u.vName != '')
      }
      else {
        this.dataPowerBITable = this.dataPowerBITable.sort((a, b) => a.vName > b.vName ? 1 : -1)
        this.orderBy = 'Asc'
        this.dataPowerBITable = this.dataPowerBITable.filter(u => u.vName != '')
      }
    }
    else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('YouMustSelectACountryAndABusiness')
      );
    }
  }
  
  search(key: string) {
    if (key == null || key == '') {
      this.dataPowerBITable = this.tempEmbeddedList
    }
    else {
      const lowerCaseKey = key.toLowerCase();
      this.dataPowerBITable = this.tempEmbeddedList
      this.dataPowerBITable = this.dataPowerBITable.filter(u => u.vName.toLowerCase().includes(lowerCaseKey


      ));
    }
  }
}
