<div class="title m-0">
  <h2 class="h3 m-0">
    <img src="assets/img/layouts/config_ico.svg" alt="" />
    {{ "SettingColors.SetColorsByCompany" | translate }}
  </h2>
</div>
<app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

<div class="mt-5 mb-4">
  <h4>{{ "SettingColors.DefinitionOfColorsTile" | translate }}</h4>
</div>
<app-choose-country-and-company></app-choose-country-and-company>

<div class="mb-5">
  <mat-tab-group class="custom-tab-group" [selectedIndex]="defaultTabIndex" (selectedIndexChange)="onTabChanged($event)">
    <mat-tab label="{{ 'SettingColors.AdvisorPortal' | translate }}">
      <ng-container *ngIf="activeTab === 0">
        <app-setting-colors [actions]="actionAdvisor" [isAdvisor]="true"
                            (colors)="getAllColors($event,'Advisor')"
                            (actionFinished)="getActionFinished($event,'Advisor')"></app-setting-colors>
        <div *ngIf="showPreviewAdvisor">
          <app-preview [allColors]="allColorsAdvisor"
                       (emitAction)="getAction($event,'Advisor')"></app-preview>
        </div>
      </ng-container>
    </mat-tab>
    <mat-tab label="{{ 'SettingColors.ClientPortal' | translate }}">
      <ng-container *ngIf="activeTab === 1">
        <app-setting-colors [actions]="actionClient" [isAdvisor]="false"
                            (colors)="getAllColors($event,'Client')"
                            (actionFinished)="getActionFinished($event,'Client')"></app-setting-colors>
        <div *ngIf="showPreviewClient">
          <app-preview [allColors]="allColorsClient"
                       (emitAction)="getAction($event,'Client')"></app-preview>
        </div>
      </ng-container>
    </mat-tab>
  </mat-tab-group>
</div>
