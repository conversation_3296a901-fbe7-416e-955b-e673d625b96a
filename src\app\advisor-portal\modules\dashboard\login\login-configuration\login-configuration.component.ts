import { Component,<PERSON><PERSON><PERSON>roy,ChangeDetectorRef  } from '@angular/core';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { CommonModule } from '@angular/common';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { DomainsComponent } from 'src/app/shared/components/domains/domains.component';
import { CheckValidationComponent } from 'src/app/shared/components/check-validation/check-validation.component';
import { catchError, of, Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators,FormsModule } from '@angular/forms';
import { ConfigloginService } from 'src/app/shared/services/login/configlogin.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import {RegistrationfieldsComponent } from 'src/app/shared/components/registrationfields/registrationfields.component'
import { RoleService } from 'src/app/shared/services/role/role.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-login-configuration',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TranslateModule,
    MatCheckboxModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReactiveFormsModule,
    DomainsComponent,
    CheckValidationComponent,
    RegistrationfieldsComponent,
    MatButtonModule

  ],
  templateUrl: './login-configuration.component.html',
  styleUrls: ['./login-configuration.component.scss']
})
export class LoginConfigurationComponent implements OnDestroy{

  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessByCountry: number = 0;
  formValid: boolean = false;
  permitirRegistro: boolean = false;
  pkLoginToModify: number = 0;
  idBusinessCountry: number = 0;
  userType: any[] = [];
  idUserType: number = 0;
  formLogin: FormGroup;
  constructor(
    private _router: Router,
    private _settingService: SettingService,
    private _fb: FormBuilder,
    private _tokenService: ConfigloginService,
    private _messageService: MessageService,
    public _translateService: TranslateService,
    private _roleService: RoleService,
    private cdr: ChangeDetectorRef

  ) {
    this.formLogin = this._fb.group({
      iTokenDuration: [null],
      iNumberBlock: [null],
      iTimeBlock: [null],
      fkIIdBusinessByCountry: [null],
      bAllow: [false],
      fkIIDRol: [null]
    });
  }
  validForm(event: boolean) {
    this.formValid = event;
    console.log("this.formValid", this.formValid)
  }
  ngOnInit(): void {
    this.getAllTypeUser();
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            }
          }
        }
      );

  }
  getToken(idBusinessByCountry: number) {
    this._tokenService
      .getToken(idBusinessByCountry)
      .subscribe({
        next: (resp) => {
  
          if (resp.result && resp.result.length > 0) {
            this.formLogin.patchValue({
              iTokenDuration: resp.result[0].iTokenDuration,
              iNumberBlock: resp.result[0].iNumberBlock,
              iTimeBlock: resp.result[0].iTimeBlock,
              bAllow: resp.result[0].bActive,
            });
          } else {
            this.formLogin.reset({
              iTokenDuration: null,
              iNumberBlock: null,
              iTimeBlock: null,
              bAllow: false,
              fkIIdBusinessByCountry: this.formLogin.get('fkIIdBusinessByCountry')?.value,
              fkIIDRol: this.formLogin.get('fkIIDRol')?.value
            });
          }
        },
        error: (error) => {
          console.error('Error en la llamada al servicio de token:', error);
          this.formLogin.reset({
            iTokenDuration: null,
            iNumberBlock: null,
            iTimeBlock: null,
            bAllow: false,
            fkIIdBusinessByCountry: this.formLogin.get('fkIIdBusinessByCountry')?.value,
            fkIIDRol: this.formLogin.get('fkIIDRol')?.value
          });
        },
        complete: () => {
          console.log('Llamada al servicio de token completada.');
        }
      });
  }
  
  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessByCountry =
                response.enterprise.pkIIdBusinessByCountry;
            }
          }
        }
      );
  }
  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
  saveLogin(){
    this.formLogin.value.fkIIdBusinessByCountry = this.idBusinessCountry;
    this.formLogin.value.fkIIDRol = this.idUserType;
    console.log("this.formLogin.value", this.formLogin.value)
    if (this.formLogin.invalid)
      return this.formLogin.markAllAsTouched()
    this._tokenService.createToken({
        ...this.formLogin.value
      }).subscribe({
        next: (response) => {
          if (!response.error) {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.QuestionCreatedSucessfully')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
  }
  getAllTypeUser() {
    this._roleService
      .getAllTypeUser()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.userType = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.userType = resp.result;
          }
        }
      });
  }
  userTypeChange(evt: any) {
    this.idUserType = evt.value;
    this.getToken(this.idBusinessCountry);
  }
  onPermitirRegistroChange(event: MatCheckboxChange) {
    this.permitirRegistro = event.checked;
  }
}
