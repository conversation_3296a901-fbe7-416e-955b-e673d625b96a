import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DetailsPolicyComponent } from '../../shared/components/details-policy/details-policy.component';
import { HistoryTableComponent } from '../../shared/components/history-table/history-table.component';
import { RiskTableComponent } from '../../shared/components/risk-table/risk-table.component';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { catchError, of } from 'rxjs';
import { BodyTableModel } from 'src/app/shared/models/table';
import { PolicyTableService } from '../../shared/service/policy-table.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-page-see-detail-movement-history-colective-individual-policy',
  standalone: true,
  imports: [
    CommonModule,
    DetailsPolicyComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    RiskTableComponent,
  ],
  templateUrl:
    './page-see-detail-movement-history-colective-individual-policy.component.html',
  styleUrls: [
    './page-see-detail-movement-history-colective-individual-policy.component.scss',
  ],
})
export class PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent
  implements OnInit
{
  //Variables relacionadas con la póliza.
  @Input() idHistory: number = 0;
  @Input() typeMovement: string = '';
  @Input() idPolicyType: number = 0;
  idWtw: number = 0;
  idParent: number = 0;
  idPolicyRisk: number = 0;
  idFilePolicy: number = 0;
  policyNumber: string = '';
  policyStatus: string = '';
  detailsPolicyData: any[] = [];
  detailsPreviousPolicyData: any[] = [];
  isRenew: boolean = true;
  previousPolicyNumber: string = '';
  previousPolicyStatus: string = '';

  estructTableRisk: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Nombre asegurado'),
      columnValue: 'nameCustomer',
    },
    {
      columnLabel: this._translateService.instant('Tipo de documento'),
      columnValue: 'typeDocument',
    },
    {
      columnLabel: this._translateService.instant('Número de documento'),
      columnValue: 'numberDocument',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'state',
      functionValue: (item: any) =>
        this._policyTableService.changeRiskStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Documentos'),
      columnValue: 'documents',
      columnIcon: 'download',
    },
  ];

  constructor(
    private _transactionService: TransactionService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _policyTableService: PolicyTableService,
    private _utilsSvc: UtilsService
  ) {}

  ngOnInit(): void {
    this.getPolicyByIdWithProduct();
  }

  //Obtiene el detalle de una póliza.
  getPolicyByIdWithProduct() {
    this._transactionService
      .getPolicyByIdWithProduct(0, this.idPolicyType, this.idHistory)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.detailsPolicyData = this.transformData(resp.result);

          if (resp.result.policy) {
            this.policyNumber = resp.result.policy.policyNumber;
            this.idWtw = resp.result.policy.idwtw;
            this.policyStatus = resp.result.policy.statePolicy;
          }

          if (resp.result.previousPolicy){
            this.previousPolicyNumber = resp.result.previousPolicy.policyNumber;
            this.previousPolicyStatus = resp.result.previousPolicy.statePolicy;
          }
        }
      });
  }

  //Transforma la información recibida a un estadarizada para que ´pueda mostarse el detalle de cualquier tipo de póliza.
  transformData(dataPolicy: any): any[] {
    const result: any[] = [];

    //Información de la póliza individual a excluir.
    const excludePolicyValues = [
      'policyNumber',
      'statePolicy',
      'idwtw',
      'dDisbursementDate',
      'dDueDate',
      'idPolicyrisk',
      'idParent',
      'endorment',
      'vPaymentFrequency',
      'iPaymentsNumber',
      'iInsuredValue',
      'creditNumber',
      'iPremium',
      'idFilePolicy',
      'fieldKey',
      'fieldName',
      'idHistoryForm'
    ];

    // Transformar la sección de `policy`
    if (dataPolicy.policy) {
      const policySection = {
        policy: [
          {
            nameSection: this._translateService.instant('Datos póliza'),
            fields: Object.keys(dataPolicy.policy)
              .filter((key) => !excludePolicyValues.includes(key)) // Filtrar los campos
              .map((key) => {
                let value = dataPolicy.policy[key];
                const regex = /^\d{4}-\d{2}-\d{2}$/;
                if(regex.test(value)){
                  value = this._utilsSvc.formatDate(value, 'DD-MM-YYYY');
                }
                return {
                  name: key,
                  value: value,
                };
              }),
          },
        ],
      };

      result.push(policySection);
    }

    // Transformar la sección de `previousPolicy`
    if (dataPolicy.previousPolicy) {
      this.isRenew = true;
      let includePreviewPolicyValues = [
        'startValidity',
        'endValidity',
        'insurance',
        'typePolicy',
      ];
      const previousPolicySection = {
        policy: [
          {
            nameSection: this._translateService.instant('Policy.PolicyData'),
            fields: Object.keys(dataPolicy.previousPolicy)
              .filter((key) => includePreviewPolicyValues.includes(key)) // Filtrar los campos
              .map((key) => {
                let value = dataPolicy.previousPolicy[key];
                const regex = /^\d{4}-\d{2}-\d{2}$/;
                if(regex.test(value)){
                  value = this._utilsSvc.formatDate(value, 'DD-MM-YYYY');
                }
                return {
                  name: key,
                  value: value,
                };
              }),
          },
        ],
      };

      this.detailsPreviousPolicyData.push(previousPolicySection);
    }

    // Transformar la sección de `takers`
    if (dataPolicy.takers && dataPolicy.takers.length > 0) {
      const takersSection = {
        takers: dataPolicy.takers.map((taker: any) => ({
          nameSection: this._translateService.instant('Datos tomador'),
          fields: taker.fields.map((field: any) => ({
            name: field.name,
            value: field.value,
          })),
        })),
      };
      result.push(takersSection);
    }

    // Transformar la sección de `insurances`
    if (dataPolicy.insurances && dataPolicy.insurances.length > 0) {
      const insurancesSection = {
        insurances: dataPolicy.insurances.map((insurance: any) => ({
          nameSection: this._translateService.instant('Datos asegurado'),
          fields: insurance.fields.map((field: any) => ({
            name: field.name,
            value: field.value,
          })),
        })),
      };
      result.push(insurancesSection);
    }

    // Transformar la sección de `others`
    if (dataPolicy.others && dataPolicy.others.length > 0) {
      const othersSection = {
        others: dataPolicy.others.map((other: any) => ({
          nameSection: other.nameSection,
          fields: other.fields.map((field: any) => ({
            name: this.checkAndModifyKeyName(field.name),
            value: field.value,
          })),
        })),
      };
      result.push(othersSection);
    }

    return result;
  }

  checkAndModifyKeyName(keyName: string): string {
    const keysToModify: string[] = ['Plan']
    return keysToModify.includes(keyName) ? keyName + ' ' : keyName;
  }
}
