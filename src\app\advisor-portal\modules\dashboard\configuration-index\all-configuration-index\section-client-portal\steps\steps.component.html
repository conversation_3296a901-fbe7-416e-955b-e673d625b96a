<div class="col-12 col-md-12 mb-3 mt-5">
    <h2>{{ "SectionClientPortal.TitleSteps" | translate }}</h2>
    <p>{{ "SectionClientPortal.TextSteps" | translate }}</p>
    <div class="row">
      <div class="col-4" *ngIf="listSteps.length > 0">
        <div cdkDropList
             class="listdrop mt-20"
             (cdkDropListDropped)="dropOrderIndex($event)">
          <div class="style-box"
               *ngFor="let item of listSteps; index as i"
               cdkDrag>
            <div class="w-10 drag-icon">
              <i class="material-icons" cdkDragHandle>reorder</i>
            </div>
            <div class="w-100 drag-content">
              {{ item.vName }}
            </div>
            <div class="w-10 delete-icon">
              <button mat-icon-button (click)="deleteStep(i)">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
            <div class="w-10 delete-icon">
              <button (click)="openModal(item)" mat-icon-button>
                <i class="material-icons">create</i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-4">
        <button type="button" mat-raised-button color="primary" (click)="updateSteps()">
          {{ "Save" | translate }}
        </button>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col">
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="openModal(null)"
      >
        {{ "SectionClientPortal.AddStep" | translate }}
      </button>
    </div>
  </div>

  <ng-template #createEditStep>
    <app-modal2 [titleModal]="(isCreate ? 'SectionClientPortal.CreateStepTitle' : 'SectionClientPortal.UpdateStepTitle') | translate">
      <ng-container body>
        <form [formGroup]="formStep">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'SectionClientPortal.StepTitle' | translate}}
            </mat-label>
            <input matInput formControlName="vName" PreventionSqlInjector/>
            <mat-error *ngIf="utilsSvc.isControlHasError(formStep, 'vName', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'SectionClientPortal.StepText' | translate}}
            </mat-label>
            <textarea matInput formControlName="vText" PreventionSqlInjector cdkTextareaAutosize cdkAutosizeMinRows="3" cdkAutosizeMaxRows="10"></textarea>
            <mat-error *ngIf="utilsSvc.isControlHasError(formStep, 'vText', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
          <app-generic-image-picker
            class="col-12 col-md-4"
            [title]="'SectionClientPortal.StepImageTitle'| translate"
            [description]="'SectionClientPortal.StepImageText'| translate"
            (changeFile)="changeImage($event)"
            [imageSrc]="imageSrc">
        </app-generic-image-picker>
  
        </form>
      </ng-container>
      <ng-container customButtonRight>
        <div class="modal-footer">
          <button mat-raised-button color="primary" type="button" class="" (click)="saveStep()">
            {{ "Save" | translate }}
          </button>
        </div>
      </ng-container>
    </app-modal2>
  </ng-template>