<app-choose-country-and-company (valueForm)="validForm($event)">
</app-choose-country-and-company>
<!-- datatable roles -->
<div class="row mt-2">
  <div class="row mb-2">
    <h4 class="col-md-12">
      {{ "Role.ConfiguredRoles" | translate }}
    </h4>
  </div>
  <app-table
    [displayedColumns]="estructTableRoles"
    [data]="dataTableRoles"
    (iconClick)="controller($event)"
  ></app-table>
</div>
<!-- end datatable roles -->

<!-- botones añadir rol y replicar rol -->
<div class="d-flex aling-items-center justify-content-init">
  <button
    class="mx-3 mt-2 col-2"
    type="button"
    (click)="openEditNewRoleDialog()"
    mat-raised-button
    color="primary"
  >
    {{ "Role.CreateRole" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>

  <button
    class="mx-3 mt-2 col-2"
    type="button"
    (click)="openReplicateDialog()"
    mat-raised-button
    color="primary"
  >
    {{ "Role.ReplicateRole" | translate }}
    <mat-icon iconPositionEnd fontIcon="flip_to_front"></mat-icon>
  </button>
  <div class="mt-3">
    <mat-icon class="click" matTooltipPosition="right" matTooltip=" {{'Tooltips.ReplicateRole' | translate}}">help_outline</mat-icon>
  </div>
</div>
<!-- end botones añadir rol y replicar rol -->

<!-- modal duplicar rol -->
<ng-template #replicateRoleModal>
  <app-modal [titleModal]="'Role.ReplicateRole' | translate">
    <app-replicate-role (submitOut)="getSubmitReplicateData($event)">
    </app-replicate-role>
  </app-modal>
</ng-template>
<!-- end modal duplicar rol -->

<!-- modal crear editar rol -->
<ng-template #editNewRoleModal>
  <app-modal [titleModal]="tittleModalText">
    <app-edit-new-role
      (submitOut)="getSubmitEditNewData($event)"
      [data]="dataEditNewRole"
    ></app-edit-new-role>
  </app-modal>
</ng-template>
<!-- end modal crear editar rol -->
