<div class="cont-company-country-history">
    <app-forms-configuration-history 
        [arrayIn]="arrayCompanyHistoryOut"
    ></app-forms-configuration-history>
</div>

<!-- configuracion etapas -->
<div class="row">
    <form [formGroup]="form"> 
        <!-- slide estado etapa -->
        <div class="row mb-3">
            <mat-slide-toggle
                [disabled]="stageAlreadyCreated && idStageIn == 0"
                class="w-100"
                formControlName="bActive">  
                {{ 'Stage.ActiveStage' | translate }}
            </mat-slide-toggle>
        </div>

        <!-- nombre etapa -->
        <div class="row mb-2">
            <h6> {{ 'Stage.StageName' | translate }} </h6>
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ 'Stage.StageName' | translate }} 
                </mat-label>
                <input
                    (disabled)="!stageAlreadyCreated"
                    matInput
                    formControlName="vNameStage"
                    required
                    type="text"
                    PreventionSqlInjector
                />
                <mat-error
                    *ngIf="_utilsService.isControlHasError(form, 'vNameStage', 'required')"
                >
                    {{ 'ThisFieldIsRequired' | translate }}
                </mat-error>
            </mat-form-field>
        </div>       

        <!-- boton guardar etapa -->
        <div *ngIf="!stageAlreadyCreated || idStageIn != 0" class="row mt-2 mb-2">
            <button
                class="ml-1 w-auto"
                type="button"
                color="primary"
                (click)="saveStageButtonClick()"
                mat-raised-button
            >
                {{'Stage.SaveStage' | translate }} 
                <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
            </button>
        </div>
    </form>
</div>

<!-- dependencias -->
<div class="row">
    <div class="row mt-2 mb-2">
        <h4 class="col-md-12">
            {{ 'Stage.Dependencies' | translate }}
            <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.DependenciesTitle' | translate }}">help_outline</mat-icon>
        </h4>
    </div>    
    <!-- slide etapa dependiente -->
    <div class="row mb-3">
        <mat-slide-toggle
            [disabled]="!stageAlreadyCreated"
            #dependentStage
            class="w-100"
            (change)="dependentStageChange(dependentStage.checked)"
            [checked]="isDependentStage"
        >
            {{ 'Stage.DependentStage' | translate }}
        </mat-slide-toggle>
    </div>
    <!-- seleccionar etapa padre y botón añadir -->
    <div *ngIf="isDependentStage" class="row mb-2">
        <div class="row">
            <form [formGroup]="dependenciesForm">
                <div class="row">
                    <h6> {{ 'Stage.SelectStage' | translate }} </h6>
                    <mat-form-field appearance="outline" class="w-100">
                        <mat-label>
                            {{ 'Stage.SelectStage' | translate }} 
                        </mat-label>
                        <mat-select
                            formControlName="fkIIdStageParent"
                            multiple
                         >
                            <mat-option #dependecyOption
                                *ngFor="let item of stageList" 
                                [value]="item.pkIIdStage"
                                (onSelectionChange)="selectDependenciesChange(item.pkIIdStage, dependecyOption.selected)"
                            >
                            {{ item.vNameStage }}
                            </mat-option>
                          </mat-select>
                    </mat-form-field>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- estados -->
<div class="row">
    <div class="row mt-2 mb-2">
        <h4 class="col-md-12">
            {{ 'Stage.StageStatus' | translate }}
            <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.DependentStage' | translate }}">help_outline</mat-icon>
        </h4>
    </div>
    
    <!-- slide estados multiples -->
    <div class="row mb-3">
        <mat-slide-toggle            
            [disabled]="multipleStatatesBlocked"
            #multipleStatus
            class="w-100"
            (change)="multipleStatusChange(multipleStatus.checked)"
            [checked]="form.value.bMultipleStates"
        >
            {{ 'Stage.MultipleStatus' | translate }}
        </mat-slide-toggle>
    </div>

    <!-- dataTable estados y botón añadir -->
    <div class="row mb-2">
        
        <!-- datatable estados -->
        <div class="row mt-2 mb-2">
            <app-table
                [displayedColumns]="estructTableStageByState"
                [data]="dataTableStageByState"
                (iconClick)="controller($event)"
            ></app-table>
        </div>

        <!-- botón añadir estado -->
        <div *ngIf="!allStatusCreated" class="row md-2">
            <button
                class="ml-1 w-auto"
                type="button"
                color="primary" 
                (click)="openEditNewStatusDialog()"
                mat-raised-button
            >
                {{'Add' | translate }} 
                <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
            </button>
        </div>
    </div>
</div>

<!-- modal crear/editar estado -->
<ng-template #editNewStatusModal>
    <app-modal2 
        [titleModal]="tittleModalText"
        [showSaveButton]="idStageByState != 0"
        [saveButtonText]=" 'SaveChanges' | translate"
        (saveModal)="updateStageByState()"
    >
        <ng-container body>
            <app-edit-new-stage-by-state
                [idStageIn]="idStageOut"
                [stageByStateModelIn]="stageByStateModelOut"
                [idisMultipleStateIn]="isMultipleStatus"
                (resultOut)="getStageByStateFormResult($event)"
            ></app-edit-new-stage-by-state>
        </ng-container>
    </app-modal2>
</ng-template>
