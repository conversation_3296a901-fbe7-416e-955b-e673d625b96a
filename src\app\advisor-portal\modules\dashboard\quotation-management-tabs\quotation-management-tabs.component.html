
<!-- se agrega cabecera en este componente de tabs y se maneja dinamicamente en quotation -->
<div class="col-md-12">
  <app-breadcrumb [breadcrumbSections]="sections">
  </app-breadcrumb>

  <div class="cont-go-to-list" *ngIf="!isClient">
    <button mat-button type="button" (click)="goBack()" class="but-trans">
      <mat-icon fontIcon="arrow_back"></mat-icon>
      <span>{{ "MyQuotation.StepData.GoToList" | translate }}</span>
    </button>
  </div>

</div>

<mat-tab-group>
  <mat-tab label="{{ 'MyQuotation.CustomerManagement.Quotation' | translate }}"> <!-- Cotización -->
    <div class="tab-content">
    <ng-container *ngIf="isClient; else advisorTab">
      <!-- No se llama a see por que alla hacen esto redireccionar a quotation con true pero con la marca de cliente y aca ya lo hacemos... -->
      <!-- <app-see-quotation [idProductInput]="idProduct"></app-see-quotation> -->
      <app-quotation [isCustomerPortal]="true" [idProductInput]="idProduct" [hideTabs]="true"></app-quotation>
    </ng-container>
    <ng-template #advisorTab>
        <app-quotation [idProductInput]="idProduct" [hideTabs]="true"></app-quotation>
    </ng-template>
     </div>
  </mat-tab>
  <mat-tab label="{{ 'MyQuotation.CustomerManagement.CustomerManagement' | translate }}" *ngIf="!isClient"><!-- Gestión de cliente -->
    <div class="tab-content">
        <app-client-management></app-client-management>
    </div>
  </mat-tab>
</mat-tab-group>
