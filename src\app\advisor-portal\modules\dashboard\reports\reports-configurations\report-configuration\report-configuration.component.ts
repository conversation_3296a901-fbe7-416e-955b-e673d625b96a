import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Observable, Subscription, catchError, finalize, map, of } from 'rxjs';

import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableDataSource } from '@angular/material/table';
import { firstValueFrom } from 'rxjs';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PorductListModel } from 'src/app/shared/models/product';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { RolesListModel } from 'src/app/shared/models/role';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';

import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { debounceTime } from 'rxjs';
import { TableDragComponent } from 'src/app/shared/components/table-drag/table-drag.component';
import { GroupModel } from 'src/app/shared/models/groups';
import { StageModel } from 'src/app/shared/models/module';
import {
  INewReportProcesoModel,
  INewReportProductoModel,
  INewReportStageModel,
  INewReportState,
  INewReportStateModel,
} from 'src/app/shared/models/reports';
import {
  IReportGenerated2,
  IReportGeneratedComplete2,
} from 'src/app/shared/models/reports/new-report-report-generate.model';
import { UserListModel, UserModel } from 'src/app/shared/models/user';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { GroupsService } from 'src/app/shared/services/groups/groups.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { ReportService } from 'src/app/shared/services/reports/report.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { MatRadioModule } from '@angular/material/radio';
import { PorductModulesModel } from 'src/app/shared/models/product/product-modules.model';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { FieldTypeModel } from 'src/app/shared/models/field/field-type.model';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import { ReportListModel } from 'src/app/shared/models/reports/report-list.model';
import { ProductByInsurerCompanyModel } from 'src/app/shared/models/product/product.model';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { REPORTS_FILTERS_DATE_MODULE, REPORTS_FILTERS_DATE_POLICY, REPORTS_FILTERS_DATE_PRODUCT, REPORTS_FILTERS_SELECT_POLICY } from 'src/app/shared/services/constants';

@Component({
  selector: 'app-report-configuration',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    TableComponent,
    MatInputModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatSelectModule,
    TableDragComponent,
    MatIconModule,
    MatButtonModule,
    MatRadioModule
  ],
  templateUrl: './report-configuration.component.html',
  styleUrls: ['./report-configuration.component.scss'],
})
export class ReportConfigurationComponent implements OnInit {

  @Input()
  tipoReporte:number = 0; // 0-normal , 1-poliza

  highlights = {
    stage: 'normal',
    product: 'normal',
    state: 'normal',
    field: 'normal',
  };

  currentReportId: string | undefined;
  currentModal: MatDialogRef<any> | null = null;
  currentState: string = '';

  dataStaticTableReportFieldStandard: any[] = [];
  dataTableReport: any[] = [];
  dataTableReportField: any[] = [];
  dataTableReportFieldStandard: any[] = [];
  dataTableFieldsStandar: any[] = [];
  dataTableReportFieldDinamic: any[] = [];
  roleList: RolesListModel[] = [];
  productList: PorductListModel[] = [];
  processList: any[] = [];
  products: any[] = [];
  stageList: StageModel[] = [];
  groupTable: GroupModel[] = [];
  statusListCopyParameters: any[] = [];
  tempUsersList: UserListModel[] = [];
  listTableRules: any[] = [];
  listReportObjetToSend: any[] = [];
  listSavedProducts: number[] = [];

  // Form group to manage form controls.
  fromConfiguration: FormGroup = new FormGroup({});
  productListSeletected: number[] = [];

  // Selector models for UI.
  selectedProcessId: number = 0;
  selectedProcessDescription!: string;
  selectedProductId: number = 0;
  selectedProductDescription!: string;
  selectedStageId: number = 0;
  selectedStageDescription!: string;
  selectedStateId: number = 0;
  selectedStateDescription!: string;
  selectedFieldId: number = 0;
  selectedFieldDescription!: string;

  // Lists for dropdowns in form.
  listProcess: INewReportProcesoModel[] = [];
  listProduct: INewReportProductoModel[] = [];
  listStage: INewReportStageModel[] = [];
  listState: INewReportStateModel[] = [];
  listField: INewReportState[] = [];

  isEditingReport: boolean = false;
  isAddorModyReport: boolean = false;
  isEditDetailReport: boolean = false;
  showDetailReport: boolean = false;

  bchekGroup: boolean = false;
  bcheckRole: boolean = false;
  bcheckUser: boolean = false;

  configuration: FormGroup;

  allReports?: Subscription;
  allReportsField1?: Subscription;
  allReportsField2?: Subscription;

  idReport!: string;
  idBusinessByCountry: number = 0;

  titleBtngenerateReport: string = 'Reports.NewReport.GenerateReport';

  // Localized strings for UI elements.
  inicio: string = this._translateService.instant('Inicio');
  reports: string = this._translateService.instant('Reports.Title');
  reportsDos: string = this._translateService.instant(
    'Reports.NewReport.Title'
  );

  // Navigation sections for breadcrumbs.
  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.reports, link: '/dashboard/reports' },
    { label: this.reportsDos, link: '/dashboard/reports/report-configuration' },
  ];



  forModules: boolean = true;
  allProductsList: PorductModulesModel[] = [];
  fieldSubs?: Subscription;
  isEditFilterReport: boolean = false;
  isEditFilterDateReport: boolean = false;
  listFieldFilter: INewReportState[] = [];
  listFieldFilterDate: INewReportState[] = [];
  filterTypeList: FieldTypeModel[] = [];
  filterTypeDateList: FieldTypeModel[] = [];
  dataTableReportFieldFilterDinamic: any[] = [];
  dataTableReportFieldFilterDateDinamic: any[] = [];
  insurers: InsuranceCompanyModel[] = [];
  productsByInsurerCompany: ProductByInsurerCompanyModel[] = [];

  constructor(
    public router: Router,
    public utilsSvc: UtilsService,
    public modalDialog: MatDialog,
    private _fb: FormBuilder,
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    private _reportSerives: ReportService,
    private _settingService: SettingService,
    private _roleService: RoleService,
    private _moduleService: ModuleService,
    private _groupsService: GroupsService,
    private _userService: UserService,
    private _parametersService: ParametersService,
    private _fieldServices: FieldService,
    private _spinnerService: SpinnerService,
    private _productService: ProductService,
    private _insuranceService: InsuranceService,
    private _policyService: PolicyService
  ) {
    this.configuration = this._fb.group({});

    this.initForm();

    this.fromConfiguration.get('fkIIdInsurance')?.valueChanges.subscribe({
      next: (data) => {
        if (data > 0)
          this._getProductsByInsurance(data)
      }
    })

    this.fromConfiguration.get('fkIIdProductUnique')?.valueChanges.subscribe({
      next: (data) => {
        //if (data != null && data != 0 && this.tipoReporte == 0) this.getFieldListField(data);
        if (data != null && data != 0) this.getFieldListField(data);
      },
    });

    this.fromConfiguration
      .get('fkIIdprocess')
      ?.valueChanges.subscribe((value) => {
        this.fromConfiguration.get('CheckDetaillReport')?.setValue(false);
        this.isEditDetailReport = false;
        if (value) {
          this.getProductsList(value, this.idBusinessByCountry);
          this.productListSeletected = [];
        }
      });

    this.fromConfiguration
      .get('fkIIdProduct')
      ?.valueChanges.subscribe((value) => {
        this.fromConfiguration.get('fkIdStage')?.setValue(null);
        this.fromConfiguration.get('fkIdStage')?.disable();

        this.fromConfiguration.get('fkIdStageFilter')?.setValue(null);
        this.fromConfiguration.get('fkIdStageFilter')?.disable();

        if (value) {
          if (value.length == 1) {
            this.showDetailReport = false;
            this.fromConfiguration.get('fkIdStage')?.enable();
            this.fromConfiguration.get('fkIdStageFilter')?.enable();
            this.fromConfiguration.get('fkIdState')?.enable();
            this.fromConfiguration.get('fkIdStateFilter')?.enable();
            this.getStageByIdProductModule();
          } else if (value.length > 1) {
            this.showDetailReport = true;
            this.fromConfiguration.get('CheckDetaillReport')?.setValue(false);

            let value1 =
              this.fromConfiguration.get('CheckDetaillReport')?.value;

            let detail: boolean = false;

            if (value1) {
              if (value.length > 1) {
                detail = false;
              } else if (value.length == 1) {
                detail = true;
              } else {
                detail = true;
              }
            }

            if (value1 == true && detail == true) {
              this.isEditDetailReport = true;
            } else if (value1 == false && detail == true) {
              this.isEditDetailReport = value1;
            } else {
              this.isEditDetailReport = false;
            }

            this.fromConfiguration.get('fkIdStage')?.enable();
            this.fromConfiguration.get('fkIdStageFilter')?.enable();
          }
        }
      });

    this.fromConfiguration.get('fkIdStage')?.valueChanges.subscribe((value) => {
      this.fromConfiguration.get('fkIdState')?.setValue(null);
      this.fromConfiguration.get('fkIdState')?.disable();
      if (value) {
        this.fromConfiguration.get('fkIdState')?.enable();
        this.currentState = '';
        if (value.vNameStage == 'Creación de cotización') {
          this.currentState = value.vNameStage;
          this.getStateByIdStage(value.pkIIdStage);
        } else {
          this.getStagByStateAssociateToFormByIdStage(value.pkIIdStage);
        }
      }
    });

    this.fromConfiguration.get('fkIdState')?.valueChanges.subscribe((value) => {
      if (value) {
        this._getFieldList(value.pkIIdStageByState)
      }
    });

    this.fromConfiguration
      .get('listGroupBussines')
      ?.valueChanges.subscribe((value) => {
        if (value) {
          if (value.length >= 1) {
            this.getRoleList(true);
          } else {
            this.getRoleList(false);
          }

          this.fromConfiguration.get('listUserBussiens')?.setValue(null);

          if (
            this.bcheckUser == true &&
            this.bcheckRole == false &&
            this.bchekGroup == false
          ) {
            this.fromConfiguration.get('listUserBussiens')?.enable();

            this.getUsersList(this.idBusinessByCountry, 0);
          } else if (
            this.bcheckUser == true &&
            this.bcheckRole == true &&
            this.bchekGroup == false
          ) {
            this.fromConfiguration.get('listUserBussiens')?.enable();

            this.getUsersList(this.idBusinessByCountry, 1);
          } else if (
            this.bcheckUser == true &&
            this.bcheckRole == false &&
            this.bchekGroup == true
          ) {
            this.fromConfiguration.get('listUserBussiens')?.enable();

            this.getUsersList(this.idBusinessByCountry, 2);
          } else if (
            this.bcheckUser == true &&
            this.bcheckRole == true &&
            this.bchekGroup == true
          ) {
            this.fromConfiguration.get('listUserBussiens')?.enable();

            this.getUsersList(this.idBusinessByCountry, 3);
          } else {
            this.fromConfiguration.get('listUserBussiens')?.setValue(null);
            this.fromConfiguration.get('listUserBussiens')?.disable();
          }
        }
      });

    this.fromConfiguration
      .get('CheckDetaillReport')
      ?.valueChanges.subscribe((value1) => {
        let product = this.fromConfiguration.get('fkIIdProduct')?.value;
        let productUnique = this.fromConfiguration.get('fkIIdProductUnique')?.value;

        let detail: boolean = false;

        if (value1) {
          if (this.dataTableReportFieldDinamic.length > 0) {
            detail = true;
          } else if (product != null && product.length == 1 && this.forModules) {
            detail = true;
          } else if (product != null && product.length > 1 && this.forModules) {
            detail = false;
          } else if (productUnique != null && productUnique > 0 && !this.forModules) { // new 
            detail = true;
          } else {
            detail = true;
          }
        }

        if (value1 == true && detail == true) {
          this.isEditDetailReport = true;
          this.getStageByIdProductModule();
        } else if (value1 == false && detail == true) {
          this.isEditDetailReport = false;
        } else {
          this.isEditDetailReport = false;
        }
      });

    /*this.fromConfiguration
      .get('CheckGroup')
      ?.valueChanges.subscribe((value) => {
        this.bchekGroup = value;
        if (!value) {
          this.fromConfiguration.get('listGroupBussines')?.setValue(null);
          this.fromConfiguration.get('listGroupBussines')?.disable();
          this.getRoleList(false);
        } else {
          this.fromConfiguration.get('listGroupBussines')?.enable();
        }
      });*/

    /*this.fromConfiguration.get('CheckRol')?.valueChanges.subscribe((value) => {
      this.bcheckRole = value;
      if (!value) {
        this.fromConfiguration.get('listRoleBusiness')?.setValue(null);
        this.fromConfiguration.get('listRoleBusiness')?.disable();
      } else {
        this.fromConfiguration.get('listRoleBusiness')?.enable();
      }
    });*/

    /*this.fromConfiguration.get('CheckUser')?.valueChanges.subscribe((value) => {
      this.bcheckUser = value;

      this.fromConfiguration.get('listUserBussiens')?.setValue(null);

      if (
        this.bcheckUser == true &&
        this.bcheckRole == false &&
        this.bchekGroup == false
      ) {
        this.fromConfiguration.get('listUserBussiens')?.enable();

        this.getUsersList(this.idBusinessByCountry, 0);
      } else if (
        this.bcheckUser == true &&
        this.bcheckRole == true &&
        this.bchekGroup == false
      ) {
        this.fromConfiguration.get('listUserBussiens')?.enable();

        this.getUsersList(this.idBusinessByCountry, 1);
      } else if (
        this.bcheckUser == true &&
        this.bcheckRole == false &&
        this.bchekGroup == true
      ) {
        this.fromConfiguration.get('listUserBussiens')?.enable();

        this.getUsersList(this.idBusinessByCountry, 2);
      } else if (
        this.bcheckUser == true &&
        this.bcheckRole == true &&
        this.bchekGroup == true
      ) {
        this.fromConfiguration.get('listUserBussiens')?.enable();

        this.getUsersList(this.idBusinessByCountry, 3);
      } else {
        this.fromConfiguration.get('listUserBussiens')?.setValue(null);
        this.fromConfiguration.get('listUserBussiens')?.disable();
      }
    });*/

    // Activar configuración de filtros
    this.fromConfiguration
      .get('CheckFilterReport')
      ?.valueChanges.subscribe((value1) => {
        let product = this.fromConfiguration.get('fkIIdProduct')?.value;
        let productUnique = this.fromConfiguration.get('fkIIdProductUnique')?.value;

        let detail: boolean = false;

        if (value1) {
          if (this.dataTableReportFieldDinamic.length > 0) {
            detail = true;
          } else if (product != null && product.length == 1 && this.forModules) {
            detail = true;
          } else if (product != null && product.length > 1 && this.forModules) {
            detail = false;
          } else if (productUnique != null && productUnique > 0 && !this.forModules) { // new 
            detail = true;
          } else {
            detail = true;
          }
        }

        if (value1 == true && detail == true) {
          this.isEditFilterReport = true;
        } else if (value1 == false && detail == true) {
          this.isEditFilterReport = false;
        } else {
          this.isEditFilterReport = false;
        }
        
      });

      // check Filtrar por fecha
      this.fromConfiguration
      .get('CheckFilterDateReport')
      ?.valueChanges.subscribe((value1) => {
        let product = this.fromConfiguration.get('fkIIdProduct')?.value;
        let productUnique = this.fromConfiguration.get('fkIIdProductUnique')?.value;

        let detail: boolean = false;

        if (value1) {
          if (this.dataTableReportFieldDinamic.length > 0) {
            detail = true;
          } else if (product != null && product.length == 1 && this.forModules) {
            detail = true;
          } else if (product != null && product.length > 1 && this.forModules) {
            detail = false;
          } else if (productUnique != null && productUnique > 0 && !this.forModules) { // new 
            detail = true;
          } else {
            detail = true;
          }
        }

        if (value1 == true && detail == true) {
          this.isEditFilterDateReport = true;
        } else if (value1 == false && detail == true) {
          this.isEditFilterDateReport = false;
        } else {
          this.isEditFilterDateReport = false;
        }

      });


      this.fromConfiguration.get('fkIdStageFilter')?.valueChanges.subscribe((value) => {
        this.fromConfiguration.get('fkIdStateFilter')?.setValue(null);
        this.fromConfiguration.get('fkIdStateFilter')?.disable();
        if (value) {
          this.fromConfiguration.get('fkIdStateFilter')?.enable();
          this.currentState = '';
          if (value.vNameStage == 'Creación de cotización') {
            this.currentState = value.vNameStage;
            this.getStateByIdStage(value.pkIIdStage);
          } else {
            this.getStagByStateAssociateToFormByIdStage(value.pkIIdStage);
          }
        }
      });
  
      this.fromConfiguration.get('fkIdStateFilter')?.valueChanges.subscribe((value) => {
        if (value) {

          if (this.currentState == 'Creación de cotización') {
            this.getFieldListFieldFilter(
              this.fromConfiguration.get('fkIIdProduct')?.value[0].fkIIdProduct
            );
          } else {
            this.getFieldListFilter(value);
          }
        }
      });


  }

  private _setFieldsFilters(){
    this.fromConfiguration.get('fkIdField')?.enable();
    this.fromConfiguration.get('fkIdFieldFilter')?.disable();
    this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
    this.listFieldFilter = this.listField.filter((item:INewReportState) => 
      this.filterTypeList.some(filterItem => filterItem.pkIIdFieldType === item.fkIIdFieldType)
    );

    // filtros campo fecha
    this.listFieldFilterDate = this.listField.filter((item:INewReportState) => 
      this.filterTypeDateList.some(filterItem => filterItem.pkIIdFieldType === item.fkIIdFieldType)
    );
    let fieldsFilters = []
    let fieldFiltersSelect: any[] = []
    if (this.tipoReporte == 0 && this.forModules)
      fieldsFilters = REPORTS_FILTERS_DATE_MODULE
    else if (this.tipoReporte == 0 && !this.forModules)
      fieldsFilters = REPORTS_FILTERS_DATE_PRODUCT
    else
    {
      fieldFiltersSelect = REPORTS_FILTERS_SELECT_POLICY
      fieldsFilters = REPORTS_FILTERS_DATE_POLICY
    }

    fieldsFilters.forEach(element => {
      this.listFieldFilterDate.push(element)
    });

    fieldFiltersSelect.forEach(element => {
      this.listFieldFilter.push(element)
    })

    if (this.listField.length != 0) {
      this.fromConfiguration.get('fkIdField')?.enable();
    } else {
      this.fromConfiguration.get('fkIdField')?.disable();
    }

    if (this.listFieldFilter.length != 0) {
      this.fromConfiguration.get('fkIdFieldFilter')?.enable();
    } else {
      this.fromConfiguration.get('fkIdFieldFilter')?.disable();
    }

    if (this.listFieldFilterDate.length != 0) {
      this.fromConfiguration.get('fkIdFieldFilterDate')?.enable();
    } else {
      this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
    }
  }

  private _getFormById(id: number): void {
    this._fieldServices.getFieldsByForm(id)
      .subscribe((resp: ResponseGlobalModel | never[]) => {

        this.fromConfiguration.get('fkIdField')?.disable();
        if (Array.isArray(resp)) {
          this.listField = [];
          this.fromConfiguration.get('fkIdField')?.disable();
          this.toggleHighlight('field', false);
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
            this.fromConfiguration.get('fkIdField')?.disable();
            this.toggleHighlight('field', false);
          } else {
            this.toggleHighlight('field', true);
            this.fromConfiguration.get('fkIdField')?.enable();
            this.listField = resp.result;
            if (this.listField.length != 0) {
              this.fromConfiguration.get('fkIdField')?.enable();
            } else {
              this.fromConfiguration.get('fkIdField')?.disable();
            }

            this._setFieldsFilters()
          }
        }
      });
  }

  async _getStageByStateEmission(): Promise<number> {
    const idStage = this.fromConfiguration.get('fkIdStage')?.value.pkIIdStage;
    return new Promise((resolve) => {
      this._moduleService
        .getStageByStateById(idStage)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              let emission = resp.result.filter((item: { vCode: string; }) => item.vCode === 'EM-EM')
              resolve(emission[0].pkIIdStageByState)
            }
          }
        });
    });
    
  }

  private _getEmissionForm(): void {
    this._fieldServices
      .registerOrGetFormCompleteAutomatically(this.fromConfiguration.get('fkIIdProduct')?.value[0].fkIIdProduct, 'ISF')
      .subscribe(async (resp: ResponseGlobalModel) => {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.statusListCopyParameters.push({
            vState: 'Emisión',
            vCode: 'EM-EM',
            fkIdFormModule: resp.result.pkIIdForm,
            pkIIdStageByState: await this._getStageByStateEmission(),
          })
        }
      });
  }

  private _getProductsByInsurance(idInsuranceCompany: number){
    this._policyService.getProductsByIdInsuranceCompany(idInsuranceCompany, this.idBusinessByCountry).subscribe({
      next: (response => {
        this.productsByInsurerCompany = response.result
      })
    })
  }
  private _getQuoteFields(): void {
      this._fieldServices
        .getFormByProduct(this.fromConfiguration.get('fkIIdProduct')?.value[0].fkIIdProduct)
        .pipe(
          catchError((respError) => {
            if (respError.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                respError.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this._getFormById(resp.result.pkIIdForm)
            }
          }
        });
  }

  private _getFieldList(value: any): void {
    const process = this.fromConfiguration.get('fkIIdprocess')?.value
    const stage = this.fromConfiguration.get('fkIdStage')?.value
    const state = this.fromConfiguration.get('fkIdState')?.value
    if (process == 7 && stage.vNameStage === "Creación de cotización") 
      this._getQuoteFields()
    if (process == 7 && state.vCode === "EM-EM") 
      this._getFormById(state.fkIdFormModule)
    else 
      this.getFieldList(value);
    
  }

  async ngOnInit(): Promise<void> {
    await this.getDataSettingInit();
    this.getAllReportsById(this.idBusinessByCountry);
    this.getRoleList(false);
    this.getProcessList();
    this.getListGroupsByIdBusiness(this.idBusinessByCountry);
    this.getUsersList(this.idBusinessByCountry, 0);
    this.getAllProcess();

    this.getAllProducts(this.idBusinessByCountry);
    this.radioForChange(1);
    this.getFieldType();
    this.getInsurersGlobal(this.idBusinessByCountry);
    this.fromConfiguration.get('fkIIdProductInsurance')?.valueChanges.subscribe({
      next: (data => {
        if (data > 0)
          this._setFieldsPolicy(data, this.fromConfiguration.get('fkIIdInsurance')?.value)
      })
    })

    // this.GetFiedsStandars();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //Traducción de la tabla de lista de reportes.
      this.estructTable[0].columnLabel = this._translateService.instant(
        'Reports.NewReport.ReportName'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'Product.Description'
      );
      this.estructTable[2].columnLabel =
        this._translateService.instant('Modify');
      this.estructTable[3].columnLabel =
        this._translateService.instant('Delete');

      //Traducción de la tabla de campos estandares.
      this.estructTableField[0].columnLabel =
        this._translateService.instant('Product.Field');
      this.estructTableField[1].columnLabel =
        this._translateService.instant('Delete');
    });
    // Subscribes to language changes to update UI elements dynamically.
    this.subscribeToLangChange();
    this.updateTableColumns()
  }

  private _setFieldsPolicy(idProduct: number, idInsurer: number){
    this._fieldServices.getFieldPolicyByIdProduct(idProduct, idInsurer).subscribe({
      next: (response => {
        this.listField = response.result
        if (this.listField.length > 0) {
          this.fromConfiguration.get('fkIdField')?.enable();
        } else {
          this.fromConfiguration.get('fkIdField')?.disable();
        }
        this._setFieldsFilters()
      })
    })
  }

  /**
   * Initializes the form with necessary fields for report creation.
   * Dependent fields are initially disabled and will be enabled based on previous selections.
   */
  initForm() {
    this.fromConfiguration = new FormGroup({
      vName: new FormControl(null, Validators.required, [
        this.nameExistsValidator(),
      ]),
      vDescription: new FormControl(null, Validators.required),

      CheckGroup: new FormControl(null),
      CheckRol: new FormControl(null),
      CheckUser: new FormControl(null),
      CheckDetaillReport: new FormControl(null),
      fkIIdInsurance: new FormControl({}),
      fkIIdProductInsurance: new FormControl({}),
      listGroupBussines: new FormControl({
        value: [] as GroupModel[],
        disabled: true,
      }),
      listRoleBusiness: new FormControl({
        value: [] as RolesListModel[],
        disabled: true,
      }),
      listUserBussiens: new FormControl({
        value: [] as UserModel[],
        disabled: true,
      }),

      listProduct: new FormControl({
        value: [] as PorductListModel[],
        disabled: false,
      }),

      fkIIdprocess: new FormControl(null, Validators.required),
      fkIIdProduct: new FormControl({ value: null, disabled: false }),
      fkIIdProductUnique: new FormControl(null, Validators.required),
      fkIdStage: new FormControl(
        { value: null, disabled: true },
        Validators.required
      ),
      fkIdState: new FormControl(
        { value: null, disabled: true },
        Validators.required
      ),
      fkIdField: new FormControl(
        { value: null, disabled: true },
        Validators.required
      ),
      fkIdFieldStandar: new FormControl({ value: null, disabled: false }),

      CheckFilterReport: new FormControl(null),
      CheckFilterDateReport: new FormControl(null),
      CheckFilterRoleUserReport: new FormControl(null),
      fkIdStageFilter: new FormControl(
        { value: null, disabled: true },
        Validators.required
      ),
      fkIdStateFilter: new FormControl(
        { value: null, disabled: true },
        Validators.required
      ),

      fkIdFieldFilter: new FormControl(
        { value: null, disabled: true },
        Validators.required
      ),

      fkIdFieldFilterDate: new FormControl(
        { value: null, disabled: true },
        Validators.required
      ),

      bIsModule: new FormControl("1"),


    });

   
    this.onValuesChanges();
  }

  onValuesChanges() {
    this.fromConfiguration.get('fkIIdprocess')?.valueChanges.subscribe(() => {
      this.fromConfiguration.get('fkIdStage')?.disable();
      this.fromConfiguration.get('fkIdState')?.disable();
      this.fromConfiguration.get('fkIdField')?.disable();
    });
    this.fromConfiguration.get('fkIIdProduct')?.valueChanges.subscribe(() => {
      this.fromConfiguration.get('fkIdState')?.disable();
      this.fromConfiguration.get('fkIdField')?.disable();
    });
    this.fromConfiguration.get('fkIdStage')?.valueChanges.subscribe(() => {
      this.fromConfiguration.get('fkIdField')?.disable();
    });
    this.fromConfiguration.get('CheckFilterRoleUserReport')?.valueChanges.subscribe((value: boolean) => {
      if (!value)
      {
        this.fromConfiguration.get('CheckGroup')?.setValue(false)
        this.fromConfiguration.get('CheckRol')?.setValue(false)
        this.fromConfiguration.get('CheckUser')?.setValue(false)
      }
    })
  }

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'Reports.NewReport.ReportName'
      ),
      columnValue: 'vReportName',
    },
    {
      columnLabel: this._translateService.instant('Product.Description'),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

    //Función que pone estilos personalziados a los tipos de la póliza, según sea el caso.
  changeVisibilityIconValue(item: any) {
    if (item.bIsVisible)
      return 'visibility'
    else
      return 'visibility_off'
  }

  estructTableField: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Field'),
      columnValue: 'vFieldNameStandard',
    },
    {
      columnLabel: this._translateService.instant('Visibilidad'),
      columnValue: 'visibility',
      columnIcon: 'visibility',
      functionValue: (item: any) => this.changeVisibilityIconValue(item),
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  estructTableDrop: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('PlanPremium.NoRule'),
      columnValue: 'pkIIdFieldCalculated',
    },
    {
      columnLabel: this._translateService.instant('PlanPremium.Description'),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  estructNewReportsTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Field'),
      columnValue: 'vFieldNameStandard',
    },
    {
      columnLabel: this._translateService.instant('Etapa'),
      columnValue: 'vNameStage',
    },
    {
      columnLabel: this._translateService.instant('Estado'),
      columnValue: 'vState',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  /**
   * Sets up subscription to language changes.
   * Whenever a language change is detected, it updates the translations
   * and adjusts the form settings based on the selected language.
   */
  private subscribeToLangChange(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant('Inicio');
      this.reports = this._translateService.instant('Reports.Title');
      this.reportsDos = this._translateService.instant(
        'Reports.NewReport.Title'
      );
      this.updateTableColumns();
      this.sections[0].label = this.inicio;
      this.sections[1].label = this.reports;
      this.sections[2].label = this.reportsDos;
      this.fromConfiguration.get('fkIIdProduct')?.enable();
    });
  }

  nameExistsValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) {
        return of(null);
      }

      return this._reportSerives.getAllReports(this.idBusinessByCountry).pipe(
        debounceTime(12000), // Espera 6 segundos antes de emitir el valor
        map((response: any) => {
          const reports: any[] = response.result || [];

          let nameExists: boolean = false;

          if (this.isEditingReport) {
            const Exists = reports.some(
              (report: any) =>
                report.vReportName === control.value &&
                report.pkGIdReport !== this.currentReportId
            );

            const Exists2 = reports.some(
              (report: any) =>
                report.vReportName === control.value &&
                report.pkGIdReport === this.currentReportId
            );

            if (Exists) {
              nameExists = true;
            }

            if (Exists2) {
              nameExists = false;
            }
          } else {
            nameExists = reports.some(
              (report: any) => report.vReportName === control.value
            );
          }

          return nameExists ? { nameExists: true } : null;
        }),
        catchError(() => {
          console.error('Error al obtener los informes');
          return of(null);
        })
      );
    };
  }

  async getDataSettingInit() {
    let data = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = data.idBusinessByCountry;
  }

  controllerReportsGenerated(evt: IconEventClickModel) {
    switch (evt.column) {
      case 'modify':
        this.isEditingReport = true;
        this.currentReportId = evt.value.pkGIdReport;
        this.isAddorModyReport = true;

        if (evt.value.fkIIdGroup) {
          const groupValues = evt.value.fkIIdGroup.split(',').map(Number);
          this.fromConfiguration.get('CheckGroup')?.enable();
          this.fromConfiguration.get('CheckGroup')?.setValue(true);
          this.fromConfiguration.get('listGroupBussines')?.enable();
          this.fromConfiguration
            .get('listGroupBussines')
            ?.setValue(groupValues);
        }

        if (evt.value.fkIIdRoleBusiness) {
          const roleValues = evt.value.fkIIdRoleBusiness.split(',').map(Number);
          this.fromConfiguration.get('CheckRol')?.enable();
          this.fromConfiguration.get('CheckRol')?.setValue(true);
          this.fromConfiguration.get('listRoleBusiness')?.enable();
          this.fromConfiguration.get('listRoleBusiness')?.setValue(roleValues);
        }

        if (evt.value.fkIIdUser) {
          const userValues = evt.value.fkIIdUser.split(',').map(Number);
          this.fromConfiguration.get('CheckUser')?.enable();
          this.fromConfiguration.get('CheckUser')?.setValue(true);
          this.fromConfiguration.get('listUserBussiens')?.enable();
          this.fromConfiguration.get('listUserBussiens')?.setValue(userValues);
        }

        this.fromConfiguration.patchValue({
          vName: evt.value.vReportName,
          vDescription: evt.value.vDescription,
        });
        if (evt.value.fkIIdInsuranceCompany){
          this.fromConfiguration.patchValue({
            fkIIdInsurance: Number(evt.value.fkIIdInsuranceCompany),
            fkIIdProductInsurance: Number(evt.value.fkIIdProduct),

          })
        }

        this.getAllReportsFieldByIdReport(evt.value.pkGIdReport);

        break;
      case 'delete':
        this._msgSvc
          .messageConfirmationAndNegation(
            this._translateService.instant(
              'FormConfiguration.ProgressBar.DeleteMessageConfirm'
            ),
            '',
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this._reportSerives
                .deleteReportById(evt.value.pkGIdReport)
                .pipe(
                  catchError((error) => {
                    this._msgSvc.messageWaring(
                      this._translateService.instant('Warning'),
                      error.error.message
                    );
                    return of([]);
                  })
                )
                .subscribe((resp: ResponseGlobalModel | never[]) => {
                  if (Array.isArray(resp)) {
                  } else {
                    if (resp.error) {
                      this._msgSvc.messageError(
                        this._translateService.instant('ThereWasAError') +
                          resp.message
                      );
                    } else {
                      this._msgSvc.messageSuccess(
                        this._translateService.instant('Confirmed'),
                        this._translateService.instant(
                          'Reports.Utils.MsgDeleteReport'
                        )
                      );
                      this.getAllReportsById(this.idBusinessByCountry);
                    }
                  }
                });
            }
          });
        break;

      default:
        break;
    }
  }

  /**
   * Handles events triggered by icon clicks within the report table.
   * Specifically manages deletion of report entries.
   * @param event - The icon event click model containing event details.
   */
  controllerStandardFields(event: IconEventClickModel) {
    if (event.column == 'delete') {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('Delete'),
          '¿' +
            this._translateService.instant('Reports.Utils.QuestionDelete') +
            '?',
          'info',
          this._translateService.instant('Accept'),
          this._translateService.instant('Cancel')
        )
        .then((r) => {
          if (r) {
            this.deleteFieldStandard(event.value);
          }
        });
    }

    if (event.column == 'visibility') {
    
      const field = this.estructTableField.find(
        (item) => item.columnIcon === 'visibility'
      );
      if (field) {
        event.value.bIsVisible = false
        // Alternar el estado de visibilidad
        field.columnIcon = 'visibility_off';
      } else {
        const field2 = this.estructTableField.find(
          (item) => item.columnIcon === 'visibility_off'
        );
        if (field2) {
          event.value.bIsVisible = true
          field2.columnIcon = 'visibility';
        }
      }
    }

  }

  /**
   * Deletes a report entry from the list of reports to send.
   * Updates the order of the remaining reports to ensure they are sequential.
   * @param value - The report entry to be deleted.
   */
  deleteFieldsDinamic(value: IReportGenerated2) {
    this.dataTableReportFieldDinamic = this.dataTableReportFieldDinamic.filter(
      (r) => r.iOrder != value.iOrder
    );

    // Ordena el array por 'iOrder'
    this.dataTableReportFieldDinamic.sort((a, b) => a.iOrder - b.iOrder);

    // Reenumerar 'iOrder' secuencialmente
    this.dataTableReportFieldDinamic = this.dataTableReportFieldDinamic.map(
      (item, index) => ({
        ...item,
        iOrder: index + 1,
      })
    );
  }

  deleteFieldsFilterDinamic(value: IReportGenerated2) {
    this.dataTableReportFieldFilterDinamic = this.dataTableReportFieldFilterDinamic.filter(
      (r) => r.iOrder != value.iOrder
    );

    // Ordena el array por 'iOrder'
    this.dataTableReportFieldFilterDinamic.sort((a, b) => a.iOrder - b.iOrder);

    // Reenumerar 'iOrder' secuencialmente
    this.dataTableReportFieldFilterDinamic = this.dataTableReportFieldFilterDinamic.map(
      (item, index) => ({
        ...item,
        iOrder: index + 1,
      })
    );
  }

  deleteFieldsFilterDateDinamic(value: IReportGenerated2) {
    this.dataTableReportFieldFilterDateDinamic = this.dataTableReportFieldFilterDateDinamic.filter(
      (r) => r.iOrder != value.iOrder
    );

    // Ordena el array por 'iOrder'
    this.dataTableReportFieldFilterDateDinamic.sort((a, b) => a.iOrder - b.iOrder);

    // Reenumerar 'iOrder' secuencialmente
    this.dataTableReportFieldFilterDateDinamic = this.dataTableReportFieldFilterDateDinamic.map(
      (item, index) => ({
        ...item,
        iOrder: index + 1,
      })
    );
  }

  /**
   * Deletes a report entry from the list of reports to send.
   * Updates the order of the remaining reports to ensure they are sequential.
   * @param value - The report entry to be deleted.
   */
  deleteFieldStandard(value: any) {
    this.dataTableReportFieldStandard =
      this.dataTableReportFieldStandard.filter((r) => r.iOrder != value.iOrder);

    // Ordena el array por 'iOrder'
    this.dataTableReportFieldStandard.sort((a, b) => a.iOrder - b.iOrder);

    // Reenumerar 'iOrder' secuencialmente
    this.dataTableReportFieldStandard = this.dataTableReportFieldStandard.map(
      (item, index) => ({
        ...item,
        iOrder: index + 1,
      })
    );
  }

  getListGroupsByIdBusiness(idBusinessByCountry: number) {
    this._groupsService
      .getListGroupsByIdBusiness(idBusinessByCountry)
      .subscribe({
        next: (response) => {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.groupTable = response.result;
          }
        },
      });
  }

  /**
   * Handles events triggered by icon clicks within the report table.
   * Specifically manages deletion of report entries.
   * @param event - The icon event click model containing event details.
   */
  controllerFieldsDinamic(event: IconEventClickModel) {
    if (event.column == 'delete') {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('Delete'),
          '¿' +
            this._translateService.instant('Reports.Utils.QuestionDelete') +
            '?',
          'info',
          this._translateService.instant('Accept'),
          this._translateService.instant('Cancel')
        )
        .then((r) => {
          if (r) {
            this.deleteFieldsDinamic(event.value);
          }
        });
    }
  }

  controllerFieldsFilterDinamic(event: IconEventClickModel) {
    if (event.column == 'delete') {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('Delete'),
          '¿' +
            this._translateService.instant('Reports.Utils.QuestionDelete') +
            '?',
          'info',
          this._translateService.instant('Accept'),
          this._translateService.instant('Cancel')
        )
        .then((r) => {
          if (r) {
            this.deleteFieldsFilterDinamic(event.value);
          }
        });
    }
  }

  controllerFieldsFilterDateDinamic(event: IconEventClickModel) {
    if (event.column == 'delete') {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('Delete'),
          '¿' +
            this._translateService.instant('Reports.Utils.QuestionDelete') +
            '?',
          'info',
          this._translateService.instant('Accept'),
          this._translateService.instant('Cancel')
        )
        .then((r) => {
          if (r) {
            this.deleteFieldsFilterDateDinamic(event.value);
          }
        });
    }
  }

  open(component: string) {
    this.isAddorModyReport = true;
  }

  closeModal() {
    if (this.currentModal) {
      this.currentModal.close();
      this.currentModal = null; // Limpia la referencia después de cerrar el modal
    }
  }

  listPlanPremiumToUpdate(event: MatTableDataSource<any>, tipeOrigin: Boolean) {
    if (tipeOrigin) {
    } else {
      this.listTableRules = event.filteredData.slice();
    }
  }

  getRoleList(bDependiente: boolean) {
    if (bDependiente) {
      let dataFilterGroup = {
        listIdGroup:
          this.fromConfiguration.get('listGroupBussines')?.value || [],
      };
      this._groupsService
        .getRoleByIdGroup(dataFilterGroup)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.roleList = resp.result;
            }
          }
        });
    } else {
      this._roleService
        .getRole(this.idBusinessByCountry)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.roleList = resp.result;
            }
          }
        });
    }
  }

  //metodos get, post, put.. que consumen la api
  getUsersList(id: number, type: number) {
    if (type == 0) {
      this._userService
        .getUserList(id)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.tempUsersList = resp.result;
            }
          }
        });
    } else if (type == 1) {
      let listIdRole =
        this.fromConfiguration.get('listRoleBusiness')?.value.join('') || '';
      this._userService
        .getUserByIRoles(this.idBusinessByCountry, listIdRole)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.tempUsersList = resp.result;
            }
          }
        });
    } else if (type == 2) {
      let listIdGroup =
        this.fromConfiguration.get('listGroupBussines')?.value.join('') || '';

      this._userService
        .getUserByIdGroup(listIdGroup)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.tempUsersList = resp.result;
            }
          }
        });
    } else if (type == 3) {
      let listIdGroup =
        this.fromConfiguration.get('listGroupBussines')?.value.join(',') || '';
      let listIdRole =
        this.fromConfiguration.get('listRoleBusiness')?.value.join(',') || '';
      this._userService
        .getUserByIRolesAndGroups(
          this.idBusinessByCountry,
          listIdGroup.toString(),
          listIdRole.toString()
        )
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.tempUsersList = resp.result;
            }
          }
        });
    }
  }

  //Obtiene todos los procesos regsitrados en el sistema
  getProcessList() {
    this._roleService
      .getAllProcessByBusinessCountry(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.processList = resp.result;
          }
        }
      });
  }

  //obtiene los productos registrados por proceso y idBusinessCountry
  getProductsList(idProcess: number, idFkBussinesByCountry: number) {
    this._roleService
      .getProductsByIdProcess(idProcess, idFkBussinesByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant('TaskTray.Messages.NoProducts')
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.listProduct = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageInfo(
              this._translateService.instant('ThereWasAError'),
              resp.message
            );
          } else {
            this.listProduct = resp.result;
            if (this.searchProductByIdList(this.listSavedProducts)) {
              this.fromConfiguration
                .get('fkIIdProduct')
                ?.setValue(this.searchProductByIdList(this.listSavedProducts));
            }
          }
        }
      });
  }

  getAllReportsById(idBusinessByCountry: number) {
    this.allReports = this._reportSerives
      .getAllReports(idBusinessByCountry)
      .pipe(
        catchError((respError) => {
          if (respError.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              respError.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataTableReport = this.tipoReporte == 0 ? 
              resp.result.filter((reporte:ReportListModel)=> reporte.fkIIdInsuranceCompany == null) :
              resp.result.filter((reporte:ReportListModel)=> reporte.fkIIdInsuranceCompany != null) ;
          }
        }
      });
  }

  //Función que obtiene la lista de campos estandares.
  GetFiedsStandars(code: string) {
    this._parametersService
      .getParameters(code)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataStaticTableReportFieldStandard = resp;
        } else {
        }
      });
  }

  //obtiene la lista de etapas asignados a un prodcut y llena el dropdwon de etapas
  getStageByIdProductModule() {
    if (this.fromConfiguration.get('fkIIdProduct')?.value) {
      this._moduleService
        .getStageByIdProductModule(
          this.fromConfiguration.get('fkIIdProduct')?.value[0]
            .pkIIdProductModule
        )
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this.fromConfiguration.get('fkIdState')?.disable();
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this.fromConfiguration.get('fkIdState')?.disable();
              this._msgSvc.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            } else {
              if (resp.result != undefined) {
                if (resp.result.length > 0) {
                  this.fromConfiguration.get('fkIdState')?.enable();
                  this.stageList = resp.result;
                }
              }
            }
          }
        });
    }
  }

  /**
   * Create and add a new report object to the list of reports to send.
   * Use the currently selected values in the form to create a new report component.
   */
  addStandardFields() {
    const selectedValue = this.fromConfiguration.get('fkIdFieldStandar')?.value;
    const selectedOption = this.dataStaticTableReportFieldStandard.find(
      (option) => option.value === selectedValue
    );
    if (
      !this.dataTableReportFieldStandard.some(
        (x) => x.vFieldNameStandard === selectedOption.vFieldNameStandard
      )
    ) {
      const newField = {
        vFieldNameStandard: selectedOption.vFieldNameStandard, // Nombre correspondiente
        bIsVisible: true,
        iOrder: this.calculateOrderStandardFields(),
      };

      // Agrega el nuevo campo a la tabla
      this.dataTableReportFieldStandard = [
        ...this.dataTableReportFieldStandard,
        newField,
      ];
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant('Reports.Utils.ExistingField'),
        this._translateService.instant('Reports.Utils.ExistingFieldDetail')
      );
    }
  }

  /**
   * Create and add a new report object to the list of reports to send.
   * Use the currently selected values in the form to create a new report component.
   */
  addDynamicFields(pfkIdField:string, pfkIdStage:string, pfkIdState:string, tipo:number) {
    const selectedValue = this.fromConfiguration.get(pfkIdField)?.value;

    const isToField = selectedValue?.pkIIdField ? true : false;
    let listF;
    let orderCalculated = 0;

    let filtersDateStandard: string[] = []
    let filtersSelectStandard: string[] = []

    REPORTS_FILTERS_DATE_MODULE.forEach(filter => {filtersDateStandard.push(filter.vNameField)})
    REPORTS_FILTERS_DATE_POLICY.forEach(filter => {filtersDateStandard.push(filter.vNameField)})
    REPORTS_FILTERS_DATE_PRODUCT.forEach(filter => {filtersDateStandard.push(filter.vNameField)})
    REPORTS_FILTERS_SELECT_POLICY.forEach(filter => {filtersSelectStandard.push(filter.vNameField)})
    if (tipo === 1)
    {
      listF = this.listField
      orderCalculated = this.calculateOrderDynamicFields()
    }
    else if (tipo === 2)
    {
      listF = this.listFieldFilter
      orderCalculated = this.calculateOrderDynamicFilterFields()
    }
    else 
    {
      listF = this.listFieldFilterDate
      orderCalculated = this.calculateOrderDynamicFilterDateFields()
    }
    
    let selectedOption: any = null
    if (isToField)
      selectedOption = listF.find(
        (option) => option.pkIIdField === selectedValue.pkIIdField
      );
    else
      selectedOption = listF.find(
        (option) => option.pkIIdFieldModule === selectedValue.pkIIdFieldModule
      );
    
    if (filtersDateStandard.includes(selectedValue.vNameFieldDb) || filtersSelectStandard.includes(selectedValue.vNameFieldDb))
      selectedOption = {
        vNameField: selectedValue.vNameFieldDb,
        iOrder: orderCalculated,
        pkIIdField: orderCalculated,
      }
    if (!selectedOption || selectedOption == null) {
      return;
    }
    let existField = false;

    if(tipo == 1) {
      existField = this.dataTableReportFieldDinamic.some(
        (x) => x.idField === (isToField ? selectedOption?.pkIIdField : selectedOption?.pkIIdFieldModule)
      )
    } else if(tipo == 2 && !filtersSelectStandard.includes(selectedValue.vNameFieldDb)) {
      existField = this.dataTableReportFieldFilterDinamic.some(
        (x) => x.idField === (isToField ? selectedOption?.pkIIdField : selectedOption?.pkIIdFieldModule)
      )
    } else if(tipo == 2 && filtersSelectStandard.includes(selectedValue.vNameFieldDb)) {
      existField = this.dataTableReportFieldFilterDinamic.some(
        (x) => x.vFieldNameStandard === selectedOption?.vNameField
      )
    } else if(tipo == 3 && !filtersDateStandard.includes(selectedValue.vNameFieldDb)) {
      existField = this.dataTableReportFieldFilterDateDinamic.some(
        (x) => x.idField === (isToField ? selectedOption?.pkIIdField : selectedOption?.pkIIdFieldModule)
      )
    } else if (tipo == 3 && filtersDateStandard.includes(selectedValue.vNameFieldDb)){
      existField = this.dataTableReportFieldFilterDateDinamic.some(
        (x) => x.vFieldNameStandard === selectedOption?.vNameField)
    }
    
    if (existField){
      this._msgSvc.messageInfo(
        this._translateService.instant('Reports.Utils.ExistingField'),
        this._translateService.instant('Reports.Utils.ExistingFieldDetail')
      );
      return;
    }

    const newField = {
      vFieldNameStandard: selectedOption.vNameField,
      iOrder: orderCalculated,
      idStage: 0,
      idField: selectedOption.pkIIdFieldModule || selectedOption.pkIIdField,
      idStageByState: 0,
      BIsStandard: false,
      vNameStage: "",
      BIsToField: isToField,
    };
  
    if (this.tipoReporte == 0 && !(selectedValue.vNameFieldDb in filtersDateStandard || selectedValue.vNameFieldDb in filtersSelectStandard))
      {
        const selectedStage = (
          this.fromConfiguration.get(pfkIdStage)?.value ||
          this.fromConfiguration.get('fkIdStageFilter')?.value);

        const idState = this.forModules ? (
            this.fromConfiguration.get(pfkIdState)?.value?.pkIIdStageByState ||
            this.fromConfiguration.get(pfkIdState)?.value ||
            this.fromConfiguration.get('fkIdStateFilter')?.value?.pkIIdStageByState ||
            this.fromConfiguration.get('fkIdStateFilter')?.value
          ) : 0
        newField["idStage"] = this.forModules && selectedStage? selectedStage.pkIIdStage : 0
        newField["idStageByState"] = idState
        newField["vNameStage"] = this.forModules && selectedStage? selectedStage.vNameStage : ''
      }
    if (filtersDateStandard.includes(selectedValue.vNameFieldDb) || filtersSelectStandard.includes(selectedValue.vNameFieldDb))
    {
      delete newField["idField"]
      newField["BIsStandard"] = true
    }

    if(tipo == 1) {
      this.dataTableReportFieldDinamic = [
        ...this.dataTableReportFieldDinamic,
        newField,
      ];
    } else if(tipo == 2) {
      this.dataTableReportFieldFilterDinamic = [
        ...this.dataTableReportFieldFilterDinamic,
        newField,
      ];
    } else if(tipo == 3) {
      this.dataTableReportFieldFilterDateDinamic = [
        ...this.dataTableReportFieldFilterDateDinamic,
        newField,
      ];
    }
  }

  getFieldById(idField: number): any | undefined {
    this._moduleService
      .getFieldById(idField)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            var field = resp.result;

            return field;
          }
        }
      });
  }

  /**
   * Calculates the next order number for a new report in the list.
   * If the list is empty, it starts at 1; otherwise, it finds the highest order and adds one.
   * @returns {number} The next order number.
   */
  calculateOrderDynamicFields(): number {
    if (this.dataTableReportFieldDinamic.length == 0) {
      return 1;
    }
    return (
      this.dataTableReportFieldDinamic.reduce(
        (max, item) => (item.iOrder > max ? item.iOrder : max),
        this.dataTableReportFieldDinamic[0].iOrder
      ) + 1
    );
  }

  calculateOrderDynamicFilterFields(): number {
    if (this.dataTableReportFieldFilterDinamic.length == 0) {
      return 1;
    }
    return (
      this.dataTableReportFieldFilterDinamic.reduce(
        (max, item) => (item.iOrder > max ? item.iOrder : max),
        this.dataTableReportFieldFilterDinamic[0].iOrder
      ) + 1
    );
  }

  calculateOrderDynamicFilterDateFields(): number {
    if (this.dataTableReportFieldFilterDateDinamic.length == 0) {
      return 1;
    }
    return (
      this.dataTableReportFieldFilterDateDinamic.reduce(
        (max, item) => (item.iOrder > max ? item.iOrder : max),
        this.dataTableReportFieldFilterDateDinamic[0].iOrder
      ) + 1
    );
  }
  /**
   * Calculates the next order number for a new report in the list.
   * If the list is empty, it starts at 1; otherwise, it finds the highest order and adds one.
   * @returns {number} The next order number.
   */
  calculateOrderStandardFields(): number {
    if (this.dataTableReportFieldStandard.length == 0) {
      return 1;
    }
    return (
      this.dataTableReportFieldStandard.reduce(
        (max, item) => (item.iOrder > max ? item.iOrder : max),
        this.dataTableReportFieldStandard[0].iOrder
      ) + 1
    );
  }

  //obtiene la lista de estados asignados a una etapa y llena el dropdwon de estados
  getStagByStateAssociateToFormByIdStage(idStage: number) {
    if (idStage > 0) {
      this._moduleService
        .getStagByStateAssociateToFormByIdStage(idStage)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            } else {
              if (resp.result != undefined) {
                if (resp.result.length > 0) {
                  this.statusListCopyParameters = resp.result;
                  if (this.fromConfiguration.get('fkIdStage')?.value.vCode === 'ST-EM')
                    this._getEmissionForm()
                }
              }
            }
          }
        });
    }
  }

  getStateByIdStage(idStage: number) {
    if (idStage > 0) {
      this._moduleService
        .getStageByStateById(idStage)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            } else {
              if (resp.result != undefined) {
                if (resp.result.length > 0) {
                  this.statusListCopyParameters = resp.result;
                }
              }
            }
          }
        });
    }
  }

  /**
   * Handles changes when a field is selected. This method sets the selected field and updates its description.
   * @param pkIIdField The ID of the selected field.
   */
  onFieldChange(pkIIdField: number) {
    // this.selectedFieldId = pkIIdField;
    // this.selectedFieldDescription = this.listField.filter(
    //   (r) => r.pkIIdFieldModule == pkIIdField
    // )[0].vNameField;
  }

  /**
   * Updates the table columns based on the current language.
   * Uses a timeout to ensure Angular detects the change in reference.
   */
  updateTableColumns() {
    var estructNewReportsTable: BodyTableModel[] = [
      {
        columnLabel: this._translateService.instant('Product.Field'),
        columnValue: 'vFieldNameStandard',
      },
      {
        columnLabel: this._translateService.instant('Etapa'),
        columnValue: 'vNameStage',
      },
      {
        columnLabel: this._translateService.instant('Estado'),
        columnValue: 'vState',
      },
      {
        columnLabel: this._translateService.instant('Delete'),
        columnValue: 'delete',
        columnIcon: 'delete',
      },
    ];

    // Change the reference of the list to ensure Angular detects the change
    this.estructNewReportsTable = [];

    // Use setTimeout to ensure Angular detects the reference change
    setTimeout(() => {
      this.estructNewReportsTable = estructNewReportsTable;
      if (this.tipoReporte == 1 || this.forModules == false){
        this.estructNewReportsTable.splice(1, 1)
        this.estructNewReportsTable.splice(1, 1)
      }
    }, 1);
  }

  getAllReportsFieldByIdReport(idReport: string) {
    this._spinnerService.show();
    this.titleBtngenerateReport = 'Reports.NewReport.SaveReport';
    this.allReportsField1 = this._reportSerives
      .getReportById(idReport)
      .pipe(
        catchError((respError) => {
          this._spinnerService.hide();
          if (respError.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              respError.error.message
            );
          }
          return of([]);
        }),
        finalize(() => {
          this._spinnerService.hide(); // Ocultar el spinner en cualquier caso, éxito o error
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {

        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            if (resp.result) {
              this.idReport = resp.result.pkIdReport;
              this.fromConfiguration.patchValue({});
              this.listSavedProducts = resp.result.listIdProductModule
                .split(',')
                .map((str: any) => Number(str));
              this.fromConfiguration
                .get('fkIIdprocess')
                ?.setValue(resp.result.fkIIdprocess);
              this.fromConfiguration.get('fkIIdProduct')?.enable();

              if (resp.result.fkIIdProduct > 0 && resp.result.listIdProductModule.trim() == "") {
                this.fromConfiguration.get('bIsModule')?.setValue("0");
                this.radioForChange(0, true);

                // this.forModules = false;
                this.fromConfiguration
                .get('fkIIdProductUnique')
                ?.setValue(Number(resp.result.fkIIdProduct));
              }

            }

            this.dataTableReportField = resp.result.reportDetails;
            this.dataTableReportFieldStandard =
              this.dataTableReportField.filter((i) => i.bIsStandard && !i.bIsFilter && !i.bIsFilterDate);
            this.dataTableReportFieldStandard = this.sortByIOrder(
              this.dataTableReportFieldStandard
            );

            this.dataTableReportFieldDinamic = this.dataTableReportField.filter(
              (i) => !i.bIsStandard && !i.bIsFilter && !i.bIsFilterDate
            );
            this.dataTableReportFieldDinamic = this.sortByIOrder(
              this.dataTableReportFieldDinamic
            );

            if (this.dataTableReportFieldDinamic.length > 0) {
              this.fromConfiguration.get('CheckDetaillReport')?.setValue(true);
              this.isEditDetailReport = true;
            }




            // new 

            // tabla filter 
            this.dataTableReportFieldFilterDinamic = this.dataTableReportField.filter(
              (i) => i.bIsFilter
            );
            this.dataTableReportFieldFilterDinamic = this.sortByIOrder(
              this.dataTableReportFieldFilterDinamic
            );

            // tabla filter date
            this.dataTableReportFieldFilterDateDinamic = this.dataTableReportField.filter(
              (i) => i.bIsFilterDate
            );
            this.dataTableReportFieldFilterDateDinamic = this.sortByIOrder(
              this.dataTableReportFieldFilterDateDinamic
            );

            // check filtros 
            if (this.dataTableReportFieldFilterDinamic.length > 0) {
              this.fromConfiguration.get('CheckFilterReport')?.setValue(true);
            }

            if (this.dataTableReportFieldFilterDateDinamic.length > 0) {
              this.fromConfiguration.get('CheckFilterDateReport')?.setValue(true);
            }

            if (resp.result.bIsGroupFilterActive || resp.result.bIsRolFilterActive || resp.result.bIsUserFilterActive) {
              this.fromConfiguration.get('CheckFilterRoleUserReport')?.setValue(true);
            }
            this.fromConfiguration.get('CheckGroup')?.setValue(resp.result.bIsGroupFilterActive)
            this.fromConfiguration.get('CheckRol')?.setValue(resp.result.bIsRolFilterActive)
            this.fromConfiguration.get('CheckUser')?.setValue(resp.result.bIsUserFilterActive)
          }
        }
      });
  }

  /**
   * Gets the list of statuses for a specific product and populates the status drop-down list.
   * Enables control of the status selection form based on results and handles any errors that may occur during the process.
   *
   * @param {number} idProduct - The ID of the product for which the states are being fetched.
   */
  getStateList(idStage: number) {
    this._moduleService
      .getStageByStateById(idStage)
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (!Array.isArray(resp)) {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            if (resp.result) {
              this.listState = resp.result;
              this.fromConfiguration.get('fkIdState')?.enable();
              this.toggleHighlight('state', true);
            }
          }
        }
      });
  }

  /**
   * Toggles highlighting for a specific field. The highlight state toggles
   * between 'active', 'inactive', and returns to 'normal' after a set duration.
   * @param key The key of the field to be highlighted.
   * @param isActive Indicates whether the highlight should be activated ('active') or deactivated ('inactive').
   */
  toggleHighlight(key: keyof typeof this.highlights, isActive: boolean): void {
    this.highlights[key] = isActive ? 'active' : 'inactive';
    setTimeout(() => (this.highlights[key] = 'normal'), 1000);
  }

  /**
   * Retrieves the list of fields associated with the specified stage.
   * @param pkIIdStageByState - The ID of the selected stage.
   */
  getFieldList(pkIIdStageByState: number) {
    this._moduleService
      .getFormModuleByIdState([pkIIdStageByState])
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          this.fromConfiguration.get('fkIdField')?.disable();

          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        this.fromConfiguration.get('fkIdField')?.disable();
        if (Array.isArray(response)) {
          this.listField = [];
          this.fromConfiguration.get('fkIdField')?.disable();
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
            this.listField = [];
            this.toggleHighlight('field', false);
            this.fromConfiguration.get('fkIdField')?.disable();
          } else {
            if (response.result) {
              this._moduleService
                .getFieldsByForm(response.result.pkIIdFormModule)
                .pipe(
                  catchError((error) => {
                    this.fromConfiguration.get('fkIdField')?.disable();
                    return of([]);
                  })
                )
                .subscribe((resp: ResponseGlobalModel | never[]) => {
                  if (Array.isArray(resp)) {
                    this.listField = [];
                    this.fromConfiguration.get('fkIdField')?.disable();
                    this.toggleHighlight('field', false);
                  } else {
                    if (resp.error) {
                      this._msgSvc.messageError(
                        this._translateService.instant('ThereWasAError') +
                          resp.message
                      );
                      this.fromConfiguration.get('fkIdField')?.disable();
                      this.toggleHighlight('field', false);
                    } else {
                      this.toggleHighlight('field', true);
                      this.fromConfiguration.get('fkIdField')?.enable();
                      this.listField = resp.result;
                      if (this.listField.length != 0) {
                        this.fromConfiguration.get('fkIdField')?.enable();
                      } else {
                        this.fromConfiguration.get('fkIdField')?.disable();
                      }
                      this._setFieldsFilters()
                    }
                  }
                });
            }
          }
        }
      });
  }

  getFieldListField(IdProduct: number) {
    this._fieldServices
      .getFieldsByProductId(IdProduct)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          this.fromConfiguration.get('fkIdField')?.disable();
          this.fromConfiguration.get('fkIdFieldFilter')?.disable();
          this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();

          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        this.fromConfiguration.get('fkIdField')?.disable();
        this.fromConfiguration.get('fkIdFieldFilter')?.disable();
        this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
        if (Array.isArray(resp)) {
          this.listField = [];
          this.fromConfiguration.get('fkIdField')?.disable();
          this.fromConfiguration.get('fkIdFieldFilter')?.disable();
          this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
          this.toggleHighlight('field', false);
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
            this.fromConfiguration.get('fkIdField')?.disable();
            this.fromConfiguration.get('fkIdFieldFilter')?.disable();
            this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
            this.toggleHighlight('field', false);
          } else {
            this.toggleHighlight('field', true);
            this.fromConfiguration.get('fkIdField')?.enable();
            this.listField = resp.result;
            if (this.listField.length != 0) {
              this.fromConfiguration.get('fkIdField')?.enable();
            } else {
              this.fromConfiguration.get('fkIdField')?.disable();
            }
            this._setFieldsFilters()

          }
        }
      });
  }

  /**
   * Asynchronously retrieves all processes by invoking the getAllProcess method from the parameters service.
   * This method uses error handling to manage any issues that might occur during the API call.
   * The results are stored in listProcess if successful.
   */
  async getAllProcess() {
    try {
      const resp = await firstValueFrom(
        this._parametersService.getAllProcess().pipe(
          catchError((error) => {
            console.error('An error occurred:', error);
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message || 'Unknown error'
            );
            return of(
              new ResponseGlobalModel(
                true,
                error.error.message || 'Unknown error',
                null,
                0
              )
            );
          })
        )
      );

      if (resp.error) {
        console.error('Error in response:', resp.message);
      } else {
        this.listProcess = resp.result;
      }
    } catch (error) {
      console.error('An unexpected error occurred:', error);
    }
  }

  /**
   * Initiates the report generation process following user confirmation.
   * Displays a confirmation message asking whether to create the report.
   * If the user confirms, proceeds to save the report using the `saveReport` function.
   */
  generateReport() {
    if (this.fromConfiguration.get('vName')?.errors)
      return this._msgSvc.messageWaring(
        this._translateService.instant('WarningMessage'),
        this._translateService.instant('NameAlreadyExistsError')
      );
    if (this.isEditingReport == true) {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('Reports.NewReport.EditReport'),
          `${this._translateService.instant(
            'Reports.Utils.AReportNamed'
          )} <strong>${
            this.fromConfiguration.get('vName')?.value
          }</strong> ${this._translateService.instant(
            'Reports.Utils.WillBeEdited'
          )}`,
          'info',
          this._translateService.instant('Accept'),
          this._translateService.instant('Cancel')
        )
        .then((resConfirmation) => {
          if (resConfirmation) {
            this.saveReport();
          }
        });
    } else {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('Reports.Utils.CreatingReport'),
          `${this._translateService.instant(
            'Reports.Utils.AReportNamed'
          )} <strong>${
            this.fromConfiguration.get('vName')?.value
          }</strong> ${this._translateService.instant(
            'Reports.Utils.WillBeCreated'
          )}`,
          'info',
          this._translateService.instant('Accept'),
          this._translateService.instant('Cancel')
        )
        .then((resConfirmation) => {
          if (resConfirmation) {
            this.saveReport();
          }
        });
    }
  }

  /**
   * Saves the report to local storage.
   * If existing report data is present, it updates and appends the new report; otherwise, it creates new storage with the current report.
   * Ultimately, displays a success message and redirects the user to the reports page.
   */
  saveReport() {
    if (!this.forModules || this.validateStandarsFields(this.dataTableReportFieldStandard)) {
      var idReports =
        this.isEditingReport == true ? this.idReport : this.generateUniqueId();
      let reportCompleted: IReportGeneratedComplete2 | null = null;

      if (this.isEditingReport == true) {
        if (this.forModules && this.tipoReporte == 0) this.assignProducts();
        reportCompleted = {
          IdReport: idReports,
          DescriptionReport: this.fromConfiguration.get('vDescription')?.value,
          NameReport: this.fromConfiguration.get('vName')?.value, // Nombre del reporte
          fkIdBussinesByCountey: this.idBusinessByCountry, // ID del negocio por país
          ListIdProductModule: this.productListSeletected.join(','),
          IdProcessModule: this.fromConfiguration.get('fkIIdprocess')?.value || 0,
          BIsGroupFilterActive: this.fromConfiguration.get('CheckGroup')?.value ?? false,
          BIsRolFilterActive: this.fromConfiguration.get('CheckRol')?.value ?? false,
          BIsUserFilterActive: this.fromConfiguration.get('CheckUser')?.value ?? false,
            ListDetailsReports: [
              // dinamico 
              ...this.dataTableReportFieldDinamic.map((detail) => ({
                IOrder: detail.iOrder,
                IdField: detail.idField,
                PkGIdDetailsReports: detail.pkGIdDetailsReports,
                IdStageByState: detail.idStageByState,
                VFieldNameStandard: detail.vFieldNameStandard,
                IdStage: detail.idStage,
                BIsStandard: false,
                BIsToField: (detail.bIsToField || detail.BIsToField) ?? false,
                BIsVisible: true
              })),
              // standar
              ...this.dataTableReportFieldStandard.map((detail) => ({
                IOrder: detail.iOrder,
                PkGIdDetailsReports: detail.pkGIdDetailsReports,
                VFieldNameStandard: detail.vFieldNameStandard,
                BIsVisible: detail.bIsVisible,
                BIsStandard: true,
              })),
              // filter 
              ...this.dataTableReportFieldFilterDinamic.map((detail) => ({
                IOrder: detail.iOrder,
                IdField: detail.idField,
                PkGIdDetailsReports: detail.pkGIdDetailsReports,
                IdStageByState: detail.idStageByState,
                VFieldNameStandard: detail.vFieldNameStandard,
                IdStage: detail.idStage,
                BIsStandard: (detail.BIsStandard || detail.bIsStandard) ?? false,
                BIsFilter: true,
                BIsToField: (detail.bIsToField || detail.BIsToField) ?? false,
                BIsVisible: true
              })),
               // filter date
               ...this.dataTableReportFieldFilterDateDinamic.map((detail) => ({
                IOrder: detail.iOrder,
                IdField: detail.idField,
                PkGIdDetailsReports: detail.pkGIdDetailsReports,
                IdStageByState: detail.idStageByState,
                VFieldNameStandard: detail.vFieldNameStandard,
                IdStage: detail.idStage,
                BIsStandard: (detail.BIsStandard || detail.bIsStandard) ?? false,
                BIsFilterDate: true,
                BIsToField: (detail.bIsToField || detail.BIsToField) ?? false,
                BIsVisible: true
              })),
            ],
            BIsFilterGroup: this.fromConfiguration.get('CheckFilterRoleUserReport')?.value,
        };
      } else {
        if (this.forModules && this.tipoReporte == 0) this.assignProducts();
        reportCompleted = {
          DescriptionReport: this.fromConfiguration.get('vDescription')?.value,
          NameReport: this.fromConfiguration.get('vName')?.value, // Nombre del reporte
          fkIdBussinesByCountey: this.idBusinessByCountry, // ID del negocio por país
          ListIdProductModule: this.productListSeletected.join(','),
          IdProcessModule: this.fromConfiguration.get('fkIIdprocess')?.value || 0,
          BIsGroupFilterActive: this.fromConfiguration.get('CheckGroup')?.value ?? false,
          BIsRolFilterActive: this.fromConfiguration.get('CheckRol')?.value ?? false,
          BIsUserFilterActive: this.fromConfiguration.get('CheckUser')?.value ?? false,
          ListDetailsReports: [
            // dinamico 
            ...this.dataTableReportFieldDinamic.map((detail) => ({
              IOrder: detail.iOrder,
              IdField: detail.idField,
              PkGIdDetailsReports: detail.pkGIdDetailsReports,
              IdStageByState: detail.idStageByState,
              VFieldNameStandard: detail.vFieldNameStandard,
              IdStage: detail.idStage,
              BIsStandard: false,
              BIsVisible: true,
              BIsToField: (detail.bIsToField || detail.BIsToField) ?? false
            })),
            // standar
            ...this.dataTableReportFieldStandard.map((detail) => ({
              IOrder: detail.iOrder,
              BIsVisible: detail.bIsVisible,
              VFieldNameStandard: detail.vFieldNameStandard,

              BIsStandard: true,
            })),
            // filter 
            ...this.dataTableReportFieldFilterDinamic.map((detail) => ({
              IOrder: detail.iOrder,
              IdField: detail.idField,
              PkGIdDetailsReports: detail.pkGIdDetailsReports,
              IdStageByState: detail.idStageByState,
              VFieldNameStandard: detail.vFieldNameStandard,
              IdStage: detail.idStage,
              BIsStandard: (detail.BIsStandard || detail.bIsStandard) ?? false,
              BIsFilter: true,
              BIsVisible: true,
              BIsToField: (detail.bIsToField || detail.BIsToField) ?? false
            })),
             // filter date
             ...this.dataTableReportFieldFilterDateDinamic.map((detail) => ({
              IOrder: detail.iOrder,
              IdField: detail.idField,
              PkGIdDetailsReports: detail.pkGIdDetailsReports,
              IdStageByState: detail.idStageByState,
              VFieldNameStandard: detail.vFieldNameStandard,
              IdStage: detail.idStage,
              BIsStandard: (detail.BIsStandard || detail.bIsStandard) ?? false,
              BIsFilterDate: true,
              BIsVisible: true,
              BIsToField: (detail.bIsToField || detail.BIsToField) ?? false
            })),

          ],
          ListIdProduct: this.fromConfiguration.get('fkIIdProductUnique')?.value,
          BIsFilterGroup: this.fromConfiguration.get('CheckFilterRoleUserReport')?.value,
        };
      }
      if (this.tipoReporte == 1)
      {
        reportCompleted["fkIdInsuranceCompany"] = this.fromConfiguration.get('fkIIdInsurance')?.value
        reportCompleted["fkIdProduct"] = this.fromConfiguration.get('fkIIdProductInsurance')?.value
      }
      if (this.isEditingReport) {
        this.editReportSet(reportCompleted);
      } else {
        this.createReportSet(reportCompleted);
      }
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant('Reports.Utils.RequiredFields'),
        ''
      );
    }
  }

  /**
   * Generates a unique identifier by combining a timestamp and a random string.
   * The timestamp is converted to base 36 for a shorter, more readable string.
   * The random string is generated from alphanumeric characters to increase entropy.
   * @returns {string} A unique identifier.
   */
  generateUniqueId() {
    const timestamp = Date.now().toString(36);
    const randomString = Math.random().toString(36).substring(2, 15);
    return `${timestamp}-${randomString}`;
  }

  /**
   * Edits an existing report and saves the changes to the server.
   * Displays a success message upon successful editing, otherwise shows an error message.
   * @param reportCompleted The completed report object to be updated.
   */
  editReportSet(reportCompleted: IReportGeneratedComplete2) {
    this._reportSerives.uptateReport(reportCompleted).subscribe(
      (r) => {
        if (r.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + r.message
          );
        } else {
          this._msgSvc.messageSuccess(
            this._translateService.instant(
              'Reports.Utils.OperationSuccessfull'
            ),
            this._translateService.instant(
              'Reports.Utils.ReportSuccessfullyEdited'
            )
          );
          window.location.reload();
        }
      },
      (error) => {
        this._msgSvc.messageError(
          error?.error?.result ??
            error?.error?.message ??
            this._translateService.instant('Reports.Utils.CouldNotEditReport')
        );
      }
    );
  }

  /**
   * Creates a new report set and saves it to the server.
   * Displays a success message upon successful creation, otherwise shows an error message.
   * @param reportCompleted The completed report object to be created.
   */
  createReportSet(reportCompleted: IReportGeneratedComplete2) {
    this._reportSerives.createReport(reportCompleted).subscribe(
      (r) => {
        if (r.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + r.message
          );
        } else {
          this._msgSvc.messageSuccess(
            this._translateService.instant(
              'Reports.Utils.OperationSuccessfull'
            ),
            this._translateService.instant(
              'Reports.Utils.ReportSuccessfullyCreated'
            )
          );

          window.location.reload();
        }
      },
      (error) => {
        this._msgSvc.messageError(
          this.isEditingReport == true
            ? error?.error?.result ??
                error?.error?.message ??
                this._translateService.instant('Warning')
            : error?.error?.result ??
                error?.error?.message ??
                this._translateService.instant(
                  'Reports.Utils.CouldNotCreateReport'
                )
        );
      }
    );
  }
  //Valida si los campos estandares 'ID de tarea' y 'Fecha de creación' fueron agregados.
  validateStandarsFields(fieldList: any[]): boolean {
    if (this.tipoReporte == 0)
    {
      const hasTaskId = fieldList.some(
        (x) => x.vFieldNameStandard === 'ID de tarea'
      );
      const hasCreationDate = fieldList.some(
        (x) => x.vFieldNameStandard === 'Fecha de creación'
      );
      return hasTaskId && hasCreationDate;
    }
    else
    {
      const hasCreationDate = fieldList.some(
        (x) => x.vFieldNameStandard === 'Id del riesgo'
      );

      return hasCreationDate;
    }
    
  }

  //Busca prodcutos por una lista de prodcutos porF pkIIdProductModule.
  searchProductByIdList = (idList: number[]): INewReportProductoModel[] => {
    return this.listProduct.filter((objeto) =>
      idList.includes(objeto.pkIIdProductModule)
    );
  };

  //Ordena un array de objetos basado en el valor de la llave iOrder.
  sortByIOrder = (array: any[]): any[] => {
    return array.sort((a, b) => a.iOrder - b.iOrder);
  };

  assignProducts() {
    const productlist = this.fromConfiguration.get('fkIIdProduct')?.value;
    this.productListSeletected = [];
    productlist.forEach((element: INewReportProductoModel) => {
      this.productListSeletected.push(element.pkIIdProductModule);
    });
  }

  /**
   * Cancels the current report creation or editing process.
   * Redirects the user back to the reports dashboard without saving any changes.
   */
  cancel() {
    window.location.reload();
  }







  // nuevo dev

  radioForChange(value: number, isupdate:boolean = false) {
    if (!isupdate) this.clearForm();

    if(this.tipoReporte == 0){
      if (value > 0) {
        this.forModules = true;
        this.GetFiedsStandars('FiedsStandars');
      } else {
        this.forModules = false;
        this.GetFiedsStandars('FiedsStandarsProduct');
      }
    } else {
      this.GetFiedsStandars('FiedsStandarsPolicy');
    }
    this.updateTableColumns();
   
  }

  //obtiene todos los productos para un id business country
  getAllProducts(idBusinessByCountry: number) {

    this._productService
      .getAllProductByBusiness(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.allProductsList = resp.result;
          }
        }
      });
  }


  clearForm(){
    if (this.fromConfiguration) {

      this.fromConfiguration.get('vName')?.setValue(null);
      this.fromConfiguration.get('vDescription')?.setValue(null);
      this.fromConfiguration.get('CheckGroup')?.setValue(null);
      this.fromConfiguration.get('CheckRol')?.setValue(null);
      this.fromConfiguration.get('CheckUser')?.setValue(null);
      this.fromConfiguration.get('CheckDetaillReport')?.setValue(false);
      this.fromConfiguration.get('listGroupBussines')?.setValue([]);
      this.fromConfiguration.get('listRoleBusiness')?.setValue([]);
      this.fromConfiguration.get('listUserBussiens')?.setValue([]);
      this.fromConfiguration.get('listProduct')?.setValue([]);
      this.fromConfiguration.get('fkIIdprocess')?.setValue(null);
      this.fromConfiguration.get('fkIIdProduct')?.setValue(null);
      this.fromConfiguration.get('fkIIdProductUnique')?.setValue(null);
      this.fromConfiguration.get('fkIdStage')?.setValue(null);
      this.fromConfiguration.get('fkIdState')?.setValue(null);
      this.fromConfiguration.get('fkIdField')?.setValue(null);
      this.fromConfiguration.get('fkIdFieldStandar')?.setValue(null);

      this.fromConfiguration.get('fkIdStageFilter')?.setValue(null);
      this.fromConfiguration.get('fkIdStateFilter')?.setValue(null);
      this.fromConfiguration.get('fkIdFieldFilter')?.setValue(null);
      this.fromConfiguration.get('fkIdFieldFilterDate')?.setValue(null);

      this.fromConfiguration.get('CheckFilterReport')?.setValue(false);
      this.fromConfiguration.get('CheckFilterDateReport')?.setValue(false);
      this.fromConfiguration.get('CheckFilterRoleUserReport')?.setValue(false);


    }
    this.dataTableReportFieldStandard = [];
    this.dataTableReportFieldFilterDinamic = [];
    this.dataTableReportFieldFilterDateDinamic = [];
    this.isEditDetailReport = false;

  }


  getFieldListFieldFilter(IdProduct: number) {

    this._fieldServices
      .getFieldsByProductId(IdProduct)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          this.fromConfiguration.get('fkIdFieldFilter')?.disable();

          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        this.fromConfiguration.get('fkIdFieldFilter')?.disable();
        this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
        if (Array.isArray(resp)) {
          this.listFieldFilter = [];
          this.listFieldFilterDate = [];
          this.fromConfiguration.get('fkIdFieldFilter')?.disable();
          this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
          this.toggleHighlight('field', false);
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
            this.fromConfiguration.get('fkIdFieldFilter')?.disable();
            this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
            this.toggleHighlight('field', false);
          } else {
            this.toggleHighlight('field', true);
            this.fromConfiguration.get('fkIdFieldFilter')?.enable();
            this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
            this._setFieldsFilters()
          }
        }
      });

  }


  getFieldListFilter(pkIIdStageByState: number) {
    this._moduleService
      .getFormModuleByIdState([pkIIdStageByState])
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          this.fromConfiguration.get('fkIdFieldFilter')?.disable();

          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        this.fromConfiguration.get('fkIdFieldFilter')?.disable();
        if (Array.isArray(response)) {
          this.listFieldFilter = [];
          this.listFieldFilterDate = [];
          this.fromConfiguration.get('fkIdFieldFilter')?.disable();
          this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
            this.listFieldFilter = [];
            this.listFieldFilterDate = [];
            this.toggleHighlight('field', false);
            this.fromConfiguration.get('fkIdFieldFilter')?.disable();
            this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
          } else {
            if (response.result) {
              this._moduleService
                .getFieldsByForm(response.result.pkIIdFormModule)
                .pipe(
                  catchError((error) => {
                    this.fromConfiguration.get('fkIdFieldFilter')?.disable();
                    this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
                    return of([]);
                  })
                )
                .subscribe((resp: ResponseGlobalModel | never[]) => {
                  if (Array.isArray(resp)) {
                    this.listFieldFilter = [];
                    this.listFieldFilterDate = [];
                    this.fromConfiguration.get('fkIdFieldFilter')?.disable();
                    this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
                    this.toggleHighlight('field', false);
                  } else {
                    if (resp.error) {
                      this._msgSvc.messageError(
                        this._translateService.instant('ThereWasAError') +
                          resp.message
                      );
                      this.fromConfiguration.get('fkIdFieldFilter')?.disable();
                      this.fromConfiguration.get('fkIdFieldFilterDate')?.disable();
                      this.toggleHighlight('field', false);
                    } else {
                      this.listField = resp.result;
                      this.toggleHighlight('field', true);
                      this._setFieldsFilters()
                    }
                  }
                });
            }
          }
        }
      });
  }

  getFieldType() {
    this.fieldSubs = this._fieldServices.getFieldType().pipe(
      catchError((error) => {
        this._msgSvc.messageWaring(this._translateService.instant('Warning'), error.error.message)
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.filterTypeList = resp.result.filter((item:FieldTypeModel) => item.vCode === 'DROPDOWN' || item.vCode === 'RADIO');
          this.filterTypeDateList = resp.result.filter((item:FieldTypeModel) => item.vCode === 'DATE');
        }
      }
    });
  }

  getInsurersGlobal(IdBusinessByCountry:number) {
    this._insuranceService
      .getInsuranceCompanyByIdBusinessByCountry(IdBusinessByCountry)
      .subscribe({
        next: (response) => {
          this.insurers = response.result;
        },
      });
  }

}
