.subtitle-text{
    margin-top: -8px; 
    color: grey;
}

.container-flow{
    background-color: rgba(128, 128, 128, 0.070);
    border-radius: 18px;
}

h6 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2f2c31d7;
}
label{
    font-weight: 500 !important;
    color: #2f2c31c4 !important;
}

.span{
    font-size: 15px;
    font-weight: 100;
    color: grey;
}
.mat-mdc-paginator{
    visibility: hidden !important;
}

.button-container {
    display: flex;
    justify-content: center;
    gap: 10px; /* Espacio entre botones */
}

.button-container button {
      margin: 10px;
}

.label-button {
    font-size: 1.2rem !important;
    font-weight: bold;
}

.margin-radioButton {
    margin: 0 10px;
  }

  mat-hint{
    color: gray;
}