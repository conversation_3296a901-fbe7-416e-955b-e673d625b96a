
<div class="clientes-container">
  <!-- Botón para regresar al listado de clientes -->
  <a mat-button class="fw-bold text-decoration-none" (click)="back()">
    <mat-icon>keyboard_backspace</mat-icon>
    {{ 'Client.GotoList' | translate }}
  </a>

  <mat-card class="mt-2">
    <mat-card-content style="margin-top: -5px; padding-left: 22px; padding-right: 0px;">
        <div class="row" style="height: 60px; width: 100%;background-color: #8080802b; border-radius: 5px; padding: 0;">
          <div class="poliza-id col-md-9" >
           
              <div class="col-md-1"> <label>Id de póliza</label></div>
              <div class="col-md-1 center-content" style="background-color: rgb(128 128 128 / 30%); height: 40px; border-radius: 8px;">
                <span style="font-weight: bold;  letter-spacing: 1px;">022484449</span>
              </div>
              <div class="col-md-10"></div>
          </div>
          <div class="poliza-status col-md-3">
            <mat-icon>check_circle</mat-icon>
            <span style="color: black; font-weight: 600;">{{ 'Client.Product.PolicyIssuedOn' | translate }} dd/mm/aaaa </span>
          </div>
        </div>

      <div class="row">
        <div class="col-md-3 mt-4">
          <label> <strong>{{ 'Client.Client' | translate }}</strong></label>
          <br>
          <span> {{ data?.customerName }}</span>
        </div>
        <div class="col-md-3 mt-4">
          <label> <strong>{{ 'Product.Product' | translate }}</strong></label>
          <br>
          <span>{{ data?.productName }}</span>
        </div>
        <div class="col-md-3 mt-4">
          <label> <strong>{{ 'Plan.Plan' | translate }}</strong></label>
          <br>
          <span>{{ data?.planName }}</span>
        </div>
        <div class="col-md-3 mt-4">
          <label> <strong>{{ 'Plan.Insurer' | translate }}</strong></label>
          <br>
          <span>{{ data?.insuranceCarrierName }}</span>
        </div>
        <div class="col-md-3 mt-4">
          <label> <strong>{{ 'MyQuotation.StepData.ValidSince' | translate }}</strong></label>
          <br>
          <span>DD-MM-AAAA</span>
        </div>
        <div class="col-md-3 mt-4">
          <label> <strong>{{ 'MyQuotation.StepData.ValidUntil' | translate }}</strong></label>
          <br>
          <span>DD-MM-AAAA</span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
  <mat-card class="document-section mt-5" *ngIf="data?.policyRiskId">
    <app-see-documents-field-quote-policy [idPolicyRisk]="data?.policyRiskId || 0"></app-see-documents-field-quote-policy>
  </mat-card>

  <mat-card class="caratula mt-5">
    <mat-card-header>
      <h5><strong>{{ 'Client.Product.PolicyCover' | translate }}</strong></h5>
    </mat-card-header>

    <div class="row">
      <div class="col-md-10">
        <mat-card-content>
          <!-- Aquí se muestra el PDF -->
          <pdf-viewer [src]="pdfSrc"
          [render-text]="true"
          [original-size]="true"
          style="width: 100%; height: 800px"
    ></pdf-viewer>
        </mat-card-content>
      </div>
      <div class="col-md-2">
        <mat-card-actions>
          <button mat-raised-button color="primary" style="width: 200px;" (click)="descargarPDF()"><strong>{{ 'Download' | translate }} PDF</strong></button>
        </mat-card-actions>
        <mat-card-actions>
          <button mat-raised-button color="accent" style="width: 200px;" (click)="sendPDF()"><strong>{{ 'Quotation.SendEmailQuote.SendButton' | translate }} PDF</strong></button>
        </mat-card-actions>
      </div>
    </div>
    
    
  </mat-card>
</div>
