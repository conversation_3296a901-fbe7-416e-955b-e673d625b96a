import {
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSelectModule } from '@angular/material/select';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { Router } from '@angular/router';
import { Subscription, catchError, of } from 'rxjs';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { TemplateModel } from 'src/app/shared/models/templates';
import { StageByStateModel } from 'src/app/shared/models/module';
import { ComunicationsModel } from 'src/app/shared/models/configuration-form';

import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { Modal2Component } from "../../../../../../shared/components/modal2/modal2.component";
import { MatDialog } from '@angular/material/dialog';
import { DataFormModel } from 'src/app/shared/models/module/data-form.model';
import { ChildrenStateListModel } from 'src/app/shared/models/module/children-state-list.model';
import { CommunicationModel } from 'src/app/shared/models/module/communication.model';


@Component({
  selector: 'app-comunications',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    MatSelectModule,
    TableComponent,
    Modal2Component
],
  templateUrl: './comunications.component.html',
  styleUrls: ['./comunications.component.scss'],
})
export class ComunicationsComponent implements OnInit, OnDestroy, OnChanges {
  @Input() idForm: number = 0;
  @Input() dataForm: DataFormModel = {};
  idBusinessByCountry: number = 0;
  form: FormGroup = new FormGroup({});
  private _formConfigurationData?: Subscription;
  templateList: TemplateModel[] = [];

  @ViewChild('createEditElementComunicationsModal')
  createEditElementComunicationsModal?: TemplateRef<any>;
  estructTableCommunications: BodyTableModel[] = [];
  stageByStateList: StageByStateModel[] = [];
  stageByStateChildrenList: ChildrenStateListModel[] = [];
  dataTableCommunications: CommunicationModel[] = [];

  constructor(
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public utilsSvc: UtilsService,
    private _fb: FormBuilder,
    private _parametersService: ParametersService,
    private _settingService: SettingService,
    private _customRouter: CustomRouterService,
    public _matDialog: MatDialog,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['idForm']) {
      this.form.get('fkIIdFormModule')?.setValue(this.idForm);
    }
  }

  ngOnInit(): void {    
    this.getSettingInit();
    this.initForm();
  }

  async getSettingInit() {
    let dataSettingInit = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = dataSettingInit.idBusinessByCountry;
    this.estrucTable();
    this.getCommunicationIdProductModule();
    this.getStageByState();

  }

  initForm() {
    this.form = this._fb.group({
      pkIIdCommunicationModule: [0],
      bEmailSend: [true],
      fkIIdTemplate: [null, [Validators.required]],
      fkIIdFormModule: [this.idForm],
      fkIIdStageByStateCurrent: [null, [Validators.required]],
      fkIIdStageByState: [null, [Validators.required]],
    });

    this.form.get('fkIIdStageByStateCurrent')?.valueChanges.subscribe({
      next: (fkIIdStageByStateCurrent) => {
        if (fkIIdStageByStateCurrent > 0) {
          this.getChildStateByIdStateParent(fkIIdStageByStateCurrent);
          this.getAllTemplatesFilters(fkIIdStageByStateCurrent)
        }
      },
    });

  }

  get valid(): boolean {
    return this.form.valid;
  }

  // creacion de registro de comunicacion
  createCommunication(model: ComunicationsModel) {
    this._moduleService
      .createCommunication(model)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant(
                'ModulesSetting.SuccessfulMessageCreated'
              )
            );
            this.getCommunicationIdProductModule();
            this._matDialog.closeAll();
          }
        }
      });
  }

  // actualizacion de registro de comunicacion
  updateCommunication(model: ComunicationsModel) {
    this._moduleService
      .updateCommunication(model)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant(
                'ModulesSetting.SuccessfulMessageCreated'
              )
            );
            this.getCommunicationIdProductModule();
            this._matDialog.closeAll();
          }
        }
      });
  }

  cancel() {
    this._customRouter.navigate(['/dashboard/forms']);
  }

  // validar guardado / actualizacion
  saveForm() {
    const formValues = this.form.value;
    const stageByStateCurrent = formValues.fkIIdStageByStateCurrent;
    const stageByState = formValues.fkIIdStageByState;
  
    // Verificamos si ya existe un registro con los mismos fkIIdStageByStateCurrent y fkIIdStageByState
    const duplicateRecord = this.dataTableCommunications.some(record => 
      record.fkIIdStageByStateCurrent === stageByStateCurrent &&
      record.fkIIdStageByState === stageByState &&
      record.pkIIdCommunicationModule !== formValues.pkIIdCommunicationModule // Excluir el caso en que es el mismo registro
    );
  
    if (duplicateRecord) {
      this._msgSvc.messageWaring(
        this._translateService.instant('ThereWasAError'),
        this._translateService.instant(
          'Communications.AddAlert'
        )
      );
      return;
    }
  
    if (formValues.pkIIdCommunicationModule > 0) {
      this.updateCommunication(formValues);
    } else {
      this.createCommunication(formValues);
    }
  }
  
  ngOnDestroy(): void {
    this._formConfigurationData?.unsubscribe();
  }

  estrucTable(){
    this.estructTableCommunications = [
      { columnLabel: this._translateService.instant('Communications.PreviousStatus'), columnValue: 'nameStageByStateCurrent' }, // Estado anterior
      { columnLabel: this._translateService.instant('Communications.ChangeStatus'), columnValue: 'nameStageByState' }, // Cambio de estado a 
      { columnLabel: this._translateService.instant('Communications.Template'), columnValue: 'nameTemplate' }, // Plantilla
      { columnLabel: this._translateService.instant('Communications.Modify'), columnValue: 'modify', columnIcon: 'edit',},
      { columnLabel: this._translateService.instant('Communications.Eliminate'), columnValue: 'delete', columnIcon: 'delete', }
    ];

    this._translateService.onLangChange.subscribe(
      (event: LangChangeEvent) => {
        this.estructTableCommunications[0].columnLabel = this._translateService.instant('Communications.PreviousStatus')
        this.estructTableCommunications[1].columnLabel = this._translateService.instant('Communications.ChangeStatus')
        this.estructTableCommunications[2].columnLabel = this._translateService.instant('Communications.Template')
        this.estructTableCommunications[3].columnLabel = this._translateService.instant('Communications.Modify')
        this.estructTableCommunications[4].columnLabel = this._translateService.instant('Communications.Eliminate')
      }
    );
  }

  // eventos tabla
  controller(evt:IconEventClickModel){
    if(evt.column=="modify"){
      this.getCommunicationId(evt.value.pkIIdCommunicationModule);
      this.openModalCreateEditElement();
    }else  if(evt.column=="delete") {
      this._msgSvc
        .messageConfirmationAndNegationReverseButton(
          this._translateService.instant('Delete') + '?',
          '',
          'warning',
          this._translateService.instant('Cancel'),
          this._translateService.instant('Confirm')
        )
        .then((result) => {
          if (result) {
            this.deleteCommunication(evt.value.pkIIdCommunicationModule);
            this._msgSvc.messageConfirmatio(              
              this._translateService.instant('Deleted'),
              '',
              'success',
              this._translateService.instant('Continue')
            );
          }
        });    
    }
  }
  
  // abrir modal de creacion / edicion
  openModalCreateEditElement() {
    this.initForm();
    this.stageByStateChildrenList = [];

    this._matDialog.open(this.createEditElementComunicationsModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  closeModalEvent(event: boolean) {
  }
  
  get validformCreateEditElement(): boolean {
    return this.form.valid;
  }

 // get plantillas por estado
  getAllTemplatesFilters(idState: any) {
    this._parametersService
      .getAllTemplatesFilters(idState)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          this.templateList = []
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this.form.get('fkIIdTemplate')?.setValue(null);
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if (response.result) {
              this.templateList = response.result.filter((t: { bIsSendFileTask: boolean; }) => t.bIsSendFileTask == false);
            }
            else
              this.templateList = []
          }
        }
      });
  }

  // get estado por id stage
  getStageByState() {
    let idStage = this.dataForm.idStage
    this._moduleService
      .getStageByStateById(idStage!)
      .pipe(
        catchError((error) => {
          console.log('No existen estados asociados a esta etapa.');
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              resp.message
            );
          } else {
            this.stageByStateList = resp.result;
          }
        }
      });
  }

  //obtiene los estados dependientes por idState
  getChildStateByIdStateParent(idStateParent: number) {
    this.stageByStateChildrenList = [];
    this._moduleService
      .getChildStateByIdStateParent(idStateParent)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.stageByStateChildrenList = resp.result;
        }
      });
  }

  // cargar las comunicaciones creadas en ese product module
  getCommunicationIdProductModule() {
    this._moduleService
      .getCommunicationIdForm(this.idForm)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
              this.dataTableCommunications = resp.result;
          }
        }
      });
  }

  // traer registro de comunicacion especifica
  getCommunicationId(id: number) {
    this._moduleService
      .getCommunicationId(id)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.form.patchValue(resp.result);
        }
      });
  }

  //Borra una registro de comunicacion
  deleteCommunication(idCommunicationModule: number)
  {
    this._moduleService
      .deleteCommunication(idCommunicationModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._msgSvc.messageSuccess(
              this._translateService.instant('DeleteMessage'),
              ''
            );
            this.getCommunicationIdProductModule();
          }
        }
      });
  }


}

