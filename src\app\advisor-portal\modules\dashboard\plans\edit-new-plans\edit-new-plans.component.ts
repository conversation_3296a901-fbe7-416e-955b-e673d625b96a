import { CommonModule, Location } from '@angular/common';
import {
  <PERSON>mponent,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild, ChangeDetectorRef,
  inject
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { of, catchError } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FieldService } from 'src/app/shared/services/field/field.service';
import {
  MatCheckboxModule,
  MatCheckboxChange,
} from '@angular/material/checkbox';

import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { InputPremiumDynamicComponent } from 'src/app/shared/components/input-premium-dynamic/input-premium-dynamic.component';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import { CategoryModel } from 'src/app/shared/models/category/category-Company.model';
import {
  RegisterSectionFieldValueModel,
  UpdateSectionFieldValueModel,
  sectionFieldValueTableModel,
} from 'src/app/shared/models/plans';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { PlansService } from 'src/app/shared/services/plans/plans.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatDividerModule } from '@angular/material/divider';
import { PlanPremiumComponent } from 'src/app/advisor-portal/modules/dashboard/plans/plan-premium/plan-premium.component';
import { PlanPremiumValuesModel } from 'src/app/shared/models/plans/plan-premium-values.model';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MatRadioModule } from '@angular/material/radio';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { ProductPolicyTypeAndTerms } from 'src/app/shared/models/product';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';

@Component({
  selector: 'app-edit-new-plans',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatCheckboxModule,
    TableComponent,
    Modal2Component,
    ModalComponent,
    TranslateModule,
    PlanPremiumComponent,
    InputPremiumDynamicComponent,
    PreventionSqlInjectorDirective,
    MatRadioModule,
    ValidationInputFileDirective
  ],
  templateUrl: './edit-new-plans.component.html',
  styleUrls: ['./edit-new-plans.component.scss'],
})
export class EditNewPlansComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  formPlans: FormGroup = new FormGroup({});
  formSectionItem: FormGroup = new FormGroup({});
  dataPremiumValues: PlanPremiumValuesModel[] = [];
  insurers: InsuranceCompanyModel[] = [];
  category: CategoryModel[] = [];
  fkIdCategory: number = 0;
  fkIIdProduct: number = 0;
  pkIIdBusinessByCountry: number = 0;
  pkIIdPlan: number = 0;
  pkIdPlanPremiumValue: number = 0;
  planCreate: boolean = false;
  plansDataTable: any[] = [];
  titelModal: string = '';
  itemType: number = 0;
  editSectionFieldValue: boolean = false;
  actionInComponent: string = 'create';
  labelItemTypeDinamic: string = '';
  bPremiumYear: boolean = false;
  bPremiumMonthly: boolean = false;
  isCalculated: boolean = false;
  isShowDecimals: boolean = false;
  typePremiums: number = 0;
  planEdit: boolean = false;
  maxSizeInMb = 20;
  documentError: string | null = null;
  fileName: string = '';
  showBtnDelete: boolean = false;
  showContOneBtns: boolean = true;
  dataPremiums: any[] = [];
  typeFormatCoin: any = [{ format: "Pesos colombianos" }, { format: "Pesos mexicanos" }, { format: "Peso argentino" }, { format: "Dolar" }, { format: "Pesos chilenos" }, { format: "Colón costarricense" }, { format: "Córdoba de nicaragua" }, { format: "Real de Brasil" }, { format: "Guarani de paraguay " }, { format: "Lempira hondureño" }, { format: "Quetzal de guatemala" }]
  @ViewChild("child") childPremiums: any;

  @ViewChild('createEditSectionItemModal')
  createEditSectionItemModal?: TemplateRef<any>;
  @ViewChild('createPremiumModal') createPremiumModal?: TemplateRef<any>;
  estructPlansTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Plan.Category'),
      columnValue: 'vDescriptionSection',
    },
    {
      columnLabel: this._translateService.instant('Plan.NameItem'),
      columnValue: 'vSectionFieldName',
    },
    {
      columnLabel: this._translateService.instant('Plan.ItemType'),
      columnValue: 'vFieldTypeName',
    },
    {
      columnLabel: this._translateService.instant('Plan.Value'),
      columnValue: 'vValue',
      functionValue: (item: sectionFieldValueTableModel) =>
        this.parseFieldTypeName(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
    {
      columnLabel: this._translateService.instant('Product.Refresh'),
      columnValue: 'delete',
      columnIcon: 'refresh',
    },
  ];

  productPolicyTypeAndTerms: ProductPolicyTypeAndTerms = {
    dPolicyTypeValidityEnd: new Date(),
    dPolicyTypeValidityStart: new Date(),
    vPolicyTypeCode: '',
    bPolicyTypeVisibleInProductConfig: false,
    fkIIdPolicyType: 0,
    pkIIdProduct: 0,
    vPolicyTypeName: '',
  };

  private localStorageService = inject(LocalStorageService);

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    private _insuranceService: InsuranceService,
    public _fieldSvc: FieldService,
    private _planService: PlansService,
    private _location: Location,
    private _activatedRoute: ActivatedRoute,
    private _messageService: MessageService,
    private _settingService: SettingService,
    public _planPremiumDialog: MatDialog,
    public modalDialog: MatDialog,
    public _translateService: TranslateService,
    private changeDetectorRef: ChangeDetectorRef,
    private _msgSvc: MessageService,
    private _productService: ProductService,

  ) { }

  ngOnInit(): void {
    this.getDataSettingInit();
    this.getParamsUrl();
    this.getIdBusinessByCountry();
    this.initForm();
    this.getInsurersGlobal();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table business
      this.estructPlansTable[0].columnLabel =
        this._translateService.instant('Plan.Category');
      this.estructPlansTable[1].columnLabel =
        this._translateService.instant('Plan.NameItem');
      this.estructPlansTable[2].columnLabel =
        this._translateService.instant('Plan.ItemType');
      this.estructPlansTable[3].columnLabel =
        this._translateService.instant('Plan.Value');
      this.estructPlansTable[4].columnLabel =
        this._translateService.instant('Modify');
      this.estructPlansTable[5].columnLabel =
        this._translateService.instant('Product.Refresh');
    });

    this.getPolicyTypeAndTermsByProductId();
  }

  public updateWithoutFinishedDate(){
    const value = this.formPlans.get('bWithoutFinishDate')?.value
    if (value) {
        this.formPlans.get('dEndValidity')?.clearValidators()
        this.formPlans.get('dEndValidity')?.disable()
        this.formPlans.get('dEndValidity')?.setValue('')
      }
    else {
      this.formPlans.get('dEndValidity')?.setValidators([Validators.required])
      this.formPlans.get('dEndValidity')?.enable()
    }
    this.formPlans.get('dEndValidity')?.updateValueAndValidity()
  }

  /**
   * Retrieves the policy type and terms associated with a product by its ID
   * and configures the form based on the retrieved values. Disables date fields if the policy type is collective.
   */
  getPolicyTypeAndTermsByProductId() {

    this._productService
      .getPolicyTypeAndTermsByProductId(this.fkIIdProduct)
      .subscribe((result) => {
        this.productPolicyTypeAndTerms = result.result;

        // If the policy type code is 'COL' (Collective), disable and set date fields
        if (this.productPolicyTypeAndTerms.vPolicyTypeCode === 'COL') {
          // Disables the date fields in the form
          this.formPlans.get('dStartValidity')?.disable();
          this.formPlans.get('dEndValidity')?.disable();

          // Sets start and end validity dates from the product configuration
          this.formPlans
            .get('dStartValidity')
            ?.setValue(this.productPolicyTypeAndTerms.dPolicyTypeValidityStart);
          this.formPlans
            .get('dEndValidity')
            ?.setValue(this.productPolicyTypeAndTerms.dPolicyTypeValidityEnd);
        }
      });
  }


  initForm() {
    this.formPlans = this._fb.group({
      pkIIdPlan: [0],
      fkIIdInsuranceCompany: [null, [Validators.required]],
      fkIIdCategory: [null, [Validators.required]],
      fkIIdProduct: [this.fkIIdProduct, [Validators.required]],
      vName: ['', [Validators.required]],
      iValidity: [0, [Validators.required]],
      dStartValidity: [null, [Validators.required] ],
      dEndValidity: [null, [Validators.required]],
      bActive: [true, [Validators.required]],
      bActiveConoce: [true, [Validators.required]],
      bWithoutFinishDate: [false, [Validators.required]],
      bAsesor: [true],
      bCliente: [true],
      ilearnMore: [''],
      vLearnMoreLink: [''],
      vlearnMorePDF: [null],
      vFileName:[''],
      dPremiumYear: [''],
      dPremiumMonthly: [''],
      vFormatCoin:['']
    });
    this.formSectionItem = this._fb.group({
      fkIIdPlan: [0],
      fkIIdCategory: [0],
      fkIIdSectionProductField: [0],
      pkIIdSectionProductValue: [0],
      itemInComparator: { value: '', disabled: true },
      category: { value: '', disabled: true },
      vFieldTypeName: { value: '', disabled: true },
      vName: [],
      vDeductible: [],
      bisCheck: [true],
      bActiveConoce: [],
      ilearnMore: [],
      vLearnMoreLink: [],
      vlearnMorePDF: [],
      vFileName: []
    });
  }

  get formValid(): boolean {
    return this.formPlans.valid;
  }

  getParamsUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      this.fkIIdProduct = Number(params.pkIIdProduct);
      this.pkIIdPlan = Number(params.pkIIdPlan);
      if (this.pkIIdPlan !== 0 && this.pkIIdPlan) {
        this.getPlanTables(this.pkIIdPlan);
        this.getPlanById(this.pkIIdPlan);
        this.actionInComponent = 'edit';
      }
      this.getCategorysGlobal();
    });


  }

  getIdBusinessByCountry() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.pkIIdBusinessByCountry =
              response.enterprise.pkIIdBusinessByCountry;
          } else {
            this.getDataSettingInit();
          }
        }
      );
  }

  getInsurersGlobal() {
    this._insuranceService
      .getInsuranceCompanyByIdBusinessByCountry(this.pkIIdBusinessByCountry)
      .subscribe({
        next: (response) => {
          this.insurers = response.result;
        },
      });
  }

  getCategorysGlobal() {
    this._fieldSvc
      .getCategoryByProduct(this.fkIIdProduct)
      .subscribe({
      next: (response) => {
        this.category = response.result;

        if (this.fkIdCategory == null || this.fkIdCategory == 0) {
          this.formPlans.get('fkIIdCategory')?.setValue(0);
        } else {
          this.formPlans.get('fkIIdCategory')?.setValue(this.fkIdCategory);
        }

      },
    });
  }

  getPlanById(idPlan: number) {
    this._planService.getPlanById(idPlan).subscribe({
      next: (response) => {
        if (!response.error) {
          this.fkIdCategory = response.result.fk_i_idCategory;
          this.getCategorysGlobal();
          this.clearFieldsPlanPremiumValues();
          this.planEdit = true;
          this.formPlans.get('pkIIdPlan')?.setValue(response.result.pkIIdPlan);
          this.formPlans.get('bWithoutFinishDate')?.setValue(response.result.bWithoutFinishDate??false);
          this.formPlans
            .get('fkIIdInsuranceCompany')
            ?.setValue(response.result.fkIIdInsuranceCompany);
          this.formPlans
            .get('fkIIdProduct')
            ?.setValue(response.result.fkIIdProduct);
          this.formPlans.get('vName')?.setValue(response.result.vName);
          this.formPlans.get('iValidity')?.setValue(response.result.iValidity);
          this.formPlans
            .get('dStartValidity')
            ?.setValue(response.result.dStartValidity);
          this.formPlans
            .get('dEndValidity')
            ?.setValue(response.result.dEndValidity);
          this.formPlans.get('bActive')?.setValue(response.result.bActive);
          this.formPlans.get('bActiveConoce')?.setValue(response.result.bActiveConoce);
          this.formPlans.get('ilearnMore')?.setValue(response.result.iLearnMore);
          this.formPlans.get('vLearnMoreLink')?.setValue(response.result.vLearnMoreLink);
          this.formPlans.get('bAsesor')?.setValue(response.result.bAsesor ?? false);
          this.formPlans.get('bCliente')?.setValue(response.result.bCliente ?? false);
          this.formPlans.get('vFormatCoin')?.setValue(response.result.vFormatCoin);
          this.fileName = response.result.vLearnMorePDF || null;
          this.dataFieldsPlanPremiumValues(response.result.premiumValues);
          this.updateWithoutFinishedDate()
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  dataFieldsPlanPremiumValues(data: any[]) {
    if (data != undefined) {
      if (data.length > 0) {
        this.dataPremiumValues = data;
        this.changeDetectorRef.detectChanges();
        this.childPremiums?.dataFieldsPlanPremiumValues(data);
      } else this.dataPremiumValues = [];
    } else this.dataPremiumValues = [];
  }

  getPlanTables(idPlan: number) {
    this._planService.getPlanTables(idPlan).subscribe({
      next: (response) => {
        if (!response.error) {
          let newJson: any = {};
          newJson.result = response.result;
          this.plansDataTable = newJson;
          this.parseJson(newJson);
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  getSectionFieldValueById(idSectionProductField: number, idPlan: number) {
    this._planService
      .getSectionFieldValueById(idSectionProductField, idPlan)
      .subscribe({
        next: (response) => {
          if (!response.error) {
            this.itemType = response.result.pkiIdPlanTypeField;
            this.parseLabel(this.itemType);
            this.formSectionItem
              .get('itemInComparator')
              ?.setValue(response.result.vSectionFieldName);
            this.formSectionItem
              .get('category')
              ?.setValue(response.result.vDescriptionSection);
            this.formSectionItem
              .get('vFieldTypeName')
              ?.setValue(response.result.vFieldTypeName);
            this.formSectionItem.get('fkIIdPlan')?.setValue(this.pkIIdPlan);
            this.formSectionItem
              .get('fkIIdSectionProductField')
              ?.setValue(response.result.pkiIdSectionProductField);
            this.formSectionItem.get('vName')?.setValue(response.result.vText);
            this.formSectionItem
              .get('vDeductible')
              ?.setValue(response.result.vDeductible);
            if (response.result.bisCheck !== null) {
              this.formSectionItem
                .get('bisCheck')
                ?.setValue(response.result.bisCheck);
            } else {
              this.formSectionItem.get('bisCheck')?.setValue(true);
            }
            this.formSectionItem
              .get('pkIIdSectionProductValue')
              ?.setValue(response.result.pkiIdSectionProductValue);
          } else {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              response.message
            );
          }
        },
      });
  }

  controller(event: IconEventClickModel) {
    if (event.column === 'edit') {
      this.titelModal = 'Modificar ' + event.value.vSectionFieldName;
      this.getSectionFieldValueById(
        event.value.pkiIdSectionProductField,
        this.pkIIdPlan
      );
      if (event.value.pkiIdSectionProductValue) {
        this.editSectionFieldValue = true;
      } else {
        this.editSectionFieldValue = false;
      }

      this.openModal();
    }
    if (event.column === 'delete') {
      if (event.value.vValue) {
        this._messageService
          .messageConfirmationAndNegation(
            this._translateService.instant('Plan.DoyouwantRemoveBenefit'),
            '',
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.deleteSectionFieldValue(
                event.value.pkiIdSectionProductValue
              );
            }
          });
      } else {
        this._messageService.messageInfo(
          this._translateService.instant('Warning'),
          this._translateService.instant('Plan.NotInformationdelete')
        );
      }
    }
  }

  parseJson(json: any) {
    let array_data = Object.keys(json).map((key) => {
      let obj: any = {};
      obj[key] = json[key];
      return obj;
    });
    this.plansDataTable = array_data[0].result;
  }

  parseFieldTypeName(item: sectionFieldValueTableModel) {
    switch (item.vValue) {
      case null:
        return '<h5><span >-</span></h5>';
      case 'True':
        return (
          '<h5><span class="badge bg-success">' +
          this._translateService.instant('Plan.Apply') +
          '</span></h5>'
        );
      case 'False':
        return (
          '<h5><span class="badge bg-danger">' +
          this._translateService.instant('Plan.NotApply') +
          '</span></h5>'
        );
      default:
        return item.vValue;
    }
  }

  parseLabel(itemType: number) {
    switch (itemType) {
      case 3:
        this.labelItemTypeDinamic = this._translateService.instant('Plan.Text');
        break;
      case 4:
        this.labelItemTypeDinamic =
          this._translateService.instant('Plan.Limit');
        break;
      default:
        this.labelItemTypeDinamic =
          this._translateService.instant('Plan.Content');
        break;
    }
  }

  savePlan() {
    if (this.formValid) {

    //Se usa getRawValue() para obtener los valores de los campos del formulario así esten deshabilitados
      this._planService.registerPlan(this.formPlans.getRawValue()).subscribe({
        next: (response) => {
          if (!response.error) {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              response.message
            );
            this.pkIIdPlan = response.result;
            this.getPlanTables(response.result);
            this.planCreate = true;
            this.planEdit = true;
          } else {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              response.message
            );
          }
        },
      });
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('Warning'),
        this._translateService.instant('CheckTheFormIsFilledCorrectly')
      );
    }
  }

  saveSectionFieldValue() {
    let payload: RegisterSectionFieldValueModel = {
      bisCheck: this.formSectionItem.get('bisCheck')?.value,
      fkIIdPlan: this.pkIIdPlan,
      fkIIdSectionProductField: this.formSectionItem.get(
        'fkIIdSectionProductField'
      )?.value,
      vDeductible: this.formSectionItem.get('vDeductible')?.value,
      vName: this.formSectionItem.get('vName')?.value,
    };
    this._planService.registerSectionFieldValue(payload).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            response.message
          );
          this.getPlanTables(this.pkIIdPlan);
          this.modalDialog.closeAll();
          this.titelModal = '';
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  updatePlan() {

    //Se usa getRawValue() para obtener los valores de los campos del formulario así esten deshabilitados
    this._planService.updatePlan(this.formPlans.getRawValue()).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            response.message
          );
          this.goBack();
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  updateSectionFieldValue() {
    let payload: UpdateSectionFieldValueModel = {
      bisCheck: this.formSectionItem.get('bisCheck')?.value,
      pkIIdSectionProductValue: this.formSectionItem.get(
        'pkIIdSectionProductValue'
      )?.value,
      vDeductible: this.formSectionItem.get('vDeductible')?.value,
      vName: this.formSectionItem.get('vName')?.value,
    };

    this._planService.updateSectionFieldValue(payload).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            //this._translateService.instant(response.message)
            response.message
          );
          this.getPlanTables(this.pkIIdPlan);
          this.modalDialog.closeAll();
          this.titelModal = '';
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  completePlan() {
    if (this.actionInComponent === 'create') {
      this.savePlan();
    } else {
      this.updatePlan();
    }
  }

  completeSectionFieldValue() {
    if (this.editSectionFieldValue) {
      this.updateSectionFieldValue();
    } else {
      this.saveSectionFieldValue();
    }
  }

  openModal() {
    this.modalDialog.open(this.createEditSectionItemModal!);
  }

  openModalPremium(typePremium: number) {
    this.typePremiums = typePremium;
    let data: any = {};
    if (this.dataPremiumValues !== undefined) {
      if (this.dataPremiumValues.length > 0) {
        data = this.dataPremiumValues.find(
          (ele) => ele.fkIIdPremiumType == typePremium
        );
        if (data !== undefined) {
          this.pkIdPlanPremiumValue = data.fkIIdPlanPremiumValue;
          this.isCalculated = data.bIsCalculated;
        } else {
          this.pkIdPlanPremiumValue = 0;
          this.isCalculated = false;
        }
      } else {
        this.clearDataModalPremiumValues();
      }
    } else {
      this.clearDataModalPremiumValues();
    }
    const dialogRef = this._planPremiumDialog.open(this.createPremiumModal!, {
      width: '90vw',
      height: '100vh',
    });
  }

  clearDataModalPremiumValues() {
    this.pkIdPlanPremiumValue = 0;
    this.isCalculated = false;
  }

  eventCloseModal(event: boolean) {
    console.log('se cerró el modal');
    this.formSectionItem.reset();
    this.formSectionItem.get('bisCheck')?.setValue(false);
    this.titelModal = '';
  }

  onChangeCheckPremiums(event: any) {
    this.updatePlanPremiumDisable(event.typePremium, event.isCheck);
  }

  goBack() {
    this._location.back();
    this.formPlans.reset();
  }

  updatePlanPremiumDisable(idPremiumType: number, isActive: boolean) {
    this._planService
      .updatePlanPremiumDisable(this.pkIIdPlan, idPremiumType, isActive)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (!response.error) {
            this.getPlanById(this.pkIIdPlan);
          } else {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              response.message
            );
          }
        }
      });
  }

  deleteSectionFieldValue(idSectionField: number) {
    this._planService.deleteSectionFieldValue(idSectionField).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            response.message
          );
          this.getPlanTables(this.pkIIdPlan);
          this.modalDialog.closeAll();
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  getDataSettingInit() {
    this.pkIIdBusinessByCountry = Number(
      this.localStorageService.getItem('businessByCountryPlan')
    );
  }

  clearFieldsPlanPremiumValues() {
    this.formPlans.get('dPremiumMonthly')?.setValue('');
    this.bPremiumMonthly = false;
    this.formPlans.get('dPremiumYear')?.setValue('');
    this.bPremiumYear = false;
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      if (file.type !== 'application/pdf') {
        return this._msgSvc.messageError('Solo se permiten archivos PDF.');
      } else if (file.size > 20 * 1024 * 1024) {  // Límite de 20 MB
        return this._msgSvc.messageError('La imagen excede el peso máximo(100MB)');
      } else {
        const files: FileList = event.target.files;
        const name = event.target.files[0].name;
        // Convierte el archivo PDF a Base64
        this.convertPdfToBase64(file)
          .then((base64: string) => {
            this.formPlans.get('vlearnMorePDF')?.setValue(base64);
          })
          .catch((error) => {
            this._msgSvc.messageError(error);
          })
          .finally(() => {
            this.showBtnDelete = true;
            this.fileName = name;
          });
        this.formPlans.get('vFileName')?.setValue(name);
      }
    }
  }
  onDragOver(event: DragEvent) {
    event.preventDefault();
  }
  onFileDropped(event: DragEvent) {
    event.preventDefault();
    const file = event.dataTransfer?.files[0];
    if (file) {
      this.validateAndUpload(file);
    }
  }
  validateAndUpload(file: File) {
    const maxSizeInBytes = this.maxSizeInMb * 1024 * 1024;
    if (file.type !== 'application/pdf') {
      return;
    }
    if (file.size > maxSizeInBytes) {
      return;
    }
  }
  convertPdfToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String);
      };
      reader.onerror = (error) => {
        this._msgSvc.messageError('Error al leer el archivo: ' + error);
      };
      reader.readAsDataURL(file);
    });
  }

  onKeyup(event: KeyboardEvent): void {
    const inputValue = (event.target as HTMLInputElement).value;

    if (this.containsForbiddenCharacters(inputValue) || this.containsForbiddenWords(inputValue)) {
      this._messageService.messageWaring(
        this._translateService.instant('SqlInjectorTitle'),
        'and ;  null . / http ¡ execute ! > console < = http - script.js -- www *  cmd  \\ ping {} ftp [] () & webconfig'
      );
      (event.target as HTMLInputElement).value = '';
    }
  }

  private containsForbiddenCharacters(inputValue: string): boolean {
    const forbiddenCharacters = [ '\\'];

    for (const char of forbiddenCharacters) {
      if (inputValue.includes(char)) {
        return true; // Si encuentra algún carácter no permitido
      }
    }
    return false; // Todo ok, no hay caracteres no permitidos
  }

  // Validar palabras no permitidas
  private containsForbiddenWords(inputValue: string): boolean {
    const forbiddenWords = [
      'null', 'http', 'script', '.js', 'www', 'ftp', 'cmd', 'execute', 'webconfig', 'console', 'ping',
    ];

    const inputValueParser = inputValue.toLowerCase(); // Convertimos a minúsculas para asegurar que no sea sensible a mayúsculas
    for (const word of forbiddenWords) {
      if (inputValueParser.includes(word)) {
        return true; // Si encuentra alguna palabra no permitida
      }
    }
    return false; // Todo ok, no hay palabras no permitidas
  }
}
