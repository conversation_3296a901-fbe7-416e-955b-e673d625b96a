import { Routes } from "@angular/router";
import { FormsComponent } from "./forms.component";


export default [
 {
  path:'',
  component: FormsComponent,
  children: [
    {path: '', loadComponent:()=> import('./filter-form-module/filter-form-module.component').then(c=>c.FilterFormModuleComponent)},
    {path: 'all', loadComponent:()=> import('./all-forms/all-forms.component').then(c=>c.AllFormsComponent)},
    {path: 'new', loadComponent:()=> import('./edit-new-form/edit-new-form.component').then(c=>c.EditNewFormComponent)},
    {path: 'modify/:id', loadComponent:()=> import('./edit-new-form/edit-new-form.component').then(c=>c.EditNewFormComponent)},
  ]
 }
] as Routes;