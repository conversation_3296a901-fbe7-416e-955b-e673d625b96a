<!-- Titulo Creación masiva de estados-->
<div class="row">
  <h5 class="title mb-1">{{ "BulkUpload.Massives.MassCreationStates.Title" | translate }}</h5>
  <p class="description">
    {{
      "BulkUpload.Massives.MassCreationStates.SelectTasksToChangeStatus" | translate
    }}
  </p>
</div>

<div class="row">
  <div class="col-md-11 col-sm-12 mb-3">
    <form [formGroup]="formFilter">
      <div class="row">
        <!-- Proceso  -->
        <div class="col-md-3 col-sm-12 mb-3">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.Process" | translate }}</mat-label>
            <mat-select formControlName="fkIIdprocess">
              <mat-option
                *ngFor="let option of listProcess"
                [value]="option.pkIIdProcessFamily"
                >{{ option.vNameProcess }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
        <!-- Producto -->
        <div class="col-md-3 col-sm-12 mb-3">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.Product" | translate }}</mat-label>
            <mat-select formControlName="fkIIdProduct">
              <mat-option
                *ngFor="let option of listProduct"
                [value]="option.pkIIdProductModule"
                >{{ option.vName }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
        <!-- Etapa -->
        <div class="col-md-3 col-sm-12 mb-3">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.Stage" | translate }}</mat-label>
            <mat-select formControlName="fkIdStage">
              <mat-option
                *ngFor="let stage of stageList"
                [value]="stage.pkIIdStage"
                >{{ stage.vNameStage }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
        <!-- Estado -->
        <div class="col-md-3 col-sm-12 mb-3">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.State" | translate }}</mat-label>
            <mat-select formControlName="fkIdState">
              <mat-option
                *ngFor="let status of statusList"
                [value]="status.pkIIdStageByState"
                >{{ status.vState }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </form>
  </div>

  <!-- Filtrar -->
  <div class="col-md-1 col-sm-12 mb-3">
    <!-- boton que abre el modal de filtrar -->
    <button
      class="mr-2 mt-1 w-100"
      type="button"
      color="primary"
      (click)="openFilterDialog()"
      mat-raised-button
    >
      {{ "Filter" | translate }}
      <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
    </button>
  </div>
</div>

<ng-container class="row">
  <div class="row">
    <div class="col-md-12">
      <!-- input de busqueda en la tabla -->
      <mat-form-field class="w-100">
        <mat-label>
          {{ "Search" | translate }}
        </mat-label>
        <input
          matInput
          type="text"
          class="form-control"
          (keyup.enter)="search(keyword)"
          [(ngModel)]="keyword"
          placeholder="{{ 'Search' | translate }}"
        />
        <mat-icon class="hand click" (click)="search(keyword)" matSuffix
          >search</mat-icon
        >
      </mat-form-field>
    </div>
  </div>
  <div class="row">
    <!-- datatable estados -->
    <div class="row mt-2">
      <div class="col-md-12" class="w-100">
        <app-table
          [displayedColumns]="estructTableState"
          [data]="dataTableState"
          [IsStatic]="false"
          [pageIndex]="pageIndex"
          [pageSize]="pageSize"
          [amountRows]="amountRows"
          (pageChanged)="onPageChange($event)"
          (iconClick)="controllerAns($event)"
        ></app-table>
      </div>
    </div>

    <div class="row mt-2">
      <b>
        {{ "BulkUpload.Massives.MassCreationStates.SelectedTasks" | translate }} {{ idsTask.length }}
      </b>
    </div>
  </div>

</ng-container>

<div class="row">
  <form [formGroup]="formChangeState">
    <div class="row">
      <!-- input de cambio sin información y cambio con plantilla para agregar información -->
      <div class="col-md-12 mt-3 mb-3 p-0">
        <mat-radio-group formControlName="isChangeInformation">
          <mat-radio-button [value]="false">
            {{'BulkUpload.Massives.MassCreationStates.ChangeWithoutAdditionalInformation'  | translate}}
          </mat-radio-button>
          <mat-radio-button [value]="true">
            {{'BulkUpload.Massives.MassCreationStates.ChangeWithTemplateToAddInformation' | translate}}
          </mat-radio-button>
        </mat-radio-group>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <h5 class="title mb-1">{{ "BulkUpload.Massives.MassCreationStates.NewState" | translate }}</h5>
        <p class="description">
          {{ "BulkUpload.Massives.MassCreationStates.SelectedTasksStatus" | translate }}
        </p>
      </div>
    </div>

    <div class="row">
      <!-- Etapa -->
      <div class="col-md-6 col-sm-12 mb-1">
        <mat-form-field class="w-100">
          <mat-label>{{ "BulkUpload.Massives.MassCreationStates.NewTaskStage" | translate }}</mat-label>
          <mat-select formControlName="fkIdStage">
            <mat-option
              *ngFor="let stage of stageList"
              [value]="stage.pkIIdStage"
              >{{ stage.vNameStage }}</mat-option
            >
          </mat-select>

          <mat-error
              *ngIf="
                utilsService.isControlHasError(
                  formChangeState,
                  'fkIdStage',
                  'required'
                )
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
        </mat-form-field>
      </div>
      <!-- Estado -->
      <div class="col-md-6 col-sm-12 mb-1">
        <mat-form-field class="w-100">
          <mat-label>{{ "BulkUpload.Massives.MassCreationStates.NewTaskStatus" | translate }}</mat-label>
          <mat-select formControlName="fkIdState">
            <mat-option
              *ngFor="let status of statusListChange"
              [value]="status.pkIIdStageByState"
              >{{ status.vState }}</mat-option
            >
          </mat-select>

          <mat-error
              *ngIf="
                utilsService.isControlHasError(
                  formChangeState,
                  'fkIdState',
                  'required'
                )
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12 p-0">
        <mat-checkbox formControlName="isDisabledCommunications">
          {{ "BulkUpload.Massives.MassCreationStates.DisableCommunications" | translate }}
        </mat-checkbox>
      </div>
      <div class="col-md-12 p-0">
        <mat-checkbox formControlName="isDisabledDependencies">
          {{ "BulkUpload.Massives.MassCreationStates.DisableDependencies" | translate }}
        </mat-checkbox>
      </div>
    </div>

    <ng-container *ngIf="formChangeState.value.isChangeInformation">
      <div class="cont-fieles mt-3">
        <!-- Subtitulo Plantilla de tarea-->
        <div class="">
          <h5 class="title mb-1">
            {{
              "BulkUpload.Massives.MassCreationTasks.TaskTemplate" | translate
            }}
          </h5>
          <p class="description">
            {{
              "BulkUpload.Massives.MassCreationTasks.TaskTemplateD" | translate
            }}
          </p>
        </div>
        <div class="cont-template">
          <div class="cont-info-file">
            <p class="m-0 p-0">{{ templateName }}</p>
            <span class="description">25 MB</span>
          </div>
          <div class="cont-download-icon">
            <span (click)="downloadTemplate()" class="material-symbols-outlined click">
              download
          </span>
          </div>
        </div>
        <!-- Subtitulo Carga de plantilla-->
        <div class="mt-3">
          <h5 class="title mb-1">
            {{
              "BulkUpload.Massives.MassCreationTasks.LoadingTemplate"
                | translate
            }}
          </h5>
          <p class="description">
            {{
              "BulkUpload.Massives.MassCreationTasks.LoadingTemplateD"
                | translate
            }}
          </p>
        </div>
        <div class="cont-template" *ngIf="uploadedFile.length > 0">
          <div class="cont-info-file">
            <p class="m-0 p-0">{{ fileName }}</p>
            <span class="description">25 MB</span>
          </div>
          <div class="cont-download-icon">
            <span class="material-symbols-outlined click" (click)="deleteFile()"> delete </span>
          </div>
        </div>
        <div class="cont-upload" *ngIf="uploadedFile.length === 0">
          <app-drag-drop-upload
            [fileAccept]="'.xlsx'"
            [isOneFile]="true"
            [message]="''"
            (saveFile)="getFiles($event)"
          ></app-drag-drop-upload>
        </div>
      </div>
    </ng-container>
  </form>
</div>

<div class="d-flex justify-content-center mt-3">
  <a class="label-button" mat-button (click)="goBackMassive()"
    ><span>{{
      "BulkUpload.Massives.MassCreationPolicy.GoOutToMass" | translate
    }}</span>
    <mat-icon fontIcon="arrow_back"></mat-icon>
  </a>
  <button
  type="button"
  class="w-auto"
  [disabled]="
    (!formChangeState.value.isChangeInformation && idsTask.length === 0) ||
    !formChangeState.valid ||
    (formChangeState.value.isChangeInformation && uploadedFile.length === 0)
  "
  mat-raised-button
  color="primary"
  (click)="validFormChange()"
>
  {{ "BulkUpload.Massives.MassCreationStates.ChangeStatus" | translate }}
</button>


</div>

<!-- modal filtros -->
<ng-template #filtersModal>
  <app-modal2
    [titleModal]="'Filter' | translate"
    [showCancelButtonBelow]="true"
  >
    <ng-container body>
      <form [formGroup]="formFilterModal">

        <!-- ID de tarea -->
        <div class="col-md-12 mb-2">
          <mat-form-field class="w-100" appearance="fill">
            <mat-label>
              {{ "BulkUpload.Massives.MassCreationStates.TaskId" | translate }}
            </mat-label>
            <input matInput type="number" formControlName="idState" />
          </mat-form-field>
        </div>


        <!-- Asignado a -->
        <div class="col-md-12 mb-2">
          <mat-form-field appearance="outline" class="select-look w-50 m-auto w-100">
            <mat-label>
              {{ "BulkUpload.Massives.MassCreationStates.AssignedTo" | translate }}
            </mat-label>
            <mat-select formControlName="IdAssignedUser" multiple>
              <mat-option
                *ngFor="let user of listUserAsigment"
                [value]="user.idResponsableFirst"
              >
                {{ user.asignado }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>


        <div class="col-md-12 mb-2">
          <h5>{{ "BulkUpload.Massives.MassCreationStates.DateRange" | translate }}</h5>
        </div>

        <!-- Fecha inicio -->
        <div class="col-md-12">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <input
              matInput
              [matDatepicker]="picker"
              formControlName="StartDate"
              [placeholder]="'BulkUpload.Massives.MassCreationStates.StartDate' | translate"
            />
            <mat-datepicker-toggle
              matSuffix
              [for]="picker"
            ></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>
        <!-- Fecha final -->
        <div class="col-md-12 mb-2">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <input
              matInput
              [matDatepicker]="pickerEnd"
              formControlName="EndDate"
              [placeholder]="'BulkUpload.Massives.MassCreationStates.EndDate' | translate"
            />
            <mat-datepicker-toggle
              matSuffix
              [for]="pickerEnd"
            ></mat-datepicker-toggle>
            <mat-datepicker #pickerEnd></mat-datepicker>
          </mat-form-field>
        </div>
      </form>
    </ng-container>

    <!-- Botón Borrar filtros -->
    <ng-container customButtonCenter>
      <button
        class="btn-custom w-100"
        type="button"
        mat-raised-button
        (click)="cleanFilterForm()"
      >
        <strong>{{
          "PolicyConfiguration.Filter.DeleteFilters" | translate
        }}</strong>
      </button>
    </ng-container>
    <!-- Botón Aplicar -->
    <ng-container customButtonRight>
      <button
        class="w-auto"
        type="button"
        mat-raised-button
        color="primary"
        (click)="applyFilters()"
      >
        {{ "Apply" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
