<form [formGroup]="form">´

  <!-- No se muestra cuando es un formulario de flujo de cotización -->
  <div class="row mb-2" *ngIf="itsJustFields == false">
    <div class="col-12 col-md-9">
      <div class="row mb-2">
        <h3 class="col-md-12">
          {{ "Product.FormData" | translate }}
        </h3>
      </div>
      <mat-slide-toggle class="mb-3" formControlName="b_Active">
        {{ "Product.ActiveForm" | translate }}
      </mat-slide-toggle>
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ "Product.FormName" | translate }}
        </mat-label>
        <input matInput formControlName="v_Name" PreventionSqlInjector/>
        <mat-error
          *ngIf="utilsSvc.isControlHasError(form, 'v_Name', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ "Product.NameInSystem" | translate }}
        </mat-label>
        <input matInput formControlName="v_NameDB" [readonly]="true" PreventionSqlInjector/>
        <mat-error
          *ngIf="utilsSvc.isControlHasError(form, 'v_NameDB', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
      <button [disabled]="idForm != 0" class="mx-3 mb-2" type="button" mat-raised-button
        (click)="open('copyParameters')">
        {{ "Product.ConfigureParameters" | translate }}
        <mat-icon iconPositionEnd fontIcon="file_copy"></mat-icon>
      </button>
    </div>
    <div class="col-12 col-md-3">
      <div class="d-flex flex-column">
        <button class="mx-3 mb-2" type="button" mat-raised-button color="primary" (click)="saveForm()">
          {{
          idForm != 0
          ? ("Product.UpdateForm" | translate)
          : ("Product.SaveForm" | translate)
          }}
          <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
        </button>
        <button [disabled]="idForm == 0" class="m-3" type="button" mat-raised-button color="primary"
          (click)="open('previewForm')">
          {{ "Product.Preview" | translate }}
          <mat-icon iconPositionEnd fontIcon="remove_red_eye"></mat-icon>
        </button>
        <button [disabled]="idForm == 0" class="m-3" type="button" mat-raised-button (click)="deleteForm()">
          {{ "Product.DeleteForm" | translate }}
          <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- No se muestra cuando es un formulario de flujo de cotización -->
  <div *ngIf="idForm != 0 && itsJustFields == false" class="row mb-2">
    <div class="col-12 col-md-12">
      <div class="row mb-2">
        <h3 class="col-md-12">
          {{ "Product.Tabs" | translate }}
        </h3>
      </div>
      <app-table [displayedColumns]="estructTable" [data]="dataTableTab" (iconClick)="EditTab($event)"></app-table>
      <button class="mx-3 mb-2" type="button" mat-raised-button color="primary"
        (click)="open('editNewTabModal'); isEditingTab = false">
        {{ "Add" | translate }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
  </div>

  <!-- No se muestra cuando es un formulario de flujo de cotización -->
  <div *ngIf="idForm != 0 && itsJustFields == false" class="row mb-2">
    <div class="col-12 col-md-12">
      <div class="row mb-2">
        <h3 class="col-md-12">
          {{ "Product.Sections" | translate }}
        </h3>
      </div>
      <app-table [displayedColumns]="estructTableSections" [data]="dataTableSection"
        (iconClick)="EditSection($event)"></app-table>
      <button class="mx-3 mb-2" type="button" mat-raised-button color="primary"
        (click)="open('editNewSectionModal'); isEditingSection = false">
        {{ "Add" | translate }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
  </div>

  <div *ngIf="idForm != 0" class="row mb-2">
    <div class="col-12 col-md-12">

  <!-- No se muestra cuando es un formulario de flujo de cotización -->
      <div class="row mb-2" *ngIf="itsJustFields == false">
        <h3 class="col-md-12">
          {{ "Product.Fields" | translate }}
        </h3>
      </div>
      <app-table
        [displayedColumns]="estructTableFields"
        [data]="dataTableFields"
        (iconClick)="EditField(true, $event)"
      ></app-table>
      <button
        class="mx-3 mb-2"
        type="button"
        mat-raised-button
        color="primary"
        (click)="open('editNewFieldModal'); isEditingField = false"
      >
        {{ "Add" | translate }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
  </div>
</form>
<form [formGroup]="formQuoteProduct" *ngIf="!itsJustFields">
  <div>
    <h5 class="fw-bold mb-2">
      {{ "Product.ButtonContinue" | translate }}
    </h5>
    <mat-label>
      {{ "Product.ButtonContinueText" | translate }}
    </mat-label>
    <mat-form-field appearance="outline" class="w-100 mb-2 mt-2">
      <mat-label>
      {{ "Product.Text" | translate }}
      </mat-label>
      <input matInput formControlName="v_Title" PreventionSqlInjector/>
    </mat-form-field>
  </div>
</form>

<ng-template #editNewTabModal>
  <app-modal2 [showCancelButtonBelow]="false" [titleModal]="
      isEditingTab
        ? ('Product.UpdateTab' | translate)
        : ('Product.NewTab' | translate)
    ">
    <ng-container body>
      <form [formGroup]="formTab">
        <mat-slide-toggle class="mb-3" formControlName="b_Active">
          {{ "Product.ActiveForm" | translate }}
        </mat-slide-toggle>
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{ "Product.TabName" | translate }}
          </mat-label>
          <input matInput formControlName="v_Name" PreventionSqlInjector/>
          <mat-error
            *ngIf="utilsSvc.isControlHasError(form, 'v_Name', 'required')"
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{ "Product.NameInDataBase" | translate }}
          </mat-label>
          <input matInput formControlName="v_NameDB" [readonly]="true" PreventionSqlInjector/>
          <mat-error
            *ngIf="utilsSvc.isControlHasError(form, 'v_NameDB', 'required')"
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="select-look w-100">
          <mat-label>
            {{ "Product.Order" | translate }}
          </mat-label>
          <mat-select formControlName="i_Order" id="i_Order" required>
            <mat-option *ngFor="let order of orderListTab" [value]="order">
              {{ order }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </form>
    </ng-container>
     <!-- botones -->
     <ng-container customButtonRight>
      <a class="w-auto mr-3" mat-button (click)="closeModal()"><span class="label-button">{{'Cancel'|
        translate}}</span></a>

      <button *ngIf="isEditingTab" class="mx-3 mb-2" type="button" mat-raised-button (click)="deleteTab()">
        {{ ["Product.DeleteTab" | translate] }}
        <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
      </button>
      <button class="mx-3 mb-2" type="button" mat-raised-button color="primary" (click)="saveTab()">
        {{
        isEditingTab
        ? ["Product.UpdateTab" | translate]
        : ["FormConfiguration.TabModule.AddTabButton" | translate]
        }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

<ng-template #editNewSectionModal>
  <app-modal2 [showCancelButtonBelow]="false" [titleModal]="
      isEditingSection
        ? ('Product.UpdateSection' | translate)
        : ('Product.NewSection' | translate)
    ">
    <ng-container body>
      <form [formGroup]="formSection">
        <mat-slide-toggle class="mb-3" formControlName="b_Active">
          {{ "Product.ActiveForm" | translate }}
        </mat-slide-toggle>
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{ "Product.SectionName" | translate }}
          </mat-label>
          <input matInput formControlName="v_Name" PreventionSqlInjector/>
          <mat-error
            *ngIf="utilsSvc.isControlHasError(form, 'v_Name', 'required')"
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="select-look w-100">
          <mat-label>
            {{ "Product.TabWhichBelongs" | translate }}
          </mat-label>
          <mat-select formControlName="fk_i_idTab" id="fk_i_idTab" required>
            <mat-option *ngFor="let item of dataTableTab" [value]="item.pkIIdTab">
              {{ item.vName }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="select-look w-100">
          <mat-label>
            {{ "Product.Order" | translate }}
          </mat-label>
          <mat-select formControlName="i_Order" id="i_Order" required>
            <mat-option *ngFor="let order of orderListSection" [value]="order">
              {{ order }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </form>
    </ng-container>
    <!-- botones -->
    <ng-container customButtonRight>
      <a class="w-auto mr-3" mat-button (click)="closeModal()"><span class="label-button">{{'Cancel'|
        translate}}</span></a>

      <button *ngIf="isEditingSection" class="mx-3 mb-2" type="button" mat-raised-button (click)="deleteSection()">
        {{ ["Product.DeleteSection" | translate] }}
        <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
      </button>
      <button class="mx-3 mb-2" type="button" mat-raised-button color="primary" (click)="saveSection()">
        {{
        isEditingSection
        ? ["Product.UpdateSection" | translate]
        : ["Product.AddSection" | translate]
        }}
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

<ng-template #editNewFieldModal>
  <app-modal2 [showCancelButtonBelow]="false" [titleModal]="
      isEditingField
        ? ('Product.UpdateField' | translate)
        : ('Product.NewField' | translate)
    ">
    <ng-container body>
      <mat-tab-group class="custom-tab-group">
          <mat-tab
          [label]="'Product.Characteristics' | translate"
          >
            <div class="row mb-2">
              <div class="border col-md-12 col-sm-12">
                <form [formGroup]="formField">
                  <div class="row">
                    <mat-checkbox formControlName="bUsingExistent" class="my-4">
                      {{ "Product.ConfigureExistingField" | translate }}
                    </mat-checkbox>
                    <mat-form-field *ngIf="formField.get('bUsingExistent')?.value" appearance="outline"
                        class="select-look w-100">
                        <mat-label>
                          {{ "Product.FieldType" | translate }}
                        </mat-label>
                        <mat-select formControlName="fkIdFieldExistent" id="idField">
                            <mat-option *ngFor="let item of allFields" [value]="item.pkIIdField">
                                {{ item.vNameField }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                  </div>
                  <mat-checkbox formControlName="bIsInheritedField" class="my-4" *ngIf="itsJustFields == true">
                      {{ "Product.InheritedField" | translate }}
                  </mat-checkbox>
                  <mat-form-field *ngIf="formField.get('bIsInheritedField')?.value" appearance="outline"
                      class="select-look w-100">
                      <mat-label>
                          {{ "Product.Fields" | translate }}
                      </mat-label>
                      <mat-select formControlName="fkIdInheritedField" >
                          <mat-option *ngFor="let item of allFieldsQuote" [value]="item.pkIIdField">
                              {{ item.vNameField }}
                          </mat-option>
                      </mat-select>
                  </mat-form-field>

                  <!-- Sólo para formulario de flujo de cotización -->
                  <div class="row" *ngIf="itsJustFields == true">
                    <mat-checkbox formControlName="bInsuranceCompany" class="my-4">
                      {{ "Product.ConfigureInsucanceCompanies" | translate }}
                    </mat-checkbox>
                    <mat-form-field
                      *ngIf="formField.get('bInsuranceCompany')?.value"
                      appearance="outline"
                      class="select-look w-100"
                    >
                      <mat-label>
                        {{ "Product.InsuranceCompany" | translate }}
                      </mat-label>
                      <mat-select
                        formControlName="fkIIdInsuranceCompany"
                        id="idInsurance"
                      >
                        <mat-option
                          *ngFor="let item of insuranceCompanies"
                          [value]="item.pkIIdInsuranceCompanies"
                        >
                          {{ item.vName }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>

                  <mat-slide-toggle class="my-4" formControlName="bActive">
                    {{ "Product.ActiveForm" | translate }}
                  </mat-slide-toggle>
                  <mat-form-field appearance="outline" class="select-look w-100">
                    <mat-label>
                      {{ "Product.FieldType" | translate }}
                    </mat-label>
                    <mat-select formControlName="fkIIdFieldType" id="fkIIdFieldType"  (ngModelChange)="resetFieldSetDefaultValue()" required>
                      <mat-option *ngFor="let item of fieldTypes" [value]="item.pkIIdFieldType">
                        {{ item.vName }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>
                      {{ "Product.FieldName" | translate }} (visible)
                    </mat-label>
                    <input matInput formControlName="vNameField" PreventionSqlInjector/>
                    <mat-error
                      *ngIf="utilsSvc.isControlHasError(form, 'vNameField', 'required')"
                    >
                      {{ "ThisFieldIsRequired" | translate }}
                    </mat-error>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>
                      {{ "Product.NameInDataBase" | translate }}
                    </mat-label>
                    <input matInput formControlName="vNameFieldDb" [readonly]="true" PreventionSqlInjector/>
                    <mat-error
                      *ngIf="
                        utilsSvc.isControlHasError(form, 'vNameFieldDb', 'required')
                      "
                    >
                      {{ "ThisFieldIsRequired" | translate }}
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="select-look w-100 mb-2">
                    <mat-label>
                      {{ "Product.Order" | translate }}
                    </mat-label>
                    <mat-select formControlName="iOrder" id="iOrder" required>
                      <mat-option *ngFor="let order of orderListFields" [value]="order">
                        {{ order }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="w-100 mb-2">
                    <mat-label>
                      {{ "Product.Description" | translate }}
                    </mat-label>
                    <input matInput formControlName="vDescription" PreventionSqlInjector/>
                    <mat-error
                      *ngIf="
                        utilsSvc.isControlHasError(form, 'vDescription', 'required')
                      "
                    >
                      {{ "ThisFieldIsRequired" | translate }}
                    </mat-error>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="select-look w-100">
                    <mat-label>
                      {{ "Product.Tab" | translate }}
                    </mat-label>
                    <mat-select formControlName="idTab" (ngModelChange)="updateSectionInfo()" required>
                      <mat-option *ngFor="let item of dataTableTab" [value]="item.pkIIdTab">
                        {{ item.vName }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="select-look w-100">
                    <mat-label>
                      {{ "Product.Section" | translate }}
                    </mat-label>
                    <mat-select formControlName="idSection" id="idSection" required>
                      <mat-option *ngFor="let item of selectedTabSections" [value]="item.pkIIdSection">
                        {{ item.vName }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <p> <strong>{{ "FormConfiguration.Fields.FieldFeatures" | translate }}</strong> </p>

                  <mat-checkbox formControlName="bRequired" class="my-4">
                    {{ "Product.Required" | translate }}
                  </mat-checkbox>

                  <!-- Control to activate or deactivate the visibility of uploaded documents. -->
                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments" formControlName="bIsVisibleInQueryModules" class="my-4">
                    {{ "Product.IsVisibleInQueryModules" | translate }}
                  </mat-checkbox>
                  <mat-checkbox formControlName="bisKeyField" class="my-4">
                    {{ "Product.FieldKey" | translate }}
                  </mat-checkbox>
                  <mat-checkbox formControlName="bHelpText" class="my-4">
                    {{ "Product.HelpText" | translate }}
                  </mat-checkbox>
                  <mat-checkbox formControlName="bIsInPdf" class="my-4">
                    {{ "Product.VisibleInPDF" | translate }}
                  </mat-checkbox>
                  <mat-checkbox formControlName="bIsReadonly" class="my-4">
                    {{ "Product.NoEditing" | translate }}
                  </mat-checkbox>
                  <mat-checkbox formControlName="bIsEncrypted" class="my-4">
                    {{ "Product.Encrypted" | translate }}
                  </mat-checkbox>
                  <mat-checkbox formControlName="bIsPolicy" class="my-4">
                    {{ "FormConfiguration.Fields.PolicyField" | translate }}
                </mat-checkbox>
                <mat-checkbox formControlName="bIsDependent" class="my-4">
                  {{ "Product.DependentField" | translate }}
                </mat-checkbox>
                <mat-checkbox formControlName="bIsPerson" class="my-4">
                  {{ "FormConfiguration.Fields.PersonField" | translate }}
                </mat-checkbox>
                <mat-checkbox  *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                formField.get('fkIIdFieldType')?.value === typeField.Numeric ||  formField.get('fkIIdFieldType')?.value === typeField.Date ||
                formField.get('fkIIdFieldType')?.value === typeField.Text ||  formField.get('fkIIdFieldType')?.value === typeField.DropDownList || formField.get('fkIIdFieldType')?.value === typeField.Decimal || formField.get('fkIIdFieldType')?.value === typeField.Money" formControlName="bIsSearch" class="my-4">
                  {{ "FormConfiguration.Fields.SearchField" | translate }}
                </mat-checkbox>
            <mat-checkbox formControlName="bIsHasDefaultValue" class="my-4"
                *ngIf="[typeField.Numeric, typeField.Text, typeField.TextArea, typeField.Date, typeField.Decimal, typeField.Alphanumeric].includes(formField.get('fkIIdFieldType')?.value)"
            >
              {{ "FormConfiguration.Fields.IsHasDefaultValue" | translate }}
            </mat-checkbox>

            <mat-checkbox formControlName="bIsMask" class="my-4"
                *ngIf="[typeField.Numeric, typeField.Text, typeField.Alphanumeric].includes(formField.get('fkIIdFieldType')?.value)">
              {{ "FormConfiguration.Fields.IsMakValue" | translate }}
            </mat-checkbox>
            <mat-checkbox formControlName="bIsExternalLink" class="my-4">
              {{ "FormConfiguration.Fields.IsExternalLinktValue" | translate }}
            </mat-checkbox>
            <mat-checkbox formControlName="bIsFromUrl" class="my-4">
              {{ "Product.IsFromUrl" | translate }}
            </mat-checkbox>

            <mat-checkbox formControlName="bIsInvisible" class="my-4">
              {{ "FormConfiguration.Fields.InvisibleField" | translate }}
            </mat-checkbox>

            <mat-checkbox formControlName="bIsGetdate" class="my-4" *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date">
              {{"FormConfiguration.Fields.GetDateActual" | translate }}
            </mat-checkbox>

            <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.DropDownList" formControlName="bIsMultiple" class="my-4">
              {{ "Selección multiple" | translate }}
            </mat-checkbox>

            <div class="form-group" *ngIf="formField.get('bIsInvisible')?.value">
              <mat-radio-group formControlName="iElementHideField">
                <mat-radio-button [value]="1">{{ "FormConfiguration.Fields.IsPortalClient" | translate }}</mat-radio-button>
                <mat-radio-button [value]="2">{{ "FormConfiguration.Fields.IsPortalAdvisor" | translate }}</mat-radio-button>
                <mat-radio-button [value]="3">{{ "FormConfiguration.Fields.IsHideBoth" | translate }}</mat-radio-button>
              </mat-radio-group>
              <ng-container *ngIf="formField.get('iElementHideField')?.value!=null">
                <mat-radio-group formControlName="iStepHideField">
                  <mat-radio-button [value]="1">{{ "FormConfiguration.Fields.HideInForm" | translate }}</mat-radio-button>
                  <mat-radio-button [value]="2">{{ "FormConfiguration.Fields.HideInQuotationSummary" | translate }}</mat-radio-button>
                  <mat-radio-button [value]="3">{{ "FormConfiguration.Fields.IsHideBoth" | translate }}</mat-radio-button>
                </mat-radio-group>
            </ng-container>
            </div>

            <ng-container *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
            formField.get('fkIIdFieldType')?.value === typeField.Text">
              <mat-checkbox formControlName="bIsUrlCheck" class="my-4">
                {{ "FormConfiguration.Fields.IsUrlCheck" | translate }}
              </mat-checkbox>
            </ng-container>

            <ng-container *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
            formField.get('fkIIdFieldType')?.value === typeField.Text || formField.get('fkIIdFieldType')?.value === typeField.TextArea || formField.get('fkIIdFieldType')?.value === typeField.DescriptionText">
              <mat-card>
                <mat-card-title><strong>{{ "FormConfiguration.Fields.TitleCases" | translate }}</strong> </mat-card-title>
                <mat-card-content>
                  <mat-radio-group formControlName="iFormatText" class="my-4">
                    <mat-radio-button [value]="1">{{ "FormConfiguration.Fields.LowerCase" | translate }}</mat-radio-button>
                    <mat-radio-button [value]="2">{{ "FormConfiguration.Fields.UpperCase" | translate }}</mat-radio-button>
                    <mat-radio-button [value]="3">{{ "FormConfiguration.Fields.SentenceCase" | translate }}</mat-radio-button>
                    <mat-radio-button [value]="4">{{ "FormConfiguration.Fields.RUTField" | translate }}</mat-radio-button>
                  </mat-radio-group>
                </mat-card-content>
              </mat-card>
            </ng-container>

            <ng-container *ngIf="formField.get('bIsDependent')?.value">
              <mat-checkbox formControlName="bIsInvisibleDependent" class="my-4">
                {{ "FormConfiguration.Fields.InvisibleDependentField" | translate }}
              </mat-checkbox>
              <mat-form-field appearance="outline" class="select-look w-100">
                <mat-label>
                  {{ "Product.DependentField" | translate }}
                </mat-label>
                <mat-select formControlName="fkIIdParent" id="idParent" required>
                  <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdField">
                    {{ item.vNameField }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-form-field *ngIf="catalogs.length>0" appearance="outline" class="select-look w-100">
                <mat-label>
                  Opción a la que es dependiente
                </mat-label>
                <mat-select formControlName="iOptionDependent" multiple id="iOptionDependent">
                  <mat-option [value]="null">
                    Opción a la que es dependiente
                  </mat-option>
                  <mat-option *ngFor="let item of catalogs" [value]="item.id">
                    {{ item.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </ng-container>

            <ng-container *ngIf="formField.get('bIsExternalLink')?.value">
              <p>{{ "FormConfiguration.Fields.linkExterno" | translate }}</p>
              <mat-label>
                {{ "FormConfiguration.Fields.SetLinkValue" | translate }}
              </mat-label>
              <p>
                <mat-radio-group formControlName="iLink">
                  <mat-radio-button [value]="1">{{ "FormConfiguration.Fields.IsDocument" | translate }}</mat-radio-button>
                  <mat-radio-button [value]="2">{{ "FormConfiguration.Fields.IsLink" | translate }}</mat-radio-button>
                </mat-radio-group>
              </p>
                <ng-container *ngIf="formField.get('iLink')?.value === 1">
                  <div class="upload-container" matTooltip="Solo se permiten archivos PDF">
                    <div class="upload-area" [ngClass]="{ 'dragover': isDragOver }" (drop)="onFileDropped($event)" (dragover)="onDragOver($event)"
                    (dragleave)="isDragOver = false">
                      <input type="file" (onChangeValidated)="onFileSelected($event)" id="fileUpload"
                        accept="application/pdf" hidden ValidationInputFile [allowedExtensions]="['pdf']" [maxFileSizeMB]="20"/>
                      <label for="fileUpload"  class="upload-label" [ngClass]="{ 'uploaded': showBtnDelete }">
                        <mat-icon  class="mb-5"
                          [style.color]="showBtnDelete ? 'green' : 'inherit'"
                          style="overflow: visible; margin-right: 23px"
                          >{{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}</mat-icon
                        >
                        <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>

                        <p *ngIf="!showBtnDelete && !fileName">
                          Haga clic para cargar o arrastre y suelte aquí los archivos
                        </p>
                        <span>PDF</span>
                      </label>
                    </div>
                    <p class="upload-info">
                      Tamaño máximo: 20 MB. Sólo permite un archivo.
                    </p>
                  </div>
                </ng-container>

                <ng-container *ngIf="formField.get('iLink')?.value === 2">
                  <mat-label>Link</mat-label>
                  <mat-form-field class="w-100">
                    <input matInput type="text" formControlName="vExternalLink"  placeholder="https://example.com"/>
                  </mat-form-field>
                </ng-container>

            </ng-container>

            <mat-form-field
            *ngIf="formField.get('bIsHasDefaultValue')?.value && formField.get('fkIIdFieldType')?.value"
            appearance="outline"
            class="select-look w-100"
          >
            <mat-label>{{
              "FormConfiguration.Fields.SetDefaultValue" | translate
            }}</mat-label>


            <ng-container [ngSwitch]="formField.get('fkIIdFieldType')?.value">
              <input
                *ngSwitchCase="typeField.Numeric"
                matInput
                type="number"
                formControlName="vSetDefaultValue"
              />
              <input
                *ngSwitchCase="typeField.Text"
                matInput
                type="text"
                formControlName="vSetDefaultValue"
              />
              <input
                *ngSwitchCase="typeField.Alphanumeric"
                matInput
                type="text"
                formControlName="vSetDefaultValue"
              />
              <div *ngSwitchCase="typeField.Date">
                <input
                  matInput
                  [matDatepicker]="initialValidityAns"
                  formControlName="vSetDefaultValue"
                />

              </div>
              <input
                *ngSwitchCase="typeField.TextArea"
                matInput
                type="text"
                formControlName="vSetDefaultValue"
              />
              <input
                *ngSwitchCase="typeField.Decimal"
                matInput
                type="text"
                step="0.001"
                formControlName="vSetDefaultValue"
                (input)="validateDecimals($event)"
                (blur)="formatToThreeDecimals($event)"
              />
            </ng-container>

            <mat-datepicker-toggle *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                  matSuffix
                  [for]="initialValidityAns"
                ></mat-datepicker-toggle>
                <mat-datepicker #initialValidityAns></mat-datepicker>
          </mat-form-field>

                  <ng-container
                    *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Numeric ||  formField.get('fkIIdFieldType')?.value === typeField.Money ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Text || formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.TextArea || formField.get('fkIIdFieldType')?.value === typeField.Decimal">
                    <mat-card [ngClass]="{'group-flow': formField.get('fkIIdFieldType')?.value === typeField.TextArea}">


                      <!-- Seccion de enmascarar -->
                      <div class="row my-2 mt-3 mb-3" *ngIf="formField.get('bIsMask')?.value">
                      <mat-card-title><strong>{{ "Product.MaskField" | translate }}</strong> </mat-card-title>

                        <div class="col-2 col-md-2">
                          <mat-checkbox formControlName="bIsFullyMasked" class="my-4">
                            {{ "Product.TheWholeField" | translate }}
                          </mat-checkbox>
                        </div>
                        <div class="col-3 col-md-3">
                          <mat-checkbox formControlName="bIsMaskedLastFourDigits" class="my-4">
                            {{ "Product.EntireFieldExceptLastFourCharacters" | translate }}
                          </mat-checkbox>
                        </div>
                      </div>
                      <mat-card-title><strong>{{ "Product.FieldRules" | translate }}</strong> </mat-card-title>

                      <mat-card-content>
                        <div class="row my-2">
                          <div class="col-6 col-md-6">
                            <!--Visible for numeric, text field, text area. Set size min that a input has to have-->
                            <mat-form-field *ngIf="
                                                  formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Text ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.TextArea || formField.get('fkIIdFieldType')?.value === typeField.Decimal
                                                " appearance="outline" class="w-100 mb-2">
                              <mat-label>
                                {{ "Product.MinimumSize" | translate }}
                              </mat-label>
                              <input matInput type="number" formControlName="iMinLength" />
                            </mat-form-field>
                          </div>
                          <div class="col-6 col-md-6">
                            <!--Visible for numeric, text field, UploadDocument, text area.
                                                      Set size max that a input has to have and set max size for a file -->
                            <mat-form-field
                              *ngIf=" formField.get('fkIIdFieldType')?.value === typeField.Numeric ||  formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                         formField.get('fkIIdFieldType')?.value === typeField.Text || formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments ||
                                                         formField.get('fkIIdFieldType')?.value === typeField.TextArea || formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                              appearance="outline" class="w-100 mb-2">
                              <mat-label>
                                {{ "Product.MaximumSize" | translate }}
                              </mat-label>
                              <input matInput type="number" formControlName="iMaxLength" [max]="maxLengthText"/>
                              <mat-hint align="start" *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Text">
                                {{ "Product.Maximun" | translate }} {{ maxTextOnlyLength }} {{ "Product.MaxLengthText" | translate }}
                              </mat-hint>
                              <mat-error *ngIf=" formField.get('iMaxLength')?.value < formField.get('iMinLength')?.value">
                                {{ "Product.MaximumSizeMustBeGreaterThanMinimumSize" | translate }}
                              </mat-error>
                              <mat-error *ngIf="formField.get('iMaxLength')?.hasError('max')">
                                {{ "Product.MaxCharacters" | translate }} {{ maxLengthText }}
                              </mat-error>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="row my-2">
                          <div class="col-12 col-md-12">
                            <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric"
                              formControlName="bIsEmail" class="my-4">
                              {{ "Product.CheckIsEmail" | translate }}
                            </mat-checkbox>
                          </div>
                        </div>
                        <div class="row my-2">
                          <div class="col-6 col-md-6">
                            <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                                          formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                                          formField.get('fkIIdFieldType')?.value === typeField.Money || formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                              formControlName="bIsValueMax" class="my-4">
                              {{ "Product.MaximumValue" | translate }}
                            </mat-checkbox>
                          </div>
                          <div class="col-6 col-md-6">
                            <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                                      formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                                      formField.get('fkIIdFieldType')?.value === typeField.Money || formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                              appearance="outline" class="w-100 mb-2">
                              <mat-label>
                                {{ "Product.EnterMaximumValue" | translate }}
                              </mat-label>
                              <input matInput type="number" formControlName="vValueMax" />
                              <mat-error *ngIf="formField.get('vValueMax')?.value < formField.get('vValueMin')?.value">
                                {{
                                "Product.MaximumSizeMustBeGreaterThanMinimumSize" | translate
                                }}
                              </mat-error>
                              <mat-error *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                <span *ngIf="utilsSvc.isControlHasError(formField, 'vValueMax', 'required')">
                                  {{ 'ThisFieldIsRequired' | translate }}
                                </span>
                              </mat-error>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="row my-2">
                          <div class="col-6 col-md-6">
                            <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                                          formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                                          formField.get('fkIIdFieldType')?.value === typeField.Money || formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                              formControlName="bIsValueMin" class="my-4">
                              {{ "Product.MinimumValue" | translate }}
                            </mat-checkbox>
                          </div>
                          <div class="col-6 col-md-6">
                            <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                                      formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                                      formField.get('fkIIdFieldType')?.value === typeField.Money || formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                              appearance="outline" class="w-100 mb-2">
                              <mat-label>
                                {{ "Product.EnterMinimumValue" | translate }}
                              </mat-label>
                              <input matInput type="number" formControlName="vValueMin" />
                              <mat-error *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                <span *ngIf="utilsSvc.isControlHasError(formField, 'vValueMin', 'required')">
                                  {{ 'ThisFieldIsRequired' | translate }}
                                </span>
                              </mat-error>
                            </mat-form-field>
                          </div>
                        </div>
                      </mat-card-content>
                    </mat-card>
                  </ng-container>


                  <mat-form-field class="w-100" *ngIf="
                      formField.get('fkIIdFieldType')?.value ===
                      typeField.UploadDocuments
                    ">
                    <mat-label>{{ "Product.TypeFile" | translate }}</mat-label>
                    <div class="input-container">
                      <input matInput formControlName="selectedFileTypes" [readonly]="true" (click)="open('typeUpload')"
                        placeholder="Seleccione los tipos de archivo" />
                      <button mat-icon-button (click)="open('typeUpload')">
                        <mat-icon iconPositionEnd fontIcon="edit"></mat-icon>
                      </button>
                    </div>
                  </mat-form-field>
                  <mat-checkbox *ngIf="
                      formField.get('fkIIdFieldType')?.value ===
                      typeField.UploadDocuments
                    " formControlName="bAllowMultipleUploads" class="my-4">
                    {{ "Product.MultipleFile" | translate }}
                  </mat-checkbox>

                  <mat-form-field *ngIf="
                      formField.get('fkIIdFieldType')?.value === typeField.Radio
                    " appearance="outline" class="w-100 mb-2">
                    <mat-label>
                      {{ "Product.OptionNumber" | translate }}
                    </mat-label>
                    <input
                      (focusout)="updateOptions()"
                      matInput
                      type="number"
                      formControlName="options"
                    />
                  </mat-form-field>
                  <div *ngIf="
                      formField.get('fkIIdFieldType')?.value === typeField.Radio
                    " formArrayName="optionValues">
                    <div *ngFor="let option of orderItems.controls; let i = index">
                      <mat-form-field appearance="outline" class="w-100 mb-2">
                        <mat-label>
                          {{ "Product.Option" | translate }} {{ i + 1 }}
                        </mat-label>
                        <input
                          id="optionInput-{{ i }}"
                          matInput
                          type="text"
                          [formControlName]="i"
                          (focusout)="updateOptionValue(i, $event.target)"
                        />
                      </mat-form-field>
                    </div>
                  </div>
                  <mat-form-field
                    *ngIf="formField.get('bHelpText')?.value"
                    appearance="outline"
                    class="w-100 mb-2"
                  >
                    <mat-label>
                      {{ "Product.HelpText" | translate }}
                    </mat-label>
                    <input matInput type="text" formControlName="vHelpText" [maxlength]="maxHelpTextLength" PreventionSqlInjector/>
                  </mat-form-field>

                  <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" appearance="outline"
                    class="select-look w-100">
                    <mat-label>
                      {{ "Product.FormatDate" | translate }}
                    </mat-label>
                    <mat-select formControlName="vFormat" [required]="
                        formField.get('fkIIdFieldType')?.value === typeField.Date
                      ">
                      <mat-option *ngFor="let item of typeFormatDate" [value]="item.format">
                        {{ item.format }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" formControlName="bShowYear"
                    class="my-4">
                    {{ "Product.ShowYear" | translate }}
                  </mat-checkbox>
                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" formControlName="bShowMonth"
                    class="my-4">
                    {{ "Product.ShowMonth" | translate }}
                  </mat-checkbox>
                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" formControlName="bShowDay"
                    class="my-4">
                    {{ "Product.ShowDay" | translate }}
                  </mat-checkbox>
                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" formControlName="bShowHour"
                    class="my-4">
                    {{ "Product.ShowHour" | translate }}
                  </mat-checkbox>

                  <ng-container *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date">
                    <mat-card>
                      <mat-card-title><strong>{{ "Product.FieldRules" | translate }}</strong> </mat-card-title>
                      <mat-card-content>
                        <div class="row my-3">
                          <div class="col-4 col-md-4">
                            <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                              formControlName="bIsMinDateRequerid" class="my-4">
                              {{ "Product.MinimumDateRequired" | translate }}
                            </mat-checkbox>
                          </div>
                          <div class="col-4 col-md-4">
                            <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" appearance="outline"
                              class="select-look w-100">
                              <mat-label>
                                {{ "Product.Type" | translate }}
                              </mat-label>
                              <mat-select formControlName="vTypeDateMin"
                              (ngModelChange)="onChangeTypeMinDate($event)" [required]="formField.get('fkIIdFieldType')?.value === typeField.Date">
                                <mat-option *ngFor="let item of typeDateMinFilter" [value]="item.Name">
                                  {{ item.Name }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <div class="col-4 col-md-4">
                            <!-- Campo de fecha -->
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                              <mat-label>{{ "Product.EnterMinimumDate" | translate }} </mat-label>
                              <input matInput [mode]="modeDatePicker" [dpDayPicker]="config" theme="dp-material" formControlName="dMinDateRequerid"
                              [placeholder]="placeholderText">
                              <mat-icon class="click"  *ngIf="!clockTypeIcon"  mat-icon-button  matSuffix>today</mat-icon>
                              <mat-icon class="click"  *ngIf="clockTypeIcon"  mat-icon-button  matSuffix>query_builder</mat-icon>
                              <mat-error *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                <span *ngIf="utilsSvc.isControlHasError(formField, 'dMinDateRequerid', 'required')">
                                    {{ 'ThisFieldIsRequired' | translate }}
                                </span>
                              </mat-error>
                              <mat-error *ngIf="formField.get('dMinDateRequerid')?.hasError('dateRangeInvalid')">
                                {{ "Product.MinimumDateWarning" | translate }}
                              </mat-error>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="row my-3">
                          <div class="col-4 col-md-4">
                            <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                              formControlName="bIsMaxDateRequerid" class="my-4">
                              {{ "Product.MaximumDateRequired" | translate }}
                            </mat-checkbox>
                          </div>
                          <div class="col-4 col-md-4">
                            <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" appearance="outline"
                              class="select-look w-100">
                              <mat-label>
                                {{ "Product.Type" | translate }}
                              </mat-label>
                              <mat-select formControlName="vTypeDateMax"
                              (ngModelChange)="onChangeTypeMaxDate($event)"  [required]="formField.get('fkIIdFieldType')?.value === typeField.Date">
                                <mat-option *ngFor="let item of typeDateMaxFilter" [value]="item.Name">
                                  {{ item.Name }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <div class="col-4 col-md-4">
                            <!-- Campo de fecha -->
                            <mat-form-field appearance="outline" class="w-100 mb-2">
                              <mat-label>{{ "Product.EnterMaxiumDate" | translate }}</mat-label>
                              <input matInput [mode]="modeDatePicker" [dpDayPicker]="config" theme="dp-material"
                                formControlName="dMaxDateRequerid" [placeholder]="placeholderText">
                              <mat-icon class="click" *ngIf="!clockTypeIcon" mat-icon-button matSuffix>today</mat-icon>
                              <mat-icon class="click" *ngIf="clockTypeIcon" mat-icon-button matSuffix>query_builder</mat-icon>
                              <mat-error>
                                <span *ngIf="utilsSvc.isControlHasError(formField, 'dMaxDateRequerid', 'required')">
                                    {{ 'ThisFieldIsRequired' | translate }}
                                </span>
                              </mat-error>
                              <mat-error *ngIf="formField.get('dMaxDateRequerid')?.hasError('dateRangeInvalid')">
                                {{ "Product.MaximumDateWarning" | translate }}
                              </mat-error>
                            </mat-form-field>
                          </div>
                        </div>
                      </mat-card-content>
                    </mat-card>
                  </ng-container>

                  <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Money" appearance="outline"
                    class="select-look w-100">
                    <mat-label>
                      {{ "Product.SelectCoin" | translate }}
                    </mat-label>
                    <mat-select formControlName="vFormat" [required]="
                        formField.get('fkIIdFieldType')?.value === typeField.Money
                      ">
                      <mat-option *ngFor="let item of typeFormatCoin" [value]="item.format">
                        {{ item.format }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Money" formControlName="bShowCoin"
                    class="my-4">
                    {{ "Product.ShowCoin" | translate }}
                  </mat-checkbox>

                  <!--Visible for searching field-->
                  <div class="container group-flow" *ngIf="formField.get('bIsSearch')?.value">
                    <fieldset>
                      <legend><b>{{ "FormConfiguration.Fields.SearchField" | translate }}</b></legend>
                      <mat-radio-group formControlName="iSearchType">
                        <mat-radio-button
                          *ngIf="formField.get('fkIIdFieldType')?.value !== typeField.DropDownList"
                          [value]="1">{{ "Product.IsPersonType" | translate }}
                        </mat-radio-button>
                        <mat-radio-button
                          *ngIf="formField.get('fkIIdFieldType')?.value !== typeField.DropDownList"
                          [value]="2">{{ "Product.ItQuoteType" | translate }}
                        </mat-radio-button>
                        <mat-radio-button
                          [value]="3">{{ "Product.ItEndpointype" | translate }}
                        </mat-radio-button>
                      </mat-radio-group>
                    </fieldset>

                    <ng-container  *ngIf="formField.get('iSearchType')?.value === 3">
                        <mat-checkbox formControlName="bIsSearchLogin"
                        class="my-4">
                        {{ "Cargar información de sesión" | translate }}
                      </mat-checkbox>
                    </ng-container>


                    <mat-form-field *ngIf="formField.get('iSearchType')?.value==2" appearance="outline"
                      class="select-look w-100">
                      <mat-label>
                        {{ "Product.DependentField" | translate }}
                      </mat-label>
                      <mat-select multiple formControlName="fkIIdChildrenDependent" id="Children" required>
                        <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdFieldModule">
                          {{ item.vNameField }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>


                    <ng-container *ngIf="formField.get('iSearchType')?.value == 3">
                      <div class="row">
                        <div class="col-md-12">
                          <mat-form-field appearance="outline" class="w-100">
                            <mat-label>{{ "Product.URLEndpoint" | translate }}</mat-label>
                            <input formControlName="vEndpoint" matInput placeholder="https://DOMAIN/CONTROLLER/METHOD" value="">
                          </mat-form-field>
                        </div>
                        <div class="col-md-12">
                          <mat-form-field class="w-100">
                            <mat-label>{{ "Product.Type" | translate }}</mat-label>
                            <mat-select formControlName="iTypeRequest" id="typeRequest" required>
                              <mat-option [value]="1">GET</mat-option>
                              <mat-option [value]="2">POST</mat-option>
                              <mat-option [value]="3">GET con Body</mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                      </div>

                      <div class="row">
                        <div class="col-md-12">
                          <h3>{{ "Product.MappingElementsSend" | translate }}</h3>
                          <table class="table">
                            <thead>
                              <tr>
                                <th>{{ "Product.Field" | translate }}</th>
                                <th>{{ "Product.TextAPI" | translate }}</th>
                                <th>{{ "Product.Convertion" | translate }}</th>
                              </tr>
                            </thead>
                            <tbody formArrayName="rowsRequest">
                              <tr *ngFor="let row of rowsRequest; let i = index" [formGroupName]="i">
                                <td>
                                  <mat-form-field class="w-100">
                                    <mat-label>{{ "Product.Field" | translate }} {{i}}</mat-label>
                                    <mat-select formControlName="fkIIdField" id="Children" required>
                                      <mat-option [value]="0">{{ "Product.CurrentField" | translate }}</mat-option>
                                      <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdField">
                                        {{ item.vNameField }}
                                      </mat-option>
                                    </mat-select>
                                  </mat-form-field>
                                </td>
                                <td>
                                  <mat-form-field class="w-100">
                                    <mat-label>{{ "Product.TextAPI" | translate }}</mat-label>
                                    <input matInput formControlName="vEquivalentField" />
                                  </mat-form-field>
                                </td>
                                <td>
                                  <mat-form-field class="w-100">
                                    <mat-label>{{ "Product.Convertion" | translate }}</mat-label>
                                    <mat-select formControlName="iTypeConvertion" id="iTypeConvertion">
                                      <mat-option [value]="1">{{ "Product.ByDefect" | translate }}</mat-option>
                                      <mat-option [value]="2">{{ "Product.String" | translate }}</mat-option>
                                    </mat-select>
                                  </mat-form-field>
                                </td>
                                <td>
                                  <button mat-icon-button (click)="deleteRowForTable('rowsRequest', i)" type="button">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <button type="button" mat-raised-button color="primary"
                            (click)="addRow('rowsRequest')">Añadir</button>
                          <mat-checkbox formControlName="bIsUsingJson">
                            {{ "Product.AddJSON" | translate }}
                          </mat-checkbox>
                        </div>
                      </div>

                      <div *ngIf="formField.get('bIsUsingJson')?.value" class="row my-2">
                        <div class="col-md-12">
                          <mat-form-field class="w-100">
                            <mat-label>{{ "Product.JSONStructure" | translate }}</mat-label>
                            <textarea formControlName="vSchemaJson" matInput placeholder="{KEY:VALUE}"></textarea>
                          </mat-form-field>
                        </div>
                      </div>
                      <div class="row my-5" *ngIf="formField.get('fkIIdFieldType')?.value === typeField.DropDownList">
                        <div class="col-md-12">
                          <h3>{{ "Product.MappingFieldsReceive" | translate }}</h3>
                          <div class="col-md-12">
                            <mat-form-field class="w-100">
                              <mat-label>{{ "Product.TypeResponseList" | translate }}</mat-label>
                              <mat-select formControlName="iTypeResponseList" id="typeResponseList" required>
                                <mat-option [value]="1">{{ "Product.Object" | translate }}</mat-option>
                                <mat-option [value]="2">{{ "Product.List" | translate }}</mat-option>
                              </mat-select>
                            </mat-form-field>
                            <mat-form-field class="w-100" *ngIf="formField.get('iTypeResponseList')?.value === 1">
                              <mat-label>{{ "Product.NameResponseList" | translate }}</mat-label>
                              <input matInput formControlName="vNameResponseList" />
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                      <div class="row my-5" *ngIf="formField.get('fkIIdFieldType')?.value !== typeField.DropDownList">
                        <div class="col-md-12">
                          <h3>{{ "Product.MappingElementsReceive" | translate }}</h3>
                          <table class="table">
                            <thead>
                              <tr>
                                <th>{{ "Product.Field" | translate }}</th>
                                <th>{{ "Product.JSONReceive" | translate }}</th>
                              </tr>
                            </thead>
                            <tbody formArrayName="rowsResponse">
                              <tr *ngFor="let row of rowsResponse; let i = index" [formGroupName]="i">
                                <td>
                                  <mat-form-field class="w-100">
                                    <mat-label>{{ "Product.Field" | translate }}:</mat-label>
                                    <mat-select formControlName="fkIIdField" id="Children" required>
                                      <mat-option [value]="0">{{ "Product.CurrentField" | translate }}</mat-option>
                                      <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdField">
                                        {{ item.vNameField }}
                                      </mat-option>
                                    </mat-select>
                                  </mat-form-field>
                                </td>
                                <td>
                                  <mat-form-field class="w-100">
                                    <mat-label>{{ "Product.JSONReceive" | translate }}</mat-label>
                                    <input matInput formControlName="vEquivalentField" />
                                  </mat-form-field>
                                </td>
                                <td>
                                  <button mat-icon-button (click)="deleteRowForTable('rowsResponse', i)" type="button">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <button type="button" mat-raised-button color="primary"
                            (click)="addRow('rowsResponse')">{{ "Product.Add" | translate }}</button>
                        </div>
                      </div>
                      <div class="row my-5">
                        <div class="col-md-12">
                          <h3>{{ "Product.AuthorizationField" | translate }}</h3>
                          <div class="col-md-12">
                            <mat-form-field class="w-100">
                              <mat-label>{{ "Product.TypeAuthorization" | translate }}</mat-label>
                              <mat-select formControlName="iTypeAuthorization" id="typeAuthorization">
                                <mat-option [value]="0">{{ "Product.Void" | translate }}</mat-option>
                                <mat-option [value]="1">{{ "Product.BasicAuthentication" | translate }}</mat-option>
                              </mat-select>
                            </mat-form-field>
                            <mat-form-field class="w-100" *ngIf="formField.get('iTypeAuthorization')?.value === 1">
                              <mat-label>{{ "Product.UserAuthentication" | translate }}</mat-label>
                              <input matInput formControlName="vUserAuthorization" />
                            </mat-form-field>

                            <mat-form-field class="w-100" *ngIf="formField.get('iTypeAuthorization')?.value === 1">
                              <mat-label>{{ "Product.PasswordAuthentication" | translate }}</mat-label>
                              <input matInput formControlName="vPasswordAuthorization" type="password"/>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                    </ng-container>


                  </div>

                  <!--Visible for person field-->
                  <ng-container *ngIf="formField.get('bIsPerson')?.value">
                    <mat-checkbox formControlName="bIsGrouper" class="my-4">
                      {{ "Product.IsGroupField" | translate }}
                    </mat-checkbox>

                    <mat-form-field *ngIf="!formField.get('bIsGrouper')?.value" appearance="outline" class="select-look w-100">
                      <mat-label>
                        {{ "Product.GroupField" | translate }}
                      </mat-label>
                      <mat-select formControlName="fkIGrouperField" id="idParent">
                        <mat-option *ngFor="let item of fieldGroupers" [value]="item.id">
                          {{ item.name }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="select-look w-100">
                      <mat-label>
                        {{ "Product.EquivalenceItemTablePerson" | translate }}
                      </mat-label>
                      <mat-select formControlName="vEquivalentField" id="idParent">
                        <mat-option *ngFor="let item of nameColumnsCustomerTable" [value]="item">
                          {{ item }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </ng-container>

                  <div class="group-flow container" *ngIf="formField.get('bIsPolicy')?.value">
                    <h6 class="subtitle">{{ "FormConfiguration.Fields.PolicyTypeField" | translate }}</h6>
                    <mat-checkbox formControlName="bIsGrouper" class="my-4">
                        {{ "FormConfiguration.Fields.ItIsGrouperField" | translate }}
                    </mat-checkbox>

                    <mat-form-field *ngIf="!formField.get('bIsGrouper')?.value" appearance="outline"
                        class="select-look w-100">
                        <mat-label>
                            {{ "FormConfiguration.Fields.GrouperField" | translate }}
                        </mat-label>
                        <mat-select formControlName="fkIGrouperField" id="idParent" required>
                            <mat-option *ngFor="let item of fieldGroupers" [value]="item.id">
                                {{ item.name }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="select-look w-100">
                        <mat-label>
                            {{ "FormConfiguration.Fields.EquivalenceItemPolicyTable" | translate }}
                        </mat-label>
                        <mat-select formControlName="vEquivalentField" id="idParent" required>
                            <mat-option *ngFor="let item of nameColumnsPolicyTable" [value]="item">
                                {{ item }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                  <ng-container *ngIf="
                  formField.get('fkIIdFieldType')?.value ===
                  typeField.DropDownList ">

                    <mat-form-field appearance="outline" class="select-look w-100">
                      <mat-label>
                        {{ "Product.SelectCatalog" | translate }}
                      </mat-label>
                      <mat-select formControlName="fkIIdCatalog" id="fkIIdCatalog">
                        <mat-option *ngFor="let item of catalogTable" [value]="item.pkIIdCatalog">
                          {{ item.vName }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="select-look w-100">
                      <mat-label>
                        {{ "Product.SelectCatalogField" | translate }}
                      </mat-label>
                      <mat-select formControlName="fkIIdFieldCatalog" id="fkIIdFieldCatalog">
                        <mat-option *ngFor="let item of catalogFields" [value]="item.pkIIdCatalogField">
                          {{ item.vName }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </ng-container>
                </form>
              </div>
            </div>
          <!-- botones -->
          <ng-container customButtonRight>
            <div class="button-container">
              <a class="w-auto mr-3" mat-button (click)="closeModal()" style="margin: 10px;"><span
                  class="label-button">{{'Cancel'|
                  translate}}</span></a>

              <button *ngIf="isEditingField" class="w-auto mr-3" type="button" mat-raised-button (click)="deleteField()">
                {{ ["Product.DeleteCharacteristics" | translate] }}
                <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
              </button>
              <button class="w-auto" type="button" mat-raised-button color="primary" (click)="saveField()">
                {{
                isEditingField
                ? ["Product.UpdateCharacteristics" | translate]
                : ["Product.SaveCharacteristics" | translate]
                }}
                <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
              </button>
            </div>
          </ng-container>
        </mat-tab>
        <mat-tab [disabled]="!isFieldDisabledTabRuleField()" [label]="'Product.FieldRules' | translate">
          <app-calculation-formulas [idField]="idFieldModule" [idForm]="idForm" [isModule]="1"
            (getFieldsByForm)="getFieldsByForm(idForm)"></app-calculation-formulas>
        </mat-tab>
        </mat-tab-group>


    </ng-container>
  </app-modal2>
</ng-template>

<ng-template #previewForm>
  <app-modal2 [titleModal]="'Product.FormConfigurator' | translate">
    <ng-container body>
      <app-preview-form [idProduct]="idProduct"></app-preview-form>
    </ng-container>
  </app-modal2>
</ng-template>

<ng-template #typeUpload>
  <app-modal2 [titleModal]="'Product.TypeFile' | translate">
    <ng-container body>
      <div class="row">
        <div class="col-4 form-check" *ngFor="let fileType of fileTypes">
          <input class="form-check-input" type="checkbox" [checked]="isSelected(fileType.Name)"
            (change)="toggleFileType(fileType.Name)" />
          <label class="form-check-label">{{ fileType.Name }}</label>
        </div>
      </div>
    </ng-container>
    <ng-container customButtonRight>
      <div class="modal-footer">
        <button mat-raised-button color="primary" type="button" class="" (click)="saveInfo()">
          {{ "Save" | translate }}
        </button>
      </div>
    </ng-container>
  </app-modal2>
</ng-template>

<ng-template #copyParameters>
  <app-modal2 [titleModal]="'Replicar configuración'">
    <ng-container body>
      <app-choose-country-and-company></app-choose-country-and-company>
      <mat-form-field appearance="outline" class="select-look w-100">
        <mat-label>
          {{ "Product.SelectProduct" | translate }}
        </mat-label>
        <mat-select [(ngModel)]="idProductReply" required>
          <mat-option *ngFor="let item of products" [value]="item.pkIIdProduct">
            {{ item.vProductName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </ng-container>
    <ng-container customButtonRight>
      <div class="modal-footer">
        <button type="button" class="" mat-raised-button color="primary" (click)="replyProduct()">
          {{ "Product.ReplicateConfiguration" | translate }}
        </button>
      </div>
    </ng-container>
  </app-modal2>
</ng-template>
