import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-preview',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule, TranslateModule],
  templateUrl: './preview.component.html',
  styleUrls: ['./preview.component.scss'],
})
export class PreviewComponent implements OnInit, OnDestroy {
  @Input() allColors: any = {};
  @Output() emitAction = new EventEmitter<string>();
  constructor() {}
  ngOnInit(): void {}

  resetToDefault() {
    this.emitAction.emit('reset');
  }

  saveSetting() {
    this.emitAction.emit('save');
  }
  ngOnDestroy(): void {}
}
