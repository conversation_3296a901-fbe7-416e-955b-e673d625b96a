import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ClientModel } from 'src/app/shared/models/client';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SelectModel } from 'src/app/shared/models/select';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-edit-client',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSlideToggleModule,
    TranslateModule,
    MatButtonModule,
    MatOptionModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRadioModule,
    MatIconModule
  ],
  templateUrl: './edit-client.component.html',
  styleUrls: ['./edit-client.component.scss']
})
export class EditClientComponent implements OnInit, AfterViewInit {

  labelPosition: 'before' | 'after' = 'after';
  formClient: FormGroup = new FormGroup({});
  formBusiness: FormGroup = new FormGroup({});
  documentsTypes: SelectModel[] = [];
  documentsTypesFiltered: SelectModel[] = [];
  originalDocumentsTypesFiltered: SelectModel[] = [];
  client: any = {};
  @Input() clientInput : any = {};
  @Input() dataClientId: number = 0;
  @Output() submitOut = new EventEmitter<ResponseGlobalModel>();
  public emailPattern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';
  @Output() editClient = new EventEmitter<any>();

  //variables relacionadas con empresa pais.
  idBusinessCountry: number = 0;

  constructor(
    private _fb: FormBuilder,
    public _utilsService: UtilsService,
    public _messageService: MessageService,
    private _parametersService: ParametersService,
    public _translateService: TranslateService,
    private cdr: ChangeDetectorRef,
    private _settingService: SettingService,
  ) {
  }

  ngOnInit(): void {
    this.getDataSettingInit();
    this.getListDocumentType();
    this.initForm();
    this.client = this.clientInput;
    this.fullForm(this.client);
  }

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  initForm() {
    this.formClient = this._fb.group({
      iIdDocumentType: ['', [Validators.required]],
      vDocumentNumber: ['', [Validators.required, Validators.pattern("^[0-9]*$")]],
      vFirstName: ['', [Validators.required]],
      vSecondName: ['', []],
      vSurname: ['', [Validators.required]],
      vSecondSurname: ['', []],
      vEmailUser: ['', [Validators.pattern(this.emailPattern)]],
      vCellPhone: ['', [Validators.pattern("^[0-9]*$")]],
      bActive: [true]
    });

    this.formBusiness = this._fb.group({
      bActive: [true],
      iIdDocumentType: ['', [Validators.required]],
      vDocumentNumber: ['', [Validators.required, Validators.pattern("^[0-9]*$")]],
      vVerificationDigit: ['', [Validators.pattern("^[0-9]*$")]],
      vBusinessName: ['', [Validators.required]],
      vEmailUser: ['', [Validators.pattern(this.emailPattern)]],
      vCellPhone: ['', [Validators.pattern("^[0-9]*$")]],

      iIdDocumentTypeContact: ['', []],
      vDocumentNumberContact: ['', [Validators.pattern("^[0-9]*$")]],
      vFirstNameContact: ['', []],
      vSecondNameContact: ['', []],
      vSurnameContact: ['', []],
      vSecondSurnameContact: ['', []],
      vEmailUserContact: ['', [Validators.pattern(this.emailPattern)]],
      vCellPhoneContact: ['', [Validators.pattern("^[0-9]*$")]],
      vRoleContact: ['', []]
    });
  }

  getListDocumentType() {
    this._parametersService.getListCatalogGlobalDocumentTypes().subscribe({
      next: (response) => {
        if (!response.error) {
          this.originalDocumentsTypesFiltered = response.result;
          this.documentsTypes = this.originalDocumentsTypesFiltered.filter((x: any) =>
            (x.name === 'Cédula de Ciudadanía' || x.name === 'Cédula de Extranjería' || x.name === 'Pasaporte')
          );

          if (this.client.vTypePerson === 1) {
            this.documentsTypesFiltered = this.originalDocumentsTypesFiltered.filter((x: any) =>
              (x.name === 'Cédula de Ciudadanía' || x.name === 'Cédula de Extranjería' || x.name === 'Pasaporte')
            );
          } else {
            this.documentsTypesFiltered = this.originalDocumentsTypesFiltered.filter((x: any) => x.name === 'Nit');
          }

        }
      },
    });
  }

  fullForm(client: any) {


    if (client.vTypePerson == 1) {
      this.formClient.patchValue({
        iIdDocumentType: client.iIdDocumentType,
        vDocumentNumber: client.vDocumentNumber,
        vFirstName: client.vFirstName,
        vSecondName: client.vSecondName,
        vSurname: client.vSurname,
        vSecondSurname: client.vSecondSurname,
        vEmailUser: client.vEmailUser,
        vCellPhone: client.vCellPhone,
        bActive: client.bActive
      });
    } else {
      this.formBusiness.patchValue({
        iIdDocumentType: client.iIdDocumentType,
        vDocumentNumber: client.vDocumentNumber,
        vVerificationDigit: client.vVerificationDigit,
        vBusinessName: client.vFullName,
        vEmailUser: client.vEmailUser,
        vCellPhone: client.vCellPhone,
        bActive: client.bActive,

        iIdDocumentTypeContact: client.contactCustomer.iIdDocumentType,
        vDocumentNumberContact: client.contactCustomer.vDocumentNumber,
        vFirstNameContact: client.contactCustomer.vFirstName,
        vSecondNameContact: client.contactCustomer.vSecondName,
        vSurnameContact: client.contactCustomer.vSurname,
        vSecondSurnameContact: client.contactCustomer.vSecondSurname,
        vEmailUserContact: client.contactCustomer.vEmailUser,
        vCellPhoneContact: client.contactCustomer.vPhone,
        vRoleContact: client.contactCustomer.vRole
      });
    }



  }

  updateClient() {

    const newClient: ClientModel = {
      pkIIdCustomer: this.client.pkIIdCustomer,
      iIdDocumentType: this.client.vTypePerson === 1 ? this.formClient.value.iIdDocumentType : this.formBusiness.value.iIdDocumentType,
      vDocumentNumber: this.client.vTypePerson === 1 ? this.formClient.value.vDocumentNumber : this.formBusiness.value.vDocumentNumber,
      vCellPhone: this.client.vTypePerson === 1 ? this.formClient.value.vCellPhone : this.formBusiness.value.vCellPhone,
      vEmailUser: this.client.vTypePerson === 1 ? this.formClient.value.vEmailUser : this.formBusiness.value.vEmailUser,
      vTypePerson: this.client.vTypePerson,
      bActive: this.client.vTypePerson === 1 ? this.formClient.value.bActive : this.formBusiness.value.bActive,
      FkIIdBusinessByCountry : this.idBusinessCountry
    };


    if (this.client.vTypePerson === 1) {
      Object.assign(newClient, {
        vFirstName: this.formClient.value.vFirstName,
        vSecondName: this.formClient.value.vSecondName,
        vSurname: this.formClient.value.vSurname,
        vSecondSurname: this.formClient.value.vSecondSurname
      });
    } else {
      Object.assign(newClient, {
        vVerificationDigit: this.formBusiness.value.vVerificationDigit,
        vFullName: this.formBusiness.value.vBusinessName,
        contactCustomer: {
          pkIIdTypePerson: this.client.pkIIdCustomer,
          vEmailUser: this.formBusiness.value.vEmailUserContact,
          bActive: 1,
          vPhone: this.formBusiness.value.vCellPhoneContact,
          fkIIdCustomer: 0,
          iIdDocumentType: this.formBusiness.value.iIdDocumentTypeContact,
          vDocumentNumber: this.formBusiness.value.vDocumentNumberContact,
          vFirstName: this.formBusiness.value.vFirstNameContact,
          vSecondName: this.formBusiness.value.vSecondNameContact,
          vSurname: this.formBusiness.value.vSurnameContact,
          vSecondSurname: this.formBusiness.value.vSecondSurnameContact,
          vRole: this.formBusiness.value.vRoleContact
        }
      });
    }
    this.editClient.emit(newClient);
  }

  //Get BusinessByCountry Initial Configuration.
  async getDataSettingInit() {
    let data = await this._settingService.getDataSettingInit();
    this.idBusinessCountry = data.idBusinessByCountry;
  }
}
