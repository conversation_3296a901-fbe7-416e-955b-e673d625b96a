<div class="col-12 mt-2">
    <div class="row mb-2">
        <form [formGroup]="dynamicFormGroup">
            <ng-container *ngFor="let tab of form?.pTabs">
                <h3 class="bold mb-3 pad-field-gen"><PERSON><PERSON> del {{ tab.vName }}</h3>
                <div class="row" *ngFor="let section of tab.pSections">
                    <ng-container *ngFor="let field of section.uSectionFields; let i = index">
                        <div class="col-6">
                            <app-field-generator [field]="field.fkIIdFieldNavigation" [isEditing]="false">
                            </app-field-generator>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
        </form>
    </div>
</div>