<div class="col-12 col-md-12 mt-3">
  <div class="d-flex justify-content-start align-items-center mb-2">
    <h3 style="margin: 0"> {{ "FormConfiguration.Sections.sectionTitle" | translate }}</h3>
    <mat-icon class="click ml-1" matTooltipPosition="right" matTooltip="{{ 'Tooltips.FormSection' | translate }}">help_outline</mat-icon>
  </div>
  <app-table
    [displayedColumns]="estructTable"
    [data]="dataTableSections"
    (iconClick)="controller($event)"
  ></app-table>
  <button
    class="mb-2"
    type="button"
    mat-raised-button
    color="primary"
    (click)="openModalCreateEditSections()"
  >
    {{ "Add" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
</div>

<!-- modal editar-crear sección -->
<ng-template #editNewSectionsModal>
  <app-modal2 [titleModal]="titelModal" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="form">
        <div class="row mt-5">
          <div class="col-md-12 col-sm-12">
            <mat-slide-toggle class="mb-5" formControlName="bActive">
              {{ "FormConfiguration.Sections.ActiveSectionLabel" | translate }}
            </mat-slide-toggle>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "FormConfiguration.Sections.SectionName" | translate }}
              </mat-label>
              <input matInput formControlName="vName" />
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'vName', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{
                  "FormConfiguration.ProgressBar.StepNameDbLabel" | translate
                }}
              </mat-label>
              <input matInput formControlName="vNameDb" />
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ "FormConfiguration.Sections.StepLabel" | translate }}
              </mat-label>
              <mat-select formControlName="fkIIdProgressBar">
                <mat-option
                  *ngFor="let step of progressBarList"
                  [value]="step.pkIIdProgressBar"
                >
                  {{ step.vName }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    form,
                    'fkIIdProgressBar',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{
                  "FormConfiguration.Sections.TabLabel" | translate
                }}</mat-label
              >
              <mat-select formControlName="fkIIdTabModule">
                <mat-option
                  *ngFor="let tab of tabList"
                  [value]="tab.pkIIdTabModule"
                >
                  {{ tab.vName }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(form, 'fkIIdTabModule', 'required')
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ "FormConfiguration.ProgressBar.StepOrderLabel" | translate }}
              </mat-label>
              <mat-select formControlName="iOrder">
                <mat-option *ngFor="let order of orderList" [value]="order">
                  {{ order }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'iOrder', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonCenter>
      <button
        (click)="deleteSection()"
        type="button"
        mat-raised-button
        *ngIf="action === 'edit'"
        class="delete"
      >
        {{ "FormConfiguration.Sections.DeleteSectionButton" | translate }}
        <mat-icon class="delete" iconPositionEnd fontIcon="close"></mat-icon>
      </button>
    </ng-container>

    <ng-container customButtonRight>
      <button
        *ngIf="action === 'create'"
        type="button"
        mat-raised-button
        color="primary"
        (click)="createSection()"
        [disabled]="!valid"
      >
        {{ "FormConfiguration.Sections.AddSectionButton" | translate }}
      </button>
      <button
        *ngIf="action === 'edit'"
        type="button"
        mat-raised-button
        color="primary"
        (click)="editSection()"
        [disabled]="!valid"
      >
        {{ "FormConfiguration.Sections.SaveSectionButton" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
