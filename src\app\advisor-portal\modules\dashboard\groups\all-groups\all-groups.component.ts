import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { GroupsService } from 'src/app/shared/services/groups/groups.service';
import { GroupModel } from 'src/app/shared/models/groups';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-all-groups',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TableComponent,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule

  ],
  templateUrl: './all-groups.component.html',
  styleUrls: ['./all-groups.component.scss'],
})
export class AllGroupsComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  formValid: boolean = false;
  groupTable: GroupModel[] = [];
  estructGroupTable: BodyTableModel[] = [
    { columnLabel: this._translateService.instant('Group.Name'), 
    columnValue: 'vGroupName' },
    {
      columnLabel: this._translateService.instant('Status'), 
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsService.changeStatusValue(item)
    },
    {
      columnLabel: this._translateService.instant('Modify'), 
      columnValue: 'edit',
      columnIcon: 'create',
    },
  ];

  constructor(
    private _groupsService: GroupsService,
    private _router: Router,
    private _settingService: SettingService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _utilsService: UtilsService,
    private _customRouter: CustomRouterService

  ) {}

  ngOnInit(): void {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if(this._router.url == response.currentModule)
          { 
            if (!(Object.keys(response).length === 0)) {
              this.groupTable = [];
              this.getListGroupsByIdBusiness(response.enterprise.pkIIdBusinessByCountry);
            }
          }          
        }
      );
      this._translateService.onLangChange.subscribe(
        (event: LangChangeEvent) => { 
          //traduccion data table business
          this.estructGroupTable[0].columnLabel = this._translateService.instant('Group.Name')
          this.estructGroupTable[1].columnLabel = this._translateService.instant('Status')
          this.estructGroupTable[2].columnLabel = this._translateService.instant('Modify')
  
        }
      );
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.goToEdit(event.value.pkIIdGroup);
        break;
      default:
        break;
    }
  }

  getListGroupsByIdBusiness(idBusinessByCountry: number) {
    this._groupsService
      .getListGroupsByIdBusiness(idBusinessByCountry)
      .subscribe({
        next: (response) => {
          this.groupTable = response.result;
        },
      });
  }
  gotToCreate() {
    if (this.formValid) {
      this._customRouter.navigate(['/dashboard/groups/new']);
    } else {
      this._messageService.messageInfo(
        this._translateService.instant("InvalidForm"),
        this._translateService.instant("YouMustSelectACountryAndABusiness"),
      );
    }
  }

  goToEdit(id: number) {
    this._customRouter.navigate([`/dashboard/groups/new/${id}`]);
  }

  validForm(event: boolean) {
    this.formValid = event;
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
