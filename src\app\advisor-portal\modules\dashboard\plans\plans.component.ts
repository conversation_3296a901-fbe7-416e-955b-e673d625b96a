import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { Subscription, filter } from 'rxjs';


@Component({
  selector: 'app-plans',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent, MatIconModule, MatTooltipModule],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" /> 
        {{"Plan.PlanSettings" | translate}}
        <mat-icon *ngIf="listing" class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.PlanConfigurationTitle' | translate }}">help_outline</mat-icon>
        <mat-icon *ngIf="creating" class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.PlanConfigurationTitleCreate' | translate }}">help_outline</mat-icon>
        <mat-icon *ngIf="editing" class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.PlanConfigurationTitleEdit' | translate }}">help_outline</mat-icon>
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class PlansComponent  implements OnInit, OnDestroy{
  constructor(
    private _translateService: TranslateService,
    private _router: Router
  ) {}
  
  inicio: string = this._translateService.instant("Inicio")  
  planes: string = this._translateService.instant("Planes")
  listing: boolean = true;
  creating: boolean = false;
  editing: boolean = false;
  private _routerSubscription!: Subscription;

  sections: {label: string, link: string}[]=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.planes, link: '/dashboard/plans'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Inicio")
      this.planes = this._translateService.instant("Planes")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.planes
    });

    this._routerSubscription = this._router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      const navEndEvent = event as NavigationEnd;
      this.checkRoute(navEndEvent.urlAfterRedirects);
    });
  }

  ngOnDestroy(): void {
    if (this._routerSubscription) {
      this._routerSubscription.unsubscribe();
    }
  }

  checkRoute(url: string): void {
    if (url === '/dashboard/plans') { 
      this.listing = true;
      this.creating = false;
      this.editing = false;
    } else if(url.includes('plans/new/')){
      this.listing = false;
      this.creating = true;
      this.editing = false;
    } else if(url.includes('plans/edit/')){
      this.listing = false;
      this.creating = false;
      this.editing = true;
    }
  }
}
