import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-procedures',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    BreadcrumbComponent
  ],
  template: `
  <div>
    <h2 class="h3">
      <img src="assets/img/layouts/config_ico.svg" alt="" /> 
      {{"ProceduresViewerSettings.Procedures" | translate}}
    </h2>
  </div>
  <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

  <router-outlet></router-outlet>
`,
  styles: []
})
export class ProceduresComponent implements OnInit{

  constructor(
    private _translateService: TranslateService,
  ) {}
  
  inicio: string = this._translateService.instant("Inicio")  
  procedures: string = this._translateService.instant("ProceduresViewerSettings.Procedures")

  sections: {label: string, link: string}[]=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.procedures, link: '/dashboard/procedures'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Inicio")
      this.procedures = this._translateService.instant("ProceduresViewerSettings.Procedures")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.procedures
    });
  }

}
