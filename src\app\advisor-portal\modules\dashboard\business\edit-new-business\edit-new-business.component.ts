import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';

import {
  GetListCountriesModel,
  BusinessGroupListModel,
  ImagesPortals,
} from 'src/app/shared/models/business';

import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import {
  MatSlideToggleChange,
  MatSlideToggleModule,
} from '@angular/material/slide-toggle';
import { ImageModel } from 'src/app/shared/models/image-picker/image.model';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { MatDialog } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { EditNewBusinessGroupComponent } from '../edit-new-business-group/edit-new-business-group.component';
import { MatButtonModule } from '@angular/material/button';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import {
  ImageUpdateUpload,
  ImageUploadModel,
} from 'src/app/shared/models/file';
import { forkJoin, Observable } from 'rxjs';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';

@Component({
  selector: 'app-edit-new-business',
  standalone: true,
  templateUrl: './edit-new-business.component.html',
  styleUrls: ['./edit-new-business.component.scss'],
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    TranslateModule,
    GenericImagePickerComponent,
    ModalComponent,
    EditNewBusinessGroupComponent,
    MatButtonModule,
    PreventionSqlInjectorDirective,
    MatTooltipModule,
  ],
})
export class EditNewBusinessComponent implements OnInit {
  @ViewChild('editNewBusinessGroupModal')
  editNewBusinessGroupModal?: TemplateRef<any>;
  form: FormGroup = this._fb.group({});
  backRoute = '/dashboard/business';
  operationType = 'create';
  //data que se envia al componente modal crear grupo empresarial
  dataBusinessGroupId: number = -1;

  //idbusiness country en caso de que se este editando
  idBusiness: number = 0;
  idBusinessCountry: number = 0;
  idImage: number = 0;
  idImageClientPortal: number = 0;
  isImageEdit: boolean = false;
  businessGroup_Checked: boolean = false;
  hasLogin_Checked: boolean = false;
  hasRegister_Checked: boolean = false;
  hasButtonIWantIt_Checked: boolean = false;
  imageSrc: any[] = [];
  imageSrcClientPortalLogo: any[] = [];
  countries: GetListCountriesModel[] = [];
  businessGroups: BusinessGroupListModel[] = [];
  titleText: string = '';
  navTittle: string = '';
  isValidForm: string = this._translateService.instant(
    'PleaseCompleteAllTheInformationOnTheForm'
  );

  sliderImages: ImagesPortals[] = [
    { imageSrc: '', name: '', imageBase64: '', codeCategory: '' },
  ];

  sliderImagesInitial: ImagesPortals[] = [
    { imageSrc: '', name: '', imageBase64: '', codeCategory: '' },
  ];

  constructor(
    private _fb: FormBuilder,
    private _activatedRoute: ActivatedRoute,
    private _businessService: BusinessService,
    private _messageService: MessageService,
    private _fileService: FileService,
    private _settingService: SettingService,
    public _router: Router,
    public _utilsSvc: UtilsService,
    public _translateService: TranslateService,
    public _editNewBusinessGroupDialog: MatDialog,
    private _customRouter: CustomRouterService,
    private sanitizer: DomSanitizer,
    private _spinnerService: SpinnerService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getCountries();
    this.getBusinessGroup();
    this.validateEditOrNew();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.isValidForm = this._translateService.instant(
        'PleaseCompleteAllTheInformationOnTheForm'
      );
      if (this.idBusiness == 0) {
        this.titleText = this._translateService.instant('Business.NewBusiness');
      } else {
        this.titleText = this._translateService.instant(
          'Business.EditBusiness'
        );
      }
    });
  }

  /**
   * Initializes the form with default values and validators.
   */
  initForm() {
    this.form = this._fb.group({
      b_Active: [true],
      b_EnableClientPortal: [false],
      country: ['', [Validators.required]],
      v_Name: [null, [Validators.required]],
      v_Document: [null, Validators.required],
      v_BusinessName: [null, [Validators.required]],
      v_SubDomainAdvisorPortal: [null, [Validators.required]],
      v_SubDomainClientPortal: [null],
      fk_i_IdBusinessGroup: [null],
      vImageName: [null],
      vImageBase64: [null],
      vImageNameClientPortalLogo: [null],
      vImageBase64ClientPortalLogo: [null],
      vImageCodeCategoryClientPortalLogo: [null],
      vImageCodeCategoryAdvisorPortalLogo: [null],
      b_HasLogin: [false],
      b_HasRegister: [false],
      b_HasButtonIwantIt: [false]
    });
  }

  /**
   * Validates whether the current operation is to create a new business or edit an existing one.
   * If editing, it fetches the relevant business data and updates the form accordingly.
   */
  validateEditOrNew() {
    this._activatedRoute.params.subscribe((e) => {
      if (e['id']) {
        this.idBusiness = e['id'];
        this.operationType = 'edit';
        this.titleText = this._translateService.instant(
          'Business.EditBusiness'
        );

        // Fetch business data by ID and patch the form values
        this._businessService
          .getByBusinessId(this.idBusiness)
          .subscribe((resp) => {
            this.form.patchValue({
              pk_i_IdBusiness: resp.result.pk_i_IdBusiness,
              v_Name: resp.result.v_Name,
              v_Document: resp.result.v_Document,
              v_BusinessName: resp.result.v_BusinessName,
              v_SubDomainClientPortal: resp.result.v_SubDomainClientPortal,
              v_SubDomainAdvisorPortal: resp.result.v_SubDomainAdvisorPortal,
              b_EnableClientPortal: resp.result.b_EnableClientPortal,
              b_Active: resp.result.b_Active,
              fk_i_IdBusinessGroup: resp.result.fk_i_IdBusinessGroup
            });

            // Handle business group validation
            if (
              resp.result.fk_i_IdBusinessGroup != null &&
              resp.result.fk_i_IdBusinessGroup != 0
            ) {
              this.businessGroup_Checked = true;
              this.form
                .get('fk_i_IdBusinessGroup')
                ?.setValidators(Validators.required);
              this.form.get('fk_i_IdBusinessGroup')?.updateValueAndValidity();
            } else {
              this.businessGroup_Checked = false;
              this.form.get('fk_i_IdBusinessGroup')?.clearValidators();
              this.form.get('fk_i_IdBusinessGroup')?.updateValueAndValidity();
            }

            // Fetch business country data by business ID
            this._businessService
              .getBusinessCountryByBusinessId(this.idBusiness)
              .subscribe((resp1) => {
                this.form.patchValue
                ({ 
                  country: resp1.result[0].pkIIdCountry, 
                  b_EnableClientPortal:resp1.result[0].bEnableClientPortal,
                  v_SubDomainClientPortal: resp1.result[0].vSubDomainClientPortal,
                  v_SubDomainAdvisorPortal: resp1.result[0].vSubDomainAdvisorPortal,
                  b_HasLogin: resp1.result[0].bHasLogin,
                  b_HasRegister: resp1.result[0].bHasRegister,
                  b_HasButtonIwantIt: resp1.result[0].bHasButtonIwantIt
                });

                this.idBusinessCountry = resp1.result[0].pkIIdBusinessByCountry;
                this.hasLogin_Checked = resp1.result[0].bHasLogin;
                this.hasRegister_Checked = resp1.result[0].bHasRegister;
                this.hasButtonIWantIt_Checked = resp1.result[0].bHasButtonIwantIt;

                // Fetch logos and slider images
                this.getLogoAdvisorsByIdBusinessCountryAndCodeCategory();
                this.getLogoClientByIdBusinessCountryAndCodeCategory();
                this.getImagesSliderByIdBusinessCountryAndCodeCategory();
              });
          });
      } else {
        this.idBusiness = 0;
        this.operationType = 'create';
        this.titleText = this._translateService.instant('Business.NewBusiness');
      }
    });
  }

  /**
   * Fetches slider images by the business country ID and the category code 'SPC'.
   * The images are then processed and sanitized for secure display.
   */
  getImagesSliderByIdBusinessCountryAndCodeCategory(): void {
    this._fileService
      .getImagensByIdBusinessCountryAndCodeCategory(
        this.idBusinessCountry,
        'SPC'
      )
      .subscribe(
        (resp2) => {
          if (resp2.result && resp2.result.length > 0) {
            let extension =
              resp2.result[0].vFileName.split('.')[
                resp2.result[0].vFileName.split('.').length - 1
              ];
            if (extension === 'svg') {
              extension = 'svg+xml';
            }

            // Fetch image URLs and sanitize them
            this._fileService
              .getImageUrls(resp2.result)
              .subscribe((imagesWithUrls) => {
                this.sliderImages = imagesWithUrls.map((image) => {
                  image.imageSrc = this.sanitizer.bypassSecurityTrustUrl(
                    image.src
                  ) as SafeUrl;
                  return image;
                });

                // Store initial slider images for comparison
                this.sliderImagesInitial = JSON.parse(
                  JSON.stringify(this.sliderImages)
                );

                // Ensure the slider has at least one image slot
                this.addSliderImage();
              });

            // Patch form with the first image's data
            this.form.patchValue({
              vImageName: resp2.result[0].vFileName,
              vImageBase64: resp2.result[0].imageBase64,
            });
            this.idImage = resp2.result[0].pkIIdUploadFile;
          }
        },
        (error) => {
          console.error('Error en la petición', error);
        }
      );
  }

  /**
   * Fetches the logo for advisors by the business country ID and the category code 'LPA'.
   * The logo image is processed and sanitized for secure display.
   */
  getLogoAdvisorsByIdBusinessCountryAndCodeCategory(): void {
    this._fileService
      .getImagensByIdBusinessCountryAndCodeCategory(
        this.idBusinessCountry,
        'LPA'
      )
      .subscribe(
        (resp2) => {
          if (resp2.result && resp2.result.length > 0) {
            let extension =
              resp2.result[0].vFileName.split('.')[
                resp2.result[0].vFileName.split('.').length - 1
              ];
            if (extension === 'svg') {
              extension = 'svg+xml';
            }

            // Fetch and sanitize image URLs
            this._fileService
              .getImageUrls(resp2.result)
              .subscribe((imagesWithUrls) => {
                this.imageSrc = imagesWithUrls.map((image) => {
                  image.src = this.sanitizer.bypassSecurityTrustUrl(
                    image.src
                  ) as SafeUrl;
                  return image;
                });
              });

            // Patch form with the logo image data
            this.form.patchValue({
              vImageName: resp2.result[0].vFileName,
              vImageBase64: resp2.result[0].imageBase64,
            });
            this.idImage = resp2.result[0].pkIIdUploadFile;
          }
        },
        (error) => {
          console.error('Error en la petición', error);
        }
      );
  }

  /**
   * Fetches the client portal logo by the business country ID and the category code 'LPC'.
   * The logo image is processed and sanitized for secure display.
   */
  getLogoClientByIdBusinessCountryAndCodeCategory(): void {
    this._fileService
      .getImagensByIdBusinessCountryAndCodeCategory(
        this.idBusinessCountry,
        'LPC'
      )
      .subscribe(
        (resp2) => {
          if (resp2.result && resp2.result.length > 0) {
            let extension =
              resp2.result[0].vFileName.split('.')[
                resp2.result[0].vFileName.split('.').length - 1
              ];
            if (extension === 'svg') {
              extension = 'svg+xml';
            }

            // Fetch and sanitize image URLs
            this._fileService
              .getImageUrls(resp2.result)
              .subscribe((imagesWithUrls) => {
                this.imageSrcClientPortalLogo = imagesWithUrls.map((image) => {
                  image.src = this.sanitizer.bypassSecurityTrustUrl(
                    image.src
                  ) as SafeUrl;
                  return image;
                });
              });

            // Patch form with the client portal logo image data
            this.form.patchValue({
              vImageNameClientPortalLogo: resp2.result[0].vFileName,
              vImageBase64ClientPortalLogo: resp2.result[0].imageBase64,
            });
            this.idImageClientPortal = resp2.result[0].pkIIdUploadFile;
          }
        },
        (error) => {
          console.error('Error en la petición', error);
        }
      );
  }

  /**
   * Checks if the form is valid.
   *
   * @returns {boolean} True if the form is valid, otherwise false.
   */
  get valid(): boolean {
    return this.form.valid;
  }

  /**
   * Handles the response data after submitting a new business group.
   * Displays a success message if the operation was successful, otherwise displays an error message.
   *
   * @param {ResponseGlobalModel} event - The response model containing the result of the operation.
   */
  getSubmintData(event: ResponseGlobalModel) {
    if (!event.error) {
      this._messageService.messageSuccess(
        this._translateService.instant('Created'),
        this._translateService.instant(
          'Business.YouCanFindItInTheBusinessSettings'
        )
      );
      this.getBusinessGroup();
      this._editNewBusinessGroupDialog.closeAll();
    } else {
      this._messageService.messageError(
        this._translateService.instant('ThereWasAError') + event.message
      );
    }
  }

  /**
   * Handles the change event of the slide toggle for activating or deactivating the business.
   * If the toggle is turned off, it prompts the user for confirmation. If confirmed, it shows a success message.
   * If the user cancels, the toggle is reset to its previous state.
   *
   * @param {MatSlideToggleChange} event - The slide toggle change event.
   */
  slideStatus_Change(event: MatSlideToggleChange) {
    if (!event.checked) {
      this._messageService
        .messageConfirmationAndNegation(
          this._translateService.instant('Business.Deactivate'),
          '',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            this._messageService.messageConfirmatio(
              this._translateService.instant('Business.Disabled'),
              '',
              'success',
              this._translateService.instant('Continue')
            );
          } else {
            this.form.patchValue({ b_Active: true });
          }
        });
    }
  }

  /**
   * Handles the change event of the slide toggle for activating or deactivating the client portal.
   * If the toggle is turned off, it prompts the user for confirmation. If confirmed, it shows a success message.
   * If the user cancels, the toggle is reset to its previous state.
   *
   * @param {MatSlideToggleChange} event - The slide toggle change event.
   */
  slideStatusChangePortalClient(event: MatSlideToggleChange) {
    if (!event.checked) {
      this._messageService
        .messageConfirmationAndNegation(
          this._translateService.instant('Business.Deactivate'),
          '',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            this._messageService.messageConfirmatio(
              this._translateService.instant('Business.Disabled'),
              '',
              'success',
              this._translateService.instant('Continue')
            );
          } else {
            this.form.patchValue({ b_Active: true });
          }
        });
    }
  }

  /**
   * Handles the change event for the business group checkbox.
   * If the checkbox is selected, the business group field is set as required.
   * If the checkbox is deselected, the business group field is cleared and its validation is removed.
   *
   * @param {Event} event - The change event triggered by the checkbox.
   */
  checkBusinessGroup_Change(event: Event) {
    const ischecked = (<HTMLInputElement>event.target).checked;
    if (ischecked) {
      // If the checkbox was selected
      this.businessGroup_Checked = true;
      this.form.get('fk_i_IdBusinessGroup')?.setValidators(Validators.required);
      this.form.get('fk_i_IdBusinessGroup')?.updateValueAndValidity();
    } else {
      // If the checkbox was deselected
      this.businessGroup_Checked = false;
      this.form.patchValue({ fk_i_IdBusinessGroup: null });
      this.form.get('fk_i_IdBusinessGroup')?.clearValidators();
      this.form.get('fk_i_IdBusinessGroup')?.updateValueAndValidity();
    }
  }

  checkHasLogin_Change(event: Event) {
    const ischecked = (<HTMLInputElement>event.target).checked;
    this.hasLogin_Checked = ischecked ?? false;
  }

  checkHasRegister_Change(event: Event) {
    const ischecked = (<HTMLInputElement>event.target).checked;
    this.hasRegister_Checked = ischecked ?? false;
  }

  checkHasButtonWantIt_Change(event: Event) {
    const ischecked = (<HTMLInputElement>event.target).checked;
    this.hasButtonIWantIt_Checked = ischecked ?? false;
  }

  /**
   * Handles the change event for the advisor portal logo image.
   * Extracts the image name, extension, and base64 data from the event and updates the form fields accordingly.
   * Marks the image as edited.
   *
   * @param {ImageModel} event - The image change event containing the image data.
   */
  logoImage_Change(event: ImageModel) {
    const imageName: string = event.dataJson[0].name.split('.')[0];
    const extension: string = event.dataJson[0].type.split('/')[1];
    const base64: string = event.dataString.split(',')[1];

    this.form.get('vImageName')?.setValue(`${imageName}.${extension}`);
    this.form.get('vImageBase64')?.setValue(base64);
    this.form.get('vImageCodeCategoryAdvisorPortalLogo')?.setValue('LPA');

    this.isImageEdit = true;
  }

  /**
   * Handles the change event for the client portal logo image.
   * Extracts the image name, extension, and base64 data from the event and updates the form fields accordingly.
   * Marks the image as edited.
   *
   * @param {ImageModel} event - The image change event containing the image data.
   */
  logoImageClientPortalLogo_Change(event: ImageModel) {
    const imageName: string = event.dataJson[0].name.split('.')[0];
    const extension: string = event.dataJson[0].type.split('/')[1];
    const base64: string = event.dataString.split(',')[1];

    this.form
      .get('vImageNameClientPortalLogo')
      ?.setValue(`${imageName}.${extension}`);
    this.form.get('vImageBase64ClientPortalLogo')?.setValue(base64);
    this.form.get('vImageCodeCategoryClientPortalLogo')?.setValue('LPC');

    this.isImageEdit = true;
  }

  /**
   * Removes all spaces from a string and validates whether the resulting string is not empty.
   *
   * @param {string} input - The input string to be processed.
   * @returns {boolean} - Returns `true` if the processed string is not empty; otherwise, `false`.
   */
  removeSpacesAndValidate(input: string): boolean {
    if (!input) {
      return false;
    }
    // Remove all spaces from the string
    const processedString = input.replace(/\s+/g, '');

    // Validate that the string is not empty after removing spaces
    return processedString.length > 0;
  }

  /**
   * Handles the click event for saving a business.
   * Validates the form, checks if the client portal is enabled and if the necessary subdomain is provided.
   * Depending on the operation type (create or edit), it either creates a new business or updates an existing one.
   */
  saveBusiness_Click() {
    // Ensure the client portal settings are correctly set
    this.form.value.b_EnableClientPortal =
      this.form.value.b_EnableClientPortal ?? false;
    this.form.value.v_SubDomainClientPortal =
      this.form.value.v_SubDomainClientPortal ?? null;

    // Validate that if the client portal is enabled, a subdomain is provided
    if (
      this.form.value.b_EnableClientPortal &&
      !this.form.value.v_SubDomainClientPortal &&
      !this.removeSpacesAndValidate(this.form.value.v_SubDomainClientPortal)
    ) {
      return this._messageService.messageError(
        this._translateService.instant(
          'Business.IfYouActivateTheClientPortalItIsMandatoryToProvideTheSubdomain'
        )
      );
    }

    // Check if the form is valid
    if (this.valid) {
      if (this.idBusiness === 0) {
        // If creating a new business
        this._messageService
          .messageConfirmationAndNegation(
            this._translateService.instant('Create?'),
            '',
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.postBusiness();
            }
          });
      } else {
        // If editing an existing business
        if (this.operationType === 'edit' && !this.validationsPut()) {
          return;
        }

        this._messageService
          .messageConfirmationAndNegation(
            this._translateService.instant('Confirm?'),
            '',
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.putBusiness();
            }
          });
      }
    } else {
      // If the form is invalid, show an information message
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this.isValidForm
      );
    }
  }

  /**
   * Handles the click event to navigate back to the business dashboard.
   * Displays a confirmation dialog to the user, warning that unsaved settings will be lost.
   * If the user confirms, navigates back to the business dashboard.
   */
  goBack_Click() {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Out?'),
        this._translateService.instant('UnsavedSettingsWillBeLost'),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._customRouter.navigate(['dashboard/business/']);
        }
      });
  }

  /**
   * Opens the dialog for editing or creating a new business group.
   * Sets the business group ID and configures the dialog dimensions.
   *
   * @param {number} [id=0] - The ID of the business group to edit. Defaults to 0 for creating a new group.
   */
  openEditNewBusinessGroupDialog(id: number = 0) {
    this.dataBusinessGroupId = id;
    const dialogRef = this._editNewBusinessGroupDialog.open(
      this.editNewBusinessGroupModal!,
      {
        width: '30vw',
        maxHeight: '90vh',
      }
    );
  }

  /**
   * Fetches the list of countries from the business service and updates the component's country list.
   */
  getCountries() {
    this._businessService.getCountries().subscribe((resp) => {
      this.countries = resp.result;
    });
  }

  /**
   * Fetches the list of business groups from the business service and updates the component's business group list.
   */
  getBusinessGroup() {
    this._businessService.getBusinessGroup().subscribe((resp) => {
      this.businessGroups = resp.result;
    });
  }

  /**
   * Handles the creation of a new business.
   * Constructs the business model, submits it to the business service, and processes the response.
   * If the creation is successful, uploads related images and navigates back to the business dashboard.
   */
  async postBusiness() {
    let dataSetting =  await this._settingService.getDataSettingInit();
    const businessModel = {
      pk_i_IdBusiness: 0,
      fk_i_IdBusinessGroup: parseInt(this.form.value.fk_i_IdBusinessGroup),
      v_Name: this.form.value.v_Name,
      v_Document: this.form.value.v_Document,
      v_BusinessName: this.form.value.v_BusinessName,
      fk_i_IdCountry: this.form.value.country,
      fk_i_IdUser: dataSetting.idUser,
      b_EnableClientPortal: this.form.value.b_EnableClientPortal ?? false,
      v_SubDomainClientPortal: this.form.value.v_SubDomainClientPortal,
      v_SubDomainAdvisorPortal: this.form.value.v_SubDomainAdvisorPortal,
      d_CreationDate: new Date(),
      b_Active: this.form.value.b_Active,
      b_HasLogin: this.hasLogin_Checked,
      b_HasRegister: this.hasRegister_Checked,
      b_HasButtonIwantIt: this.hasButtonIWantIt_Checked,
      FileNameLogoAdvisor: this.form.value.vImageName,
      FileNameLogoClient: this.form.value.vImageNameClientPortalLogo

    };

    this._businessService
      .postBusiness(businessModel)
      .subscribe(async (resp) => {
        if (!resp.error) {
          const dataSetting = await this._settingService.getDataSettingInit();
          const postByCountryModel = {
            pk_i_IdBusinessByCountry: 0,
            fk_i_IdBusiness: resp.result,
            fk_i_IdCountry: this.form.value.country,
            fk_i_IdUploadFile: 0,
            fk_i_IdUser: dataSetting.idUser,
            b_EnableClientPortal: this.form.value.b_EnableClientPortal ?? false,
            v_SubDomainClientPortal: this.form.value.v_SubDomainClientPortal,
            v_SubDomainAdvisorPortal: this.form.value.v_SubDomainAdvisorPortal,
            sliderImages: this.sliderImages,
          };

          if (!resp.error) {
            this.idBusinessCountry = resp.result;
            this._spinnerService.showChanges(true);
            await this.uploadImageLogos();
            this._spinnerService.showChanges(false);
            this._messageService.messageSuccess(
              this._translateService.instant('Saved'),
              ''
            );
            this._customRouter.navigate(['/dashboard/business']);
          } else {
            this._spinnerService.showChanges(false);

            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                ': ' +
                this._translateService.instant(resp.message)
            );
          }
        } else {
          this._spinnerService.showChanges(false);

          this._messageService.messageError(
            this._translateService.instant('ThereWasAError') +
              ': ' +
              this._translateService.instant(resp.message)
          );
        }
      });
  }

  /**
   * Uploads the advisor portal and client portal logos to the server.
   * Returns a Promise that resolves when both images are successfully uploaded.
   *
   * @returns {Promise<void>} - A Promise that resolves when the images are uploaded successfully or rejects if an error occurs.
   */
  uploadImageLogos(): Promise<void> {
    return new Promise((resolve, reject) => {
      const logoAdvisorPortal: ImageUploadModel = {
        fileName: this.generateUUID(this.form.value.vImageName),
        imageBase64: this.form.value.vImageBase64,
        fkIIdBusinessByCountry: this.idBusinessCountry,
        pkIIdInsuranceCompanies: 0,
        type: 2,
        categoryCode: 'LPA',
      };

      if (this.form.value.b_EnableClientPortal) {
        const logoClientPortal = {
          pkIIdInsuranceCompanies: 0,
          fkIIdBusinessByCountry: this.idBusinessCountry,
          fileName: this.generateUUID(
            this.form.value.vImageNameClientPortalLogo
          ),
          imageBase64: this.form.value.vImageBase64ClientPortalLogo,
          type: 2,
          categoryCode: 'LPC',
        };

        forkJoin([
          this._fileService.uploadImage(logoAdvisorPortal),
          this._fileService.uploadImage(logoClientPortal),
        ]).subscribe(
          () => resolve(),
          (error) => {
            this._spinnerService.showChanges(false);
            console.error('Error in one of the upload requests', error);
            reject(error);
          }
        );
      } else {
        forkJoin([this._fileService.uploadImage(logoAdvisorPortal)]).subscribe(
          () => resolve(),
          (error) => {
            this._spinnerService.showChanges(false);
            console.error('Error in one of the upload requests', error);
            reject(error);
          }
        );
      }
    });
  }

  /**
   * Uploads the advisor portal logo to the server.
   * Returns a Promise that resolves when the image is successfully uploaded.
   *
   * @returns {Promise<void>} - A Promise that resolves when the image is uploaded successfully or rejects if an error occurs.
   */
  uploadImageAdvisorPortal(): Promise<void> {
    return new Promise((resolve, reject) => {
      const logoAdvisorPortal: ImageUploadModel = {
        fileName: this.generateUUID(this.form.value.vImageName),
        imageBase64: this.form.value.vImageBase64,
        fkIIdBusinessByCountry: this.idBusinessCountry,
        pkIIdInsuranceCompanies: 0,
        type: 2,
        categoryCode: 'LPA',
      };

      this._fileService.uploadImage(logoAdvisorPortal).subscribe(
        () => resolve(),
        (error) => {
          this._spinnerService.showChanges(false);
          console.error('Error in the upload request', error);
          reject(error);
        }
      );
    });
  }

  /**
   * Uploads the client portal logo to the server.
   * Returns a Promise that resolves when the image is successfully uploaded.
   *
   * @returns {Promise<void>} - A Promise that resolves when the image is uploaded successfully or rejects if an error occurs.
   */
  uploadImagClientPortal(): Promise<void> {
    return new Promise((resolve, reject) => {
      const logoClientPortal: ImageUploadModel = {
        pkIIdInsuranceCompanies: 0,
        fkIIdBusinessByCountry: this.idBusinessCountry,
        fileName: this.generateUUID(this.form.value.vImageNameClientPortalLogo),
        imageBase64: this.form.value.vImageBase64ClientPortalLogo,
        type: 2,
        categoryCode: 'LPC',
      };

      this._fileService.uploadImage(logoClientPortal).subscribe(
        () => resolve(),
        (error) => {
          this._spinnerService.showChanges(false);
          console.error('Error in the upload request for Client Portal', error);
          reject(error);
        }
      );
    });
  }

  /**
   * Validates the necessary conditions before proceeding with a business update (PUT).
   * Ensures that required logos are provided if needed.
   *
   * @returns {boolean} - Returns `true` if all validations pass; otherwise, returns `false`.
   */
  validationsPut(): boolean {
    if (
      this.imageSrc.length === 0 &&
      !this.form.value.vImageName &&
      !this.form.value.vImageBase64
    ) {
      this._messageService.messageError(
        this._translateService.instant('Business.LogoPortalAdvisorRequired')
      );
      return false;
    }

    this.form.value.b_EnableClientPortal =
      this.form.value.b_EnableClientPortal ?? false;

    if (
      this.form.value.b_EnableClientPortal &&
      this.imageSrcClientPortalLogo.length === 0 &&
      (!this.form.value.vImageBase64ClientPortalLogo ||
        !this.form.value.vImageNameClientPortalLogo)
    ) {
      this._messageService.messageError(
        this._translateService.instant(
          'Business.IfYouActivateTheClientPortalItIsMandatoryToProvideTheLogoClientPortal'
        )
      );
      return false;
    }

    return true;
  }

  /**
   * Handles the process of updating business information, including the management of related images.
   * This method performs the following steps:
   * 1. Builds the business model.
   * 2. Updates or uploads the advisor and client portal logos.
   * 3. Manages the slider images, including deletions and uploads.
   *
   * @returns {Promise<void>} - A promise that resolves when the update process is complete.
   */
  async putBusiness(): Promise<void> {
    // Build the business model to be updated
    const businessModel = this.buildBusinessModel();

    // Prepare the upload models for advisor and client portal logos
    const imageUpdateAdvisorLogoUploadModel = {
      pkIIdUploadFile: this.idImage,
      imageBase64: this.form.value.vImageBase64,
      fileName: this.generateUUID(this.form.value.vImageName),
      pkIIdInsuranceCompanies: 0,
      isImageEdit: this.isImageEdit,
      type: 2,
    };

    let imageUpdateClientLogoUp = null;
    if (this.form.value.b_EnableClientPortal){
      imageUpdateClientLogoUp = {
        pkIIdUploadFile: this.idImageClientPortal,
        imageBase64: this.form.value.vImageBase64ClientPortalLogo,
        fileName: this.generateUUID(this.form.value.vImageNameClientPortalLogo),
        pkIIdInsuranceCompanies: 0,
        isImageEdit: this.isImageEdit,
        type: 2,
      };
    }
   
    this._spinnerService.showChanges(true);
    // Handle updating or uploading the advisor portal logo
    await this.handleImageUpdateOrUpload(
      this.imageSrc.length,
      this.form.value.vImageBase64,
      this.idImage,
      imageUpdateAdvisorLogoUploadModel,
      'LPA',
      this.uploadImageAdvisorPortal.bind(this)
    );

    // Handle updating or uploading the client portal logo
    await this.handleImageUpdateOrUpload(
      this.imageSrcClientPortalLogo.length,
      this.form.value.vImageBase64ClientPortalLogo,
      this.idImageClientPortal,
      imageUpdateClientLogoUp,
      'LPC',
      this.uploadImagClientPortal.bind(this)
    );

    // Manage the slider images, including handling deletions and uploads
    await this.handleSliderImages();
    this._spinnerService.showChanges(false);
    
    // Make the final PUT request to update the business data
    this._businessService
      .putBusiness(businessModel, this.idBusiness)
      .subscribe(() => {
        this._messageService.messageSuccess(
          this._translateService.instant('Saved'),
          ''
        );
        this._customRouter.navigate(['/dashboard/business']);
      });
  }

  /**
   * Constructs and returns the business model based on the current form values and component state.
   * This model is used for updating or creating a business entity.
   *
   * @returns {any} - The constructed business model.
   */
  private buildBusinessModel(): any {
    return {
      pk_i_IdBusiness: this.idBusiness,
      fk_i_IdBusinessGroup: parseInt(this.form.value.fk_i_IdBusinessGroup), // Parse business group ID to an integer
      v_Name: this.form.value.v_Name,
      v_Document: this.form.value.v_Document,
      v_BusinessName: this.form.value.v_BusinessName,
      b_EnableClientPortal: this.form.value.b_EnableClientPortal ?? false, // Default to false if undefined
      v_SubDomainClientPortal: this.form.value.v_SubDomainClientPortal,
      v_SubDomainAdvisorPortal: this.form.value.v_SubDomainAdvisorPortal,
      b_Active: this.form.value.b_Active,
      d_CreationDate: new Date(), // Set creation date to current date
      fk_i_IdCountry: this.form.value.country,
      fk_i_IdBusinessCountry: this.idBusinessCountry,
      b_HasLogin: this.hasLogin_Checked,
      b_HasRegister: this.hasRegister_Checked,
      b_HasButtonIwantIt: this.hasButtonIWantIt_Checked
    };
  }

  /**
   * Handles the logic for updating or uploading an image based on the current state.
   * If the image exists and needs to be updated, it attempts to update it;
   * otherwise, it uploads a new image. Additionally, if images already exist,
   * they are deleted before uploading a new one.
   *
   * @param {number} imageSrcLength - The length of the source image array to determine if images already exist.
   * @param {string} formImageBase64 - The Base64 string of the image from the form.
   * @param {number} idImage - The ID of the image to be updated.
   * @param {any} imageUpdateModel - The model containing the updated image data.
   * @param {string} codeCategory - The category code used to identify the type of image.
   * @param {() => void} uploadImage - The function to call to upload the image if needed.
   * @returns {Promise<void>} - A Promise that resolves when the process is complete.
   */
  private async handleImageUpdateOrUpload(
    imageSrcLength: number,
    formImageBase64: string,
    idImage: number,
    imageUpdateModel: any,
    codeCategory: string,
    uploadImage: () => void
  ): Promise<void> {
    // Check if there are no existing images and a new image is provided
    if (imageSrcLength === 0 && formImageBase64) {
      // Validate if the image exists by ID
      const imageExists =
        idImage !== 0 && (await this.validateImageExistenceById(idImage));

      // Attempt to update the image if it exists
      const updateSuccess =
        imageExists && (await this.updateImage(imageUpdateModel));

      // If the update was not successful, upload a new image
      if (!updateSuccess) {
        uploadImage();
      }
    }

    // If images already exist, delete them and upload the new one
    if (this.imageSrc.length !== 0 && formImageBase64) {
      this._fileService
        .deleteAllImagesByIdBusinessCountryAndCodeCategory(
          this.idBusinessCountry,
          codeCategory
        )
        .subscribe(() => {
          uploadImage();
        });
    }
  }

  /**
   * Handles the processing of slider images, including filtering, deletion, and uploading.
   * This method ensures that only valid images are kept, removes images that are no longer present,
   * and uploads new images if required.
   *
   * @returns {Promise<void>} - A Promise that resolves when all slider image operations are complete.
   */
  private async handleSliderImages(): Promise<void> {
    
    // Filter out any slider images that do not have a valid name
    this.sliderImages = this.sliderImages.filter((r) => r.name != '');

    // Identify and delete removed images
    const idsRemoved = this.getRemovedIds(this.sliderImages);
    if (idsRemoved.length > 0) {
      idsRemoved.forEach((r) => {
        this._fileService.deleteFile(r.pkIIdUploadFile).subscribe();
      });
    }

    // Identify and delete disappeared images based on initial state
    if (this.sliderImagesInitial.length >= this.sliderImages.length) {
      const disappearedIds = this.getDisappearedIds(
        this.sliderImagesInitial,
        this.sliderImages
      );
      disappearedIds.forEach((r) => {
        this._fileService.deleteFile(r).subscribe();
      });
    }

    // If the client portal is enabled and there are slider images, upload them
    if (this.form.value.b_EnableClientPortal && this.sliderImages.length > 0) {
      this.sliderImages.forEach(async (element) => {
        if (element.imageBase64) {
          this._fileService
            .uploadImage({
              fileName: this.generateUUID(element.name),
              fkIIdBusinessByCountry: this.idBusinessCountry,
              imageBase64: element.imageBase64,
              type: 2,
              categoryCode: 'SPC',
              pkIIdInsuranceCompanies: 0,
            })
            .subscribe();
        }
      });
    }
  }

  /**
   * Validates if an image exists in the system based on its ID.
   *
   * @param {number} idImage - The ID of the image to validate.
   * @returns {Promise<boolean>} - A promise that resolves to `true` if the image exists, otherwise `false`.
   */
  validateImageExistenceById(idImage: number): Promise<boolean> {
    return new Promise((resolve) => {
      this._fileService.validateImageExistenceById(idImage).subscribe(
        (resp) => {
          resolve(resp.result);
        },
        (error) => {
          // Log the error if the validation fails
          console.error('Error validating image existence by ID:', error);
          this._spinnerService.showChanges(false);
          resolve(false);
        }
      );
    });
  }

  /**
   * Updates an existing image in the system.
   *
   * @param {ImageUpdateUpload} imageUpdateUploadModel - The model containing the updated image data.
   * @returns {Promise<boolean>} - A promise that resolves to `true` if the update is successful, otherwise `false`.
   */
  updateImage(imageUpdateUploadModel: ImageUpdateUpload): Promise<boolean> {
    return new Promise((resolve) => {
      this._fileService.updateUploadImage(imageUpdateUploadModel).subscribe(
        () => {
          resolve(true);
        },
        (error) => {
          // Log the error if the update fails
          console.error('Error updating the image:', error);            
          this._spinnerService.showChanges(false);
          resolve(false);
        }
      );
    });
  }

  /**
   * Handles the change event when an image in the slider is updated.
   *
   * @param {ImageModel} event - The event containing the updated image data.
   * @param {number} index - The index of the image in the slider array to update.
   * @returns {void}
   */
  onSliderImageChange(event: ImageModel, index: number): void {
    const imageName: string = event.dataJson[0].name.split('.')[0];
    const extension: string = event.dataJson[0].type.split('/')[1];
    const base64: string = event.dataString.split(',')[1];

    // Update the image details in the slider array
    this.sliderImages[
      index
    ].imageSrc = `data:image/${extension};base64,${base64}`;
    this.sliderImages[index].name = `${imageName}.${extension}`;
    this.sliderImages[index].codeCategory = 'SPC';
    this.sliderImages[index].imageBase64 = base64;

    // Add a new empty slot for the slider image if needed
    this.addSliderImage();
  }

  /**
   * Adds a new empty image slot to the slider if the total number of images is less than 3.
   *
   * @returns {void}
   */
  addSliderImage(): void {
    if (this.sliderImages.length < 3) {
      this.sliderImages.push({
        imageSrc: '',
        name: '',
        imageBase64: '',
        codeCategory: '',
      });
    }
  }

  /**
   * Removes an image from the slider at the specified index and adds a new empty slot if needed.
   *
   * @param {number} index - The index of the image in the slider array to remove.
   * @returns {void}
   */
  removeSliderImage(index: number): void {
    this.sliderImages.splice(index, 1);
    this.addSliderImage();
  }

  /**
   * Filters the provided array to return only the items that have a valid `pkIIdUploadFile`
   * and an associated `imageBase64` value.
   *
   * @param {any[]} currentArray - The array of items to filter.
   * @returns {{ pkIIdUploadFile: number; imageBase64: boolean }[]} - The filtered array of objects containing `pkIIdUploadFile` and `imageBase64` status.
   */
  getRemovedIds(
    currentArray: any[]
  ): { pkIIdUploadFile: number; imageBase64: boolean }[] {
    const currentIds = currentArray
      .map((item) => ({
        pkIIdUploadFile: item.pkIIdUploadFile,
        imageBase64: !!item.imageBase64, // Simplified to use double negation for boolean conversion
      }))
      .filter((r) => r.imageBase64 && r.pkIIdUploadFile);

    return currentIds;
  }

  /**
   * Identifies and returns the IDs of items that are present in the original array but are missing in the current array.
   *
   * @param {any[]} originalArray - The original array containing the initial set of items.
   * @param {any[]} currentArray - The current array to compare against the original.
   * @returns {number[]} - An array of IDs that are present in the original array but not in the current array.
   */
  getDisappearedIds(originalArray: any[], currentArray: any[]): number[] {
    
    // Extract the IDs from the original array
    const originalIds = originalArray.map((item) => item.pkIIdUploadFile);

    // Extract the IDs from the current array
    const currentIds = currentArray.map((item) => item.pkIIdUploadFile).filter(r=> r);

    // Filter the IDs that are in the original array but not in the current array
    return originalIds.filter((id) => !currentIds.includes(id));
  }

  /**
   * Generates a UUID (Universally Unique Identifier) and appends it to the provided file name, preserving the file extension.
   *
   * The UUID is generated based on random numbers. It has the format 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx',
   * where each 'x' and 'y' is replaced with a random hexadecimal digit. The '4' in the sequence indicates
   * that this is a version 4 UUID. The generated UUID is appended to the original file extension.
   *
   * @param {string} fileName - The original file name, including its extension (e.g., 'image.png').
   * @returns {string} The new file name with the UUID appended before the file extension.
   */
  generateUUID(fileName: string): string {
    if (!fileName) {
      return '';
    }
    const extension = fileName.slice(fileName.lastIndexOf('.'));

    // Generate the UUID
    const uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16); // Convert the number to a hexadecimal string
      }
    );

    return `${uuid}${extension}`;
  }
}
