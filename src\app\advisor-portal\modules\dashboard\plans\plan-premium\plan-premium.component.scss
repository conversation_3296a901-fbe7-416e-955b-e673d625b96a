.listbox-container {
  display: block;
  border: 1px solid black;
}

.chip-container {
  margin-left: 9%;
  display: block;
  border: 1px solid black;
}

.listbox-invalid {
  border-color: red;
}

.listbox-label {
  display: block;
  padding: 5px;
}

.listbox-invalid .example-listbox-label {
  color: red;
}

.listbox {
  list-style: none;
  padding: 0;
  margin: 0;
  height: 200px;
  overflow: auto;
}

.option-focus {
  position: relative;
  padding: 5px 5px 5px 25px;
}

.option-focus[aria-selected='true']::before {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24" width="24"><path d="m9.55 18-5.7-5.7 1.425-1.425L9.55 15.15l9.175-9.175L20.15 7.4Z"/></svg>'); /* stylelint-disable-line */
  background-size: cover;
  position: absolute;
  left: 2px;
}

.option-focus:focus {
  background: rgba(0, 0, 0, 0.2);
}

.margin-section {
  margin: 12px 0;
}

.margin-radioButton {
  margin: 0 10px;
}


.form-premium {
  min-width: 150px;
  max-width: 500px;
  width: 100%;
}