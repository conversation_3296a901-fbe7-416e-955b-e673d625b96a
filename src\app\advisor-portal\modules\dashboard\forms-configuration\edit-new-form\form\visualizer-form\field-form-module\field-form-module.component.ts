import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TypeField } from 'src/app/shared/models/form/form.model';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { MatNativeDateModule } from '@angular/material/core';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { Store } from '@ngrx/store';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { Subscription, catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { addFile } from 'src/app/store/actions';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';

@Component({
  selector: 'app-field-form-module',
  standalone: true,
  imports: [
    CommonModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    MatRadioModule,
    MatNativeDateModule,
    ValidationInputFileDirective
  ],
  templateUrl: './field-form-module.component.html',
  styleUrls: ['./field-form-module.component.scss'],
})
export class FieldFormModuleComponent implements OnInit {
  formName: FormGroup;
  fieldSubs?: Subscription;
  catalogs: any[] = [];
  selectedFileName: string = '';
  @Input() field: any = {};
  @Input() showPreview: boolean = false;
  constructor(
    public _fieldSvc: FieldService,
    private store: Store,
    private _msgSvc: MessageService,
    private formgroupDirective: FormGroupDirective
  ) {
    this.formName = formgroupDirective.control;
  }

  ngOnInit(): void {
    if (this.field.fkIIdCatalog != null) {
      this.getFieldType(this.field.fkIIdCatalog);
    }
  }

  // Función para procesar la cadena de formatos separados por comas y generar el valor para "accept"
  parseAcceptFormats(formatsString: string): string {
    if (formatsString != null) {
      const formatsArray = formatsString
        .split(',')
        .map((format) => format.trim());
      return formatsArray.map((format) => '.' + format.toLowerCase()).join(',');
    }
    return '';
  }

  ngOnDestroy(): void {
    this.fieldSubs?.unsubscribe();
  }

  getFieldType(idCatalog: number) {
    this.fieldSubs = this._fieldSvc
      .getCatalogById(idCatalog)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring('Advertencia', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError('Hubo un error' + resp.message);
          } else {
            this.catalogs = JSON.parse(resp.result.vJson);
          }
        }
      });
  }

  onInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const maxLength = this.field.iMaxLength;
    const minLength = this.field.iMinLength;
    if (target.value.length > maxLength) {
      target.value = target.value.slice(0, maxLength);
    } else if (target.value.length < minLength) {
      // Rellenar con ceros a la izquierda para cumplir con la longitud mínima
      const leadingZeros = '0'.repeat(minLength - target.value.length);
      target.value = leadingZeros + target.value;
    }
  }

  blockNumbers(event: KeyboardEvent) {
    const inputChar = String.fromCharCode(event.which || event.keyCode);
    if (/^\d+$/.test(inputChar)) {
      event.preventDefault();
    }
  }

  allowOnlyNumbers(event: KeyboardEvent) {
    const inputChar = String.fromCharCode(event.which || event.keyCode);
    if (!/^\d+$/.test(inputChar)) {
      event.preventDefault();
    }
  }

  onFileChange(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    const files = inputElement.files;

    let fileName: Array<string> = [];

    if (files && files.length > 0) {
      this.selectedFileName = files[0].name;

      // Guardar los nombres en un array
      for (let i = 0; i < files.length; i++) {
        fileName.push(files[i].name);
      }
      this.formName
        .get(this.field.pkIIdFieldModule + '_' + this.field.vNameFieldDb)
        ?.setValue(fileName);

      // Crear una copia inmutable de cada objeto de archivo para asegurar inmutabilidad
      const copiedFiles: File[] = Array.from(files).map(
        (file) => new File([file], file.name, { type: file.type })
      );

      // Guardar los nombres y los archivos copiados en el Store para guardarlos en el componente padre.
      const file: any = {
        files: copiedFiles,
        nameField: this.field.vNameFieldDb,
      };
      this.store.dispatch(addFile({ file: file }));
    }
  }

  public get TypeField(): typeof TypeField {
    return TypeField;
  }
}
