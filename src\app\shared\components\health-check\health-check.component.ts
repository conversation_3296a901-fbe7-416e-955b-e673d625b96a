// Angular SSR Health Check Component
import { Component, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser, isPlatformServer, CommonModule } from '@angular/common';

@Component({
    selector: 'app-health-check',
    templateUrl: './health-check.component.html',
    styleUrls: ['./health-check.component.scss'],
    standalone: true,
    imports: [CommonModule]
})
export class HealthCheckComponent implements OnInit {
    healthStatus = 'Healthy';
    renderPlatform = 'Unknown';
    initialRenderPlatform = 'Unknown';
    timestamp = new Date().toISOString();
    isBrowser = false;
    hostname = '';
    wasSSRRendered = false;

    // Health data object
    healthData = {
        server: true // Set to true for healthy server
    };

    constructor(
        @Inject(PLATFORM_ID) private readonly platformId: Object
    ) {
        // Set the platform immediately in the constructor
        if (isPlatformServer(this.platformId)) {
            // On server
            this.renderPlatform = 'Server (SSR)';
            this.initialRenderPlatform = 'Server (SSR)';
            this.wasSSRRendered = true;
        } else if (isPlatformBrowser(this.platformId)) {
            // On browser initial load
            this.isBrowser = true;
            
            // Check if this was initially server rendered
            if (typeof document !== 'undefined') {
                // Look for server-rendered attribute on our component
                const ssrMarker = document.querySelector('[data-ssr-rendered]');
                this.wasSSRRendered = !!ssrMarker;
                
                // If we have the SSR marker, remember that initial render was SSR
                if (this.wasSSRRendered) {
                    this.initialRenderPlatform = 'Server (SSR)';
                    this.renderPlatform = 'Server (SSR) → Browser (Hydration)';
                } else {
                    this.initialRenderPlatform = 'Browser (CSR)';
                    this.renderPlatform = 'Browser (CSR)';
                }
            }
            
            // Get hostname for Azure App Service detection
            if (typeof window !== 'undefined') {
                this.hostname = window.location.hostname;
            }
        }
    }    ngOnInit() {
        // Update timestamp to ensure it's accurate in both server and browser contexts
        this.timestamp = new Date().toISOString();
        
        // Check URL for health parameter
        if (this.isBrowser && typeof window !== 'undefined') {
            const urlParams = new URLSearchParams(window.location.search);
            const healthParam = urlParams.get('health');
            
            if (healthParam === 'false') {
                this.healthData.server = false;
                this.healthStatus = 'Unhealthy';
            }
        }
    }
      

}