import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PageSeeDetailMovementHistoryIndividualPolicyComponent } from './page-see-detail-movement-history-individual-policy.component';

describe('PageSeeDetailMovementHistoryIndividualPolicyComponent', () => {
  let component: PageSeeDetailMovementHistoryIndividualPolicyComponent;
  let fixture: ComponentFixture<PageSeeDetailMovementHistoryIndividualPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ PageSeeDetailMovementHistoryIndividualPolicyComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PageSeeDetailMovementHistoryIndividualPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
