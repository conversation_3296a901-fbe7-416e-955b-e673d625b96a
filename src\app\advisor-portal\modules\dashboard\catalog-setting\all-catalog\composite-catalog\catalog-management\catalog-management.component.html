<mat-tab-group>
    <mat-tab *ngIf="currentTab === TypeTabEnum.Management" [label]="'CatalogSetting.CatalogManagement' | translate">
      <ng-template matTabContent>
        <app-edit-catalog-management (setActiveTabCatalog)="setActiveTabCatalog($event)" [idCatalog]="idCatalog"
         [idBusinessCountry]="idBusinessCountry" [idCountry]="idCountry" ></app-edit-catalog-management>
      </ng-template>
    </mat-tab>
    <mat-tab *ngIf="currentTab === TypeTabEnum.Sheet" [label]="'CatalogSetting.Sheet' | translate">
      <ng-template matTabContent>
        <app-edit-catalog-sheet (setActiveTabCatalog)="setActiveTabCatalog($event)" [idCatalog]="idCatalog"
        [idBusinessCountry]="idBusinessCountry" [idCountry]="idCountry"></app-edit-catalog-sheet>
      </ng-template>
    </mat-tab>
    <mat-tab *ngIf="currentTab === TypeTabEnum.Column" [label]="'CatalogSetting.Column' | translate">
      <ng-template matTabContent>
        <app-edit-catalog-column (setActiveTabCatalog)="setActiveTabCatalog($event)" [idCatalog]="idCatalog"></app-edit-catalog-column>
      </ng-template>
    </mat-tab>
    <mat-tab *ngIf="currentTab === TypeTabEnum.FileUpload" [label]="'CatalogSetting.UploadFile' | translate">
        <ng-template matTabContent>
          <app-upload-composite-catalog (setActiveTabCatalog)="setActiveTabCatalog($event)"></app-upload-composite-catalog>
        </ng-template>
    </mat-tab>

  </mat-tab-group>
