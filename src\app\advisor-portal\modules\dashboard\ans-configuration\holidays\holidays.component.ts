import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GetListCountriesModel } from 'src/app/shared/models/business';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { catchError, of } from 'rxjs';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatDialog } from '@angular/material/dialog';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-holidays',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    TranslateModule,
    MatButtonModule,
    TableComponent,
    Modal2Component,
    FormsModule,
    MatDatepickerModule,
    MatNativeDateModule,
  ],
  templateUrl: './holidays.component.html',
  styleUrls: ['./holidays.component.scss'],
})
export class HolidaysComponent implements OnInit {
  //Variables relacionadas con el formulario.
  form: FormGroup = this._fb.group({});

  //Variables relacionadas con los select.
  countries: GetListCountriesModel[] = [];
  countrySelect: number = 0;

  //Variables para modal
  @ViewChild('AddHolidaysModal') AddHolidaysModal?: TemplateRef<any>;
  modalTitle: string = 'Añadir Festivo';

  //Variables relacionadas con la tabla.
  dataHolidaysTable: any[] = [];
  estructHolidaysTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'MyQuotation.AllQuotation.Date'
      ),
      columnValue: 'dDateHolidays',
      functionValue: (item: any) => this.formatDate(item.dDateHolidays),
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'Delete',
      columnIcon: 'delete',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Modify'
      ),
      columnValue: 'Modify',
      columnIcon: 'create',
    },
  ];

  constructor(
    private _fb: FormBuilder,
    private _businessService: BusinessService,
    public _translateService: TranslateService,
    private _messageService: MessageService,
    public modalDialog: MatDialog,
    private _parametersService: ParametersService,
    public _utilsSvc: UtilsService
  ) {}

  ngOnInit(): void {
    this.getCountries();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table Holidays
      this.estructHolidaysTable[0].columnLabel = this._translateService.instant(
        'MyQuotation.AllQuotation.Date'
      );
      this.estructHolidaysTable[1].columnLabel =
        this._translateService.instant('Status');
      this.estructHolidaysTable[2].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Modify'
      );
      this.estructHolidaysTable[3].columnLabel =
        this._translateService.instant('Delete');
    });
  }

  //Obtiene todos los paieses.
  getCountries() {
    this._businessService
      .getCountries()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.countries = resp.result;
          }
        }
      });
  }

  //Obtiene todos los festivos relacioandos a un pais.
  getHolidaysByIdCountry() {
    this._parametersService
      .getHolidaysByIdCountry(this.countrySelect)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataHolidaysTable = [];
        } else {
          this.dataHolidaysTable = resp.result;
        }
      });
  }

  //Obtiene los datos de un festivo por idHolidays .
  getByIdHolidays(idHolidays: number) {
    this._parametersService
      .getByIdHolidays(idHolidays)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.form.patchValue(resp.result);
        }
      });
  }

  //Inicialización del formulario.
  initForm() {
    this.form = this._fb.group({
      pkIIdHolidaysAns: [0],
      dDateHolidays: ['', [Validators.required]],
      bActive: [true],
      fkIIdCountry: [this.countrySelect],
    });
  }

  //Obtiene el valor actual, del campo pkIIdHolidaysAns en el formulario.
  get pkIIdHolidaysAns(): number {
    return this.form.get('pkIIdHolidaysAns')?.value;
  }

  //Controlador de la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'Modify':
        this.modalTitle = this._translateService.instant(
          'AnsConfiguration.Holidays.Modal.TitleEdit'
        );
        this.openModal();
        this.getByIdHolidays(event.value.pkIIdHolidaysAns);
        break;
      case 'Delete':
        this.deleteHolidays(event.value.pkIIdHolidaysAns);
        break;
      default:
        break;
    }
  }

  //Abre el modal para Añadir un festivo.
  openModal() {
    this.initForm();
    this.modalDialog.open(this.AddHolidaysModal!, {
      width: '720px',
    });
  }

  //Ejecuta acciones personalizadas después de cerrar el modal.
  closeModalActions() {}

  //Añade un festivo en el sistema.
  addHolidays() {
    this._parametersService
      .createHolidays(this.form.value)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess(
            '',
            this._translateService.instant(
              'ModulesSetting.SuccessfulMessageCreated'
            )
          );
          this.modalDialog.closeAll();
          this.getHolidaysByIdCountry();
        }
      });
  }

  //Función que elimina un festivo por medio de la llave idHolidays.
  deleteHolidays(idHolidays: number) {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant(
          'FormConfiguration.ProgressBar.DeleteMessageConfirm'
        ),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._parametersService
            .deleteHolidays(idHolidays)
            .pipe(
              catchError((error) => {
                if (error.error.error) {
                  this._messageService.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    error.error.message
                  );
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
              } else {
                this._messageService.messageSuccess(
                  '',
                  this._translateService.instant(
                    'FormConfiguration.ProgressBar.DeleteMessageSuccess'
                  )
                );
                this.getHolidaysByIdCountry();
              }
            });
        }
      });
  }

  //Función que edita un festivo.
  editHolidays() {
    this._parametersService
      .updateHolidays(this.form.value)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess(
            '',
            this._translateService.instant(
              'Modified'
            )
          );
          this.modalDialog.closeAll();
          this.getHolidaysByIdCountry();
        }
      });
  }

  //Formatea el valor de la fecha en la tabla, al formato DD-MM-YYYY
  formatDate(inputDate: string): string {
    if (!inputDate) {
      return '';
    }
    const date = new Date(inputDate);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  //Función que dececta cada vez qaue se cierra el modal.
  closeModal(event: boolean) {
    this.modalTitle = this._translateService.instant(
      'AnsConfiguration.Holidays.AddHolidayButton'
    );
  }
}
