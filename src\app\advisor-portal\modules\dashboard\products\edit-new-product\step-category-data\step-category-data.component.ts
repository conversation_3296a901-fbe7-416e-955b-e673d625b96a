import { CommonModule } from '@angular/common';
import {
  Compo<PERSON>,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subscription, catchError, of } from 'rxjs';

import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

import { MatSelectModule } from '@angular/material/select';
import { ActionsToCreateComponent } from 'src/app/shared/components/actions-to-create/actions-to-create.component';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';

import {
  CategoryModelCreate
} from 'src/app/shared/models/form/form.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { PreviewFormComponent } from '../preview-form/preview-form.component';


import { FieldService } from 'src/app/shared/services/field/field.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { AppState } from 'src/app/store/app.reducers';
import { PlansService } from 'src/app/shared/services/plans/plans.service';

@Component({
  selector: 'app-step-category-data',
  standalone: true,
  imports: [
    TableComponent,
    CommonModule,
    MatRadioModule,
    MatInputModule,
    MatFormFieldModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    GenericImagePickerComponent,
    ActionsToCreateComponent,
    ModalComponent,
    MatSelectModule,
    MatCheckboxModule,
    PreviewFormComponent,
    TranslateModule,
    ChooseCountryAndCompanyComponent,
    PreventionSqlInjectorDirective,
    Modal2Component,
  ],
  templateUrl: './step-category-data.component.html',
  styleUrls: ['./step-category-data.component.scss'],
})
export class StepCategoryDataComponent implements OnInit, OnDestroy {
  //Modals
  @ViewChild('editNewTabModal') editNewTabModal?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;

  dataTablecategory: any[] = [];

  isEditingcategory: boolean = false;
  orderListcategory: number[] = [];

  idCategory: number = 0;
  idProduct: number = 0;

  productSubs?: Subscription;
  formSubs?: Subscription;
  fieldSubs?: Subscription;
  productList?: Subscription;
  transactionSubs?: Subscription;

  constructor(
    public router: Router,
    private _productService: ProductService,
    public utilsSvc: UtilsService,
    public _fieldSvc: FieldService,
    public modalDialog: MatDialog,
    private _fb: FormBuilder,
    private _store: Store<AppState>,
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    private _planService: PlansService
  ) {}

  ngOnInit(): void {
    this.productSubs = this._store.select('product').subscribe((p) => {
      if (p.error) return this._msgSvc.messageError(p.error);
      this.idProduct = p.Product?.id == undefined ? 0 : p.Product?.id;

      this.getFormById(this.idProduct);
    });
  }

  /**
   * Lifecycle hook that is called when the component is destroyed.
   *
   * This method checks if there is an active subscription
   * If the subscription exists, it unsubscribes to prevent memory leaks.
   *
   * @memberof YourComponentName
   */
  ngOnDestroy(): void {
    if (this.formSubs) {
      this.formSubs.unsubscribe();
    }
    if (this.fieldSubs) {
      this.fieldSubs.unsubscribe();
    }
    if (this.productList) {
      this.productList.unsubscribe();
    }
    if (this.transactionSubs) {
      this.transactionSubs.unsubscribe();
    }
  }

  //category
  category: FormGroup = this._fb.group({
    pkIIdCategory: [0],
    fkIIdProduct: [0],
    bActive: [true],
    vName: [null],
    vDescription: [null],
  });

  formcategory: FormGroup = this._fb.group({
    pkIIdCategory: [0],
    fkIIdProduct: [0],
    bActive: [true],
    vName: [null, Validators.required],
    vDescription: [null, Validators.required],
  });

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Category'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Product.Description'),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  getFormById(IdProduct: number) {
    this.fieldSubs = this._fieldSvc
      .getCategoryByProduct(IdProduct)
      .pipe(
        catchError((respError) => {
          if (respError.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              respError.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.idCategory = resp.result.pkIIdCategory;
            this.category.patchValue({
              PkIIdCategory: resp.result.pkIIdCategory,
              BActive: resp.result.bActive,
              VName: resp.result.vName,
              FkIIdProduct: resp.result.fkIIdProduct,
              VDescription: resp.result.vDescription,
            });

            this.dataTablecategory = resp.result;

            this._productService.isValidForm = this.category.valid;
          }
        }
      });
  }

  controller(evt: IconEventClickModel) {
    console.log(evt);
    
    switch (evt.column) {
      case 'modify':
        this.open('editNewTabModal');
        this.isEditingcategory = true;
        this.formcategory.patchValue({
          pkIIdCategory: evt.value.pkIIdCategory,
          fkIIdProduct: evt.value.fkIIdProduct,
          bActive: evt.value.bActive,
          vName: evt.value.vName,
          vDescription: evt.value.vDescription,
        });
        break;

      case 'delete':
        this._msgSvc.messageConfirmationAndNegation(
          this._translateService.instant('Delete'), '¿' + this._translateService.instant('Reports.Utils.QuestionDelete') + '?', 'warning',
          this._translateService.instant('Accept'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            this.validatePlansByCategory(evt.value.pkIIdCategory);
          }
        });
        break;
    
      default:
        break;
    }

  }

  open(component: string) {
    var sizeConfiguration = {
      disableClose: false,
      width: '70vw',
      maxHeight: '90vh',
    };
    this.formcategory.patchValue({
      pkIIdCategory: 0,
      vName: null,
      vDescription: null,
    });
    var modal: TemplateRef<any>;
    switch (component) {
      case 'editNewTabModal':
        modal = this.editNewTabModal!;
        break;
      default:        
        return;
    }
    this.formcategory.markAsUntouched();
    // Abre el modal y guarda su referencia en la propiedad currentModal
    this.currentModal = this.modalDialog.open(modal, sizeConfiguration);
  }

  closeModal() {
    if (this.currentModal) {
      this.currentModal.close();
      this.currentModal = null; // Limpia la referencia después de cerrar el modal
    }
  }

  saveCategory() {
    this.formcategory.markAllAsTouched();
    if (this.formcategory.valid) {
      var caller;
      var CatG: CategoryModelCreate = this.formcategory.value;
      CatG.fkIIdProduct = this.idProduct;
      if (this.isEditingcategory) {
        caller = this._fieldSvc.updateCategory(CatG);
      } else {
        caller = this._fieldSvc.createCategory(CatG);
      }

      this.fieldSubs = caller
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant('Saved')
              );

              this.getFormById(this.idProduct);
            }
            this.closeModal();
          }
        });
    }
  }

  //Validate the list of plans associated with a category
  validatePlansByCategory(idCategory: number): void {
    this._planService
      .getPlansByIdCategory(idCategory)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.deleteCategory(idCategory);
        } else {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            this._translateService.instant('Product.CategoryNotDeleted'),
          );
        }
      });
  }

  deleteCategory(idCategory: number): void {
    this._fieldSvc
      .DeleteCategory(idCategory)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                resp.message
            );
          } else {
            this._msgSvc.messageSuccess(
              this._translateService.instant('FormConfiguration.ProgressBar.DeleteMessageSuccess'),
              this._translateService.instant('Product.CategoryDeleted')
            );
            this.getFormById(this.idProduct);
          }
        }
      });
  }
}
