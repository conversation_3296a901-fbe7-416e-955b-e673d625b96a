<app-task-filter
  [paginatorData]="paginatorData"
  [reassignedTasks]="reassignedTasks"
></app-task-filter>

<!-- datatable tareas -->
<div
  class="row mt-2"
  *ngIf="dataTableTaskTray.length > 0 && estructTableTaskTray.length > 1"
>
  <app-table
    [displayedColumns]="estructTableTaskTray"
    [data]="dataTableTaskTray"
    [IsStatic]="false"
    [pageIndex]="pageIndex"
    [pageSize]="pageSize"
    [amountRows]="amountRows"
    (pageChanged)="onPageChange($event)"
    (iconClick)="controller($event)"
  ></app-table>
</div>

<!-- boton reasignar tareas -->
<div class="row">
  <button
    [disabled]="userListByTaskIdTable.length <= 0"
    class="w-auto mx-2 mt-2"
    type="button"
    color="primary"
    mat-raised-button
    (click)="reassignTasks()"
  >
    {{ "ReassignTasks.Button" | translate }}
    <mat-icon iconPositionEnd fontIcon="call_split"></mat-icon>
  </button>
</div>

<!-- Modal Reasignar Tareas -->
<ng-template #reassignTasksModal>
  <app-modal2
    [titleModal]="'ReassignTasks.Title' | translate"
    (closeModal)="closeModalReassignTasks()"
  >
    <ng-container body>
      <form [formGroup]="formReassignTasks">
        <!--Tareas seleccionadas-->
        <div class="row mt-3">
          <p>
            <strong>{{ "ReassignTasks.SelectedTasks" | translate }} </strong>
            {{ userListByTaskIdTable.length }}
          </p>
        </div>

        <!--Usuario actual-->
        <div class="row mt-3">
          <p>
            <strong>{{ "ReassignTasks.CurrentUser" | translate }} </strong>
            <span class="user-names">
              {{ userNames }}
            </span>
          </p>
        </div>

        <div class="row mt-3">
          <!--Select  Usuario que recibe las tareas -->
          <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
              {{ "ReassignTasks.UserWhoReceivesTheTasks" | translate }}
            </mat-label>
            <mat-select formControlName="pkIIdUser">
              <mat-option
                *ngFor="let item of usersListreassignTasks"
                [value]="item.pkIIdUser"
              >
                {{ item.vPersonName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </form>
    </ng-container>

    <!-- botones limpiar/aplicar filtros -->
    <ng-container customButtonRight>
      <button
        class="w-auto"
        type="button"
        mat-raised-button
        color="primary"
        (click)="confirmationReasignTask()"
      >
        {{ "ReassignTasks.ModalButton" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
<!-- end modal filtros -->
