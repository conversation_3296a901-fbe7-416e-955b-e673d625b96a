<div class="row mt-5">
  <h3 class="col-md-12"> {{ 'CatalogSetting.SheetConfiguration' | translate }}
    <mat-icon class="click" matTooltipPosition="right"
      matTooltip="{{ 'Tooltips.CatalogFieldSubtitle' | translate }}">help_outline</mat-icon>
  </h3>
</div>

<div class="mt-5">
  <app-table [displayedColumns]="estructTable" [data]="sheetTable" (iconClick)="controller($event)"></app-table>
</div>

<div class="row">
  <div class="col-md-12">
    <button type="button" class="w-auto" (click)="openModal()" mat-raised-button color="primary">
      {{ 'CatalogSetting.Add' | translate }}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
</div>

<div class="cont-btns">
  <button class="mx-3" mat-raised-button type="button" (click)="goBackMain()">
    {{ 'CatalogSetting.ExitCatalogs' | translate }}
  </button>
  <button class="mx-3" mat-raised-button type="button" (click)="goBack()">
    {{ 'CatalogSetting.BackCatalog' | translate }}
  </button>
  <button [disabled]="sheetTable.length == 0" type="button" mat-raised-button color="primary" (click)="next()">
    {{ 'CatalogSetting.NextCatalog' | translate }}
    <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
  </button>
</div>

<!-- Modal add or modify sheet -->
<ng-template #AddSheetModal>
  <app-modal2 [titleModal]="modalTitle" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="form">
        <div class="row mt-5">

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-5" appearance="fill">
              <mat-label>
                {{ 'CatalogSetting.SheetName' | translate }}
              </mat-label>
              <input matInput type="text" formControlName="nameSheet" />
              <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'nameSheet', 'required')">
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      </form>
    </ng-container>
    <ng-container customButtonRight>
      <button *ngIf="!isEdit" type="button" mat-raised-button type="button" color="primary" (click)="saveSheet()">
        {{ 'Add' | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
      <button *ngIf="isEdit" type="button" mat-raised-button type="button" color="primary" (click)="updateSheet()">
        {{ 'CatalogSetting.Update' | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
