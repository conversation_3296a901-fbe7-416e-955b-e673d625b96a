import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent } from './page-see-detail-movement-history-colective-individual-policy.component';

describe('PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent', () => {
  let component: PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent;
  let fixture: ComponentFixture<PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
