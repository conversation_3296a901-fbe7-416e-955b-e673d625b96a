.file-download {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 8px;
    max-width: 300px;
    position: relative;
  }

  .file-info {
    display: flex;
    flex-direction: column;
    text-align: left;
  }

  .file-name {
    font-weight: bold;
  }

  .file-size {
    color: #666;
    font-size: 0.9em;
  }

  .download-button {
    position: absolute;
    bottom: 5px;
    right: 5px;
  }

  .select-container {
    display: flex;
    gap: 32px; /* Más espacio entre los selects */
    align-items: center; /* Alinear verticalmente */
    margin-bottom: 24px;
  }

  .custom-select {
    width: 200px; /* Ajustar ancho de los selects */
  }

  .mat-form-field-appearance-outline .mat-form-field-wrapper {
    padding: 0 16px; /* Ajustar el padding del campo */
  }

  .col-md-6.col-sm-12 {
    margin-bottom: 24px; /* Espacio adicional entre las filas */
  }

  .file-download {
    margin-bottom: 24px; /* Espacio adicional antes del área de descarga */
  }

  .upload-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    // justify-content: center; /* Centrar el contenido horizontalmente */
    gap: 0; /* Eliminar espacio entre el campo de entrada y el botón */
    padding: 8px;
    border-radius: 8px; /* Bordes redondeados */
    width: fit-content; /* Ajustar al contenido */
    margin: 0 auto;
    margin-bottom: 50px; /* Centrar el contenedor */
  }

  .upload-input {
    flex: 1;
    min-width: 300px; /* Ancho mínimo del campo de entrada */
  }

  .upload-button {
    color: white;
    border-radius: 16px; /* Bordes redondeados */
    margin-left: -16px; /* Superposición del botón sobre el borde */
    width: 50%;
  }

  .file-hint {
    font-size: 12px;
    color: gray;
    margin-top: 40px;
    text-align: center; /* Centrar el texto */
  }
  .col-md-12.col-sm-12 {
    margin-bottom: 24px; /* Espacio adicional entre las filas */
  }

  .contenedor {
    position: relative;
    display: flex;
    flex-direction: column;
    max-width: 430px;

    border: 2px solid #ccc; /* Borde rojo */
    border-radius: 8px;
    padding: 10px;
  }

  .labelModal {
    font-weight: bold;
    margin-bottom: 8px;
  }

  .input-file {
    width: 80%;
  }

  .input-file:hover {
    background-color: white;
  }


  .file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }

  .file-name {
    font-weight: 500;
  }

  .file-size {
    color: gray;
  }

  .iconos-dentro {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 10px;
  }



 .btn-seleccionar:hover {
    background-color: white;
  }


/* Contenedor principal de errores */
.error-container {
  margin-top: 20px;
  padding: 20px;
  border-radius: 16px;
  background: #f4f6f9;
  border-left: 5px solid #d7868b;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  color: #37474f;
  max-width: 60%;
  animation: fadeIn 0.5s ease-in-out;
}

.error-title {
  margin-top: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
}


.error-icon {
  margin-right: 12px;
  font-size: 2rem;
  color: #d7868b;
}


.error-list {
  max-height: 300px;
  overflow-y: auto;
  margin: 12px 0 0;
  padding-left: 0;
}


.error-item {
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 12px;
  background: #ffffff;
  display: flex;
  align-items: center;
  transition: background 0.3s ease;
  border: 1px solid #dce1e8;
}


.error-badge {
  background-color: #4a90e2;
  color: white;
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 0.8rem;
  margin-right: 12px;
  font-weight: bold;
}

.error-item:hover {
  background: #e9f2ff;
}

/* Animación de entrada */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scroll personalizado */
.error-list::-webkit-scrollbar {
  width: 8px;
}

.error-list::-webkit-scrollbar-thumb {
  background-color: #4a90e2; /* Azul pastel */
  border-radius: 10px;
}

.error-list::-webkit-scrollbar-track {
  background: #f4f6f9;
}

#validando {
  display: none; /* Oculto inicialmente */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 20px;
  border-radius: 5px;
  font-size: 18px;
}

.contenedor2 {
  position: relative;
  display: flex;
  flex-direction: column;
  max-width: 430px;
  height: 74px;
  background-color: #fff;
  border: 2px solid #ccc;
  border-radius: 8px;
  padding: 10px;
}

.file-hint2 {
  font-size: 12px;
  color: gray;
  margin-top: 6px;
  text-align: left; /* Centrar el texto */
}

.iconos-dentro2 {
  position: absolute;
  bottom: 10px;
  right: 39px;
  display: flex;
  gap: 60px;
  background-color: red;
  border-radius: 20px;
  width: 151px;
}

.file-button3 {
  padding: 8px 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s;
}

// test2

.file-container4 {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-input-container4 {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.file-input4 {
  border: none;
  border-bottom: 1px solid #888;
  padding: 8px;
  outline: none;
  width: 100%;
}

.file-hint4 {
  font-size: 12px;
  color: #888;
}

.file-button4 {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background-color: #7e39a0;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: background 0.3s;
}

.file-button4:hover {
  background-color: #6a2d85;
}

input[type="file"] {
  display: none;
}

// pop show
.popup-container {
  display: flex;
  justify-content: center; /* Centra el contenido horizontalmente */
  align-items: center;     /* Centra el contenido verticalmente */
  height: 100%;
}

.incentive-card {
  // display: flex;
  // flex-direction: column;
  // justify-content: center;
  // align-items: center;
  min-width: 300px;
  max-width: 320px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.scaled-image {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.overlay-title {
  position: absolute;
  text-align: center;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.valid-date, .description {
  margin: 0;
  padding: 4px 0;
  text-align: center;
}

.description{
  font-weight: bolder;
  color:black;
}

mat-card-content {
  text-align: left;
  padding: 16px;
}

mat-card-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

.valid-date, .description {
  margin: 0;
  padding: 4px 0;
}

.btn-conditions {
  margin-top: 12px;
  // background-color: #9c27b0;
  color: white;
}

// graphs///////////////////////////////////////////////////////
.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.summary {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.summary-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.icon {
  font-size: 40px;
}

.info {
  display: flex;
  gap: 16px;
}

.label {
  background: #E0E0E0;
  padding: 4px 8px;
  border-radius: 8px;
}

.ventas {
  color: #36BFAF;
}

.cancelaciones {
  color: #F2994A;
}

.incentivos {
  color: #2F80ED;
}

.detail-button {
  align-self: flex-start;
}

.dta {
  letter-spacing: initial;
  display: block;
  overflow: hidden;
}
