import { CommonModule } from '@angular/common';
import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  TranslateModule,
  TranslateService
} from '@ngx-translate/core';
import { debounceTime } from 'rxjs';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import {
  GroupModel,
  LevelModel,
  UserGroupLevelModel,
} from 'src/app/shared/models/groups';
import { EditNewGroupData } from 'src/app/shared/models/groups/edit-new-group-data.model';
import { GroupsService } from 'src/app/shared/services/groups/groups.service';
import { LevelsService } from 'src/app/shared/services/levels/levels.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-levels',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    TranslateModule,
    PreventionSqlInjectorDirective,
    MatTooltipModule
  ],
  templateUrl: './levels.component.html',
  styleUrls: ['./levels.component.scss'],
})
export class LevelsComponent implements OnInit, OnDestroy {
  @Input() action: string = '';
  @Input() idGroup: number = 0;
  @Input() gruopState: boolean = false;
  @Input() groupData: GroupModel = GroupModel.fromObj({});
  form: FormGroup = new FormGroup({});
  private _listLevel: LevelModel[] = [];

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    private _groupsService: GroupsService,
    private _levelsService: LevelsService,
    private _messageService: MessageService,
    public _translateService: TranslateService
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.getLevelsByIdGroup();
  }

  initForm() {
    this.form = this._fb.group({
      lavels: this._fb.array([]),
    });
    this.form.valueChanges.pipe(debounceTime(600)).subscribe({
      next: (data) => {
        this.addOrderAndIdGroup();
        let payload: EditNewGroupData;
        let user: UserGroupLevelModel[] = [
          {
            bActive: false,
            fkIIdLevel: 0,
            fkIIdUser: 0,
            pkIIdUserLevel: 0,
          },
        ];
        payload = {
          group: this.groupData,
          formgroupValid: true,
          level: data.lavels,
          forLevelValid: this.form.valid,
          user: user,
          forUserValid: false,
          typeAction: this.action,
        };
        this._groupsService.setCurrentEditNewGroupData(payload);
      },
    });
  }
  get lavels(): FormArray {
    return this.form.get('lavels') as FormArray;
  }

  newGroup(): FormGroup {
    return this._fb.group({
      pkIIdLevel: [0],
      vLevelName: ['', [Validators.required]],
      iOrder: [null],
      fkIIdGroup: [0],
      bActive: [true],
    });
  }

  addGroup() {
    this.lavels.push(this.newGroup());
  }

  removeGroup(i: number, level: any) {
    if (this.action === 'edit' && level.value.pkIIdLevel !== 0) {
      this._levelsService.deleteLevel(level.value).subscribe({
        next: (response) => {
          if (!response.error) {
            this.lavels.removeAt(i);
            this._messageService.messageSuccess('Correcto', response.message);
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('Group.CannotUserAssignedDeleted'),
            this._translateService.instant(
              'Group.CannotUserAssignedDeletedMessage'
            )
          );
        },
      });
    } else if (this.action === 'create' && level.value.pkIIdLevel !== 0) {
      this._levelsService.deleteLevel(level.value).subscribe({
        next: (response) => {
          if (!response.error) {
            this.lavels.removeAt(i);
            this._messageService.messageSuccess('Correcto', response.message);
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('Group.CannotUserAssignedDeleted'),
            this._translateService.instant(
              'Group.CannotUserAssignedDeletedMessage'
            )
          );
        },
      });
    } else {
      this.lavels.removeAt(i);
    }
  }

  addOrderAndIdGroup() {
    let levels = this.lavels.value;
    levels.forEach((element: LevelModel, index: number) => {
      element.iOrder = index + 1;
      element.fkIIdGroup = this.idGroup;
    });
    this.form.get('levels')?.setValue(levels);
  }

  getLevelsByIdGroup() {
    if (this.idGroup !== 0) {
      this._levelsService.getListLvelsByIdGroup(this.idGroup).subscribe({
        next: (response) => {
          if (!response.error) {
            this._listLevel = response.result.map((item: LevelModel) => ({
              pkIIdLevel: item.pkIIdLevel,
              vLevelName: item.vLevelName,
              bActive: item.bActive,
              iOrder: item.iOrder,
              fkIIdGroup: Number(item.fkIIdGroup),
            }));
            this._listLevel.forEach((element: LevelModel) => {
              this.lavels.push(
                this._fb.group({
                  pkIIdLevel: [element.pkIIdLevel],
                  vLevelName: [element.vLevelName, [Validators.required]],
                  iOrder: [element.iOrder],
                  fkIIdGroup: [element.fkIIdGroup],
                  bActive: [element.bActive],
                })
              );
            });
          }
        },
      });
    }
  }

  get valid(): boolean {
    return this.form.valid;
  }

  ngOnDestroy(): void {}
}
