<div class="modal-body">
  <div class="row">
    <form [formGroup]="formTypePerson">
      <!-- tipo de cliente -->
      <mat-form-field appearance="outline" class="w-25 mb-2">
        <mat-label> {{ "Clients.TipoClient" | translate }} </mat-label>
        <mat-select
          formControlName="vTypePerson"
          #vTypePerson
          (ngModelChange)="changeForm(vTypePerson.value)"
        >
          <mat-option
            *ngFor="let typeClient of typeClients"
            [value]="typeClient.id"
          >
            {{ typeClient.name }}
          </mat-option>
        </mat-select>
        <mat-error
          *ngIf="
            _utilsService.isControlHasError(
              formTypePerson,
              'vTypePerson',
              'required'
            )
          "
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <!-- Abierto - Cerrado -->
      <mat-radio-group formControlName="isClose" class="w-25 mb-2">
        <mat-radio-button class="example-margin" [value]="true">{{
          "Clients.RadioOpenCheck" | translate
        }}</mat-radio-button>
        <mat-radio-button class="example-margin" [value]="false">{{
          "Clients.RadioClosedCheck" | translate
        }}</mat-radio-button>
      </mat-radio-group>
    </form>
  </div>

  <ng-container
    *ngIf="this.typeClient === 1; then thenTemplate; else elseTemplate"
  >
  </ng-container>

   <!-- TEMPLATE FOR CLIENT -->
  <ng-template #thenTemplate>
    <div class="mb-3">
      <h3>{{ "Clients.TitlePerson" | translate }}</h3>
    </div>

    <form [formGroup]="formClient">
      <div class="row">
        <!-- tipo de documento -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ "Clients.TypeDocument" | translate }} </mat-label>
          <mat-select formControlName="iIdDocumentType">
            <mat-option
              *ngFor="let document of documentsTypesFiltered"
              [value]="document.id"
            >
              {{ document.name }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'iIdDocumentType',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- numero de documento -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ "Clients.FormSearchDoc" | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.FormSearchDoc' | translate"
            formControlName="vDocumentNumber"
            required
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'vDocumentNumber',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>

          <mat-error
            *ngIf="formClient.get('vDocumentNumber')?.hasError('pattern')"
          >
            {{ "Clients.InvalidNumber" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- primer nombre -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ "Clients.FirstName" | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.FirstName' | translate"
            formControlName="vFirstName"
            required
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'vFirstName',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- segundo nombre -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ "Clients.SecondName" | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.SecondName' | translate"
            formControlName="vSecondName"
            type="text"
          />
        </mat-form-field>
      </div>

      <div class="row">
        <!-- primer apellido -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ "Clients.Surname" | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.Surname' | translate"
            formControlName="vSurname"
            required
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'vSurname',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- segundo nombre -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.SecondSurname' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.SecondSurname' | translate"
            formControlName="vSecondSurname"
            type="text"
          />
        </mat-form-field>

        <!-- email -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.EmailClient' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.EmailClient' | translate"
            formControlName="VEmailUser"
            type="email"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'VEmailUser',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'VEmailUser',
                'invalidEmail'
              )
            "
          >
          {{ "Clients.InvalidEmail" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- celular -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.CellPhone' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.CellPhone' | translate"
            formControlName="vCellPhone"
            type="text"
          />

          <mat-error *ngIf="formClient.get('vCellPhone')?.hasError('pattern')">
            {{ "Clients.InvalidNumber" | translate }}
          </mat-error>

          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'vCellPhone',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </form>
  </ng-template>

  <!-- TEMPLATE FOR BUSINESS -->
  <ng-template #elseTemplate>
    <div class="mb-3">
      <h3>{{ "Empresa" }}</h3>
    </div>

    <form [formGroup]="formBusiness">
      <div class="row">
        <!-- tipo de documento -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ "Clients.TypeDocument" | translate }} </mat-label>
          <mat-select formControlName="iIdDocumentType">
            <mat-option
              *ngFor="let document of documentsTypesFiltered"
              [value]="document.id"
            >
              {{ document.name }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'iIdDocumentType',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- numero de documento -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ "Clients.FormSearchDoc" | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.FormSearchDoc' | translate"
            formControlName="vDocumentNumber"
            required
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'vDocumentNumber',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>

          <mat-error
            *ngIf="formBusiness.get('vDocumentNumber')?.hasError('pattern')"
          >
            {{ "Clients.InvalidNumber" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- Dígito de verificación -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.VerificationDigit' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.VerificationDigit' | translate"
            formControlName="vVerificationDigit"
            type="text"
          />

          <mat-error
            *ngIf="formBusiness.get('vVerificationDigit')?.hasError('pattern')"
          >
            {{ "Clients.InvalidNumber" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- Razón Social -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{'Clients.CompanyName' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.CompanyName' | translate"
            formControlName="vBusinessName"
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'vBusinessName',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row">
        <!-- email -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{'Clients.EmailClient' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.EmailClient' | translate"
            formControlName="VEmailUser"
            type="email"
          />

          <mat-error *ngIf="formBusiness.get('VEmailUser')?.hasError('pattern')">
            {{ 'Clients.InvalidEmail' | translate }}
          </mat-error>

          <!-- <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'VEmailUser',
                'invalidEmail'
              )
            "
          >
            El correo electrónico no es válido.
          </mat-error> -->
        </mat-form-field>

        <!-- celular -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.CellPhone' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.CellPhone' | translate"
            formControlName="vCellPhone"
            type="text"
          />

          <mat-error
            *ngIf="formBusiness.get('vCellPhone')?.hasError('pattern')"
          >
            {{ 'Clients.InvalidNumber' | translate }}
          </mat-error>

          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'vCellPhone',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="mb-3">
        <h3>{{ 'Clients.Contact' | translate }}</h3>
      </div>

      <div class="row">
        <!-- tipo de documento -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.TypeDocument' | translate }} </mat-label>
          <mat-select formControlName="iIdDocumentTypeContact">
            <mat-option
              *ngFor="let document of documentsTypes"
              [value]="document.id"
            >
              {{ document.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- numero de documento -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.FormSearchDoc' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.FormSearchDoc' | translate"
            formControlName="vDocumentNumberContact"
            type="text"
          />

          <mat-error
            *ngIf="
              formBusiness.get('vDocumentNumberContact')?.hasError('pattern')
            "
          >
            {{ 'Clients.InvalidNumber' | translate }}
          </mat-error>
        </mat-form-field>

        <!-- primer nombre -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.FirstName' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.FirstName' | translate "
            formControlName="vFirstNameContact"
            type="text"
          />
        </mat-form-field>

        <!-- segundo nombre -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.SecondName' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.SecondName' | translate"
            formControlName="vSecondNameContact"
            type="text"
          />
        </mat-form-field>
      </div>

      <div class="row">
        <!-- primer apellido -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.Surname' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.Surname' | translate"
            formControlName="vSurnameContact"
            type="text"
          />
        </mat-form-field>

        <!-- segundo nombre -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.SecondSurname' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.SecondSurname' | translate"
            formControlName="vSecondSurnameContact"
            type="text"
          />
        </mat-form-field>

        <!-- email -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.EmailClient' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.EmailClient' | translate"
            formControlName="VEmailUserContact"
            type="email"
          />

          <mat-error *ngIf="formBusiness.get('VEmailUserContact')?.hasError('pattern')">
           {{  'Clients.InvalidEmail' | translate  }}
          </mat-error>

          <!-- <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'VEmailUserContact',
                'invalidEmail'
              )
            "
          >
            El correo electrónico no es válido.
          </mat-error> -->
        </mat-form-field>

        <!-- celular -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
          <mat-label> {{ 'Clients.CellPhone' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.CellPhone' | translate"
            formControlName="vCellPhoneContact"
            type="text"
          />

          <mat-error
            *ngIf="formBusiness.get('vCellPhoneContact')?.hasError('pattern')"
          >
            {{ 'Clients.InvalidNumber' | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row">

        <!-- role -->
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label> {{ 'Clients.Role' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.Role' | translate"
            formControlName="vRoleContact"
            type="text"
          />


        </mat-form-field>
      </div>
    </form>
  </ng-template>
</div>

<!-- save button -->
<div class="d-flex justify-content-center gap-3">
  <button
  (click)="goToClient()"
    type="button"
    mat-raised-button
    class="w-150px"
    [routerLink]="['#']"
  >
    {{ 'Clients.CancelClient' | translate }}
  </button>

  <button
    type="button"
    mat-raised-button
    [disabled]="typeClient == 1 ? !formClient.valid : !formBusiness.valid"
    color="primary"
    class="w-150px"
    (click)="registerPersonDinamically()"
  >
    {{ 'Clients.CreateClient' | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
</div>
