import { <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, inject, Inject, input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder,FormsModule, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { DomSanitizer } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { TranslateService,TranslateModule } from '@ngx-translate/core';
import { Chart, ChartData, ChartOptions } from 'chart.js';
import { NgChartsModule } from 'ng2-charts';
import { FileService } from 'src/app/shared/services/file/file.service';
import { IncentivesService } from 'src/app/shared/services/incentives/incentives.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import {  Detail, Statistic, StatisticsData } from 'src/app/shared/models/incentives/statistics-info.model';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatIconModule } from '@angular/material/icon';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { StatisticsDetailTableModel } from 'src/app/shared/models/incentives/statistics-detail-table.model';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { IncentiveDataModel } from 'src/app/shared/models/incentives/incentives-data.model';
import { CarouselIncentivesModel } from 'src/app/shared/models/incentives/carousel-incentives.model';
import { catchError, of, Subscription } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-incentives-index',
  standalone: true,    
  imports: [ReactiveFormsModule,FormsModule,MatCardModule,MatSelectModule,MatFormFieldModule,NgChartsModule,RouterModule,TranslateModule,NgFor,BreadcrumbComponent,MatIconModule,Modal2Component,TableComponent,CurrencyPipe,NgIf,DatePipe,MatButtonModule],
  templateUrl: './incentives-index.component.html',
  styleUrl: './incentives-index.component.scss'
})
export class IncentivesIndexComponent implements OnInit { 

  @ViewChild('StatisticsDetailModal') StatisticsDetailModal?: TemplateRef<any>;
  
  inicio: string = "Inicio";  
  metricas: string = 'Mis Incentivos';
  private _settingCountryAndCompanySubscription?: Subscription;

  sections: { label: string, link: string } []=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.metricas, link: '/dashboard/incentives-index'}
  ];
  
  constructor( 
    private _messageService: MessageService,private _translateService: TranslateService,
    public _utilsService: UtilsService,private _formBuilder: FormBuilder,public modalDialog: MatDialog,
    private _fileService: FileService, public _incentivesService: IncentivesService,
    private _settingService: SettingService,private sanitizer: DomSanitizer, 
  ){}
  
  selectedFilter: string = '';  
  availableFilters: { value: string; label: string }[] = []; 
  
  axisMonths = this._utilsService.getMonths();

  // chartLabels: string[] =this._utilsService.getMonths().map(m=>m.month[m.value-1]); 
  chartLabels: string[] =[];

  ventasData: IncentiveDataModel[] = [];
  cancelacionesData: IncentiveDataModel[] = [];

  chartData: ChartData<'bar'> = this.setChartData();

  chartOptions: ChartOptions = {
    responsive: true,
    scales: {
      x: { stacked: true},
      y: { stacked: true,ticks:{stepSize:1,precision:0}, beginAtZero: true }
    },
    plugins: {
      legend: { position: 'top' },
    }
  };

  selectedYear = 2024;
  selectedMonthName: string='';
  totalSales: number=0;
  totalCancellations: number=0;
  totalIncentives: number=0;
  // 
  form:FormGroup = new FormGroup({});
  years: number[] = [];
  months!:Array<{ month: string; value: number }>;
  disposableMonths: { name: string; value: number }[] = [];
  userEmail!:string
  modalTitle:string = 'Detalles de Venta'

  dataStatisticsTable: StatisticsDetailTableModel[]=[];
  structDetailTable: BodyTableModel[] = [     
    {
      columnLabel: "Tipo",
      columnValue: 'vAction',       
    },
    {
      columnLabel: "Número de Documento",
      columnValue: 'iIdInsured',        
    },  

    {
      columnLabel: 'Número de Póliza',
      columnValue: 'fkiIdPolicy',        
    },  
    {
      columnLabel: 'Aseguradora',
      columnValue: 'vInsurance',        
    },   
    {
      columnLabel: 'Fecha de la Venta',
      columnValue: 'dstartValidity',      
    },
    {
      columnLabel: 'Prima',
      columnValue: 'dMonthlyValue',      
    },
    {
      columnLabel: 'Producto',
      columnValue: 'vProduct',      
    }
  ];

  statisticsData!:any;
  summary: any;
 //carousel
 incentivesCarouselImages: any[] = [];
  currentSlide = 0;    
  currentSlides: number[] = [];
  autoSlideInterval: any;
  carouselIncentives: CarouselIncentivesModel[]=[];
  srcImage!:string;
  currentIndex: number = 0; 
  href:any;
  idBusinessCountry:number=0;

  showSummary:boolean=false;

  ngOnInit(): void {
       const rememberedEmail = localStorage.getItem('rememberedEmail');
    if (rememberedEmail) {
      this.userEmail= rememberedEmail              
    }
    this.getIdBusinessByCountry();
    this.initForm();    
    this.months = this._utilsService.getMonths();    
    this.getYears();

  }

  async getIdBusinessByCountry() {
    const dataSetting = await this._settingService.getDataSettingInit();
    if (dataSetting.idBusinessByCountry) {
      this.idBusinessCountry = dataSetting.idBusinessByCountry;
      this.userHasIncentives()
      this.getAllIncentivesNonMonetarys()
    }
  }

  userHasIncentives() {
    this._incentivesService.validateIncentivesByUserEmail(this.userEmail, this.idBusinessCountry).subscribe({
      next:(response)=>{
        if(response){          
          this.ventasData = response.result.filter((item:IncentiveDataModel) => item.v_action === 'Venta');
          this.cancelacionesData = response.result.filter((item:IncentiveDataModel) => item.v_action === 'Cancelación');
          this.updateFilterOptions(true,true);          
          this.selectedFilter= 'ventasCancelaciones';
          setTimeout(() => this.onFilterChange(), 0); // default load sales and cancellations
          
          // Siempre muestra los 12 meses
          this.chartLabels = this.axisMonths.map(m => m.month);                 
        }
      },
      error:(err)=> {                        
       console.log(err.error.message);       
      }
    });
  }

  updateFilterOptions(showVentas: boolean, showCancelaciones: boolean) {
    const allOptions = [
      { value: 'ventasCancelaciones', label: 'Ventas y cancelaciones' },
      { value: 'ventas', label: 'Solo ventas' },
      { value: 'cancelaciones', label: 'Solo cancelaciones' }
    ];

    const filterKey = `${showVentas}-${showCancelaciones}`;

    switch (filterKey) {
      case 'true-true': 
        this.availableFilters = allOptions;
        break;

      case 'true-false':
        this.availableFilters = allOptions.filter(option => option.value !== 'cancelaciones');
        break;

      case 'false-true':
        this.availableFilters = allOptions.filter(option => option.value !== 'ventas');
        break;

      default: 
        this.availableFilters = [];
        break;
    }

    this.selectedFilter = this.availableFilters.length > 0 ? this.availableFilters[0].value : '';
  }

  onFilterChange() {   
    this.chartData = this.setChartData();
  }

  setChartData(): ChartData<'bar'> {
    const meses = Array.from({ length: 12 }, (_, i) => i + 1);
    const ventasPorMes = meses.map(mes => {
      const encontrado = this.ventasData.find(item => item.month === mes);
      return encontrado ? encontrado.number : 0;
    }); 
    const cancelacionesPorMes = meses.map(mes => {
      const encontrado = this.cancelacionesData.find(item => item.month === mes);
      return encontrado ? encontrado.number : 0;
    });
  
    const labels = this.chartLabels;
    switch (this.selectedFilter) {
      case 'ventas':
        return { datasets: [{ data: ventasPorMes, label: 'Ventas realizadas', backgroundColor: '#4A1B62' }],labels};
      case 'cancelaciones':
        return { datasets: [{ data: cancelacionesPorMes, label: 'Cancelaciones', backgroundColor: '#C7A6E0' }], labels};
      default:
        return {
          datasets: [
            { data: ventasPorMes, label: 'Ventas realizadas', backgroundColor: '#4A1B62' },
            { data: cancelacionesPorMes, label: 'Cancelaciones', backgroundColor: '#C7A6E0' }
          ],
          labels
        };
    }
  }

  initForm() {   
    this.form = this._formBuilder.group({
     active:[false],
     showSales:[false],
     showCancellations:[false],
     year:['',[Validators.required]],
     month:['',[Validators.required]],     
    }
   );
  }

  getYears(): void {
    const anioActual = new Date().getFullYear();
    this.years = Array.from({ length: anioActual - 1999 }, (_, i) => anioActual - i);
  }

  getSelectedYear() {
    this.selectedYear=this.form.get('year')?.value;
    this.assignMonths(this.selectedYear);
  }

  assignMonths(selectedYear: number){
    const anioActual = new Date().getFullYear();
    const mesActual = new Date().getMonth() + 1; 
      
    const limiteMes = selectedYear === anioActual ? mesActual : 12;      
    this.disposableMonths = this.months
      .slice(0, limiteMes) 
      .map((name, value) => ({ name:name.month, value }));
  }


  getStatisticsByYearMonth() {
    let year= this.form.get('year')?.value;
    let month = this.form.get('month')?.value+1;

    this._incentivesService.getAllStatisticsByYearAndMonth(year,parseInt(month),this.idBusinessCountry,this.userEmail).subscribe({
      next:(response)=>{
        if(response){       
          this.showSummary=true;           
          
          this.statisticsData=response.result;         
          this.selectedMonthName =  this.getMonthName(this.statisticsData.stadistics[0].month);
          this.totalSales = this.getTotalByActionCurrently("Venta");
          this.totalCancellations = this.getTotalByActionCurrently("Cancelación");
          this.totalIncentives = this.getTotalIncentivesCurrently();
          this.lastMonthStats     
          this.yearStats                      
        }
      },
      error:(err)=> {       
      this.showSummary=false;
      this._messageService.messageWaring('', err.error.message);                               
      }
    });
  }


  
  getMonthName(value: number | null): string {
    const monthName = this.months.find(m => m.value === value);
    return monthName ? monthName.month : '';
  }

  getTotalByActionCurrently(action: string): number {
    return this.statisticsData.stadistics
      .filter((item:Statistic) => item.v_action === action)
      .reduce((sum:number, item:Statistic) => sum + item.d_totalValue, 0);
  }
  
  
  getTotalIncentivesCurrently(): number {
    return this.statisticsData.stadistics
  .filter((item: Statistic) => item.v_action === 'Venta')
  .reduce((sum: number, item: Statistic) => sum + (item.d_incentiveValue || 0), 0);
  }


  get lastMonthStats() {
    return {
      month: this.getMonthName(this.statisticsData?.lastMonth[0]?.month),
      sales: this.getTotalByAction(this.statisticsData?.lastMonth, "Venta"),
      cancellations: this.getTotalByAction(this.statisticsData?.lastMonth, "Cancelación"),
      incentives: this.getTotalIncentives(this.statisticsData?.lastMonth),
      totalTransactions: this.statisticsData?.lastMonth?.filter((item:Statistic) => item.v_action === "Venta")?.reduce((sum:number, item:Statistic) => sum + item.number, 0)
    };
  }
  
  get yearStats() {
    return {
      sales: this.getTotalByAction(this.statisticsData?.year, "Venta"),
      cancellations: this.getTotalByAction(this.statisticsData?.year, "Cancelación"),
      incentives: this.getTotalIncentives(this.statisticsData?.year),
      totalSalesYear: this.statisticsData?.year?.filter((item:Statistic) => item.v_action === "Venta")?.reduce((sum:number, item:Statistic) => sum + item.number, 0)
    };
  }

  getTotalByAction(data: any[], action: string): number {
    return data?.filter(item => item.v_action === action)
                .reduce((sum, item) => sum + (item.d_totalValue || 0), 0) || 0;
  }

  getTotalIncentives(data: any[]): number {
    return data?.filter(item => item.v_action === "Venta")
                .reduce((sum, item) => sum + (item.d_incentiveValue || 0), 0) || 0;
  }
      
  openModal(){
   this.getStatisticsDetailTable(this.statisticsData.details);

    this.modalDialog.open(this.StatisticsDetailModal!, {
      width: '790px',
    });
  }

  getStatisticsDetailTable(details: any[]) {          
      const detailTable =  details.map(item => ({
        vAction: item.vAction,
        iIdInsured: item.iIdInsured,
        fkiIdPolicy: item.fkIdPolicy, 
        vInsurance: item.vInsurance,
        dstartValidity: item.dStartValidity ? item.dStartValidity.split('T')[0] : '',             
        dMonthlyValue: "$" + item.dMonthlyValue,
        vProduct: item.vProduct
      }));
        
        this.dataStatisticsTable = detailTable;    
  }

  closeModal(event: boolean) { }

  // carousel

  
  getAllIncentivesNonMonetarys(){
    this._incentivesService.getAllIncentivesNonMonetarys(this.idBusinessCountry).pipe(
      catchError((error)=>{
        this._messageService.messageWaring('', error.error.message);      
        return of([]);
      })
    ).subscribe((resp:ResponseGlobalModel | never[])=>{
      if (Array.isArray(resp)) {
        this._messageService.modalMessage('','Hubo un error al tratar de obtener todos los incentivos.',  "error",
          this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
      }else{                                      
        this.carouselIncentives= resp.result.filter((img:any)=>img.imagePath);       
        this.getImagesIncentives();   
        this.getUrlPDF(this.carouselIncentives);             
      }                
    });
  }

  getImagesIncentives(){
    this.carouselIncentives.forEach((element:CarouselIncentivesModel,index:number )=>{

      this._fileService.getUploadFileById(element.fkIIdUploadImage).pipe(
        catchError((error)=>{
          console.log('', error.error.message);      
          return of([]);
        })
      ).subscribe((resp:ResponseGlobalModel | never[])=>{
        if (Array.isArray(resp)) {
          this._messageService.modalMessage('','Hubo un error al tratar de obtener todos los incentivos.',  "error",
            this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
        }else{               
              element.imageBase64=resp.result.imageBase64;                
              this.getBitsImage(this.carouselIncentives);         
        }                
      });;
    })
    
  }


  getBitsImage(array: CarouselIncentivesModel[]) {
    let src = '';
    array.forEach((element: CarouselIncentivesModel, index: number) => {     
      const fileName = element.imagePath.split('\\').pop() || '';      
      const extension = fileName.split('.').pop() || 'png';      
      src = `data:image/${extension};base64,${element.imageBase64}`;
      this.srcImage=src;     
      this.carouselIncentives[index].src = this.srcImage;     
     });     
  }
 
  prev() {
    this.currentIndex =
    (this.currentIndex - 1 + this.carouselIncentives.length) %
    this.carouselIncentives.length;
  }

  next() {
    this.currentIndex =(this.currentIndex + 1) % this.carouselIncentives.length;
  }
  
  getUrlPDF(array: CarouselIncentivesModel[]) {
    array.forEach((element: CarouselIncentivesModel, index: number) => {           
      if (element.fileConditionsPath) {
        this.carouselIncentives[index].pdfUrl = this.sanitizeFilePath(element.fileConditionsPath);
      }           
    });            
  }
    
  sanitizeFilePath(path: string): string {
    return path.replace(/\\\\/g, '/').replace(/\\/g, '/'); 
  }

  openPDF(pdfUrl: string|undefined) {
    window.open(pdfUrl, '_blank');
  }
  closeModalButton(): void {
    this.modalDialog.closeAll();
  }
}

