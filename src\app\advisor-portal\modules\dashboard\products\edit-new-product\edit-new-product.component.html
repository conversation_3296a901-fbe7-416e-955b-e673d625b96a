<app-wizard [steps]="['Product.Product' | translate, 'Form' | translate, 'Category' | translate, 'QuoteFlow.Title' | translate]" [currentStep]="product?.stage!"
  [buttonNextDisabled]="buttonNextDisabled" (back)="backStep()" (next)="nextStep()">
  <ng-container [ngSwitch]="product?.stage" stepBody>
    <app-step-product-data *ngSwitchCase="0" (formChange)="dataForm = $event"></app-step-product-data>
    <app-step-form-data *ngSwitchCase="1"    (formChange)="dataForm = $event"></app-step-form-data>
    <app-step-category-data *ngSwitchCase="2" (formChange)="dataForm = $event"></app-step-category-data>
    <app-quote-flow [productStage] = "product"  *ngSwitchCase="3" (formChange)="dataForm = $event"></app-quote-flow>
    <ng-container *ngSwitchDefault>Loading...</ng-container>

    <button *ngSwitchCase="0" class="d-inline-flex" mat-raised-button color="primary" (click)="save()">
      {{"SaveChanges" | translate}}
    </button>
  </ng-container>
  <ng-container customButtonInit>
    <button style="background-color: transparent;" mat-raised-button (click)="backToProducts()">
      <mat-icon fontIcon="arrow_back"></mat-icon> {{ actions.returnText }}
    </button>
  </ng-container>
</app-wizard>