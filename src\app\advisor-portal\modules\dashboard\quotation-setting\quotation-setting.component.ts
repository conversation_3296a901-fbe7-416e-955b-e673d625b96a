import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { ListClientProceduresComponent } from 'src/app/client-portal/modules/dashboard/client-procedures/list-client-procedures/list-client-procedures.component';
import { AllQuotationSettingComponent } from "./all-quotation-setting/all-quotation-setting.component";
import { ChooseCountryAndCompanyComponent } from "../../../../shared/components/choose-country-and-company/choose-country-and-company.component";
import { BreadcrumbComponent } from "../../../../shared/components/breadcrumb/breadcrumb.component";
import { AllClientManagementSettingComponent } from "./all-client-management-setting/all-client-management-setting.component";

@Component({
  standalone: true,
  selector: 'app-quotation-setting',
  templateUrl: './quotation-setting.component.html',
  styleUrls: ['./quotation-setting.component.scss'],
  imports: [
    ListClientProceduresComponent,
    CommonModule,
    MatTabsModule,
    TranslateModule,
    AllQuotationSettingComponent,
    ChooseCountryAndCompanyComponent,
    BreadcrumbComponent,
    AllClientManagementSettingComponent
]
})
export class QuotationSettingComponent implements OnInit {

  constructor() { }

  inicio: string = 'Inicio';
  quotationS: string = 'Mis cotizaciones';

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.quotationS, link: '/dashboard/quotation-setting' },
  ];


  ngOnInit() {
  }

}
