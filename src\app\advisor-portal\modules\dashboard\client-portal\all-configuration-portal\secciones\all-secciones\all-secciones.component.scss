.upload-container {
    margin: 20px 0;
}
.upload-area {
    border: 2px dashed #cccccc;
    padding: 30px;
    border-radius: 8px;
    cursor: pointer;
    display: inline-block;
    width: 100%;
    max-width: 700px;
    position: relative;
  }
  .upload-area.dragover {
    border-color: #4caf50;
  }
  .upload-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #007bff;
    font-size: 16px;
    text-align: center;
  }
  .upload-label mat-icon {
    font-size: 40px;
    color: #007bff;
  }
  .upload-info {
    margin-top: 10px;
    font-size: 14px;
    color: #6c757d;
  }
  

  .color-picker-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .color-box {
    width: 50px; 
    height: 50px; 
    border: 1px solid #ccc; 
    transition: background-color 0.3s ease; 
  }
  
  .color-input {
    flex: 1;
  }

  .mat-form-field {
    width: 100%;
  }
  
  .btn-add {
    background-color: #000; 
    color: #fff; 
  }
  .subtitle-section{
    font-weight: 700;
    font-size: 18px;
  }