import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

@Component({
  selector: 'app-edit-new-alert',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatInputModule,
    MatSlideToggleModule
  ],
  templateUrl: './edit-new-alert.component.html',
  styleUrls: ['./edit-new-alert.component.scss']
})
export class EditNewAlertComponent implements OnInit{

  //id forulario actual
  @Input() idFormIn: number = 0;
  //id alerta en caso de que se valla a editar
  @Input() idWarningIn: number = 0;
  //resultado de formulario
  @Output() resultOut = new EventEmitter<any>();
  //texto del input value segun sea dias u horas
  iValueTittleText:string=this._translateService.instant('Warnings.Days');
  //formulario
  form: FormGroup = new FormGroup({});
  //lista de campos
  fieldList:any=[];
  //lista de calcular desde
  calculatedFromList=[
    {
      value: true,
      name: this._translateService.instant('Warnings.Start'),
    },
    {
      value: false,
      name: this._translateService.instant('Warnings.End'),
    }
  ]
  //lista de tiempo calculado en
  calculatedInList=[
    {
      value: true,
      name: this._translateService.instant('Warnings.Days'),
    },
    {
      value: false,
      name: this._translateService.instant('Warnings.Hours'),
    }
  ]

  constructor(
    private _fb: FormBuilder,   
    private _moduleService: ModuleService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    public utilsService: UtilsService     
  ){}

  ngOnInit(): void {
    this.getFiledsForWarnings();
    this.initForm();   
    this.idWarningIn!=0?this.getWarningById(this.idWarningIn):''
  }
  
  initForm() {
    this.form = this._fb.group({
      pkIIdWarningModule:[0],
      fkIIdFieldModule: [null, [Validators.required]],
      vName: [null, [Validators.required]],
      isBegin: [null, [Validators.required]],
      isDay: [null, [Validators.required]],
      iValue: [null, [Validators.required]],
      bActive:[true]
    });

    this.form.valueChanges.subscribe({
      next: (data) => {
        if(this.form.valid){
          this.resultOut.emit(data);
        }
        else{
          this.resultOut.emit('invalid');
        }
      },
    });

    this.form.get('isDay')?.valueChanges.subscribe((data) => {
      data?
        this.iValueTittleText=this._translateService.instant('Warnings.Days')
      :
        this.iValueTittleText=this._translateService.instant('Warnings.Hours')
    });
  }

  //obtiene los campos que llena el select
  getFiledsForWarnings() {
    this.fieldList=[];
    this._moduleService
      .getFieldforWarnings(this.idFormIn)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
            this.fieldList=[];
          }          
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            response.result?this.fieldList = response.result:this.fieldList=[];
          }
        }
      });
  }

  //obtiene una alerta por id
  getWarningById(idWarning: number){
    this._moduleService
      .getWarningById(idWarning)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          } 
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if(response.result){
              this.form.patchValue(response.result);
            }            
          }
        }
      });
  }

}
