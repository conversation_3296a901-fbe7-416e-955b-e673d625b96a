import { Routes } from '@angular/router';
import { CatalogSettingComponent } from './catalog-setting.component';

export default [
  {
    path: '',
    component: CatalogSettingComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-catalog/all-catalog.component').then(
            (c) => c.AllCatalogComponent
          ),
      },
      {
        path: 'new/:idBusinessCountry/:idCountry',
        loadComponent: () =>
          import(
            './all-catalog/all-field-catalog/all-field-catalog.component'
          ).then((c) => c.AllFieldCatalogComponent),
      },
      {
        path: 'modifyCatalog/:id/:idBusinessCountry/:idCountry',
        loadComponent: () =>
          import(
            './all-catalog/all-field-catalog/all-field-catalog.component'
          ).then((c) => c.AllFieldCatalogComponent),
      },
      {
        path: 'newField/:idCatalog',
        loadComponent: () =>
          import(
            './all-catalog/edit-field-catalog/edit-field-catalog.component'
          ).then((c) => c.EditFieldCatalogComponent),
      },
      {
        path: 'modifyField/:idCatalog/:idCatalogField',
        loadComponent: () =>
          import(
            './all-catalog/edit-field-catalog/edit-field-catalog.component'
          ).then((c) => c.EditFieldCatalogComponent),
      },
      {
        path: 'new/:idBusinessCountry/:idCountry',
        loadComponent: () =>
          import(
            './all-catalog/all-field-catalog/all-field-catalog.component'
          ).then((c) => c.AllFieldCatalogComponent),
      },
      {
        path: 'newComposite/:idBusinessCountry/:idCountry',
        loadComponent: () =>
          import(
            './all-catalog/composite-catalog/all-composite-catalog/all-composite-catalog.component'
          ).then((c) => c.AllCompositeCatalogComponent),
      },
      {
        path: 'newManagement/:idBusinessCountry/:idCountry',
        loadComponent: () =>
          import(
            './all-catalog/composite-catalog/catalog-management/catalog-management.component'
          ).then((c) => c.CatalogManagementComponent),
      },
      {
        path: 'editManagement/:idCatalog/:idBusinessCountry/:idCountry',
        loadComponent: () =>
          import(
            './all-catalog/composite-catalog/catalog-management/catalog-management.component'
          ).then((c) => c.CatalogManagementComponent),
      },
      {
        path: 'editSheet',
        loadComponent: () =>
          import(
            './all-catalog/composite-catalog/catalog-management/edit-catalog-sheet/edit-catalog-sheet.component'
          ).then((c) => c.EditCatalogSheetComponent),
      },
      {
        path: 'editColumn',
        loadComponent: () =>
          import(
            './all-catalog/composite-catalog/catalog-management/edit-catalog-column/edit-catalog-column.component'
          ).then((c) => c.EditCatalogColumnComponent),
      },

    ],
  },
] as Routes;
