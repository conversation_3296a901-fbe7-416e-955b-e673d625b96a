* {
    border: 0;
    padding: 0;
  }
  
  html {
    font-size: 100%;
  }
  
  /*16px*/
  body {
    font-weight: 400;
    line-height: 1.3;
    color: #2F2C31;
    background: #fff;
    position: relative;
    letter-spacing: -0.5px;
  }
  
  p {
    font-family: Inter;
    font-weight: 400;
    font-size: 16px;
  }
  p strong {
    font-weight: 600;
  }
  
  .h1 {
    font-family: Playfair Display;
    font-size: 48px;
    font-weight: 600;
    line-height: 64px;
    letter-spacing: -0.01em;
    text-align: center;
  }
  
  .h2 {
    font-family: Inter;
    font-size: 40px;
    font-weight: 700;
    line-height: 44px;
    letter-spacing: -0.012em;
    text-align: left;
  }
  
  .h3 {
    font-family: Inter;
    font-size: 32px;
    font-weight: 700;
    line-height: 39px;
    letter-spacing: -0.012em;
    text-align: left;
  }
  
  .h4 {
    font-family: Inter;
    font-size: 24px;
    font-weight: 700;
    line-height: 29px;
    letter-spacing: -0.012em;
    text-align: left;
  }
  
  .h5 {
    font-family: Inter;
    font-size: 20px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.012em;
    text-align: left;
  }
  
  .h6 {
    font-family: Inter;
    font-size: 16px;
    font-weight: 700;
    line-height: 19px;
    letter-spacing: -0.012em;
    text-align: left;
  }
  
  .h7 {
    font-family: Inter;
    font-size: 14px;
    font-weight: 700;
    line-height: 17px;
    letter-spacing: -0.012em;
    text-align: center;
  }
  
  .p {
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    letter-spacing: 0.011em;
    text-align: left;
  }
  
  .p_sb {
    font-family: Inter;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
  }
  
  .p_lg {
    font-family: Inter;
    font-size: 16px;
    font-weight: 300;
    line-height: 19px;
    letter-spacing: -0.012em;
    text-align: center;
  }
  
  .p_small_sb {
    font-family: Inter;
    font-size: 10px;
    font-weight: 600;
    line-height: 12px;
    letter-spacing: -0.03em;
    text-align: center;
  }
  
  .p_small_lg {
    font-family: Inter;
    font-size: 10px;
    font-weight: 300;
    line-height: 12px;
    letter-spacing: -0.03em;
    text-align: left;
  }
  
  .w-100 {
    width: 100%;
  }
  
  .m0 {
    margin: 0 auto;
  }
  
  .mt-1 {
    margin-top: 8px !important;
  }
  
  .mt-2 {
    margin-top: 16px !important;
  }
  
  .mt-3 {
    margin-top: 24px !important;
  }
  
  .mt-4 {
    margin-top: 32px !important;
  }
  
  .mt-5 {
    margin-top: 40px !important;
  }
  
  .mt-6 {
    margin-top: 48px !important;
  }
  
  .mt-7 {
    margin-top: 56px !important;
  }
  
  .mt-8 {
    margin-top: 64px !important;
  }
  
  .mb-0 {
    margin-bottom: 0 !important;
  }
  
  .mb-1 {
    margin-bottom: 8px !important;
  }
  
  .mb-2 {
    margin-bottom: 16px !important;
  }
  
  .mb-3 {
    margin-bottom: 24px !important;
  }
  
  .mb-4 {
    margin-bottom: 32px !important;
  }
  
  .mb-5 {
    margin-bottom: 40px !important;
  }
  
  .mb-6 {
    margin-bottom: 48px !important;
  }
  
  .mb-7 {
    margin-bottom: 56px !important;
  }
  
  .mb-8 {
    margin-bottom: 64px !important;
  }
  
  .mr-1 {
    margin-right: 8px !important;
  }
  
  .mr-2 {
    margin-right: 16px !important;
  }
  
  .mr-3 {
    margin-right: 24px !important;
  }
  
  .mr-4 {
    margin-right: 32px !important;
  }
  
  .mr-5 {
    margin-right: 40px !important;
  }
  
  .mr-6 {
    margin-right: 48px !important;
  }
  
  .mr-7 {
    margin-right: 56px !important;
  }
  
  .mr-8 {
    margin-right: 64px !important;
  }
  
  .ml-1 {
    margin-left: 8px !important;
  }
  
  .ml-2 {
    margin-left: 16px !important;
  }
  
  .ml-3 {
    margin-left: 24px !important;
  }
  
  .ml-4 {
    margin-left: 32px !important;
  }
  
  .ml-5 {
    margin-left: 40px !important;
  }
  
  .ml-6 {
    margin-left: 48px !important;
  }
  
  .ml-7 {
    margin-left: 56px !important;
  }
  
  .ml-8 {
    margin-left: 64px !important;
  }
  
  form .form-group {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    height: 120px;
    margin-bottom: 24px;
  }
  form .form-group label {
    font-weight: 700;
    color: #2F2C31;
    line-height: 24px;
    font-size: 20px;
    width: 100%;
    display: block;
    margin-bottom: 8px;
  }
  form .form-group input[type=text],
  form .form-group input[type=password],
  form .form-group input[type=email],
  form .form-group input[type=date],
  form .form-group input[type=time],
  form .form-group input[type=month],
  form .form-group input[type=tel],
  form .form-group input[type=number] {
    width: 100%;
    display: flex;
    padding: 0 16px;
    font-size: 16px;
    letter-spacing: -3%;
    height: 56px;
    border: 1px solid #2F2C31;
    color: #B3B3B3;
    font-weight: 400;
    background: #fff;
  }
  form .form-group input[type=text]:blank,
  form .form-group input[type=password]:blank,
  form .form-group input[type=email]:blank,
  form .form-group input[type=date]:blank,
  form .form-group input[type=time]:blank,
  form .form-group input[type=month]:blank,
  form .form-group input[type=tel]:blank,
  form .form-group input[type=number]:blank {
    color: #B3B3B3;
    border-bottom-width: 1px;
  }
  form .form-group input[type=text]:focus,
  form .form-group input[type=password]:focus,
  form .form-group input[type=email]:focus,
  form .form-group input[type=date]:focus,
  form .form-group input[type=time]:focus,
  form .form-group input[type=month]:focus,
  form .form-group input[type=tel]:focus,
  form .form-group input[type=number]:focus {
    border-width: 2px;
    outline: none;
    color: #2F2C31;
  }
  form .form-group input[type=text]:disabled,
  form .form-group input[type=password]:disabled,
  form .form-group input[type=email]:disabled,
  form .form-group input[type=date]:disabled,
  form .form-group input[type=time]:disabled,
  form .form-group input[type=month]:disabled,
  form .form-group input[type=tel]:disabled,
  form .form-group input[type=number]:disabled {
    background: #D9D9D9;
  }
  form .form-group input[type=text]:invalid,
  form .form-group input[type=password]:invalid,
  form .form-group input[type=email]:invalid,
  form .form-group input[type=date]:invalid,
  form .form-group input[type=time]:invalid,
  form .form-group input[type=month]:invalid,
  form .form-group input[type=tel]:invalid,
  form .form-group input[type=number]:invalid {
    border-bottom-color: #A61932;
  }
  form .form-group input[type=text]:invalid + .message.error,
  form .form-group input[type=password]:invalid + .message.error,
  form .form-group input[type=email]:invalid + .message.error,
  form .form-group input[type=date]:invalid + .message.error,
  form .form-group input[type=time]:invalid + .message.error,
  form .form-group input[type=month]:invalid + .message.error,
  form .form-group input[type=tel]:invalid + .message.error,
  form .form-group input[type=number]:invalid + .message.error {
    display: block;
  }
  form .form-group input[type=text]:required,
  form .form-group input[type=password]:required,
  form .form-group input[type=email]:required,
  form .form-group input[type=date]:required,
  form .form-group input[type=time]:required,
  form .form-group input[type=month]:required,
  form .form-group input[type=tel]:required,
  form .form-group input[type=number]:required {
    border-bottom-color: #DB961E;
  }
  form .form-group input[type=text]:required + .message.required,
  form .form-group input[type=password]:required + .message.required,
  form .form-group input[type=email]:required + .message.required,
  form .form-group input[type=date]:required + .message.required,
  form .form-group input[type=time]:required + .message.required,
  form .form-group input[type=month]:required + .message.required,
  form .form-group input[type=tel]:required + .message.required,
  form .form-group input[type=number]:required + .message.required {
    display: block;
  }
  form .form-group input[type=text]:user-invalid,
  form .form-group input[type=password]:user-invalid,
  form .form-group input[type=email]:user-invalid,
  form .form-group input[type=date]:user-invalid,
  form .form-group input[type=time]:user-invalid,
  form .form-group input[type=month]:user-invalid,
  form .form-group input[type=tel]:user-invalid,
  form .form-group input[type=number]:user-invalid {
    border-bottom-color: #DB961E;
  }
  form .form-group input[type=text].error,
  form .form-group input[type=password].error,
  form .form-group input[type=email].error,
  form .form-group input[type=date].error,
  form .form-group input[type=time].error,
  form .form-group input[type=month].error,
  form .form-group input[type=tel].error,
  form .form-group input[type=number].error {
    border-color: #A61932;
  }
  form .form-group .message {
    display: none;
    width: 100%;
    padding: 4px;
  }
  form .form-group .message.error {
    color: #fff;
    background: #A61932;
  }
  form .form-group .message.required {
    color: #DB961E;
  }
  
  .btn {
    height: 48px;
    padding: 0 16px;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.125rem;
    line-height: 1.75rem;
    letter-spacing: 0.01em;
    text-align: center;
    outline: none;
    border: none;
    min-width: 100px;
  }
  .btn.btn-primary {
    background: #2F2C31;
    color: #fff;
  }
  .btn.btn-primary.fire {
    background: #D124B8;
    color: #fff;
  }
  .btn.btn-primary.ultra {
    background: #7F35B2;
    color: #fff;
  }
  .btn.btn-primary:hover {
    background: #000;
  }
  .btn.btn-secondary {
    background: none;
    border: 1px solid #2F2C31;
    color: #2F2C31;
  }
  .btn.btn-secondary.fire {
    border: 2px solid #D124B8;
    color: #D124B8;
  }
  .btn.btn-secondary.ultra {
    border: 2px solid #7F35B2;
    color: #7F35B2;
  }
  .btn.btn-terciary {
    color: #2F2C31;
  }
  .btn.btn-terciary.fire {
    color: #D124B8;
  }
  .btn.btn-terciary.ultra {
    color: #7F35B2;
  }
  .btn.btn-link {
    color: #2F2C31;
  }
  .btn.btn-link.fire {
    color: #D124B8;
  }
  .btn.btn-link.ultra {
    color: #7F35B2;
  }
  .btn.w-icon.icon-left img {
    margin-right: 8px;
  }
  .btn.w-icon.icon-right img {
    margin-left: 8px;
  }
  .btn.small {
    height: 32px;
    font-size: 14px;
    line-height: 16.94px;
    letter-spacing: -1.2%;
  }
  .btn.small.w-icon img {
    width: 18px;
  }
  
  a.link {
    color: #2F2C31;
  }
  a.link.fire {
    color: #D124B8;
  }
  a.link.ultra {
    color: #7F35B2;
  }
  
  * {
    border: 0;
    padding: 0;
  }
  
  .row.container {
    max-width: 1312px;
    width: 95%;
    margin: 0 auto;
  }
  
  main section {
    width: 100%;
  }
  
  .modal .modal-dialog {
    max-width: 556px;
  }
  .modal .modal-dialog.modal-sm {
    max-width: 330px;
  }
  .modal .modal-dialog.modal-lg {
    max-width: 700px;
  }
  .modal .modal-dialog.modal-xl {
    width: 900px;
  }
  .modal .modal-dialog .modal-body h4 {
    font-family: Inter;
    font-size: 24px;
    font-weight: 700;
    line-height: 29px;
    letter-spacing: -0.012em;
    text-align: center;
  }
  .modal .modal-dialog .modal-body h5 {
    font-family: Inter;
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0em;
    text-align: center;
  }
  .modal .modal-dialog .modal-footer {
    border: none;
  }
  
  #login {
    min-height: calc(100vh - 90px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #login h2 {
    font-family: Inter;
    font-size: 32px;
    font-weight: 700;
    line-height: 39px;
    letter-spacing: -0.012em;
    text-align: left;
  }
  #login form {
    width: 90%;
    max-width: 450px;
    background: #fff;
    border: 1px solid #2F2C31;
    display: flex;
    justify-content: center;
    padding: 40px 32px;
    margin: 0 auto;
    flex-wrap: wrap;
    margin-top: 32px;
    margin-bottom: 32px;
    position: relative;
  }
  #login form.md {
    max-width: 653px;
  }
  #login form legend {
    font-family: Inter;
    font-size: 32px;
    font-weight: 700;
    line-height: 39px;
    letter-spacing: -0.012em;
    width: 100%;
    display: flex;
    text-align: center;
    justify-content: center;
  }
  #login form legend.h4 {
    font-family: Inter;
    font-size: 24px;
    font-weight: 700;
    line-height: 29px;
    letter-spacing: -0.012em;
    text-align: left;
    justify-content: start;
  }
  #login form a.link {
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    letter-spacing: -0.03em;
    text-align: left;
    width: 100%;
    display: flex;
    justify-content: center;
    text-align: center;
    text-decoration: underline;
    color: #2F2C31;
  }
  #login form span {
    position: absolute;
    z-index: -1;
  }
  #login form span.top {
    top: -30px;
    right: -30px;
  }
  #login form span.bottom {
    bottom: -30px;
    left: -30px;
  }
  #login .explanation {
    position: relative;
    max-width: 582px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    overflow: hidden;
    height: 100%;
  }
  #login .explanation img {
    position: absolute;
    z-index: 1;
  }
  #login .explanation div {
    position: relative;
    width: 80%;
    max-width: 444px;
    background: #fff;
    z-index: 2;
    background: #fff;
    padding: 24px;
  }
  #login .explanation h1 {
    font-family: Playfair Display;
    font-size: 48px;
    font-weight: 600;
    line-height: 64px;
    letter-spacing: -0.01em;
    text-align: center;
    color: #2F2C31;
  }
  #login .explanation p {
    font-family: Inter;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: center;
    margin: 0;
    margin-top: 16px;
  }
  #login .explanation ul {
    width: 100%;
    max-width: 290px;
    margin: 0 auto;
    margin-top: 16px;
  }
  #login .explanation ul li {
    font-family: Inter;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
  }