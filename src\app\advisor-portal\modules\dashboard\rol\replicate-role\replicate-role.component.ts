import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';


import { RoleService } from 'src/app/shared/services/role/role.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { ChooseCountryAndCompanyDuplicateComponent } from 'src/app/shared/components/choose-country-and-company-duplicate/choose-country-and-company-duplicate.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-replicate-role',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyDuplicateComponent,
    MatIconModule,
    TranslateModule,
    MatButtonModule,
  ],
  templateUrl: './replicate-role.component.html',
  styleUrls: ['./replicate-role.component.scss'],
})
export class ReplicateRoleComponent implements OnInit, OnDestroy {
  formValid: boolean = false;
  //Empresa País dónde se quiere hacer la replica.
  idBusinessCountryCurrent: number = 0;
  //Empresa País que tiene los roles a replicar.
  idBusinessCountryToReplicate: number = 0;
  @Output() submitOut = new EventEmitter<ResponseGlobalModel>();
  private _settingCountryAndCompanySubscription?: Subscription;

  constructor(
    private _roleService: RoleService,
    private _settingService: SettingService,
    public utilsService: UtilsService,
    private _router: Router,
    private _customRouter: CustomRouterService,
  ) {}

  ngOnInit(): void {
    this.getDataCountryAndCompanySubscription();
  }

  validFormReplicate(event: boolean) {
    this.formValid = event;
  }

  getDataCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessCountryCurrent =
                response.enterprise.pkIIdBusinessByCountry;
            }
          }
        }
      );
  }

  replicateRole() {
    this._roleService
      .duplicateRol(
        this.idBusinessCountryToReplicate,
        this.idBusinessCountryCurrent
      )
      .subscribe((resp) => {
        this.submitOut.emit(resp);
        this._customRouter.navigate(['/dashboard/roles']);
      });
  }

  getValueForm(event: any) {
    this.idBusinessCountryToReplicate = event.value.enterprise;
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
