import { Component, OnInit, Input,OnChanges, SimpleChanges, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { catchError, of } from 'rxjs';
import { ActivatedRoute, RouterModule, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';

import { MessageService } from 'src/app/shared/services/message/message.service';
import { RoleService } from 'src/app/shared/services/role/role.service';

import { ResponseGlobalModel } from 'src/app/shared/models/response';

import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';



@Component({
  selector: 'app-modules',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, MatFormFieldModule, MatSelectModule, BreadcrumbComponent],
  templateUrl: './modules.component.html',
  styleUrls: ['./modules.component.scss'],
})
export class ModulesComponent implements OnInit, OnChanges {

  title: string = '';
  idMenu: number = 0;
  image: string = '';
  productList: any[] = [];
  idProductModule: number = 0;
  index: string = this._translateService.instant('Inicio');
  isSelected: boolean = false;
  isClientPortal: boolean = false;
  @Input() idMenuInserted: number = 0;

  sections: { label: string; link: string }[] = [
    { label: '', link: '' }];
  
  private localStorageService = inject(LocalStorageService);
  
  constructor(
    private _router: Router,
    private _customRouter: CustomRouterService,
    private _messageService: MessageService,
    private _activatedRoute: ActivatedRoute,
    public _translateService: TranslateService,
    private _roleService: RoleService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.isClientPortal = this._customRouter.isClientPortal()
    this.isSelected = false;
    this.getRouterName();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['idMenuInserted'] && !changes['idMenuInserted'].firstChange) {
      this.idMenu = this.idMenuInserted;
      this.title = localStorage.getItem('nameModule' + this.idMenuInserted) || '';
      this.image = localStorage.getItem('imageModule' + this.idMenuInserted) || '';
      this.getProductByIdMenu(this.idMenu);
    }
  }
  getRouterName() {
    this._activatedRoute.params.subscribe((params: any) => {
      this.isSelected = false;
      this.title = this.localStorageService.getItem('nameModule'+params.idMenu)!;      
      this.idMenu = params.idMenu || this.idMenuInserted;
      this.image = this.localStorageService.getItem('imageModule'+params.idMenu)!;
      this.sections = [
        { label: this.index, link: '/dashboard' },
        { label: this.title, link: '/dashboard/configuration' },
      ]
      this.getProductByIdMenu(this.idMenu)

    });
  }

  async getProductByIdMenu(idMenu: number) {
    if (idMenu > 0) {
      this._roleService
        .getProductByIdMenu(idMenu, this.isClientPortal)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            } else {
              this.productList = resp.result;
              var dataIdProductModule = 0;
              if (this.productList != undefined) {
                this.productList.filter((element) => {
                  this.idProductModule = element.idProductModule;
                  if (element.idProduct === null || element.idProduct === '')
                    dataIdProductModule = 1;
                  else dataIdProductModule = 0;
                });

                if (dataIdProductModule === 1) {
                  this.onProductSelected({ value: this.idProductModule })
                }
                if (this.isClientPortal && this.productList.length === 1)
                  this.onProductSelected({ value: this.productList[0] })
              }
            }
          }
        });
    }
  }

  onProductSelected(event: any) {
    this.isSelected = true;
    const selectedProductId = event.value.idProductModule;
    if (this.isClientPortal)
      this._customRouter.updateSelectedProductByModule(selectedProductId)
    else
    //Dejar con _router
      this._router.navigate(['./', selectedProductId], { relativeTo: this._activatedRoute });
  }
}
