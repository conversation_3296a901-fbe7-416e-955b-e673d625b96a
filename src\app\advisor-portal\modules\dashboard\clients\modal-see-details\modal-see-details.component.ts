import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { catchError, of } from 'rxjs';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ManagementHistoryModel } from 'src/app/shared/models/task';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CommonModule, DatePipe } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { TableComponent } from 'src/app/shared/components/table/table.component';
@Component({
  selector: 'app-modal-see-details',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatDialogModule, TableComponent],
  templateUrl: './modal-see-details.component.html',
  styleUrls: ['./modal-see-details.component.scss'],
  providers: [DatePipe]
})
export class ModalSeeDetailsComponent implements OnInit {
 // Table structure for managing history
estructTableManagementHistory: BodyTableModel[] = [
  {
    columnLabel: this._translateService.instant(
      'TaskTray.ManagementHistory.Table.WhoModifies'
    ),
    columnValue: 'vUserName',
  },
  {
    columnLabel: this._translateService.instant(
      'TaskTray.ManagementHistory.Table.ModificationDate'
    ),
    columnValue: 'dDateCreation',
  },
  {
    columnLabel: this._translateService.instant(
      'TaskTray.ManagementHistory.Table.Stage'
    ),
    columnValue: 'vStage',
  },
  {
    columnLabel: this._translateService.instant(
      'TaskTray.ManagementHistory.Table.State'
    ),
    columnValue: 'vState',
  },
];

// Variable to store data for the management history table
dataTableManagementHistory: ManagementHistoryModel[] = [];

// Variables for storing task and details information
idTask: number = 0;
productName: string = '';
procedureName: string = '';
stageName: string = '';
stateName: string = '';
dProcedureDate?: string | null = '';
plates: string[] = []; // List of plates
assignedBy: string = '';
modifiedBy: string = '';
fieldNameKey?: string = '';
fieldValueKey?: string = '';

constructor(
  public dialogRef: MatDialogRef<ModalSeeDetailsComponent>,
  @Inject(MAT_DIALOG_DATA) public data: any,
  private fb: FormBuilder,
  private _translateService: TranslateService,
  private _taskTayConfigService: TaskTayConfigService,
  private _messageService: MessageService,
  private datePipe: DatePipe
) {
}

ngOnInit(): void {
  // Initialize task and details if available
  if (this.data.iTask) {
    this.idTask = this.data.iTask;
    this.getManagementHistory(this.data.iTask);
  }

  // Set additional data if provided
  if (this.data.allData) {
    this.productName = this.data.allData.vProductName;
    this.procedureName = this.data.allData.vProcedureName;
    this.stageName = this.data.allData.vStage;
    this.stateName = this.data.allData.vProcedureState;
    this.dProcedureDate = this.formatDate(this.data.allData.dProcedureDate);
  }
}

// Close the dialog
close(): void {
  this.dialogRef.close();
}

/**
 * Formats a date string into a human-readable format.
 * @param date - The date string to be formatted.
 * @returns The formatted date string, or null if the input date is not valid.
 */
formatDate(date: string): string | null {
  // Use the Angular DatePipe to format the date string.
  // The format is 'd 'de' MMMM 'de' y 'a las' h:mm a', which results in a format like '21 de julio de 2024 a las 3:45 pm'.
  return this.datePipe.transform(date, 'd \'de\' MMMM \'de\' y \'a las\' h:mm a');
}

/**
 * Retrieves the management history table data based on the task ID.
 * @param idTask - The ID of the task for which to retrieve the management history.
 */
getManagementHistory(idTask: number) {
  this._taskTayConfigService
    .getManagementHistory(idTask)
    .pipe(
      catchError((error) => {
        // If the error contains a message, display it as a warning.
        if (error.error.error) {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
        }
        // Return an empty array as a fallback.
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        // Handle the case where the response is an array (no action needed in this case).
      } else {
        // Check if the response contains an error.
        if (resp.error) {
          // Handle error response (no action specified here).
        } else {
          this.dataTableManagementHistory = resp.result;

          const sortedData = this.dataTableManagementHistory.sort((a, b) => {
            return new Date(a.dDateCreation).getTime() - new Date(b.dDateCreation).getTime();
          });
  
          const oldestEntry = sortedData[0];
  
          this.fieldNameKey = oldestEntry?.fieldName;
          this.fieldValueKey = oldestEntry?.fieldValue;

          this.findPlates();
          this.searchResponsible();
        }
      }
    });
}


/**
 * Handles the actions triggered from the table based on the event.
 * @param evt - The event object containing information about the action and associated data.
 */
controller(evt: IconEventClickModel) {
}


/**
 * Extracts license plate information from the management history data.
 * This method parses the `vTextCaptured` field of each item in `dataTableManagementHistory` 
 * and extracts values associated with keys that include the word 'placa'.
 */
findPlates() {

  // Iterate over each item in the management history data table
  this.dataTableManagementHistory.forEach((item: ManagementHistoryModel) => {

    // Parse the `vTextCaptured` field from JSON string to an object
    const vTextCaptured = JSON.parse(item.vTextCaptured);

    // Iterate over each key in the parsed object
    for (const key in vTextCaptured) {

      // Check if the key includes the word 'placa', case-insensitive
      if (key.toLowerCase().includes('placa')) {

        // Add the associated value to the `plates` array
        this.plates.push(vTextCaptured[key]);
      }
    }
  });
}


/**
 * Determines who is responsible for the task based on the management history.
 * Sets the `assignedBy` and `modifiedBy` properties based on the number of records in the management history.
 */
searchResponsible() {
  const { length } = this.dataTableManagementHistory;

  if (length === 0) {
    
    // If there are no records, `assignedBy` and `modifiedBy` remain unchanged
    return;
  }

  if (length === 1) {

    // If there is only one record, both `assignedBy` and `modifiedBy` are set to the same user
    this.assignedBy = this.dataTableManagementHistory[0].vUserName;
    this.modifiedBy = this.dataTableManagementHistory[0].vUserName;
  } else {

    // If there are multiple records, `assignedBy` is set to the user from the first record,
    // and `modifiedBy` is set to the user from the last record
    this.assignedBy = this.dataTableManagementHistory[0].vUserName;
    this.modifiedBy = this.dataTableManagementHistory[length - 1].vUserName;
  }
}

}
