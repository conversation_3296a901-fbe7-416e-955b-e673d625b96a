import {
  Component,
  EventEmitter,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription, catchError, debounceTime, of } from 'rxjs';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatDialog } from '@angular/material/dialog';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { TableModel } from 'src/app/shared/models/form-tables';
import { MatTableModule } from '@angular/material/table';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';

@Component({
  selector: 'app-edit-new-table',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatInputModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    Modal2Component,
    MatTableModule,
    ValidationInputFileDirective
  ],
  templateUrl: './edit-new-table.component.html',
  styleUrls: ['./edit-new-table.component.scss'],
})
export class EditNewTableComponent implements OnInit, OnDestroy {
  //idForm de entrada
  @Input() idFormIn: number = 0;
  //idTable en caso de que se valla a editar
  @Input() idTableIn: number = 0;
  //output del formulario
  @Output() resultOut = new EventEmitter<any>();
  //modal añadir campo
  @ViewChild('addFieldModal') addFieldModal?: TemplateRef<any>;
  //suscripciones que guarda la empresa pais activa
  private _settingCountryAndCompanySubscription?: Subscription;

  //idBusinessCountry activo
  idBusinessCountry: number = 0;
  //variables que cambian un valor en el html
  modalTittleText: string = '';
  modalSaveButtonText: string = '';
  //variables que habilitan o deshabilitan algun elemento segun corresponda.
  tableAlreadyCreated = false;
  showModalSaveButton: boolean = false;
  disableProgressBarSelect: boolean = false;
  disableTabSelect: boolean = false;
  disableSectionsSelect: boolean = false;
  //listas que llenan los dropDown
  progressBarList: any[] = [];
  tabsList: any[] = [];
  sectionsList: any[] = [];
  fieldList: any[] = [];
  orderList: number[] = [];
  //formularios
  form: FormGroup = new FormGroup({});
  formField: FormGroup = new FormGroup({});
  //tabla campos
  estructTableColumns: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Table.ColumnName'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Table.Order'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsService.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];
  dataTableColumns: any[] = [];
  allowedExtensions: string[] = ['xlsx', 'pdf', 'jpg', 'png'];

  constructor(
    private _fb: FormBuilder,
    private _router: Router,
    private _moduleService: ModuleService,
    private _messageService: MessageService,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    public utilsService: UtilsService,
    private _addFieldDialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this.initForm();
    this.getProgressBarModuleByIdFormModule(this.idFormIn);
    this.idTableIn != 0
      ? (this.getColumnsByTableId(this.idTableIn),
        this.getTableId(this.idTableIn),
        (this.tableAlreadyCreated = true))
      : '';
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdTableModule: [0],
      vName: [null, [Validators.required]],
      fkIIdSectionModule: [null],
      fkIIdTabModule: [null],
      fkIIdProgressBar: [null],
      fkiIdBusinessCountry: [0],
      fkiIdFormModule: [this.idFormIn],
      vFileName: [null],
      vFileBase64: [null],
      bMultipleValues: [true],
      bUpload: [true],
      bDownload: [true],
      bDelete: [true],
      bActive: [true],
    });

    this.form.valueChanges.pipe(debounceTime(0)).subscribe({
      next: (data) => {
        if (this.form.valid) {
          this.resultOut.emit(data);
        } else {
          this.resultOut.emit('invalid');
        }
      },
    });
  }

  initFieldFrom() {
    this.formField = this._fb.group({
      pkIIdColumnTableModule: [0],
      fkIIdTableModule: [this.idTableIn],
      fkIIdFieldModule: [0, [Validators.required]],
      iOrder: [0, [Validators.required]],
      bActive: [true],
    });

    this.formField.valueChanges.subscribe({
      next: (data) => {
        if (this.formField.valid) {
          this.showModalSaveButton = true;
        } else {
          this.showModalSaveButton = false;
        }
      },
    });
  }

  openAddFieldModal(idColumnTable: number) {
    idColumnTable > 0
      ? ((this.modalTittleText = this._translateService.instant(
          'Table.ModifyColumnTable'
        )),
        (this.modalSaveButtonText =
          this._translateService.instant('SaveChanges')))
      : ((this.modalTittleText = this._translateService.instant(
          'Table.NewColumnTable'
        )),
        (this.modalSaveButtonText = this._translateService.instant('Save')));
    this.initFieldFrom();
    this.getListTableFields(this.idFormIn);
    this.getColumnsByTableId(this.idTableIn);
    const dialogRef = this._addFieldDialog.open(this.addFieldModal!, {
      disableClose: true,
      width: '70vh',
      maxHeight: 'auto',
    });
  }

  controller(evt: IconEventClickModel) {
    if (evt.column == 'modify') {
      this.getColumnById(evt.value.pkIIdColumnTableModule);
      this.openAddFieldModal(evt.value.pkIIdColumnTableModule);
    } else {
      this._messageService
        .messageConfirmationAndNegation(
          this._translateService.instant('Delete') + '?',
          '',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            this.deleteColumnTable(evt.value.pkIIdColumnTableModule);
            this._messageService.messageConfirmatio(
              this._translateService.instant('Deleted'),
              '',
              'success',
              this._translateService.instant('Continue')
            );
          }
        });
    }
  }
  
  selectFile(event: any) {
    let extension: string = '';
    extension = this.form.value.vFileName =
      event.target.files[0].name.split('.')[1];
    if (
      extension === 'xlsx' ||
      extension === 'pdf' ||
      extension === 'jpg' ||
      extension === 'png'
    ) {
      this.form.patchValue({ vFileName: event.target.files[0].name });
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.form.patchValue({
          vFileBase64: reader.result?.toString().split('base64,').pop(),
        });
      };
    } else {
      this._messageService.messageInfo(
        'Advertencia',
        'El archivo selecionado debe ser un excel, un pdf, un jpg o png'
      );
      this.form.patchValue({ vFileName: '' });
      this.form.patchValue({ vFileBase64: '' });
    }
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.form
              .get('fkiIdBusinessCountry')
              ?.setValue(response.enterprise.pkIIdBusinessByCountry);
          }
        }
      );
  }

  //#region metodos que consumen alguna api
  //obtiene una tabla por id
  getTableId(idTable: number) {
    this._moduleService
      .getTableId(idTable)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.form.patchValue(resp.result);
          this.form.patchValue({ vFileName: resp.result.fileName });
        }
      });
  }
  //obtiene los pasos asugnados a un idForm
  getProgressBarModuleByIdFormModule(idFormModule: number) {
    this._moduleService
      .getProgressBarModuleByIdFormModule(idFormModule)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.progressBarList = response.result;
            this.progressBarList.length == 0
              ? (this.disableProgressBarSelect = true)
              : (this.disableProgressBarSelect = false);
              if(this.progressBarList.length == 0){
                this.getTabModuleListByFormId();
              }
              
          }
        }
      });
  }
  //obtiene las pestañas asugnados a un idForm
  getTabModuleListByFormId() {
    this.tabsList = [];
    this.sectionsList = [];
    this._moduleService
      .getTabModuleListByFormId(this.idFormIn)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.tabsList = response.result.filter(
              (t: any) => t.fkIIdProgressBar == this.form.value.fkIIdProgressBar
            );
            this.tabsList.length == 0
              ? (this.disableTabSelect = true)
              : (this.disableTabSelect = false);
              if( this.tabsList.length == 0){
                this.getSectionModuleByFormId()
              }
          }
        }
      });
  }
  //obtiene las secciones asugnados a un idForm
  getSectionModuleByFormId() {
    this._moduleService
      .getSectionModuleByFormId(this.idFormIn)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.sectionsList = response.result.filter(
              (s: any) =>
                s.fkIIdProgressBar == this.form.value.fkIIdProgressBar &&
                s.fkIIdTabModule == this.form.value.fkIIdTabModule
            );
            this.sectionsList.length == 0
              ? (this.disableSectionsSelect = true)
              : (this.disableSectionsSelect = false);
          }
        }
      });
  }
  //obtiene la lista de campos
  getListTableFields(idForm: number) {
    this._moduleService
      .getListTableFields(idForm)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          this.fieldList = [];
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.fieldList = [];
        } else {
          this.fieldList = resp.result;
        }
      });
  }
  //obtiene los campos asigando a una tabla
  getColumnsByTableId(idTable: number) {
    this.orderList = [];
    this._moduleService
      .getColumnsByTableId(idTable)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          this.orderList = [];
          this.dataTableColumns = [];
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTableColumns = [];
          this.orderList.push(1);
        } else {
          this.dataTableColumns = resp.result;
          let i = 1;
          this.orderList.push(i);
          for (let item of resp.result) {
            i = i + 1;
            this.orderList.push(i);
          }
        }
      });
  }
  //obtiene un campo asignado a tabla por id
  getColumnById(idColumnTable: number) {
    this.orderList = [];
    this._moduleService
      .getColumnById(idColumnTable)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          this.orderList = [];
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.orderList.push(1);
        } else {
          this.formField.patchValue(resp.result);
        }
      });
  }
  //crea una tabla
  createTable() {
    this.getSettingCountryAndCompanySubscription();
    if (this.form.valid) {
      this._moduleService
        .createTable(this.form.value)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageError(error.error.message);
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this.tableAlreadyCreated = true;
            this.form.patchValue({ pkIIdTableModule: resp.result });
            this.idTableIn = resp.result;
            this.resultOut.emit(resp.result);
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              ''
            );
          }
        });
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant(
          'PleaseCompleteAllTheInformationOnTheForm'
        )
      );
    }
  }
  //crea a actualiza relacion de columna y tabla segun corresponda
  saveColumnTable(event: boolean) {
    if (event) {
      if (
        this.modalTittleText ==
        this._translateService.instant('Table.NewColumnTable')
      ) {
        this._moduleService
          .createColumnTable(this.formField.value)
          .pipe(
            catchError((error) => {
              if (error.error.error) {
                this._messageService.messageError(error.error.message);
              }
              return of([]);
            })
          )
          .subscribe((resp: ResponseGlobalModel | never[]) => {
            if (Array.isArray(resp)) {
            } else {
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                ''
              );
              this.getColumnsByTableId(this.idTableIn);
            }
          });
      } else {
        this._moduleService
          .updateColumnTable(this.formField.value)
          .pipe(
            catchError((error) => {
              if (error.error.error) {
                this._messageService.messageError(error.error.message);
              }
              return of([]);
            })
          )
          .subscribe((resp: ResponseGlobalModel | never[]) => {
            if (Array.isArray(resp)) {
            } else {
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                ''
              );
              this.getColumnsByTableId(this.idTableIn);
            }
          });
      }
    }
  }
  //borra una columna asignada a una tabla
  deleteColumnTable(idColumnTable: number) {
    this._moduleService
      .deleteColumnTable(idColumnTable)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.getColumnsByTableId(this.idTableIn);
        }
      });
  }
  //#endregion
}
