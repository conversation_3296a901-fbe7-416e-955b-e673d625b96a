import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatNativeDateModule } from '@angular/material/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { catchError, of } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDialog } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { ImageModel } from 'src/app/shared/models/image-picker';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatSelectModule } from '@angular/material/select';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { NewsService } from 'src/app/shared/services/news/news.service';
import { UploadImageVariesExtComponent } from 'src/app/shared/components/upload-image-varies-ext/upload-image-varies-ext.component';

@Component({
  selector: 'app-edit-active-news',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatSlideToggleModule,
    MatCardModule,
    TranslateModule,
    UploadImageVariesExtComponent,
    PreventionSqlInjectorDirective,
    MatSelectModule,
  ],
  templateUrl: './edit-active-news.component.html',
  styleUrls: ['./edit-active-news.component.scss'],
})
export class EditActiveNewsComponent implements OnInit {
  @Input() newsId: number = 0;
  @Input() pkIIdBusinessByCountry: number = 0;
  @Input() objetActNews: any = {};
  @Input() userType: number = 0;
  @Output() getNewsByIdBusinessCountry = new EventEmitter<number>();

  formActiveNews: FormGroup = new FormGroup({});
  operationType: string = 'create';
  imageSrc: string = '';
  title: string = '';
  description: string = '';
  category: string = '';
  link: string = '';
  listSize: any[] = [];
  size: number = 0;

  keepReading: boolean = false;
  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    public utilsSvc: UtilsService,
    public _translateService: TranslateService,
    private _fileService: FileService,
    private _newsService: NewsService,
    public modalDialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.validatedEditOrNew();
    this.getSizeNew();
  }

  initForm() {
    this.formActiveNews = this._fb.group({
      pkIIdNews: [0],
      vTitle: ['', [Validators.required]],
      vDescription: ['', [Validators.required, Validators.maxLength(150)]],
      vLink: [
        '',
        [
          Validators.pattern(
            /(?:https?):\/\/(\w+:?\w*)?(\S+)(:\d+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/
          ),
        ],
      ],
      dEndDate: [null],
      bActiveButton: [null],
      vTextButton: [null],
      iOrder: [0],
      fkIIdUploadFile: [0],
      fkIIdSectionBusiness: [this.objetActNews?.pkIIdSectionIndexBusiness],
      fkIIdBusinessByCountry: [this.pkIIdBusinessByCountry],
      fileName: ['', [Validators.required]],
      imageBase64: ['', [Validators.required]],
      isImageEdit: [false],
      fkIIdSizeNew: [null, [Validators.required]],
      vCategory: ['', [Validators.required]],
      fkIIdUserType: [this.userType],
    });

    this.formActiveNews.get('vTitle')?.valueChanges.subscribe({
      next: (data) => {
        data ? (this.title = data) : '';
      },
    });
    this.formActiveNews.get('vDescription')?.valueChanges.subscribe({
      next: (data) => {
        data ? (this.description = data) : '';
      },
    });

    this.formActiveNews.get('vLink')?.valueChanges.subscribe({
      next: (data) => {
        data ? (this.keepReading = true) : (this.keepReading = false);
      },
    });
  }
  get formValid(): boolean {
    return this.formActiveNews.valid;
  }

  validatedEditOrNew() {
    if (this.newsId > 0) {
      this._newsService.getNewsById(this.newsId).subscribe((resp) => {
        console.log(resp.result);
        this.formActiveNews.patchValue({ pkIIdNews: resp.result.pkIIdNews });
        this.formActiveNews.patchValue({ vTitle: resp.result.vTitle });
        this.formActiveNews.patchValue({
          vDescription: resp.result.vDescription,
        });
        this.formActiveNews.patchValue({
          bActiveButton: resp.result.bActiveButton,
        });
        this.formActiveNews.patchValue({
          vTextButton: resp.result.vTextButton,
        });
        this.formActiveNews.patchValue({ vLink: resp.result.vLink });
        this.formActiveNews.patchValue({ dEndDate: resp.result.dEndDate });
        this.formActiveNews.patchValue({ iOrder: resp.result.iOrder });
        this.formActiveNews.patchValue({
          fkIIdUploadFile: resp.result.fkIIdUploadFile,
        });
        this.formActiveNews.patchValue({
          fkIIdSectionBusiness: resp.result.fkIIdSectionBusiness,
        });
        this.formActiveNews.patchValue({ bActive: resp.result.bActive });
        this.formActiveNews.patchValue({
          fkIIdBusinessByCountry: resp.result.fkIIdBusinessByCountry,
        });
        this.formActiveNews.patchValue({
          fkIIdSizeNew: resp.result.fkIIdSizeNew,
        });
        this.formActiveNews.patchValue({ vCategory: resp.result.vCategory });
        this.formActiveNews.patchValue({
          fkIIdUserType: resp.result.fkIIdUserType,
        });
        this.size = resp.result.fkIIdSizeNew;
        this.getImage();
        this.title_Change();
        this.description_Change();
        this.category_Change();
        this.link_Change();
        this.operationType = 'edit';
      });
    } else {
      this.operationType = 'create';
    }
  }

  getImage() {
    let extension: string = '';
    this._fileService
      .getUploadFileById(this.formActiveNews.get('fkIIdUploadFile')?.value)
      .subscribe({
        next: (response) => {
          if (response.result.vFileName && response.result.imageBase64) {
            extension = response.result.vFileName.split('.');
            extension =
              extension[response.result.vFileName.split('.').length - 1];
            this.imageSrc = `data:image/${extension};base64,${response.result.imageBase64}`;
            this.formActiveNews
              .get('fileName')
              ?.setValue(response.result.vFileName);
            this.formActiveNews
              .get('imageBase64')
              ?.setValue(response.result.imageBase64);
          } else {
            this._messageService.messageInfo(
              this._translateService.instant('Warning'),
              this._translateService.instant(
                'ThisIsurerHasNotAssociatedPicture'
              )
            );
          }
        },
      });
  }

  changeFile(event: ImageModel) {
    if (this.operationType === 'edit') {
      this.formActiveNews.get('isImageEdit')?.setValue(true);
    }
    this.imageSrc = event.dataString;
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString.split(',')[1];
    this.formActiveNews.get('fileName')?.setValue(`${imageName}.${extension}`);
    this.formActiveNews.get('imageBase64')?.setValue(base64);
  }

  // Función para eliminar imágen cargada.
  deleteFile(event: boolean) {
    if (event) {
      this.formActiveNews.get('fileName')?.setValue('');
      this.formActiveNews.get('imageBase64')?.setValue('');
      this.imageSrc = '';
    }
  }

  /// validates the type of operation that is going to be performed
  completeNews() {
    if (this.operationType === 'create') {
      this.saveNew();
    } else {
      this.updateNews();
    }
  }

  title_Change() {
    this.title = this.formActiveNews.get('vTitle')?.value;
  }
  category_Change() {
    this.category = this.formActiveNews.get('vCategory')?.value;
  }

  description_Change() {
    this.description = this.formActiveNews.get('vDescription')?.value;
  }

  link_Change() {
    this.link = this.formActiveNews.get('vLink')?.value;
  }

  saveNew() {
    if (this.formValid) {
      this._newsService
        .createNews(this.formActiveNews.value)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
            this.modalDialog.closeAll();
            this.getNewsByIdBusinessCountry.emit(this.pkIIdBusinessByCountry);
            return of([]);
          })
        )
        .subscribe({
          next: (response) => {
            if (Array.isArray(response)) {
              console.log('El tipo de datos devueltos es un array vacío.');
            } else {
              if (!response.error) {
                this._messageService.messageSuccess(
                  this._translateService.instant('DataSavedSuccessfully'),
                  ''
                );
                this.modalDialog.closeAll();
                this.getNewsByIdBusinessCountry.emit(
                  this.pkIIdBusinessByCountry
                );
              } else {
                this._messageService.messageInfo(
                  this._translateService.instant('ThereWasAError'),
                  response.message
                );
              }
            }
          },
        });
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('Warning'),
        this._translateService.instant('CheckTheFormIsFilledCorrectly')
      );
    }
  }

  updateNews() {
    if (this.formValid) {
      this._newsService
        .updateNews(this.formActiveNews.value)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe({
          next: (response) => {
            if (Array.isArray(response)) {
              console.log('El tipo de datos devueltos es un array vacío.');
            } else {
              if (!response.error) {
                this._messageService.messageSuccess(
                  this._translateService.instant('DataSavedSuccessfully'),
                  response.message
                );
                this.modalDialog.closeAll();
                this.getNewsByIdBusinessCountry.emit(
                  this.pkIIdBusinessByCountry
                );
              } else {
                this._messageService.messageInfo(
                  this._translateService.instant('ThereWasAError'),
                  response.message
                );
              }
            }
          },
        });
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('Warning'),
        this._translateService.instant('CheckTheFormIsFilledCorrectly')
      );
    }
  }

  deleteteNews() {
    this._newsService.deleteNews(this.newsId).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('Delete'),
            ''
          );
          this.modalDialog.closeAll();
          this.getNewsByIdBusinessCountry.emit(this.pkIIdBusinessByCountry);
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  cancel() {
    this.formActiveNews.reset();
    this.modalDialog.closeAll();
  }

  SizeChange(evt: any) {
    this.size = evt.value;
  }

  getSizeNew() {
    this._newsService
      .GetSizeNew()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe({
        next: (response) => {
          if (!Array.isArray(response)) {
            this.listSize = response.error == false ? response.result : [];
          }
        },
      });
  }
}
