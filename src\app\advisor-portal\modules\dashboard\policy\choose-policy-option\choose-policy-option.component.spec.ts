import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ChoosePolicyOptionComponent } from './choose-policy-option.component';

describe('ChoosePolicyOptionComponent', () => {
  let component: ChoosePolicyOptionComponent;
  let fixture: ComponentFixture<ChoosePolicyOptionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ ChoosePolicyOptionComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ChoosePolicyOptionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
