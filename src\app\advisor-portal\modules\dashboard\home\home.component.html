<div class="title-secundary">
  <h2 class="h3">{{"Hello" | translate}}
  <span>{{userSessionName}}</span>
  </h2>
</div>

<div *ngFor="let section of listSectionIndex" class="cont-home-advisors">
  <ng-container *ngIf="section.fkIdSectionIndex === 1 && section.bActive">
    <div class="cont-products">
      <app-products-home [productsObject]="productsObject"></app-products-home>
    </div>
  </ng-container>
  <ng-container *ngIf="section.fkIdSectionIndex === 2 && section.bActive">
    <div class="cont-news">
      <app-news-home [newsObject]="newsObject"></app-news-home>
    </div>
  </ng-container>
  <ng-container *ngIf="section.fkIdSectionIndex === 3 && section.bActive">
    <div class="cont-tops">
      <app-tops-home [topsObject]="topsObject"></app-tops-home>
    </div>
  </ng-container>
  <ng-container *ngIf="section.fkIdSectionIndex === 4 && section.bActive">
    <div class="cont-organization-chart">
      <app-organization-chart-home
        [organizationChartObject]="organizationChartObject"
      ></app-organization-chart-home>
    </div>
  </ng-container>

  <ng-container *ngIf="section.fkIdSectionIndex === 6 && section.bActive">
    <div class="cont-organization-chart">
      <app-incentives-home></app-incentives-home>
    </div>
  </ng-container>
</div>
