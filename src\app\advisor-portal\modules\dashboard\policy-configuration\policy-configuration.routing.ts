import { Routes } from '@angular/router';
import { PolicyConfigurationComponent } from './policy-configuration.component';

export default [
  {
    path: '',
    component: PolicyConfigurationComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-policy/all-policy.component').then(
            (c) => c.AllPolicyComponent
          ),
      },
      {
        path: 'new/:idBusinessByCountry/:idCountry',
        loadComponent: () =>
          import('./new-edit-policy/new-edit-policy.component').then(
            (c) => c.NewEditPolicyComponent
          ),
      },
      {
        path: 'modify/:idBusinessByCountry/:idPolicy/:idCountry',
        loadComponent: () =>
          import('./new-edit-policy/new-edit-policy.component').then(
            (c) => c.NewEditPolicyComponent
          ),
      },
    ],
  },
] as Routes;
