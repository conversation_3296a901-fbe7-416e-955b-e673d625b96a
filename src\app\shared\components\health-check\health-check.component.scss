:host {
    display: block;
    padding: 20px;
    font-family: sans-serif;
    width: 100%;
    height: 100%;
}

.health-check-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    width: 100%;
}

.status-indicator {
    font-weight: bold;
}

.healthy {
    color: #00c853; /* Green */
}

.unhealthy {
    color: #ff3d00; /* Red */
}

.health-status {
    color: white;
    font-weight: bold;
    background-color: #0277bd;
    padding: 5px 15px;
    border-radius: 20px;
    display: inline-block;
}

.error-message {
    background-color: #ffebee;
    border-left: 4px solid #ff3d00;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toggle-btn {
    background-color: #0277bd;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    transition: background-color 0.3s;
}

.toggle-btn:hover {
    background-color: #01579b;
}

#ssr-health-check {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    background-color: #ffffff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    text-align: center;
}

h3 {
    margin-top: 20px;
    color: #444;
    font-size: 18px;
}

#ssr-rendering-info {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

#health-details {
    line-height: 1.6;
}
