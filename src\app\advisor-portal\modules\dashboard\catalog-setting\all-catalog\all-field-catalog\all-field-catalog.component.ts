import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { catchError, of, Subscription } from 'rxjs';
import { Router, ActivatedRoute } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  TranslateModule,
  TranslateService,
  LangChangeEvent,
} from '@ngx-translate/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { CommonModule } from '@angular/common';

import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import {
  CatalogFieldModel,
  CatalogModel,
} from 'src/app/shared/models/catalog-setting/catalog.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-field-catalog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    TableComponent,
  ],
  templateUrl: './all-field-catalog.component.html',
  styleUrls: ['./all-field-catalog.component.scss'],
})
export class AllFieldCatalogComponent implements OnInit {
  form: FormGroup = new FormGroup({});
  estructTableField: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'CatalogSetting.TableCatalogField.Field'
      ),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant(
        'CatalogSetting.TableCatalogField.Order'
      ),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];
  fieldTable: CatalogFieldModel[] = [];
  action: string = 'create';
  idCatalog: number = 0;

  constructor(
    private _translateService: TranslateService,
    public _utilsSvc: UtilsService,
    private _catalogService: CatalogSettingService,
    private _msgSvc: MessageService,
    private _fb: FormBuilder,
    public _router: Router,
    private _activatedRoute: ActivatedRoute,
    public _location: Location,
    private _customRouter: CustomRouterService

  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.validateAction();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table catalog
      this.estructTableField[0].columnLabel = this._translateService.instant(
        'CatalogSetting.TableCatalogField.Field'
      );
      (this.estructTableField[1].columnLabel = this._translateService.instant(
        'CatalogSetting.TableCatalogField.Order'
      )),
        (this.estructTableField[2].columnLabel =
          this._translateService.instant('Action'));
    });
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdCatalog: [0],
      vName: ['', [Validators.required]],
      bActive: [true],
      fkIIdBusinessByCountry: [null],
      fkIIdCountry: [null],
    });
  }

  validateAction() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.id) {
        this.idCatalog = params.id;
        this.getCatalogById(params.id);
        this.getCatalogFieldByCatalogId(params.id);
      } else {
        this.form
          .get('fkIIdBusinessByCountry')
          ?.setValue(params.idBusinessCountry);
        this.form.get('fkIIdCountry')?.setValue(params.idCountry);
      }
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  getCatalogById(idCatalog: number) {
    this._catalogService
      .getCatalogById(idCatalog)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.form.patchValue(response.result);
          }
        }
      });
  }

  getCatalogFieldByCatalogId(idCatalog: number) {
    this._catalogService
      .getCatalogFieldByCatalogId(idCatalog)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.fieldTable = response.result;
          }
        }
      });
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'modify':
        this._customRouter.navigate([
          `/dashboard/catalog-setting/modifyField/${this.idCatalog}/${event.value.pkIIdCatalogField}`,
        ]);
        break;
      default:
        break;
    }
  }

  addField() {
    this._customRouter.navigate([
      `/dashboard/catalog-setting/newField/${this.idCatalog}`,
    ]);
  }

  saveCatalog() {
    if (this.valid) {
      let idCountry = this.form.get('fkIIdCountry')?.value;
      let idBusinessCountry = this.form.get('fkIIdBusinessByCountry')?.value;
      if (Number(idCountry) === 0)
        this.form.get('fkIIdCountry')?.setValue(null);
      if (Number(idBusinessCountry) === 0)
        this.form.get('fkIIdBusinessByCountry')?.setValue(null);

      let payload: CatalogModel = this.form.getRawValue();
      this._catalogService
        .registerCatalog(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageInfo(
                this._translateService.instant('ThereWasAError'),
                resp.message
              );
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'CatalogSetting.MessagesCatalogo.SuccessfulMessageCreated'
                )
              );
              this.goBack();
            }
          }
        });
    }
  }

  updateCatalog() {
    if (this.valid) {
      let payload: CatalogModel = this.form.getRawValue();
      this._catalogService
        .updateCatalog(payload)
        .pipe(
          catchError((error) => {
            if (error.error != null) {
              if (error.error.error) {
                this._msgSvc.messageWaring(
                  this._translateService.instant('ThereWasAError'),
                  error.error.message
                );
              }
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageInfo(
                this._translateService.instant('ThereWasAError'),
                resp.message
              );
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'CatalogSetting.MessagesCatalogo.SuccessfulMessageUpdated'
                )
              );
              this.goBack();
            }
          }
        });
    }
  }

  /// Delete catalog by id
  deleteCatalog() {
    let idCatalog = 0;
    idCatalog = this.form.get('pkIIdCatalog')?.value;
    if (idCatalog > 0) {
      this._catalogService
        .deleteCatalog(idCatalog)
        .pipe(
          catchError((error) => {
            if (
              error.error.error &&
              error.error.message === 'hasAssociatedItems'
            ) {
              this._msgSvc.messageInfo(
                this._translateService.instant(
                  'CatalogSetting.MessagesCatalogo.deletedMessageTitle'
                ),
                this._translateService.instant(
                  'CatalogSetting.MessagesCatalogo.deletedMessageSubTitle'
                )
              );
            } else {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageInfo(
                this._translateService.instant('ThereWasAError'),
                resp.message
              );
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant('DeleteMessage')
              );
              this.goBack();
            }
          }
        });
    }
  }

  goBack() {
    this._location.back();
    this.form.reset();
  }
}
