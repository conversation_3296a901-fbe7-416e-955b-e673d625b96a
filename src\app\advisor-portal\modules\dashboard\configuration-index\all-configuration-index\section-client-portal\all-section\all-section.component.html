<div class="row mt-2">
  <div class="row mb-2">
    <h4 class="col-md-12">{{ "SectionClientPortal.Sections" | translate }}</h4>
  </div>
  <mat-accordion>
    <mat-expansion-panel *ngFor="let item of listSections">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <strong>{{ "SectionClientPortal.Section" | translate }} {{item.order}}</strong>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <form>
        <div class="row">
          <div class="col-12 col-md-6">
            <mat-form-field class="w-100" appearance="fill">
              <mat-label>
                {{ "SectionClientPortal.Title" | translate }}
              </mat-label>
              <input matInput PreventionSqlInjector readonly [value]="item.title" />
            </mat-form-field>

            <mat-form-field class="w-100" appearance="fill">
              <mat-label>
                {{ "SectionClientPortal.Subtitle" | translate }}
              </mat-label>
              <input matInput PreventionSqlInjector readonly [value]="item.subTitle" />
            </mat-form-field>

            <div class="w-100">
              <img [src]="item.blob" class="img-section-client">
            </div>

            <div class="container group-flow">
              <mat-checkbox disabled [checked]="item.hasButton">
                {{ "SectionClientPortal.HasButton" | translate}}
              </mat-checkbox>
            </div>

            <ng-container *ngIf="item.hasButton === true">
              <mat-form-field class="w-100" appearance="fill">
                <mat-label>
                  {{ "SectionClientPortal.ButtonText" | translate }}
                </mat-label>
                <input matInput PreventionSqlInjector readonly [value]="item.buttonText" />
              </mat-form-field>

              <mat-form-field class="w-100" appearance="fill">
                <mat-label>
                  {{ "SectionClientPortal.ButtonLink" | translate }}
                </mat-label>
                <input matInput PreventionSqlInjector readonly [value]="item.buttonLink" />
              </mat-form-field>
            </ng-container>

            <div class="d-flex justify-content-center">
              <div class="mt-3">
                <button class="mx-2" (click)="modify(item.idSection)" type="button" mat-raised-button color="primary">
                  <mat-icon iconPositionEnd fontIcon="edit"></mat-icon>
                  {{ "Modify" | translate }}
                </button>
                <button class="mx-2" (click)="delete(item.idSection)" type="button" mat-raised-button color="warn">
                  <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
                  {{ "Delete" | translate }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </mat-expansion-panel>
  </mat-accordion>
</div>
<br />
<div class="row">
  <div class="col">
    <button type="button" mat-raised-button color="primary" (click)="gotToCreate()">
      {{ "SectionClientPortal.AddSection" | translate }}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
</div>

<ng-template #sectionClientPortalModal>
  <app-modal2 [titleModal]="titelModal" [showTooltip]="true" [showCancelButton]="false">
    <ng-container body>
      <app-create-edit-section [idOrder]="idOrder" [idBusinessByCountry]="idBusinessByCountry"
                               [idSection]="sectionId" (changeSuccess)="changeSuccess($event)">
      </app-create-edit-section>
    </ng-container>
  </app-modal2>
</ng-template>
