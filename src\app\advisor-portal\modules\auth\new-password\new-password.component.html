<app-toast [toastConfiguration]="toastConfiguration"></app-toast>
<section class="row container d-flex flex-wrap">
  <div class="col-12 col-md-6 d-flex flex-column align-items-center">
    <h2>Nueva contraseña</h2>
    <form
      [formGroup]="form"
      (ngSubmit)="recoverPassword()"
      style="margin: 32px 0"
    >
      <p>Por favor, ingresa la nueva contraseña</p>
      <div class="col-12">
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>Contraseña</mat-label>
          <input
            matInput
            placeholder="Contraseña"
            formControlName="password"
            required
            type="password"
          />
          <mat-error
            *ngIf="utilsService.isControlHasError(form, 'password', 'required')"
          >
            Este campo es requerido
          </mat-error>
          <mat-error
            *ngIf="utilsService.isControlHasError(form, 'password', 'pattern')"
          >
            La contraseña no es valida
          </mat-error>
        </mat-form-field>
      </div>
      <div class="col-12">
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>Confirmar nueva contraseña</mat-label>
          <input
            matInput
            placeholder="Confirmar nueva contraseña"
            formControlName="confirmPassword"
            required
            type="password"
          />
          <mat-error
            *ngIf="
              utilsService.isControlHasError(
                form,
                'confirmPassword',
                'required'
              )
            "
          >
            Este campo es requerido
          </mat-error>
          <mat-error
            *ngIf="
              utilsService.isControlHasError(form, 'confirmPassword', 'pattern')
            "
          >
            La contraseña no es valida
          </mat-error>
        </mat-form-field>
      </div>
      <div class="col-12 mt-2 d-flex justify-content-around">
        <button
          type="submit"
          class="btn btn-primary d-inline-flex"
          [disabled]="!valid"
        >
          Asignar contraseña
        </button>
      </div>
    </form>
  </div>
</section>
