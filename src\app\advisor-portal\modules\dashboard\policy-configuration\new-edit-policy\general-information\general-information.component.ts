import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, debounceTime, of, Subscription } from 'rxjs';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import {
  ListPolicyFileModel,
  PolicyDetailModel,
  RequestGeneralInfoModel,
} from 'src/app/shared/models/policy';
import { PorductModulesModel } from 'src/app/shared/models/product/product-modules.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SelectModel } from 'src/app/shared/models/select';
import { GlobalSelectModel } from 'src/app/shared/models/shared';
import { FileService } from 'src/app/shared/services/file/file.service';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-general-information',
  standalone: true,
  imports: [
    CommonModule,
    MatSlideToggleModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    ValidationInputFileDirective
  ],
  templateUrl: './general-information.component.html',
  styleUrls: ['./general-information.component.scss'],
})
export class GeneralInformationComponent implements OnInit {
  //variable formulario.
  form: FormGroup = new FormGroup({});
  productList: PorductModulesModel[] = [];
  typeOfValidity: GlobalSelectModel[] = [];
  insuranceList: InsuranceCompanyModel[] = [];
  listFile: File[] = [];
  documentsTypes: SelectModel[] = [];
  selectedFileName: string = '';
  fileTypes: string[] = ['XLS', 'CSV', 'PDF', 'PNG', 'JPG'];
  maxFileSizeMB: number = 20;
  showByPolicyType: boolean = false;
  idPolicy: number = 0;
  idPolicyType: number = 0;
  idBusinessByCountry: number = 0;
  listIdFiles: ListPolicyFileModel[] = [];
  showBtnDelete: boolean = false;
  showBtnDownload: boolean = false;
  isEditing: boolean = false;

  //Variables para compartir información entre componentes.
  @Output() formGeneralInfo = new EventEmitter<RequestGeneralInfoModel>();
  @Output() editedFiles = new EventEmitter<boolean>();
  @Input() isANewPolicy!:boolean;

  private _policyDataSubscription?: Subscription;
  private localStorageService = inject(LocalStorageService);
  
  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _activatedRoute: ActivatedRoute,
    public utilsSvc: UtilsService,
    private _parametersService: ParametersService,
    private _insuranceService: InsuranceService,
    private _productService: ProductService,
    private _transactionService: TransactionService,
    private _policyService: PolicyService,
    private _fileService: FileService,
    public _translateService: TranslateService
  ) {}

  get formValid(): boolean {
    return this.form.valid;
  }

  ngOnInit(): void {
    this.getBusinessByCountry();
    this.getPolicyDataSubscription();
    this.getListDocumentType();
    this.initForm();
    this.getAllValidyOfType();
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
        this.getInsuranceCompanyByIdBusinessByCountry(this.idBusinessByCountry);
        this.getAllProducts(this.idBusinessByCountry);
      }
      if (params.idPolicy) {
        this.isEditing = true;
        this.idPolicy = Number(params.idPolicy);
        this.getPolicyDetailById(this.idPolicy);
      }
    });
  }

  getPolicyDataSubscription() {
    this._policyDataSubscription =
      this._policyService.currentpolicyData.subscribe({
        next: (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idPolicy = response.idPolicy;
            this.idPolicyType = response.idPolicyType;
            this.validationsForm(response.idPolicyType);
            if (!(Object.keys(this.form.controls).length === 0)) {
              const form: RequestGeneralInfoModel = this.form.getRawValue();
              form.formValid = this.formValid;
              this.formGeneralInfo.emit(form);
            }
            if (response.idPolicy > 0) {
              this.form.get('IdPolicy')?.setValue(response.idPolicy);
              this.getPolicyDetailById(response.idPolicy);

              if (!this.isEditing) {
                this.listFile = [];
                this.listIdFiles = [];
                this.form.get('Files')?.setValue(null);
              }
            }
            this.form.get('IdPolicyType')?.setValue(response.idPolicyType);
          }
        },
      });
  }

  initForm() {
    this.form = this._fb.group({
      IdPolicyType: [0],
      PolicyName: [null, [Validators.required]],
      IdProduct: [null, [Validators.required]],
      IdInsurance: [null, [Validators.required]],
      Active: [true, [Validators.required]],
      BAssociatedWithCredit: [true],
      IdBusinessCountry: [this.idBusinessByCountry, [Validators.required]],
      IdPolicy: [{ value: null, disabled: true }],
      IdValidityType: [null],
      PolicyNumber: [null],
      StartValidity: [null],
      EndValidity: [null],
      PolicyHolder: [null],
      IdDocumentType: [null],
      DocumentNumber: [null],
      Files: [null],
      EditedFiles: [false],
    });
    this.form.valueChanges.pipe(debounceTime(300)).subscribe({
      next: (data: RequestGeneralInfoModel) => {
        const form: RequestGeneralInfoModel = this.form.getRawValue();
        form.Files = this.listFile;
        if (this.form.status === 'DISABLED') {
          form.formValid = true;
        } else {
          form.formValid = this.form.valid;
        }
        this.formGeneralInfo.emit(form);
        if (data.Files) {
          this.showBtnDelete = true;
        } else {
          this.showBtnDelete = false;
        }
         this.getFlagReplicatePolicy();
        },
    });

    this.form
      .get('IdDocumentType')
      ?.valueChanges.pipe(debounceTime(300))
      .subscribe({
        next: (data: string) => {
          if (this.form.get('DocumentNumber')?.value && data) {
            this.getCustomerByDocumentNumberAndIdBusinessByCountry(
              this.idBusinessByCountry,
              this.form.get('DocumentNumber')?.value
            );
          }
        },
      });

    this.form
      .get('DocumentNumber')
      ?.valueChanges.pipe(debounceTime(300))
      .subscribe({
        next: (data: string) => {
          if (this.form.get('IdDocumentType')?.value && data) {
            this.getCustomerByDocumentNumberAndIdBusinessByCountry(
              this.idBusinessByCountry,
              this.form.get('DocumentNumber')?.value
            );
          }
        },
      });
  }

  //Detecta los cambios del campo Activar póliza.
  onToggleChange(event: any) {}

  //Obtiene la lista de tipo de identificación registrados en el sistema.
  getListDocumentType() {
    this._parametersService
      .getListCatalogGlobalDocumentTypes()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.documentsTypes = resp.result;
        }
      });
  }

  //Obtiene todas las aseguradoras asociadas por IdBusinessByCountry.
  getInsuranceCompanyByIdBusinessByCountry(idBusinessByCountry: number) {
    this._insuranceService
      .getInsuranceCompanyByIdBusinessByCountry(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.insuranceList = resp.result;
        }
      });
  }

  //Obtiene todos los prodcutos asociadas por IdBusinessByCountry.
  getAllProducts(idBusinessByCountry: number) {
    this._productService
      .getAllProductByBusiness(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.productList = resp.result;
        }
      });
  }

  //Obtiene todos los tipos de vigencia registrados en el sistema.
  getAllValidyOfType() {
    this._parametersService
      .getParameters('Type_Of_Validity')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.typeOfValidity = resp;
        } else {
        }
      });
  }

  //Obtiene todos los tipos de vigencia registrados en el sistema.
  getCustomerByDocumentNumberAndIdBusinessByCountry(
    idBusinessByCountry: number,
    documentNumber: string
  ) {
    this._transactionService
      .getCustomerByDocumentNumberAndIdBusinessByCountry(
        idBusinessByCountry,
        documentNumber
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this.form.get('PolicyHolder')?.enable();
            this.form.get('PolicyHolder')?.setValue(null);
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.form.get('PolicyHolder')?.enable();
          this.form.get('PolicyHolder')?.setValue(null);
        } else {
          this.form.get('PolicyHolder')?.setValue(resp.result.vFullName);
          this.form.get('PolicyHolder')?.disable();
        }
      });
  }

  //Permite descargar desde la api de file una lista de archivos.
  getFileByIdList(idFiles: Array<number>) {
    this._fileService
      .GetFileByIdList(idFiles)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: any) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            // Intenta obtener el nombre del archivo desde la cabecera Content-Disposition
            const fileName = resp.headers.get('X-File-Name') || 'file.zip';
            // Especifica el tipo MIME del archivo según el tipo que se espera
            const mimeType = 'application/octet-stream';
            const blob = new Blob([resp.body], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = fileName;
            link.href = url;
            link.click();
            window.URL.revokeObjectURL(url);
          }
        }
      });
  }

  //Obtiene la lista de tipo de identificación registrados en el sistema.
  getPolicyDetailById(idPolicy: number) {
    this._transactionService
      .getPolicyDetailById(idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.validationsForm(resp.result.idPolicyType);
          this.assignDataForm(resp.result);
        }
      });
  }

  onFileChange(event: Event) {
    const guid = crypto.randomUUID();
    const inputElement = event.target as HTMLInputElement;
    const files = inputElement.files;
    let totalSize: number = 0;

    // Array de extensiones permitidas
    const allowedExtensions: string[] = ['xls', 'csv', 'pdf', 'png', 'jpg'];

    // Limpiamos la lista de archivos y el nombre de los archivos seleccionados si ya hay elementos guardados.
    if (this.listFile) {
      this.selectedFileName = '';
    }

    let fileName: Array<string> = [];

    if (
      this.selectedFileName === '' ||
      this.selectedFileName.split(' ').length == 0
    ) {
      if (files && files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const fileExtension = file.name.split('.').pop()?.toLowerCase(); // Obtenemos la extensión del archivo

          // Validamos si la extensión está permitida
          if (fileExtension && allowedExtensions.includes(fileExtension)) {
            this.selectedFileName += `${guid}-` + file.name + ' ';
            fileName.push(`${guid}-` + file.name);
            totalSize += file.size;
          } else {
            this._messageService.messageInfo(
              `El archivo ${file.name} tiene una extensión no permitida.`,
              `Solo se permiten: ${allowedExtensions.join(', ').toUpperCase()}`
            );

            // Limpiar la lista de archivos y el nombre seleccionado
            this.selectedFileName = '';
            this.listFile = [];
            this.listIdFiles = [];
            this.editedFiles.emit(false); // Emitir el estado de que no se seleccionaron archivos válidos

            return; // Salir de la función si hay un archivo con extensión no válida
          }
        }

        totalSize = totalSize / (1024 * 1024);
        if (totalSize < 20) {
          const copiedFiles: File[] = Array.from(files).map(
            (file) =>
              new File([file], `${guid}-` + file.name, { type: file.type })
          );
          // const file: FileModel = {
          //   files: copiedFiles,
          //   nameField: 'soportDocumentPolicyDb',
          // };
          this.listFile = copiedFiles;
          this.assignDocumentNames();
          this.editedFiles.emit(true);
        }
      }
    }
  }

  //Evento que se dispara una vez se preciona en el iciono de descargar documento.
  downloadDocument() {
    if (this.listIdFiles.length > 0) {
      this.getFileByIdList(this.listIdFiles.map((value) => value.idUploadFile));
    }
  }

  //Elimina el archivo cargado.
  deleteDocument() {
    this.form.get('Files')?.setValue(null);
    this.listFile = [];
    this.listIdFiles = [];
    this.selectedFileName = '';
    this.showBtnDownload = false;
    if (this.isEditing || this.idPolicy) {
      this.form.get('EditedFiles')?.setValue(true);
    } else {
      this.form.get('EditedFiles')?.setValue(false);
    }
  }

  validationsForm(policyType: number) {
    switch (policyType) {
      case 1:
        this.form.get('IdValidityType')?.setValue(null);
        this.form.get('StartValidity')?.setValue(null);
        this.form.get('EndValidity')?.setValue(null);
        this.form.get('IdDocumentType')?.setValue(null);
        this.form.get('DocumentNumber')?.setValue(null);
        this.form.get('PolicyHolder')?.setValue(null);
        this.form.get('PolicyNumber')?.clearValidators();
        this.form.get('IdValidityType')?.clearValidators();
        this.form.get('IdValidityType')?.disable();
        this.form.get('StartValidity')?.disable();
        this.form.get('EndValidity')?.disable();
        this.form.get('IdDocumentType')?.disable();
        this.form.get('DocumentNumber')?.disable();
        this.form.get('PolicyHolder')?.disable();
        this.showByPolicyType = false;

        break;
      case 2:
        this.form.get('PolicyNumber')?.clearValidators();
        this.form.get('IdValidityType')?.clearValidators();
        this.form.get('IdValidityType')?.enable();
        this.form.get('StartValidity')?.enable();
        this.form.get('EndValidity')?.enable();
        this.form.get('IdDocumentType')?.enable();
        this.form.get('DocumentNumber')?.enable();
        this.form.get('PolicyHolder')?.enable();

        this.form.get('PolicyNumber')?.setValidators(Validators.required);
        this.form.get('PolicyNumber')?.updateValueAndValidity();
        this.form.get('IdValidityType')?.setValidators(Validators.required);
        this.form.get('IdValidityType')?.updateValueAndValidity();
        this.form.get('StartValidity')?.setValidators(Validators.required);
        this.form.get('StartValidity')?.updateValueAndValidity();
        this.form.get('EndValidity')?.setValidators(Validators.required);
        this.form.get('EndValidity')?.updateValueAndValidity();
        this.form.get('IdDocumentType')?.setValidators(Validators.required);
        this.form.get('IdDocumentType')?.updateValueAndValidity();
        this.form.get('DocumentNumber')?.setValidators(Validators.required);
        this.form.get('DocumentNumber')?.updateValueAndValidity();
        this.form.get('PolicyHolder')?.setValidators(Validators.required);
        this.form.get('PolicyHolder')?.updateValueAndValidity();
        this.showByPolicyType = true;

        break;
      case 3:
        this.form.get('PolicyNumber')?.clearValidators();
        this.form.get('PolicyNumber')?.updateValueAndValidity();
        this.form.get('IdValidityType')?.clearValidators();
        this.form.get('IdValidityType')?.enable();
        this.form.get('StartValidity')?.enable();
        this.form.get('EndValidity')?.enable();
        this.form.get('IdDocumentType')?.enable();
        this.form.get('DocumentNumber')?.enable();

        this.form.get('IdValidityType')?.setValidators(Validators.required);
        this.form.get('IdValidityType')?.updateValueAndValidity();
        this.form.get('StartValidity')?.setValidators(Validators.required);
        this.form.get('StartValidity')?.updateValueAndValidity();
        this.form.get('EndValidity')?.setValidators(Validators.required);
        this.form.get('EndValidity')?.updateValueAndValidity();
        this.form.get('IdDocumentType')?.setValidators(Validators.required);
        this.form.get('IdDocumentType')?.updateValueAndValidity();
        this.form.get('DocumentNumber')?.setValidators(Validators.required);
        this.form.get('DocumentNumber')?.updateValueAndValidity();
        this.form.get('PolicyHolder')?.setValidators(Validators.required);
        this.form.get('PolicyHolder')?.updateValueAndValidity();
        this.showByPolicyType = true;
        break;

      default:
        break;
    }
  }

  assignDocumentNames() {
    let listFileName: string = '';
    this.listFile.forEach((element) => {
      listFileName = listFileName + element.name;
    });
    this.form.get('Files')?.setValue(listFileName);
  }

  assignDataForm(data: PolicyDetailModel) {
    if (data.idPolicyType !== 1) {
      this.getValidateRiskAndPolicyByIdPolicy(data.idPolicy);
    }
    this.form.get('PolicyName')?.setValue(data.policyName);
    this.form.get('IdPolicyType')?.setValue(data.idPolicyType);
    this.form.get('IdProduct')?.setValue(data.idProduct);
    this.form.get('IdInsurance')?.setValue(data.idInsurance);
    this.form.get('Active')?.setValue(data.active);
    this.form.get('IdBusinessCountry')?.setValue(data.idBusinessCountry);
    this.form.get('IdPolicy')?.setValue(data.idPolicy);
    this.form.get('IdValidityType')?.setValue(data.idValidityType);
    this.form.get('PolicyNumber')?.setValue(data.policyNumber);
    if(data.startValidity){
      this.form.get('StartValidity')?.setValue(this.utilsSvc.stringToDate(data.startValidity))
    }
    if(data.endValidity){
      this.form.get('EndValidity')?.setValue(this.utilsSvc.stringToDate(data.endValidity));
    }
    this.form.get('PolicyHolder')?.setValue(data.policyHolder);
    this.form.get('IdDocumentType')?.setValue(data.idDocumentType);
    this.form.get('DocumentNumber')?.setValue(data.documentNumber);
    this.form.get('EditedFiles')?.setValue(false);
    this.form
      .get('BAssociatedWithCredit')
      ?.setValue(data.bAssociatedWithCredit);
    this.listIdFiles = data.filesCreated;
    if (data.filesCreated) {
      let listFileName: string = '';
      if (data.filesCreated.length > 0) {
        data.filesCreated.forEach((element: any) => {
          listFileName = listFileName + element.fileName;
        });
        this.form.get('Files')?.setValue(listFileName);
        this.showBtnDownload = true;
      }
    }
  }

  //Verifica si una póliza tiene riesgos y alguno está en estado activo.
  getValidateRiskAndPolicyByIdPolicy(idPolicy: number) {
    this._transactionService
      .getValidateRiskAndPolicyByIdPolicy(idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.isHasRisks && !resp.result.isHasActiveRisks) {
            if(!this.isANewPolicy){
              this.form.disable();
              this.form.get('Active')?.enable();
            }
           
          }
          if (resp.result.isHasRisks && resp.result.isHasActiveRisks) {
            if(!this.isANewPolicy){
              this.form.disable();
            }
           
          }
        }
      });
  }
  ngOnDestroy(): void {
    this._policyDataSubscription?.unsubscribe();
  }
  
  getFlagReplicatePolicy(){
    const flag = this.localStorageService.getItem('cloneFlag');
    if(flag !== null ){
      this.form.get('IdPolicy')?.setValue('');
      this.form.get('PolicyName')?.setValue('');
      this.form.get('PolicyNumber')?.setValue(''); 
      this.isANewPolicy=false;                  
    }
  }
}
