import {
  Component,
  OnInit,
  Input,
  Output,
  EventE<PERSON>ter,
  On<PERSON><PERSON>roy,
  ViewChild,
  TemplateRef,
} from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { catchError, of, Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';

import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { Sheet<PERSON>ode<PERSON> } from 'src/app/shared/models/composite-catalog/sheet.model';

import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';

import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-edit-catalog-sheet',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    Modal2Component,
    TranslateModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule,
  ],
  templateUrl: './edit-catalog-sheet.component.html',
  styleUrls: ['./edit-catalog-sheet.component.scss'],
})
export class EditCatalogSheetComponent implements OnInit, OnDestroy {
  @Input() idCatalog: number = 0;
  @Input() idBusinessCountry: number = 0;
  @Input() idCountry: number = 0;
  @Output() setActiveTabCatalog = new EventEmitter<any>();
  @ViewChild('AddSheetModal') AddSheetModal?: TemplateRef<any>;

  private _settingCompositeCatalogSubscription?: Subscription;

  dataJson: any = {};
  isEdit: boolean = false;
  form: FormGroup = new FormGroup({});

  modalTitle: string = this._translateService.instant(
    'CatalogSetting.AddSheet'
  );

  pkIIdHistoryCatalog: number = 0;
  sheetTable: any[] = [];
  originalArray: any[] = [];

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('CatalogSetting.Sheet'),
      columnValue: 'id',
    },
    {
      columnLabel: this._translateService.instant('CatalogSetting.SheetName'),
      columnValue: 'nameSheet',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  constructor(
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _catalogService: CatalogSettingService,
    private _msgSvc: MessageService,
    public _utilsSvc: UtilsService,
    public _location: Location,
    public modalDialog: MatDialog,
    private _fb: FormBuilder,
    public _router: Router
  ) {}

  ngOnInit(): void {
    this.getSettinCompositeCatalogSubscription();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      /// traduction titles modals
      this.modalTitle = this._translateService.instant(
        'CatalogSetting.AddColumn'
      );

      //traduccion data table sheet
      this.estructTable[0].columnLabel = this._translateService.instant(
        'CatalogSetting.Sheet'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'CatalogSetting.SheetName'
      );

      this.estructTable[2].columnLabel =
        this._translateService.instant('Delete');
      this.estructTable[3].columnLabel =
        this._translateService.instant('Modify');
    });

    if (this.idCatalog > 0) this.getSheetByIdHistoryCatalog(this.idCatalog);
  }

  initForm() {
    this.form = this._fb.group({
      id: [0],
      nameSheet: ['', Validators.required],
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  //Abre el modal para Añadir un festivo.
  openModal() {
    this.initForm();
    if (!this.isEdit) {
      this.modalTitle = this._translateService.instant(
        'CatalogSetting.AddSheet'
      );
    }

    this.modalDialog.open(this.AddSheetModal!, {
      width: '720px',
    });
  }

  getSettinCompositeCatalogSubscription() {
    this._settingCompositeCatalogSubscription =
      this._catalogService.currentCompositeSetting.subscribe((response) => {
        if (!(Object.keys(response).length === 0)) {
          this.dataJson = response;
          if (this.dataJson != null)
            if (this.dataJson.Sheets != undefined)
              this.sheetTable = this.dataJson.Sheets;
        }
      });
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'delete':
        this._msgSvc
          .messageConfirmationAndNegationReverseButton(
            this._translateService.instant(
              'CatalogSetting.MessagesCatalogo.DeleteItem'
            ),
            this._translateService.instant(
              'CatalogSetting.MessagesCatalogo.FunctionalityCatalog'
            ),
            'warning',
            this._translateService.instant('Cancel'),
            this._translateService.instant('Confirm')
          )
          .then((result) => {
            if (result) {
              this.deleteSheetArrayOriginal(event.value.id);
            }
          });
        break;
      case 'modify':
        this.modalTitle = this._translateService.instant(
          'CatalogSetting.EditSheet'
        );
        this.isEdit = true;
        this.openModal();
        this.form.patchValue(event.value);
        break;
      default:
        break;
    }
  }

  getSheetByIdHistoryCatalog(idCatalog: number) {
    this._catalogService
      .getSheetByIdHistoryCatalog(idCatalog)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if (response.result !== undefined) {
              this.sheetTable = response.result.sheets;
              if (this.dataJson.Sheets !== undefined) {
                this.sheetTable = this.dataJson.Sheets.concat(
                  response.result.sheets
                );
                this.dataJson.Sheets.includes(this.dataJson.Sheets);
              } else {
                this.saveCurrentDataCatalogComposite();
              }
              this.pkIIdHistoryCatalog = response.result.fkIdHistoryCatalog;
            }

            this.deleteDuplicateJSON();
          }
        }
      });
  }

  saveSheet() {
    if (this.valid) {
      let newOptionField = {
        id: this.generateNewId(),
        nameSheet: this.form.get('nameSheet')?.value,
      };

      if (this.idCatalog > 0) {
        this.sheetTable.push(newOptionField);
        this.sheetTable = this.sheetTable.slice();

        this.originalArray.push(newOptionField);
        this.originalArray = this.originalArray.slice();
      } else {
        this.sheetTable.push(newOptionField);
        this.sheetTable = this.sheetTable.slice();
        this.originalArray.push(newOptionField);
      }
      this.saveCurrentDataCatalogComposite();
      this.closeModal(false);
    }
    console.log(this.dataJson);
  }

  updateSheet() {
    if (this.valid) {
      let UpdateOptionField = {
        id: this.form.get('id')?.value,
        nameSheet: this.form.get('nameSheet')?.value,
      };

      this.deleteSheetArrayOriginal(this.form.get('id')?.value);
      this.sheetTable.push(UpdateOptionField);
      this.sheetTable = this.sheetTable.slice();
      this.originalArray.push(UpdateOptionField);

      this.saveCurrentDataCatalogComposite();
      this.closeModal(false);
    }
  }

  deleteSheetArrayOriginal(id: number) {
    this.originalArray = this.originalArray.filter(
      (objeto) => objeto.id !== id
    );

    const index = this.sheetTable.findIndex((item) => item.id === id);

    this.sheetTable.splice(index, 1);
    this.sheetTable = this.sheetTable.slice();

    if (!this.isEdit) {
      // Reordenar los 'order' del array
      this.sheetTable.forEach((sheet, idx) => {
        sheet.id = idx + 1; // El nuevo 'order' será el índice + 1
      });
    }
  }

  next() {
    let dataReturn = {
      value: 3,
      id: 0,
    };
    this.setActiveTabCatalog.emit(dataReturn);
  }

  saveCurrentDataCatalogComposite() {
    let payload: any = {
      pkIIdHistoryCatalog: this.pkIIdHistoryCatalog,
      Sheets: this.sheetTable,
    };
    Object.assign(this.dataJson, payload);
    this._catalogService.setCurrentEditNewCompositeSetting(this.dataJson);
  }

  generateNewId(): number {
    return this.sheetTable.length + 1; // Genera un ID basado en el tamaño del array
  }

  deleteDuplicateJSON() {
    this.sheetTable = this.sheetTable.filter(
      (item: { id: any }, index: any, self: any[]) =>
        index === self.findIndex((t) => t.id === item.id)
    );

    this.saveCurrentDataCatalogComposite();
  }

  goBack() {
    this.form.reset();
    let dataReturn = {
      value: 1,
      id: 0,
    };
    this.setActiveTabCatalog.emit(dataReturn);
  }

  goBackMain() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant('Out'),
        this._translateService.instant('CatalogSetting.MessagesCatalogo.UnsavedData'),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._location.back();
          this.form.reset();
        }
      });
  }

  closeModal($event: boolean) {
    this.isEdit = false;
    this.modalDialog.closeAll();
  }

  ngOnDestroy(): void {
    this._settingCompositeCatalogSubscription?.unsubscribe();
  }
}
