<form action="" id="business" [formGroup]="form">

    <!-- dropDown opcionales de pasos, pestañas, secciones y etapas -->
    <div class="row w-auto">
        <!--pasos de barra de progreso-->
        <mat-form-field appearance="outline" class="select-look col-4">
            <mat-label>
              {{ "Table.SelectStepFromProgressBar" | translate }}
            </mat-label>
            <mat-select 
              [disabled]="disableProgressBarSelect"
              formControlName="fkIIdProgressBar"
              #fkIIdProgressBar
              >
              <mat-option 
                *ngFor="let item of progressBarList" 
                [value]="item.pkIIdProgressBar"
                (onSelectionChange)="getTabModuleListByFormId()"  
              >
                {{ item.vName }}
              </mat-option>
            </mat-select>
        </mat-form-field>

        <!--pestañas-->
        <mat-form-field appearance="outline" class="select-look col-4">
            <mat-label>
                {{ "Table.SelectTab" | translate }}
            </mat-label>
            <mat-select 
              [disabled]="disableTabSelect"
              formControlName="fkIIdTabModule" 
              #fkIIdTabModule
              >
              <mat-option 
                *ngFor="let item of tabsList" 
                [value]="item.pkIIdTabModule"
                (onSelectionChange)="getSectionModuleByFormId()"  
              >
                {{ item.vName }}
              </mat-option>
            </mat-select>
        </mat-form-field>

        <!--secciones-->
        <mat-form-field appearance="outline" class="select-look col-4">
            <mat-label>
                {{ "Table.SelectSection" | translate }}
            </mat-label>
            <mat-select 
              [disabled]="disableSectionsSelect"
              formControlName="fkIIdSectionModule" 
              #fkIIdSectionModule
              >
              <mat-option *ngFor="let item of sectionsList" [value]="item.pkIIdSectionModule">
                {{ item.vName }}
              </mat-option>
            </mat-select>
        </mat-form-field>
    </div>

    <!-- nombre de tabla y tabla activa -->
    <div class="row w-50 mt-3">
        <!-- nombre tabla -->
        <mat-form-field appearance="outline" class="col-8">
            <mat-label>
                {{'Table.TableName'|translate}}
            </mat-label>
            <input matInput formControlName="vName"/>
            <mat-error *ngIf="utilsService.isControlHasError(form, 'vName', 'required')" >
              {{'ThisFieldIsRequired'|translate}}
            </mat-error>
        </mat-form-field>

        <!-- slide estado tabla -->
        <div class="row col-4">
            <mat-slide-toggle
                class="w-100"
                formControlName="bActive">
                {{ 'Table.ActiveTable' | translate }}
            </mat-slide-toggle>
        </div>      
    </div>

    <!-- checks boton eliminar, varios registros, boton carga y boton descarga -->
    <div class="row mt-3">        
        <div class="form-check d-flex col-2">
          <input class="form-check-input" 
            type="checkbox" 
            id="check1"
            formControlName="bDelete"
            />
          <label class="form-check-label" for="flexCheckDefault">
            {{ "Table.DeleteButton" | translate }}
          </label>
        </div>
        <div class="form-check d-flex col-2">
            <input class="form-check-input" 
              type="checkbox" 
              id="check2"
              formControlName="bMultipleValues"
              />
            <label class="form-check-label" for="flexCheckDefault">
              {{ "Table.AddMultipleRecords" | translate }}
            </label>
          </div>
          <div class="form-check d-flex col-2">
            <input class="form-check-input" 
              type="checkbox" 
              id="check3"
              formControlName="bUpload"
              />
            <label class="form-check-label" for="flexCheckDefault">
              {{ "Table.UploadButton" | translate }}
            </label>
          </div>
          <div class="form-check d-flex col-2">
            <input class="form-check-input" 
              type="checkbox" 
              id="check4"
              formControlName="bDownload"
              />
            <label class="form-check-label" for="flexCheckDefault">
              {{ "Table.DownloadButton" | translate }}
            </label>
          </div>
    </div>

    <!-- documento para descarga -->
    <div class="row mt-3" *ngIf="form.value.bDownload">
      <mat-form-field style="width: 100%;">
        <mat-label>
          {{ "Table.DocumentForDownload" | translate }}
        </mat-label>			
        <input type="text" readonly matInput formControlName="vFileName" />
        <input                
          type="file"
          multiple
          hidden
          #f_input
          (onChangeValidated)="selectFile($event)"
          ValidationInputFile 
          [allowedExtensions]="allowedExtensions"
        />
        <button  mat-icon-button matSuffix (click)="f_input.click()">
          <mat-icon>publish</mat-icon>
        </button>
      </mat-form-field>      
    </div>
    
    <!-- boton guardar tabla -->
    <div class="row mt-3" *ngIf="!tableAlreadyCreated">
      <button
        class="w-auto"
        type="button"
        (click)="createTable()"
        mat-raised-button
        color="primary"
      >
        {{'Table.SaveTable' | translate }} 
      </button>
    </div>

    <!-- dataTable de las columnas -->
    <div class="row mt-3">
      <app-table
        [displayedColumns]="estructTableColumns"
        [data]="dataTableColumns"
        (iconClick)="controller($event)"
      ></app-table>
    </div>
    <!-- boton crear campo -->
    <div *ngIf="tableAlreadyCreated" class="row">
      <button
        class="w-auto"
        type="button"
        (click)="openAddFieldModal(0)"
        mat-raised-button
        color="primary"
      >
        {{'Table.AddColumn' | translate }} 
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
    <!-- vista previa -->
    <div class="row mt-3">
      <h4 class="h4 mt-3">
        {{'Table.Preview' | translate }} 
      </h4>
      <table>
        <tr>
          <th *ngFor="let item of dataTableColumns">
            {{ item.vName }}
          </th>
          <th *ngIf="this.form.value.bDelete">
            {{'Delete' | translate }}
          </th>
          <th *ngIf="this.form.value.bUpload">
            {{'Upload' | translate }}
          </th>
          <th *ngIf="this.form.value.bDownload">
            {{'Download' | translate }}
          </th>
          
        </tr>
        <tr>
          <td *ngFor="let item of dataTableColumns">
            ejemplo {{ item.vName }}
          </td>
          <th *ngIf="this.form.value.bDelete">
            <mat-icon>delete</mat-icon>
          </th>
          <th *ngIf="this.form.value.bUpload">
            <mat-icon>upload</mat-icon>
          </th>
          <th *ngIf="this.form.value.bDownload">
            <mat-icon>download</mat-icon>
          </th>
        </tr>
      </table>
    </div>
    <!-- boton añadir registro -->
    <div *ngIf="tableAlreadyCreated" class="row mt-3">
      <button
        class="w-auto"
        type="button"
        mat-raised-button
      >
        {{'Table.AddRecord' | translate }} 
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </div>
</form>

<!-- modal añadir campo tabla -->
<ng-template #addFieldModal>
  <app-modal2 
    (saveModal)="saveColumnTable($event)" 
    [showSaveButton]="showModalSaveButton" 
    [saveButtonText]="modalSaveButtonText"
    [titleModal]="modalTittleText">
    <ng-container body> 
      <form id="business" [formGroup]="formField">
        <!-- estado  -->
        <div class="row mb-3">
          <mat-slide-toggle
            class="w-100"
            formControlName="bActive">            
            {{ 'Table.ActiveField' | translate }}
          </mat-slide-toggle>
        </div>

        <!-- campo -->
        <div class="row mb-3">
          <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
              {{ 'Table.SelectField' | translate }}
            </mat-label>
            <mat-select 
              formControlName="fkIIdFieldModule" 
              required>
              <mat-option 
                *ngFor="let item of fieldList" 
                [value]="item.pkIIdFieldModule"
              >
                {{ item.vNameField }}
              </mat-option>
              <mat-error *ngIf="utilsService.isControlHasError(formField, 'fkIIdFieldModule', 'required')" >
                {{'ThisFieldIsRequired'|translate}}
              </mat-error>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- orden -->
        <div class="row mb-3">
          <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
              {{ 'Table.Order' | translate }}
            </mat-label>
            <mat-select 
              formControlName="iOrder"
              required>
              <mat-option 
                *ngFor="let item of orderList" 
                [value]="item"
              >
                {{ item }}
              </mat-option>
              <mat-error *ngIf="utilsService.isControlHasError(formField, 'iOrder', 'required')" >
                {{'ThisFieldIsRequired'|translate}}
              </mat-error>
            </mat-select>
          </mat-form-field>
        </div>
      </form>       
    </ng-container>
  </app-modal2>
</ng-template>