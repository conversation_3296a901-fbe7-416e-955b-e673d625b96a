import { CommonModule, Location } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormGroup, FormsModule, ReactiveFormsModule, FormBuilder,
  FormControl,
  Validators,
  FormArray,
  ValidatorFn,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FieldComponent } from 'src/app/shared/components/field-generator/field.component';
import { TranslateModule, TranslateService, LangChangeEvent } from '@ngx-translate/core';
import { BehaviorSubject, catchError, Observable, of, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/store/app.reducers';
import { deleteFile, resetState } from 'src/app/store/actions/quote.actions';



import { ModuleService } from 'src/app/shared/services/module/module.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { DynamicState } from 'src/app/shared/models/module/dynamic.model';
import { addFile } from 'src/app/store/actions/quote.actions';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { FileModel } from 'src/app/shared/models/my-quotation/myQuotation.model';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { HttpResponse } from '@angular/common/http';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { UserService } from 'src/app/shared/services/user/user.service';


@Component({
  selector: 'app-dynamic-component',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatIconModule,
    MatSelectModule,
    MatButtonModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    MatRadioModule,
    MatNativeDateModule,
    WizardComponent,
    MatTabsModule,
    FormsModule,
    TranslateModule,
    MatSlideToggleModule,
    FieldComponent,
    TableComponent,
  ],
  templateUrl: './dynamic-component.component.html',
  styleUrls: ['./dynamic-component.component.scss'],
})
export class DynamicComponentComponent implements OnInit, OnDestroy {
  @Input() isViewing: boolean = false;
  @Input() isInternal: boolean = true;
  @Input() formComplete: any = {};
  @Input() IdStateByStage = 0;
  @Input() idTaskStateEditing = 0;
  @Input() idTaskEditing = 0;
  @Input() isClientProcedure: boolean = false;
  @Output() saveSuccess = new EventEmitter<string>();
  isClientPortal: boolean = false;
  @Input() idFormModule: any[] = [];
  @Input() formulasCalculated: any[] = [];

  listFile: Array<FileModel> = [];
  listFielId: any = [];

  data: any = [];
  steps: string[] = [];
  currentStep: number = 0;
  idProductModule: number = 0;
  idProductModuleInserted: number = 0;
  idStateModule: number = 0;
  listPeople: any[] = [];
  idTaskState: number = 0;
  listIdCustomer: number[] = [];
  dynamicState: DynamicState = DynamicState.WithoutLoading;
  idTableModule: number = 0;
  //observationInput: string = '';
  idBusinessByCountry: number = 0;

  estructMyQuotationTable: BehaviorSubject<BodyTableModel[]> = new BehaviorSubject<BodyTableModel[]>([]);

  getTranslatedTable() {
    this._translateService.get([
        'ModulesDynamic.DocumentName',
        'ModulesDynamic.TypeFile',
        'ModulesDynamic.Required',
        'ModulesDynamic.DownloadFormat',
        'ModulesDynamic.Load',
        'ModulesDynamic.State',
        'ModulesDynamic.DownloadDocument',
        'ModulesDynamic.Delete',
    ]).subscribe((translations) => {
        const tableData: BodyTableModel[] = [
          {
            columnLabel: translations['ModulesDynamic.DocumentName'],
            columnValue: 'vName',
          },
          {
            columnLabel: translations['ModulesDynamic.TypeFile'],
            columnValue: 'vExtension',
          },
          {
            columnLabel: translations['ModulesDynamic.Required'],
            columnValue: 'bRequired',
            functionValue: (item: any) => this.ShowBasicValue(item.bRequired),
          },
          {
            columnLabel: translations['ModulesDynamic.DownloadFormat'],
            columnValue: 'download',
            columnIcon: 'download',
            hidenIcon: (item: any) => this.showIcon(item),
          },
          {
            columnLabel: translations['ModulesDynamic.Load'],
            columnValue: 'load',
            columnIcon: 'upload',
            hidenIcon: (item: any) => this.showIconUploadDocument(),
          },
          {
            columnLabel: translations['ModulesDynamic.State'],
            columnValue: 'pkIIdTableModule',
          functionValue: (item: any) => this.changeStatusValue(item, this.dynamicState),
          },
          {
            columnLabel: translations['ModulesDynamic.DownloadDocument'],
            columnValue: 'downloadDocument',
            columnIcon: 'download',
            hidenIcon: (item: any) => this.showIconDownloadDoc(item),
          },
          {
            columnLabel: translations['ModulesDynamic.Delete'],
            columnValue: 'Eliminar',
            columnIcon: 'delete',
            hidenIcon: (item: any) => this.showIconDeleteDoc(item),
          },
        ];

        this.estructMyQuotationTable.next(tableData);
      });
  }

  //Se agrega esta variable para evitar error por falta de instanciar un form en el componente field.
  public dynamicFormGroup: FormGroup = this.formBuilder.group({});

  quoteSubscription!: Subscription;

  constructor(
    private formBuilder: FormBuilder,
    private _moduleService: ModuleService,
    private _transactionService: TransactionService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    private _activatedRoute: ActivatedRoute,
    private _fileSerive: FileService,
    private _store: Store<AppState>,
    private _settingService: SettingService,
    private _location: Location,
    private _customRouter: CustomRouterService,
    private _fileService: FileService,
    private _utilsService: UtilsService,
    private _userService: UserService,
    private _router: Router
  ) { }

  getColumnClass(iAmountColumns: number, bExternalHidden: boolean): any {
    const colClass = !bExternalHidden ? `col-md-${12 / iAmountColumns}` : '';
    return {
      [colClass]: !bExternalHidden,
      'mt-3 mb-3': !bExternalHidden,
      'hidden-field': bExternalHidden
    };
  }

  getColumnClassShort(iAmountColumns: number, bExternalHidden: boolean): any {
    const colClass = !bExternalHidden ? `col-${12 / iAmountColumns}` : '';
    return {
      [colClass]: !bExternalHidden,
      'hidden-field': bExternalHidden
    };
  }

  async ngOnInit(): Promise<void> {
    this.getTranslatedTable();
    this.isClientPortal = (await this._userService.getUserIdSesion()) == 0
    this.getDataBusinnesByCountry();
    this._store.dispatch(resetState());
    if (this.isInternal) {
      this.getRouterName();
    } else {
      if (this.formComplete !== undefined && this.formComplete.length > 0) {
        this.data = this.formComplete;
        this.idStateModule = this.IdStateByStage;
        this.steps = this.getNameProgressBar(this.data);
        const formGroupFields = this.getFormControlsFields();
        this.dynamicFormGroup = new FormGroup(formGroupFields);
        //this.dynamicFormGroup.addControl('observationInput', this.formBuilder.control('', [Validators.required, Validators.maxLength(1000)]));
        if (this.isViewing) {
          this.getTaskState(this.idTaskStateEditing);
        }
      }
    }

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.getTranslatedTable();
    });
  }

  getRouterName() {
    this._activatedRoute.params.subscribe((params: any) => {
      this.idProductModule = params.idProduct;
      if (this.idProductModule)
        this.getCompleteFormByStage(this.idProductModule);
    });

    this._customRouter.selectedPM$.subscribe(({ productModule }) => {
      this.idProductModuleInserted =  Number(productModule);
      if(this.idProductModuleInserted > 0){
        this.isClientPortal = true
        this.idProductModule = this.idProductModuleInserted
        this.getCompleteFormByStage(this.idProductModule);
      }
    });
    if(this.idFormModule[0].iIdForm > 0){
      this.idProductModuleInserted =  Number(this.idFormModule[0].iIdForm);
      this.getCompleteFormByStage(this.idProductModuleInserted);
    }
  }

  async getDataBusinnesByCountry() {
    let dataSettingInit = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = dataSettingInit?.idBusinessByCountry || this._customRouter.getIdbusinessByCountry();
  }

  /**
   * Lifecycle hook that is called when the component is destroyed.
   *
   * This method checks if there is an active subscription to `companyConfigSubscription`.
   * If the subscription exists, it unsubscribes to prevent memory leaks.
   *
   * @memberof YourComponentName
   */
  ngOnDestroy(): void {
    if (this.quoteSubscription) {
      this.quoteSubscription.unsubscribe();
    }
  }

  next(event: string) { }

  back(event: string) { }

  currentStepChange(event: number) {
    this.currentStep = event;
  }

  goToBack() {
    this._location.back();
  }

  changeStatusValue(item: any, typeDynamic: number) {
    var _item = this.dynamicFormGroup.get(
      'documents_' + item.pkIIdTableModule
    )?.value;
    if (this.idTaskEditing) {
      if (_item) {
        return (
          '<h5><span class="badge bg-success">' +
          this._translateService.instant('ModulesDynamic.Loaded') +
          '</span></h5>'
        );
      }
    }
    if (item && item.state != undefined) {
      if (item.state === 0) {
        return (
          '<h5><span class="badge bg-success">' +
          this._translateService.instant('ModulesDynamic.Loaded') +
          '</span></h5>'
        );
      } else if (item.state === 1) {
        return (
          '<h5><span class="badge bg-warning">' +
          this._translateService.instant('ModulesDynamic.WithoutLoading') +
          '</span></h5>'
        );
      } else {
        return (
          '<h5><span class="badge bg-warning">' +
          this._translateService.instant('ModulesDynamic.FileError') +
          '</span></h5>'
        );
      }
    } else {
      return (
        '<h5><span class="badge  bg-warning">' +
        this._translateService.instant('ModulesDynamic.WithoutLoading') +
        '</span></h5>'
      );
    }
  }

  getCompleteFormByStage(idProductModule: number) {
    this._moduleService
      .GetFirstWorkFlowByProductModule(idProductModule, this.isClientPortal)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this.data = resp.result.json;
            if (this.data && this.data?.length > 0) {
              const uSectionFields = this.data[0].progressBarModules[0].tabModules[0].sectionModules[0].fieldModules;

              const fechaActualFieldWrapper = uSectionFields.find((f:any) => f.bIsGetdate === true);

              if (fechaActualFieldWrapper) {
                const originalField = fechaActualFieldWrapper;

                const newField = JSON.parse(JSON.stringify(originalField));

                newField.pkIIdFieldModule = 99999;
                newField.vNameField = "Fecha de cotización";
                newField.vNameFieldDb = "FechadecotizacionEstandarDB";
                newField.bIsGetdate = false;
                uSectionFields.push(newField);
              }
            }
            this.idStateModule = resp.result.fkIIdStateModule;
            this.steps = this.getNameProgressBar(this.data);
            const formGroupFields = this.getFormControlsFields();
            this.dynamicFormGroup = new FormGroup(formGroupFields);
          }
        }
      });
  }

  private getFormControlsFields() {
    //Generate keys of the  formBuilder dinamically
    const form = this.data[0];
    const formGroupFields: any = {};

    if (form.tableData) {
      for (const table of form.tableData) {
        if (table.columnTable[0].pkIIdColumnTableModule != null) {
          formGroupFields['table' + '_' + table.vName] = new FormArray([
            this.generateRow(table),
          ]);
        }
      }
    }

    const processTableData = (data: any[]) => {
      for (const item of data) {
        if (item.tableData) {
          for (const table of item.tableData) {
            if (table.columnTable[0].pkIIdColumnTableModule != null) {
              formGroupFields['table' + '_' + table.vName] = new FormArray([
                this.generateRow(table),
              ]);
            }
          }
        }
        if (item.fieldModules) {
          for (const field of item.fieldModules) {
            const validators = this.addValidator(field);
            formGroupFields[field.pkIIdFieldModule + '_' + field.vNameFieldDb] =
              new FormControl(null, validators);
          }
        }
        if (item.tabModules) {
          processTableData(item.tabModules);
        }
        if (item.sectionModules) {
          processTableData(item.sectionModules);
        }
      }
    };

    processTableData(form.progressBarModules);
    if (form.documentTable != undefined) {
      for (const column of form.documentTable) {
        formGroupFields['documents' + '_' + column.pkIIdTableModule] =
          new FormControl(null, null);
      }
    }
    return formGroupFields;
  }

  removeRecord(index: number) {
    this.data.splice(index, 1);
  }

  generateRow(fields: any): FormGroup {
    const formGroup = new FormGroup({});

    for (const column of fields.columnTable) {
      formGroup.addControl(
        column.pkIIdFieldModule + '_' + column.fieldModules.vNameFieldDb,
        new FormControl(null, null)
      );
    }
    return formGroup;
  }

  reviewStore() {
    this.quoteSubscription = this._store.select('quote').subscribe((state) => {
      this.listPeople = state.people;
      this.listFile = state.files;
    });
  }

  addRow(table: any) {
    const rowsArray = this.dynamicFormGroup.get(
      'table_' + table.vName
    ) as FormArray;
    rowsArray.push(this.generateRow(table));
  }
  deleteRowForTable(table: any, rowIndex: number): void {
    const rows = this.numberRows(table);
    if (rows.length > 1) {
      rows.removeAt(rowIndex);
    }
  }
  onFileUploadForTable(event: Event, row: any) {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      const file = input.files[0];
      // Aquí puedes manejar el archivo subido, por ejemplo, guardarlo en la fila correspondiente
    }
  }
  downloadFileForTable(row: any) { }

  numberRows(table: any) {
    return this.dynamicFormGroup.get('table_' + table.vName) as FormArray;
  }

  private addValidator(field: any): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    // Agregar la validación de campo requerido si bRequired es true
    if (field.bRequired) {
      validators.push(Validators.required);
    }

    // Agregar la validación de minLength si iMinLength es mayor que 0
    if (field.iMinLength > 0) {
      validators.push(Validators.minLength(field.iMinLength));
    }

    // Agregar la validación de maxLength si iMaxLength es mayor que 0
    if (field.iMaxLength > 0) {
      validators.push(Validators.maxLength(field.iMaxLength));
    }
    return validators;
  }

  // Función para obtener los valores de la key "vName" dentro de "progressBarModules"
  getNameProgressBar(data: any): string[] {
    const steps: string[] = [];

    // Itera sobre cada objeto en el array
    data.forEach((objeto: any) => {
      // Verifica si existe la propiedad "progressBarModules"
      if (
        objeto.progressBarModules &&
        Array.isArray(objeto.progressBarModules)
      ) {
        // Itera sobre cada objeto dentro de "progressBarModules"
        objeto.progressBarModules.forEach((progressBar: any) => {
          // Verifica si existe la propiedad "vName" y agrega el valor al array de nombres
          if (progressBar.vName) {
            steps.push(progressBar.vName);
          }
        });
      }
    });
    return steps;
  }

  async getIdUserSession(): Promise<number> {
    let dataSetting = await this._settingService.getDataSettingInit();
    let userIdSession: number = dataSetting?.idUser || this._userService.getUserIdSesion();

    return userIdSession;
  }

  async saveTask() {
    this.dynamicFormGroup.markAllAsTouched();
    if (this.dynamicFormGroup.valid) {
      this.reviewStore();
      const formData = new FormData();

      formData.append(
        'vTextCaptured',
        JSON.stringify({
          ...this.dynamicFormGroup?.getRawValue()
          // Observation: this.observationInput,
        })
      );
      formData.append(
        'fkIIdRequestingUser',
        (await this.getIdUserSession()).toString()
      );
      formData.append('fkIIdStateModule', this.idStateModule.toString());
      formData.append(
        'fkIIdBusinessCountry',
        this.idBusinessByCountry.toString()
      );

      if (!this.isInternal && this.idTaskEditing != 0) {
        //register.fkIIdTask = this.idTaskEditing
        formData.append('fkIIdTask', this.idTaskEditing.toString());
      }
      if (this.listFile.length > 0) {
        const filesArray: FormData[] = [];
        const observables = this.listFile.map((item, index) => {
          const formDataFile = new FormData();

          // Agregar cada archivo individualmente al formData
          for (let i = 0; i < item.files.length; i++) {
            const fileObject = item.files[i];
            if (fileObject) {
              formData.append(
                `Files[${index}].Files`,
                fileObject,
                fileObject.name
              );
            }
          }
          formData.append(`Files[${index}].FieldName`, item.nameField);
        });
      }

      this._transactionService
        .saveTask(formData, this.isClientPortal)
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            } else {
              const arrayPeople = [this.listPeople];
              if (this.listPeople != null && arrayPeople.length > 0) {
                this.idTaskState = resp.result.item2;
                this.savePeople();
              }
              if (this.isClientPortal)
              {
                this._msgSvc.messageSuccess(
                  this._translateService.instant('ModulesDynamic.DataSaved'),
                  resp.message + ' ' + resp.result.item1
                );
              }
              this._msgSvc.messageSuccess(
                this._translateService.instant('ModulesDynamic.TaskSave'),
                resp.message + ' ' + resp.result.item1
              );
              if(this._customRouter.isClientPortal()){
                setTimeout(() => {
                  this._customRouter.navigate(['home']).then(() =>{
                    window.location.reload();
                  });
                }, 2000);
              }
              else{
                setTimeout(() => {
                  //Keep in the same location  current/{idTask}/{idTaskState}/{stageByState}
                  const currentPath = this._location.path()
                  var newUrl = currentPath
                  if(this.idTaskEditing == 0){
                     newUrl = newUrl + '/'+resp.result.item1+'/'+resp.result.item2+'/'+this.idStateModule
                  }
                  else{
                    //Reemplazar cuando se esta visualizando tarea existente
                    const parts = currentPath.split('/'); // Divide por slash
                    const newValues = [resp.result.item1, resp.result.item2, this.idStateModule];
                    const elementsDelete=parts.length == 13 ? -4 : -3
                    const updatedParts = parts.slice(0, elementsDelete).concat(newValues);
                    newUrl=updatedParts.join('/');
                  }
                  
                  this._router.navigateByUrl(newUrl);
                  
                }, 2000);
              }
              if (!this.isClientProcedure){
                if(this.idFormModule[0].iIdForm > 0){}
                else{
                  this._location.back();
                }
              } else {
                this.saveSuccess.emit(resp.message);
              }
            }
          }
        });
    }
  }

  generateLabel(table: any): BodyTableModel[] {
    let elements: BodyTableModel[] = [];
    for (let column of table.columnTable) {
      elements.push({
        columnLabel: column.bName,
        columnValue: column.pkIIdFieldModule + '_' + column.bName,
      });
    }
    if (table.bDelete) {
      elements.push({
        columnLabel: this._translateService.instant('ModulesDynamic.Delete'),
        columnValue: 'delete',
        columnIcon: 'delete',
      });
    }
    if (table.bDownload) {
      elements.push({
        columnLabel: this._translateService.instant('ModulesDynamic.Download'),
        columnValue: 'download',
        columnIcon: 'download',
      });
    }
    if (table.bUpload) {
      elements.push({
        columnLabel: this._translateService.instant('ModulesDynamic.Load'),
        columnValue: 'upload',
        columnIcon: 'upload',
      });
    }
    return elements;
  }

  ShowBasicValue(required: boolean): string {
    return required
      ? this._translateService.instant('ModulesDynamic.Yes')
      : this._translateService.instant('ModulesDynamic.Not');
  }

  controllerDocuments(event: IconEventClickModel) {
    switch (event.column) {
      case 'load':
        const guid = crypto.randomUUID();
        const input = document.createElement('input');
        // Verificar si se permiten múltiples archivos
        if (event.value.bIsMultipleFiles) {
          input.multiple = true; // Permitir subir múltiples archivos
        }
        // Obtener las extensiones permitidas desde la propiedad vExtension
        const allowedExtensions = event.value.vExtension
          ? event.value.vExtension
              .split(',')
              .map((ext: string) => '.' + ext.trim().toLowerCase())
          : [];
        input.type = 'file';
        //Asignanmos las extensiones permitidas al input construido de tipo file para que solo muestre archivos de ese tipo de extensión.
        input.accept = allowedExtensions.join(',');
        input.addEventListener('change', (fileChangeEvent: any) => {
          try {
            this.idTableModule = event.value.pkIIdTableModule;
            const inputElement = fileChangeEvent.target as HTMLInputElement;
            const files = inputElement.files;
            if (files && files.length > 0) {
              // Limpiamos el file anterior para evitar acumular varios file si el usuario agrega un nuevo archivo sobre el mismo row.
              this._store.dispatch(
                deleteFile({
                  nameField: `documents_${event.value.pkIIdTableModule}`,
                })
              );
              this.dynamicFormGroup
                .get('documents_' + event.value.pkIIdTableModule)
                ?.setValue(null);
              //Se agrega el estado al evento de la tabla que pone los estados dinamicamente para las respectivas validaciones.
              event.value.state = DynamicState.WithoutLoading;

              let fileName: Array<string> = [];
              for (let i = 0; i < files.length; i++) {
                fileName.push(`${guid}-` + files[i].name);
                const fileExtension =
                  '.' + files[i].name.split('.').pop()?.toLowerCase();
                if (
                  fileExtension &&
                  allowedExtensions.includes(fileExtension)
                ) {
                  // Crear una copia inmutable de cada objeto de archivo para asegurar inmutabilidad
                  const copiedFiles: File[] = Array.from(files).map(
                    (file) =>
                      new File([file], `${guid}-` + file.name, {
                        type: file.type,
                      })
                  );

                  // Guardar los nombres y los archivos copiados en el Store para guardarlos en el componente padre.
                  const file: FileModel = {
                    files: copiedFiles,
                    nameField: 'documents_' + this.idTableModule,
                  };
                  const exists = this.listFile.some(element => element.nameField === file.nameField);
                  if(!exists){
                    this._store.dispatch(addFile({ file: file }));
                  }
                  this.reviewStore();
                  //Se agrega el estado al evento de la tabla que pone los estados dinamicamente para las respectivas validaciones.
                  event.value.state = DynamicState.Load;
                  this.dynamicFormGroup
                    .get('documents_' + this.idTableModule)
                    ?.setValue(fileName);
                } else {
                  this._store.dispatch(
                    deleteFile({ nameField: `${guid}-` + files[i].name })
                  );
                  this.dynamicFormGroup
                    .get('documents_' + this.idTableModule)
                    ?.setValue(null);
                  //Se agrega el estado al evento de la tabla que pone los estados dinamicamente para las respectivas validaciones.
                  event.value.state = DynamicState.FileError;
                }
              }

            } else event.value.state = DynamicState.WithoutLoading;
          } catch (e) {
            //Se agrega el estado al evento de la tabla que pone los estados dinamicamente para las respectivas validaciones.
            event.value.state = DynamicState.FileError;
          }
        });
        input.click();
        break;
      case 'download':
        if (event.value.bDownload) {
          this.GetFileByIdList([event.value.fkIIdUploadFile]);
        }
        break;
      case 'downloadDocument':
        if (this.idTaskStateEditing > 0) {
          this.getQuoteFileByIdTaskState(
            this.idTaskStateEditing,
            this.dynamicFormGroup.get(
              `documents_${event.value.pkIIdTableModule}`
            )?.value
          );
        } else {
          this.reviewStore();
          const filesOfRow = this.listFile.find(
            (f) => f.nameField.indexOf(event.value.pkIIdTableModule) !== -1
          );
          this._utilsService.processAndDownloadFiles(
            this.findFilesByNames(
              [filesOfRow!],
              this.dynamicFormGroup.get(
                `documents_${event.value.pkIIdTableModule}`
              )?.value
            )
          );
        }
        break;
      case 'Eliminar':
        this._store.dispatch(
          deleteFile({ nameField: `documents_${event.value.pkIIdTableModule}` })
        );
        this.dynamicFormGroup
          .get('documents_' + event.value.pkIIdTableModule)
          ?.setValue(null);
        //Se agrega el estado al evento de la tabla que pone los estados dinamicamente para las respectivas validaciones.
        event.value.state = DynamicState.WithoutLoading;
        break;
      default:
        break;
    }
  }

  GetFileByIdList(idFiles: Array<number>) {
    this._fileService
      .GetFileByIdList(idFiles)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: any) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            // Intenta obtener el nombre del archivo desde la cabecera Content-Disposition
            const fileName = resp.headers.get('X-File-Name') || 'file.zip';
            // Especifica el tipo MIME del archivo según el tipo que se espera
            const mimeType = 'application/octet-stream';
            const blob = new Blob([resp.body], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = fileName;
            link.href = url;
            link.click();
            window.URL.revokeObjectURL(url);
          }
        }
      });
  }

  savePeople() {
    var register = Object.values(this.listPeople);

    if (register.length !== 0) {
      const updatedRegister = register.map((person) => {
        let updatedPerson = { ...person };

        if (updatedPerson.dBirthDate) {
          const parsedDate = this.parseAndFormatDate(updatedPerson.dBirthDate);
          if (parsedDate) {
            updatedPerson.dBirthDate = parsedDate;
          }
        }

        return updatedPerson;
      });
      this._transactionService
        .createPeopleDinamically(updatedRegister)
        .pipe(
          catchError((error) => {
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this.listIdCustomer = resp.result;
              this.saveQuotesCustomer();
            }
          }
        });
    }
  }

  private parseAndFormatDate(inputDate: string): string | null {
    if (!inputDate) {
      return null;
    }

    const yyyyMMddRegex = /^\d{4}-\d{2}-\d{2}$/;
    const ddMMyyyyRegex = /^\d{2}-\d{2}-\d{4}$/;
    const mmDDyyyyRegex = /^\d{2}-\d{2}-\d{4}$/;

    let date: Date | null = null;

    if (yyyyMMddRegex.test(inputDate)) {
      date = new Date(inputDate);
    } else if (ddMMyyyyRegex.test(inputDate)) {
      const [day, month, year] = inputDate.split('-').map(Number);
      date = new Date(year, month - 1, day);
    } else if (mmDDyyyyRegex.test(inputDate)) {
      const [month, day, year] = inputDate.split('-').map(Number);
      date = new Date(year, month - 1, day);
    }

    if (date && !isNaN(date.getTime())) {
      const formattedYear = date.getFullYear();
      const formattedMonth = (date.getMonth() + 1).toString().padStart(2, '0');
      const formattedDay = date.getDate().toString().padStart(2, '0');

      return `${formattedYear}-${formattedMonth}-${formattedDay}`;
    }

    return null;
  }

  saveQuotesCustomer() {
    var register = {
      pkIIdCustomerQuotes: 0,
      fkIIdCustomer: this.listIdCustomer,
      fkIIdQuotes: 0,
      fkIIdTaskState: this.idTaskState,
    };
    this._transactionService
      .saveQuotesCustomer(register)
      .pipe(
        catchError((error) => {
          // this._msgSvc.messageWaring(
          //   this._translateService.instant('Warning'),
          //   error.error.message
          // );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
          }
        }
      });
  }

  get formValid(): boolean {
    return this.dynamicFormGroup.valid;
  }

  getTaskState(idTaskState: number) {
    this._transactionService
      .getTaskState(idTaskState)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            var jsonTask = JSON.parse(resp.result.vTextCaptured);
            if(resp.result.vTextCaptured.includes('table_')){
              this.checkAndGenerateRows(jsonTask,this.data[0]);
            }
            this.dynamicFormGroup.patchValue(jsonTask);
          }
        }
      });
  }

  showIcon(item: any): boolean {
    return !item.bDownload;
  }

  //Obtiene el id o los id file asociados a un archivo de una tarea en un estado.
  getQuoteFileByIdTaskState(idTaskState: number, fileName: string[]) {
    this._transactionService
      .getQuoteFileByIdTaskState(idTaskState, fileName)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.listFielId = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.listFielId = resp.result;
            if (this.listFielId.length > 0) {
              this.GetFileByIdList(this.listFielId);
            }
          }
        }
      });
  }

  //Obtiene el evento de los campos de tipo cargue de documento de fiel-componente.
  getdownloadDocumentEvent(event: string[]) {
    if (this.idTaskStateEditing > 0) {
      this.getQuoteFileByIdTaskState(this.idTaskStateEditing, event);
    } else {
      this.reviewStore();
      this._utilsService.processAndDownloadFiles(
        this.findFilesByNames(this.listFile, event)
      );
    }
  }

  //Función que busca un documento en el array de documentos por medio de un nombre.
  findFilesByNames(fileModels: FileModel[], names: string[]): File[] {
    // Flatten the array of FileModel objects into a single array of File objects
    const allFiles = fileModels.flatMap((fileModel) => fileModel.files);
    // Filter the flattened array to find files with names that match the given names
    return allFiles.filter((file) => names.includes(file.name));
  }

  //Función que determina si se muestra o no el icono de descargar en la tabla de cargue de documentos.
  showIconDownloadDoc(item: any): boolean {
    if (
      this.dynamicFormGroup.get('documents_' + item.pkIIdTableModule)?.value
    ) {
      return false;
    } else {
      return true;
    }
  }
  //Función que determina si se muestra o no el icono de eliminar en la tabla de cargue de documentos.
  showIconDeleteDoc(item: any): boolean {
    if (this.idTaskStateEditing) {
      return true;
    }
    if (
      this.dynamicFormGroup.get('documents_' + item.pkIIdTableModule)?.value
    ) {
      return false;
    } else {
      return true;
    }
  }

  //Función que determina si se muestra o no el botón de subir documentos en la tabla, cargue de docuemntos.
  showIconUploadDocument(): boolean {
    return this.isViewing;
  }

  findTableInDynamicForm(formDynamic: any, tableName: string): any {
    // Recorre dinámicamente las estructuras de progreso, tabs y secciones para encontrar la tabla
    const processTableData = (data: any[]): any => {
      for (const item of data) {
        if (item.tableData) {
          const table = item.tableData.find(
            (tbl: any) => tbl.vName.toLowerCase() === tableName.toLowerCase()
          );
          if (table) {
            return table; // Si se encuentra la tabla, devolver el objeto
          }
        }
        if (item.tabModules) {
          const foundInTab = processTableData(item.tabModules);
          if (foundInTab) {
            return foundInTab;
          }
        }
        if (item.sectionModules) {
          const foundInSection = processTableData(item.sectionModules);
          if (foundInSection) {
            return foundInSection;
          }
        }
      }
      return null; // Si no se encuentra nada, devolver null
    };

    return processTableData(formDynamic.progressBarModules || []);
  }

  checkAndGenerateRows(taskResult: any, formDynamic: any) {
    // Paso 1: Iterar sobre todas las claves de la tarea para encontrar claves que empiecen con "table_"
    Object.keys(taskResult).forEach((key) => {
      if (key.startsWith('table_')) {
        const tableData = taskResult[key]; // Obtener los datos de la tabla desde taskResult
        if (Array.isArray(tableData) && tableData.length > 1) {
          const tableName = key.replace('table_', ''); // Extraer el nombre de la tabla
          const tableDefinition = this.findTableInDynamicForm(formDynamic, tableName);

          if (tableDefinition) {
            // Generar filas dinámicamente según la cantidad de datos en la tabla
            for (let i = 1; i < tableData.length; i++) {
              this.addRow(tableDefinition); // Generar solo las filas adicionales
            }
          } else {
            console.log(`La tabla "${tableName}" no está definida en formDynamic.`);
          }
        }
      }
    });
  }

  //Función que filtra los campos activos de un array de campos.
  getActiveFields(fields: any[]): any[] {
    console.log("fields", fields);
    return fields.filter((f) => f.bActive !== false);
  }
}

















