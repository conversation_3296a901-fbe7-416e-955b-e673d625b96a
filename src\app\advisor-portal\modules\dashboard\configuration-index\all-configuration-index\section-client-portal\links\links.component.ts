import { CommonModule } from "@angular/common";
import { ChangeDetectorRef, Component, Input, TemplateRef, ViewChild } from "@angular/core";
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { MatInputModule } from "@angular/material/input";
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { combineLatest, Subscription } from "rxjs";
import { Modal2Component } from "src/app/shared/components/modal2/modal2.component";
import { TableComponent } from "src/app/shared/components/table/table.component";
import { PreventionSqlInjectorDirective } from "src/app/shared/directives/shared/prevention-sql-injector.directive";
import { BodyTableModel, IconEventClickModel } from "src/app/shared/models/table";
import { CopyrigthService } from "src/app/shared/services/copyrigth/copyrigth.service";
import { LinkService } from "src/app/shared/services/links/links.service";
import { MessageService } from "src/app/shared/services/message/message.service";
import { SocialSocialMediaService } from "src/app/shared/services/social-media-links/social-media-links.service";
import { UtilsService } from "src/app/shared/services/utils/utils.service";


@Component({
  selector: 'app-links',
  standalone: true,
  imports: [
    MatInputModule,
    Modal2Component,
    PreventionSqlInjectorDirective,
    TranslateModule,
    ReactiveFormsModule,
    CommonModule,
    TableComponent,
    MatButtonModule
  ],
  templateUrl: './links.component.html',
  styleUrls: ['./links.component.scss']
})
export class LinksComponent {

  @Input() idBusinessByCountry: number = 0;
  @ViewChild('createEditLink') createEditLink?: TemplateRef<any>;
  @ViewChild('createEditSocialMedia') createEditSocialMedia?: TemplateRef<any>;
  _currentModal: MatDialogRef<any> | null = null;

  formSubs?: Subscription;
  
  dataTableLinks: any[] = [];
  dataTableSocialMedia: any[] = [];

  indexLinkToModify: number | null = null;
  indexSocialMediaToModify: number | null = null;
  estructTableLinks: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('SectionClientPortal.LinkName'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('SectionClientPortal.LinkUrl'),
      columnValue: 'vlink',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyLink',
      columnIcon: 'edit',
    },
  ];

  estructTableSocialMedia: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('SectionClientPortal.SocialMediaName'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('SectionClientPortal.SocialMediaUrl'),
      columnValue: 'vlink',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifySocialMedia',
      columnIcon: 'edit',
    },
  ];

  constructor(
    public utilsSvc: UtilsService,
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    private _modalDialog: MatDialog,
    private _cdr: ChangeDetectorRef,
    private _linkService: LinkService,
    private _socialMediaService: SocialSocialMediaService,
    private _copyrigthService: CopyrigthService,
    private _messageService: MessageService,
  ){

  }
  ngOnInit(): void {
    this._loadLinks()
    this._loadSocialMedia()
    this._loadCopyrigth()
  }

  formLink: FormGroup = this._fb.group({
    vName: [null, Validators.required],
    vlink: [null, Validators.required],
  })

  formSocialMedia: FormGroup = this._fb.group({
    vName: [null, Validators.required],
    vlink: [null, Validators.required],
  })

  formCopyrigth: FormGroup = this._fb.group({
    vText: [null, Validators.required]
  })

  private _loadCopyrigth(){
    this._copyrigthService.getCopyrigths(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error)
          {
            this.formCopyrigth.patchValue({
              vText: resp.result[0].vName
            })
          }
      })
    })
  }

  public save(){
    const socialMedia = this.dataTableSocialMedia.map(element => {
      return {
        FkIIdBusinessByCountry: this.idBusinessByCountry,
        vName: element.vName,
        vlink: element.vlink,
        bActive: true
      }
    })
    const link = this.dataTableLinks.map(element => {
      return {
        FkIIdBusinessByCountry: this.idBusinessByCountry,
        vName: element.vName,
        vlink: element.vlink,
        bActive: true
      }
    })
    combineLatest([
      this._socialMediaService.modifySocialMedia(this.idBusinessByCountry, socialMedia),
      this._linkService.modifyLink(this.idBusinessByCountry, link),
      this._copyrigthService.modifyCopyrigth( {
        FkIdBusinessByCountry: this.idBusinessByCountry,
        vName: this.formCopyrigth.get('vText')?.value
      })
    ])
    .subscribe({
      next:(resp => {
        this._messageService.messageSuccess(
          this._translateService.instant('SectionClientPortal.Confirmation'),
          ''
        );
      })
    })
  }

  private _loadSocialMedia(){
    this._socialMediaService.getSocialMedias(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error)
          {
            this.dataTableSocialMedia = [...resp.result];
            this.indexSocialMediaToModify = 0;
            this._cdr.detectChanges()
          }
      })
    })
    
  }
  private _loadLinks(){
    this._linkService.getLinks(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error)
          {
            this.dataTableLinks = [...resp.result];
            this.indexLinkToModify = 0;
            this._cdr.detectChanges()
          }
      })
    })
  }

  openModal(modal: 'link' | 'socialMedia'){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    let modalToOpen = null;
    if (modal === 'link')
      modalToOpen = this.createEditLink!
    else
      modalToOpen = this.createEditSocialMedia!
    this._currentModal = this._modalDialog.open(modalToOpen, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formLink.reset();
        this.formSocialMedia.reset();
      })
    })
  }

  saveLink(){
    if (this.formLink.invalid)
      return this.formLink.markAllAsTouched()

    let data = [...this.dataTableLinks];
    if (this.indexLinkToModify === null)
      data.push(this.formLink.value)
    else
      data[this.indexLinkToModify] = {
        vName: this.formLink.get('vName')?.value,
        vlink: this.formLink.get('vlink')?.value,
        FkIIdBusinessByCountry: this.idBusinessByCountry
    }
    this.dataTableLinks = data
    this._cdr.detectChanges();
    this.closeModal()
  }

  saveSocialMedia(){
    if (this.formSocialMedia.invalid)
      return this.formSocialMedia.markAllAsTouched()

    let data = [...this.dataTableSocialMedia];
    if (this.indexSocialMediaToModify === null)
      data.push(this.formSocialMedia.value)
    else
      data[this.indexSocialMediaToModify] = {
        vName: this.formSocialMedia.get('vName')?.value,
        vlink: this.formSocialMedia.get('vlink')?.value,
        FkIIdBusinessByCountry: this.idBusinessByCountry
    }
    this.dataTableSocialMedia = data
    this._cdr.detectChanges();
    this.closeModal()
  }

  closeModal() {
    if (this._currentModal) {
      this._currentModal.close();
      this._currentModal = null;
      this.formSubs?.unsubscribe();
      this.indexLinkToModify = null
    }
  }


  controller(evt: IconEventClickModel) {
    if (evt.column === 'modifyLink')
      {
        this.indexLinkToModify = evt.index
        this.formLink.patchValue({
          vName: evt.value.vName,
          vlink: evt.value.vlink
        })
        this.openModal('link')
      }
    if (evt.column === 'modifySocialMedia')
      {
        this.indexSocialMediaToModify = evt.index
        this.formSocialMedia.patchValue({
          vName: evt.value.vName,
          vlink: evt.value.vlink
        })
        this.openModal('socialMedia')
      }
    
  }
}
