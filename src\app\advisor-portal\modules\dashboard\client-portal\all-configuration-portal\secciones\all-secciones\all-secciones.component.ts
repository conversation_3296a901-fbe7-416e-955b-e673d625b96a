import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormArray } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { PestanaService } from 'src/app/shared/services/pestana/pestana.service';
import { Component, OnInit, Input, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { lastValueFrom, Subscription, of, catchError } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule } from '@angular/forms';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MatTableDataSource } from '@angular/material/table';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { BenefitService } from 'src/app/shared/services/benefits/benefit.service';
import { BenefitModel } from 'src/app/shared/models/business/benefit.model';
import { ConvertJSONToExcel } from 'src/app/shared/models/file';
import { NewsService } from 'src/app/shared/services/news/news.service';
import { QuestionService } from 'src/app/shared/services/question/question.service';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';




@Component({
  selector: 'app-all-secciones',
  standalone: true,
  imports: [
    MatInputModule,
    MatSelectModule,
    TableComponent,
    TranslateModule,
    Modal2Component,
    ReactiveFormsModule,
    MatRadioModule,
    CommonModule,
    MatButtonModule,
    PreventionSqlInjectorDirective,
    MatSlideToggleModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    MatCheckboxModule,
    ValidationInputFileDirective
  ],
  templateUrl: './all-secciones.component.html',
  styleUrls: ['./all-secciones.component.scss']
})
export class AllSeccionesComponent implements OnInit {
  @Input() idBusinessByCountry: number = 0;
  selectedPestanaButton: any = null;
  selectedSubPestanaButton: any = null;
  subpestanaButton: any[] = [];
  disable: boolean = true;
  pestana: any[] = [];
  Allpestana: any[] = [];
  subpestana: any[] = [];
  selectedPestana: any = null;
  selectedSubPestana: any = null;
  nameModule: string = '-';
  dataTableSecciones: any[] = [];
  dataTableSeccionesProducto: any[] = [];
  dataTableSeccionesLanding: any[] = [];
  dataTableBeneficios: any[] = [];
  dataTableCarruselImg: any[] = [];
  dataTableTarjetas: any[] = [];
  dataTableCarruselProducto: any[] = [];
  dataTableNews: any[] = [];
  dataTableQuestion: any[] = [];
  dataTableProduct: any[] = [];
  dataTableCoverage: any[] = [];
  Listproducts: any[] = [];
  selectedService: any = null;
  showQuotation: boolean = true;
  ListCoverage: any[] = [];
  allowedExtensions: string[] = ['png', 'jpeg', 'svg'];
  maxFileSizeMB: number = 20;

  pkSeccionesToModify: number = 0;
  orderseccionesList: any[] = [];
  _currentModal: MatDialogRef<any>[] = [];
  @ViewChild('createEditSeccion') createEditSeccion?: TemplateRef<any>;
  @ViewChild('createEditSeccionProduct') createEditSeccionProduct?: TemplateRef<any>;
  @ViewChild('createEditIMG') createEditIMG?: TemplateRef<any>;
  @ViewChild('createEditTarjet') createEditTarjet?: TemplateRef<any>;
  @ViewChild('createEditProduct') createEditProduct?: TemplateRef<any>;
  @ViewChild('createEditNotice') createEditNotice?: TemplateRef<any>;
  @ViewChild('createEditQuestion') createEditQuestion?: TemplateRef<any>;
  @ViewChild('createEditaddProduct') createEditaddProduct?: TemplateRef<any>;
  colorprimary: string = '#000000';
  colorsecundary: string = '#000000';
  colortertiary: string = '#000000';
  background: string = '#000000';
  colortittle: string = '#ffffff';
  colortext: string = '#cccccc';
  Bbackground: string = '#ffffff';
  Bcolortittle: string = '#000000';


  isValid: boolean = true;
  isDragOver = false;
  isDragOverMovil = false;

  showBtnDelete: boolean = false;
  showBtnDeleteMovil: boolean = false;

  fileName: string = '';
  fileNameMovil: string = ''
  formSubs?: Subscription;
  opaco: boolean = true;
  pkSeccionToModify: number = 0;
  pkSeccionProductoToModify: number = 0;
  pkSeccionCardsToModify : number = 0;
  pkSeccionCorosuelProductoToModify : number = 0;
  pkSeccionNewToModify : number = 0;
  pkSeccionQuestionToModify : number = 0;
  pkSeccionProductToModify: number = 0;
  tipoSeccion: number = 0;
  orderSeccion: any[] = [];
  orderTarjetas: any[] = [1];
  typeNew: any[] = [];
  infoSection: any[] = [];
  pkSeccionesLandingToModify: number = 0;
  orderbeneficio: any[] = [1];
  orderimagen: any[] = [1];
  orderproducto: any[] = [1];
  orderCoverage: any[] = [1];
  form: any[] = [];
  ordernoticias: any[] = [1];
  orderpreguntas: any[] = [1];
  orderCarruselproducto: any[] = [1];

 
  editingItem: any = null; //Variable to know if we are editing a temporary benefit
  isEditingRealBenefit: boolean = false; // Saber si es un beneficio NO temporal








  constructor(
    private _pestanaService: PestanaService,
    private _cdr: ChangeDetectorRef,
    public utilsSvc: UtilsService,
    private _translateService: TranslateService,
    private _messageService: MessageService,
    private _fb: FormBuilder,
    private _modalDialog: MatDialog,
    private _businessService: BusinessService,
    private _modulesSettingService: ModulesSettingService,
    private _spinnerService: SpinnerService,
    private _productServices: ProductService,
    private _fileService: FileService,
    private _moduleService: ModuleService,
    private _benefitService: BenefitService,
    private _newsService: NewsService,
    private _questionService: QuestionService,


    


  ) {
  }
  formLandingSecciones: FormGroup = this._fb.group({
    bActiveSeccion: [true],
    iTypeSeccion: [null],
    iOrderSeccion: [null],
    iTypeCards: [null],
    FkIIdSizeNew: [null],
    vTitle: [null],
    vText: [null],
    vFileNamePC: [null],
    vExternalPDFPC: [null],
    vExternalPDFMovil: [null],
    vFileNameMovil: [null],
    iPosition: [null],
    bActiveButton: [null],
    vTextButton: [null],
    vLinkRedirection: [null],
    iIdForm: [null],
    vCategory: [null],
    iIdProduct: [null],
    vPrimaryColor: ['#000000'],
    vSecondaryColor: ['#ffffff'],
    vTertiaryColor: ['#cccccc'],
    bActiveKnowMore: [null],
    bActiveQuote: [null],
    IdSeccion: [null],
    idOrder: [null],
    fkIdUploadedFileMovil: [null],
    fkIdUploadedFilePC: [null],
    // temporal
    vBenefic: [[]],
    Carousel: [[]],
    Cards: [[]],
    ProductCarousel: [[]],
    News: [[]],
    Questions: [[]],
    product: [[]],

    bActiveButtonImg: [false],
    iActionButton: [null],
    iPestana: [null],
    iSubPestana: [null],
    iOpenLink: [null],
    bActiveTexts: [false],
    VQuaternaryColor:['#ffffff'],
    VQuinaryColor:['#000000'],

  })
  formCarruselImg: FormGroup = this._fb.group({
    iOrder: [false],
    vFileNamePC: [null],
    vFileNameMovil: [null],
    vExternalPDFPC: [null],
    vExternalPDFMovil: [null],
    pkIIdCarouselLanding: [0],
    fkIIdBusinessCountry: [0]
  })
  formTarjetas: FormGroup = this._fb.group({
    iOrder: [false],
    vDescription: [null],
    vCategory: [null],
    vText: [null],
    vFileNamePC: [null],
    vExternalPDFPC: [null],
    bActiveButton: [null],
    vTextButton: [null],
    vLinkRedirection: [null],
    iOpenLink: [null],
    pkIIdCardLanding: [0],
    fkIIdBusinessCountry: [0]
  })
  formCarruselProducto: FormGroup = this._fb.group({
    iOrder: [false],
    idSeccion: [null],
    iIdProduct: [null],
    vTitle: [null],
    vText: [null],
    vFileNamePC: [null],
    vExternalPDFPC: [null],
    pkIIdProductCorouselLanding: [0],
    fkIIdBusinessCountry: [0]
  })
  formNews: FormGroup = this._fb.group({
    IOrder: [null],
    VCategory: [null],
    VTitle: [null],
    VDescription: [null],
    BActiveButton: [false],
    VTextButton: [null],
    VLinkRedirection: [null],
    DEndDate: [null],
    vFileNamePC: [null],
    vExternalPDFPC: [null],
    iOpenLink: [null],
    pkIIdNews: [0]
  })
  formQuestion: FormGroup = this._fb.group({
    iOrder: [false],
    vQuestion: [null],
    vAnswer: [null],
    pkIIdQuestion: [0]
  })
  formProduct: FormGroup = this._fb.group({
    iOrder: [null],
    vCategory: [null],
    fkIIdProduct: [null],
    vTitle: [null],
    vText: [null],
    vFileNamePC: [null],
    vExternalPDFPC: [null],
    vPrimaryColor: [null],
    vSecondaryColor: [null],
    vTertiaryColor: [null],
    bActiveKnowMore: [false],
    bActiveQuote: [false],
    iPosition: [null],
    vLinkRedirection: [null],
    vCoverage: [[]],
    iOpenLink: [null],
    pkIIdProductLanding: [0]

  })

  formCoverage: FormGroup = this._fb.group({
    iOrder: [false],
    iCoverage: [null],
    vText: [null],
    pkIdProductBenefict: [0]
  })
  selectedCoverage: { id: number; description: string } | null = null;
  formSecciones: FormGroup = this._fb.group({
    bActive: [false],
    iTypeSeccion: [null, Validators.required],
    iOrderSeccion: [null, Validators.required],

  })
  TypeSeccion = [
    { id: 1, name: 'Banners' },
    { id: 2, name: 'Beneficios' },
    { id: 3, name: 'Botón' },
    { id: 4, name: 'Carrusel imágenes' },
    { id: 5, name: 'Carrusel tarjetas' },
    { id: 6, name: 'Carrusel producto' },
    { id: 7, name: 'Formulario' },
    { id: 8, name: 'Imágenes estáticas' },
    { id: 9, name: 'Noticias' },
    { id: 10, name: 'Preguntas frecuentes' },
    { id: 11, name: 'Productos' }
  ];
  TypeCards = [
    { id: 1, name: 'Blanca' },
    { id: 2, name: 'Color' },
  ]
  formSeccion: FormGroup = this._fb.group({

  })
  // Estructura tabla principal de secciones
  estructTableSecciones: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrderSeccion',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.typeseccion'),
      columnValue: 'iTypeSeccion',
      functionValue: (item: any) => this.getTypeSeccionName(item.iTypeSeccion),
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.pestna'),
      columnValue: 'idMenu',
      functionValue: (item: any) => this.getNamePestana(item.idMenu),
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.subpestana'),
      columnValue: 'idMenus',
      functionValue: (item: any) => this.getNameSubPestana(item.idMenu),
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Active'),
      columnValue: 'bActiveSeccion',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifySeccion',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteSeccion',
      columnIcon: 'delete',
    },
  ];
  // Estructura tabla de beneficios
  estructTBenefic: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.benefic'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyBenefic',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];
  dataSource = new MatTableDataSource<any>();
  // Estructura tabla de beneficios
  estructTCarruselIMG: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.FileName'),
      columnValue: 'nameImgPC',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyCarouselImage',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteImg',
      columnIcon: 'delete',
    },
  ];
  // Estructura tabla de Tarjetas
  estructTTarjetas: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.tittle'),
      columnValue: 'vText',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyCardImage',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteCard',
      columnIcon: 'delete',
    },
  ];
  // Estructura tabla de carrusel producto
  estructTCarruselProducto: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.producto'),
      columnValue: 'vTitle',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyProductCarousel',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteCProduct',
      columnIcon: 'delete',
    },
  ];
  // Estructura tabla de preguntas
  estructTPreguntas: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.question'),
      columnValue: 'vQuestion',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyQuestion',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteQuestion',
      columnIcon: 'delete',
    },
  ];
  // Estructura tabla de coberturas
  estructTProduct: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.coverage'),
      columnValue: 'vText',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteSub',
      columnIcon: 'delete',
    },
  ];
  // Estructura tabla de coberturas
  estructTableProductos: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.coverage'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteCobertura',
      columnIcon: 'delete',
    },
  ];
  estructTNews: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.tittle'),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyNews',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteNews',
      columnIcon: 'delete',
    },
  ];
  estructTSecciones: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('configurationClientPortal.Orden'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('configurationClientPortal.product'),
      columnValue: 'vCategory',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyProduct',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteProducto',
      columnIcon: 'delete',
    },
  ];
  formPestana: FormGroup = this._fb.group({
    bActive: [false],
    iAssociated: [0, Validators.required],
    vDescription: ['', Validators.required],
    iOrder: [0, Validators.required],
    FkIIdProduct: [0],
    FkIIdModule: [0],
    vLink: ['', Validators.pattern('https?://.+')],
    IdMenu: [0]
  })
  ngOnInit(): void {
    this._loadPestanas()
    this.TypeNews()
    this.GetProductsByBusinessCountryHome(this.idBusinessByCountry);
    this.getForms(this.idBusinessByCountry)
  }

  TypeNews() {
    this._businessService.GetSizeNews().subscribe({
      next: (resp => {
        if (!resp.error) {
          this.typeNew = [...resp.result]
        }
      })
    })

  }
  onPestanaChange(value: { id: number; description: string }): void {
    this.selectedPestana = value.id;
    this.nameModule = value.description
    this.loadSeccionesLanding(this.selectedPestana);
    this.loadSubPestana(this.selectedPestana)
    this._cdr.detectChanges();
  }
  onSubPestanaChange(value: any): void {
    this.selectedSubPestana = value;
    this.loadSeccionesLanding(this.selectedSubPestana);
    this._cdr.detectChanges();

  }

  private _loadPestanas() {
    this._pestanaService.getPestana(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.Allpestana = [...resp.result]
          this.pestana = resp.result.filter((item: any) => item.iParent === null);
          this.pestana.sort((a: any, b: any) => {
            return (a.iOrder === '' || a.iOrder === 0 ? -1 : 1) - (b.iOrder === '' || b.iOrder === 0 ? -1 : 1);
          });
        }
      })
    })
  }
  loadSubPestana(selectedValue: number) {
    this._pestanaService.getSubPestana(selectedValue).subscribe({
      next: (resp) => {
        if (!resp.error) {
          this.subpestana = [...resp.result];
          this.opaco = false
          this.loadSecciones(this.subpestana)
        } else {
          console.error('Error en la respuesta del servidor:', resp.message);
        }
      },
      error: (err) => {
        console.error('Error en la solicitud HTTP:', err);
      },
      complete: () => {
      },
    });
  }

  loadSecciones(selectedValue: any) {
    this._pestanaService.getSubPestana(selectedValue).subscribe({
      next: (resp) => {
        if (!resp.error) {
          this.dataTableSecciones = [...resp.result];
          if (resp.result.length > 0) {
            this.orderseccionesList = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderseccionesList = this.utilsSvc.generarArrayOrderList(1);
          }
          this.pkSeccionesToModify = 0;
          this._cdr.detectChanges();

        } else {
          console.error('Error en la respuesta del servidor:', resp.message);
          this.dataTableSecciones = [];
        }
      },
      error: (err) => {
        console.error('Error en la solicitud HTTP:', err);
        this.dataTableSecciones = [];
      }
    });
  }

  controller(evt: IconEventClickModel) {
    if (evt.column === 'deleteSeccion') {
      this._businessService.deleteSeccion(evt.value.iSeccion).subscribe({
        next: (response) => {
          if (!response.error) {
            this.loadSeccionesLanding(this.selectedPestana);
            this.loadSubPestana(this.selectedPestana)
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        },
      })
    }
    if (evt.column === 'modifySeccion') {
      const pestanaEncontrada = this.pestana.find(pt => pt.pkIIdMenu === evt.value.iMenuButton);
      let tabF = 0;
      let tabS = 0;
      if (pestanaEncontrada) {
        this.onPestanaChangeButton(evt.value.iMenuButton);
        tabF = evt.value.iMenuButton;
      } else {
        const pestanaPadre = this.Allpestana.find(pt => pt.pkIIdMenu === evt.value.iMenuButton);
        if (pestanaPadre) {
          this.onPestanaChangeButton(pestanaPadre.iParent);
          this.onSubPestanaChangeButton(evt.value.iMenuButton);
          tabF = pestanaPadre.iParent;
          tabS = evt.value.iMenuButton
        }
      }
      this.pkSeccionToModify = evt.value.idSeccion;
      this.background = evt.value.vPrimaryColor;
      this.colortittle = evt.value.vSecondaryColor;
      this.colortext = evt.value.vTertiaryColor;
      this.Bbackground = evt.value.vQuaternaryColor;
      this.Bcolortittle = evt.value.vQuinaryColor;
      this.formLandingSecciones.patchValue({
        bActiveSeccion: evt.value.bActiveSeccion,
        iTypeSeccion: evt.value.iTypeSeccion,
        iOrderSeccion: evt.value.iOrderSeccion,
        iTypeCards: evt.value.iTypeCards,
        iSizeNew: evt.value.iSizeNew,
        vTitle: evt.value.vTitle,
        vText: evt.value.vText,
        vFileNamePC: evt.value.vFileNamePC,
        vExternalPDFPC: evt.value.vExternalPDFPC,
        vExternalPDFMovil: evt.value.vExternalPDFMovil,
        vFileNameMovil: evt.value.vFileNameMovil,
        iPosition: evt.value.iPosition,
        bActiveButton: evt.value.bActiveButton,
        vTextButton: evt.value.vTextButton,
        vLinkRedirection: evt.value.vLinkRedirection,
        iIdForm: evt.value.iIdForm,
        vCategory: evt.value.vCategory,
        iIdProduct: evt.value.iIdProduct,
        vPrimaryColor: evt.value.vPrimaryColor,
        vSecondaryColor: evt.value.vSecondaryColor,
        vTertiaryColor: evt.value.vTertiaryColor,
        bActiveKnowMore: evt.value.bActiveKnowMore,
        bActiveQuote: evt.value.bActiveQuote,
        fkIdUploadedFileMovil: evt.value.fkIdUploadedFileMovil,
        fkIdUploadedFilePC: evt.value.fkIdUploadedFilePC,
        FkIIdSizeNew: evt.value.fkIIdSizeNew,
        bActiveButtonImg: evt.value.bActiveButtonImg,
        iActionButton: evt.value.iActionButton,
        iPestana: tabF,
        iSubPestana: tabS,
        iOpenLink: evt.value.iOpenLink,
        bActiveTexts: evt.value.bActiveTexts,
        VQuaternaryColor:evt.value.vQuaternaryColor,
        VQuinaryColor:evt.value.vQuinaryColor,


      })


      if (evt.value.fkIdUploadedFileMovil != null || evt.value.fkIdUploadedFileMovil > 0) {
        this.showBtnDeleteMovil = true;
        this._fileService.getImageGeneral(evt.value.fkIdUploadedFileMovil).then((name) => {
          this.fileNameMovil = name.vFileName;
        });
      }
      if (evt.value.fkIdUploadedFilePC != null || evt.value.fkIdUploadedFilePC > 0) {
        this.showBtnDelete = true;
        this._fileService.getImageGeneral(evt.value.fkIdUploadedFilePC).then((name) => {
          this.fileName = name.vFileName;
        });
      }


      this.onTypeSeccionChange(evt.value.iTypeSeccion)

      this.openModalSeccion()
    }
  }


  openModalSeccion() {
    this.dataTableQuestion = [];
    this.dataTableBeneficios = [];
    this.dataTableTarjetas = [];
    this.dataTableCarruselImg = [];
    this.dataTableProduct = [];
    this.dataTableNews = [];
    this.dataTableCarruselProducto = [];
    this.showBtnDeleteMovil = false;
    this.showBtnDelete = false;
    this.fileNameMovil = '';
    this.fileName = '';


    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    const modalRef = this._modalDialog.open(this.createEditSeccion!, sizeConfiguration);
    this._currentModal.push(modalRef);
    this.formSubs = modalRef.beforeClosed().subscribe({
      next: (_ => {
        this.background = '#000000';
        this.colortittle = '#ffffff';
        this.colortext = '#cccccc';
        this.Bbackground = '#ffffff';
        this.Bcolortittle = '#000000';
        this.formLandingSecciones.reset();
      })
    })
  }
  async saveSeccion() {
    if (this.formLandingSecciones.invalid)
      return this.formLandingSecciones.markAllAsTouched()
    let data = {
      ...this.formLandingSecciones.value,
      IdBusinessCountry: this.idBusinessByCountry,
      idMenu: this.selectedSubPestana != null ? this.selectedSubPestana : this.selectedPestana,
      iMenuButton: this.selectedSubPestanaButton != null ? this.selectedSubPestanaButton : this.selectedPestanaButton,
      vBenefic: this.dataTableBeneficios,
      Carousel: this.dataTableCarruselImg,
      Cards: this.dataTableTarjetas,
      ProductCarousel: this.dataTableCarruselProducto,
      News: this.dataTableNews,
      Questions: this.dataTableQuestion,
      product: Array.isArray(this.dataTableProduct)
        ? this.dataTableProduct.map(prod => ({
          ...prod,
          vCoverage: Array.isArray(prod.vCoverage) ? prod.vCoverage : []
        }))
        : []
    }
    if(data.iTypeSeccion == 1){
      data.vPrimaryColor = data.vPrimaryColor ? data.vPrimaryColor : this.background;
      data.vSecondaryColor = data.vSecondaryColor ? data.vSecondaryColor : this.colortittle;
      data.vTertiaryColor = data.vTertiaryColor ? data.vTertiaryColor : this.colortext;
      data.VQuaternaryColor = data.VQuaternaryColor ? data.VQuaternaryColor : this.Bbackground;
      data.VQuinaryColor = data.VQuinaryColor ? data.VQuinaryColor :this.Bcolortittle;
    }
    if (this.pkSeccionToModify === 0)
      this._businessService.createSeccion(data).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadPestanas()

            this.loadSeccionesLanding(this.selectedPestana);
            this.loadSubPestana(this.selectedPestana)
            this._messageService.messageSuccessSimple(
              this._translateService.instant('DataSavedSuccessfully'),
            );
            this.closeModal();
          } else {
            this.closeModal();
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    else {
      data.IdSeccion = this.pkSeccionToModify
      this._businessService.createSeccion(data).subscribe({
        next: (response) => {
          if (!response.error) {
            this.loadSeccionesLanding(this.selectedPestana);
            this.loadSubPestana(this.selectedPestana)

            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.CoverageModifiedSucessfully')
            );
            this.closeModal();

          }
        },
        error: (error) => {
          this.closeModal()
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    }
    this.closeModal()
  }
  closeModal() {
    if (this._currentModal.length > 0) {
      const modalRef = this._currentModal.pop();
      if (modalRef) {
        modalRef.close();
      }
    }
  }
  onFileDropped(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
    const file = event.dataTransfer?.files[0];
    if (file) {
      const allowedTypes = ['image/png', 'image/jpeg', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        this._messageService.messageError('Solo se permiten archivos PNG, JPG, SVG.');
        return;
      } else if (file.size > 20 * 1024 * 1024) {
        this._messageService.messageError(
          'El archivo excede el tamaño máximo (20 MB).'
        );
        return;
      }

      // Convierte el archivo PDF a Base64
      this.convertPdfToBase64(file)
        .then((base64: string) => {
          this.formLandingSecciones.get('vExternalPDFPC')?.setValue(base64);
          this.formCarruselImg.get('vExternalPDFPC')?.setValue(base64);
          this.formTarjetas.get('vExternalPDFPC')?.setValue(base64);
          this.formCarruselProducto.get('vExternalPDFPC')?.setValue(base64);
          this.formProduct.get('vExternalPDFPC')?.setValue(base64);
          this.formNews.get('vExternalPDFPC')?.setValue(base64);





        })
        .catch((error) => {
          this._messageService.messageError(error);
        })
        .finally(() => {
          this.showBtnDelete = true;
          this.fileName = file.name;  // Asigna el nombre del archivo
          this.formLandingSecciones.get('vFileNamePC')?.setValue(file.name);
          this.formLandingSecciones.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessByCountry);
          this.formCarruselImg.get('vFileNamePC')?.setValue(file.name);
          this.formCarruselImg.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessByCountry);
          this.formTarjetas.get('vFileNamePC')?.setValue(file.name);
          this.formTarjetas.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessByCountry);
          this.formCarruselProducto.get('vFileNamePC')?.setValue(file.name);
          this.formCarruselProducto.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessByCountry);
          this.formProduct.get('vFileNamePC')?.setValue(file.name);
          this.formNews.get('vFileNamePC')?.setValue(file.name);


        });
    }

  }
  onFileDroppedMovil(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOverMovil = false;
    const file = event.dataTransfer?.files[0];
    if (file) {
      const allowedTypes = ['image/png', 'image/jpeg', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        this._messageService.messageError('Solo se permiten archivos PNG, JPG, SVG.');
        return;
      } else if (file.size > 20 * 1024 * 1024) {
        this._messageService.messageError(
          'El archivo excede el tamaño máximo (20 MB).'
        );
        return;
      }

      this.convertPdfToBase64(file)
        .then((base64: string) => {
          this.formLandingSecciones.get('vExternalPDFMovil')?.setValue(base64);
          this.formCarruselImg.get('vExternalPDFMovil')?.setValue(base64);

        })
        .catch((error) => {
          this._messageService.messageError(error);
        })
        .finally(() => {
          this.showBtnDeleteMovil = true;
          this.fileNameMovil = file.name;  // Asigna el nombre del archivo
          this.formLandingSecciones.get('vFileNameMovil')?.setValue(file.name);
          this.formLandingSecciones.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessByCountry);
          this.formCarruselImg.get('vFileNameMovil')?.setValue(file.name);
          this.formCarruselImg.get('fkIIdBusinessByCountry')?.setValue(this.idBusinessByCountry);

        });
    }

  }
  convertPdfToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String);
      };
      reader.onerror = (error) => {
        this._messageService.messageError('Error al leer el archivo: ' + error);
      };
      reader.readAsDataURL(file);
    });
  }
  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }
  onDragOverMovil(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOverMovil = true;
  }
  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      const allowedTypes = ['image/png', 'image/jpeg', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        return this._messageService.messageError('Solo se permiten archivos PNG, JPG, SVG.');
      } else if (file.size > 20 * 1024 * 1024) {
        // Límite de 20 MB
        return this._messageService.messageError(
          'La imagen excede el peso máximo(100MB)'
        );
      } else {
        const files: FileList = event.target.files;
        const name = event.target.files[0].name;
        // Convierte el archivo PDF a Base64
        this.convertPdfToBase64(file)
          .then((base64: string) => {
            this.formLandingSecciones.get('vExternalPDFPC')?.setValue(base64);
            this.formCarruselImg.get('vExternalPDFPC')?.setValue(base64);
            this.formTarjetas.get('vExternalPDFPC')?.setValue(base64);
            this.formCarruselProducto.get('vExternalPDFPC')?.setValue(base64);
            this.formProduct.get('vExternalPDFPC')?.setValue(base64);
            this.formNews.get('vExternalPDFPC')?.setValue(base64);





          })
          .catch((error) => {
            this._messageService.messageError(error);
          })
          .finally(() => {
            this.showBtnDelete = true;
            this.fileName = name;
          });
        this.formLandingSecciones.get('vFileNamePC')?.setValue(name);
        this.formCarruselImg.get('vFileNamePC')?.setValue(name);
        this.formTarjetas.get('vFileNamePC')?.setValue(name);
        this.formCarruselProducto.get('vFileNamePC')?.setValue(name);
        this.formProduct.get('vFileNamePC')?.setValue(name);
        this.formNews.get('vFileNamePC')?.setValue(name);





        this.formLandingSecciones
          .get('fkIIdBusinessByCountry')
          ?.setValue(this.idBusinessByCountry);
      }
    }
  }
  onFileSelectedMovil(event: any): void {
    const input = event.target as HTMLInputElement;
    if (input.dataset['fileValid'] === 'false') return;

    const file: File = event.target.files[0];
    if (file) {
      const allowedTypes = ['image/png', 'image/jpeg', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        return this._messageService.messageError('Solo se permiten archivos PNG, JPG, SVG.');
      } else if (file.size > 20 * 1024 * 1024) {
        // Límite de 20 MB
        return this._messageService.messageError(
          'La imagen excede el peso máximo(100MB)'
        );
      } else {
        const files: FileList = event.target.files;
        const name = event.target.files[0].name;
        // Convierte el archivo PDF a Base64
        this.convertPdfToBase64(file)
          .then((base64: string) => {
            this.formLandingSecciones.get('vExternalPDFMovil')?.setValue(base64);
            this.formCarruselImg.get('vExternalPDFMovil')?.setValue(base64);

          })
          .catch((error) => {
            this._messageService.messageError(error);
          })
          .finally(() => {
            this.showBtnDeleteMovil = true;
            this.fileNameMovil = name;
          });
        this.formLandingSecciones.get('vFileNameMovil')?.setValue(name);
        this.formCarruselImg.get('vFileNameMovil')?.setValue(name);

        this.formLandingSecciones
          .get('fkIIdBusinessByCountry')
          ?.setValue(this.idBusinessByCountry);
      }
    }
  }
  openModalIMG() {
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    const modalRef = this._modalDialog.open(this.createEditIMG!, sizeConfiguration);
    this._currentModal.push(modalRef);
    this.formSubs = modalRef.beforeClosed().subscribe({
      next: (_ => {
        this.pkSeccionProductoToModify = 0;
        this.fileName = '';
        this.fileNameMovil = '';
        this.formCarruselImg.reset();
      })
    })
  }
  openModalTarjet() {
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    const modalRef = this._modalDialog.open(this.createEditTarjet!, sizeConfiguration);
    this._currentModal.push(modalRef);
    this.formSubs = modalRef.beforeClosed().subscribe({
      next: (_ => {
        this.pkSeccionCardsToModify = 0;
        this.fileName = '';
        this.fileNameMovil = '';
        this.formTarjetas.reset();
      })
    })
  }
  openModalProduct() {
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    const modalRef = this._modalDialog.open(this.createEditProduct!, sizeConfiguration);
    this._currentModal.push(modalRef);
    this.formSubs = modalRef.beforeClosed().subscribe({
      next: (_ => {
        this.pkSeccionCorosuelProductoToModify = 0;
        this.fileName = '';
        this.formPestana.reset();
      })
    })
  }
  openModalNotice() {
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    const modalRef = this._modalDialog.open(this.createEditNotice!, sizeConfiguration);
    this._currentModal.push(modalRef);
    this.formSubs = modalRef.beforeClosed().subscribe({
      next: (_ => {
        this.pkSeccionNewToModify = 0
        this.fileName = '';
        this.formNews.reset();
      })
    })
  }
  openModalQuestion() {
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    const modalRef = this._modalDialog.open(this.createEditQuestion!, sizeConfiguration);
    this._currentModal.push(modalRef);
    this.formSubs = modalRef.beforeClosed().subscribe({
      next: (_ => {
        this.pkSeccionQuestionToModify = 0;
        this.formQuestion.reset();
      })
    })
  }
  openModaladdProduct() {
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    const modalRef = this._modalDialog.open(this.createEditaddProduct!, sizeConfiguration);
    this._currentModal.push(modalRef);
    this.formSubs = modalRef.beforeClosed().subscribe({
      next: (_ => {
        this.fileName = '';
        this.pkSeccionProductToModify = 0;
        this.colortertiary = '';
        this.colorsecundary = '';
        this.colorprimary = '';
        this.formProduct.reset();
      })
    })
  }

  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const hexRegex = /^#[0-9A-Fa-f]{6}$/;
    this.isValid = hexRegex.test(input.value);
    this.colorprimary = input.value;
  }
  onInputChangeS(event: Event): void {
    const input = event.target as HTMLInputElement;
    const hexRegex = /^#[0-9A-Fa-f]{6}$/;
    this.isValid = hexRegex.test(input.value);
    this.colorsecundary = input.value;
  }
  onInputChangeT(event: Event): void {
    const input = event.target as HTMLInputElement;
    const hexRegex = /^#[0-9A-Fa-f]{6}$/;
    this.isValid = hexRegex.test(input.value);
    this.colortertiary = input.value;
  }
  onInputChangeBakground(event: Event): void {
    const input = event.target as HTMLInputElement;
    const hexRegex = /^#[0-9A-Fa-f]{6}$/;
    const value = input.value; 
    this.isValid = hexRegex.test(value);
    this.background = value;
    if (value === '' || value === null) {
      this.formLandingSecciones.get('vPrimaryColor')?.setValue('#000000', { emitEvent: false });
      this.background = '#000000';
    }
  }
  onInputChangeTittle(event: Event): void {
    const input = event.target as HTMLInputElement;
    const hexRegex = /^#[0-9A-Fa-f]{6}$/;
    const value = input.value; 
    this.isValid = hexRegex.test(value);
    this.colortittle = value;
    if (value === '' || value === null) {
      this.formLandingSecciones.get('vSecondaryColor')?.setValue('#ffffff', { emitEvent: false });
      this.colortittle = '#ffffff';
    }
  }
  onInputChangeText(event: Event): void {
    const input = event.target as HTMLInputElement;
    const hexRegex = /^#[0-9A-Fa-f]{6}$/;
    const value = input.value; 
    this.isValid = hexRegex.test(value);
    this.colortext = value;
    if (value === '' || value === null) {
      this.formLandingSecciones.get('vTertiaryColor')?.setValue('#cccccc', { emitEvent: false });
      this.colortext = '#cccccc';
    }
  }
  onInputChangeBBackground(event: Event): void {
    const input = event.target as HTMLInputElement;
    const hexRegex = /^#[0-9A-Fa-f]{6}$/;
    const value = input.value; 
    this.isValid = hexRegex.test(value);
    this.Bbackground = value;
    if (value === '' || value === null) {
      this.formLandingSecciones.get('VQuaternaryColor')?.setValue('#ffffff', { emitEvent: false });
      this.Bbackground = '#ffffff';
    }
  }
  onInputChangeBTittle(event: Event): void {
  const input = event.target as HTMLInputElement;
  const hexRegex = /^#[0-9A-Fa-f]{6}$/;
  const value = input.value;
  this.isValid = hexRegex.test(value);
  this.Bcolortittle = value;
  if (value === '' || value === null) {
    this.formLandingSecciones.get('VQuinaryColor')?.setValue('#000000', { emitEvent: false });
    this.Bcolortittle = '#000000';
  }
}

  onTypeSeccionChange(value: number): void {
    this.tipoSeccion = value;
    this.getInfoSeccion(this.tipoSeccion)
  }

  getInfoSeccion(value: number) {
    var menu = this.selectedSubPestana != null ? this.selectedSubPestana : this.selectedPestana;

    this._businessService.GetInfoSection(value, this.idBusinessByCountry, menu).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.infoSection = [...resp.result]
          if (resp.result.length > 0) {
            this.orderSeccion = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderSeccion = this.utilsSvc.generarArrayOrderList(1);
          }

          if (this.pkSeccionToModify !== 0) {
            this.loadTarjetas(this.pkSeccionToModify, this.idBusinessByCountry)
            this.GetQuestions(this.pkSeccionToModify, this.idBusinessByCountry)
            this.GetImages(this.pkSeccionToModify, this.idBusinessByCountry)
            this.GetProductCarousel(this.pkSeccionToModify, this.idBusinessByCountry)
            this.GetNews(this.pkSeccionToModify, this.idBusinessByCountry)
            this.GetBenefit(this.pkSeccionToModify)
            this.GetProduct(this.pkSeccionToModify, this.idBusinessByCountry)
          }
        }
      })
    })
  }
  loadSeccionesLanding(selectedValue: any) {
    this._businessService.GetInfoSection(0, this.idBusinessByCountry, selectedValue).subscribe({
      next: (resp) => {
        if (!resp.error) {
          this.dataTableSeccionesLanding = [...resp.result];
          this.pkSeccionesLandingToModify = 0;
          this._cdr.detectChanges();

        } else {
          console.error(resp.message);
          this.dataTableSeccionesLanding = [];
        }
      },
      error: (err) => {
        console.error('Error en la solicitud HTTP:', err);
        this.dataTableSeccionesLanding = [];
      }

    });
  }
  getTypeSeccionName(id: number): string {
    const type = this.TypeSeccion.find(item => item.id === id);
    return type ? type.name : '-';
  }
  getNamePestana(id: number): string {
    const type = this.pestana.find(item => item.pkIIdMenu === id);
    return type ? type.vDescription : '-';
  }
  getNameSubPestana(id: number): string {
    const type = this.subpestana.find(item => item.pkIIdMenu === id);
    return type ? type.vDescription : '-';
  }
  saveBenefic() {
    const vName = this.formLandingSecciones.get('vBenefic')?.value.trim();
    if (vName) {
      if (this.editingItem) {
        if (this.isEditingRealBenefit) {
          this.updateRealBenefit(this.editingItem, vName);
        }else{
          const index = this.dataTableBeneficios.findIndex(item => item.iOrder === this.editingItem.iOrder);
          if (index !== -1) {
            this.dataTableBeneficios[index].vName = vName;
          }
        }
        this.cancelEdit();
      }else{
        const iOrder = this.dataTableBeneficios.length + 1;
        this.dataTableBeneficios = [
          ...this.dataTableBeneficios,
          { iOrder, vName, isTemporary: true }
        ];
      }
      this.formLandingSecciones.get('vBenefic')?.reset();
    }
  }
  cancelEdit() {
    this.editingItem = null;
    this.isEditingRealBenefit = false;
    this.formLandingSecciones.get('vBenefic')?.reset();
  }
  updateRealBenefit(beneficio: any, newName: string) {
    const beneficioReducido: BenefitModel = {
      FkIIdBusinessByCountry: this.idBusinessByCountry,
      vName: newName
    };
    this._benefitService.modifyBenefit(beneficio.pkIIdBenefit, beneficioReducido).subscribe({
      next: (response) => {
        if (!response.error) {
          const index = this.dataTableBeneficios.findIndex(b => b.pkIIdBenefit === beneficio.pkIIdBenefit);
          if (index !== -1) {
            this.dataTableBeneficios[index].vName = newName;
          }
  
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            this._translateService.instant('SectionClientPortal.BenefitModifiedSucessfully')
          );
  
          // Salir del modo edición
          this.cancelEdit();
        }
      },
      error: (error) => {
        this._messageService.messageInfo(
          this._translateService.instant('ThereWasAError'),
          error
        );
      }
    });
  }

  saveCarruselIMG() {
    if (this.formCarruselImg.valid) {
      const formData = this.formCarruselImg.value;
      formData.editImagePC = formData.vExternalPDFPC != null;
      formData.editImageMovil = formData.vExternalPDFMovil != null;
      const isTemporal = !formData.pkIIdCarouselLanding || formData.pkIIdCarouselLanding === 0;
      if (!isTemporal) {    
          this._businessService.modifyImageCarousel(formData.pkIIdCarouselLanding,formData.fkIIdBusinessCountry, formData ).subscribe({
                  next: (res) => {
                    this._messageService.messageSuccess(
                      this._translateService.instant('DataSavedSuccessfully'),
                      this._translateService.instant('Modificado')
                    );
                    this.closeModal();
                    this.formCarruselImg.reset();
                    this.showBtnDeleteMovil = false;
                    this.showBtnDelete = false;
                  },
                  error: (err) => {
                    console.error("Error-->",err )
                    this._messageService.messageInfo(
                      this._translateService.instant('ThereWasAError'),
                      err
                    );
                  }
                });
      }else{
        const iOrder = formData.iOrder || this.dataTableCarruselImg.length + 1;
        const index = this.dataTableCarruselImg.findIndex(x => x.iOrder === iOrder && x.isTemporary);
        const newData = { ...formData, iOrder, isTemporary: true };
        if (index !== -1) {
          // modify temporary
          this.dataTableCarruselImg[index] = newData;
        } else {
          //  new 
          this.dataTableCarruselImg = [...this.dataTableCarruselImg, newData];
        }
        this.orderimagen = this.utilsSvc.generarArrayOrderList(
          this.dataTableCarruselImg.length + 1
        );
      }     
    }
    this.formCarruselImg?.reset();
    this.showBtnDeleteMovil = false;
    this.showBtnDelete = false
    this.closeModal()

  }
  saveTarjetas() {
    if (this.formTarjetas.valid) {
      const formData = this.formTarjetas.value;
      formData.editImage = formData.vExternalPDFPC != null;
      formData.Active = true
      formData.imageBase64 = formData.vExternalPDFPC
      formData.fileName = formData.vFileNamePC
      const isTemporal = !formData.pkIIdCardLanding || formData.pkIIdCardLanding === 0;
      if (!isTemporal) {  
        this._businessService.modifyCardslanding(formData.pkIIdCardLanding,formData.fkIIdBusinessCountry, formData ).subscribe({
          next: (res) => {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('Modificado')
            );
            this.closeModal();
            this.formCarruselImg.reset();
            this.showBtnDeleteMovil = false;
            this.showBtnDelete = false;
          },
          error: (err) => {
            console.error("Error-->",err )
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              err
            );
          }
        });  
      }else{
      const iOrder = this.dataTableTarjetas.length + 1;
        this.dataTableTarjetas = [
          ...this.dataTableTarjetas,
          { ...formData, iOrder, isTemporary: true },
        ];
        this.orderTarjetas = this.utilsSvc.generarArrayOrderList(
          this.dataTableTarjetas.length + 1
        );
      }       
    }
    this.formTarjetas.reset();
    this.showBtnDelete = false;
    this.closeModal()

  }
  saveCarruselProducto() {
    if (this.formCarruselProducto.valid) {
      const formData = this.formCarruselProducto.value;
      formData.imageBase64PC = formData.vExternalPDFPC
      formData.editImagePC = formData.vExternalPDFPC != null;
      const isTemporal = !formData.pkIIdProductCorouselLanding || formData.pkIIdProductCorouselLanding === 0;
      if (!isTemporal) {  
        this._businessService.modifyProductCarousellanding(formData.pkIIdProductCorouselLanding,this.idBusinessByCountry, formData ).subscribe({
          next: (res) => {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('Modificado')
            );
            this.closeModal();
            this.formCarruselImg.reset();
            this.showBtnDeleteMovil = false;
            this.showBtnDelete = false;
          },
          error: (err) => {
            console.error("Error-->",err )
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              err
            );
          }
        }); 
      }else{
        const iOrder = this.dataTableCarruselProducto.length + 1;
        this.dataTableCarruselProducto = [
          ...this.dataTableCarruselProducto,
          { ...formData, iOrder, isTemporary: true },
        ];
        this.orderCarruselproducto = this.utilsSvc.generarArrayOrderList(
          this.dataTableCarruselProducto.length + 1
        );
      } 
    }
    this.formCarruselProducto?.reset();
    this.showBtnDelete = false
    this.closeModal()
  }
  saveNews() {
    if (this.formNews.valid) {
      const formData = this.formNews.value;
      const first = this.formLandingSecciones.value
      const isTemporal = !formData.pkIIdNews || formData.pkIIdNews === 0;
      formData.FkIIdBusinessByCountry = this.idBusinessByCountry;
      formData.ImageBase64 = formData.vExternalPDFPC
      formData.FileName = formData.vFileNamePC
      formData.fkIIdSizeNew =  first.FkIIdSizeNew;

      if (!isTemporal) {
        this._newsService.updateNews(formData).subscribe({
          next: (res) => {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('Modificado')
            );
            this.closeModal();
            this.formCarruselImg.reset();
            this.showBtnDeleteMovil = false;
            this.showBtnDelete = false;
          },
          error: (err) => {
            console.error("Error-->",err )
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              err
            );
          }
        }); 
      }else{
        formData.FkIIdSectionBusiness = this.pkSeccionToModify
        if (formData.ImageBase64.includes('base64,')) {
          formData.ImageBase64 = formData.ImageBase64.split('base64,')[1];
        }
        if(this.pkSeccionToModify > 0){
          this._newsService.createNews(formData).subscribe({
            next: (res) => {
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('Modificado')
              );
              this.closeModal();
              this.formCarruselImg.reset();
              this.showBtnDeleteMovil = false;
              this.showBtnDelete = false;
            },
            error: (err) => {
              console.error("Error-->",err )
              this._messageService.messageInfo(
                this._translateService.instant('ThereWasAError'),
                err
              );
            }
          });
        }else{
          const iOrder = this.dataTableNews.length + 1;
          this.dataTableNews = [
            ...this.dataTableNews,
            { ...formData, iOrder, isTemporary: true },
          ];
          this.ordernoticias = this.utilsSvc.generarArrayOrderList(
            this.dataTableNews.length + 1
          );
        }
        

       
      }     
    }
    this.formNews?.reset();
    this.showBtnDelete = false
    this.closeModal()
  }
  saveQuestion() {
    if (this.formQuestion.valid) {
      const formData = this.formQuestion.value;
      const isTemporal = !formData.pkIIdQuestion || formData.pkIIdQuestion === 0;
      if (!isTemporal) {
        this._questionService.modifyQuestion(formData.pkIIdQuestion,formData ).subscribe({
          next: (res) => {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('Modificado')
            );
            this.closeModal();
            this.formCarruselImg.reset();
            this.showBtnDeleteMovil = false;
            this.showBtnDelete = false;
          },
          error: (err) => {
            console.error("Error-->",err )
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              err
            );
          }
        }); 
      }
      else{
        const iOrder = this.dataTableQuestion.length + 1;
        this.dataTableQuestion = [
          ...this.dataTableQuestion,
          { ...formData, iOrder, isTemporary: true },
        ];
        this.orderpreguntas = this.utilsSvc.generarArrayOrderList(
          this.dataTableQuestion.length + 1
        );
      }    
    }
    this.formQuestion?.reset();
    this.showBtnDelete = false
    this.closeModal()
  }
  saveProduct() {
    if (this.formProduct.valid) {
      this.formProduct.patchValue({
        vCoverage: this.dataTableCoverage
      });

      const formData = this.formProduct.value;
      formData.editImagePC = formData.vExternalPDFPC != null;
      formData.imageBase64PC = formData.vExternalPDFPC
      const isTemporal = !formData.pkIIdProductLanding || formData.pkIIdProductLanding === 0;
      if (!isTemporal) {
        this._businessService.modifyProductlanding(formData.pkIIdProductLanding, this.idBusinessByCountry, formData ).subscribe({
          next: (res) => {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('Modificado')
            );
            this.closeModal();
            this.formCarruselImg.reset();
            this.showBtnDeleteMovil = false;
            this.showBtnDelete = false;
          },
          error: (err) => {
            console.error("Error-->",err )
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              err
            );
          }
        }); 
      }else{
        const iOrder = this.dataTableProduct.length + 1;
        this.dataTableProduct = [
          ...this.dataTableProduct,
          { ...formData, iOrder, isTemporary: true },
        ];
        this.orderproducto = this.utilsSvc.generarArrayOrderList(
          this.dataTableProduct.length + 1
        );
      }

      
    }
    this.formProduct?.reset()
    this.showBtnDelete = false
    this.closeModal()
  }
  saveCoverage() {
    if (this.formCoverage.valid) {
      const formData = this.formCoverage.value;
      
      if (formData.iCoverage == null || formData.iCoverage == '' || formData.iCoverage == 0) {
        return;
      }

      const iOrder = this.dataTableCoverage.length + 1;
      this.dataTableCoverage = [
        ...this.dataTableCoverage,
        { ...formData, iOrder, isTemporary: true },
      ];
      this.orderCoverage = this.utilsSvc.generarArrayOrderList(
        this.dataTableCoverage.length + 1
      );

      this.formCoverage.reset();
    }
  }

  async GetProductsByBusinessCountryHome(empresa: number) {
    this._spinnerService.showChanges(true);
    await lastValueFrom(
      this._productServices.GetProductsByBusinessCountryHome(empresa)
    )
      .then((res) => {
        this.Listproducts = res.result;
      })
      .catch((r) => {
        this._spinnerService.showChanges(false);
      });

    this.Listproducts = this.Listproducts.map((r) => {
      if (r.vFilePath) {
        let arr = r.vFilePath.split('.');
        r.newSrc = 'data:image/' + arr[1] + ';base64,' + r.imageBase64;
      }
      return r;
    });

    if (this.Listproducts.length > 0) {
      this.selectproductService(this.Listproducts[0]);
    }

    this._spinnerService.showChanges(false);

    if (this.Listproducts.length > 0) {
      this.selectproductService(this.Listproducts[0]);
    }
  }
  selectproductService(service: any) {
    this.selectedService = service;
    this.showQuotation = false;
    setTimeout(() => {
      this.showQuotation = true;
    }, 1);
  }

  async loadTarjetas(value: number, idbusiness: number) {
    this._businessService.GetTarjet(value, idbusiness).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.dataTableTarjetas = [...resp.result]
          if (resp.result.length > 0) {
            this.orderTarjetas = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderTarjetas = this.utilsSvc.generarArrayOrderList(1);
          }
          this.getImagenesEditarCards(this.dataTableTarjetas)
        }
      })
    })
  }
  async GetBenefit(value: number) {
    this._businessService.GetBenefit(value).subscribe({
      next: (resp => {
        if (!resp.error) {
          const beneficios = resp.result || [];
          this.dataTableBeneficios = beneficios.map((item : any, index : number) => ({
            ...item,
            iOrder: index + 1
          }));
          if (resp.result.length > 0) {
            this.orderbeneficio = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderbeneficio = this.utilsSvc.generarArrayOrderList(1);
          }
        }
      })
    })
  }

  async GetImages(value: number, idbusiness: number) {
    this._businessService.GetImages(value, idbusiness).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.dataTableCarruselImg = [...resp.result]
          if (resp.result.length > 0) {
            this.orderimagen = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderimagen = this.utilsSvc.generarArrayOrderList(1);
          }
          this.getImagenesEditar(this.dataTableCarruselImg);
        }
      })
    })
  }
  async GetProductCarousel(value: number, idbusiness: number) {
    this._businessService.GetProductCarousel(value, idbusiness).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.dataTableCarruselProducto = [...resp.result]
          if (resp.result.length > 0) {
            this.orderCarruselproducto = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderCarruselproducto = this.utilsSvc.generarArrayOrderList(1);
          }
          this.getImagenesEditar(this.dataTableCarruselProducto);
        }
      })
    })
  }
  async GetNews(value: number, idbusiness: number) {
    this._businessService.GetNews(value, idbusiness).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.dataTableNews = [...resp.result]
          if (resp.result.length > 0) {
            this.ordernoticias = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.ordernoticias = this.utilsSvc.generarArrayOrderList(1);
          }
          this.getImagenesEditar(this.dataTableNews);

        }
      })
    })
  }
  async GetQuestions(value: number, idbusiness: number) {
    this._businessService.GetQuestions(value, idbusiness).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.dataTableQuestion = [...resp.result]
          if (resp.result.length > 0) {
            this.orderpreguntas = this.utilsSvc.generarArrayOrderList(
              resp.result.length + 1
            );
          } else {
            this.orderpreguntas = this.utilsSvc.generarArrayOrderList(1);
          }
        }
      })
    })
  }
  async GetProduct(value: number, idbusiness: number) {
    this._businessService.GetProducts(value, idbusiness).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.dataTableProduct = [...resp.result]
          this.getImagenesEditar(this.dataTableProduct);

          for (const product of this.dataTableProduct) {
            if (Array.isArray(product.beneficts) && product.beneficts.length > 0) {
              product.fkidCoverage = product.beneficts
                .map((b: { fkIIdCoverage: number }) => b.fkIIdCoverage)
                .filter((id: number) => id != null);
          
              if (product.fkidCoverage.length > 0) {
                this._productServices.GetCoverageById(product.fkidCoverage).subscribe({
                  next: (coverageResp: any) => {
                    if (!coverageResp.error) {
                      product.vCoverage = coverageResp.result.map((coverage: any) => {
                        const match = product.beneficts.find(
                          (b: any) => Number(b.fkIIdCoverage) === Number(coverage.iCoverage)
                        );
                        return {
                          ...coverage,
                          pkIIdProductBenefict: match?.pkIIdProductBenefict ?? null
                        };
                      });
                    } else {
                      product.vCoverage = [];
                    }
                  },
                  error: (err: any) => {
                    product.vCoverage = [];
                    console.error('Error en la petición de cobertura', err);
                  }
                });
              } else {
                product.vCoverage = [];
              }
            } else {
              product.vCoverage = [];
            }
          }
        }
      })
    })
  }
  async GetCoverage(value: number){
    const idProduct = this.formProduct.get('iIdProduct');
    this._productServices.getCoverage(value).subscribe({
      next: (resp =>{
        if(!resp.error){
          this.ListCoverage  = [...resp.result]
        }
      })
    })
  }
  onCoverage(value: any) {
    console.log('Selected Coverage:', value);
  
    this.formCoverage.patchValue({ 
      iCoverage: value.pkIIdCoverage,
      vText: value.vName
    }, { emitEvent: false });         
  }  

  compareCoverage(option: any, value: any): boolean {
    return option && value ? option.pkIIdCoverage === value : option === value;
  }  
  controllerModal(evt: IconEventClickModel) {
    if (evt.column === 'delete') {
      const beneficio = evt.value;
      if (beneficio.isTemporary) {
        this.dataTableBeneficios = this.dataTableBeneficios.filter(
          (b) => b.iOrder !== beneficio.iOrder
        );

      } else {
        this._businessService.deleteBenefit(evt.value.pkIIdBenefit).subscribe({
          next: (response) => {
            if (!response.error) {
              this.GetBenefit(this.pkSeccionToModify)
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
      }
    }
    if (evt.column === 'deleteImg') {
      const beneficio = evt.value;
      if (beneficio.isTemporary) {
        this.dataTableCarruselImg = this.dataTableCarruselImg.filter(
          (b) => b.iOrder !== beneficio.iOrder
        );

      } else {
        this._businessService.deleteCorouselIMG(evt.value.pkIIdCarouselLanding).subscribe({
          next: (response) => {
            if (!response.error) {
              this.GetImages(this.pkSeccionToModify, this.idBusinessByCountry)
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
      }
    }
    if (evt.column === 'deleteCard') {
      const beneficio = evt.value;
      if (beneficio.isTemporary) {
        this.dataTableTarjetas = this.dataTableTarjetas.filter(
          (b) => b.iOrder !== beneficio.iOrder
        );

      } else {
        this._businessService.deleteCorouselCard(evt.value.pkIIdCardsLanding).subscribe({
          next: (response) => {
            if (!response.error) {
              this.loadTarjetas(this.pkSeccionToModify, this.idBusinessByCountry);
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
      }
    }
    if (evt.column === 'deleteCProduct') {
      const beneficio = evt.value;
      if (beneficio.isTemporary) {
        this.dataTableCarruselProducto = this.dataTableCarruselProducto.filter(
          (b) => b.iOrder !== beneficio.iOrder
        );

      } else {
        this._businessService.deleteCorouselProduct(evt.value.pkIIdProductCorouselLanding).subscribe({
          next: (response) => {
            if (!response.error) {
              this.GetProductCarousel(this.pkSeccionToModify, this.idBusinessByCountry)
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
      }
    }
    if (evt.column === 'deleteNews') {
      const beneficio = evt.value;
      if (beneficio.isTemporary) {
        this.dataTableNews = this.dataTableNews.filter(
          (b) => b.iOrder !== beneficio.iOrder
        );

      } else {
        this._businessService.deleteNews(evt.value.pkIIdNew).subscribe({
          next: (response) => {
            if (!response.error) {
              this.loadSeccionesLanding(this.selectedPestana);
              this.loadSubPestana(this.selectedPestana)
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })

      }
    }
    if (evt.column === 'deleteQuestion') {
      const beneficio = evt.value;
      if (beneficio.isTemporary) {
        this.dataTableQuestion = this.dataTableQuestion.filter(
          (b) => b.iOrder !== beneficio.iOrder
        );

      } else {
        this._businessService.deleteQuestion(evt.value.pkIIdQuestion).subscribe({
          next: (response) => {
            if (!response.error) {
              this.GetQuestions(this.pkSeccionToModify, this.idBusinessByCountry)
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
      }
    }
    if (evt.column === 'deleteProducto') {
      const beneficio = evt.value;
      if (beneficio.isTemporary) {
        this.dataTableProduct = this.dataTableProduct.filter(
          (b) => b.iOrder !== beneficio.iOrder
        );

      } else {
        this._businessService.deleteProduct(evt.value.pkIIdProductLanding).subscribe({
          next: (response) => {
            if (!response.error) {
              this.GetProduct(this.pkSeccionToModify, this.idBusinessByCountry)
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
      }
    }
    if (evt.column === 'deleteCobertura') {
      const beneficio = evt.value;
      if (beneficio.isTemporary) {
        this.dataTableCoverage = this.dataTableCoverage.filter(
          (b) => b.iOrder !== beneficio.iOrder
        );
      }else {
        this._businessService.deletecoverage(evt.value.pkIdProductBenefict).subscribe({
          next: (response) => {
            if (!response.error) {
              this.GetProduct(this.pkSeccionToModify, this.idBusinessByCountry)
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
      }
    }
    if(evt.column === 'modifyBenefic'){
      var beneficio;
      if(evt.value.pkIIdBenefit > 0){
        beneficio = this.dataTableBeneficios.find(item => item.pkIIdBenefit === evt.value.pkIIdBenefit);
      }else{
        beneficio = this.dataTableBeneficios.find(item => item.iOrder === evt.value.iOrder);
      }
      if (beneficio) {
        this.formLandingSecciones.get('vBenefic')?.setValue(beneficio.vName);

        if (beneficio.isTemporary) {
          this.editingItem = beneficio;
          this.isEditingRealBenefit = false;
        } else {
          this.editingItem = beneficio;
          this.isEditingRealBenefit = true;
        }
      }
    }
    if (evt.column === 'modifyCarouselImage') {
      this.pkSeccionProductoToModify = evt.value.pkIIdCarouselLanding;
      this.fileName = evt.value?.imagenPC?.vFileName
      this.fileNameMovil =  evt.value?.imagenMovil?.vFileName
      this.formCarruselImg.patchValue({
        iOrder: evt.value.iOrder,  
        pkIIdCarouselLanding: evt.value.pkIIdCarouselLanding,
        fkIIdBusinessCountry: evt.value.fkIIdBusinessCountry
      })
      this.openModalIMG()
      
    }
    if(evt.column === 'modifyCardImage') {
      this.pkSeccionCardsToModify= evt.value.pkIIdCarouselLanding;
      this.fileName = evt.value.nameImgPC
      this.formTarjetas.patchValue({
        iOrder: evt.value.iOrder,  
        vDescription: evt.value.vTitle,
        vCategory: evt.value.vCategory,
        vText: evt.value.vText,
        bActiveButton: evt.value.bActiveButton,
        vTextButton: evt.value.vTextButton,
        vLinkRedirection: evt.value.vLinkRedirection,
        pkIIdCardLanding: evt.value.pkIIdCardsLanding,
        fkIIdBusinessCountry: evt.value.fkIIdBusinessCountry
      })
      this.openModalTarjet()
    }
    if(evt.column === 'modifyProductCarousel'){
      this.pkSeccionCorosuelProductoToModify= evt.value.pkIIdProductCorouselLanding;
      this.fileName = evt.value.nameImgPC
      this.formCarruselProducto.patchValue({
        iOrder: evt.value.iOrder,  
        iIdProduct: evt.value.fkIIdProduct,
        vTitle: evt.value.vTitle,
        vText: evt.value.vText,
        pkIIdProductCorouselLanding: evt.value.pkIIdProductCorouselLanding
      })    
      this.openModalProduct()
    }
    if(evt.column === 'modifyNews'){
      console.log("evt.value", evt.value)
      this.pkSeccionNewToModify = evt.value.pkIIdNews
      this.fileName = evt.value.nameImgPC
      this.formNews.patchValue({
        IOrder: evt.value.iOrder,  
        VCategory: evt.value.vCategory,
        VTitle: evt.value.vTitle,
        vText: evt.value.vText,
        VDescription: evt.value.vDescription,
        BActiveButton: evt.value.bActiveButton,
        VTextButton: evt.value.vTextButton,
        VLinkRedirection: evt.value.vLinkRedirection,
        DEndDate: evt.value.dEndDate,
        pkIIdNews:  evt.value.pkIIdNews,
        iOpenLink: evt.value.iOpenLink
      })    
      this.openModalNotice()

    }
    if(evt.column === 'modifyQuestion'){
      this.pkSeccionQuestionToModify = evt.value.pkIIdQuestion
      this.formQuestion.patchValue({
        iOrder: evt.value.iOrder,  
        vQuestion: evt.value.vQuestion,
        vAnswer: evt.value.vAnswer,
        pkIIdQuestion: evt.value.pkIIdQuestion
      })   
      this.openModalQuestion();
    }
    if(evt.column === 'modifyProduct'){
      this.pkSeccionProductToModify = evt.value.pkIIdProductLanding
      this.fileName = evt.value.nameImgPC
      this.colortertiary = evt.value.vTertiaryColor
      this.colorsecundary = evt.value.vSecondaryColor
      this.colorprimary = evt.value.vPrimaryColor
      this.formProduct.patchValue({
        iOrder: evt.value.iOrder,  
        vCategory: evt.value.vCategory,
        fkIIdProduct: evt.value.fkIIdProduct,
        vTitle: evt.value.vTitle,
        vText: evt.value.vText,
        vPrimaryColor: evt.value.vPrimaryColor,
        vSecondaryColor: evt.value.vSecondaryColor,
        vTertiaryColor: evt.value.vTertiaryColor,
        iPosition: evt.value.iPosition,
        bActiveKnowMore: evt.value.bActiveKnowMore,
        bActiveQuote: evt.value.bActiveQuote,
        vLinkRedirection: evt.value.vLinkRedirection,
        iOpenLink: evt.value.iOpenLink,
        pkIIdProductLanding: evt.value.pkIIdProductLanding

      })  
      this.dataTableCoverage = (evt.value.vCoverage || []).map((item: any, index: number) => ({
        ...item,
        iOrder: index + 1 ,
        pkIdProductBenefict: item.pkIdProductBenefict
      }));
      this.openModaladdProduct();
    }
  }

  async getImagenesEditar(imagenes: any[]): Promise<void> {
    const promises = imagenes.map(async (img, index) => {
      const pcId = img.fkIIdUploadedFilePc ?? img.fkIIdUploadedFile ?? img.fkIIdUploadFile ?? img.fkIdUploadedFilePC
      ?? img.fkIIdUploadFile;
      if (pcId != null) {
          const responsePC = await this._fileService.getImageGeneral(pcId);
          img.imagenPC = responsePC;
          img.nameImgPC = responsePC?.vFileName;
      }
      if (img.fkIIdUploadedFileMovil != null) {
          const responseMovil = await this._fileService.getImageGeneral(img.fkIIdUploadedFileMovil);
          img.imagenMovil = responseMovil;
          img.nameImgMovil = responseMovil?.vFileName;
      } 
    }); 
    await Promise.all(promises);
  }
  async getImagenesEditarCards(imagen: any[]): Promise<void> {
    const promises = imagen.map(async img => {
      if (img.fkIIdUploadedFile != null) {
        img.imagenPC = await this._fileService.getImageGeneral(img.fkIIdUploadedFile);
        img.nameImgPC = img.imagenPC.vFileName
      }
    });
    await Promise.all(promises);
  }
  async getForms(idBusiness: number) {
    this._moduleService
      .getForms(idBusiness)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            'No existen formularios asociados a este estado'
          );
        } else {
          this.form = resp.result;
          // this.seeManage();
        }
      });

  }
  onPestanaChangeButton(id: number): void {
    this.selectedPestanaButton = id;
    this.loadSubPestanaButton(this.selectedPestanaButton)
    this._cdr.detectChanges();
  }
  onSubPestanaChangeButton(value: any): void {
    this.selectedSubPestanaButton = value;
    this._cdr.detectChanges();

  }
  loadSubPestanaButton(selectedValue: number) {
    this._pestanaService.getSubPestana(selectedValue).subscribe({
      next: (resp) => {
        if (!resp.error) {
          this.subpestanaButton = [...resp.result];
          if (this.subpestanaButton.length == 0) {
            this.selectedSubPestanaButton = null
          }
          this.disable = false
        } else {
          console.error('Error en la respuesta del servidor:', resp.message);
        }
      }
    });
  }


}
