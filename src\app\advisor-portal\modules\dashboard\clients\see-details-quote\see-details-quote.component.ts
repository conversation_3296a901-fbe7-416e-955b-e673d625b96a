import { CommonModule } from '@angular/common';
import { Component, OnDestroy, Input } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/store/app.reducers';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { TemplateModel } from 'src/app/shared/models/templates';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { QuotationFormComponent } from 'src/app/shared/components/quotation/quotation-form/quotation-form.component';
import { QuotationDataComponent } from 'src/app/shared/components/quotation/quotation-data/quotation-data.component';
import { QuotationOfferComponent } from 'src/app/shared/components/quotation/quotation-offer/quotation-offer.component';
import { QuotationEmitComponent } from 'src/app/shared/components/quotation/quotation-emit/quotation-emit.component';
import { QuotationPayComponent } from 'src/app/shared/components/quotation/quotation-pay/quotation-pay.component';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatIconModule } from '@angular/material/icon';
import { ClientService } from 'src/app/shared/services/client/client.service';

@Component({
  standalone: true,
  selector: 'app-see-details-quote',
  templateUrl: './see-details-quote.component.html',
  styleUrls: ['./see-details-quote.component.scss'],
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    WizardComponent,
    QuotationFormComponent,
    QuotationDataComponent,
    QuotationOfferComponent,
    QuotationEmitComponent,
    QuotationPayComponent,
    MatProgressBarModule,
    BreadcrumbComponent,
    MatIconModule,
  ],
})
export class SeeDetailsQuoteComponent implements OnDestroy {
  id: number = 0;
  extraId: number = 0;
  responseTemplate?: TemplateModel;
  listProductId: number[] = [];
  idPlan: number = 0;
  idProducto: number = 0;
  routeSubs?: Subscription;
  loadingProductIcon: any;
  imageSrc: any;
  showLoading: boolean = true;
  transactionTimeInSeconds?: number;
  timer: any;
  startTime?: number;
  loadingProgress: number = 0;
  intervalDuration: number = 1000;
  buttonNextDisabled: boolean = false;
  currentStep: number = 0;
  idForm: number = 0;
  idClient: number = 0;
  @Input() isCustomerPortal: boolean = false;

  constructor(
    private _activatedRoute: ActivatedRoute,
    private store: Store<AppState>,
    private _fileService: FileService,
    private _sanitizer: DomSanitizer,
    private _translateService: TranslateService,
    private _settingService: SettingService,
    private _productService: ProductService,
    private _messageService: MessageService,
    private _parameterService: ParametersService,
    private _clientService: ClientService
  ) {}

  /**
   * Initializes the component by setting up route parameters and subscribing to necessary services.
   *
   * This method is called when the component is initialized. It subscribes to route parameters to
   * retrieve client and product IDs, sends client information to the service, fetches images for loading,
   * and listens to store updates for the quote state.
   */
  ngOnInit(): void {
    // Subscribe to route parameters to extract client and product IDs
    this.routeSubs = this._activatedRoute.params.subscribe((p) => {

      // Check if client ID, product ID, and comesFromClient parameters are present
      if (p['idClient'] && p['idProduct'] && p['comesFromClient']) {
        this.idClient = p['idClient'];

        // Notify the client service with current client details and quote status
        this._clientService.sendIsClientTrue({
          idClient: p['idClient'],
          isClientDetailProduyct: false,
          isDetailClient: false,
          isClientDetailQuote: true,
        });
      }

      // Parse the product ID from route parameters and fetch relevant images
      this.idProducto = Number(p['idProduct']);
      this.getImageForLoading(this.idProducto);
      this.getImageBusiness();

      // Subscribe to store updates related to the quote state
      this.store.select('quote').subscribe((state) => {
        // Set loading status based on the state
        this.showLoading = state.ShowLoadingQuote;
        this.showLoading
          ? this.startTransaction() // Start transaction if loading
          : this.finishTransaction(); // End transaction if not loading

        // Update button state and current step based on the state
        this.buttonNextDisabled = state.isDisableNextButton;
        this.currentStep = state.stage + 1;
      });
    });

    // Perform additional action validation
    this.validateAction();
  }

  /**
   * Validates actions based on route parameters.
   *
   * This method subscribes to route parameters to check if `idProduct` is present. If `idProduct`
   * is found, it retrieves the template associated with that product ID by calling the
   * `getTemplateByIdProduct` method.
   */
  validateAction() {
    // Subscribe to route parameters to extract `idProduct`
    this._activatedRoute.params.subscribe((params: any) => {
      // Check if `idProduct` is present in the route parameters
      if (params.idProduct) {
        // Fetch the template for the specified product ID
        this.getTemplateByIdProduct(params.idProduct);
      }
    });
  }

  /**
   * Fetches the template associated with a specific product ID.
   *
   * This method retrieves a template by calling the `getTemplateByIdProduct` method from
   * `_parameterService`. It handles any potential errors and processes the response accordingly.
   *
   * @param idProduct - The ID of the product for which the template is to be fetched.
   */
  getTemplateByIdProduct(idProduct: number) {
    this._parameterService
      .getTemplateByIdProduct(idProduct) // Call the service method to get the template
      .pipe(
        catchError((error) => {
          // Handle errors from the service call
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          // Return an empty array to continue the observable stream
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {

        // Check if the response is an array
        if (Array.isArray(response)) {
          // Handle case when response is an empty array or other logic if needed
        } else {
          // Handle the case when response is an object
          if (response.error) {
            // Show error message if the response contains an error
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            // Assign the result to `responseTemplate` and log it
            this.responseTemplate = response.result;

          }
        }
      });
  }

  /**
   * Handles the event to update the product array and set the current plan ID.
   *
   * This method updates the `listProductId` with the new array received from the event,
   * sets the `idPlan` to the first product ID in the array, and logs the event for debugging.
   *
   * @param event - The event containing the array of product IDs.
   */
  getProductArray(event: any) {
    // Update the list of product IDs with the new array from the event
    this.listProductId = event;

    // Set the ID of the current plan to the first product ID in the list
    this.idPlan = this.listProductId[0];

  }

  /**
   * Retrieves and sets the image for the loading icon based on the provided product ID.
   *
   * This method fetches the product details using the given `idProduct`. It then retrieves
   * the upload file information associated with the product and sets the `loadingProductIcon`
   * to the base64-encoded image if available.
   *
   * @param idProduct - The ID of the product for which to retrieve the image.
   */
  getImageForLoading(idProduct: number) {
    // Fetch product details using the product ID
    this._productService
      .getProduct(idProduct)
      .pipe(
        // Handle errors during the product retrieval
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant('')
            );
          }
          return of([]); // Return an empty observable to complete the stream
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        // Check if the response is an array (indicating an error or no data)
        if (Array.isArray(resp)) {
          // No additional action needed if the response is an array
        } else {
          // Fetch the upload file information associated with the product
          this._fileService
            .getUploadFileById(resp.result.fkIIdUploadFile)
            .pipe(
              // Handle errors during the file retrieval
              catchError((error) => {
                if (error.error.error) {
                  this._messageService.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    this._translateService.instant('')
                  );
                }
                return of([]); // Return an empty observable to complete the stream
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              // Check if the response is an array (indicating an error or no data)
              if (Array.isArray(resp)) {
                // No additional action needed if the response is an array
              } else {
                // Check if the file path and base64 image data are available
                if (resp.result.vFilePath && resp.result.imageBase64) {
                  // Extract the file extension from the file path
                  let extension =
                    resp.result.vFilePath.split('.')[
                      resp.result.vFilePath.split('.').length - 1
                    ];
                  // Create a data URL for the image using the base64 data
                  let src = `data:image/${extension};base64,${resp.result.imageBase64}`;
                  // Set the image source for the loading product icon
                  this.loadingProductIcon = src;
                } else {
                  // Set an empty string if no image data is available
                  this.loadingProductIcon = '';
                }
              }
            });
        }
      });
  }

  /**
   * Retrieves and sets the business image based on the business country ID.
   *
   * This method fetches the initial data settings to determine the business country ID.
   * It then retrieves the upload file information associated with the business country ID and
   * sets the `imageSrc` property to a sanitized URL representing the image. If an error occurs,
   * or if no data is available, it sets `imageSrc` to a default image.
   */
  async getImageBusiness() {
    // Retrieve the initial data settings asynchronously
    let dataSetting = await this._settingService.getDataSettingInit();

    // Extract the business country ID from the data settings, defaulting to 0 if not available
    const pkIIdBusinessByCountry =
      dataSetting == undefined ? 0 : dataSetting.idBusinessByCountry;

    // Declare variables to hold file extension and base64 image data
    let extension: string = '';
    let base64: string = '';

    // Conditional block to check if image retrieval is needed
    if (true) {
      // Fetch the upload file information based on the business country ID
      this._fileService
        .getUploadFileByBusinessCountryId(pkIIdBusinessByCountry)
        .subscribe({
          // Handle successful response
          next: (response) => {
            // Extract the file extension from the file name
            extension = response.result.vFileName.split('.');
            extension =
              extension[response.result.vFileName.split('.').length - 1];

            // Create a data URL for the image using the base64 data
            base64 = `data:image/${extension};base64,${response.result.imageBase64}`;

            // Sanitize the data URL and set it to the imageSrc property
            this.imageSrc =
              this._sanitizer.bypassSecurityTrustResourceUrl(base64);
          },
          // Handle errors during file retrieval
          error: (err) => {
            // Set a default image if an error occurs
            this.imageSrc = `assets/img/image.svg`;
          },
        });
    } else {

    }
  }

  /**
   * Starts a transaction timer.
   *
   * This method initializes the transaction time, records the start time, and sets up
   * a recurring timer that updates the elapsed transaction time at regular intervals.
   * The timer calls the `updateElapsedTime` method to update the transaction time.
   */
  startTransaction() {
    // Initialize the transaction time to 0 seconds
    this.transactionTimeInSeconds = 0;

    // Record the current time as the start time of the transaction
    this.startTime = Date.now();

    // Set up a recurring timer that updates the transaction time
    this.timer = setInterval(() => {
      // Call the method to update the elapsed transaction time
      this.updateElapsedTime();
    }, this.intervalDuration); // Timer interval duration in milliseconds
  }

  /**
   * Updates the elapsed transaction time and manages the loading progress.
   *
   * This method calculates the elapsed time since the transaction started, updates
   * the transaction duration in seconds, and adjusts the loading progress. If the elapsed
   * time exceeds 10 seconds, it calls the `finishTransaction` method to conclude the transaction.
   */
  updateElapsedTime() {
    // Check if loading is currently being displayed
    if (this.showLoading) {
      // Get the current time
      const now = Date.now();

      // Calculate the elapsed time in milliseconds since the transaction started
      const milliseconds = now - this.startTime!;

      // Convert elapsed time from milliseconds to seconds
      this.transactionTimeInSeconds = milliseconds / 1000;

      // If the elapsed time exceeds 10 seconds, finalize the transaction
      if (this.transactionTimeInSeconds > 10) {
        this.finishTransaction();
      }

      // Update the loading progress based on the elapsed time
      this.loadingProgress = this.transactionTimeInSeconds * 10;

      // Call the method to update the visual representation of progress
      this.updateProgress();
    } else {
      // If loading is not displayed, finalize the transaction
      this.finishTransaction();
    }
  }

  /**
   * Concludes the ongoing transaction by stopping the timer.
   *
   * This method clears the interval timer used to track the elapsed transaction time and
   * sets the timer reference to `null`, indicating that the transaction has been concluded.
   */
  finishTransaction() {
    // Stop the interval timer to cease tracking elapsed time
    clearInterval(this.timer);

    // Set the timer reference to null to indicate that no timer is active
    this.timer = null;
  }

  /**
   * Updates the position of a moving icon based on the current loading progress.
   *
   * This method adjusts the horizontal position of an HTML element with the ID 'movingIcon'
   * according to the `loadingProgress` property. The position is set with a small offset
   * to account for visual alignment.
   */
  updateProgress() {
    // Retrieve the HTML element with the ID 'movingIcon'
    const icon = document.getElementById('movingIcon') as HTMLElement;

    // Check if the element exists and is valid
    if (icon) {
      // Update the horizontal position of the icon based on the loading progress
      // The position is set as a percentage value with a small offset (-2%) for alignment
      icon.style.left = this.loadingProgress - 2 + '%';
    }
  }

  /**
   * Cleanup logic when the component is destroyed.
   *
   * This method performs necessary cleanup tasks when the component is destroyed,
   * such as unsubscribing from observables and sending a service call to update client details.
   */
  ngOnDestroy(): void {
    // Unsubscribe from the route subscription to avoid memory leaks
    this.routeSubs?.unsubscribe();

    // Send an update to the client service to indicate that the client detail view is no longer active
    this._clientService.sendIsClientTrue({
      idClient: this.idClient,
      isClientDetailProduyct: false,
      isDetailClient: false,
      isClientDetailQuote: false,
    });
  }
}
