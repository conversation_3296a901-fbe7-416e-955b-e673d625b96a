import { Component, ViewChild, TemplateRef } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateAdapter, MAT_DATE_LOCALE, MatNativeDateModule } from '@angular/material/core';
import { MatTabChangeEvent, MatTabGroup, MatTabsModule } from '@angular/material/tabs';
import { ListPolicyFileModel, PolicyDetailModel, RequestGeneralInfoModel } from 'src/app/shared/models/policy';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ActivatedRoute } from '@angular/router';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { catchError, debounceTime, of } from 'rxjs';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import { DetailsRiskModificationsComponent } from '../../shared/components/details-risk-modifications/details-risk-modifications.component';
import { PolicyRiskFormModel } from '../../shared/models/policy-risk-form.model';
import { BeneficiariesTableComponent } from '../../shared/components/beneficiaries-table/beneficiaries-table.component';
import { BodyTableModel } from 'src/app/shared/models/table';
import { BeneficiaryModel } from '../../shared/models';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { MatDialog } from '@angular/material/dialog';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { UserService } from 'src/app/shared/services/user/user.service';
import { CustomDateAdapter } from '../../shared/utils/custom-date-adapter';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';

@Component({
  selector: 'app-page-renewal-individual-policy',
  standalone: true,
  imports: [
    CommonModule,
    MatSlideToggleModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTabsModule,
    DetailsRiskModificationsComponent,
    BeneficiariesTableComponent,
    Modal2Component,
    ValidationInputFileDirective
  ],
  templateUrl: './page-renewal-individual-policy.component.html',
  styleUrls: ['./page-renewal-individual-policy.component.scss'],
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'es' },
    { provide: DateAdapter, useClass: CustomDateAdapter },
  ]
})
export class PageRenewalIndividualPolicyComponent {

  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  @ViewChild('beneficiaryModal') beneficiaryModal?: TemplateRef<any>;

  //Variables relacionadas con el formulario.
  idWtw: number = 0;
  idParent: number = 0;
  idPolicyRisk: number = 0;
  idPolicyType: number = 0;
  maximumNumberBeneficiaries: number = 0;
  idUser: number = 0;
  beneficiaryModalTitle: string = '';
  selectedFileName: string = '';
  readonly POLICY_HOLDER: string = "Tomador";
  readonly INSURED: string = "Asegurado";
  readonly BENEFICIARY: string = "Beneficiario";
  readonly OTHER: string = "Otro";
  fileTypes: string[] = ['PDF'];
  dynamicTabs: string[] = [];
  showBtnDelete: boolean = false;
  showBtnDownload: boolean = false;
  editedFiles: boolean = false;
  isLastTab: boolean = false;
  hasCredit: boolean = false;
  newBeneficiary: boolean = false;
  listFile: File[] = [];
  form: FormGroup = new FormGroup({});
  renewPolicy = new FormData();
  endValidity: Date = new Date();
  insuranceList: InsuranceCompanyModel[] = [];
  listIdFiles: ListPolicyFileModel[] = [];
  policyRiskForm: PolicyRiskFormModel[] = [];
  riskBeneficiary: PolicyRiskFormModel = {
    id: 0,
    tab: this.BENEFICIARY,
    isValid: false,
    valueForm: '',
  };
  beneficiariesData: BeneficiaryModel[] = [];
  estructTableBeneficiariesModify: BodyTableModel[] = [
    { columnLabel: this._translateService.instant('Policy.Beneficiarie'), columnValue: 'nameComplete' },
    { columnLabel: this._translateService.instant('Policy.DivisionByPercentage'), columnValue: 'percentage' },
    { columnLabel: this._translateService.instant('Modify'), columnValue: 'modify', columnIcon: 'edit' },
    { columnLabel: this._translateService.instant('Delete'), columnValue: 'delete', columnIcon: 'delete' }
  ];

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _activatedRoute: ActivatedRoute,
    public _utilService: UtilsService,
    private _transactionService: TransactionService,
    private _fileService: FileService,
    private _translateService: TranslateService,
    private _location: Location,
    private _productService: ProductService,
    private _fieldService: FieldService,
    public modalDialog: MatDialog,
    private _userService: UserService
  ) {}

  async ngOnInit() {
    this.initForm();
    this.getDataUrl();
    this.idUser = await this._userService.getUserIdSesion();
  }

  //Obtiene los valores de las variables enviadas por medio de la URL.
  getDataUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idPolicy) {
        this.idWtw = Number(params.idPolicy);
        this.getPolicyDetailById(this.idWtw);
        this.getConfiguredInsurancesList(this.idWtw);
      }
      if (params.idPolicyRisk) {
        this.idPolicyRisk = Number(params.idPolicyRisk);
        this.getBeneficiesByIdPolicy();
        this.getRiskForRenewByIdPolicyRisk();
      }
      if (params.idPolicyType){
        this.idPolicyType = Number(params.idPolicyType);
      }
    });
  }

  //Inicializa el formulario.
  initForm() {
    this.form = this._fb.group({
      YearRenewal: [null, [Validators.required]],
      IdPolicy: [{ value: null, disabled: true }],
      ProductName: [{ value: null, disabled: true }],
      Extension: [false, [Validators.required]],
      Endorsement: [true, [Validators.required]],
      PolicyNumber: [null, [Validators.required]],
      IdInsurance: [null, [Validators.required]],
      StartValidity: [null, [Validators.required]],
      EndValidity: [null, [Validators.required]],
      Premium: [null, [Validators.required]],
      InsuredValue: [null, [Validators.required]],
      PaymentFrequency: [null, [Validators.required]],
      PaymentsNumber: [null, [Validators.required]],
      CreditNumber: [null],
      DisbursementDate: [null],
      DueDate: [null],
      Files: [null],
      EditedFiles: [false],
    });
    this.form.valueChanges.pipe(debounceTime(300)).subscribe({
      next: (data: RequestGeneralInfoModel) => {
        const form: RequestGeneralInfoModel = this.form.getRawValue();
        form.Files = this.listFile;
        form.formValid = this.form.valid;
        if (data.Files) {
          this.showBtnDelete = true;
        }
        else {
          this.showBtnDelete = false;
        }
      },
    });
    this.form.get('StartValidity')?.valueChanges.subscribe((startDate) => {
      if (startDate) {
        const start = new Date(startDate);
        const endControl = this.form.get('EndValidity');
        const end = endControl?.value ? new Date(endControl.value) : null;

        if (end && start > end) {
          endControl?.setValue(null);
        }
        endControl?.updateValueAndValidity();
      }
    });
    this.form.get('IdInsurance')?.valueChanges.subscribe((idParent) => {
      if (idParent) {
        this.idParent = idParent;
      }
    });
  }

  //Obtiene la lista de tipo de identificación registrados en el sistema.
  getPolicyDetailById(idPolicy: number) {
    this._transactionService
      .getPolicyDetailById(idPolicy, true)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.validationsForm(resp.result.bAssociatedWithCredit);
          this.assignDataForm(resp.result);
        }
      });
  }

  //Obtiene todas las aseguradoras asociadas por IdBusinessByCountry.
  getConfiguredInsurancesList(idPolicy: number) {
    this._transactionService
      .getConfiguredInsurancesList(idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.insuranceList = resp.result;
        }
      });
    }

  //Obtiene los beneficiarios de una póliza.
  getBeneficiesByIdPolicy() {
    this._transactionService
      .getBeneficiesByIdPolicy(this.idPolicyRisk)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.beneficiariesData = resp.result;
        }
      });
  }

  //Obtiene los riesgos para renovación
  getRiskForRenewByIdPolicyRisk() {
    this._transactionService
      .getRiskForRenewByIdPolicyRisk(this.idPolicyRisk)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.policyRiskForm = resp.result.map((x: any) => {
            return {
              tab: x.customerType,
              isValid: true,
              valueForm: x.vJson,
            };
          });
        }
      });
  }

  getBeneficiariesNumberByIdPolicy() {
    this._transactionService
      .getBeneficiariesNumberByIdPolicy(this.idParent)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(this._translateService.instant('Warning'), error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message);
          } else {
            this.maximumNumberBeneficiaries = resp.result;
          }
        }
      });
  }

  //Obtene los cambios de una pestaña a otra en el componente tab.
  onTabChange(event: MatTabChangeEvent) {
    const totalTabs = this.tabGroup._tabs.length;
    this.isLastTab = event.index === totalTabs - 1;
  }

  // Cambiar al siguiente tab
  goToNextTab(): void {
    if (this.tabGroup) {
      const currentIndex = this.tabGroup.selectedIndex || 0;
      this.tabGroup.selectedIndex = currentIndex + 1;
    }
  }

  onFileChange(event: Event) {
    const guid = crypto.randomUUID();
    const inputElement = event.target as HTMLInputElement;
    const files = inputElement.files;
    let totalSize: number = 0;

    // Array de extensiones permitidas
    const allowedExtensions: string[] = ['pdf'];

    // Limpiamos la lista de archivos y el nombre de los archivos seleccionados si ya hay elementos guardados.
    if (this.listFile) {
      this.selectedFileName = '';
    }

    let fileName: Array<string> = [];

    if (
      this.selectedFileName === '' ||
      this.selectedFileName.split(' ').length == 0
    ) {
      if (files && files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const fileExtension = file.name.split('.').pop()?.toLowerCase(); // Obtenemos la extensión del archivo

          // Validamos si la extensión está permitida
          if (fileExtension && allowedExtensions.includes(fileExtension)) {
            this.selectedFileName += `${guid}-` + file.name + ' ';
            fileName.push(`${guid}-` + file.name);
            totalSize += file.size;
          } else {
            this._messageService.messageInfo(
              `El archivo ${file.name} tiene una extensión no permitida.`,
              `Solo se permiten: ${allowedExtensions.join(', ').toUpperCase()}`
            );

            // Limpiar la lista de archivos y el nombre seleccionado
            this.selectedFileName = '';
            this.listFile = [];
            this.listIdFiles = [];
            this.editedFiles = false;

            return; // Salir de la función si hay un archivo con extensión no válida
          }
        }

        totalSize = totalSize / (1024 * 1024);
        if (totalSize < 20) {
          const copiedFiles: File[] = Array.from(files).map((file) => new File([file], `${guid}-` + file.name, { type: file.type }));
          this.listFile = copiedFiles;
          this.assignDocumentNames();
          this.editedFiles = true;
        }
      }
    }
  }

  assignDocumentNames() {
    let listFileName: string = '';
    this.listFile.forEach((element) => { listFileName = listFileName + element.name });
    this.form.get('Files')?.setValue(listFileName);
  }

  //Permite descargar desde la api de file una lista de archivos.
  getFileByIdList(idFiles: Array<number>) {
    this._fileService
      .GetFileByIdList(idFiles)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: any) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            // Intenta obtener el nombre del archivo desde la cabecera Content-Disposition
            const fileName = resp.headers.get('X-File-Name') || 'file.zip';
            // Especifica el tipo MIME del archivo según el tipo que se espera
            const mimeType = 'application/octet-stream';
            const blob = new Blob([resp.body], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = fileName;
            link.href = url;
            link.click();
            window.URL.revokeObjectURL(url);
          }
        }
      });
  }

  //Evento que se dispara una vez se preciona en el iciono de descargar documento.
  downloadDocument() {
    if (this.listIdFiles.length > 0) {
      this.getFileByIdList(this.listIdFiles.map((value) => value.idUploadFile));
    }
  }

  //Elimina el archivo cargado.
  deleteDocument() {
    this.form.get('Files')?.setValue(null);
    this.listFile = [];
    this.listIdFiles = [];
    this.selectedFileName = '';
    this.showBtnDownload = false;
    this.editedFiles = true;
  }

  //Función que se ejecuta al darle click al botón cancelar.
  cancel() {
    this._location.back();
  }

  validationsForm(bAssociatedWithCredit: boolean) {
    if (bAssociatedWithCredit) {
      this.form.get('CreditNumber')?.setValidators(Validators.required);
      this.form.get('CreditNumber')?.updateValueAndValidity();
      this.form.get('DisbursementDate')?.setValidators(Validators.required);
      this.form.get('DisbursementDate')?.updateValueAndValidity();
      this.form.get('DueDate')?.setValidators(Validators.required);
      this.form.get('DueDate')?.updateValueAndValidity();
    }
  }

  assignDataForm(data: PolicyDetailModel) {
    let startDate = new Date(data.startValidity);
    startDate.setDate(startDate.getDate() + 1);
    let endDate = new Date(data.endValidity);
    endDate.setDate(endDate.getDate() + 1);

    this.form.get('IdPolicy')?.setValue(data.idPolicy);
    this.form.get('Endorsement')?.setValue(data.endorsement);
    this.form.get('YearRenewal')?.setValue(data.yearRenewal);
    this.form.get('PolicyNumber')?.setValue(data.policyNumber);
    this.form.get('IdInsurance')?.setValue(data.idParent);
    this.form.get('StartValidity')?.setValue(startDate);
    this.form.get('EndValidity')?.setValue(endDate);
    this.form.get('Premium')?.setValue(data.premium);
    this.form.get('InsuredValue')?.setValue(data.insuredValue);
    this.form.get('PaymentFrequency')?.setValue(data.paymentFrequency);
    this.form.get('PaymentsNumber')?.setValue(data.paymentsNumber);
    this.form.get('CreditNumber')?.setValue(data.creditNumber);
    this.form.get('DisbursementDate')?.setValue(data.disbursementDate);
    this.form.get('DueDate')?.setValue(data.dueDate);
    this.form.get('EditedFiles')?.setValue(false);

    this.listIdFiles = data.filesCreated;
    const [year, month, day] = data.endValidity.split('-').map(Number);
    this.endValidity = new Date(year, month - 1, day);
    this.endValidity.setHours(0, 0, 0, 0);
    if (data.filesCreated) {
      let listFileName: string = '';
      if (data.filesCreated.length > 0) {
        data.filesCreated.forEach((element: any) => {
          listFileName = listFileName + element.fileName;
        });
        this.form.get('Files')?.setValue(listFileName);
        this.showBtnDownload = true;
      }
    }
    this.hasCredit = data.bAssociatedWithCredit;
    this.idParent = data.idParent;
    this.GetProductById(data.idProduct);
    this.getTabsWithFieldsByIdPolicy();
    this.getBeneficiariesNumberByIdPolicy();
  }

  GetProductById(idProduct: number) {
    this._productService
      .GetProductById(idProduct)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.form.get('ProductName')?.setValue(resp.result.vProductName);
        }
      });
  }

  

  onInsuranceChange() {
    this.getTabsWithFieldsByIdPolicy();
    this.getFieldsByPolicyForm();
    this.getBeneficiariesNumberByIdPolicy();
  }

  getFormValue(event: any, tab: string) {
    if (tab === this.BENEFICIARY) {
      this.riskBeneficiary.isValid = event.formValid;
      this.riskBeneficiary.valueForm = event.valueForm;
      return;
    }

    if (tab !== this.POLICY_HOLDER && tab !== this.INSURED && tab !== this.BENEFICIARY)
      tab = this.OTHER;
    const existingTab = this.policyRiskForm.find((x) => x.tab === tab);

    if (!existingTab) {
      const riskForm: PolicyRiskFormModel = {
        tab: tab,
        isValid: event.formValid,
        valueForm: event.valueForm,
      };
      this.policyRiskForm.push(riskForm);
    } else {
      existingTab.isValid = event.formValid;
      existingTab.valueForm = event.valueForm;
    }
  }

  

  //Obtiene los beneficiarios de una póliza.
  getTabsWithFieldsByIdPolicy() {
    this._fieldService
      .getTabsWithFieldsByIdPolicy(this.idParent)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.dynamicTabs = [];
          setTimeout(() => {
            this.dynamicTabs = resp.result.map((x: any) => x.vName);
          }, 0);
        }
      });
  }

  

  getDataFormByCustomerType(tab: string): any {
    if (tab !== this.POLICY_HOLDER && tab !== this.INSURED && tab !== this.BENEFICIARY)
      tab = this.OTHER;
    const found = this.policyRiskForm.find((x) => x.tab === tab);
    return found ? found.valueForm : null;
  }

  //Obtiene los beneficiarios de una póliza.
  getFieldsByPolicyForm() {
    let risks = this.policyRiskForm.map((x: PolicyRiskFormModel) => {
      return {
        tab: x.tab,
        isValid: false,
        valueForm: this.isJson(x.valueForm) ? JSON.stringify(x.valueForm || {}) : x.valueForm,
      };
    });

    this.beneficiariesData.forEach(x => {
      let beneficiary: PolicyRiskFormModel = {
        id: x.id,
        tab: this.BENEFICIARY,
        isValid: false,
        valueForm: this.isJson(x.json) ? JSON.stringify(x.json || {}) : x.json,
      }
      risks.push(beneficiary);
    });

    this._fieldService
      .getFieldsByPolicyForm(risks, this.idParent)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.policyRiskForm = resp.result
            .filter((x: PolicyRiskFormModel) => x.id == null)
            .map((x: PolicyRiskFormModel) => ({
              tab: x.tab,
              isValid: true,
              valueForm: this.convertToJson(x.valueForm),
            }));
          this.beneficiariesData.forEach(x => {
            let result = resp.result.find((y: PolicyRiskFormModel) => y.id === x.id);
            if (result) {
              x.json = this.convertToJson(result.valueForm);
            }
          });
        }
      });
  }

  //Función que sirve para añadir beneficiarios.
  getActionTableBeneficiaries(event: any) {
    this.riskBeneficiary.id = event.value.id;
    switch (event.column) {
      case 'modify':
        this.beneficiaryModalTitle = this._translateService.instant('Policy.ModifyBeneficiary');
        this.newBeneficiary = false;
        this.riskBeneficiary.valueForm = this.isJson(event.value.json) ? event.value.json : this.convertToJson(event.value.json);
        this.openModal();
        break;
      case 'delete':
        this.deleteBeneficiary();
        break;
      default:
        break;
    }
  }

  // Función que abre un modal.
  openModal() {
    const modalConfiguration = {
      disableClose: false,
      width: '90vw',
      height: 'auto',
    };
    this.modalDialog.open(this.beneficiaryModal!, modalConfiguration);
  }

  closeModal() {
    this.modalDialog.closeAll();
  }

  addBeneficiary() {
    this.beneficiaryModalTitle = this._translateService.instant('Policy.AddBeneficiary');
    this.riskBeneficiary.valueForm = '';
    this.riskBeneficiary.isValid = false;
    this.newBeneficiary = true;
    this.openModal();
  }

  //Función para eliminar un beneficiario.
  deleteBeneficiary() {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('FormConfiguration.ProgressBar.DeleteMessageConfirm' ),
        '',
        'warning',
        this._translateService.instant('Delete'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this.beneficiariesData = this.beneficiariesData
            .filter((item) => item.id !== this.riskBeneficiary.id)
            .map((item, index) => ({
              ...item,
              id: index + 1,
            }));
        }
      });
  }

  createOrUpdateBeneficiary() {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Policy.ConfirmModification'),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          if (this.riskBeneficiary.isValid) {
            this.closeModal();
            const valueForm = this.riskBeneficiary.valueForm;
            const nameComplete = `${ this.findValueByPrefix( valueForm, 'PrimerNombreBeneficiarioPolicy' ) || '' } 
                                  ${ this.findValueByPrefix( valueForm, 'SegundoNombreBeneficiarioPolicy' ) || '' } 
                                  ${ this.findValueByPrefix( valueForm, 'PrimerApellidoBeneficiarioPolicy' ) || '' } 
                                  ${ this.findValueByPrefix( valueForm, 'SegundoApellidoBeneficiarioPolicy' ) || '' }`.trim();
            const percentage = `${ this.findValueByPrefix( valueForm, 'PorcentajeBeneficiarioPolicy' ) || '0' }%`;
            if (this.newBeneficiary) {
              const newBeneficiary: BeneficiaryModel = {
                id: this.beneficiariesData.length + 1,
                nameComplete: nameComplete,
                document: '',
                typeDocument: '',
                percentage: percentage,
                json: this.riskBeneficiary.valueForm,
              };
              this.beneficiariesData = [
                ...this.beneficiariesData,
                newBeneficiary,
              ];
            } else {
              const currentBeneficiary: BeneficiaryModel = {
                id: this.riskBeneficiary.id || 0,
                nameComplete: nameComplete,
                document: '',
                typeDocument: '',
                percentage: percentage,
                json: this.riskBeneficiary.valueForm,
              };
              this.beneficiariesData = this.beneficiariesData.map((item) =>
                item.id === this.riskBeneficiary.id
                  ? { ...item, ...currentBeneficiary }
                  : item
              );
            }
          }
        }
      });
  }

  findValueByPrefix(obj: any, prefix: string): string | undefined {
    const key = Object.keys(obj).find((k) => k.includes(prefix));
    return key ? obj[key] : undefined;
  }

  // Validar si es un json
  isJson(json: any){
    return typeof json === 'object' && json !== null && !Array.isArray(json)
  }

  //Convierte la data guardada en el cargue masivo de string a formato JSON.
  convertToJson(inputString: string): any {
    try {
      const cleanedString = inputString.replace(/\\/g, '');
      const jsonObject = JSON.parse(cleanedString);
      return jsonObject;
    } catch (error) {
      return null;
    }
  }

  //Función que se ejecuta al darle click al botón guardar cambios.
  saveChange() {
    if (!this.form.valid) {
      this.form.markAllAsTouched();
      this._messageService.messageWaring(this._translateService.instant('InvalidForm') + ": " + this._translateService.instant('Policy.PolicyData'), '');
      return;
    }

    let validationResult = this.saveValidations();
    if (!validationResult.isValid){
      return;
    }

    const payload: FormData = new FormData();
    payload.append('IdPolicy', this.idWtw?.toString() || '');
    payload.append('IdParent', this.idParent?.toString() || '');
    payload.append('IdPolicyType', this.idPolicyType?.toString() || '');
    payload.append('IdUser', this.idUser?.toString() || '');
    payload.append('BIsExtension', this.form.get('Extension')?.value?.toString() || '');
    payload.append('BEndorsement', this.form.get('Endorsement')?.value?.toString() || '');
    payload.append('IYearRenewal', this.form.get('YearRenewal')?.value?.toString() || '');
    payload.append('VPolicyNumber', this.form.get('PolicyNumber')?.value?.toString() || '');
    payload.append('DStartValidity', this.form.get('StartValidity')?.value ? this._utilService.formatDate(this.form.get('StartValidity')?.value).toString() : '');
    payload.append('DEndValidity', this.form.get('EndValidity')?.value ? this._utilService.formatDate(this.form.get('EndValidity')?.value).toString() : '');
    payload.append('IdInsurance', this.insuranceList.find(x => x.idParent == this.form.get('IdInsurance')?.value)?.pkIIdInsuranceCompanies?.toString() || '');
    payload.append('IPremium', this.form.get('Premium')?.value?.toString() || '');
    payload.append('IInsuredValue', this.form.get('InsuredValue')?.value?.toString() || '');
    payload.append('VPaymentFrequency', this.form.get('PaymentFrequency')?.value?.toString() || '');
    payload.append('IPaymentsNumber', this.form.get('PaymentsNumber')?.value?.toString() || '');
    payload.append('ICreditNumber', this.form.get('CreditNumber')?.value?.toString() || '');
    payload.append('DDisbursementDate', this.form.get('DisbursementDate')?.value ? this._utilService.formatDate(this.form.get('DisbursementDate')?.value).toString() : '');
    payload.append('DDueDate', this.form.get('DueDate')?.value ? this._utilService.formatDate(this.form.get('DueDate')?.value).toString() : '');
    payload.append('EditedFiles', this.editedFiles?.toString() || '');
    payload.append('VRiskJson', validationResult.json || '');

    let files: File[] = this.listFile;
    if (files) {
      if (files.length > 0) {
        if (this.idWtw > 0 && this.editedFiles) {
          const EditedFiles: boolean = true;
          const addFiles: File[] = files;
          if (addFiles && addFiles.length > 0) {
            addFiles.forEach((file) => {
              const fileObject = file;
              payload.append(`Cover`, fileObject); // Agregar cada archivo al FormData
            });
            payload.append('EditedFiles', EditedFiles.toString());
          }
        } else {
          const EditedFiles: boolean = false;
          const addFiles: File[] = files;
          if (addFiles && addFiles.length > 0) {
            addFiles.forEach((file) => {
              const fileObject = file;
              payload.append(`Cover`, fileObject); // Agregar cada archivo al FormData
            });
            payload.append('EditedFiles', EditedFiles.toString());
          }
        }
      }
    }
    
    this._transactionService
      .renewPolicy(payload)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._location.back();
          if (this.form.get('Extension')?.value == true){
            this._messageService.messageConfirmatio(
              this._translateService.instant('Policy.ExtensionSaved'), '', 'success', 
              this._translateService.instant('Continue'));
          }
          else {
            this._messageService.messageConfirmatio(
              this._translateService.instant('Policy.RenovationSaved'), '', 'success', 
              this._translateService.instant('Continue'));
          }
        }
      });
  }

  saveValidations(): { isValid: boolean; json: string } {
    let startValidity = this.form.get('StartValidity')?.value;
    startValidity.setHours(0, 0, 0, 0);
    if (startValidity <= this.endValidity) {
      this._messageService.messageWaring(this._translateService.instant('Policy.InvalidRenovationValidity'), '');
      return { isValid: false, json: "" };
    }

    let riskJson = {};
    let msgError = "";
    let msgTab = "";
    this.dynamicTabs.forEach(tab => {
      let originalName = tab;
      if (tab !== this.POLICY_HOLDER && tab !== this.INSURED && tab !== this.BENEFICIARY)
        tab = this.OTHER;
      let risk = this.policyRiskForm.find(y => y.tab === tab);
      if (tab === this.BENEFICIARY){
        let totalPercentage = 0;
        let hasPercentage = false;
        this.beneficiariesData.forEach(x => {
          let beneficiaryRisk = this.isJson(x.json) ? x.json : this.convertToJson(x.json);

          // Validar el porcentaje total
          const key = Object.keys(beneficiaryRisk).find(k => k.includes("PorcentajeBeneficiario"));
          if (key) {
            hasPercentage = true;
            const percentageValue = parseFloat(beneficiaryRisk[key]);
            if (!isNaN(percentageValue)) {
              totalPercentage += percentageValue;
            }
          }

          // Combinar los json de beneficiarios
          const transformedRisk = Object.fromEntries(
            Object.entries(beneficiaryRisk).map(([key, value]) => {
              const [prefix, rest] = key.split(/_(.+)/);
              const newKey = `${prefix}_${x.id}_${rest}`;
              return [newKey, value];
            })
          );
          riskJson = { ...riskJson, ...transformedRisk }; // Fusionar los objetos
          if (x.id > this.maximumNumberBeneficiaries){
            msgError = 'Policy.MaximumBeneficiariesExceeded';
          }
          if (msgError !== "") return;
        });
        if (hasPercentage && totalPercentage !== 100){
          msgError = 'Policy.MaximumPecentageExceeded';
        }
        return;
      }

      if (risk && risk.isValid){
        let addRisk = this.isJson(risk.valueForm) ? risk.valueForm : this.convertToJson(risk.valueForm);
        riskJson = { ...riskJson, ...addRisk };
      }
      else{
        msgError = 'InvalidForm';
        msgTab = `: ${risk?.tab == this.OTHER ? originalName : risk?.tab}`;
      }
      
    });

    if (msgError !== ""){
      this._messageService.messageWaring(this._translateService.instant(msgError) + msgTab, '');
      return { isValid: false, json: "" };
    }
    else{
      return { isValid: true, json: JSON.stringify(riskJson) };
    }
  }
}
