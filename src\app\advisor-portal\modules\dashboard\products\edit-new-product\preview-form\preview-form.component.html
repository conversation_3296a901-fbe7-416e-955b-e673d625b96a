<div class="row mb-2">
    <div class="col-12 col-md-3">
        <div class="row mb-2">
            <h3 class="col-md-12">Layout</h3>
            <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>
                    {{"Product.NumberOfColumns" | translate}}
                </mat-label>
                <input matInput type="number" [(ngModel)]="amountColumns" />
            </mat-form-field>
        </div>
    </div>
    <div [formGroup]="formBuild" class="col-12 col-md-9">
        <div class="row mb-2">
            <h3 class="col-md-12">
                {{"Product.Preview" | translate}}
            </h3>
            <h2 class="col-md-12">{{form?.vName}}</h2>
            <mat-tab-group class="border-form">
                <ng-container *ngFor="let tab of form?.pTabs">
                    <mat-tab label="{{ tab.vName }}">
                        <div class="row my-2" *ngFor="let section of tab.pSections">
                            <h3>{{ section.vName }}</h3>
                            <ng-container *ngFor="let field of section.uSectionFields">
                                <div class="col-{{12/amountColumns}} drag-container" [dragula]='"bag-one"' [hidden]="field.bIsInvisible"
                                    [dragulaModel]="section.uSectionFields">
                                    <app-field-generator class="drag-field"
                                        [field]="field.fkIIdFieldNavigation">
                                    </app-field-generator>
                                </div>

                            </ng-container>
                        </div>
                    </mat-tab>
                </ng-container>
            </mat-tab-group>
        </div>
    </div>
</div>
