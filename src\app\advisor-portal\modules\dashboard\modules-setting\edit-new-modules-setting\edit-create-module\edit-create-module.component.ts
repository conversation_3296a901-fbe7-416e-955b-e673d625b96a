import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BehaviorSubject, Subscription, catchError, debounceTime, of } from 'rxjs';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { ImageModel } from 'src/app/shared/models/image-picker';
import { ProcessModel } from 'src/app/shared/models/menu';
import { ModulesSettingModel } from 'src/app/shared/models/menu/modules-setting-model';
import { WizardModuleSettingModel } from 'src/app/shared/models/modules-setting';
import { PorductModulesModel } from 'src/app/shared/models/product/product-modules.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-edit-create-module',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatIconModule,
    GenericImagePickerComponent,
    TranslateModule,
    MatSelectModule,
    PreventionSqlInjectorDirective,
    MatTooltipModule
  ],
  templateUrl: './edit-create-module.component.html',
  styleUrls: ['./edit-create-module.component.scss'],
})
export class EditCreateModuleComponent implements OnInit, OnDestroy, OnChanges {
  @Output() hasSubmodules = new EventEmitter<boolean>();
  @Output() objetModule = new EventEmitter<any>();
  @Output() objetImageModule = new EventEmitter<ImageModel>();
  @Output() isImagenEdit = new EventEmitter<boolean>();
  @Input() pkIIdMenu: number = 0;
  form: FormGroup = new FormGroup({});
  imageSrc: string = '';
  operationType: string = '';
  processes: ProcessModel[] = [];
  products: PorductModulesModel[] = [];
  pkCotizationProcess: number = 0;

  private _settingCountryAndCompanySubscription?: Subscription;
  private _processSubject = new BehaviorSubject<number>(0);
  idBusinessCountry: number = 0;
  titleUploaderImage: string = this._translateService.instant(
    'ModulesSetting.Modules.ModuleImageTitle'
  );
  descriptionUploaderImage: string = this._translateService.instant(
    'ModulesSetting.Modules.DescriptionImageModule'
  );

  constructor(
    public utilsSvc: UtilsService,
    private _fb: FormBuilder,
    private _modulesSettingService: ModulesSettingService,
    private _activatedRoute: ActivatedRoute,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _parametersService: ParametersService,
    private _settingService: SettingService,
    private _fileService: FileService,
    private _productService: ProductService,
    private _customRouter: CustomRouterService

  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['pkIIdMenu'].currentValue > 0) {
      this.getByMenuId(changes['pkIIdMenu'].currentValue);
    }
  }

  ngOnInit(): void {
    this.validateAction();
    this.initForm();
    this.getAllProcess();
    this.getSettingCountryAndCompanySubscription();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion componentes del cargador de imagenes.
      this.titleUploaderImage = this._translateService.instant(
        'ModulesSetting.Modules.ModuleImageTitle'
      );
      this.descriptionUploaderImage = this._translateService.instant(
        'ModulesSetting.Modules.DescriptionImageModule'
      );
    });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.form
              .get('idBusinessCountry')
              ?.setValue(response.enterprise.pkIIdBusinessByCountry);
              if(this.idBusinessCountry > 0 ){
                this.getAllProducts();
              }
          } else {
            this._customRouter.navigate(['/dashboard/modules-setting']);
          }
        }
      );
  }

  initForm() {
    this.form = this._fb.group({
      idMenu: [0],
      vDescription: ['', [Validators.required]],
      vTag: [''],
      fkIdProcess: [0],
      vImage: [''],
      iParent: [0],
      bActive: [true, [Validators.required]],
      binAdvisorPortal: [false, [Validators.required]],
      binCustomerPortal: [false, [Validators.required]],
      bLoginRequired: [false, [Validators.required]],
      hasChildren: [true, [Validators.required]],
      isModule: [true],
      idBusinessCountry: [0],
      idFile: [null, [Validators.required]],
      idProducts: [''],
      bIsTab: [],
      FkIIdModule: [],
      FkIIdProduct: [],
      vLink: [],
      iAssociated: []
    });
    this.hasSubmodules.emit(this.form.get('hasChildren')?.value);
    this.form.get('hasChildren')?.valueChanges.subscribe({
      next: (response) => {
        this.hasSubmodules.emit(response);
        this.form.get('fkIdProcess')?.setValue(null);
        this.form.get('idProducts')?.setValue([]);
        if (!response) {
          this.form.get('fkIdProcess')?.setValidators(Validators.required);
          this.form.get('fkIdProcess')?.updateValueAndValidity();
        } else {
          this.form.get('fkIdProcess')?.clearValidators();
          this.form.get('fkIdProcess')?.updateValueAndValidity();
        }
      },
    });

    this.form.valueChanges.pipe(debounceTime(600)).subscribe({
      next: (data) => {
        let payload = {
          formModuleValid: this.valid,
          formModuleValue: data,
        };
        this.objetModule.emit(payload);
        let payloadSubscription: WizardModuleSettingModel = {
          pkIdMenu: data.idMenu,
        };

        this._modulesSettingService.setCurrentEditNewModulesSetting(
          payloadSubscription
        );
      },
    });
  }

  validateAction() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.pkIIdMenu) {
        this.operationType = 'edit';
        this.getByMenuId(params.pkIIdMenu);
      } else {
        this.operationType = 'create';
        if (this.pkIIdMenu > 0) {
          this.getByMenuId(this.pkIIdMenu);
        }
      }
    });
  }

  getAllProcess() {
    this._parametersService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.processes = resp.result;
            this.pkCotizationProcess = this.processes.find(p => p.vNameProcess === 'Cotización')?.pkIIdProcessFamily || 0
            this._processSubject.next(this.pkCotizationProcess)
          }
        }
      });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  getByMenuId(pkIIdMenu: number) {
    this._modulesSettingService
      .getByMenuId(pkIIdMenu)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.setForm(resp.result);
            let pkIdProcess = 0;
            pkIdProcess = Number(resp.result.fkIdProcess);
            if (!this.form.get('hasChildren')?.value) {
              this.form.get('fkIdProcess')?.setValue(pkIdProcess);
            } else {
            }
            this.form.get('isModule')?.setValue(true);
            this.getImage();
          }
        }
      });
  }

  changeImage(event: ImageModel) {
    this.isImagenEdit.emit(true);
    this.objetImageModule.emit(event);
    if (event && !this.form.get('idFile')?.value) {
      this.form.get('idFile')?.setValue(0);
    }
  }

  getImage() {
    let extension: string = '';
    this._fileService
      .getUploadFileById(this.form.get('idFile')?.value)
      .subscribe({
        next: (response) => {
          if (response.result.vFileName && response.result.imageBase64) {
            extension = response.result.vFileName.split('.');
            extension =
              extension[response.result.vFileName.split('.').length - 1];
            this.imageSrc = `data:image/${extension};base64,${response.result.imageBase64}`;
            this.form.get('vImage')?.setValue(null);
          } else {
          }
        },
      });
  }

  getAllProducts() {
    this._productService
      .getAllProductByBusiness(this.idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.products = resp.result;
          }
        }
      });
  }

  setForm(data: ModulesSettingModel) {
    let products = [];
    if (typeof data.idProducts === 'string') {
      products = JSON.parse(data.idProducts);
    }
    this.form.get('idMenu')?.setValue(data.idMenu);
    this.form.get('vDescription')?.setValue(data.vDescription);
    this.form.get('vTag')?.setValue(data.vTag);
    this.form.get('vImage')?.setValue(data.vImage);
    this.form.get('iParent')?.setValue(data.iParent);
    this.form.get('bActive')?.setValue(data.bActive);
    this.form.get('hasChildren')?.setValue(data.hasChildren);
    this.form.get('isModule')?.setValue(data.isModule);
    this.form.get('idBusinessCountry')?.setValue(data.idBusinessCountry);
    this.form.get('idFile')?.setValue(data.idFile);
    this.form.get('idProducts')?.setValue(products);
    this.form.get('fkIdProcess')?.setValue(data.fkIdProcess);
    this.form.get('binAdvisorPortal')?.setValue(data.binAdvisorPortal);
    this.form.get('binCustomerPortal')?.setValue(data.binCustomerPortal);
    this.form.get('bLoginRequired')?.setValue(data.bLoginRequired);
    this.form.get('bIsTab')?.setValue(data.bIsTab);
    this.form.get('FkIIdModule')?.setValue(data.fkIIdModule);
    this.form.get('FkIIdProduct')?.setValue(data.fkIIdProduct);
    this.form.get('vLink')?.setValue(data.vLink);
    this.form.get('iAssociated')?.setValue(data.iAssociated);
    this.disableForm(products)
  }

  disableForm(products: number[]){
    this._processSubject.subscribe({
      next: (() => {
        if (Number(this.form.get('fkIdProcess')?.value) === this.pkCotizationProcess)
          {
            this.form.get('vDescription')?.disable()
            this.form.get('hasChildren')?.disable()
            this.form.get('fkIdProcess')?.disable()
            this.form.get('idProducts')?.disable()
            this.form.get('idProducts')?.setValue(products);
          }
      })
    })
  }
  complete() {}

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
