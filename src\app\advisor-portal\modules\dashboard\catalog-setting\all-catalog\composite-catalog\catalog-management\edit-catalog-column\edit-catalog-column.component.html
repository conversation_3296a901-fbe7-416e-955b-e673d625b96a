<div class="row mt-5">
    <h3 class="col-md-12">{{ 'CatalogSetting.ColumnConfiguration' | translate }}
        <h6>{{ 'CatalogSetting.ConfigureColumns' | translate }}</h6>
    </h3>
</div>

<div class="mt-5">
    <app-table [displayedColumns]="estructColumnsTable" [data]="dataColumnTable"
        (iconClick)="controller($event)"></app-table>
</div>

<div class="row">
    <div class="col-md-12">
        <button type="button" class="w-auto" (click)="openModal()" mat-raised-button color="primary">
            {{ 'Add' | translate }}
            <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
        </button>
    </div>
</div>

<div class="cont-btns">
    <button class="mx-3" mat-raised-button type="button" (click)="goBackMain()">
      {{ 'CatalogSetting.ExitCatalogs' | translate }}
    </button>
    <button class="mx-3" mat-raised-button type="button" (click)="goBack()">
      {{ 'CatalogSetting.BackCatalog' | translate }}
    </button>
    <button [disabled]="dataColumnTable.length == 0" type="button" mat-raised-button color="primary" (click)="next()">
      {{ 'CatalogSetting.NextCatalog' | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
    </button>
</div>

<!-- Modal add or modify columns -->
<ng-template #AddColumnModal>
    <app-modal2 [titleModal]="modalTitle" (closeModal)="closeModal($event)">
        <ng-container body>
            <form [formGroup]="form">
                <div class="row mt-5">
                    <div class="col-12 col-md-12">
                        <mat-form-field class="w-100 mb-5" appearance="fill">
                            <mat-label>
                              {{ 'CatalogSetting.SheetName' | translate }}
                            </mat-label>
                            <mat-select formControlName="fkIIdSheet">
                                <mat-option *ngFor="let opcion of listSheets" [value]="opcion.id">
                                    {{ opcion.nameSheet }}
                                </mat-option>
                            </mat-select>
                            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'fkIIdSheet', 'required')">
                                {{ "ThisFieldIsRequired" | translate }}
                            </mat-error>
                        </mat-form-field>
                    </div>
                    <div class="col-12 col-md-12">
                        <mat-form-field class="w-100 mb-5" appearance="fill">
                            <mat-label>
                              {{ 'CatalogSetting.ColumnName' | translate }}
                            </mat-label>
                            <input matInput type="text" formControlName="vNameColumn" />
                            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'vNameColumn', 'required')">
                                {{ "ThisFieldIsRequired" | translate }}
                            </mat-error>
                        </mat-form-field>
                    </div>

                    <div class="col-12 col-md-12">
                        <mat-form-field class="w-100 mb-5" appearance="fill">
                            <mat-label>
                              {{ 'CatalogSetting.FieldName' | translate }}
                            </mat-label>
                            <input matInput type="text" formControlName="vName" />
                            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'vName', 'required')">
                                {{ "ThisFieldIsRequired" | translate }}
                            </mat-error>
                        </mat-form-field>
                    </div>
                    <div class="col-12 col-md-12">
                        <mat-checkbox formControlName="bIsRemoveDuplicated" class="my-4">
                          {{ 'CatalogSetting.RemoveDuplicates' | translate }}
                        </mat-checkbox>
                        <mat-checkbox formControlName="bIsDependent" class="my-4">
                          {{ 'CatalogSetting.IsDependent' | translate }}
                        </mat-checkbox>
                    </div>

                    <ng-container *ngIf="form.get('bIsDependent')?.value">
                        <div class="col-12 col-md-12">
                            <mat-form-field class="w-100 mb-5" appearance="fill">
                                <mat-label>
                                    {{ 'CatalogSetting.SelectSheet' | translate }}
                                </mat-label>
                                <mat-select   formControlName="fkIIdSheetParent">
                                    <mat-option *ngFor="let opcion of listSheets" [value]="opcion.id" (onSelectionChange)="getDatalistColumnFilter('D',opcion.id)">
                                        {{ opcion.nameSheet }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                        <div class="col-12 col-md-12">
                            <mat-form-field class="w-100 mb-5" appearance="fill">
                                <mat-label>
                                    {{ 'CatalogSetting.SelectColumn' | translate }}
                                </mat-label>
                                <mat-select formControlName="fkIIdColumn">
                                    <mat-option *ngFor="let opcion of listColumn" [value]="opcion.id">
                                        {{ opcion.vNameColumn }}
                                    </mat-option>
                                </mat-select> <mat-error
                                    *ngIf="_utilsSvc.isControlHasError(form, 'fkIIdColumn', 'required')">
                                    {{ "ThisFieldIsRequired" | translate }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </ng-container>

                    <div class="row">
                        <h6 class="col-md-12">
                            <mat-icon class="click" matTooltipPosition="right"
                              matTooltip="{{ 'CatalogSetting.MessagesCatalogo.YouNeedSave' | translate }}">help_outline</mat-icon>
                        </h6>
                        <div class="col-md-12">
                            <button *ngIf="!isEdit" type="button" class="w-auto" (click)="saveColumn()" mat-raised-button
                                color="primary">
                                {{ 'CatalogSetting.Save' | translate }}
                                <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
                            </button>
                            <button *ngIf="isEdit" type="button" mat-raised-button type="button" color="primary"
                                (click)="updateColumn()">
                                {{ 'CatalogSetting.Update' | translate }}
                                <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
                            </button>
                        </div>
                    </div>

                    <div class="col-12 col-md-12">
                        <mat-checkbox formControlName="bIsChildren" class="my-4">
                          {{ 'CatalogSetting.HasRelationship' | translate }}
                        </mat-checkbox>
                    </div>

                    <ng-container *ngIf="form.get('bIsChildren')?.value">
                        <div class="col-6 col-md-6">
                            <mat-form-field class="w-100 mb-5" appearance="fill">
                                <mat-label>
                                  {{ 'CatalogSetting.Sheet' | translate }}
                                </mat-label>
                                <mat-select formControlName="fkIIdSheetRelation">
                                    <mat-option *ngFor="let opcion of listSheets" [value]="opcion.id" (onSelectionChange)="getDatalistColumnFilter('R',opcion.id)">
                                        {{ opcion.nameSheet }}
                                    </mat-option>
                                </mat-select> <mat-error
                                    *ngIf="_utilsSvc.isControlHasError(form, 'fkIIdSheetRelation', 'required')">
                                    {{ "ThisFieldIsRequired" | translate }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="col-6 col-md-6">
                            <mat-form-field class="w-100 mb-5" appearance="fill">
                                <mat-label>
                                  {{ 'CatalogSetting.Column' | translate }}
                                </mat-label>
                                <mat-select formControlName="fkIIdColumnRelation">
                                    <mat-option *ngFor="let opcion of listColumnRelation" [value]="opcion.id">
                                        {{ opcion.vNameColumn }}
                                    </mat-option>
                                </mat-select> <mat-error
                                    *ngIf="_utilsSvc.isControlHasError(form, 'fkIIdColumnRelation', 'required')">
                                    {{ "ThisFieldIsRequired" | translate }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <button type="button" class="w-auto" (click)="addRelation()" mat-raised-button
                                    color="primary">
                                    {{ 'CatalogSetting.Add' | translate }}
                                    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
                                </button>
                            </div>
                        </div>
                        <div>
                            <div class="mt-5">
                                <app-table [displayedColumns]="estructChildrenRelationTable"
                                    [data]="dataChildrenRelation"
                                    (iconClick)="controllerChildrenRelation($event)"></app-table>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </form>
        </ng-container>

        <ng-container customButtonRight>
            <button type="button" mat-raised-button type="button" color="primary" (click)="save()">
                {{ 'Add' | translate }}
                <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
            </button>
        </ng-container>
    </app-modal2>
</ng-template>
