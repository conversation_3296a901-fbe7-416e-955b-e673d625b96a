import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { ChooseFilterFormModuleComponent } from 'src/app/shared/components/choose-filter-form-module/choose-filter-form-module.component';
import { AllFormsComponent } from 'src/app/advisor-portal/modules/dashboard/forms-configuration/all-forms/all-forms.component';
import { WizardFilterFormModuleSettingModel } from 'src/app/shared/models/modules-setting/wizard-filter-form-module-setting';
import {  LangChangeEvent,  TranslateModule,  TranslateService,
} from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
@Component({
  selector: 'app-filter-form-module',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    ChooseFilterFormModuleComponent,
    AllFormsComponent,TranslateModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './filter-form-module.component.html',
  styleUrls: ['./filter-form-module.component.scss'],
})
export class FilterFormModuleComponent {
  dataFilterFormModuleSetting!: WizardFilterFormModuleSettingModel;
  isForm: boolean = false;
  idStage: number = 0;

  constructor(public _translateService: TranslateService) {}
  validForm(event: boolean) {}

  saveDataFilter(event: any) {
    this.clear();
    if (event != undefined) {
      if(event.idStage != undefined){        
          this.dataFilterFormModuleSetting = event;
          this.idStage = event.idStage;
          this.isForm = true;        
      }      
    }
  }

  clear(){
    this.idStage = 0;
    this.isForm = false;
  }
}
