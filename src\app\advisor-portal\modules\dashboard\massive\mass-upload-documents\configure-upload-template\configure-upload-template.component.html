<!-- Plantillas de carga-->
<div class="row">
    <h5 class="title mb-1">{{ 'UploadDocuments.LoadingTemplates' | translate }}</h5>
    <p class="description">{{ 'UploadDocuments.LoadingTemplatesDecription' | translate }}</p>
</div>


<div class="row">
    <!-- datatable plantillas configuradas -->
    <div class="row mt-2">
        <app-table [displayedColumns]="estructTableTemplate" [data]="dataTableTemplate"
            (iconClick)="controller($event)"></app-table>
    </div>
</div>

<div class="">
    <button (click)="openModal('selectDocumentUploadTypeModal')" class=" mr-2 mt-1" type="button" color="primary"
        mat-raised-button>{{ "Add" | translate }}</button>
</div>

<div class="d-flex justify-content-center mt-3">
    <a class="label-button" mat-button (click)="goBack()"><span>{{"Back" | translate}}</span>
        <mat-icon fontIcon="arrow_back"></mat-icon>
    </a>
</div>

<!-- modal Escoger tipo de carga de documento -->
<ng-template #selectDocumentUploadTypeModal>
    <app-modal2 [titleModal]="modalTitle" [showCancelButtonBelow]="false">
        <ng-container body>
            <form [formGroup]="form">
                <div class="row">
                    <!-- Seleccione el tipo de carga de documento -->
                    <div class="col-md-12 mb-2">
                        <mat-form-field appearance="outline" class="select-look m-auto w-100">
                            <mat-label>
                                {{ "UploadDocuments.SelectTypeDocumentUpload" | translate }}
                            </mat-label>
                            <mat-select formControlName="idManagementType">
                                <mat-option *ngFor="let load of typeOfLoad" [value]="load.pkIIdManagementType">
                                    {{ load.vName }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div class="row">
                    <!-- Nombre de plantilla -->
                    <div class="col-md-12 col-sm-12 mb-2">
                        <mat-form-field appearance="outline" class="w-100">
                            <mat-label> {{ "UploadDocuments.TemplateName" | translate }} </mat-label>
                            <input matInput formControlName="templateName" PreventionSqlInjector />
                        </mat-form-field>
                    </div>
                </div>

                <ng-container *ngIf="idManagementType === 7">
                    <div class="row mb-3">
                        <h4 class="title"> {{"Ubicación de los documentos" | translate }} </h4>
                    </div>

                    <div class="row">
                        <!-- Proceso -->
                        <div class="col-md-6">
                            <mat-form-field id="fkIdProcess" class="w-100">
                                <mat-label>
                                    {{ "AnsConfiguration.Table.Process" | translate }}
                                </mat-label>
                                <mat-select formControlName="fkIdProcess">
                                    <mat-option *ngFor="let item of processes" [value]="item.pkIIdProcessFamily">{{
                                        item.vNameProcess }}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                        <!-- Producto -->
                        <div class="col-md-6">
                            <mat-form-field id="fkIdProduct" class="w-100">
                                <mat-label>
                                    {{ "AnsConfiguration.Table.Product" | translate }}
                                </mat-label>
                                <mat-select formControlName="fkIdProduct">
                                    <mat-option *ngFor="let item of products" [value]="item.pkIIdProductModule">{{
                                        item.vName }}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Etapa -->
                        <div class="col-md-6">
                            <mat-form-field id="stage" class="w-100">
                                <mat-label>
                                    {{ "AnsConfiguration.Table.Stage" | translate }}
                                </mat-label>
                                <mat-select formControlName="fkIdStage">
                                    <mat-option *ngFor="let item of stage" [value]="item.pkIIdStage">{{
                                        item.vNameStage }}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>

                        <!-- Estado -->
                        <div class="col-md-6">
                            <mat-form-field id="state" class="w-100">
                                <mat-label> {{ "Status" | translate }} </mat-label>
                                <mat-select formControlName="fkIdState">
                                    <mat-option *ngFor="let item of state" [value]="item">{{
                                        item.vState }}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <mat-radio-group aria-label="Select an option" formControlName="taskRadio">
                            <!-- Tabla de documentos de tarea -->
                            <mat-radio-button value="documentTable">{{"UploadDocuments.TaskDocumentTable" |
                                translate}}</mat-radio-button>
                            <!-- Campo(s) específico -->
                            <mat-radio-button value="field">{{"UploadDocuments.SpecificField(s)" |
                                translate}}</mat-radio-button>
                        </mat-radio-group>

                    </div>

                    <ng-container *ngIf="taskRadio === 'field'">
                        <div class="row">
                            <!-- Seleccione el campo del documento-->
                            <div class="col-md-12 mb-2">
                                <mat-form-field appearance="outline" class="select-look m-auto w-100">
                                    <mat-label>
                                        {{ "UploadDocuments.SelectTheDocumentField" | translate }}
                                    </mat-label>
                                    <mat-select formControlName="idFieldModule">
                                        <mat-option *ngFor="let field of fieldDocumentList"
                                            [value]="field.pkIIdFieldModule">
                                            {{ field.vNameField }}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                    </ng-container>

                    <ng-container *ngIf="taskRadio === 'documentTable'">
                        <div class="row">
                            <!-- Seleccione el campo del documento -->
                            <div class="col-md-12 mb-2">
                                <mat-form-field appearance="outline" class="select-look m-auto w-100">
                                    <mat-label>
                                        {{ "UploadDocuments.SelectTheDocumentField" | translate }}
                                    </mat-label>
                                    <mat-select formControlName="idTableModule">
                                        <mat-option *ngFor="let field of fieldDocumentList"
                                            [value]="field.pkIIdTableModule">
                                            {{ field.vName }}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                    </ng-container>

                    <!-- Sobrescribir documentos exisitentes -->
                    <div class="row mt-3 mx-1">
                        <div class="form-check d-flex col-12">
                            <input class="form-check-input" type="checkbox" id="check1"
                                formControlName="overwriteDocuments" />
                            <label class="form-check-label" for="flexCheckDefault">
                                {{ "UploadDocuments.OverwriteDocument" | translate }}
                            </label>
                        </div>
                    </div>
                </ng-container>

                <ng-container *ngIf="idManagementType === 8">
                    <div class="row mb-3">
                        <!-- Selección de póliza -->
                        <h4 class="title"> {{"UploadDocuments.PolicySelection" | translate }} </h4>
                    </div>
                    <div class="row">
                        <!-- ID WTW -->
                        <div class="col-md-12 col-sm-12 mb-2">
                            <mat-form-field class="w-100">
                                <mat-label>
                                    {{ "ID WTW" | translate }}
                                </mat-label>
                                <input (keyup.enter)="getPolicyById(keyword)" [(ngModel)]="keyword"
                                    [ngModelOptions]="{standalone: true}" matInput type="text" class="form-control"
                                    placeholder="{{ 'ID WTW' | translate }}" PreventionSqlInjector />
                                <mat-icon class="hand click" (click)="getPolicyById(keyword)"
                                    matSuffix>search</mat-icon>
                            </mat-form-field>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <!-- datatable plantillas configuradas -->
                        <div class="row mt-2">
                            <app-table [displayedColumns]="estructTablePolicy" [data]="dataTablePolicy"></app-table>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Seleccione el campo de la póliza-->
                        <div class="col-md-12 mb-2">
                            <mat-form-field appearance="outline" class="select-look m-auto w-100">
                                <mat-label>
                                    {{ "UploadDocuments.SelectThePolicyField" | translate }}
                                </mat-label>
                                <mat-select formControlName="idFieldPolicy">
                                    <mat-option *ngFor="let field of policyFieldList" [value]="field.pkIIdField">
                                        {{ field.vNameField }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Sobrescribir documentos exisitentes -->
                    <div class="row mt-3 mx-1">
                        <div class="form-check d-flex col-12">
                            <input class="form-check-input" type="checkbox" id="check1"
                                formControlName="overwriteDocuments" />
                            <label class="form-check-label" for="flexCheckDefault">
                                {{ "UploadDocuments.OverwriteDocument" | translate }}
                            </label>
                        </div>
                    </div>
                </ng-container>
            </form>
        </ng-container>

        <!-- Botones-->
        <ng-container customButtonRight>
            <a class="label-button mt-1 mx-3" mat-button (click)="closeModal()"><span>{{"Close"
                    | translate}}</span>
            </a>
            <button class="w-auto mx-3" type="button" mat-raised-button color="primary" (click)="save()"
                [disabled]="form.invalid">
                {{ "Save" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>