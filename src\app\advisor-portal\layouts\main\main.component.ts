import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { HeaderComponent } from '../shared/header/header.component';
import { NavbarComponent } from '../shared/navbar/navbar.component';
import { FooterComponent } from '../shared/footer/footer.component';
import { HeaderTestComponent } from '../shared/header-test/header-test.component';
import { SizeService } from 'src/app/shared/services/nav-bar/size.service';

@Component({
  selector: 'app-main',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeaderComponent,
    NavbarComponent,
    FooterComponent,
    HeaderComponent
  ],
  templateUrl: './main.component.html',
  styleUrls: ['../shared/platform.scss']
})
export class MainComponent implements OnInit{
  isNavbarExpanded: boolean = false;
  constructor(
    private _sizeService: SizeService
  ){}
  
  ngOnInit(){
    this._sizeService.size$.subscribe((size) => {
      if (size) {
        this.isNavbarExpanded = size.width >= 100;
      }
    });
  }

}
