import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    MatTooltipModule,
    MatIconModule,
    BreadcrumbComponent
  ],
  template: `
  <div class="title">
    <h2 class="h3">
      <img src="assets/img/layouts/config_ico.svg" alt="" /> 
      {{"configLogin.LoginSettings" | translate}}
      <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'configLogin.LoginSettings' | translate }}">help_outline</mat-icon>
    </h2>
  </div>
  <app-breadcrumb [breadcrumbSections]="sections" ></app-breadcrumb>

  <router-outlet></router-outlet>
  `,    
  styleUrls: []
})
export class LoginComponent {
  constructor(
    private _translateService: TranslateService,
  ) {}
  
  inicio: string = this._translateService.instant("Configuración")
  login: string = this._translateService.instant("configLogin.portalClient")

  sections: {label: string, link: string}[]=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.login, link: '/dashboard/Configlogin'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Configuración")
      this.login = this._translateService.instant("configLogin.portalClient")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.login
    });
  }
}
