import { Component, OnInit } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';

import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FileService } from 'src/app/shared/services/file/file.service';

@Component({
  selector: 'app-all-composite-catalog',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    TableComponent,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
  ],
  templateUrl: './all-composite-catalog.component.html',
  styleUrls: ['./all-composite-catalog.component.scss'],
})
export class AllCompositeCatalogComponent implements OnInit {
  idCatalog: number = 0;
  idBusinessCountry: number = 0;
  idCountry: number = 0;
  catalogTable: any[] = [];
  originalArray: any[] = [];

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('CatalogSetting.Catalog'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('CatalogSetting.LastUpdate'),
      columnValue: 'dDateModification',
    },
    {
      columnLabel: this._translateService.instant('CatalogSetting.Responsible'),
      columnValue: 'nameUserResponsable',
    },
    {
      columnLabel: this._translateService.instant('Download'),
      columnValue: 'download',
      columnIcon: 'download',
      hidenIcon: (row: any) => row.fkIIdUploadFile == null
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  constructor(
    private _catalogService: CatalogSettingService,
    private _translateService: TranslateService,
    public _utilsSvc: UtilsService,
    private _settingService: SettingService,
    public _router: Router,
    private _activatedRoute: ActivatedRoute,
    public _location: Location,
    private _msgSvc: MessageService,
    private _customRouter: CustomRouterService,
    private _fileService: FileService
  ) {}

  ngOnInit(): void {
    this.validateAction();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table catalog composite
      this.estructTable[0].columnLabel = this._translateService.instant(
        'CatalogSetting.Catalog'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'CatalogSetting.LastUpdate'
      );
      this.estructTable[2].columnLabel = this._translateService.instant(
        'CatalogSetting.Responsible'
      );

      this.estructTable[3].columnLabel =
        this._translateService.instant('Delete');
      this.estructTable[4].columnLabel =
        this._translateService.instant('Modify');
    });
  }

  validateAction() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessCountry || params.idCountry) {
        this.idCatalog = 0;
        this.idBusinessCountry = params.idBusinessCountry;
        this.idCountry = params.idCountry;
        this.getListCatalog(this.idBusinessCountry, this.idCountry);
      }
    });
  }

  getListCatalog(idBusinessCountry: number, idCountry: number) {
    this._catalogService
      .getListCatalogComposite(idBusinessCountry, idCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.catalogTable = response.result;
          }
        }
      });
  }

  base64toBlob(base64Data: string) {
    const byteCharacters = atob(base64Data.split(',')[1]);
    const arrayBuffer = new ArrayBuffer(byteCharacters.length);
    const uint8Array = new Uint8Array(arrayBuffer);
    for (let i = 0; i < byteCharacters.length; i++) {
      uint8Array[i] = byteCharacters.charCodeAt(i);
    }
    return new Blob([arrayBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }); // Cambia el tipo según el tipo de imagen
  }

  async controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'modify':
        if (this.idCountry > 0) {
          this._customRouter.navigate([
            `/dashboard/catalog-setting/editManagement/${
              event.value.pkIIdCatalog
            }/${0}/${this.idCountry}`,
          ]);
        } else {
          this._customRouter.navigate([
            `/dashboard/catalog-setting/editManagement/${
              event.value.pkIIdCatalog
            }/${this.idBusinessCountry}/${0}`,
          ]);
        }
        break;
      case 'delete':
        this._msgSvc
          .messageConfirmationAndNegation(
            this._translateService.instant('CatalogSetting.MessagesCatalogo.DeleteItem'),
            this._translateService.instant(
              'CatalogSetting.MessagesCatalogo.FunctionalityCatalog'
            ),
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.deleteCatalogComposite(event.value.pkIIdCatalog);
            }
          });
        break;
      case 'download':
        let imgBase64 = await this._fileService.getImage(event.value.fkIIdUploadFile)
        const blob = this.base64toBlob(imgBase64);
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = '';
        a.click();
        break;
      default:
        break;
    }
  }

  createNew() {
    if (this.idCountry > 0) {
      this._customRouter.navigate([
        `/dashboard/catalog-setting/newManagement/${0}/${this.idCountry}`,
      ]);
    } else {
      this._customRouter.navigate([
        `/dashboard/catalog-setting/newManagement/${
          this.idBusinessCountry
        }/${0}`,
      ]);
    }
  }

  deleteArrayOriginal(id: number) {
    this.originalArray = this.originalArray.filter(
      (objeto) => objeto.id !== id
    );

    const index = this.catalogTable.findIndex((a) => a.id === id);
    this.catalogTable.splice(index, 1);
    this.catalogTable = this.catalogTable.slice();
  }

  /// Delete catalog by id
  deleteCatalogComposite(idCatalog: number) {
    if (idCatalog > 0) {
      this._catalogService
        .deleteCatalogComposite(idCatalog)
        .pipe(
          catchError((error) => {
            if (
              error.error.error &&
              error.error.message === 'hasAssociatedItems'
            ) {
              this._msgSvc.messageInfo(
                this._translateService.instant(
                  'CatalogSetting.MessagesCatalogo.deletedMessageTitle'
                ),
                this._translateService.instant(
                  'CatalogSetting.MessagesCatalogo.deletedMessageSubTitle'
                )
              );
            } else {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageInfo(
                this._translateService.instant('ThereWasAError'),
                resp.message
              );
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant('DeleteMessage')
              );
              this.deleteArrayOriginal(idCatalog);
              this.getListCatalog(this.idBusinessCountry, this.idCountry);
            }
          }
        });
    }
  }
}
