import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [CommonModule, RouterModule,TranslateModule, BreadcrumbComponent],
  template: `
  <div>
    <h2 class="h3">
      <img src="assets/img/layouts/report.svg" alt="" /> 
      {{"Reports.Title" | translate}}
    </h2>
  </div>
  <router-outlet></router-outlet>
`,
  styles: []
})
export class ReportsComponent implements OnInit{
  constructor(
    private _translateService: TranslateService,
  ) {}

  inicio: string = this._translateService.instant("Inicio")  
  reports: string = this._translateService.instant("Reports.Title")

  sections: {label: string, link: string}[]=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.reports, link: '/dashboard/reports'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Inicio")
      this.reports = this._translateService.instant("Reports.Title")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.reports
    });
  }
}
