import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { MatSelectModule } from '@angular/material/select';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import {
  INewReportProcesoModel,
  INewReportProductoModel,
} from 'src/app/shared/models/reports';
import { catchError, finalize, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { StageModel } from 'src/app/shared/models/module';
import { ActivatedRoute } from '@angular/router';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { DragDropUploadComponent } from 'src/app/shared/components/drag-drop-upload/drag-drop-upload.component';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { MatDialog } from '@angular/material/dialog';
import { MatRadioModule } from '@angular/material/radio';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FilterTaskTrayModel } from 'src/app/shared/models/task';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PageEvent } from '@angular/material/paginator';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { GetTemplateStateModel, SaveMassiveStateModel } from 'src/app/shared/models/massive';
@Component({
  selector: 'app-mass-creation-state',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    DragDropUploadComponent,
    MatRadioModule,
    MatCheckboxModule,
    Modal2Component,
    TableComponent,
    MatNativeDateModule,
    MatDatepickerModule,
  ],
  providers: [DatePipe],
  templateUrl: './mass-creation-state.component.html',
  styleUrls: ['./mass-creation-state.component.scss']
})
export class MassCreationStateComponent {

  //Variables para el formulario de filtro.
  formFilter: FormGroup = new FormGroup({});
  formFilterModal: FormGroup = new FormGroup({});

  //Variables para el formulario de cambio de estado.
  formChangeState: FormGroup = new FormGroup({});

  validDepdenciesStage: boolean = false;
  validDepdenciesState: boolean = false;
  validCommunicationState: boolean = false;

  listProcess: INewReportProcesoModel[] = [];
  listProduct: INewReportProductoModel[] = [];
  stageList: StageModel[] = [];
  statusList: any[] = [];
  statusListChange: any[] = [];
  statusListModal: any[] = [];
  idBusinessByCountry: number = 0;
  idUser: number = 0;
  showFileUpload: boolean = false;
  fileName: string = '';
  templateName: string = 'Creación_masiva_estado';
  uploadedFile: File[] = [];
  template!: File;
  idProductModule: number = 0;
  listUserAsigment: any[] = [];
  listState: any[] = [];
  idsTask: number[] = [];
  fieldsTask: any;
  filterRequest: FilterTaskTrayModel = {
    idTarea: 0,
    idStateModule: 0,
    idAssignedUser: 0,
    idStage: 0,
    idProcess: 0,
    idProduct: 0,
    idRequestingUser: 0,
    jsonString: '',
    startDate: '',
    endDate: '',
    from: 1,
    pageSize: 5,
  };

  //Variables relacioandas con la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;
  currentPosition: number = 0;

  //Variables para filtrar la data de la tabla.
  keyword: string = '';
  dataTableState: any[] = [];

  //Variables relacionadas con los modales.
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;

  estructTableState: BodyTableModel[] = [
    {
      columnLabel: ' ',
      columnValue: '1',
      check: true
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.TaskId'
      ),
      columnValue: 'Id_de_tarea',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.Process'
      ),
      columnValue: 'Proceso',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.ProductType'
      ),
      columnValue: 'Tipo_de_producto',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.Stage'
      ),
      columnValue: 'Stage',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.User'
      ),
      columnValue: 'Creador_de_tarea',
    },
    // {
    //   columnLabel: this._translateService.instant(
    //     'Aseguradora'
    //   ),
    //   columnValue: '',
    // },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.AssignedTo'
      ),
      columnValue: 'Asignado_A',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.CreationDate'
      ),
      columnValue: 'd_DateCreation',
    },
    {
      columnLabel: this._translateService.instant(
        'Status'
      ),
      columnValue: 'Estado',
      functionValue: (item: any) => this.utilsService.changeStatusTaskValue(item)
    },
  ];

  constructor(
    private _parametersService: ParametersService,
    private _fb: FormBuilder,
    private _msgSvc: MessageService,
    private _activatedRoute: ActivatedRoute,
    private _translateService: TranslateService,
    private _roleService: RoleService,
    private _moduleService: ModuleService,
    public utilsService: UtilsService,
    private _customeRouter: CustomRouterService,
    private _fileService: FileService,
    private _userService: UserService,
    public matDialog: MatDialog,
    private _taskTayConfigService: TaskTayConfigService,
    private _messageService: MessageService,
    private _spinner: SpinnerService,
    private _transactionService: TransactionService,
    private _utilsService: UtilsService,
    private _spinnerService: SpinnerService,
  ) {
    this.getBusinessByCountry();
  }

  async ngOnInit() {
    this.getAllProcess();
    this.initFormFilter();
    this.initFormChangeState();
    this.initFormFilterModal();
    this.idUser = await this._userService.getUserIdSesion();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTableState[1].columnLabel = this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.TaskId'
      );
      this.estructTableState[2].columnLabel = this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.Process'
      );
      this.estructTableState[3].columnLabel = this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.ProductType'
      );
      this.estructTableState[4].columnLabel = this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.Stage'
      );
      this.estructTableState[5].columnLabel = this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.User'
      );
      this.estructTableState[6].columnLabel = this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.AssignedTo'
      );
      this.estructTableState[7].columnLabel = this._translateService.instant(
        'BulkUpload.Massives.MassCreationStates.CreationDate'
      );
      this.estructTableState[8].columnLabel = this._translateService.instant(
        'Status'
      );
    });
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
    });
  }

  initFormChangeState() {
    this.formChangeState = this._fb.group({
      isChangeInformation: [false],
      fkIdStage: [{ value: null, disabled: false }, Validators.required],
      fkIdState: [{ value: null, disabled: false }, Validators.required],
      isDisabledCommunications: [false],
      isDisabledDependencies: [false]
    });


    this.formChangeState.get('fkIdStage')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formChangeState.get('fkIdState')?.setValue(null);
        this.statusListChange = [];
        this.getStagByStateAssociateToFormByIdStage(value, false);
      }
    });

    this.formChangeState.get('isChangeInformation')?.valueChanges.subscribe((value) => {
      if (value === true) {
        if (this.uploadedFile.length > 0) {
          this.formChangeState.setErrors({ invalidUpload: true });
        } else {
          this.formChangeState.setErrors(null);
        }
      } else {
        this.formChangeState.setErrors(null); // Asegura que se limpien errores si se desactiva la opción
      }
    });

  }

  initFormFilterModal(){
    this.formFilterModal = this._fb.group({
      idState: [],
      IdAssignedUser: [],
      StartDate: [],
      EndDate: []
    });
  }

  initFormFilter() {
    this.formFilter = this._fb.group({
      fkIIdprocess: [],
      fkIIdProduct: [],
      fkIdStage: [{ value: null }],
      fkIdState: [{ value: null }],
      IdAssignedUser: [],
      StartDate: [],
      EndDate: []
    });

    this.formFilter.get('fkIIdprocess')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formFilter.get('fkIIdProduct')?.setValue(null);
        this.formFilter.get('fkIdStage')?.setValue(null);
        this.formFilter.get('fkIdState')?.setValue(null);
        this.listProduct = [];
        this.stageList = [];
        this.statusList = [];
        let fileNull!: File;
        this.template = fileNull;
        this.uploadedFile = [];
        this.getProductsList(value, this.idBusinessByCountry);
      }
    });

    this.formFilter.get('fkIIdProduct')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formFilter.get('fkIdStage')?.setValue(null);
        this.formFilter.get('fkIdState')?.setValue(null);
        this.stageList = [];
        this.statusList = [];
        let fileNull!: File;
        this.template = fileNull;
        this.uploadedFile = [];
        this.getStageByIdProductModule(value);
      }
    });

    this.formFilter.get('fkIdStage')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formFilter.get('fkIdState')?.setValue(null);
        this.statusList = [];
        this.getStagByStateAssociateToFormByIdStage(value);
      }
    });
  }

  //Obtiene la lista de Procesos.
  getAllProcess() {
    this._parametersService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant(error.error.message)
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.listProcess = resp.result;
        }
      });
  }

  //obtiene los productos registrados por proceso y idBusinessCountry
  getProductsList(idProcess: number, idBusinessByCountry: number) {
    this._roleService
      .getProductsByIdProcess(idProcess, idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant(error.error.message)
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.listProduct = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageInfo(
              this._translateService.instant('ThereWasAError'),
              resp.message
            );
          } else {
            this.listProduct = resp.result;
          }
        }
      });
  }

  //obtiene la lista de etapas asignadas a un producto.
  getStageByIdProductModule(pkIIdProductModule: number) {
    if (pkIIdProductModule) {
      this._moduleService
        .getStageByIdProductModule(pkIIdProductModule)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.result != undefined) {
              if (resp.result.length > 0) {
                this.stageList = resp.result;
                this.formFilter
                  .get('fkIdStage')
                  ?.setValue(this.stageList[0].pkIIdStage);
              }
            }
          }
        });
    }
  }

  //obtiene la lista de estados asignados a una etapa.
  getStagByStateAssociateToFormByIdStage(idStage: number, isFilter: boolean = true) {
    if (idStage > 0) {
      this._moduleService
        .getStagByStateAssociateToFormByIdStage(idStage)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.result != undefined) {
              if (resp.result.length > 0) {

                if (isFilter) {
                  this.statusList = resp.result;
                  this.listState = resp.result;
                  this.statusListModal = resp.result;
                  this.formFilter
                    .get('fkIdState')
                    ?.setValue(this.statusList[0].pkIIdStageByState);
                  this.pageIndex = 0;
                  this.idProductModule = this.formFilter.value.fkIIdProduct;

                  this.filterRequest.idStateModule = this.formFilter.value.fkIdState;
                  this.filterRequest.idProcess = this.formFilter.value.fkIIdprocess;
                  this.filterRequest.idStage = this.formFilter.value.fkIdStage;

                  this.getStateFilter(this.filterRequest, this.idProductModule, this.pageIndex, this.pageSize)
                  this.getCustomer(this.filterRequest, this.idProductModule, this.pageIndex, this.pageSize);

                } else {
                  this.statusListChange = resp.result;
                }


              }
            }
          }
        });
    }
  }

  //Función que abre el modal para filtrar de filtros.
  openFilterDialog() {
    const dialogRef = this.matDialog.open(this.filtersModal!, {
      width: '30vw',
      maxHeight: '90vh',
    });
    this.initFormFilterModal();

  }

  closeModal() {
    this.matDialog.closeAll();
  }

  //obtiene la lista de tareas por idProductModule
  async getStateFilter(
    filterForm: FilterTaskTrayModel,
    idProductModule: number,
    from?: number,
    pageSize?: number
  ) {
    this._spinnerService.showChanges(true);

    this._taskTayConfigService
      .GetMyTaskConfigFilterCustomerMassiveState(
        filterForm,
        idProductModule,
        this.idBusinessByCountry,
        false
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this.dataTableState = [];
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          this._spinner.hide();
          this._spinnerService.showChanges(false);
          return of([]);
        }),
        finalize(() => {
          this._spinner.hide();
          this._spinnerService.showChanges(false);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this._messageService.messageWaring(
            this._translateService.instant('WarningMessage'),
            this._translateService.instant('TaskTray.Messages.NoDataProduct')
          );
          this.dataTableState = [];
        } else {
          this.dataTableState = resp.result;
          if(this.formFilterModal.value.IdAssignedUser != null ){

              this.dataTableState = (resp.result as any[])
              .filter((item: any) => this.formFilterModal.value.IdAssignedUser.includes(item.IdResponsableFirst?.toString())) // Filtra las coincidencias
              .map((item: any) => {
                return {
                  ...item,
                  Fecha_de_creación: item.Fecha_de_creación ? this.formatDate(item.Fecha_de_creación) : '',
                  d_DateCreation: item.d_DateCreation ? this.formatDate(item.d_DateCreation) : '',
                };
              });

          }else{
            this.dataTableState = resp.result.map((item: any) => {
              return {
                ...item,
                Fecha_de_creación: item.Fecha_de_creación ? this.formatDate(item.Fecha_de_creación) : '',
                d_DateCreation: item.d_DateCreation ? this.formatDate(item.d_DateCreation) : '',
              };
            });
          }

          this.getFieldsTask();
          this.closeModal();
          this.amountRows = resp.rowCount;

        }
      });
  }

  //obtiene la lista de tareas por idProductModule
  async getCustomer(
    filterForm: FilterTaskTrayModel,
    idProductModule: number,
    from?: number,
    pageSize?: number
  ) {

    const filter: FilterTaskTrayModel = {
      idTarea: 0,
      idStateModule: this.formFilter.value.fkIdState,
      idAssignedUser: 0,
      idProcess: this.formFilter.value.fkIIdprocess,
      idProduct: 0,
      idRequestingUser: 0,
      jsonString: '',
      startDate: '',
      endDate: '',
      from: 1,
      pageSize: 30,
      idStage: this.formFilter.value.fkIdStage
    }
    this._spinnerService.showChanges(true);
    this._taskTayConfigService
      .GetMyTaskConfigFilterCustomerMassiveState(
        filter,
        idProductModule,
        this.idBusinessByCountry,
        false
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this.dataTableState = [];
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          this._spinnerService.showChanges(false);
          return of([]);
        }),
        finalize(() => {
          this._spinnerService.showChanges(false);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this._messageService.messageWaring(
            this._translateService.instant('WarningMessage'),
            this._translateService.instant('TaskTray.Messages.NoDataProduct')
          );

        } else {

          const uniqueTaskDetails = Array.from(
            new Set(
              resp.result
                .filter((resp: any) => resp.Asignado_A && resp.IdResponsableFirst)
                .map((resp: any) =>
                  JSON.stringify({
                    asignado: resp.Asignado_A,
                    idResponsableFirst: resp.IdResponsableFirst
                  })
                )
            )
          ).map((item: any) => JSON.parse(item));

          this.listUserAsigment = uniqueTaskDetails;
          filterForm.pageSize = 5;
        }
      });
  }

  formatDate(dateStr: string): string {
    if (!dateStr) return '';

    // Separar la parte de fecha y hora
    const [datePart, timePart, meridian] = dateStr.split(' ');
    const [month, day, year] = datePart.split('/').map(Number); // Convertir a números
    const [hours, minutes, seconds] = timePart.split(':').map(Number);

    // Convertir a formato de 24 horas
    let hours24 = hours;
    if (meridian === 'PM' && hours !== 12) {
      hours24 += 12;
    } else if (meridian === 'AM' && hours === 12) {
      hours24 = 0;
    }

    // Crear objeto Date
    const date = new Date(year, month - 1, day, hours24, minutes, seconds);

    // Formatear manualmente la salida
    const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;

    return formattedDate;
  }

  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 1;
    }
    this.filterRequest.from = this.pageIndex + 1;
    this.filterRequest.pageSize = this.pageSize;
    this.getStateFilter(
      this.filterRequest,
      this.idProductModule,
      this.pageIndex + 1,
      this.pageSize
    );
  }

  cleanfilterRequest() {
    this.pageIndex = 0;
    this.filterRequest = {
      idTarea: 0,
      idStateModule: 0,
      idAssignedUser: 0,
      idProcess: 0,
      idProduct: 0,
      idRequestingUser: 0,
      jsonString: '',
      startDate: '',
      endDate: '',
      from: 1,
      pageSize: 5,
    };
  }


  //Función que se ejecuta al dar enter o click en el icono de busqueda, del input buscar.
  search(event: any) {
    this.filterRequest.keyword = this.keyword;
    this.filterRequest.pageSize = 5;
    this.filterRequest.idStateModule = this.formFilter.value.fkIdState;
    this.filterRequest.idProcess = this.formFilter.value.fkIIdprocess;
    this.filterRequest.idStage = this.formFilter.value.fkIdStage;
    this.getStateFilter(this.filterRequest, this.idProductModule);
  }

  cleanFilterForm() {
    this.formFilterModal.reset();
    this.listUserAsigment = [];
    this.statusListModal = [];
    this.filterRequest = {
      idTarea: 0,
      idStateModule: this.formFilter.value.fkIdState,
      idAssignedUser: 0,
      idProcess: this.formFilter.value.fkIIdprocess,
      idProduct: 0,
      idRequestingUser: 0,
      jsonString: '',
      startDate: '',
      endDate: '',
      from: 1,
      pageSize: 5,
      idStage: this.formFilter.value.fkIdStage
    };
    this.getStateFilter(this.filterRequest, this.idProductModule);
  }

  //Función que aplica los filtros seleccionados en el modal de filtros generales.
  applyFilters() {
    this.pageIndex = 0;
    if(this.formFilterModal.value.IdAssignedUser !=null && this.formFilterModal.value.IdAssignedUser.length  > 0 ){
      this.filterRequest = {
        idTarea: this.formFilterModal.value.idState ?? 0,
        idStateModule: this.formFilter.value.fkIdState,
        idAssignedUser: 0,
        idProcess: this.formFilter.value.fkIIdprocess,
        idProduct: 0,
        idRequestingUser: 0,
        jsonString: '',
        startDate: this.formFilterModal.value.StartDate,
        endDate: this.formFilterModal.value.EndDate,
        from: 1,
        pageSize: 50,
        idStage: this.formFilter.value.fkIdStage
      };
    }else{
      this.filterRequest = {
        idTarea: this.formFilterModal.value.idState ?? 0,
        idStateModule: this.formFilter.value.fkIdState,
        idAssignedUser: this.formFilterModal.value.IdAssignedUser ?? 0,
        idProcess: this.formFilter.value.fkIIdprocess,
        idProduct: 0,
        idRequestingUser: 0,
        jsonString: '',
        startDate: this.formFilterModal.value.StartDate,
        endDate: this.formFilterModal.value.EndDate,
        from: 1,
        pageSize: 5,
        idStage: this.formFilter.value.fkIdStage
      };
    }
    this.getStateFilter(this.filterRequest, this.idProductModule);
  }


  //Controlador para las acciones de la tabla.
  controllerAns(event: IconEventClickModel) {
    if (event.value === 'ALL') {
      this.dataTableState.forEach((element: any) => {
        this.addOrRemoveAns(element.fk_i_IdTask, true);
      });
    } else if (event.value === 'NONE') {
      this.idsTask = [];
    } else if (event.value.fk_i_IdTask) {
      this.addOrRemoveAns(event.value.fk_i_IdTask);
    }
  }

  //Añade o elimina el id de un ANS a un array, según sea el caso.
  addOrRemoveAns(idGeneralAns: number, allValue?: boolean): void {
    const index = this.idsTask.indexOf(idGeneralAns);
    if (index === -1) {
      this.idsTask.push(idGeneralAns);
    } else {
      //Validamos que no esté agregando todos los valores por medio del check de seleccionar todos los ans en la tabla, para evitar que borre id que ya estén previamente agregados.
      if (!allValue) {
        this.idsTask.splice(index, 1);
      }
    }
  }

  getFieldsTask() {
    const idState = this.formFilter.value.fkIdState;
    this._moduleService
      .GetFieldsTask(idState)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result != undefined) {
            this.fieldsTask = resp.result;
          }
        }
      });
  }

  validFormChange() {
    if (this.formChangeState.value.isChangeInformation) {
      if (this.template && this.uploadedFile.length > 0) {
        const fileToCompare: File[] = [this.template, this.uploadedFile[0]];
        const payload = new FormData();

        fileToCompare.forEach((element) => {
          payload.append(`Files`, element);
        });
        this.comparerExcelFiles(payload);
      }
    } else {
      this.validConfirmMessage();
    }
  }

  async validConfirmMessage() {
    try {
      await this.validateStateAndStageDependecies();
      if (!this.formChangeState.value.isDisabledDependencies && this.validDepdenciesStage) {
        const resultDependencies = await this._messageService.messageConfirmationAndNegation(
          this._translateService.instant('BulkUpload.Massives.MassCreationStates.SomeStagesAndStatusesAreDependent'),
          this._translateService.instant('BulkUpload.Massives.MassCreationStates.DoYouWantToDisableDependencies'),
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        );

        if (resultDependencies) {

        } else {
          return; // Detener la secuencia si el usuario cancela
        }
      }

      if (!this.formChangeState.value.isDisabledCommunications  && this.validCommunicationState) {
        const resultCommunications = await this._messageService.messageConfirmationAndNegation(
          this._translateService.instant('BulkUpload.Massives.MassCreationStates.SomeStagesAndStatusesGenerateCommunications'),
          this._translateService.instant('BulkUpload.Massives.MassCreationStates.DoYouWantToDisableCommunications'),
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        );

        if (resultCommunications) {

        } else {
          return; // Detener la secuencia si el usuario cancela
        }
      }

      const resultStateChange = await this._messageService.messageConfirmationAndNegation(
        this._translateService.instant('BulkUpload.Massives.MassCreationStates.DoYouWantToConfirmStateChange'),
        this._translateService.instant('BulkUpload.Massives.MassCreationStates.VerifyThatTheInformationIsCorrect'),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      );

      if (resultStateChange) {

        if(this.formChangeState.value.isChangeInformation){
          this.uploadMassivePolicyTask();
        }else{
          this.changeStateMassive()
        }

      }
    } catch (error) {
      console.error("Se produjo un error en la secuencia de confirmaciones:", error);
    }
  }

  comparerExcelFiles(files: FormData) {
    this._fileService
      .comparerExcelFiles(files)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe(async (resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.length > 0) {
            this._msgSvc.messageErrorCustomer(
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadTitle'
              ),
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadSubtitle'
              )
            );
          } else {
            this.validConfirmMessage();
            // await this.uploadMassivePolicyTask();
          }
        }
      });
  }

  async changeStateMassive() {


    const additionalData = {
      idStateModule: this.formChangeState.value.fkIdState,
      disableCommunications: this.formChangeState.value.isDisabledCommunications,
      disableDependecies: this.formChangeState.value.isDisabledDependencies
    };

    const request: SaveMassiveStateModel = {
      idUser: this.idUser,
      idBusinessByCountry: this.idBusinessByCountry,
      vtextCaptured: JSON.stringify(this.fieldsTask),
      jsonAditionalData: JSON.stringify(additionalData),
      idTasks: this.idsTask
    }

    await this._spinner.show();
    this._transactionService
      .saveBulkLoadTaskStateMasive(
        request
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          this._spinner.hide();
          return of([]);
        }),
        finalize(() => {
          this._spinner.hide();
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this._messageService.messageWaring(
            this._translateService.instant('WarningMessage'),
            this._translateService.instant('TaskTray.Messages.NoDataProduct')
          );
        } else {

          if (!this.formChangeState.value.isChangeInformation) {
            this._messageService.messageConfirmatio(
              this._translateService.instant('BulkUpload.Massives.MassCreationStates.TaskStatusModified'),
              '',
              'success',
              this._translateService.instant('Continue')
            );
            this.filterRequest = {
              idTarea: 0,
              idStateModule: this.formFilter.value.fkIdState,
              idAssignedUser: 0,
              idProcess: this.formFilter.value.fkIIdprocess,
              idProduct: 0,
              idRequestingUser: 0,
              jsonString: '',
              startDate: '',
              endDate: '',
              from: 1,
              pageSize: 5,
              idStage: this.formFilter.value.fkIdStage
            };
            this.getStateFilter(this.filterRequest, this.idProductModule);
            this.formChangeState.reset();
          }

        }
      });
  }

  async validateStateAndStageDependecies() {
    await this.getChildrenStateByIdStateParent(this.formChangeState.value.fkIdState);
    await this.getChildrenStageByIdStageParent(this.formChangeState.value.fkIdStage);
    await this.getCommunicationByIdState(this.formChangeState.value.fkIdState);
  }

  //obtiene los estados dependientes por idState
  async getChildrenStateByIdStateParent(idStateParent: number) {

    this._moduleService
      .getChildrenStateByIdStateParent(idStateParent)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.validDepdenciesState = resp.length > 0;
        } else {
          this.validDepdenciesState = false;
        }
      });
  }

  //obtiene las estapas dependientes por idStage
  async getChildrenStageByIdStageParent(idStageParent: number) {
    return new Promise((resolve, reject) => {
      this._moduleService
        .getChildrenStageByIdStageParent(idStageParent)
        .pipe(
          catchError((error) => {
            if (error?.error?.message) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }

            this.validDepdenciesStage = false;
            resolve(false);
            return of(null);
          })
        )
        .subscribe((resp: ResponseGlobalModel | null) => {
          if (resp && Array.isArray(resp.result)) {
            this.validDepdenciesStage = resp.result.length > 0;
            resolve(true);
          } else {
            this.validDepdenciesStage = false;
            resolve(false);
          }
        });
    });
  }

  //obtiene las comunicación por estado
  async getCommunicationByIdState(idState: number) {
    this._moduleService
      .getCommunicationByIdState(idState)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.validCommunicationState = resp.length > 0;
        } else {
          this.validCommunicationState = false;
        }
      });
  }

  //Función que se encarga de llamar a la función que devuelve el base64 con el template.
  downloadTemplate() {

    const idStageByState = this.formChangeState.get('fkIdState')?.value;
    const request: GetTemplateStateModel = {
      idStageByState: idStageByState,
      isTask: true,
      idTask: this.idsTask
    }

    if (idStageByState > 0) {
      this._moduleService
        .getFieldByStateWithIdTask(request)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.result != undefined) {
              this.downloadTemplateByBase64(resp.result, this.templateName);
              const file = this._utilsService.base64ToFile(
                resp.result,
                this.templateName
              );
              if (file) {
                this.template = file;
              }
            }
          }
        });
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant(
          'BulkUpload.Massives.MassCreationTasks.MandatoryFiltersTitle'
        ),
        this._translateService.instant(
          'BulkUpload.Massives.MassCreationTasks.MandatoryFiltersSubtitle'
        )
      );
    }
  }

  //Elimina el file cargado.
  deleteFile() {
    this.uploadedFile = [];
    this.fileName = '';
  }

  //Detecta los eventos de componente de cargue de archivos.
  getFiles(files: File[]) {
    if (this._utilsService.validateFileSize(files[0].size, 25)) {
      this.uploadedFile = files;
      if (this.uploadedFile.length > 0) {
        this.fileName = this.uploadedFile[0].name;
      }
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant('MaximumFileSizeTitle'),
        this._translateService.instant('MaximumFileSizeSubtitle')
      );
    }
  }

  //Función que descarga une xcel, basado en un base64.
  downloadTemplateByBase64(base64Data: string, fileName: string) {
    // Convierte el base64 a un Blob
    const byteCharacters = atob(base64Data); // Decodifica base64
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    // Crea un enlace de descarga
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = fileName; // El nombre que tendrá el archivo descargado
    link.click();
  }


  //Guarda la carga masiva en la BD.
  async uploadMassivePolicyTask() {
    await this._spinner.show();
    const payload = new FormData();
    let jsonAditional: any = {
      idStateModule: this.formChangeState.get('fkIdState')?.value,
      disableCommunications: this.formChangeState.value.isDisabledCommunications,
      disableDependecies: this.formChangeState.value.isDisabledDependencies
    };

    payload.append(`IdManagementType`, (6).toString());
    payload.append(`IdUser`, this.idUser.toString());
    payload.append(`File`, this.uploadedFile[0]);
    payload.append(`JsonAditional`, JSON.stringify(jsonAditional));
    this._spinner.show();
    this._fileService
      .uploadMassiveState(payload)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        }),
        finalize(() => {
          this._spinner.hide();
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            this._msgSvc
              .messageConfirmatioCustomer(
                `${this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadTitle'
                )} ${resp.result}`,
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadSubtitle'
                ),
                'success',
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadBtnLeft'
                ),
                this._translateService.instant('Continue')
              )
              .then((result) => {
                if (result) {
                  this._customeRouter.navigate([
                    `dashboard/massive/load-viewer/${this.idBusinessByCountry}`,
                  ]);
                }
              });
          }
        }
      });
  }

  goBackMassive() {
    this._customeRouter.navigate([`dashboard/massive`]);
  }
}
