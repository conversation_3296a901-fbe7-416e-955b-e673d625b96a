<form [formGroup]="configuration" *ngIf="!isAddorModyReport">
  <div class="row mb-3 mt-3">
    <!--  Reportes Configurados -->
    <h3 class="col-md-12">
      {{ "Reports.Utils.ConfiguredReports" | translate }}
    </h3>
  </div>

  <!-- Tabla Reportes Configurados  -->
  <div class="row mt-3">
    <div class="col-md-12">
      <app-table [displayedColumns]="estructTable" [data]="dataTableReport"
        (iconClick)="controllerReportsGenerated($event)"></app-table>
      <button class="mb-3" type="button" mat-raised-button color="primary" (click)="open('AddReport');">
        {{ "Add" | translate }} {{" "}} {{ "+" }}
      </button>
    </div>
  </div>
</form>

<form [formGroup]="fromConfiguration" *ngIf="isAddorModyReport">
  <div class="row mt-5">

    <!-- tipo de reporte => porudcto - modulo -->
    <div class="row mt-3 mb-3" *ngIf="tipoReporte == 0">
      <mat-radio-group aria-label="Select an option" formControlName="bIsModule">

        <mat-radio-button
          #radioForProduct
          (change)="radioForChange(radioForProduct.value)"
          value="0"
        >
          Producto
        </mat-radio-button>

        <mat-radio-button
          #radioForModule
          (change)="radioForChange(radioForModule.value)"
          value="1"
          
        >
          Módulo
        </mat-radio-button>

      </mat-radio-group>
    </div>


    <div class="col-md-6 col-sm-12 mb-3">
      <!-- Nombre del reporte -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>
          {{ "MyProfile.Name" | translate }}
        </mat-label>
        <input matInput formControlName="vName" PreventionSqlInjector />
        <mat-error *ngIf="utilsSvc.isControlHasError(fromConfiguration, 'vName', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
        <mat-error *ngIf="fromConfiguration.get('vName')?.hasError('nameExists')">
          {{ "NameAlreadyExistsError" | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-md-6 col-sm-12 mb-3">
      <!-- Descripción del reporte -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>
          {{ "Group.Description" | translate }}
        </mat-label>
        <input matInput formControlName="vDescription" PreventionSqlInjector />
        <mat-error *ngIf="utilsSvc.isControlHasError(configuration, 'vDescription', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-md-6 col-sm-12 mb-3" *ngIf="forModules && tipoReporte == 0">
      <!-- Proceso  modulo-->
      <mat-form-field class="w-100">
        <mat-label>{{ "Reports.NewReport.Process" | translate }}</mat-label>
        <mat-select formControlName="fkIIdprocess" required="true">
          <mat-option *ngFor="let option of listProcess" [value]="option.pkIIdProcessFamily">{{ option.vNameProcess
            }}</mat-option>
        </mat-select>
        <mat-error *ngIf="fromConfiguration.get('fkIIdprocess')?.hasError('required')">{{ "ThisFieldIsRequired" |
          translate
          }}</mat-error>
      </mat-form-field>

    </div>

    <div class="col-md-6 col-sm-12 mb-3" *ngIf="tipoReporte == 1">
      <!-- Aseguradora -->
      <mat-form-field class="w-100">
        <mat-label>{{ "Reports.Insurance" | translate }}</mat-label>
        <mat-select formControlName="fkIIdInsurance" required="true">
          <mat-option *ngFor="let option of insurers" [value]="option.pkIIdInsuranceCompanies">{{ option.vName
            }}</mat-option>
        </mat-select>
        <mat-error *ngIf="fromConfiguration.get('fkIIdInsurance')?.hasError('required')">{{ "ThisFieldIsRequired" |
          translate
          }}</mat-error>
      </mat-form-field>

    </div>
    <div class="col-md-6 col-sm-12 mb-3" *ngIf="tipoReporte == 1">
      <!-- Aseguradora -->
      <mat-form-field class="w-100">
        <mat-label>{{ "Reports.Product" | translate }}</mat-label>
        <mat-select formControlName="fkIIdProductInsurance" required="true">
          <mat-option *ngFor="let option of productsByInsurerCompany" [value]="option.pkIIdProduct">{{ option.vProductName
            }}</mat-option>
        </mat-select>
        <mat-error *ngIf="fromConfiguration.get('fkIIdProductInsurance')?.hasError('required')">{{ "ThisFieldIsRequired" |
          translate
          }}</mat-error>
      </mat-form-field>
    </div>

    <div class="col-md-6 col-sm-12" *ngIf="forModules && tipoReporte == 0">
      <!-- Producto modulo -->
      <mat-form-field class="w-100">
        <mat-label>{{ "Reports.NewReport.Product" | translate }}</mat-label>
        <mat-select formControlName="fkIIdProduct" multiple>
          <mat-option *ngFor="let option of listProduct" [value]="option">{{ option.vName
            }}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div *ngIf="!forModules">

      <div class="col-md-6 col-sm-12">
        <!-- Producto -->
        <mat-form-field class="w-100 mb-2">
          <mat-label>
            {{ "Reports.Product" | translate }}
          </mat-label>
          <mat-select formControlName="fkIIdProductUnique">
            <mat-option
              *ngFor="let item of allProductsList"
              [value]="item.pkIIdProduct"
              >{{ item.vProductName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

  </div>

  <!-- Filtros de reporte  -->
  <div class="row mb-3 mt-3">
    <h3 class="col-md-12">
      <!-- {{ "Reports.Utils.ReportFilters" | translate }} -->

      <h4 class="mt-4 mb-0">{{ "Reports.TitleFieldReports" | translate }}</h4>
      <p class="descrption">{{ "Reports.DescriptionFieldReports" | translate }}</p>

    </h3>
  </div>

  <div class="row">
    <div class="col-md-9">
      <!-- Seleccionar campo estandar para añadir -->
      <mat-form-field class="w-100">
        <mat-label>{{ "Reports.Utils.selectField" | translate }}</mat-label>
        <mat-select formControlName="fkIdFieldStandar">
          <mat-option *ngFor="let option of dataStaticTableReportFieldStandard" [value]="option.value">{{
            option.vFieldNameStandard }}</mat-option>
        </mat-select>
        <mat-error *ngIf="fromConfiguration.get('fkIdFieldStandar')?.hasError('required')">{{ "ThisFieldIsRequired" |
          translate }}</mat-error>
      </mat-form-field>
    </div>

    <div class="col-md-3">
      <div class="text-center mb-2">
        <!-- Agregar campo estandar -->
        <button [disabled]="fromConfiguration.get('fkIdFieldStandar')?.value < 1" (click)="addStandardFields()"
          class="btn-custom w-75" type="button" mat-raised-button>
          <!-- <strong>{{ "Reports.NewReport.AddField" | translate }}</strong> -->
          <strong>{{ "Reports.AddStandardField" | translate }}</strong>
          <mat-icon iconPositionEnd class="ml-1">add</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Tabla de campos estandar -->
  <div class="row mb-5">
    <div class="col-12 col-md-12">
      <div class="row mb-2">
      </div>
      <app-table-drag (iconClick)="controllerStandardFields($event)" [displayedColumns]="estructTableField"
        [data]="dataTableReportFieldStandard"></app-table-drag>
    </div>
    <span class="cont-msg-info">
      {{"Reports.Utils.MsgInfoTable" | translate}}
    </span>
  </div>


  <!-- Sección campos dinámicos -->
  <section class="mt-2">
    <ng-template #editTemplate>
      <h3>{{ "Reports.NewReport.EditReport" | translate }}</h3>
    </ng-template>

    <form [formGroup]="fromConfiguration" class="mt-8">
      <div class="row mb-3">
        <!-- Dettalle de reporte -->
        <div class="col-md-12">
          <h4 class="mt-4 mb-0">{{ "Reports.NewReport.ReportDetail" | translate }}.</h4>
          <p class="descrption">{{ "Reports.DetailsReportConfiguration" | translate }}</p>
        </div>
      </div>
      <div class="row mb-3">
        <!-- Activar Detalle Reportes -->
        <div class="col-md-12">
          <mat-checkbox formControlName="CheckDetaillReport">{{"Reports.Utils.ActivateDetailReports" |
            translate}}</mat-checkbox>
        </div>
      </div>
      <div class="row mb-3" *ngIf="isEditDetailReport && forModules && tipoReporte == 0">
        <!-- Etapa -->
        <div class="col-md-6 col-sm-12">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.Stage" | translate }}</mat-label>
            <mat-select formControlName="fkIdStage" required="true">
              <mat-option *ngFor="let option of stageList" [value]="option">{{ option.vNameStage }}</mat-option>
            </mat-select>
            <mat-error *ngIf="fromConfiguration.get('fkIdStage')?.hasError('required')">{{ "ThisFieldIsRequired" |
              translate
              }}</mat-error>
          </mat-form-field>
        </div>
        <!-- Estado -->
        <div class="col-md-6 col-sm-12">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.State" | translate }}</mat-label>
            <mat-select formControlName="fkIdState" required="true">
              <mat-option *ngFor="let option of statusListCopyParameters" [value]="option">{{
                option.vState }}</mat-option>
            </mat-select>
            <mat-error *ngIf="fromConfiguration.get('fkIdState')?.hasError('required')">{{ "ThisFieldIsRequired" |
              translate
              }}</mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="row mb-3" *ngIf="isEditDetailReport">
        <div class="row">
          <!-- Seleccionar campo dinámico para añadir -->
          <div class="col-md-9">
            <mat-form-field class="w-100">
              <mat-label>{{ "Reports.NewReport.Field" | translate }}</mat-label>
              <mat-select formControlName="fkIdField" required="true" (selectionChange)="onFieldChange($event.value)">
                <mat-option *ngFor="let option of listField" [value]="option">{{ option.vNameField }}</mat-option>
              </mat-select>
              <mat-error *ngIf="fromConfiguration.get('fkIdField')?.hasError('required')">{{ "ThisFieldIsRequired" |
                translate }}</mat-error>
            </mat-form-field>
          </div>
          <!-- Añadir campo dinámico -->
          <div class="col-md-3">
            <div class="text-center mb-2">
              <button (click)="addDynamicFields('fkIdField','fkIdStage','fkIdState', 1)" class="btn-custom w-75" type="button" mat-raised-button>
                <strong>{{ "Reports.AddDynamicField" | translate }}</strong>
                <mat-icon iconPositionEnd class="ml-1">add</mat-icon>
              </button>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <!-- Tabla campos dinámicos-->
            <app-table-drag *ngIf="isEditDetailReport" (iconClick)="controllerFieldsDinamic($event)"
              [displayedColumns]="estructNewReportsTable" [data]="dataTableReportFieldDinamic"></app-table-drag>
          </div>
        </div>
        <span class="cont-msg-info">
          {{"Reports.Utils.MsgInfoTable" | translate}}
        </span>
      </div>
    </form>
  </section>





  <!-- CONFIGURACION FILTROS ADICIONALES -->
  <section class="mt-2">

    <form [formGroup]="fromConfiguration" class="mt-8">
      <div class="row mb-3">
        <!-- Dettalle de reporte -->
        <div class="col-md-12">
          <h4 class="mt-4 mb-0">{{ "Reports.TitleFilterReport" | translate }}</h4>
          <p class="descrption">{{ "Reports.DescriptionFilterReport" | translate }}</p>
        </div>
      </div>
      <div class="row mb-3">
        <!-- Activar Detalle Reportes -->
        <div class="col-md-3">
          <mat-checkbox formControlName="CheckFilterReport">{{ "Reports.ActiveFilterReport" | translate }}</mat-checkbox>
        </div>
        <div class="col-md-3">
          <mat-checkbox formControlName="CheckFilterDateReport">{{ "Reports.ActiveFilterDateReport" | translate }}</mat-checkbox>
        </div>
        <div class="col-md-3" *ngIf="tipoReporte == 0 && forModules">
          <mat-checkbox formControlName="CheckFilterRoleUserReport">{{ "Reports.ActiveFilterTypesUsersReport" | translate }}</mat-checkbox>
        </div>
      </div>

      <div class="row mb-3" *ngIf="forModules && tipoReporte == 0 && (fromConfiguration.get('CheckFilterReport')?.value || fromConfiguration.get('CheckFilterDateReport')?.value)">
        <!-- Etapa -->
        <div class="col-md-6 col-sm-12">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.Stage" | translate }}</mat-label>
            <mat-select formControlName="fkIdStageFilter" required="true">
              <mat-option *ngFor="let option of stageList" [value]="option">{{ option.vNameStage }}</mat-option>
            </mat-select>
            <mat-error *ngIf="fromConfiguration.get('fkIdStageFilter')?.hasError('required')">{{ "ThisFieldIsRequired" |
              translate
              }}</mat-error>
          </mat-form-field>
        </div>
        <!-- Estado -->
        <div class="col-md-6 col-sm-12">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.State" | translate }}</mat-label>
            <mat-select formControlName="fkIdStateFilter" required="true">
              <mat-option *ngFor="let option of statusListCopyParameters" [value]="option.pkIIdStageByState">{{
                option.vState }}</mat-option>
            </mat-select>
            <mat-error *ngIf="fromConfiguration.get('fkIdStateFilter')?.hasError('required')">{{ "ThisFieldIsRequired" |
              translate
              }}</mat-error>
          </mat-form-field>
        </div>
      </div>

      <!-- Activar configuración de filtros -->
      <div class="row mb-3" *ngIf="isEditFilterReport">
        <div class="row">
          <!-- Seleccionar campo para añadir -->
          <div class="col-md-9">
            <mat-form-field class="w-100">
              <mat-label>{{ "Reports.SelectFilterToAdd" | translate }}</mat-label>
              <mat-select formControlName="fkIdFieldFilter" required="true" (selectionChange)="onFieldChange($event.value)">
                <mat-option *ngFor="let option of listFieldFilter" [value]="option">{{ option.vNameField }}</mat-option>
              </mat-select>
              <mat-error *ngIf="fromConfiguration.get('fkIdFieldFilter')?.hasError('required')">{{ "ThisFieldIsRequired" |
                translate }}</mat-error>
            </mat-form-field>
          </div>

          <!-- Añadir campo filtro -->
          <div class="col-md-3">
            <div class="text-center mb-2">
              <button (click)="addDynamicFields('fkIdFieldFilter','fkIdStage','fkIdState', 2)" class="btn-custom w-75" type="button" mat-raised-button>
                <strong>{{ "Reports.AddFilter" | translate }}</strong>
                <mat-icon iconPositionEnd class="ml-1">add</mat-icon>
              </button>
            </div>
          </div>

        </div>


        <div class="row">
          <div class="col-md-12">
            <!-- Tabla campos filtro-->
            <app-table-drag *ngIf="isEditFilterReport" (iconClick)="controllerFieldsFilterDinamic($event)"
              [displayedColumns]="estructNewReportsTable" [data]="dataTableReportFieldFilterDinamic"></app-table-drag>
          </div>
        </div>
        <span class="cont-msg-info">
          {{"Reports.Utils.MsgInfoTable" | translate}}
        </span>

      </div>


      <!-- Activar configuración de filtros tipo fecha-->
      <div class="row mb-3" *ngIf="isEditFilterDateReport">
        <div class="row">

          <!-- Seleccionar campo para añadir tipo fecha-->
          <div class="col-md-9">
            <mat-form-field class="w-100">
              <mat-label>{{ "Reports.AddFilterDate" | translate }}</mat-label>
              <mat-select formControlName="fkIdFieldFilterDate" required="true" (selectionChange)="onFieldChange($event.value)">
                <mat-option *ngFor="let option of listFieldFilterDate" [value]="option">{{ option.vNameField }}</mat-option>
              </mat-select>
              <mat-error *ngIf="fromConfiguration.get('fkIdFieldFilterDate')?.hasError('required')">{{ "ThisFieldIsRequired" |
                translate }}</mat-error>
            </mat-form-field>
          </div>

          <!-- Añadir campo filtro tipo fecha-->
          <div class="col-md-3">
            <div class="text-center mb-2">
              <button (click)="addDynamicFields('fkIdFieldFilterDate','fkIdStage','fkIdState', 3)" class="btn-custom w-75" type="button" mat-raised-button>
                <strong>{{ "Reports.AddDateFieldFilter" | translate }}</strong>
                <mat-icon iconPositionEnd class="ml-1">add</mat-icon>
              </button>
            </div>
          </div>

        </div>


        <div class="row">
          <div class="col-md-12">
            <!-- Tabla campos filtro-->
            <app-table-drag *ngIf="isEditFilterDateReport" (iconClick)="controllerFieldsFilterDateDinamic($event)"
              [displayedColumns]="estructNewReportsTable" [data]="dataTableReportFieldFilterDateDinamic"></app-table-drag>
          </div>
        </div>
        <span class="cont-msg-info">
          {{"Reports.Utils.MsgInfoTable" | translate}}
        </span>

      </div>

      <div *ngIf="fromConfiguration.get('CheckFilterRoleUserReport')?.value">
        <div class="row mb-3 mt-3">
          <h3 class="col-md-12">
            {{ "Reports.Utils.AdditionalFilters" | translate }}
          </h3>
        </div>
        <div class="row">
          <div class="col-md-2 mb-2">
            <mat-checkbox formControlName="CheckGroup">{{"MyQuotation.AllQuotation.Group" |translate}}</mat-checkbox>
          </div>
          <div class="col-md-2 mb-2">
            <mat-checkbox formControlName="CheckRol">{{"User.DataTableColumns.Role" | translate}}</mat-checkbox>
          </div>
          <div class="col-md-2 mb-2">
            <mat-checkbox formControlName="CheckUser">{{"MyQuotation.AllQuotation.User" | translate}}</mat-checkbox>
          </div>
        </div>
      </div>
      <div class="d-flex align-items-center justify-content-center mb-5">
        <!-- Botón Cancelar -->
        <div class="mx-1">
          <button (click)="cancel()" type="button" mat-raised-button color="">
            {{ "Reports.NewReport.Cancel" | translate }}
          </button>
        </div>
        <!-- Botón Generar reporte -->
        <div class="mx-1">
          <button
            [ngClass]="!(dataTableReportFieldDinamic.length + dataTableReportFieldStandard.length > 0) ? 'disabled' : ''"
            (click)="generateReport()"
            [disabled]="!(dataTableReportFieldDinamic.length + dataTableReportFieldStandard.length > 0)" type="button"
            mat-raised-button color="primary">
            {{ titleBtngenerateReport | translate }}
            <mat-icon class="ml-1">area_chart</mat-icon>
          </button>

        </div>
      </div>
      
    </form>
  </section>

</form>