<header>
  <div class="language">
    <div>
      <div class="dropdown">
        <!-- TRADUCCION -->
        <div class="row">
          <div class="col-3">
            <span>
              <img src="assets/img/layouts/world.svg" alt="" />
            </span>  
          </div>
          <div class="col-8">
            <select 
            
              class="form-control shadow-none" 
              style="border-color: black;"
              #selectedLang
              (change)="switchLang(selectedLang.value)"
            >
              <option *ngFor="let language of _translateService.getLangs()"
                [value] = "language"
                [selected] = "language === _translateService.currentLang"
              >
                {{language.toUpperCase()}}
              </option>
            </select>
          </div>
        </div>

      </div>
    </div>
  </div>
  <div class="user">
    <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
      <span>
        <img src="assets/img/layouts/perfil.svg" alt="" />
      </span>
    </button>
    <ul class="dropdown-menu">
      <li>
        <a class="dropdown-item" (click)="logout();" >{{'Header.LogOut'| translate}}</a>
      </li>
    </ul>
  </div>
</header>