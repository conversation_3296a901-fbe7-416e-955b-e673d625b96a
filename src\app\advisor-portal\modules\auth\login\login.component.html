<section class="row container d-flex flex-wrap align-items-stretch">
  <div class="col-12 col-md-6">
    <form [formGroup]="form" (ngSubmit)="login()">
      <legend class="mb-3">
        {{'Auth.LogIn' | translate}}
      </legend>
      <mat-label class="label-Email">
        {{'Auth.Email' | translate}}
      </mat-label>

      <mat-form-field appearance="outline" class="w-100 mb-2 ">

        <input class="input-Email" matInput placeholder="<EMAIL>" formControlName="email" required />
        <mat-error *ngIf="utilsService.isControlHasError(form, 'email', 'required')">
          {{'ThisFieldIsRequired' | translate}}
        </mat-error>
        <mat-error *ngIf="utilsService.isControlHasError(form, 'email', 'pattern')">
          {{'ThisEmailIsNotValid' | translate}}
        </mat-error>
      </mat-form-field>

      <mat-label class="label-Email">
        {{'Auth.Password' | translate}}
      </mat-label>
      <mat-form-field appearance="outline" class="w-100 mb-2">

        <input matInput placeholder="{{'Auth.Password' | translate}}" formControlName="password" required
          type="password" />
        <mat-error *ngIf="utilsService.isControlHasError(form, 'password', 'required')">
          {{'ThisFieldIsRequired' | translate}}
        </mat-error>
        <mat-error *ngIf="utilsService.isControlHasError(form, 'password', 'pattern')">
          {{'ThisPasswordIsNotValid' | translate}}
        </mat-error>
      </mat-form-field>

      <mat-checkbox formControlName="rememberMe">Recordarme</mat-checkbox>
      <a (click)="goToRecoverPassword()" [routerLink]="['#']"   class="link mb-2">
        {{'Auth.ForgotPassword' | translate}}
      </a>
      <div class="col-12 mt-2 d-flex justify-content-center">
        <ngx-recaptcha2 #captchaElem [siteKey]="siteKey" formControlName="recaptcha"></ngx-recaptcha2>

      </div>
      <div class="col-12 mt-2 d-flex justify-content-center">
        <button class="btn btn-primary w-auto mr-2 fire" mat-button color="primary" type="submit" [disabled]="!valid">
          {{'Auth.LogIn' | translate}}
        </button>
        <!-- <a class="btn btn-primary fire" (click)="login()">Ingresar</a> -->
      </div>
      <span class="top"><img src="assets/img/img_form_top.svg" alt="" /></span>
      <span class="bottom"><img src="assets/img/img_form_bottom.svg" alt="" /></span>
    </form>
  </div>
  <div class="col-12 col-md-6 d-inline-flex justify-content-end" style="padding: 0">
    <div class="explanation">
      <img src="assets/img/img_login.png" alt="" width="100%" />
      <div>
        <h1>
          {{'Auth.AdvisorsPortal' | translate}}
        </h1>
        <p>{{'Auth.QuickAndEasyAccessTo' | translate}}</p>
        <ul>
          <li>{{'Auth.CustomerInquiry' | translate}}</li>
          <li>{{'Auth.ConsultationProductManagement' | translate}}</li>
          <li>{{'Auth.ToolsThatImproveYourProcesses' | translate}}</li>
        </ul>
      </div>
    </div>
  </div>
</section>