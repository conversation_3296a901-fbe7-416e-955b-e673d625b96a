import { CommonModule, Location } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { OptionModel } from 'src/app/shared/models/options/optionmodel';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BodyTableModel } from 'src/app/shared/models/table';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { BeneficiariesTableComponent } from '../../shared/components/beneficiaries-table/beneficiaries-table.component';
import { DetailsPolicyComponent } from '../../shared/components/details-policy/details-policy.component';
import { DetailsRiskModificationsComponent } from '../../shared/components/details-risk-modifications/details-risk-modifications.component';
import { HistoryTableComponent } from '../../shared/components/history-table/history-table.component';
import {
  BeneficiaryModel,
  CreateHistoryRiskModel,
  CreateUpdateBeneficiariesModel,
  HistoricalRiskModificationsModel,
  PolicyMovementHistoryModel,
  RequesGetDataFormModel,
} from '../../shared/models';
import { PageSeeDetailMovementHistoryIndividualPolicyComponent } from '../page-see-detail-movement-history-individual-policy/page-see-detail-movement-history-individual-policy.component';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';

@Component({
  selector: 'app-page-see-details-individual-policy',
  standalone: true,
  imports: [
    CommonModule,
    DetailsPolicyComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    BeneficiariesTableComponent,
    PdfViewerModule,
    HistoryTableComponent,
    MatCardModule,
    Modal2Component,
    DetailsRiskModificationsComponent,
    PageSeeDetailMovementHistoryIndividualPolicyComponent,
    MatSelectModule,
    MatSlideToggleModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule
  ],
  templateUrl: './page-see-details-individual-policy.component.html',
  styleUrls: ['./page-see-details-individual-policy.component.scss'],
})
export class PageSeeDetailsIndividualPolicyComponent implements OnInit {
  //Variables relacionadas con los modales.
  @ViewChild('viewHistoricalRiskDetails')
  viewHistoricalRiskDetails?: TemplateRef<any>;
  @ViewChild('viewBeneficiariesModal')
  viewBeneficiariesModal?: TemplateRef<any>;
  @ViewChild('viewHistoryMovementModal')
  viewHistoryMovementModal?: TemplateRef<any>;
  @ViewChild('selectModificationModal')
  selectModificationModal?: TemplateRef<any>;
  @ViewChild('modifyPolicyModal')
  modifyPolicyModal?: TemplateRef<any>;
  @ViewChild('viewBeneficiariesToEditModal')
  viewBeneficiariesToEditModal?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;
  modalOpenRerefence: string = '';
  typeMovement: string = '';
  modificationTitle: string = this._translateService.instant(
    'Policy.Modification'
  );
  modificationTitleCustom: string = this._translateService.instant('Modify');
  modalTitleRiskModificationHistory: string = this._translateService.instant(
    'Policy.Modification'
  );
  isReadOnlyBeneficiaries: boolean = true;

  //Variables relacionadas con los formularios.
  modificationOptions: OptionModel[] = [];
  modificationType: string = '';
  idBusinessByCountry: number = 0;
  userIdSession: number = 0;
  modifyPremiumValue: boolean = false;
  form: any = {};
  formValid: boolean = false;

  //Variables relacionadas con la póliza.
  idWtw: number = 0;
  YearRenewal: number = 0;
  idParent: number = 0;
  idPolicyRisk: number = 0;
  idPolicyType: number = 0;
  idFilePolicy: number = 0;
  idHistoryPolicy: number = 0;
  policyNumber: string = '';
  policyStatus: string = '';
  customerType: string = '';
  dataForm: string = '';
  fieldKeyName: string = '';
  fieldKeyValue: string = '';
  isRisk: boolean = false;
  pdfSrc: Uint8Array = new Uint8Array();
  showDetailsColumn: boolean = false;
  detailsPolicyData: any[] = [];
  valueInsurance: string = '';
  premium: string = '';
  premiumTotal: string = '';
  dDueDate: string = '';
  beneficiaryId: number = 0;
  modifiedPremium: boolean = false;
  isHistory: boolean = false;
  isUpdate: boolean = false;
  idHistoryPolicyRisk: number = 0;
  maximumNumberBeneficiaries: number = 0;

  //Tabla Beneficiarios
  beneficiariesData: BeneficiaryModel[] = [];
  beneficiariesDataHistory: BeneficiaryModel[] = [];
  beneficiariesDataBpk: BeneficiaryModel[] = [];
  estructTableBeneficiaries: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Policy.NameOfBeneficiary'),
      columnValue: 'nameComplete',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.DocumentType'),
      columnValue: 'typeDocument',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
      columnValue: 'document',
    },
    {
      columnLabel: this._translateService.instant('Policy.Percentage'),
      columnValue: 'percentage',
    },
    {
      columnLabel: this._translateService.instant('Details'),
      columnValue: 'viewDetails',
      columnIcon: 'search',
    },
  ];
  estructTableBeneficiariesModify: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Policy.Beneficiarie'),
      columnValue: 'nameComplete',
    },

    {
      columnLabel: this._translateService.instant(
        'Policy.DivisionByPercentage'
      ),
      columnValue: 'percentage',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  //Tabla Historico de modificaciones de riesgos.
  titleModificationHistoryRisk = this._translateService.instant(
    'Policy.RiskModificationHistory'
  );
  modificationHistoryRiskData: HistoricalRiskModificationsModel[] = [];
  estructTableModificationHistoryRisk: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Policy.ModificationsOf'),
      columnValue: 'customerType',
    },
    {
      columnLabel: this._translateService.instant('Policy.UserWhoUpdates'),
      columnValue: 'user',
    },
    {
      columnLabel: this._translateService.instant('Policy.UpdateDate'),
      columnValue: 'dateUpdate',
      functionValue: (item: HistoricalRiskModificationsModel) => this._utilsSvc.formatDate(item.dateUpdate, 'DD-MM-YYYY'),
    },
    {
      columnLabel: this._translateService.instant(
        'Policy.ModificationOfPremium'
      ),
      columnValue: 'isModifyPremium',
      functionValue: (item: any) => this.modifyPremium(item),
    },
    {
      columnLabel: this._translateService.instant('Details'),
      columnValue: 'detailsRisk',
      columnIcon: 'search',
    },
  ];

  //Tabla Historico de movimientos de póliza.
  titlePolicyMovementHistory: string = this._translateService.instant(
    'Policy.PolicyMovementHistory'
  );
  policyMovementHistoryData: PolicyMovementHistoryModel[] = [];
  estructTablePolicyMovementHistory: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Policy.TypeOfMovement'),
      columnValue: 'movementsPolicyName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyId'
      ),
      columnValue: 'idPolicy',
    },
    {
      columnLabel: this._translateService.instant('Policy.UserWhoUpdates'),
      columnValue: 'user',
    },
    {
      columnLabel: this._translateService.instant('Policy.UpdateDate'),
      columnValue: 'dateUpdate',
      functionValue: (item: PolicyMovementHistoryModel) => this._utilsSvc.formatDate(item.dateUpdate, 'DD-MM-YYYY'),
    },
    {
      columnLabel: this._translateService.instant('Details'),
      columnValue: 'detailsMovement',
      columnIcon: 'search',
    },
  ];
  isDisabledModifyButton: boolean = false;
  isDisabledRenewalButton: boolean = false;
  motive: string = "";

  constructor(
    private _transactionService: TransactionService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _activatedRoute: ActivatedRoute,
    private _fileService: FileService,
    private _utilsSvc: UtilsService,
    private _location: Location,
    private _settingService: SettingService,
    private _fieldService: FieldService,
    private _userService: UserService,
    public modalDialog: MatDialog,
    private _customeRouter: CustomRouterService
  ) {
    this.getDataUrl();
    this.getUserId();
    this.getDataSettingInit();
  }

  ngOnInit() {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //Traducción titulos de tablas.
      this.titleModificationHistoryRisk = this._translateService.instant(
        'Policy.RiskModificationHistory'
      );

      //Traducción de los datos de la tabla Beneficiarios.
      this.estructTableBeneficiaries[0].columnLabel =
        this._translateService.instant('Policy.NameOfBeneficiary');
      this.estructTableBeneficiaries[1].columnLabel =
        this._translateService.instant('MyProfile.DocumentType');
      this.estructTableBeneficiaries[2].columnLabel =
        this._translateService.instant('MyProfile.DocumentNumber');
      this.estructTableBeneficiaries[3].columnLabel =
        this._translateService.instant('Policy.Percentage');
      this.estructTableBeneficiaries[4].columnLabel =
        this._translateService.instant('Details');

      //Traducción de la tabla beneficiarios dentro del modal de modificaciones.
      this.estructTableBeneficiariesModify[0].columnLabel =
        this._translateService.instant('Policy.Beneficiarie');
      this.estructTableBeneficiariesModify[1].columnLabel =
        this._translateService.instant('Policy.DivisionByPercentage');
      this.estructTableBeneficiariesModify[2].columnLabel =
        this._translateService.instant('Modify');
      this.estructTableBeneficiariesModify[3].columnLabel =
        this._translateService.instant('Delete');

      //Traducción de los datos de la tabla Historico de modificaciones de riesgos.
      this.estructTableModificationHistoryRisk[0].columnLabel =
        this._translateService.instant('Policy.ModificationsOf');
      this.estructTableModificationHistoryRisk[1].columnLabel =
        this._translateService.instant('Policy.UserWhoUpdates');
      this.estructTableModificationHistoryRisk[2].columnLabel =
        this._translateService.instant('Policy.UpdateDate');
      this.estructTableModificationHistoryRisk[3].columnLabel =
        this._translateService.instant('Policy.ModificationOfPremium');
      this.estructTableModificationHistoryRisk[4].columnLabel =
        this._translateService.instant('Details');

      //Traducción de los datos de la tabla Historico de movimientos de póliza.
      this.estructTablePolicyMovementHistory[0].columnLabel =
        this._translateService.instant('Policy.TypeOfMovement');
      this.estructTablePolicyMovementHistory[1].columnLabel =
        this._translateService.instant(
          'PolicyConfiguration.GeneralInformation.PolicyId'
        );
      this.estructTablePolicyMovementHistory[2].columnLabel =
        this._translateService.instant('Policy.UserWhoUpdates');
      this.estructTablePolicyMovementHistory[3].columnLabel =
        this._translateService.instant('Policy.UpdateDate');
      this.estructTablePolicyMovementHistory[4].columnLabel =
        this._translateService.instant('Details');

      //Traducción de los titulos de las secciones de los detalles de la póliza.
      this.detailsPolicyData[0].policy[0].nameSection =
        this._translateService.instant('Policy.PolicyData');
      if (this.detailsPolicyData[1].takers) {
        this.detailsPolicyData[1].takers[0].nameSection =
          this._translateService.instant('Policy.TakerData');
      }
      if (this.detailsPolicyData[1].takers) {
        this.detailsPolicyData[2].insurances[0].nameSection =
          this._translateService.instant('Policy.DataSecured');
      }
    });
  }

  //Obtiene el valor de las variables enviadas por medio de la URL.
  getDataUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idPolicyType) {
        this.idPolicyType = Number(params.idPolicyType);
      }

      if (params.idPolicyRisk) {
        this.idPolicyRisk = Number(params.idPolicyRisk);
      }

      if (params.idParent) {
        this.idParent = Number(params.idParent);
      }

      if (params.idPolicy) {
        this.idWtw = Number(params.idPolicy);
        if (this.idWtw > 0) {
          this.getHistoryPolicy();
        }
      }
      if (this.idParent === 0) {
        this.isRisk = true;
        this.getRiskByIdWithProduct();
      } else {
        this.getPolicyByIdWithProduct();
      }
      this.getBeneficiesByIdPolicy();
      this.getHistoryRisk();
      this.getBeneficiariesNumberByIdPolicy(this.idWtw);
    });
  }

  //Obtiene el id del usuario en sesión.
  async getUserId() {
    this.userIdSession = await this._userService.getUserIdSesion();
  }

  //Obtiene el idBusinessByCountry de la empresa país, seleccionada al momeneto de iniciar sesión.
  async getDataSettingInit() {
    let data: any = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = data.idBusinessByCountry;
  }

  //Obtiene el detalle de una póliza.
  getPolicyByIdWithProduct() {
    this._transactionService
      .getPolicyByIdWithProduct(this.idPolicyRisk, this.idPolicyType)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.detailsPolicyData = this.transformDataDetails(resp.result);
          if (resp.result.policy) {
            this.policyNumber = resp.result.policy.policyNumber;
            this.idWtw = resp.result.policy.idwtw;
            this.policyStatus = resp.result.policy.statePolicy;
            this.idFilePolicy = resp.result.policy.idFilePolicy;
            this.fieldKeyName = resp.result.policy.fieldName;
            this.fieldKeyValue = resp.result.policy.fieldKey;
            this.idHistoryPolicy = resp.result.policy.idHistoryForm;
            this.idHistoryPolicyRisk = resp.result.policy.idHistoryForm;
            this.motive =  resp.result.policy.motive;
            if (this.idFilePolicy && this.idFilePolicy > 0) {
              this.getPolicyCover();
            }

            const expiredValue = this._translateService.instant('PolicyReplicationConfiguration.PolicyExpired');
            const cancelleddValue = this._translateService.instant('PolicyReplicationConfiguration.PolicyCancelled');

            switch (this.policyStatus) {
              case expiredValue:
                this.isDisabledModifyButton = true;
                break;
              case cancelleddValue:
                this.isDisabledRenewalButton = true;
                this.isDisabledModifyButton = true;
                break;
            }

          }
        }
      });
  }

  //Obtiene el detalle de un riesgo.
  getRiskByIdWithProduct() {
    this._transactionService
      .getRiskByIdWithProduct(this.idPolicyRisk, this.idPolicyType)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.detailsPolicyData = this.transformDataDetails(resp.result);

          if (resp.result.policy) {
            this.policyNumber = resp.result.policy.policyNumber;
            this.idWtw = resp.result.policy.idwtw;
            this.policyStatus = resp.result.policy.statePolicy;
            this.idFilePolicy = resp.result.policy.idFilePolicy;
            this.fieldKeyName = resp.result.policy.fieldName;
            this.fieldKeyValue = resp.result.policy.fieldKey;
            this.motive =  resp.result.policy.motive;
            if (this.idFilePolicy && this.idFilePolicy > 0) {
              this.getPolicyCover();
            }
          }
        }
      });
  }

  //Obtiene la carátula de una póliza.
  getPolicyCover() {
    this._fileService
      .getUploadFileById(this.idFilePolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant('')
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.imageBase64) {
            this.pdfSrc = this._utilsSvc.base64ToUint8Array(
              resp.result.imageBase64
            );
          }
        }
      });
  }

  //Obtiene los beneficiarios de una póliza.
  getBeneficiesByIdPolicy() {
    this._transactionService
      .getBeneficiesByIdPolicy(this.idPolicyRisk)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.beneficiariesData = resp.result;
          this.beneficiariesDataBpk = resp.result;
        }
      });
  }

  //Obtiene el historial de modificaciones de riesgos de una póliza.
  getHistoryRisk() {
    let queryId: number = 0;
    if (this.idParent > 0) {
      queryId = this.idParent;
    } else {
      queryId = this.idWtw;
    }
    this._transactionService
      .getHistoryRisk(this.idPolicyRisk, queryId)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.modificationHistoryRiskData = resp.result;
        }
      });
  }

  //Obtiene el detalle de una póliza.
  getHistoryPolicy() {
    this._transactionService
      .getHistoryPolicy(this.idWtw)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.policyMovementHistoryData = resp.result;
        }
      });
  }

  // Obtiene el JSON con los valores guardados en el formulario de cada pestaña de la póliza.
  getDataJSON(rquest: RequesGetDataFormModel) {
    this._transactionService
      .getDataJSON(rquest)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            this.dataForm = this.convertToJson(resp.result.item1);
            this.premium = this.formatMoney(
              '',
              false,
              resp.result.item2.premium
            );

            this.valueInsurance = this.formatMoney(
              '',
              false,
              resp.result.item2.valueInsurance
            );

            this.premiumTotal = this.formatMoney(
              '',
              false,
              resp.result.item2.totalPremium
            );

            this.dDueDate = resp.result.item2.dDueDate
          }
        }
      });
  }

  //Obtiene las pestañas asociadas al formulario de una póliza.
  getTabsListByIdPolicy(idPolicyParent: number) {
    this._fieldService
      .getTabsListByIdPolicy(idPolicyParent)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.modificationOptions = resp.result;
        }
      });
  }

  //Obtiene las pestañas asociadas al formulario de una póliza.
  getBeneficiariesByIdHistory(idHistory: number, idPolicyRisk: number) {
    this._transactionService
      .getBeneficiariesByIdHistory(idHistory, idPolicyRisk)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.beneficiariesDataHistory = resp.result;
        }
      });
  }

  //Reemplaza el valor boolean por una cadena SI/NO.
  modifyPremium(item: HistoricalRiskModificationsModel) {
    return item.isModifyPremium ? 'Si' : 'No';
  }

  //Obtiene la data de la acción ver detalles en la tabla de beneficiarios.
  getActionTableBeneficiaries(event: any, seeBeneficiaryOnly = false) {

    switch (event.column) {
      case 'viewDetails':
        if (seeBeneficiaryOnly) {
          this.modalTitleRiskModificationHistory = this._translateService.instant(
            'Policy.BeneficiaryData'
          );
        }
        this.premium = this.formatMoney('', false, event.value.iPremium || '0');
        this.valueInsurance = this.formatMoney(
          '',
          false,
          event.value.iInsuredValue || '0'
        );
        this.isReadOnlyBeneficiaries = true;
        this.idHistoryPolicy = this.idHistoryPolicyRisk;
        this.customerType = 'Beneficiario';
        this.openModal('viewHistoricalRiskDetails');
        this.dataForm = this.convertToJson(event.value.json);
        break;
      case 'modify':
        this.customerType = 'Beneficiario';
        this.modalTitleRiskModificationHistory = this._translateService.instant(
          'Policy.ModifyBeneficiary'
        );
        this.isReadOnlyBeneficiaries = false;
        this.premium = this.formatMoney('', false, event.value.iPremium);
        this.valueInsurance = this.formatMoney(
          '',
          false,
          event.value.iInsuredValue
        );
        this.beneficiaryId = event.value.id;
        this.openModal('viewHistoricalRiskDetails');
        this.isUpdate = true;
        this.dataForm = this.convertToJson(event.value.json);
        break;
      case 'delete':
        this.beneficiaryId = event.value.id;
        this.isUpdate = true;
        this.deleteBeneficiarie();
        break;

      default:
        break;
    }
  }

  //Obtiene la data de la acción ver detalles en la tabla Historico de modificaciones de riesgos de una póliza.
  getActionTableModificationHistoryRisk(
    event: HistoricalRiskModificationsModel
  ) {

    event.isModifyPremium
      ? (this.modifiedPremium = true)
      : (this.modifiedPremium = false);
    this.isReadOnlyBeneficiaries = true;
    this.modifyPremiumValue = false;
    this.isHistory = true;
    this.modalTitleRiskModificationHistory =
      this._translateService.instant('Policy.Modification');
    this.premium = this.formatMoney('', false, event.iPremium || '0');
    this.valueInsurance = this.formatMoney(
      '',
      false,
      event.iInsuredValue || '0'
    );
    if (event.customerType === 'Beneficiario') {
      this.getBeneficiariesByIdHistory(event.id, event.idPolicyRisk);
      this.openModal('viewBeneficiariesModal');
    } else {
      this.idHistoryPolicy = this.idHistoryPolicyRisk;
      this.dataForm = this.convertToJson(event.json);
      this.customerType = event.customerType;
      this.openModal('viewHistoricalRiskDetails');
    }
  }

  ///Obtiene la data de la acción ver detalles en la tabla  Historico de movimientos de póliza.
  getActionTableModificationHistory(event: PolicyMovementHistoryModel) {
    this.idHistoryPolicy = event.id;
    this.typeMovement = event.movementsPolicyName;
    this.openModal('viewHistoryMovementModal');
  }

  //Detecta los cambios de modificar prima.
  onToggleChangePremium(evet: any) {
    this.modifyPremiumValue = evet.checked;
  }

  // Función que abre un modal, dependiendo el selecctor indicado.
  openModal(modalReference: string) {
    const modalConfiguration = {
      disableClose: false,
      width: '90vw',
      height: 'auto',
    };
    let modal: TemplateRef<any>;
    switch (modalReference) {
      case 'viewHistoricalRiskDetails':
        this.modalOpenRerefence = 'viewHistoricalRiskDetails';
        modal = this.viewHistoricalRiskDetails!;
        break;
      case 'viewBeneficiariesModal':
        this.modalOpenRerefence = 'viewBeneficiariesModal';
        modal = this.viewBeneficiariesModal!;
        break;
      case 'viewHistoryMovementModal':
        this.modalOpenRerefence = 'viewHistoryMovementModal';
        modal = this.viewHistoryMovementModal!;
        break;
      case 'selectModificationModal':
        this.isRisk
          ? this.getTabsListByIdPolicy(this.idWtw)
          : this.getTabsListByIdPolicy(this.idParent);

        this.modificationType = '';
        this.modifiedPremium = false;
        this.modifyPremiumValue = false;
        this.isHistory = false;
        this.modalOpenRerefence = 'selectModificationModal';
        modal = this.selectModificationModal!;
        modalConfiguration.width = '40vw';
        break;

      case 'modifyPolicyModal':
        if (this.modificationType === 'Beneficiario') {
          this.modalOpenRerefence = 'viewBeneficiariesToEditModal';
          modal = this.viewBeneficiariesToEditModal!;
          this.beneficiariesDataBpk = this.beneficiariesData;
        } else {
          const request: RequesGetDataFormModel = {
            idBusinessByCountry: this.idBusinessByCountry.toString(),
            idParent: this.idParent,
            idPolicyRisk: this.idPolicyRisk,
            typeCustomer: this.modificationType,
          };
          this.modalOpenRerefence = 'modifyPolicyModal';
          modal = this.modifyPolicyModal!;
          this.getDataJSON(request);
        }

        break;

      default:
        return;
    }
    //Abre el modal y guarda la referencia en la variable currentModal.
    this.currentModal = this.modalDialog.open(modal, modalConfiguration);
  }

  //Cierra el modal que muestra el detalle de modificación de riesgo.
  closeModalViewHistoricalRiskDetails() {
    this.currentModal?.close();
  }

  //Cierra el modal que contiene la tabla con la lista de beneficiarios.
  closeModalviewBeneficiaries() {
    this.modalDialog.closeAll();
  }

  //Cierra el modal que contiene el hsitorico de movimientos de una póliza.
  closeModalviewHistoryMovement() {
    this.modalDialog.closeAll();
  }

  //Cierra modal y valida el porcentaje de los beneficiarios
  closeModalBeneficiaries() {
    this.beneficiariesDataBpk = this.beneficiariesData;
    this.closeModalviewBeneficiaries();
  }

  //Convierte la data guardada en el cargue masivo de string a formato JSON.
  convertToJson(inputString: string): any {
    try {
      // Elimina los caracteres de escape invertidos para que sea un JSON válido
      const cleanedString = inputString.replace(/\\/g, '');
      // Convierte el string a objeto JSON
      const jsonObject = JSON.parse(cleanedString);
      return jsonObject;
    } catch (error) {
      return null;
    }
  }

  //Transforma la información recibida a un estadarizada para que pueda mostarse el detalle de cualquier tipo de póliza.
  transformDataDetails(dataPolicy: any): any[] {
    const result: any[] = [];

    //Información de la póliza individual a excluir.
    let excludePolicyValues = [
      'policyNumber',
      'statePolicy',
      'idwtw',
      'idPolicyrisk',
      'idFilePolicy',
      'idParent',
      'fieldKey',
      'fieldName',
      'idHistoryForm',
      "idCatalog",
      "idNovelty",
      "motive"
    ];

    if (this.isRisk) {
      excludePolicyValues.push('endorment');
    }


    // Transformar la sección de `policy`
    if (dataPolicy.policy) {
      const policySection = {
        policy: [
          {
            nameSection: this._translateService.instant('Policy.PolicyData'),
            fields: Object.keys(dataPolicy.policy)
              .filter((key) => !excludePolicyValues.includes(key)) // Filtrar los campos
              .map((key) => {
                let value = dataPolicy.policy[key];
                const regex = /^\d{4}-\d{2}-\d{2}$/;
                if (regex.test(value)) {
                  value = this._utilsSvc.formatDate(value, 'DD-MM-YYYY');
                }
                return {
                  name: key,
                  value: value,
                };
              }),
          },
        ],
      };
      result.push(policySection);
    }

    // Transformar la sección de `takers`
    if (dataPolicy.takers && dataPolicy.takers.length > 0) {
      const takersSection = {
        takers: dataPolicy.takers.map((taker: any) => ({
          nameSection: this._translateService.instant('Policy.TakerData'),
          fields: taker.fields.map((field: any) => ({
            name: field.name,
            value: field.value,
          })),
        })),
      };
      result.push(takersSection);
    }

    // Transformar la sección de `insurances`
    if (dataPolicy.insurances && dataPolicy.insurances.length > 0) {
      const insurancesSection = {
        insurances: dataPolicy.insurances.map((insurance: any) => ({
          nameSection: this._translateService.instant('Policy.DataSecured'),
          fields: insurance.fields.map((field: any) => ({
            name: field.name,
            value: field.value,
          })),
        })),
      };
      result.push(insurancesSection);
    }

    // Transformar la sección de `others`
    if (dataPolicy.others && dataPolicy.others.length > 0) {
      const othersSection = {
        others: dataPolicy.others.map((other: any) => ({
          nameSection: other.nameSection,
          fields: other.fields.map((field: any) => ({
            name: this.checkAndModifyKeyName(field.name),
            value: field.value,
          })),
        })),
      };
      result.push(othersSection);
    }

    return result;
  }

  checkAndModifyKeyName(keyName: string): string {
    const keysToModify: string[] = ['Plan']
    return keysToModify.includes(keyName) ? keyName + ' ' : keyName;
  }

  // Función para descargar la caratula de la póliza en formato PDF.
  downloadPDF() {
    if (this.idFilePolicy > 0) {
      const blob = new Blob([this.pdfSrc], {
        type: 'application/pdf', // Tipo MIME para PDF
      });
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = 'policy-cover.pdf'; // Nombre del archivo con extensión PDF
      link.click();
      window.URL.revokeObjectURL(downloadUrl);
    }
  }

  //Detecta los cambios del select de tipo de modificación.
  changesModificationType(event: any) {
    this.modificationType = event.source._value;
  }

  //Obtiene el vlaor del formulario al cual se le quiere hacer una modificación.
  getFormValue(event: any) {
    this.formValid = event.formValid;
    event ? (this.form = event) : (this.form = {});
  }

  //Función que módifica la opción seleccionada de la póliza.
  modifyPolicy() {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Policy.ConfirmModification'),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {

          const request: CreateHistoryRiskModel = {
            customerType: this.modificationType,
            idPolicyRisk: this.idPolicyRisk,
            idUser: this.userIdSession,
            isModifyPremium: this.modifyPremiumValue,
            json: JSON.stringify(this.form.valueForm),
            iInsuredValue: this.parseToMoney(this.valueInsurance),
            iPremium: this.parseToMoney(this.premium),
            fPremiumYear:  this.parseToMoney(this.premiumTotal),
            dDueDate: this.dDueDate
          };
          if (this.form.formValid) {
            this._transactionService
              .createHistoryRisk(request)
              .pipe(
                catchError((error) => {
                  this._messageService.messageWaring('', error.error.message);
                  return of([]);
                })
              )
              .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                } else {
                  if (!resp.error) {
                    this._messageService.messageSuccess(
                      this._translateService.instant(
                        'Policy.ModificationSaved'
                      ),
                      ''
                    );
                    this.modalDialog.closeAll();
                    this.isRisk
                      ? this.getRiskByIdWithProduct()
                      : this.getPolicyByIdWithProduct();
                    this.getBeneficiesByIdPolicy();
                    this.getHistoryRisk();
                    this.getHistoryPolicy();
                  }
                }
              });
          }
        }
      });
  }

  //Crea o actualiza un beneficiario.
  createAndUpdateBeneficiesByIdPolicy() {

    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Policy.ConfirmModification'),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          let beneficiaryId: number;
          const existingBeneficiary = this.beneficiariesDataBpk.find(
            (b) => b.id === this.beneficiaryId
          );

          if (this.beneficiariesDataBpk.length == 10) {
            this._messageService.messageWaring(
              '',
              this._translateService.instant('Policy.MaximumBeneficiaries')
            );
            return;
          }

          if (existingBeneficiary) {
            const request: CreateUpdateBeneficiariesModel = {
              customerType: this.modificationType,
              idPolicyRisk: this.idPolicyRisk,
              idUser: this.userIdSession,
              isModifyPremium: this.modifyPremiumValue,
              json: JSON.stringify(this.form.valueForm),
              iInsuredValue: this.isValidMoney(this.valueInsurance)
                ? this.parseToMoney(this.valueInsurance)
                : 0,
              iPremium: this.isValidMoney(this.premium)
                ? this.parseToMoney(this.premium)
                : 0,
              isUpdate: true,
            };

            this.beneficiariesDataBpk = this.beneficiariesDataBpk.map((beneficiary) =>
              beneficiary.id === this.beneficiaryId
                ? { ...beneficiary, ...this.extractBeneficiaryData(request.json, this.beneficiaryId) }
                : beneficiary
            );

          } else {
            if (this.beneficiariesDataBpk.length > 0) {
              const lastRecord =
                this.beneficiariesDataBpk[this.beneficiariesDataBpk.length - 1];
              beneficiaryId = lastRecord.id + 1;
            } else {
              beneficiaryId = 1;
            }
            const request: CreateUpdateBeneficiariesModel = {
              customerType: this.modificationType,
              idPolicyRisk: this.idPolicyRisk,
              idUser: this.userIdSession,
              isModifyPremium: this.modifyPremiumValue,
              json: JSON.stringify(this.form.valueForm),
              iInsuredValue: this.isValidMoney(this.valueInsurance)
                ? this.parseToMoney(this.valueInsurance)
                : 0,
              iPremium: this.isValidMoney(this.premium)
                ? this.parseToMoney(this.premium)
                : 0,
              isUpdate: true,
            };

            const beneficiaryData = this.extractBeneficiaryData(
              request.json,
              beneficiaryId
            );

            this.beneficiariesDataBpk = [
              ...this.beneficiariesDataBpk,
              beneficiaryData,
            ];
          }

          this._messageService.messageSuccess(
            this._translateService.instant('Policy.ModificationSaved'),
            ''
          );

          this.currentModal?.close();
        }
      });
  }

  //Función para validar si el valor viene como NaN
  isValidMoney(value: any): boolean {
    return !isNaN(value) && value > 0;
  }

  // Función para extraer data de los beneficiarios a partir  del JSON
  extractBeneficiaryData(
    jsonString: string,
    beneficiaryId: number = 0
  ): BeneficiaryModel {
    const parsedData = JSON.parse(jsonString);

    const keyMappings = {
      firstName: 'PrimerNombreBeneficiarioPolicy',
      secondName: 'SegundoNombreBeneficiarioPolicy',
      firstLastName: 'PrimerApellidoBeneficiarioPolicy',
      secondLastName: 'SegundoApellidoBeneficiarioPolicy',
      document: 'NúmeroDeDocumentoBeneficiarioPolicy',
      typeDocument: 'TipoDeDocumentoBeneficiarioPolicy',
      percentage: 'PorcentajeBeneficiarioPolicy',
      Name: 'Nombredebeneficiario'
    };

    let firstName = this.getValueFromJson(parsedData, keyMappings.firstName);

    if(firstName == ""){
      firstName = this.getValueFromJson(parsedData, keyMappings.Name);
    }

    const secondName = this.getValueFromJson(
      parsedData,
      keyMappings.secondName
    );
    const firstLastName = this.getValueFromJson(
      parsedData,
      keyMappings.firstLastName
    );
    const secondLastName = this.getValueFromJson(
      parsedData,
      keyMappings.secondLastName
    );
    const document = this.getValueFromJson(parsedData, keyMappings.document);
    const typeDocument = this.getValueFromJson(
      parsedData,
      keyMappings.typeDocument
    );
    const percentage = this.getValueFromJson(
      parsedData,
      keyMappings.percentage
    );

    const nameParts = [
      firstName,
      secondName,
      firstLastName,
      secondLastName,
    ].filter((name) => name);

    const nameComplete = nameParts.join(' ');

    return {
      id: beneficiaryId,
      nameComplete: nameComplete,
      document: document,
      typeDocument: typeDocument,
      percentage: percentage !== "" ? `${percentage}%` : "",
      json: jsonString,
      iInsuredValue: this.parseToMoney(this.valueInsurance),
      iPremium: this.parseToMoney(this.premium),
    } as BeneficiaryModel;
  }

  //  Función para traer el valor del JSON
  getValueFromJson(parsedData: any, key: string): string {
    const matchingKey = Object.keys(parsedData).find((keyName) =>
      keyName.includes(key)
    );
    return matchingKey ? parsedData[matchingKey] : "";
  }

  //Función que sirve para añadir beneficiarios.
  addBeneficiaries() {
    this.customerType = 'Beneficiario';
    this.premium = '0';
    this.valueInsurance = '0';
    this.beneficiaryId = 0;
    this.modalTitleRiskModificationHistory = this._translateService.instant(
      'Policy.AddBeneficiary'
    );
    this.isReadOnlyBeneficiaries = false;
    this.isUpdate = false;
    this.openModal('viewHistoricalRiskDetails');
    this.dataForm = '';
  }

  //Función para eliminar un beneficiario.
  deleteBeneficiarie() {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant(
          'FormConfiguration.ProgressBar.DeleteMessageConfirm'
        ),
        '',
        'warning',
        this._translateService.instant('Delete'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this.beneficiariesDataBpk = this.beneficiariesDataBpk.filter(
            (beneficiary) => beneficiary.id !== this.beneficiaryId
          );
        }
      });
  }

  //Función para guardar todos los beneficiarios con validaciones
  saveAllBeneficiaries() {

    const extractPercentage = (percentage: string): number => {
      return parseFloat(percentage.replace('%', '')) || 0;
    };

    const totalPercentage = this.beneficiariesDataBpk.reduce(
      (sum, beneficiary) => {
        return sum + extractPercentage(beneficiary.percentage);
      },
      0
    );

    if (this.beneficiariesDataBpk.length > this.maximumNumberBeneficiaries){

      this._messageService.messageWaring(
        '',
        this._translateService.instant('Policy.MaximumBeneficiariesExceeded')
      );
      return;
    }

    if (totalPercentage > 100 &&  totalPercentage < 100 ) {
      this._messageService.messageWaring(
        '',
        this._translateService.instant('Policy.BeneficiaryPercentageError') +
        `${totalPercentage}%`
      );
      return;
    }

    // Crear un objeto único para juntar todos los json
    const combinedJson: any = {};

    // Transformar y actualizar la propiedad json de cada beneficiario
    const allBeneficiaries = this.beneficiariesDataBpk.map((item) => {
      const json = JSON.parse(item.json);

      // Actualizar las claves del JSON para incluir el ID
      for (const key in json) {
        const newKey = key.replace(/(\d+)_/, `$1_${item.id}_`);
        combinedJson[newKey] = json[key];
      }

      // Devolver el objeto beneficiario sin la propiedad json, ya que ahora está combinada
      return {
        id: item.id,
        name: item.nameComplete,
        document: item.document,
        typeDocument: item.typeDocument,
        percentage: extractPercentage(item.percentage),
        insuredValue: this.parseToMoney(this.valueInsurance),
        premium: this.parseToMoney(this.premium),
      };
    });

    const valueInsurance =
      this.valueInsurance !== '' ? this.parseToMoney(this.valueInsurance) : 0;
    const premium = this.premium !== '' ? this.parseToMoney(this.premium) : 0;
    const request: CreateUpdateBeneficiariesModel = {
      customerType: this.modificationType,
      idPolicyRisk: this.idPolicyRisk,
      idUser: this.userIdSession,
      isModifyPremium: this.modifyPremiumValue,
      json: JSON.stringify(combinedJson),
      iInsuredValue: valueInsurance,
      iPremium: premium,
      isUpdate: this.isUpdate,
    };

    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Policy.ConfirmModification'),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._transactionService
            .createAndUpdateBeneficiesByIdPolicy(request)
            .pipe(
              catchError((error) => {
                this._messageService.messageWaring('', error.error.message);
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
              } else {
                if (!resp.error) {
                  this._messageService.messageSuccess(
                    this._translateService.instant('Policy.ModificationSaved'),
                    ''
                  );
                  this.modalDialog.closeAll();
                  this.isRisk
                    ? this.getRiskByIdWithProduct()
                    : this.getPolicyByIdWithProduct();
                  this.getBeneficiesByIdPolicy();
                  this.getHistoryRisk();
                  this.getHistoryPolicy();
                }
              }
            });
        }
      });
  }

  //Formatea los valores ingresados en los input de prima y valor asegurado a tipo moneda.
  formatMoney(
    event: any,
    isCalculatedValue: boolean,
    valueInput?: any
  ): string {
    const symbol = '$';
    let value = '';

    if (!isCalculatedValue) {
      if (!valueInput) {
        if (event.target && event.target.value !== undefined) {
          value = event.target.value.toString().replace(/[^\d]/g, '');
        } else {
          return ''; // Retorna vacío si 'event.target.value' no está definido
        }
      } else {
        value = valueInput.toString().replace(/[^\d]/g, '');
      }
    } else {
      value = event.toString().replace(/[^\d]/g, ''); // Asegura que 'event' sea procesado como string
    }

    value = symbol + this.addCommas(value); // Agrega el símbolo de la moneda y las comas para separar los miles

    if (!isCalculatedValue) {
      if (!valueInput) {
        if (event.target) {
          return (event.target.value = value);
        } else {
          return ''; // Retorna vacío si 'event.target' no existe
        }
      } else {
        return (valueInput = value);
      }
    } else {
      return value;
    }
  }

  //Añade comas a las cifras monetarias.
  addCommas(nStr: string): string {
    const x = nStr.split('.');
    let x1 = x[0];
    const x2 = x.length > 1 ? '.' + x[1] : '';
    const rgx = /(\d+)(\d{3})/;
    while (rgx.test(x1)) {
      x1 = x1.replace(rgx, '$1' + ',' + '$2');
    }
    return x1 + x2;
  }

  //Convierte una representación de moneda en formato string, a un valor númerico.
  parseToMoney(value: string): number {
    // Eliminar el símbolo '$' y las comas ','
    const valorSinFormato = value.replace(/[\$,]/g, '');
    // Convertir a número
    return parseFloat(valorSinFormato);
  }

  //Redirige a la página anteriror.
  goBack() {
    this._location.back();
  }

  //Redirige a la página de renovación de pólizas.
  goToRenewal() {
    this._transactionService
      .validateRenewalInProcess(this.idWtw)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result == true) {
            this._messageService.messageConfirmatio(
              this._translateService.instant('Policy.RenovationInProgess'),
              '', 'info', 'OK');
          }
          else {
            this._messageService
              .messageConfirmationAndNegation(
                this._translateService.instant('Policy.StartRenovation'),
                '',
                '',
                this._translateService.instant('Continue'),
                this._translateService.instant('Cancel')
              )
              .then((result) => {
                if (result) {
                  this._customeRouter.navigate([
                    `dashboard/policy/renewal-individual-policy/${this.idWtw}/${this.idPolicyType}/${this.idPolicyRisk}`,
                  ]);
                }
              });
          }
        }
      });
  }

  getBeneficiariesNumberByIdPolicy(idPolicy:number) {
    this._transactionService
      .getBeneficiariesNumberByIdPolicy(idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(this._translateService.instant('Warning'), error.error.message);
          }
          this.maximumNumberBeneficiaries =  0;
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message);
          } else {
            this.maximumNumberBeneficiaries = resp.result;
          }
        }
      });
  }
}
