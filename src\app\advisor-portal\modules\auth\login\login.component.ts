import { CommonModule } from '@angular/common';
import { Component, inject, Input, Sanitizer } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { NgxCaptchaModule } from 'ngx-captcha';
import { LoginModel } from 'src/app/shared/models/user';
import { CryptoService } from 'src/app/shared/services/Crypto/crypto.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Store } from '@ngrx/store';
import { loadCompanyConfigSuccess } from 'src/app/store/actions';
import { CompanyConfigState } from 'src/app/store/reducers/company.reducer';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { FileService } from '../../../../shared/services/file/file.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { catchError, of } from 'rxjs';
import { MsalService } from '@azure/msal-angular';
import { environment } from 'src/environments/environment';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    NgxCaptchaModule,
    MatInputModule,
    MatButtonModule,
    RouterModule,
    TranslateModule,
    MatCheckboxModule,
  ],
  templateUrl: './login.component.html',
  styleUrls: ['../auth.scss'],
})
export class LoginComponent {
  siteKey = '6LdCkc0UAAAAANygwqPPmQVuGUjiU69YmrCaeKbh';
  captchaResponse: string = '';
  form: FormGroup = new FormGroup({});
  primaryColor: string = '';
  secondaryColor: string = '';
  idBusinessByCountry: number = 0;
  stateByStage: number = 0;
  userIdSession: number = 0;
  companyName: string = '';
  userProfile: any;
  autoLogin: any;
  isMSALActive: boolean = false;

  constructor(
    private userService: UserService,
    private fb: FormBuilder,
    public utilsService: UtilsService,
    private router: Router,
    private messageService: MessageService,
    private _cryptoService: CryptoService,
    private _customRouter: CustomRouterService,
    private _businessService: BusinessService,
    private _messageService: MessageService,
    private _settingService: SettingService,
    private _sanitizer: DomSanitizer,
    private _fileService: FileService,
    private _spinnerService: SpinnerService,
    public _translateService: TranslateService,
    public _store: Store,
    public _storeRes: Store<{ companyConfig: CompanyConfigState }>,
    private authService: MsalService
  ) {
    this.initForm();
  }
  private localStorageService = inject(LocalStorageService);
  ngOnInit(): void {
    this.companyName = this.router.parseUrl(this.router.url).root.children[
      'primary'
    ]?.segments[1]?.path;

    this._storeRes.select('companyConfig').subscribe((r) => {
      this.primaryColor = r.config?.colorsCompanyClient?.primaryColor ?? '';
      this.secondaryColor = r.config?.colorsCompanyClient?.secondaryColor ?? '';
      this.idBusinessByCountry = r.config?.pkIIdBusinessByCountry ?? 0;
      this._customRouter.setIdbusinessbycountry(this.idBusinessByCountry);
    });
    const fullUrl = this.router.url;
    const parts = fullUrl.split('/');
    this._customRouter.setNavigationData(parts[1], parts[2]);

    this.loadRememberedEmail();

    this.isMSALActive = environment.msalConfig.isActive;
    const currentUrl = window.location.href.split(/[?#]/)[0];
    
    if (this.isMSALActive && environment.msalConfig.redirectUri==currentUrl) {
      this.autoLogin = localStorage.getItem('autoLoginMSAL');
      if (this.autoLogin === null) {
        localStorage.setItem('autoLoginMSAL', 'true');
        this.autoLogin = 'true';
      }

      console.log('🛠️ Inicializando MSAL...');

      this.authService.instance
        .initialize()
        .then(() => {
          this.authService.handleRedirectObservable().subscribe({
            next: (result) => {
              if (result) {
                console.log('✅ Usuario autenticado desde la redirección.');
                this.authService.instance.setActiveAccount(result.account);
              }
              this.checkLogin();
            },
            error: (error) => console.error('❌ Error en MSAL:', error),
          });
        })
        .catch((error) => {
          console.error('❌ Error inicializando MSAL:', error);
        });
    } else {
      this.autoLogin = localStorage.getItem('autoLoginMSAL');
      if (this.autoLogin === null) {
        localStorage.setItem('autoLoginMSAL', 'false');
        this.autoLogin = 'false';
      }
    }
  }

  initForm() {
    this.form = this.fb.group({
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],
      isClose: true,
      password: [
        '',
        [
          Validators.required,
          // Validators.pattern(
          //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/g
          // ),
        ],
      ],
      recaptcha: ['', []],
      // recaptcha: ['', [Validators.required]],

      rememberMe: [false],
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  checkLogin(): void {
    this.autoLogin = localStorage.getItem('autoLoginMSAL');

    if (this.autoLogin === 'false') {
      console.log(
        '🚫 AutoLogin deshabilitado. No se ejecutará la autenticación.'
      );
      return;
    }

    const account = this.authService.instance.getActiveAccount();
    if (account) {
      console.log('✅ Usuario ya autenticado checkLogin:');
      this.getUserProfile();
      this.autoCallLogin();
    } else {
      console.log('🔄 No hay usuario autenticado, verificando sesión...');

      this.authService.loginRedirect(); // 🔹 No se hace logout, solo login nuevamente
    }
  }

  getUserProfile() {
    this.userProfile = this.authService.instance.getActiveAccount();
    console.log('✅ getUserProfile:',this.userProfile);

  }

  autoCallLogin() {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (
      this.userProfile?.username &&
      emailRegex.test(this.userProfile.username)
    ) {
      this.login('@wtwco.com');
      this.login();
    }
  }

  remember() {
    // Si "Recordarme" está marcado, guarda el correo electrónico en localStorage
    if (this.form.get('rememberMe')?.value) {
      this.localStorageService.setItem('rememberedEmail', this.form.get('email')?.value);
    } else {
      this.localStorageService.removeItem('rememberedEmail');
    }
  }

  async login(domain?: string) {
    let email = '';
    let password = '';
    let isAD = false;

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (
      this.userProfile?.username &&
      emailRegex.test(this.userProfile.username)
    ) {
      const parts = this.userProfile.username.split('@');
      email = domain ? `${parts[0]}${domain}` : this.userProfile.username;

      isAD = true;
    } else {
      email = this.form.get('email')?.value;
      password = this.form.get('password')?.value;
    }

    let payload: LoginModel = {
      email: await this._cryptoService.encrypt256(email),
      isClose: this.form.get('isClose')?.value,
      password: await this._cryptoService.encrypt256(password),
      isAD: isAD,
      isClientPortal: false,
    };

    if (this.valid || isAD) {
      this.remember();
      const fullUrl = this.router.url;
      const parts = fullUrl.split('/');

      if (!(await this.validateCompanyByName(parts[2]))) {
        this._settingService.removeDataSettingInit();
        this._customRouter.deleteNavigationData();
        this.userService.removeTokenLocalStorage();
        this._customRouter.deleteLoggedInAdvisorPortal();
        return;
      }

      this.userService.login(payload).subscribe({
        next: async (response) => {
          if (response.error) {
            if (!isAD) {
              this.messageService.messageError(response.message);
            }
            return;
          } else {
            this.userService.saveTokenLocalStorage(response.result.token);
            this._customRouter.setNavigationData(parts[1], parts[2]);

            this._customRouter.setLoggedInAdvisorPortal();

            this._customRouter.navigate([`/onboard/choose-company`]);
          }
        },
        error: (err) => {
          this.messageService.messageError(err.error.mensaje);
        },
      });
    }
  }

  /**
   * Validates the existence of a company by its name.
   * This function returns a promise that resolves to true if the company exists,
   * or false if it does not. If an error occurs, it rejects the promise.
   *
   * @param {string} name - The name of the company to validate.
   * @returns {Promise<boolean>} - A promise that resolves to a boolean indicating whether the company exists.
   */
  private async validateCompanyByName(name: string): Promise<boolean> {
    try {
      this._spinnerService.showChanges(true);

      const result = await new Promise<boolean>((resolve, reject) => {
        this._businessService.validateCompanyByName(name, false).subscribe({
          next: (r) => {
            if (r.result && !r.result.error) {
              // Procesar todas las imágenes y luego despachar la acción al store
              this._fileService
                .getAllImagesOfServer(r.result, false)
                .subscribe((processedResult) => {
                  this._customRouter.setIdbusinessbycountry(
                    r.result.pkIIdBusinessByCountry
                  );
                  this._store.dispatch(
                    loadCompanyConfigSuccess({ config: processedResult })
                  );
                  resolve(true);
                });
            } else if (r.result && r.result.error) {
              this._messageService.messageError(
                this._translateService.instant(r.result.message)
              );
              resolve(false);
            } else {
              this._messageService.messageError(
                this._translateService.instant(r.message ?? 'ThereWasAError')
              );
              resolve(false);
            }
          },
          error: (err) => {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError')
            );
            reject(err);
          },
        });
      });
      this._spinnerService.showChanges(false);

      return result;
    } catch (err) {
      console.error('Error al validar la compañía:', err);
      this._spinnerService.showChanges(false);

      return false;
    }
  }

  // Función para cargar el correo electrónico almacenado en localStorage cuando la página se carga
  loadRememberedEmail() {
    const rememberedEmail = this.localStorageService.getItem('rememberedEmail');
    if (rememberedEmail) {
      this.form.get('email')?.setValue(rememberedEmail);
      this.form.get('rememberMe')?.setValue(true);
    }
  }

  goToRecoverPassword() {
    this._customRouter.navigate([
      `/advisor-portal/`,
      this.companyName,
      `/auth/recover-password`,
    ]);
  }
}
