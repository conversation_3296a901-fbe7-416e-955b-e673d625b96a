import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, finalize, of } from 'rxjs';
import { DragDropUploadComponent } from 'src/app/shared/components/drag-drop-upload/drag-drop-upload.component';
import { ConvertJSONToExcel } from 'src/app/shared/models/file';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-mass-renewal-policy',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    DragDropUploadComponent,
  ],
  templateUrl: './mass-renewal-policy.component.html',
  styleUrls: ['./mass-renewal-policy.component.scss'],
})
export class MassRenewalPolicyComponent implements OnInit {
  //Variables para el formulario.
  form: FormGroup = new FormGroup({});
  templateName = this._translateService.instant('RenewalPolicy.RenewalPolicy');
  listProduct: any[] = [];
  template!: File;
  uploadedFile: File[] = [];
  fileName: string = '';
  idUser: number = 0;
  idBusinessByCountry: number = 0;
  policyCredit: any;
  constructor(
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    private _transactionService: TransactionService,
    private _msgSvc: MessageService,
    private _fileService: FileService,
    private _customeRouter: CustomRouterService,
    private _spinnerService: SpinnerService,
    private _userService: UserService,
    private _activatedRoute: ActivatedRoute,
    public utilsService: UtilsService
  ) {
    this.initForm();
    this.getProductsList();
  }

  async ngOnInit() {
    this.idUser = await this._userService.getUserIdSesion();
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
    });
  }

  initForm() {
    this.form = this._fb.group({
      fkIIdProduct: [{ value: null, disabled: false }, Validators.required],
    });

    this.form.get('fkIIdProduct')?.valueChanges.subscribe({
      next: (data) => {
        if (data)  this.getCreditPolicy(data);
      },
    });
  }

  //obtiene los productos de las polizas idBusinessCountry
  getProductsList() {
    this._transactionService
      .getProductsByIndividualPolicy()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant(error.error.message)
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.listProduct = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageInfo(
              this._translateService.instant('ThereWasAError'),
              resp.message
            );
          } else {
            this.listProduct = resp.result;
          }
        }
      });
  }

  //Función que se encarga de llamar a la función que devuelve el base64 con el template.
  downloadTemplate(withoutDownload: boolean = false) {
    const idStageByState = this.form.get('fkIIdProduct')?.value;

    const headersBase = [
      'Número póliza anterior',
      'Número póliza nuevo',
      'Endoso',
      'Inicio vigencia',
      'Fin vigencia',
      'Prima mensual',
      'Prima total',
      'Periodicidad de pago',
      'N° de pagos',
      'Valor asegurado',
    ];

    const request: ConvertJSONToExcel = {
      isTask: false,
      headers: this.policyCredit ? [...headersBase, 'N° Crédito'] : headersBase,
    };

    if (idStageByState > 0) {
      this._fileService
        .convertJSONToExcel(request)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.result != undefined) {
              if (!withoutDownload) {
                this.utilsService.downloadTemplateByBase64(
                  resp.result,
                  this.templateName
                );
              }

              const file = this.utilsService.base64ToFile(
                resp.result,
                this.templateName
              );
              if (file) {
                this.template = file;
              }
              console.log(this.template);
            }
          }
        });
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant(
          'BulkUpload.Massives.MassCreationTasks.MandatoryFiltersTitle'
        ),
        this._translateService.instant(
          'BulkUpload.Massives.MassCreationTasks.MandatoryFiltersSubtitle'
        )
      );
    }
  }

  //Elimina el file cargado.
  deleteFile() {
    this.uploadedFile = [];
    this.fileName = '';
  }

  //Detecta los eventos de componente de cargue de archivos.
  getFiles(files: File[]) {
    if (this.utilsService.validateFileSize(files[0].size, 25)) {
      this.uploadedFile = files;
      if (this.uploadedFile.length > 0) {
        this.fileName = this.uploadedFile[0].name;
      }
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant('MaximumFileSizeTitle'),
        this._translateService.instant('MaximumFileSizeSubtitle')
      );
    }
  }

  //Guarda la carga masiva en la BD.
  loadMassive() {
    if (this.template && this.uploadedFile.length > 0) {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('BulkUpload.PopUp.ValidateLoadTitle'),
          '',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            const fileToCompare: File[] = [this.template, this.uploadedFile[0]];
            const payload = new FormData();

            fileToCompare.forEach((element) => {
              payload.append(`Files`, element);
            });
            this.comparerExcelFiles(payload);
          }
        });
    }
  }

  comparerExcelFiles(files: FormData) {
    this._fileService
      .comparerExcelFiles(files)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe(async (resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.length > 0) {
            this._msgSvc.messageErrorCustomer(
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadTitle'
              ),
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadSubtitle'
              )
            );
          } else {
            await this.uploadMassivePolicyTask();
          }
        }
      });
  }

  // //Guarda la carga masiva en la BD.
  async uploadMassivePolicyTask() {
    await this._spinnerService.show();
    const payload = new FormData();
    let jsonAditional: any = {
      idProduct: this.form.get('fkIIdProduct')?.value,
    };
    payload.append(`IdManagementType`, (9).toString());
    payload.append(`IdUser`, this.idUser.toString());
    payload.append(`File`, this.uploadedFile[0]);
    payload.append(`JsonAditional`, JSON.stringify(jsonAditional));
    payload.append(`IsTask`, true.toString());
    this._spinnerService.show();
    this._fileService
      .uploadMassivePolicyTask(payload)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        }),
        finalize(() => {
          this._spinnerService.hide();
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            this._msgSvc
              .messageConfirmatioCustomer(
                `${this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadTitle'
                )} ${resp.result}`,
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadSubtitle'
                ),
                'success',
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadBtnLeft'
                ),
                this._translateService.instant('Continuar')
              )
              .then((result) => {
                if (result) {
                  this._customeRouter.navigate([
                    `dashboard/massive/load-viewer/${this.idBusinessByCountry}`,
                  ]);
                }
              });
          }
        }
      });
  }

  getCreditPolicy(idProduct: number){
    this._transactionService
    .getAssociatedWithCreditPolicyByIdProduct(idProduct)
    .pipe(
      catchError((error) => {
        if (error.error.error) {
          this._msgSvc.messageWaring(
            this._translateService.instant('WarningMessage'),
            this._translateService.instant(error.error.message)
          );
        }
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        this.policyCredit = {};
      } else {
        if (resp.error) {
          this._msgSvc.messageInfo(
            this._translateService.instant('ThereWasAError'),
            resp.message
          );
        } else {
          this.policyCredit = resp.result.associatedWithCredit ?? false;;

          this.downloadTemplate(true);
        }
      }
    });
  }

  goBackMassive() {
    this._customeRouter.navigate([`dashboard/massive`]);
  }

}
