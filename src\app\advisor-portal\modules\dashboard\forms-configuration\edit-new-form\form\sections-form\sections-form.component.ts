import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, debounceTime, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ProgressBarModel } from 'src/app/shared/models/configuration-form';
import {
  SectionListTableModel,
  SectionsModel,
} from 'src/app/shared/models/configuration-form/sections';
import { TabModuleModel } from 'src/app/shared/models/configuration-form/tab-module';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-sections-form',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    Modal2Component,
    MatSelectModule,
    MatTooltipModule
  ],
  templateUrl: './sections-form.component.html',
  styleUrls: ['./sections-form.component.scss'],
})
export class SectionsFormComponent implements OnInit {
  @Input() idForm: number = 0;
  form: FormGroup = new FormGroup({});
  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.Sections.Order'
      ),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.Sections.SectionName'
      ),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.Sections.Tab'
      ),
      columnValue: 'vNameTab',
      functionValue: (item: any) => this.validationItemTable(item, 'tab'),
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.Sections.Step ProgressBar'
      ),
      columnValue: 'vNameProgress',
      functionValue: (item: any) =>
        this.validationItemTable(item, 'progresbar'),
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'edit',
      columnIcon: 'edit',
    },
  ];

  dataTableSections: SectionListTableModel[] = [];
  @ViewChild('editNewSectionsModal')
  editNewSectionsModal?: TemplateRef<any>;
  titelModal: string = 'Nueva sección';
  orderList: number[] = [];
  tabList: TabModuleModel[] = [];
  progressBarList: ProgressBarModel[] = [];
  action: string = 'create';
  associatedTab: any = null;

  constructor(
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public modalDialog: MatDialog,
    public utilsSvc: UtilsService,
    private _fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initForm();
    if (this.idForm !== 0) {
      this.getSectionModuleByFormId(this.idForm);
    }
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table modules
      this.estructTable[0].columnLabel = this._translateService.instant(
        'FormConfiguration.Sections.Order'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'FormConfiguration.Sections.SectionName'
      );

      this.estructTable[2].columnLabel = this._translateService.instant(
        'FormConfiguration.Sections.Tab'
      );

      this.estructTable[3].columnLabel = this._translateService.instant(
        'FormConfiguration.Sections.Step ProgressBar'
      );

      this.estructTable[4].columnLabel =
        this._translateService.instant('Status');

      this.estructTable[5].columnLabel =
        this._translateService.instant('Action');
    });
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdSectionModule: [0],
      vName: ['', [Validators.required]],
      vNameDb: { value: '', disabled: true },
      iOrder: [[], [Validators.required]],
      fkIIdTabModule: [null],
      fkIIdProgressBar: [null],
      bActive: [true],
      fkIIdFormModule: [this.idForm],
    });
    this.form
      .get('vName')
      ?.valueChanges.pipe(debounceTime(600))
      .subscribe({
        next: (data) => {
          if (data) {
            this.form
              .get('vNameDb')
              ?.setValue(this.utilsSvc.generateNameDb(data));
          } else {
            this.form.get('vNameDb')?.setValue('');
          }
        },
      });

    this.form
      .get('fkIIdProgressBar')
      ?.valueChanges.pipe(debounceTime(600))
      .subscribe({
        next: (data) => {
          if (data > 0) {
            this.form.get('fkIIdTabModule')?.setValue(null);
            this.getFormTabModulesListByIdProgress(data);
          }
        },
      });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  getSectionModuleByFormId(idFormModule: number) {
    this._moduleService
      .getSectionModuleByFormId(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.dataTableSections = response.result;
            if (response.result.length > 0) {
              this.orderList = this.utilsSvc.generarArrayOrderList(
                response.result.length + 1
              );
            } else {
              this.orderList = this.utilsSvc.generarArrayOrderList(1);
            }
          }
        }
      });
  }

  getProgressBarModuleByIdFormModule(idFormModule: number) {
    this._moduleService
      .getProgressBarModuleByIdFormModule(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if (response.result.length > 0) {
              this.progressBarList = response.result;
              this.form.get('fkIIdProgressBar')?.enable();
              this.form.get('fkIIdTabModule')?.disable();
              this.form
                .get('fkIIdProgressBar')
                ?.setValidators(Validators.required);
              this.form.get('fkIIdProgressBar')?.updateValueAndValidity();
            } else {
              this.form.get('fkIIdProgressBar')?.disable();
              this.getTabModuleListByFormId(this.idForm);
            }
          }
        }
      });
  }

  getFormTabModulesListByIdProgress(idProgressBar: number) {
    this._moduleService
      .getFormTabModulesListByIdProgress(idProgressBar)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if (response.result.length === 0) {
              this.form.get('fkIIdTabModule')?.disable();
              this.form.get('fkIIdTabModule')?.clearValidators();
              this.form.get('fkIIdTabModule')?.updateValueAndValidity();
            } else {
              this.tabList = response.result;
              this.form.get('fkIIdTabModule')?.enable();
              this.form.get('fkIIdTabModule')?.clearValidators();
              this.form
                .get('fkIIdTabModule')
                ?.setValidators(Validators.required);
              this.form.get('fkIIdTabModule')?.updateValueAndValidity();
              this.form.get('fkIIdTabModule')?.setValue(this.associatedTab);
            }
          }
        }
      });
  }

  getTabModuleListByFormId(idFormModule: number) {
    this._moduleService
      .getTabModuleListByFormId(idFormModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if (response.result.length === 0) {
              this.form.get('fkIIdTabModule')?.disable();
              this.form.get('fkIIdTabModule')?.clearValidators();
              this.form.get('fkIIdTabModule')?.updateValueAndValidity();
            } else {
              this.tabList = response.result;
              this.form
                .get('fkIIdTabModule')
                ?.setValidators(Validators.required);
              this.form.get('fkIIdTabModule')?.updateValueAndValidity();
            }
          }
        }
      });
  }

  getSectionModuleById(idSectionModule: number) {
    this._moduleService
      .getSectionModuleById(idSectionModule)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.form.patchValue(response.result);
            if (response.result.fkIIdTabModule) {
              if (response.result.fkIIdTabModule === 0) {
                this.associatedTab = null;
              } else {
                this.associatedTab = response.result.fkIIdTabModule;
              }
            }
          }
        }
      });
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.action = 'edit';
        this.setOrder(true);
        this.getSectionModuleById(event.value.pkIIdSectionModule);
        this.openModalCreateEditSections();
        break;
      default:
        break;
    }
  }

  setOrder(edit: boolean){
    if(edit){
      this.orderList = this.utilsSvc.generarArrayOrderList(
        this.dataTableSections.length
      );
    }
    else{
      this.orderList = this.utilsSvc.generarArrayOrderList(
        this.dataTableSections.length + 1
      );
    }
  }

  openModalCreateEditSections() {
    this.getProgressBarModuleByIdFormModule(this.idForm);
    if (this.action === 'edit') {
      this.titelModal = this._translateService.instant(
        'FormConfiguration.Sections.TitleModalEdit'
      );
    } else {
      this.titelModal = this._translateService.instant(
        'FormConfiguration.Sections.TitleModalCreate'
      );
    }
    this.modalDialog.open(this.editNewSectionsModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  validationItemTable(item: SectionListTableModel, key: string) {
    if (key === 'tab') {
      if (item.fkIIdTabModule) {
        return '<span>' + item.vNameTab + '</span>';
      } else {
        return '<span>' + 'No Aplica' + '</span>';
      }
    } else if (key === 'progresbar') {
      if (item.fkIIdProgressBar) {
        return '<span>' + item.vNameProgress + '</span>';
      } else {
        return '<span>' + 'No Aplica' + '</span>';
      }
    } else {
      return '<span>' + 'No Aplica' + '</span>';
    }
  }

  closeModal(event: boolean) {
    this.form.reset();
    this.form.get('pkIIdSectionModule')?.setValue(0);
    this.form.get('bActive')?.setValue(true);
    this.form.get('pkIIdProgressBar')?.setValue(null);
    this.form.get('fkIIdTabModule')?.setValue(null);
    this.form.get('fkIIdFormModule')?.setValue(this.idForm);
    this.associatedTab = null;
    this.form.get('pkIIdProgressBar')?.enable();
    this.form.get('fkIIdTabModule')?.enable();
    this.action = 'create';
    this.setOrder(false);
  }

  createSection() {
    if (this.valid) {
      let payload: SectionsModel = this.form.getRawValue();
      this._moduleService
        .registerSectionModule(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              if (resp.message === 'MessageGenericCreateFormsModules') {
                this._msgSvc.messageInfo(
                  this._translateService.instant(
                    'FormConfiguration.TitleConfirmationMessageCreate'
                  ),
                  this._translateService.instant(
                    'FormConfiguration.SubtitleConfirmationMessageCreate'
                  )
                );
              } else {
                this._msgSvc.messageSuccess(
                  '',
                  this._translateService.instant(
                    'ModulesSetting.SuccessfulMessageCreated'
                  )
                );
              }
              this.getSectionModuleByFormId(this.idForm);
              this.modalDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  editSection() {
    if (this.valid) {
      let payload: SectionsModel = this.form.getRawValue();
      this._moduleService
        .updateSectionModule(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageUpdated'
                )
              );
              this.getSectionModuleByFormId(this.idForm);
              this.modalDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  deleteSection() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant(
          'FormConfiguration.ProgressBar.DeleteMessageConfirm'
        ),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._moduleService
            .deleteSectionModule(this.form.get('pkIIdSectionModule')?.value)
            .pipe(
              catchError((error) => {
                if (
                  error.error.error &&
                  error.error.message === 'hasAssociatedItems'
                ) {
                  this._msgSvc.messageInfo(
                    this._translateService.instant(
                      'FormConfiguration.deletedMessageTitle'
                    ),
                    this._translateService.instant(
                      'FormConfiguration.deletedMessageSubtitle'
                    )
                  );
                } else {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    error.error.message
                  );
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
                console.log('El tipo de datos devueltos es un array vacío.');
              } else {
                this._msgSvc.messageSuccess(
                  '',
                  this._translateService.instant(
                    'FormConfiguration.ProgressBar.DeleteMessageSuccess'
                  )
                );
                this.getSectionModuleByFormId(this.idForm);
                this.modalDialog.closeAll();
                this.closeModal(true);
              }
            });
        }
      });
  }
}
