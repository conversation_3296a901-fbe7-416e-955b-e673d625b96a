import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, debounceTime, of, Subscription } from 'rxjs';
import { FieldComponent } from 'src/app/shared/components/field-generator/field.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { MessageService } from 'src/app/shared/services/message/message.service';

@Component({
  selector: 'app-details-risk-modifications',
  standalone: true,
  imports: [
    CommonModule,
    MatInputModule,
    FieldComponent,
    ReactiveFormsModule,
    TranslateModule,
    MatButtonModule,
  ],
  templateUrl: './details-risk-modifications.component.html',
  styleUrls: ['./details-risk-modifications.component.scss'],
})
export class DetailsRiskModificationsComponent implements OnInit, OnChanges {
  //Variables para compartir información entre componentes.
  @Input() dataForm: any;
  @Input() idPolicy: number = 0;
  @Input() idParent: number = 0;
  @Input() idHistoryPolicy: number = 0;
  @Input() customerType: string = '';
  @Input() isReadOnly: boolean = false;
  @Input() isHistory: boolean = false;
  @Output() formValue = new EventEmitter<any>();

  //Variables de formulario.
  public dynamicFormGroup: FormGroup = this.formBuilder.group({}); //TODO: Se agrega esta variable para evitar error por falta de instanciar un form en el componente field.
  form: any;
  formGroup: FormGroup;
  formArray: FormArray;

  //Subscripciones.
  fieldSubs?: Subscription;
  constructor(
    private formBuilder: FormBuilder,
    private _translateService: TranslateService,
    private _fieldSvc: FieldService,
    private _msgSvc: MessageService
  ) {
    this.formGroup = this.formBuilder.group({
      fields: this.formBuilder.array([]),
    });
    this.formArray = this.formGroup.get('fields') as FormArray;
  }

  async ngOnInit(): Promise<void> {
    if (this.isHistory) {
      await this.getFormByHistory();
    } else {
      await this.getCompleteFormByPolicy();
    }

    this.dynamicFormGroup.patchValue(this.dataForm);
    const formValue = {
      formValid: this.dynamicFormGroup.valid,
      valueForm: this.dynamicFormGroup.getRawValue(),
    };
    this.formValue.emit(formValue);
    if (!this.isReadOnly) {
      this.dynamicFormGroup.valueChanges.pipe(debounceTime(500)).subscribe({
        next: (data) => {
          const formValue = {
            formValid: false,
            valueForm: {},
          };
          if (this.dynamicFormGroup.getRawValue()) {
            formValue.formValid = this.dynamicFormGroup.valid;
            formValue.valueForm = this.dynamicFormGroup.getRawValue();
            this.formValue.emit(formValue);
          } else {
            formValue.formValid = this.dynamicFormGroup.valid;
            formValue.valueForm = data;
            this.formValue.emit(formValue);
          }
        },
      });
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['dataForm']) {
      if (changes['dataForm'].currentValue) {
        this.dynamicFormGroup.patchValue(this.dataForm);
      }
    }
  }

  //Función que obtiene la estructura del formulario configurado para dicha póliza.
  getCompleteFormByPolicy() {
    if (this.idParent === 0) {
      return new Promise((resolve, reject) => {
        this.fieldSubs = this._fieldSvc
          .getCompleteFormByPolicy(this.idPolicy, this.customerType)
          .pipe(
            catchError((error) => {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
              resolve(false);
              return of([]);
            })
          )
          .subscribe((resp: ResponseGlobalModel | never[]) => {
            if (Array.isArray(resp)) {
            } else {
              this.form = resp.result[0];
              const formGroupFields = this.getFormControlsFields();
              this.dynamicFormGroup = new FormGroup(formGroupFields);
              if (this.isReadOnly) {
                this.dynamicFormGroup.disable();
              }
            }
            resolve(true);
          });
      });
    } else {
      return new Promise((resolve, reject) => {
        this.fieldSubs = this._fieldSvc
          .getCompleteFormByPolicy(this.idParent, this.customerType)
          .pipe(
            catchError((error) => {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
              resolve(false);
              return of([]);
            })
          )
          .subscribe((resp: ResponseGlobalModel | never[]) => {
            if (Array.isArray(resp)) {
            } else {
              this.form = resp.result[0];
              const formGroupFields = this.getFormControlsFields();
              this.dynamicFormGroup = new FormGroup(formGroupFields);
              if (this.isReadOnly) {
                this.dynamicFormGroup.disable();
              }
            }
            resolve(true);
          });
      });
    }
  }

  //Función que obtiene la estructura del formulario configurado para dicha póliza, tomada desde el historico.
  getFormByHistory() {
    return new Promise((resolve, reject) => {
      this.fieldSubs = this._fieldSvc
        .getFormByHistory(this.idHistoryPolicy, this.customerType)
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            resolve(false);
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this.form = resp.result[0];
            const formGroupFields = this.getFormControlsFields();
            this.dynamicFormGroup = new FormGroup(formGroupFields);
            if (this.isReadOnly) {
              this.dynamicFormGroup.disable();
            }
          }
          resolve(true);
        });
    });
  }

  //Función que construye los controls del formulario, basandose en la data que se obtiene del endpoint getCompleteFormByPolicy().
  private getFormControlsFields() {
    const formGroupFields: any = {};
    for (const tab of this.form.pTabs) {
      for (const section of tab.pSections) {
        for (const field of section.uSectionFields) {
          //N**3
          const validators = this.addValidator(field.fkIIdFieldNavigation);
          formGroupFields[
            field.fkIIdFieldNavigation.pkIIdField +
              '_' +
              field.fkIIdFieldNavigation.vNameFieldDb
          ] = new FormControl(null, validators);
        }
      }
    }
    return formGroupFields;
  }

  //Función que añade las validaciones configuradas para cada uno de los controls generados en la función getFormControlsFields().
  private addValidator(field: any): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    // Agregar la validación de campo requerido si bRequired es true
    if (field.bRequired) {
      validators.push(Validators.required);
    }

    // Agregar la validación de minLength si iMinLength es mayor que 0
    if (field.iMinLength > 0) {
      validators.push(Validators.minLength(field.iMinLength));
    }

    // Agregar la validación de maxLength si iMaxLength es mayor que 0
    if (field.iMaxLength > 0) {
      validators.push(Validators.maxLength(field.iMaxLength));
    }
    return validators;
  }

  //Agrega una validación para los campos que se configuraron como deshabilitados y requeridos
  validateRequiredFields(): string[] {
    const controls = this.dynamicFormGroup.controls;
    const incompleteFields: string[] = [];

    Object.keys(controls).forEach((key) => {
      const control = controls[key];
      const isRequired = control.hasValidator(Validators.required);
      const isDisabled = control.disabled;
      const hasValue =
        control.getRawValue() !== '' &&
        control.getRawValue() !== null &&
        control.getRawValue() !== undefined;

      if (isRequired && isDisabled && !hasValue) {
        incompleteFields.push(key);
      }
    });

    return incompleteFields;
  }
}
