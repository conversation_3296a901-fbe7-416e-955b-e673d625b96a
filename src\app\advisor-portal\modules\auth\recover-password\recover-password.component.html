<section class="row container d-flex flex-wrap">
  <div class="col-12 col-md-6 d-flex flex-column align-items-center">
    <h2>
      {{'Auth.RecoverPassword' | translate}}
    </h2>
    <form
      [formGroup]="form"
      (ngSubmit)="recoverPassword()"
      style="margin: 32px 0"
    >
      <p>
        {{'Auth.EnterEmailAndDocumentNumber' | translate}}
      </p>

      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{'Auth.Email' | translate}}
        </mat-label>
        <input
          matInput
          placeholder="<EMAIL>"
          formControlName="email"
          required
        />
        <mat-error
          *ngIf="utilsService.isControlHasError(form, 'email', 'required')"
        >
          {{'ThisFieldIsRequired' | translate}}
        </mat-error>
        <mat-error
          *ngIf="utilsService.isControlHasError(form, 'email', 'pattern')"
        >
          {{'ThisEmailIsNotValid' | translate}}
        </mat-error>
      </mat-form-field>
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{'Auth.DoumentNumber' | translate}}
        </mat-label>
        <input
          matInput
          placeholder="{{'Auth.DoumentNumber' | translate}}"
          formControlName="documentNumber"
          required
        />
        <mat-error
          *ngIf="
            utilsService.isControlHasError(form, 'documentNumber', 'required')
          "
        >
          {{'ThisFieldIsRequired' | translate}}
        </mat-error>
      </mat-form-field>
      <div class="col-12 mt-2 d-flex justify-content-around">
        <a (click)="goToLogIn()" class="btn btn-terciary d-inline-flex">
          {{'Back' | translate}}
        </a>
        <button
          type="submit"
          [disabled]="!valid"
          class="btn btn-primary d-inline-flex"
        >
          {{'Auth.RecoverPassword' | translate}}
        </button>
      </div>
    </form>
  </div>
</section>
