import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PageAllIndividualPolicyComponent } from './page-all-individual-policy.component';

describe('PageAllIndividualPolicyComponent', () => {
  let component: PageAllIndividualPolicyComponent;
  let fixture: ComponentFixture<PageAllIndividualPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ PageAllIndividualPolicyComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PageAllIndividualPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
