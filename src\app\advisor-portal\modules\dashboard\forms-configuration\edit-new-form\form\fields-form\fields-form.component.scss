.subtitle{
    margin: 0px !important;
}

.label-button {
    font-size: 1.2rem !important;
    font-weight: bold;
}

a {
    text-decoration: none;
}
.group-flow{
    background-color: #F2F3F6;
}

::ng-deep .mat-mdc-tab-body-content {
    height: 100%;
    overflow: hidden;
}

.button-container {
    display: flex;
    justify-content: center;
    gap: 10px; /* Espacio entre botones */
}

.button-container button {
      margin: 10px;
}

mat-hint{
    color: gray;
}

.title-local {
    font-weight: bold;
}
  