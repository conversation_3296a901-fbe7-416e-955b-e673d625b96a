import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Router, RouterModule } from '@angular/router';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Subscription, catchError, of } from 'rxjs';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { TemplateModel } from 'src/app/shared/models/templates';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-all-templates',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TableComponent,
    TranslateModule,
    RouterModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './all-templates.component.html',
  styleUrls: ['./all-templates.component.scss']
})
export class AllTemplatesComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessCountry: number=0;
  
  constructor(  
    private _messageService: MessageService,
    public _utilsService: UtilsService, 
    public _translateService: TranslateService,
    private _settingService: SettingService,
    private _parameterService: ParametersService,
    private _customRouter: CustomRouterService,

  ){} 

  ngOnInit(): void {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if(!(Object.keys(response).length === 0)){
            this.idBusinessCountry=response.enterprise.pkIIdBusinessByCountry;
            this.getTemplates(response.enterprise.pkIIdBusinessByCountry);
          }
        }
      );
  }

  estructTableTemplates: BodyTableModel[] = [
    { 
      columnLabel: this._translateService.instant('Template.TemplateName'), 
      columnValue: 'vName' 
    },
    {
      columnLabel: this._translateService.instant('CreationDate'),
      columnValue: 'dDateCreation',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsService.changeStatusValue(item)
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: '',
      columnValue: 'delete',
      columnIcon: 'delete',
    }
  ];

  dataTableTemplates: TemplateModel[] = [];

  controller(evt:IconEventClickModel){
    if(evt.column=="modify"){
      this._customRouter.navigate([
        `/dashboard/templates/modify/${evt.value.pkIIdTemplate}/${this.idBusinessCountry}`,
      ]);
    }else{
      this._messageService
        .messageConfirmationAndNegationReverseButton(
          this._translateService.instant('Delete') + '?',
          '',
          'warning',
          this._translateService.instant('Cancel'),
          this._translateService.instant('Confirm')
        )
        .then((result) => {
          if (result) {
            this.deleteTemplate(evt.value.pkIIdTemplate);
            this._messageService.messageConfirmatio(              
              this._translateService.instant('Deleted'),
              '',
              'success',
              this._translateService.instant('Continue')
            );
          }
        });    
    }
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  validForm(event: boolean) {
    //this.formValid = event;
  }

  newTemplateButton_Click(){
    this._customRouter.navigate([`/dashboard/templates/new/${this.idBusinessCountry}`]);
  }
  
  getTemplates(idBusinessByCountry: number){
    this._parameterService.getAllTemplates(idBusinessByCountry)
    .pipe(
      catchError((error) => {
        console.log('No existen etapas asociadas')
        this.dataTableTemplates = [];
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log('El tipo de datos devueltos es un array vacío.');
        this.dataTableTemplates = [];
      } else {
        if (resp.error) {
          this._messageService.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.dataTableTemplates = resp.result;
        }
      }
    });    
  }

  //Borra una plantilla
  deleteTemplate(idTemplate: number)
  {
    this._parameterService
      .deleteTemplate(idTemplate)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('DeleteMessage'),
              ''
            );
            this.getTemplates(this.idBusinessCountry);
          }
        }
      });
  }
}

