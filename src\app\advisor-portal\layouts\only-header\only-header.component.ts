import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { HeaderComponent } from '../shared/header/header.component';
import { FooterComponent } from '../shared/footer/footer.component';
import { HeaderTestComponent } from '../shared/header-test/header-test.component';

@Component({
  selector: 'app-only-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeaderComponent,
    FooterComponent,
    HeaderTestComponent
  ],
  templateUrl: './only-header.component.html',
  styleUrls: ['../shared/platform.scss']
})
export class OnlyHeaderComponent {

}
