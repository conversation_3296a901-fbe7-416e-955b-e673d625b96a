import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { UploadImageComponent } from 'src/app/shared/components/upload-image/upload-image.component';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TranslateService } from '@ngx-translate/core';
import {
  ModifyOrganizationChart,
  SectionIndexModel,
} from 'src/app/shared/models/configuration-index';

@Component({
  selector: 'app-organization-chart',
  standalone: true,
  imports: [CommonModule, UploadImageComponent],
  templateUrl: './organization-chart.component.html',
  styleUrls: ['./organization-chart.component.scss'],
})
export class OrganizationChartComponent implements OnInit, OnDestroy {
  @Input() objetOrganizationChart: SectionIndexModel =
    SectionIndexModel.fromObj({});
  imageSrc: string = '';
  firstTime: boolean = true;
  showContTowBtns: boolean = false;
  showContOneBtns: boolean = true;
  imageName: string = '';
  extension: string = '';

  constructor(
    private _businessService: BusinessService,
    private _msgSvc: MessageService,
    private _translateService: TranslateService
  ) {}
  ngOnInit(): void {
    this.uploadImageFirstTime();
  }

  changeFile(event: any) {
    if (event) {
      this.imageSrc = event.dataString;
      this.imageName = event.dataJson[0].name;
      this.extension = event.dataJson[0].type.split('/')[1];
      this.uploadOrganizationChart();
    }
  }

  //Carga la imagen guardada en BD la primera vez que se ingresa al apartado.
  uploadImageFirstTime() {
    if (
      this.objetOrganizationChart.vFileName &&
      this.objetOrganizationChart.vImageBase64
    ) {
      let extension =
        this.objetOrganizationChart.vFileName.split('.')[
          this.objetOrganizationChart.vFileName.split('.').length - 1
        ];
      this.imageSrc = `data:image/${extension};base64,${this.objetOrganizationChart.vImageBase64}`;
      this.showContOneBtns = false;
      this.showContTowBtns = true;
      this.imageName = this.objetOrganizationChart.vFileName;
      this.firstTime = false;
    }
  }

  uploadOrganizationChart() {
    let extension: string = '';
    let base64: string = '';
    let payload: ModifyOrganizationChart = {
      imageBase64: ``,
      pkIIdSectionIndex: this.objetOrganizationChart.pkIIdSectionIndexBusiness,
    };
    if (
      this.objetOrganizationChart.vFileName &&
      this.objetOrganizationChart.vImageBase64
    ) {
      extension =
        this.objetOrganizationChart.vFileName.split('.')[
          this.objetOrganizationChart.vFileName.split('.').length - 1
        ];
      payload = {
        imageBase64: `data:image/${extension};base64,${this.objetOrganizationChart.vImageBase64}`,
        pkIIdSectionIndex:
          this.objetOrganizationChart.pkIIdSectionIndexBusiness,
      };
    } else {
      base64 = this.imageSrc.split(',')[this.imageSrc.split(',').length - 1];
      payload.imageBase64 = `data:image/${this.extension};base64,${base64}`;
    }

    this._businessService
      .uploadOrganizationChart(payload)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant(
                'HomeAdvisors.OrganizationChart.SuccessfulRegistration'
              )
            );
          }
        }
      });
  }

  //Elimina la imagen del organigrama
  deleteOrganizationChartImage() {
    this._businessService
      .deleteOrganizationChart(
        this.objetOrganizationChart.pkIIdSectionIndexBusiness
      )
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant('DeleteMessage')
            );
          }
        }
      });
  }

  deleteFile(event: boolean) {
    if (event) {
      this.deleteOrganizationChartImage();
    }
  }

  ngOnDestroy(): void {}
}
