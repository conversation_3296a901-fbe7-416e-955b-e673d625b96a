import { Injectable } from '@angular/core';
import { PolicyModel, RiskTableDataModel } from '../models';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class PolicyTableService {
  constructor(private _translateService: TranslateService) {}

  //Función que pone estilos personalziados a los tipos de la póliza, según sea el caso.
  changePolicyStatusValue(item: PolicyModel) {
    switch (item.statePolicy) {
      case 'Activa':
        return (
          '<span class="generalState greenState">' +
          this._translateService.instant('Activa') +
          '</span>'
        );
      case 'Inactiva':
        return (
          '<span class="generalState blueState">' +
          this._translateService.instant('Inactive') +
          '</span>'
        );
      case 'Vencida':
        return (
          '<span class="generalState yellowState">' +
          this._translateService.instant('Expired') +
          '</span>'
        );
      case 'Cancelada':
        return (
          '<span class="generalState redState">' +
          this._translateService.instant('Cancellation') +
          '</span>'
        );

      default:
        return (
          '<h5><span class="generalState redState">' +
          this._translateService.instant('Error status') +
          '</span></h5>'
        );
    }
  }

  //Función que pone estilos personalziados a los tipos de estado de los riesgos, según sea el caso.
  changeRiskStatusValue(item: RiskTableDataModel) {
    switch (item.state) {
      case 'Activa':
        return (
          '<span class="generalState greenState">' +
          this._translateService.instant('Active') +
          '</span>'
        );
      case 'Vencida':
        return (
          '<span class="generalState yellowState">' +
          this._translateService.instant('Vencido') +
          '</span>'
        );
      case 'Excluido':
        return (
          '<span class="generalState redState">' +
          this._translateService.instant('Excluded') +
          '</span>'
        );

      default:
        return (
          '<h5><span class="generalState redState">' +
          this._translateService.instant('Error status') +
          '</span></h5>'
        );
    }
  }
}
