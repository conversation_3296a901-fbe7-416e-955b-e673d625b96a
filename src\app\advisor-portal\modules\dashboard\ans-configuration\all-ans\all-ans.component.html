<div class="mt-5">
  <app-choose-country-and-company
    (valueForm)="validFormBusinessByCounty($event)"
  ></app-choose-country-and-company>
</div>

<div class="mt-4 mb-3">
  <h2>{{ "AnsConfiguration.Subtitle" | translate }}</h2>
  <p class="description">
    {{ "AnsConfiguration.SubtitleDescription" | translate }}
  </p>
</div>

<div class="row mt-2">
  <div class="col-md-9">
    <mat-form-field class="w-100">
      <mat-label>
        {{ "AnsConfiguration.FilterByLabel" | translate }}
      </mat-label>
      <input
        (keyup.enter)="searchAns()"
        [(ngModel)]="keyword"
        (ngModelChange)="onInputChange($event)"
        matInput
        type="text"
        class="form-control"
        placeholder="Filtrar por:"
      />
      <mat-icon class="hand click" (click)="searchAns()" matSuffix>search</mat-icon>
    </mat-form-field>
  </div>
  <div class="col-md-3">
    <div class="d-flex">
      <button
        type="button"
        class="mx-3"
        (click)="openModalFilter()"
        mat-raised-button
        color="primary"
      >
        {{ "Filter" | translate }}
        <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
      </button>
      <div class="btn-group dropup">
        <button
          type="button"
          id="orderAns"
          data-bs-toggle="dropdown"
          aria-expanded="false"
          mat-raised-button
          color="primary"
          class="dropdown-toggle"
        >
          {{ "Quotation.SorButton" | translate }}
        </button>
        <ul class="dropdown-menu" aria-labelledby="orderAns">
          <li>
            <a class="dropdown-item" (click)="orderTable(0)">
              {{ "AnsConfiguration.MoreRecent" | translate }}</a
            >
          </li>
          <li>
            <a class="dropdown-item" (click)="orderTable(1)">{{
              "AnsConfiguration.Oldest" | translate
            }}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>

<div class="row mt-2">
  <app-table
    [displayedColumns]="estructAnsTable"
    [data]="dataAnsTable"
    [IsStatic]="false"
    [pageIndex]="pageIndex"
    [pageSize]="pageSize"
    [amountRows]="amountRows"
    (pageChanged)="onPageChange($event)"
    (iconClick)="controller($event)"
  ></app-table>
</div>
<div class="row">
  <div class="col-md-12">
    <button
      type="button"
      class="w-auto"
      (click)="openModalAddAns()"
      mat-raised-button
      color="primary"
    >
      {{ "AnsConfiguration.AddAnsButton" | translate }}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
</div>

<div class="mt-4 mb-3">
  <h3>{{ "AnsConfiguration.Subtitle2" | translate }}</h3>
  <p class="description">
    {{ "AnsConfiguration.SubtitleDescription2" | translate }}
  </p>
</div>

<div class="row">
  <div class="col-md-12">
    <button
      type="button"
      class="w-auto"
      mat-raised-button
      color="primary"
      (click)="openModalGeneralConditions()"
      [disabled]="dataAnsTable.length <= 0"
    >
      {{ "AnsConfiguration.ModifyGeneralConditionsButton" | translate }}
      <mat-icon iconPositionEnd fontIcon="create"></mat-icon>
    </button>
  </div>
</div>

<!-- Modal Filtro -->
<ng-template #filterModal>
  <app-modal2
    [titleModal]="'AnsConfiguration.Modal.Title' | translate"
    [showCancelButtonBelow]="false"
  >
    <ng-container body>
      <form [formGroup]="formFilter">
        <div class="row">
          <h5>{{ "AnsConfiguration.Modal.Subtitle" | translate }}</h5>
        </div>
        <div class="row">
          <div class="col-md-12 p-0 mb-2">
            <mat-checkbox class="example-margin" formControlName="active">{{
              "AnsConfiguration.Modal.Active" | translate
            }}</mat-checkbox>
            <mat-checkbox class="example-margin" formControlName="inactive">{{
              "AnsConfiguration.Modal.Inactive" | translate
            }}</mat-checkbox>
          </div>
        </div>
        <div class="row">
          <!-- Select de Proceso -->
          <div class="col-md-6">
            <mat-form-field id="processes" class="w-100">
              <mat-label>
                {{ "AnsConfiguration.Table.Process" | translate }}
              </mat-label>
              <mat-select formControlName="idProcess">
                <mat-option
                  *ngFor="let item of processes"
                  [value]="item.pkIIdProcessFamily"
                  >{{ item.vNameProcess }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>
          <!-- Select de Producto -->
          <div class="col-md-6">
            <mat-form-field id="product" class="w-100">
              <mat-label>
                {{ "AnsConfiguration.Table.Product" | translate }}
              </mat-label>
              <mat-select formControlName="idProduct">
                <mat-option
                  *ngFor="let item of products"
                  [value]="item.pkIIdProductModule"
                  >{{ item.vNameProduct }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <div class="row">
          <!-- Select de Etapa -->
          <div class="col-md-6">
            <mat-form-field id="stage" class="w-100">
              <mat-label>
                {{ "AnsConfiguration.Table.Stage" | translate }}
              </mat-label>
              <mat-select formControlName="idStage">
                <mat-option
                  *ngFor="let item of stage"
                  [value]="item.pkIIdStage"
                  >{{ item.vNameStage }}</mat-option
                >
              </mat-select>
            </mat-form-field>
            <p class="description">
              {{ "AnsConfiguration.Modal.DescriptionStage" | translate }}
            </p>
          </div>
          <!-- Select de Estado -->
          <div class="col-md-6">
            <mat-form-field id="product" class="w-100">
              <mat-label> {{ "Status" | translate }} </mat-label>
              <mat-select formControlName="idState">
                <mat-option
                  *ngFor="let item of state"
                  [value]="item.pkIIdStageByState"
                  >{{ item.vNameState }}</mat-option
                >
              </mat-select>
            </mat-form-field>
            <p class="description">
              {{ "AnsConfiguration.Modal.DescriptionState" | translate }}
            </p>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonCenter>
      <button
        (click)="resetSearch()"
        type="button"
        mat-raised-button
        class="w-auto mr-2"
      >{{ "MyQuotation.AllQuotation.Clean" | translate }}
      </button>
    </ng-container>

    <ng-container customButtonRight>
      <button
        (click)="applyFilter()"
        type="button"
        mat-raised-button
        color="primary"
      >
        {{ "Apply" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

<!-- Modal Add ANS -->
<ng-template #AddAnsModal>
  <app-modal2 [titleModal]="titleModal" (closeModal)="modalCloseEvent($event)">
    <ng-container body>
      <div class="col-md-12 mt-3">
        <app-edit-new-ans
          (formValid)="getValidFormAddAns($event)"
          (modalAction)="getModalAction($event)"
          (closeModal)="getCloseModal($event)"
          [pkIIdGeneralAns]="pkIIdGeneralAns"
        ></app-edit-new-ans>
      </div>
    </ng-container>

    <ng-container customButtonRight>
      <button
        *ngIf="!showBtnEdit"
        (click)="addAns()"
        type="button"
        mat-raised-button
        color="primary"
        [disabled]="!validFormAddAns"
      >
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
        {{ "AnsConfiguration.AddAnsButton" | translate }}
      </button>

      <button
        *ngIf="showBtnEdit"
        (click)="editAns()"
        type="button"
        mat-raised-button
        color="primary"
        [disabled]="!validFormAddAns"
      >
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "SaveChanges" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

<!-- Modal Modificar condiciones generales -->
<ng-template #generalConditions>
  <app-modal2 [border]="false">
    <ng-container body>
      <app-general-conditions
        (currentFormValue)="getCurrentFormValue($event)"
      ></app-general-conditions>
    </ng-container>

    <ng-container customButtonRight>
      <button
        (click)="validateUpdateGeneralConditionMassive()"
        type="button"
        mat-raised-button
        color="primary"
        [disabled]="!validateFormGeneralConditions"
      >
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "SaveChanges" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
