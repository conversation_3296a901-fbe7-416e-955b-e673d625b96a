import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule, Location } from '@angular/common';

import {
  FormBuilder,
  FormGroup,
  FormArray,
  FormsModule,
  ReactiveFormsModule,
  Validators,
  FormControl,
} from '@angular/forms';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';

import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CatalogModel } from 'src/app/shared/models/catalog-setting';
import { SettingService } from 'src/app/shared/services/setting/setting.service';

@Component({
  selector: 'app-edit-catalog-management',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  templateUrl: './edit-catalog-management.component.html',
  styleUrls: ['./edit-catalog-management.component.scss'],
})
export class EditCatalogManagementComponent implements OnInit {
  @Input() idCatalog: number = 0;
  @Input() idBusinessCountry: number = 0;
  @Input() idCountry: number = 0;
  @Output() setActiveTabCatalog = new EventEmitter<any>();

  idUser: number = 0;
  nameOriginalCatalog: string = '';
  dataSetting: any = {};
  dataJson: any = {};
  isEdit: boolean = false;

  form: FormGroup = new FormGroup({});
  ngOnInit(): void {
    this.getIdUserSession();
    if (this.idCatalog > 0) this.getCatalogById(this.idCatalog);
  }

  constructor(
    private _catalogService: CatalogSettingService,
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    public _utilsSvc: UtilsService,
    private _settingService: SettingService,
    private _fb: FormBuilder,
    public _location: Location
  ) {
    this.initForm();
  }

  async getIdUserSession() {
    this.dataSetting = await this._settingService.getDataSettingInit();
    this.idUser = this.dataSetting.idUser;
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdCatalog: [0],
      vName: ['', Validators.required],
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  getCatalogById(idCatalog: number) {
    this._catalogService
      .getCatalogById(idCatalog)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.nameOriginalCatalog = response.result.vName;
            this.form.patchValue(response.result);
            this.isEdit = true;
          }
        }
      });
  }

  next() {
    if (this.valid) {
      if (this.nameOriginalCatalog !== this.form.get('vName')?.value)
        this.validateIfExistNameCatalog();
      else {
        this.saveCatalog();
      }
    }
  }

  validateIfExistNameCatalog() {
    this._catalogService
      .checkExistNameCatalog(this.form.get('vName')?.value)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            if (!response.result) {
              this.validateInsertOrUpdate();
            } else {
              this._msgSvc.messageInfo(
                'Error',
                this._translateService.instant('CatalogSetting.MessagesCatalogo.ExistingCatalog') +
                this.form.get('vName')?.value
              );
            }
          }
        }
      });
  }

  validateInsertOrUpdate() {
    if (this.isEdit) {
      if (this.idCatalog > 0) this.saveCatalog();
      else this.goBack();
    } else this.saveCatalog();
  }

  saveCatalog() {
    if (this.valid) {
      /// Add data in object JSON
      this.dataJson = {
        catalogs: {
          fkIIdBusinessByCountry: this.idBusinessCountry,
          fkIIdCountry: this.idCountry,
          pkIIdCatalog: this.idCatalog,
          vName: this.form.get('vName')?.value,
          BActive: true,
          dDateModification: '',
          fkIIdUserResponsable: this.idUser,
        },
      };
      this.saveCurrentDataCatalogComposite();

      /// Add data to return in a JSON
      let dataReturn = {
        value: 2,
        id: 0,
      };

      this._msgSvc.messageSuccess(
        '',
        this._translateService.instant(
          'CatalogSetting.MessagesCatalogo.SuccessfulMessageCreated'
        )
      );

      /// Return event for active tab catalog sheet
      this.setActiveTabCatalog.emit(dataReturn);
    }
  }

  saveCurrentDataCatalogComposite() {
    this._catalogService.setCurrentEditNewCompositeSetting(this.dataJson);
  }

  goBack() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant('Out'),
        this._translateService.instant('CatalogSetting.MessagesCatalogo.UnsavedData'),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._location.back();
          this.form.reset();
        }
      });
  }
}
