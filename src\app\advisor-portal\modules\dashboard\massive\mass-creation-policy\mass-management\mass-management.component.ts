import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, finalize, of } from 'rxjs';
import { DragDropUploadComponent } from 'src/app/shared/components/drag-drop-upload/drag-drop-upload.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { CatalogModel } from 'src/app/shared/models/catalog-setting';
import {
  RequestGetTemplateModel,
  TypeOfManagementModel,
} from 'src/app/shared/models/massive';
import { PolicyModel } from 'src/app/shared/models/policy';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BodyTableModel } from 'src/app/shared/models/table';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { UserService } from 'src/app/shared/services/user/user.service';

@Component({
  selector: 'app-mass-management',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    DragDropUploadComponent,
  ],
  templateUrl: './mass-management.component.html',
  styleUrls: ['./mass-management.component.scss'],
})
export class MassManagementComponent implements OnInit {
  dataTablePolicy: PolicyModel[] = [];
  estructTablePolicy: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      ),
      columnValue: 'idPolicy',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      ),
      columnValue: 'insuranceName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      ),
      columnValue: 'productName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyName'
      ),
      columnValue: 'policyName',
      functionValue: (item: any) => this.validatePolicyName(item),
    },
  ];
  typesOfManagement: TypeOfManagementModel[] = [];
  files: any[] = [];
  catalogList: CatalogModel[] = [];
  fileName: string = '';
  templateName: string = '';
  showFileUpload: boolean = false;
  TypeOfManagementSelected: number = 0;
  policyType: number = 0;
  policyData!: PolicyModel;
  idBusinessByCountry: number = 0;
  idCatalog: number = 0;
  idLoad: number = 0;
  idUser: number = 0;
  template!: File;
  
  private localStorageService = inject(LocalStorageService);
  
  constructor(
    private _translateService: TranslateService,
    private _messageService: MessageService,
    private _fieldService: FieldService,
    private _customeRouter: CustomRouterService,
    private _activatedRoute: ActivatedRoute,
    private _fileService: FileService,
    private _catalogService: CatalogSettingService,
    private _userService: UserService,
    private _spinnerService: SpinnerService
  ) {
    this.getBusinessByCountry();
  }

  async ngOnInit() {
    this.getPolicyData();
    this.getAllTypesOfManagement();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTablePolicy[0].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      );
      this.estructTablePolicy[1].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      );
      this.estructTablePolicy[2].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      );
      this.estructTablePolicy[3].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyName'
      );
    });
    this.idUser = await this._userService.getUserIdSesion();
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
        if (this.idBusinessByCountry > 0) {
          this.idBusinessByCountry = this.idBusinessByCountry;
        }
      }
    });
  }

  //Obtiene la información de la póliza.
  getPolicyData() {
    if (this.localStorageService.getItem('selectedPolicy')) {
      const policyData: PolicyModel = JSON.parse(
        this.localStorageService.getItem('selectedPolicy') || ''
      );
      this.policyData = policyData;
      this.dataTablePolicy.push(policyData);
      this.policyType = policyData.idPolicyType;
    }
  }

  //Obtiene todos los tipos de gestiones registrados en el sistema.
  getAllTypesOfManagement() {
    this._fileService
      .getAllTypesOfManagement()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.typesOfManagement = resp.result;
        }
      });
  }

  //Obtiene la plantilla según el tipo de gestión.
  getTemplate() {
    const paylaod: RequestGetTemplateModel = {
      beneficiariesNumber: this.policyData.iBeneficiariesNumber || 0,
      isHasCredit: this.policyData.bAssociatedWithCredit || false,
      managementType: this.TypeOfManagementSelected,
      policyId: this.policyData.idPolicy,
      policyType: this.policyData.idPolicyType,
      idCatalog: this.idCatalog,
      typeOfVality: this.policyData.fkIIdValidityType || 0,
    };
    this._fieldService
      .generateJSONFieldByIdPolicy(paylaod)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            this.downloadTemplateByBase64(
              resp.result,
              this.generateTemplateName()
            );
            const file = this.base64ToFile(resp.result, this.templateName);
            if (file) {
              this.template = file;
            } else {
            }
          }
        }
      });
  }

  //Obtine la lista de cátalogos asociados a una empresa país.
  getListCatalog(idBusinessCountry: number) {
    this._catalogService
      .getListCatalog(idBusinessCountry, 0)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.catalogList = response.result;
          }
        }
      });
  }

  //Compara 2 archivos excel para verificar si las cabeceras son iguales.
  comparerExcelFiles(files: FormData) {
    this._fileService
      .comparerExcelFiles(files)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.length > 0) {
            this._messageService.messageErrorCustomer(
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadTitle'
              ),
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadSubtitle'
              )
            );
          } else {
            let jsonAditional: any = {
              idPolicy: this.policyData.idPolicy,
            };
            if (this.TypeOfManagementSelected === 4) {
              jsonAditional.idCatalogCancellation = this.idCatalog;
            } else if (this.TypeOfManagementSelected === 2) {
              jsonAditional.idCatalogExclusions = this.idCatalog;
            }
            const payload = new FormData();
            payload.append(
              `IdManagementType`,
              this.TypeOfManagementSelected.toString()
            );
            payload.append(`IdUser`, this.idUser.toString());
            payload.append(`File`, this.files[0]);
            payload.append(`JsonAditional`, JSON.stringify(jsonAditional));
            this.uploadMassivePolicyTask(payload);
          }
        }
      });
  }

  //Guarda la carga masiva en la BD.
  async uploadMassivePolicyTask(files: FormData) {
    await this._spinnerService.show();
    this._fileService
      .uploadMassivePolicyTask(files)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        }),
        finalize(() => {
          this._spinnerService.hide();
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            this._messageService
              .messageConfirmatioCustomer(
                `${this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadTitle'
                )} ${resp.result}`,
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadSubtitle'
                ),
                'success',
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadBtnLeft'
                ),
                this._translateService.instant('Continuar')
              )
              .then((result) => {
                if (result) {
                  this._customeRouter.navigate([
                    `dashboard/massive/load-viewer/${this.idBusinessByCountry}`,
                  ]);
                }else{
                  this._customeRouter.navigate([
                    `dashboard/massive/mass-creation-policy/${this.idBusinessByCountry}`,
                  ]);
                }
              });
          }
        }
      });
  }

  //Valida si la póliza tiene nombre y depende el caso renderiza un valor en la tabla.
  validatePolicyName(policy: PolicyModel): string {
    return policy.policyName ? policy.policyName : 'No aplica';
  }

  //Filtra la lista de gestiones por medio del tipo de póliza.
  getFilterOption(): TypeOfManagementModel[] {
    if (this.policyType === 2 || this.policyType === 3) {
      return this.typesOfManagement.filter(
        (option: TypeOfManagementModel) =>
          option.pkIIdManagementType === 1 ||
          option.pkIIdManagementType === 2
      );
    } else {
      return this.typesOfManagement.filter(
        (option: TypeOfManagementModel) =>
          option.pkIIdManagementType === 3 ||
          option.pkIIdManagementType === 4
      );
    }
  }

  //Detecta los cambios del select de tipo de gestión.
  onTypeManagement(event: any) {
    this.template = new File([], '');
    this.files = [];
    this.idCatalog = 0;
    if (event.source._value) {
      this.TypeOfManagementSelected = event.source._value;
      this.generateTemplateName();
      if (event.source._value === 2 || event.source._value === 4) {
        this.getListCatalog(this.idBusinessByCountry);
      } else {
        this.generateJSONFieldByIdPolicy();
      }
      event.source._value > 0
        ? (this.showFileUpload = true)
        : (this.showFileUpload = false);
    }
  }

  //Detecta los cambios del select  de catalogos.
  onCatalog(event: any) {
    this.template = new File([], '');
    if (event.source._value) {
      this.idCatalog = event.source._value;
      if (this.idCatalog > 0) {
        this.generateJSONFieldByIdPolicy();
      }
    }
  }

  //Función que se encarga de llamar a la función que devuelve el base64 con el template según el tipo de gestión.
  downloadTemplate() {
    if (
      this.TypeOfManagementSelected === 2 ||
      this.TypeOfManagementSelected === 4
    ) {
      if (this.idCatalog > 0) {
        this.getTemplate();
      } else {
        this._messageService.messageInfo(this._translateService.instant('BulkUpload.PopUp.selectCatalog'), '');
      }
    } else {
      this.getTemplate();
    }
  }

  //Detecta los eventos de componente de cargue de archivos.
  saveFiles(files: File[]) {
    if (this.validateFileSize(files[0].size)) {
      this.files = files;
      if (this.files.length > 0) {
        this.fileName = this.files[0].name;
      }
    }
  }

  //Elimina el file cargado.
  deleteFile() {
    this.files = [];
    this.fileName = '';
  }

  //Función que se ejecuta al dar click en botón cerrar.
  close() {
    this._customeRouter.navigate([
      `dashboard/massive/mass-creation-policy/${this.idBusinessByCountry}`,
    ]);
  }

  //Función que descarga une xcel, basado en un base64.
  downloadTemplateByBase64(base64Data: string, fileName: string) {
    // Convierte el base64 a un Blob
    const byteCharacters = atob(base64Data); // Decodifica base64
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    // Crea un enlace de descarga
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = fileName; // El nombre que tendrá el archivo descargado
    link.click();
  }

  generateTemplateName(): string {
    switch (this.TypeOfManagementSelected) {
      case 1:
        this.templateName = 'plantilla_inclusiones';
        return 'plantilla_inclusiones';
      case 2:
        this.templateName = 'plantilla_exclusiones';
        return 'plantilla_exclusiones';
      case 3:
        this.templateName = 'plantilla_cargapólizas';
        return 'plantilla_cargapólizas';
      case 4:
        this.templateName = 'plantilla_cancelaciónpólizas';
        return 'plantilla_cancelaciónpólizas';
      default:
        this.templateName = 'plantilla';
        return 'plantilla';
    }
  }

  //Convierte un archivo de su representación de base64 a tipo File.
  base64ToFile(base64String: string, fileName: string): File | null {
    try {
      // Limpiar la cadena Base64 (quitar saltos de línea o espacios adicionales)
      const cleanedBase64String = base64String.replace(/\s/g, '');

      // Verificar si hay prefijo 'data:' y quitarlo si es necesario
      const base64Data = cleanedBase64String.includes(',')
        ? cleanedBase64String.split(',')[1]
        : cleanedBase64String;

      // Decodificar la cadena Base64
      const byteString = atob(base64Data);

      // Crear un array de bytes
      const byteNumbers = new Array(byteString.length);
      for (let i = 0; i < byteString.length; i++) {
        byteNumbers[i] = byteString.charCodeAt(i);
      }

      // Crear un Uint8Array con los bytes
      const byteArray = new Uint8Array(byteNumbers);

      // Especificar el tipo MIME correcto (en este caso, para un archivo Excel)
      const mimeType =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

      // Crear el blob
      const blob = new Blob([byteArray], { type: mimeType });

      // Crear y retornar el archivo a partir del blob
      return new File([blob], fileName, { type: mimeType });
    } catch (error) {
      return null;
    }
  }

  //Función que guarda la carga de la póliza suministrada.
  loadPolic() {
    if (this.files.length >= 1) {
      this._messageService
        .messageConfirmationAndNegation(
          this._translateService.instant('BulkUpload.PopUp.ValidateLoadTitle'),
          '',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            const files: File[] = [this.template, this.files[0]];
            const payload = new FormData();

            files.forEach((element) => {
              payload.append(`Files`, element);
            });

            this.comparerExcelFiles(payload);
          }
        });
    }
  }

  //Valida el tamño maximo permitido del archivo cargado.
  validateFileSize(sizeFile: number): boolean {
    const maxSizeInMB = 25; // Tamaño máximo en MB
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024; // Convertimos MB a bytes

    if (sizeFile > maxSizeInBytes) {
      this._messageService.messageInfo(
        this._translateService.instant('MaximumFileSizeTitle'),
        this._translateService.instant('MaximumFileSizeSubtitle')
      );
      return false;
    }
    return true;
  }

  //Obtiene la plantilla en formato file según el tipo de gestión.
  generateJSONFieldByIdPolicy() {
    const paylaod: RequestGetTemplateModel = {
      beneficiariesNumber: this.policyData.iBeneficiariesNumber || 0,
      isHasCredit: this.policyData.bAssociatedWithCredit || false,
      managementType: this.TypeOfManagementSelected,
      policyId: this.policyData.idPolicy,
      policyType: this.policyData.idPolicyType,
      idCatalog: this.idCatalog,
      typeOfVality: this.policyData.fkIIdValidityType || 0,
    };
    this._fieldService
      .generateJSONFieldByIdPolicy(paylaod)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            const file = this.base64ToFile(resp.result, this.templateName);
            if (file) {
              this.template = file;
            }
          }
        }
      });
  }
}
