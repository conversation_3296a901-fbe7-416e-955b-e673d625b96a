import {
  Compo<PERSON>,
  OnIni<PERSON>,
  On<PERSON><PERSON>roy,
  Input,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatTabsModule } from '@angular/material/tabs';
import { CommonModule } from '@angular/common';
import { DpDatePickerModule } from 'ng2-date-picker';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatCardModule } from '@angular/material/card';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatDialog } from '@angular/material/dialog';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { catchError, Subscription, throwError, firstValueFrom } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/store/app.reducers';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { BusinessComponentModel } from 'src/app/shared/models/business';
import {
  BodyUpdateQuoteFlowProperty,
  PaymentFlowProperties,
  QuoteFlowProperties,
} from 'src/app/shared/models/quote-flow';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import { ErrorHandlingService } from 'src/app/shared/services/error/errorHandlingService';
import { StepFormDataComponent } from "../edit-new-product/step-form-data/step-form-data.component";
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { TemplateModel } from 'src/app/shared/models/templates/template.model';

interface DataTableField {
  pkIIdField: number;
  vNameField: string;
  vNameFieldDb: string;
  fkIIdFieldType: number;
  fkIIdTab: number;
  bActive: boolean;
}

interface FieldType {
  pkIIdFieldType: number;
  vName: string;
}

interface Tab {
  pkIIdTab: number;
  vName: string;
}

interface GatewayConfig {
  vName: string;
  pkGatewayConfig: number;
}

@Component({
  selector: 'app-quote-flow',
  standalone: true,
  templateUrl: './quote-flow.component.html',
  styleUrls: ['./quote-flow.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    MatInputModule,
    MatTabsModule,
    MatFormFieldModule,
    MatSlideToggleModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatSelectModule,
    MatRadioModule,
    MatIconModule,
    DpDatePickerModule,
    MatDatepickerModule,
    MatCardModule,
    MatCheckboxModule,
    StepFormDataComponent,
  ],
})
export class QuoteFlowComponent implements OnInit, OnDestroy {
  @Input() productStage: any;
  insuranceCompanies: InsuranceCompanyModel[] = [];

  formQuoteCommunication: FormGroup = this._fb.group({
    bActive: [false],
    FkIdTemplate: [null]
  });

  formField: FormGroup = this._fb.group({
    pkIIdField: 0,
    bActive: [true],
    vNameField: [null, [Validators.required]],
    vNameFieldDb: [null, Validators.required],
    idForm: [null],
  });

  private _settingCountryAndCompanySubscription?: Subscription;
  productList?: Subscription;


  idForm: number = 0;
  config: any = {};
  typeDate: any[] = [];
  fieldTypes: FieldType[] = [];
  fieldSubs?: Subscription;
  product?: Subscription;
  productSubs?: Subscription;
  gatewayConfigs: GatewayConfig[] = [];
  emailsTemplates: TemplateModel[] = [];
  products: any = [];
  idBusinessCountry: number = 0;
  businessComponent?: BusinessComponentModel;

  idCountry: number = 0;
  isbQuoteFlowStageActive: boolean = false;
  form: FormGroup = this._fb.group(
    {
      id: [0],
      showCustomerPortal: [false, [Validators.required]],
      active: [true, [Validators.required]],
      name: [null, [Validators.required]],
      description: [null, Validators.required],
      logo: [null, [Validators.required]],
      logoCustomerPortal: [null, [Validators.required]],
      IdProductParent: [null, [Validators.required]],
      inCustomerPortal: [false, [Validators.required]],
      bQuoteFlowVisibleClients: [false, [Validators.required]],
      bQuoteFlowVisibleAdvisors: [false, [Validators.required]],
      bQuoteFlowRequiredLogin: [false, [Validators.required]],
      bQuoteFlowStageActive: [false, [Validators.required]],
      bQuoteFlowManualData: [null, [Validators.required]],
      bQuoteFlowDataIntegration: [null, [Validators.required]],
      bQuoteFlowCommunicationActive: [false, [Validators.required]],
      FkIdTemplate: [],
      listCoverages: [],
      comments: [null],
      coverages: [[]],
      questions: [[]],
      bQuoteFlowData:[null,[Validators.required]],
      vEmissionTitle:[null],
      vEmissionSubtitle:[null],
      includeProcessNumber:[null],
      bIsUrlCheck:[null]
    },
    {
      validators: this.portalCheckerValidator(),
    }
  );

  idProduct: number = 0;
  idProductReply: number = 0;

  emissionStageProperties!: QuoteFlowProperties;
  paymentStageProperties!: PaymentFlowProperties;

  // ------------------------------ Pago
  isbPayQuoteFlowStageActive: boolean = false;
  formPagos: FormGroup = this._fb.group(
    {
    bPayQuoteFlowVisibleClients: [false, [Validators.required]],
    bVisibleAdvisors: [false, [Validators.required]],
    bVisibleClients: [false, [Validators.required]],
    bRequiredLogin: [false, [Validators.required]],
    bIsActive: [false, [Validators.required]],
    bManualData: [false, [Validators.required]],
    bEmbeddedGateway: [false, [Validators.required]],
    vButtonText: [''],
    vDisclaimerText: [''],
    bActiveDisclaimerText: [false],
    bActiveProductCertificate: [false],
    vTextDisclaimerLink: [''],
    vLinkDisclaimer: [''],
    bPayQuoteFlowDataEmbedded: [true, [Validators.required]],
    fkIIdGatewayConfig: [''],
    bPaymentCommunicationActive: [false, [Validators.required]],
    fkIIdEmailTemplateCommunicationPayment: [],
    vHeader: [''],
    vTittle: [''],
    vSubTittle: [''],
    bActiveCoverageCertificate: [false],
    bActivePolicyDowmload: [false],
    bActiveContinueShopping: [false],
    bActiveCoverageVisualization: [false],
    bActiveStartValidity: [false],
    bActiveEndValidity: [false],
    }
  );

  formSkip: FormGroup = this._fb.group(
    {
      bSkipOffers: [false, [Validators.required]]
    }
  );
  // --------------------------------------

  constructor(
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    private _msgSvc: MessageService,
    public _fieldSvc: FieldService,
    public modalDialog: MatDialog,
    private _parametersService: ParametersService,
    private _store: Store<AppState>,
    private _settingService: SettingService,
    private _productService: ProductService,
    private _businessService: BusinessService,
    private _errorHandlingService: ErrorHandlingService
  ) {}

  /**
   * Initializes the component by setting up necessary parameters and subscriptions.
   *
   * This method is called when the component is initialized. It performs the following actions:
   * - Retrieves parameters needed for the component.
   * - Subscribes to the product state from the store and handles potential errors.
   * - Calls various methods to fetch data based on the retrieved product ID.
   * - Sets up value change subscriptions on various form controls to react to changes in their values,
   *   including cleaning names, fetching catalogs, and updating validators.
   * - Handles translation language changes.
   *
   * @returns {void} This method does not return any value.
   */
  ngOnInit(): void {
    this.getParameters();
    this.productSubs = this._store.select('product').subscribe((p) => {
      if (p.error) return this._msgSvc.messageError(p.error);
      this.idProduct = p.Product?.id == undefined ? 0 : p.Product?.id;
      this.getProductById(this.idProduct);
      this.getFieldType();
      this.getFormById(this.idProduct);
      this.getBusinessByCountry();
      this.getBusinessComponentByIdBusinessCountry();
      this._getEmailTemplates()
    });


    this.form.get('bQuoteFlowStageActive')?.valueChanges.subscribe((value) => {

      if (value != null) {
        this.isbQuoteFlowStageActive = value;
      }
    });

    this.formPagos.get('bIsActive')?.valueChanges.subscribe((value) => {
      if (value != null) {
        this.isbPayQuoteFlowStageActive = value;
      }
    });

    this.formPagos.get('vButtonText')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null)
        this.onClickPayment('vButtonText')
    });

    this.formPagos.get('vDisclaimerText')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null)
        this.onClickPayment('vDisclaimerText')
    });

    this.formPagos.get('vTextDisclaimerLink')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null)
        this.onClickPayment('vTextDisclaimerLink')
    });

    this.formPagos.get('vLinkDisclaimer')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null)
        this.onClickPayment('vLinkDisclaimer')
    });

    this.form.get('vEmissionTitle')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null) {
        this.updateQuoteFlowProperty({
          productId: this.idProduct,
          property:'VEmissionMessageTitle',
          value: value,
        })
      }
    });

    this.form.get('vEmissionSubtitle')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null) {
        this.updateQuoteFlowProperty({
          productId: this.idProduct,
          property:'VEmissionMessageSubtitle',
          value: value,
        })
      }
    });

    // Callback (Thank you page)
    this.formPagos.get('vHeader')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null)
        this.onClickPayment('vHeader')
    });
    this.formPagos.get('vTittle')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null)
        this.onClickPayment('vTittle')
    });
    this.formPagos.get('vSubTittle')?.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged(),
    ).subscribe((value) => {
      if (value != null)
        this.onClickPayment('vSubTittle')
    });


    //Buscando data de product
    this.getEmissionStageProperties();
    this._getPaymentStageProperties();
  }


  /**
   * Cleans up subscriptions when the component is destroyed.
   *
   * This method is called when the component is about to be destroyed. It unsubscribes from
   * all active subscriptions to prevent memory leaks and ensure proper cleanup.
   *
   * @returns {void} This method does not return any value.
   */
  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
    this.productList?.unsubscribe();
    this.fieldSubs?.unsubscribe();
    this.productSubs?.unsubscribe();
    this.product?.unsubscribe();
  }

  private _getPaymentStageProperties(): void {
    this._productService.getPaymentStageProperties(this.idProduct).subscribe(
      (response) => {
        if (response.result) {
          this.paymentStageProperties = response.result[0];
          this.formPagos.patchValue(response.result[0]);
        }
      },
      (error) => {
        this._errorHandlingService.handleError(error);
      }
    );
  }

  /**
   * Retrieves the emission stage properties of the current product using the product ID.
   * This method fetches the emission stage properties from the backend and updates the form controls
   * based on the received data. It handles both successful responses and errors.
   *
   * @returns {void}
   */
  getEmissionStageProperties(): void {
    this._productService.getEmissionStageProperties(this.idProduct).subscribe(
      (response) => {
        // If the response contains a result, it updates the form with the properties of the issue stage.
        if (response.result) {
          this.emissionStageProperties = response.result;

          // Updates form controls with values ​​received from the API
          this.form
            .get('bQuoteFlowStageActive')
            ?.setValue(this.emissionStageProperties.bQuoteFlowStageActive);
          this.form
            .get('bQuoteFlowDataIntegration')
            ?.setValue(this.emissionStageProperties.bQuoteFlowDataIntegration);
          this.form
            .get('bQuoteFlowManualData')
            ?.setValue(this.emissionStageProperties.bQuoteFlowManualData);
          this.form
            .get('bQuoteFlowRequiredLogin')
            ?.setValue(this.emissionStageProperties.bQuoteFlowRequiredLogin);
          this.form
            .get('bQuoteFlowVisibleAdvisors')
            ?.setValue(this.emissionStageProperties.bQuoteFlowVisibleAdvisors);
          this.form
            .get('bQuoteFlowVisibleClients')
            ?.setValue(this.emissionStageProperties.bQuoteFlowVisibleClients);
          this.form
            .get('bQuoteFlowCommunicationActive')
            ?.setValue(this.emissionStageProperties.bActiveEmissionCommunication);
          this.form
            .get('FkIdTemplate')
            ?.setValue(this.emissionStageProperties.fkIIdTemplateEmailEmissionComunication);
            this.form
            .get('vEmissionTitle')
            ?.setValue(this.emissionStageProperties.vEmissionMessageTitle);
          this.form
            .get('vEmissionSubtitle')
            ?.setValue(this.emissionStageProperties.vEmissionMessageSubtitle);
          this.form
            .get('bIsUrlCheck')
            ?.setValue(this.emissionStageProperties.bIsUrlCheck);
          this.form
            .get('includeProcessNumber')
            ?.setValue(this.emissionStageProperties.bIncludeProcessNumber);
            if(!this.emissionStageProperties.bQuoteFlowManualData && !this.emissionStageProperties.bQuoteFlowDataIntegration){
              this.form.get('bQuoteFlowData')?.setValue(null);
            } else {
              if(this.emissionStageProperties.bQuoteFlowManualData){
                this.form.get('bQuoteFlowData')?.setValue(true);
              } else {
                this.form.get('bQuoteFlowData')?.setValue(false);
              }
            }
        }
      },
      (error) => {
        //Handle the error using a dedicated error handling service
        this._errorHandlingService.handleError(error);
      }
    );
  }

  /**
   * Alterna la propiedad especificada de QuoteFlow para el producto actual.
   * Basado en el nombre de la propiedad proporcionada, este método recupera el valor actual del formulario,
   * alterna su estado booleano y envía el valor actualizado al servicio de backend.
   *
   * Las propiedades que se pueden alternar incluyen:
   * - 'BQuoteFlowStageActive'
   * - 'BQuoteFlowVisibleClients'
   * - 'BQuoteFlowVisibleAdvisors'
   * - 'BQuoteFlowRequiredLogin'
   * - 'BQuoteFlowManualData'
   * - 'BQuoteFlowDataIntegration'
   *
   * @param {string} property - La propiedad de QuoteFlow que se va a alternar.
   * @returns {void}
   */
  onClickFlow(property: string): void {
    let value = false;

    switch (property) {
      case "BQuoteFlowStageActive":
        value = this.form.get('bQuoteFlowStageActive')?.value;
        break;
      case "BQuoteFlowVisibleClients":
        value = this.form.get('bQuoteFlowVisibleClients')?.value;
        break;
      case "BQuoteFlowVisibleAdvisors":
        value = this.form.get('bQuoteFlowVisibleAdvisors')?.value;
        break;
      case "BQuoteFlowRequiredLogin":
        value = this.form.get('bQuoteFlowRequiredLogin')?.value;
        break;
      case "BQuoteFlowManualData":
        this.form.get('bQuoteFlowManualData')?.setValue(true);
        this.form.get('bQuoteFlowDataIntegration')?.setValue(false);
        break;
      case "BQuoteFlowDataIntegration":
        this.form.get('bQuoteFlowManualData')?.setValue(false);
        this.form.get('bQuoteFlowDataIntegration')?.setValue(true);

        break;
      case "BIncludeProcessNumber":
        value = this.form.get('includeProcessNumber')?.value;
        break;

      case "BIsUrlCheck":
        value = this.form.get('bIsUrlCheck')?.value;
        break;

      default:
        break;
    }
    if(property === 'BQuoteFlowManualData' || property === 'BQuoteFlowDataIntegration'){
      this.updateQuoteFlowProperty({
        productId: this.idProduct,
        property: property === 'BQuoteFlowManualData' ? 'BQuoteFlowDataIntegration' : 'BQuoteFlowManualData',
        value: false
      });

      if(property === 'BQuoteFlowDataIntegration' && this.form.get('includeProcessNumber')?.value){
        this.updateQuoteFlowProperty({
          productId: this.idProduct,
          property:'BIncludeProcessNumber',
          value: false,
        });
      }
    }

    this.updateQuoteFlowProperty({
      productId: this.idProduct,
      property,
      value: !value,
    });
  }

  onSkipOffer(event: Event, property: string): void {
    const isChecked = (event.target as HTMLInputElement).checked;

    this.updateQuoteFlowProperty({
      productId: this.idProduct,
      property,
      value: isChecked,
    });
  }

  onUpdateEmailTemplate(property: string, stage: 'Quote'|'Emission'|'Payment', event: any): void {
    const element = (event.target as HTMLInputElement)
    this._productService.updateEmailTemplates(this.idProduct, {
      [property]: property[0] === 'b' ? element.checked : Number(element.value),
      vStage: stage
    }).subscribe(
        (response) => {},
        (error) => {
          this._errorHandlingService.handleError(error);
        }
      );
  }

  public onClickPayment(property: string): void {
    this._productService.updatePaymentFlowProperty(this.idProduct, {
      [property]: property[0] === 'b' ? !this.formPagos.get(property)?.value : this.formPagos.get(property)?.value
    }).subscribe(
        (response) => {},
        (error) => {
          this._errorHandlingService.handleError(error);
        }
      );

  }

  /**
   * Sends an update request to the backend for modifying a specific QuoteFlow property.
   * The method accepts a body that contains the product ID, property name, and the new value.
   * It then calls the `updateQuoteFlowProperty` service method to persist the changes.
   *
   * Upon a successful update, the method will trigger a refresh of the emission stage properties
   * by calling `getEmissionStageProperties`. If an error occurs during the update, the error
   * is handled using the error handling service.
   *
   * @param {BodyUpdateQuoteFlowProperty} body - The payload containing the productId, property name, and the updated value.
   * @returns {void}
   */
  updateQuoteFlowProperty(body: BodyUpdateQuoteFlowProperty): void {
    this._productService.updateQuoteFlowProperty(body).subscribe(
      (response) => {
        this.getEmissionStageProperties();
      },
      (error) => {
        this._errorHandlingService.handleError(error);
      }
    );
  }

  /**
   * Retrieves the business component data based on the current business country and stores it in the `businessComponent` property.
   * This method subscribes to the service that fetches the business component associated with the current business country.
   *
   * If the request fails, it displays a warning message with the error. If successful, it stores the result in `businessComponent`.
   *
   * @returns {void}
   */
  getBusinessComponentByIdBusinessCountry(): void {
    this._businessService
      .getBusinessComponentByIdBusinessCountry()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return throwError(error);
        })
      )
      .subscribe((response: ResponseGlobalModel) => {
        if (!response.error) {
          this.businessComponent = response.result;
        }
      });
  }

  /**
   * Subscribes to the current country and company settings, and retrieves the business and country information.
   * This method assigns the `idBusinessCountry` and `idCountry` from the response and triggers the `getProductByBusiness` method
   * to fetch products related to the business.
   *
   * If the response is not empty, it extracts the business and country IDs and fetches the products for the business.
   *
   * @returns {void}
   */
  getBusinessByCountry(): void {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.idCountry = response.enterprise.pkIIdCountry;

            this.getProductByBusiness(this.idBusinessCountry);
          }
        }
      );
  }

  private _getEmailTemplates(){
    this._parametersService.getTemplateByIdProduct(this.idProduct).subscribe({
      next: (resp => {
        this.emailsTemplates = resp.result
      })
    })
  }

  /**
   * Retrieves all products associated with the specified business by country and stores them in the `products` property.
   * This method subscribes to the product service to fetch the list of products and handles errors if the request fails.
   *
   * If the request is successful, the products are stored in the `products` property. If an error occurs,
   * a warning message is displayed with the error details.
   *
   * @param {number} idBusinessByCountry - The ID of the business by country to fetch the products for.
   * @returns {void}
   */
  getProductByBusiness(idBusinessByCountry: number): void {
    this.productList = this._productService
      .getAllProductByBusiness(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return throwError(error);
        })
      )
      .subscribe((resp: ResponseGlobalModel) => {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.products = resp.result;
        }
      });
  }

  /**
   * Retrieves the form associated with the specified product ID and populates the form data in the component.
   * This method subscribes to the service that automatically registers or retrieves the form, handling errors if the request fails.
   *
   * If the request is successful, the form data is patched into the component's form group, and additional methods
   * are called to fetch the tabs, sections, and fields associated with the form.
   *
   * @param {number} IdProduct - The ID of the product to retrieve the associated form.
   * @returns {void}
   */
  getFormById(IdProduct: number): void {
    this.fieldSubs = this._fieldSvc
      .registerOrGetFormCompleteAutomatically(IdProduct, 'ISF')
      .pipe(
        catchError((respError) => {
          if (respError.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              respError.error.message
            );
          }
          return throwError(respError);
        })
      )
      .subscribe((resp: ResponseGlobalModel) => {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.idForm = resp.result.pkIIdForm;
          this.form.patchValue({
            pkIIdForm: resp.result.pkIIdForm,
            b_Active: resp.result.bActive,
            v_Name: resp.result.vName,
            v_NameDB: resp.result.vNameDb,
          });
        }
      });
  }

  getProductById(IdProduct: number): void {
    this.product = this._productService
      .GetProductById(IdProduct)
      .pipe(
        catchError((respError) => {
          if (respError.error.error) {
          }
          return throwError(respError);
        })
      )
      .subscribe((resp: ResponseGlobalModel) => {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.formSkip.patchValue({
            bSkipOffers: resp.result.bSkipOffers
          });
          this.formQuoteCommunication.patchValue({
            bActive: resp.result.bActiveCommunicationQuote,
            FkIdTemplate: resp.result.fkIdEmailTemplate
          })
        }
      });
  }


  /**
   * Retrieves the available field types and stores them in the `fieldTypes` property.
   * This method subscribes to the service that fetches the list of field types and handles any errors that occur.
   *
   * If the request is successful, the field types are stored in the `fieldTypes` property. In case of an error,
   * a warning message is displayed, and the error is thrown to be handled elsewhere.
   *
   * @returns {void}
   */
  getFieldType(): void {
    this.fieldSubs = this._fieldSvc
      .getFieldType()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return throwError(error);
        })
      )
      .subscribe((resp: ResponseGlobalModel) => {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.fieldTypes = resp.result;
        }
      });
  }

  /**
   * This method subscribes to the service that fetches the parameters for the specified type ('Type_Date') and handles any errors that occur.
   *
   * In case of an error, a warning message is displayed, and the error is thrown to be handled elsewhere.
   *
   * @returns {void}
   */
  getParameters(): void {
    this._parametersService
      .getParameters('Type_Date')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return throwError(error);
        })
      )
      .subscribe((resp: ResponseGlobalModel) => {
        if (Array.isArray(resp)) {
          this.typeDate = resp;
        } else {
        }
      });

    this._parametersService.getAllGatewayConfig().subscribe({
      next: ((resp: ResponseGlobalModel) => {
        if (!resp.error) {
          this.gatewayConfigs = resp.result
        }
      })
    })
  }

  /**
   * Custom validator to check if either the customer portal or advisor portal is selected.
   * This validator checks if at least one of the controls (`inCustomerPortal` or `inAdvisorPortal`) has a true value.
   *
   * If neither control is checked, it returns an error object with the key `portalNotChecked`. Otherwise, it returns `null`, indicating no validation errors.
   *
   * @returns {ValidatorFn} - A function that performs validation on the form group and returns validation errors or null.
   */
  portalCheckerValidator(): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      const controlCustomerPortal = formGroup.get('inCustomerPortal')?.value;
      const controlAdvisorPortal = formGroup.get('inAdvisorPortal')?.value;

      return controlCustomerPortal === true || controlAdvisorPortal === true
        ? null
        : { portalNotChecked: true };
    };
  }

}
