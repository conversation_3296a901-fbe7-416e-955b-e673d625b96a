import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import {
  GroupModel,
  LevelModel,
  UserGroupLevelModel,
} from 'src/app/shared/models/groups';
import { EditNewGroupData } from 'src/app/shared/models/groups/edit-new-group-data.model';
import { GroupsService } from 'src/app/shared/services/groups/groups.service';
import { LevelsService } from 'src/app/shared/services/levels/levels.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { GroupDataComponent } from './group-data/group-data.component';
import { LevelsComponent } from './levels/levels.component';
import { UsersComponent } from './users/users.component';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-edit-new-group',
  standalone: true,
  imports: [
    CommonModule,
    WizardComponent,
    MatIconModule,
    MatButtonModule,
    GroupDataComponent,
    LevelsComponent,
    UsersComponent,
    TranslateModule,
  ],
  templateUrl: './edit-new-group.component.html',
  styleUrls: ['./edit-new-group.component.scss'],
})
// this._translateService.instant("Insurers.AlertCreateNewInsurers"),
export class EditNewGroupComponent {
  steps: string[] = [
    this._translateService.instant('Group.GroupData'),
    this._translateService.instant('Group.Levels'),
    this._translateService.instant('Group.Users'),
  ];
  saveButtonText: string = this._translateService.instant('Group.CreateGroup')
  currentStep: number = 0;
  buttonNextDisabled = true;
  buttonBackDisabled = false;
  groupData!: GroupModel;
  levelData!: LevelModel[];
  userGroupLevelData: UserGroupLevelModel[] = [];
  action: string = 'create';
  idGroup: number = 0;
  gruopState: boolean = false;
  enterprise: string = '';
  country: string = '';
  businessGroup?: string = '';
  private _editNewGroupDataSubscription?: Subscription;
  private _settingCountryAndCompanySubscription?: Subscription;

  constructor(
    private _groupsService: GroupsService,
    private _messageService: MessageService,
    private _levelsService: LevelsService,
    private _activatedRoute: ActivatedRoute,
    public _translateService: TranslateService,
    private _settingService: SettingService,
    private _customRouter: CustomRouterService

  ) {}

  ngOnInit(): void {
    this.validateAction();
    this.getDataEditNewGroupDataSubscription();
    this.getsettingCountryAndCompanySubscription();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion del boton guardar, segun sea crear o editar
      if (this.action == 'edit') {
        this.saveButtonText = this._translateService.instant('SaveChanges')
      } else {
        this.saveButtonText = this._translateService.instant('Group.CreateGroup')
      }

      //traduccion data steps group
      this.steps[0] = this._translateService.instant('Group.GroupData');
      this.steps[1] = this._translateService.instant('Group.Levels');
      this.steps[2] = this._translateService.instant('Group.Users');
    });
  }

  validateAction() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.id) {
        this.action = 'edit';
        this.idGroup = params.id;
        this.saveButtonText = this._translateService.instant('SaveChanges')
      } else {
        this.action = 'create';
        this.saveButtonText = this._translateService.instant('Group.CreateGroup')
      }
    });
  }

  getDataEditNewGroupDataSubscription() {
    this.levelData = [];
    this._editNewGroupDataSubscription =
      this._groupsService.currentGroupData.subscribe({
        next: (response) => {
          if (!(Object.keys(response).length === 0)) {
            switch (this.currentStep) {
              case 0:
                this.buttonNextDisabled = !response.formgroupValid;
                break;
              case 1:
                this.buttonNextDisabled = !response.forLevelValid;
                break;
              default:
                break;
            }
            this.gruopState = response.group.bActive;
            this.groupData = response.group;
            this.levelData = response.level;
          }
        },
      });
  }

  currentStepChange(event: number) {
    this.currentStep = event;
  }

  next(event: string) {
    this.buttonNextDisabled = true;
    switch (event) {
      case this.steps[0]:
        if (this.action == 'create' && this.idGroup == 0) {
          this.createGroup();
        } else if (this.action === 'create' && this.idGroup !== 0) {
          this.editGroup();
        } else if (this.action === 'edit') {
          this.editGroup();
        }

        break;
      case this.steps[1]:
        if (this.action == 'create' && this.idGroup == 0) {
          this.createLevels();
        } else if (this.action === 'create' && this.idGroup !== 0) {
          this.editLevels();
        } else if (this.action === 'edit') {
          this.editLevels();
        }
        break;

      case this.steps[2]:
        break;

      default:
        break;
    }
  }

  back(event: string) {}

  createGroup() {
    this._groupsService.createGroup(this.groupData).subscribe({
      next: (response) => {
        if (!response.error) {
          let payload: EditNewGroupData;
          let level: LevelModel[] = [
            {
              bActive: false,
              fkIIdGroup: 0,
              iOrder: 0,
              pkIIdLevel: 0,
              vLevelName: '',
            },
          ];
          let user: UserGroupLevelModel[] = [
            {
              bActive: false,
              fkIIdLevel: 0,
              fkIIdUser: 0,
              pkIIdUserLevel: 0,
            },
          ];
          payload = {
            group: this.groupData,
            formgroupValid: true,
            level: level,
            forLevelValid: false,
            user: user,
            forUserValid: false,
            typeAction: this.action,
          };
          this.idGroup = response.result;
          this._groupsService.setCurrentEditNewGroupData(payload);
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            ''
          );
        } else {
          this.currentStep = 0;
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  editGroup() {
    this.currentStep = 0;
    this._groupsService.editGroup(this.groupData).subscribe({
      next: (response) => {
        if (!response.error) {
          this.currentStep = 1;
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            ''
          );
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  createLevels() {
    this._levelsService.createLevels(this.levelData).subscribe({
      next: (response) => {
        if (!response.error) {
          let payload: EditNewGroupData;
          let user: UserGroupLevelModel[] = [
            {
              bActive: false,
              fkIIdLevel: 0,
              fkIIdUser: 0,
              pkIIdUserLevel: 0,
            },
          ];
          payload = {
            group: this.groupData,
            formgroupValid: true,
            level: this.levelData,
            forLevelValid: true,
            user: user,
            forUserValid: false,
            typeAction: this.action,
          };
          this._groupsService.setCurrentEditNewGroupData(payload);
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            ''
          );
        } else {
          this.currentStep = 1;
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  editLevels() {
    this._levelsService.editLevels(this.levelData).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            ''
          );
        } else {
          this.currentStep = 1;
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  listUserToAdd(event: UserGroupLevelModel) {
    this.userGroupLevelData.push(event);
  }

  idUserToDelete(event: number) {
    let position = this.userGroupLevelData.findIndex(
      (user: UserGroupLevelModel) => user.fkIIdUser === event
    );
    if (position !== -1) {
      this.userGroupLevelData.splice(position, 1);
    }
  }

  completeGroup() {
    if (this.userGroupLevelData.length > 0) {
      this._groupsService.createUserGroup(this.userGroupLevelData).subscribe({
        next: (response) => {
          if (!response.error) {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              ''
            );
            this._customRouter.navigate(['/dashboard/groups/']);
          } else {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              response.message
            );
          }
        },
      });
    } else if (this.userGroupLevelData.length == 0 && this.action == 'edit') {
      this._groupsService.createUserGroup(this.userGroupLevelData).subscribe({
        next: (response) => {
          if (!response.error) {
            this._messageService.messageSuccess(
              this._translateService.instant('Group.GroupModifiedSuccessfully'),
              ''
            );
            this._customRouter.navigate(['/dashboard/groups/']);
          } else {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              response.message
            );
          }
        },
      });
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('ThereWasAError'),
        this._translateService.instant('Group.YouMustSelectAUserAndLevel')
      );
    }
  }

  cancel() {
    this._customRouter.navigate(['/dashboard/groups/']);
  }

  getsettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe({
        next: (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.enterprise = response.enterprise.vName;
            this.country = response.country.vCountryName;
            this.businessGroup = response.businessGroup?.vBusinessGroupName;
          }
        },
      });
  }

  ngOnDestroy(): void {
    this._editNewGroupDataSubscription?.unsubscribe();
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
