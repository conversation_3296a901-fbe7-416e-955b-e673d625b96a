import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
@Component({
  selector: 'app-all-insurers',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TableComponent,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './all-insurers.component.html',
  styleUrls: ['./all-insurers.component.scss'],
})
export class AllInsurersComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  formValid: boolean = false;

  constructor(
    private _insuranceService: InsuranceService,
    private _router: Router,
    private _settingService: SettingService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _utilsSvc: UtilsService,
    private _customRouter: CustomRouterService

  ) {}
  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
  ngOnInit(): void {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.insuranceCompanyTable = [];
              this.getInsuranceCompanyByIdBusinessByCountry(
                response.enterprise.pkIIdBusinessByCountry
              );
            }
          }
        }
      );

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table business
      this.estructInsuranceCompanyTable[0].columnLabel =
        this._translateService.instant('Insurer.Name');
      this.estructInsuranceCompanyTable[1].columnLabel =
        this._translateService.instant('Insurer.GlobalInsurer');
      this.estructInsuranceCompanyTable[2].columnLabel =
        this._translateService.instant('Status');
      this.estructInsuranceCompanyTable[3].columnLabel =
        this._translateService.instant('Insurer.Edit');
    });
  }

  estructInsuranceCompanyTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Insurer.Name'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Insurer.GlobalInsurer'),
      columnValue: 'nameInsuranceGlobal',
    },
    {
      columnLabel: this._translateService.instant('Insurer.FixedRate'),
      columnValue: 'fixedRate',
    },
    {
      columnLabel: this._translateService.instant('Insurer.ProfileRate'),
      columnValue: 'profileRate',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Insurer.Edit'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
  ];

  insuranceCompanyTable: InsuranceCompanyModel[] = [];

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this._customRouter.navigate([
          `dashboard/insurers/modify/${event.value.pkIIdInsuranceCompanies}`,
        ]);
        break;
      default:
        break;
    }
  }

  getInsuranceCompanyByIdBusinessByCountry(id: number) {
    this._insuranceService
      .getInsuranceCompanyByIdBusinessByCountry(id)
      .subscribe({
        next: (response) => {
          this.insuranceCompanyTable = response.result;
        },
      });
  }

  gotToCreate() {
    if (this.formValid) {
      this._customRouter.navigate(['/dashboard/insurers/new']);
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('YouMustSelectACountryAndABusiness')
      );
    }
  }

  validForm(event: boolean) {
    this.formValid = event;
  }
}
