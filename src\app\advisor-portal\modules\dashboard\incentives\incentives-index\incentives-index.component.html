    
<h2 class="h3">
  <img src="assets/img/layouts/incentivos.svg" />
      Mis Incentivos
</h2>

<app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

<div class="cont-subtitle-form mb-3">                 
  <h4  class="h4 mt-3"> Incentivos monetarios</h4>	                                 
</div>

<mat-form-field appearance="outline">
  <mat-label>Filtrar</mat-label>
  <mat-select [(value)]="selectedFilter" (selectionChange)="onFilterChange()">
    <mat-option *ngFor="let option of availableFilters" [value]="option.value">
      {{ option.label }}
    </mat-option>
  </mat-select>
</mat-form-field>

<div class="graphs-container" >
  <mat-card>        
    <div style="height: 400px; margin: 0 auto; width: 100%;">
      <canvas baseChart
        [data]="chartData"
        [options]="chartOptions"
        [labels]="chartLabels"
        [legend]="true"
        >
      </canvas>
    </div>
  </mat-card>
</div>

<br>
  <div class="select-container">
    <form [formGroup]="form">
      <mat-form-field appearance="outline" class="custom-select">
          <mat-label>{{ "IncentivesForm.TabMonetarySelectYearLabel" | translate }}</mat-label>
        <mat-select (selectionChange)="getSelectedYear()" formControlName="year">
          <mat-option *ngFor="let year of years" [value]="year">{{ year }}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="custom-select">
          <mat-label>{{ "IncentivesForm.TabMonetarySelectMonthLabel" | translate }}</mat-label>
        <mat-select  formControlName="month" (selectionChange)="getStatisticsByYearMonth()">
          <mat-option *ngFor="let month of disposableMonths" [value]="month.value"> {{ month.name }}</mat-option>
        </mat-select>
      </mat-form-field>
    </form>
  </div>
  
  <section class="summary" *ngIf="showSummary">
    <strong>Mes</strong>

    <mat-card class="summary-card">
      <div class="info-summary">
        <div class="icon-summary">
          <mat-icon>attach_money</mat-icon>
          <div class="icon-text">
            <strong>{{ statisticsData?.stadistics[0]?.number }}</strong>
            <p class="desc">Ventas mes en curso</p>
          </div>
        </div>  
        <div class="barra-vertical"></div>

        <div class="stat-block">
          <label>Mes</label>
          <p class="stat-box label">{{ selectedMonthName }}</p>
        </div>
    
        <div class="stat-block">
          <label>Ventas</label>
          <p class="stat-box ventas">{{ totalSales | currency:'':'symbol':'1.0-0' }}</p>
        </div>
    
        <div class="stat-block">
          <label>Cancelaciones</label>
          <p class="stat-box cancelaciones">{{ totalCancellations | currency:'':'symbol':'1.0-0' }}</p>
        </div>
    
        <div class="stat-block">
          <label>Incentivos del mes</label>
          <p class="stat-box incentivos">{{ totalIncentives | currency:'':'symbol':'1.0-0' }}</p>
        </div>
      </div>
    </mat-card>

    <mat-card class="summary-card">
      <div class="info-summary">
        <div class="icon-summary">
          <mat-icon>access_time</mat-icon>
          <div class="icon-text">
            <strong>{{lastMonthStats.totalTransactions}}</strong>
            <p class="desc">Ventas del mes anterior</p>
          </div>
        </div>  
        <div class="barra-vertical"></div>

        <div class="stat-block">
          <label>Mes</label>
          <p class="stat-box label">{{ lastMonthStats.month }}</p>
        </div>
    
        <div class="stat-block">
          <label>Ventas</label>
          <p class="stat-box ventas">{{ lastMonthStats.sales | currency:'':'symbol':'1.0-0' }}</p>
        </div>
    
        <div class="stat-block">
          <label>Cancelaciones</label>
          <p class="stat-box cancelaciones">{{ lastMonthStats.cancellations | currency:'':'symbol':'1.0-0' }}</p>
        </div>
    
        <div class="stat-block">
          <label>Incentivos del mes</label>
          <p class="stat-box incentivos">{{ lastMonthStats.incentives  | currency:'':'symbol':'1.0-0' }}</p>
        </div>
      </div>
    </mat-card>

    <strong>Año</strong>

    <mat-card class="summary-card">
      <div class="info-summary">
        <div class="icon-summary">
          <mat-icon>calendar_today</mat-icon>
          <div class="icon-text">
            <strong>{{ yearStats.totalSalesYear }}</strong>
            <p class="desc">Ventas del mes anterior</p>
          </div>
        </div>  
        <div class="barra-vertical"></div>

        <div class="stat-block">
          
        </div>
    
        <div class="stat-block">
          <label>Ventas</label>
          <p class="stat-box ventas">{{ yearStats.sales | currency:'':'symbol':'1.0-0' }}</p>
        </div>
    
        <div class="stat-block">
          <label>Cancelaciones</label>
          <p class="stat-box cancelaciones">{{ yearStats.cancellations  | currency:'':'symbol':'1.0-0' }}</p>
        </div>
    
        <div class="stat-block">
          <label>Incentivos del mes</label>
          <p class="stat-box incentivos">{{ yearStats.incentives  | currency:'':'symbol':'1.0-0' }}</p>
        </div>
      </div>
    </mat-card>

    <div class="col-12 mt-2 d-flex start">
      <button mat-raised-button type="submit" color="primary" class="" (click)="openModal()">Ver detalle</button>
    </div>
    
  </section>

<br>
  <ng-container >
    <div class="cont-subtitle-form mb-3">        
        <h4>Incentivos no monetarios</h4>    
    </div>
    <div class="cont-tops" *ngIf="carouselIncentives.length > -1">
      <div class="container">        
        <div class="carousel">
          <mat-card class="incentive-card" *ngFor="let incentive of carouselIncentives">            
            <img mat-card-image [src]="incentive.src" alt="Incentivo" class="scaled-image" />
            <mat-card-content>
              <div class="overlay-title">{{ incentive.vCampainName }}</div>
              <p class="valid-date">Válido hasta: {{ incentive.dEndCampain | date: 'dd-MM-yyyy' }}</p>
              <p class="description">{{ incentive.vDescription }}</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary"  mat-raised-button class="btn-conditions"(click)="openPDF(incentive.pdfUrl)">Ver condiciones
                <mat-icon>launch</mat-icon>
              </button>
            </mat-card-actions>
          </mat-card>
    
          <button mat-icon-button class="nav-button left" (click)="prev()">
            <mat-icon>chevron_left</mat-icon>
          </button>
          <button mat-icon-button class="nav-button right" (click)="next()">
            <mat-icon>chevron_right</mat-icon>
          </button>
        </div>            
      </div>
  
    </div>
  </ng-container>
  

  <ng-template #StatisticsDetailModal>
    <app-modal2 [titleModal]="modalTitle" (closeModal)="closeModal($event)" [showCancelButtonBelow]="false">      
      <ng-container body>
        <div class="cont-subtitle-form mb-3">
          <h4>{{selectedMonthName}} - {{selectedYear}}</h4>
        </div>
        <div class="row mt-2">
          <app-table [displayedColumns]="structDetailTable" [data]="dataStatisticsTable"></app-table> 
      </div>
      </ng-container>
  
      <ng-container customButtonCenter>
        <button mat-raised-button type="button"   color="primary" (click)="closeModalButton()"> {{ 'Cerrar' | translate}} </button>        
      </ng-container>
      
    </app-modal2>
</ng-template>