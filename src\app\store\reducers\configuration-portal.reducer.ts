import { createReducer, on } from '@ngrx/store';
import { colorSetting } from '../actions/configuration-portal.actions';

// company-config.state.ts
export interface CompanyConfigColor {
  colorPrimary: string;
  colorSecondary: string;
}

export const initialConfigColor: CompanyConfigColor = {
  colorPrimary: '#0650A0',
  colorSecondary: ''
};

export const colorReducer = createReducer(
  initialConfigColor,
  on(colorSetting, (state, { colors }) => {
    console.log('✅ Reducer recibió:', colors); // ← esto debe imprimir el objeto
    return { ...state, ...colors };
  })
);