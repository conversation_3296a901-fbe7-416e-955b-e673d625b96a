<div class="row mt-2">
    <div class="row mb-2">
        <h4 class="col-md-12">Tops</h4>
        
    </div>
    <div class="d-flex justify-content-center w-100">        
        <div class="cont w-auto">

          <!-- boton añadir si no hay ningún top creado -->
          <div *ngIf="topsCount == 0" class="row">
            
            <div class="col-4 mr-10">
              <button
                (click)="openEditNewTopDialog()"
                class="w-auto"
                mat-raised-button
                color="primary"
                class="mb-3"
                type="button"                   
              >
                {{ "AddTop" | translate }}
                <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
              </button>
            </div>

          </div>
          
          <div *ngFor="let top of topList" class="row">
            
            <div class="col-8">                
              <div class="cont-cards-view-colunm">
                <div class="cont-cards-plans mt-5 mb-5">                    
                  <div class="card-view-colunm mb-3">

                    <div class="cont-title-cards mb-3">
                      <b>
                        <h4>
                          {{top.vTitle}}
                        </h4>                        
                      </b>

                    </div>

                    <div class="row" *ngFor="let element of top.aJsonElementsArray, index as i">                                                    
                        <p style="text-align: left;"><span class="mr-2 {{element.positionText}}">{{i +1}}</span>{{element.positionName}}</p>                                               
                    </div>

                  </div>
                </div>
              </div>
            </div>
      
            <div class="col-4 mt-5">
              <div class="justify-content-center w-auto">
                <button
                  *ngIf="topsCount < 2"
                  (click)="openEditNewTopDialog()"
                  class="w-auto"
                  mat-raised-button
                  color="primary"
                  class="mb-3"
                  type="button"                   
                >
                  {{ "AddTop" | translate }}
                  <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
                </button>
                <br />
                <button
                  mat-raised-button
                  type="button"
                  (click)="openEditNewTopDialog(top.pkIIdTop)"
                  class="w-auto"
                  type="button"
                >
                  {{ "Modify" | translate }}
                  <mat-icon iconPositionEnd fontIcon="edit"></mat-icon>
                </button>
              </div>
            </div>
           
          </div>
        </div>
    </div>
</div>

<!-- modal editar-crear top -->
<ng-template #editNewTopModal>
  <app-modal2 [titleModal]="'Tops'">
    <ng-container body>
      <app-edit-create-top
        [topIdIn] = topId
        [idSectionIndexBusinessIn]="objetTop.pkIIdSectionIndexBusiness"
        (topModelOut)="gettopModel($event)"
      ></app-edit-create-top>
    </ng-container>

    <ng-container customButtonRight>
      <button *ngIf="topId != 0"
        class="mr-2"
        type="button"
        (click)="removeTop_Click()"
        mat-raised-button
        color="warn"
      >
        {{ "Delete" | translate }}
      </button>

      <button
        type="button"
        (click)="saveChanges_Click()"
        mat-raised-button
        color="primary"
      >
        {{ "SaveChanges" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>