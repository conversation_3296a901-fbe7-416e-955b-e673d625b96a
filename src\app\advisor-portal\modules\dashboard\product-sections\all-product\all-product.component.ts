import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { Subscription, catchError, of } from 'rxjs';
import { Router } from '@angular/router';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-all-product',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TableComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './all-product.component.html',
  styleUrls: ['./all-product.component.scss'],
})
export class AllProductComponent implements OnInit, OnDestroy {
  productList?: Subscription;
  private _settingCountryAndCompanySubscription?: Subscription;
  dataTableProduct: any[] = [];

  estructTable: BodyTableModel[] = [
    { 
      columnLabel: this._translateService.instant('ProductSection.Product'),
      columnValue: 'vProductName' 
    },
    // {
    //   columnLabel: 'Estado',
    //   columnValue: 'bActive',
    // },
    {
      columnLabel: this._translateService.instant('Action'), 
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  constructor(
    public router: Router,
    private _productService: ProductService,
    private _settingService: SettingService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _customRouter: CustomRouterService,

  ) {}
  ngOnInit(): void {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if(this.router.url == response.currentModule)
          {
            if (!(Object.keys(response).length === 0)) {
              this.dataTableProduct = [];
              this.getProductByBusiness(response.enterprise.pkIIdBusinessByCountry);
            }
          }          
        }
      );

    this._translateService.onLangChange.subscribe(
      (event: LangChangeEvent) => { 
        this.estructTable[0].columnLabel = this._translateService.instant('ProductSection.Product')
        this.estructTable[1].columnLabel = this._translateService.instant('Action')
      }
    );
  }

  ngOnDestroy(): void {
    this.productList?.unsubscribe();
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  getProductByBusiness(idBusinessByCountry: number) {
    this.productList = this._productService
      .getAllProductByBusiness(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.dataTableProduct = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message);
          } else {
            this.dataTableProduct = resp.result;
          }
        }
      });
  }

  controller(event: IconEventClickModel) {
    this._customRouter.navigate([
      `dashboard/product-sections/all-characteristics/${event.value.pkIIdProduct}/${event.value.vProductName}`,
    ]);
  }
}
