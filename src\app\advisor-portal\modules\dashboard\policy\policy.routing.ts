import { Routes } from '@angular/router';
import { PolicyComponent } from './policy.component';

export default [
  {
    path: '',
    component: PolicyComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./choose-policy-option/choose-policy-option.component').then(
            (c) => c.ChoosePolicyOptionComponent
          ),
      },
      {
        path: 'individual-policy/:idBusinessByCountry',
        loadComponent: () =>
          import(
            './page/page-all-individual-policy/page-all-individual-policy.component'
          ).then((c) => c.PageAllIndividualPolicyComponent),
      },
      {
        path: 'colective-individual-policy/:idBusinessByCountry',
        loadComponent: () =>
          import(
            './page/page-all-colective-individual-policy/page-all-colective-individual-policy.component'
          ).then((c) => c.PageAllColectiveIndividualPolicyComponent),
      },
      {
        path: 'see-details-individual-policy/:idPolicy/:idPolicyType/:idPolicyRisk/:idParent',
        loadComponent: () =>
          import(
            './page/page-see-details-individual-policy/page-see-details-individual-policy.component'
          ).then((c) => c.PageSeeDetailsIndividualPolicyComponent),
      },
      {
        path: 'see-details-colective-individual-policy/:idPolicy/:idPolicyType',
        loadComponent: () =>
          import(
            './page/page-see-details-colective-individual-policy/page-see-details-colective-individual-policy.component'
          ).then((c) => c.PageSeeDetailsColectiveIndividualPolicyComponent),
      },
      {
        path: 'renewal-colective-individual-policy/:idPolicy/:idPolicyType',
        loadComponent: () =>
          import(
            './page/page-renewal-collective-individual-policy/page-renewal-collective-individual-policy.component'
          ).then((c) => c.PageRenewalCollectiveIndividualPolicyComponent),
      },
      {
        path: 'renewal-individual-policy/:idPolicy/:idPolicyType/:idPolicyRisk',
        loadComponent: () =>
          import(
            './page/page-renewal-individual-policy/page-renewal-individual-policy.component'
          ).then((c) => c.PageRenewalIndividualPolicyComponent),
      },
    ],
  },
] as Routes;
