<div class="container py-2">
  <div class="row">
    <div class="col-12">
      <app-drag-drop-upload
        [fileAccept]="'.xlsx'"
        [isOneFile]="true"
        [message]="'CatalogSetting.UploadDocument' | translate"
        (saveFile)="saveFiles($event)"
      ></app-drag-drop-upload>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-12 d-flex justify-content-center gap-1">
      <button
        [type]="'button'"
        [ngClass]="'width-button'"
        mat-raised-button
        color=""
        class="button-with-icon"
        (click)="goBackMain()"
      >
        <span class="button-label">{{ 'CatalogSetting.ExitCatalogs' | translate }}</span>
      </button>

      <button
        [type]="'button'"
        [ngClass]="'width-button'"
        mat-raised-button
        color=""
        class="button-with-icon"
        (click)="goBack()"
      >
        <span class="button-label">{{ 'CatalogSetting.BackCatalog' | translate }}</span>
        <mat-icon class="icon">{{ "keyboard_backspace" }}</mat-icon>
      </button>

      <app-generic-buttons
        [label]="'Save' | translate"
        [type]="'button'"
        [icon]="'save'"
        [classList]="'width-button'"
        [color]="'primary'"
        (buttonClick)="saveConfigCatalogComposite()"
      ></app-generic-buttons>
    </div>
  </div>
</div>
