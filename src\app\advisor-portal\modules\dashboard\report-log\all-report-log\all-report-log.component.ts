import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  OnInit,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { PageEvent } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import { ProductParent } from 'src/app/shared/models/product/product.model';
import {
  RequestReportLogModel,
  ResponseCarrirerModel,
} from 'src/app/shared/models/report-logs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-all-report-log',
  standalone: true,
  imports: [
    CommonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule,
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    PreventionSqlInjectorDirective,
    TableComponent,
    Modal2Component,
  ],
  templateUrl: './all-report-log.component.html',
  styleUrls: ['./all-report-log.component.scss'],
})
export class AllReportLogComponent implements OnInit {
  //Variables para los formularios
  formReportLog: FormGroup = this._fb.group({});
  idBusinessByCountry: number = 0;
  insurers: InsuranceCompanyModel[] = [];

  //Variables relacionadas con la tabla.
  dataReportLogTable: any[] = [];
  estructReportLogTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'ReportLogCarrier.Table.QuoteNumber'
      ),
      columnValue: 'idQuote',
    },
    {
      columnLabel: this._translateService.instant(
        'ReportLogCarrier.Table.ParentProduct'
      ),
      columnValue: 'productParent',
      functionValue: (item: any) =>
        this.getProductParentName(item.productParent),
    },
    {
      columnLabel: this._translateService.instant(
        'ReportLogCarrier.Table.Product'
      ),
      columnValue: 'productName',
    },
    {
      columnLabel: this._translateService.instant(
        'ReportLogCarrier.Table.Insurance'
      ),
      columnValue: 'insurance',
    },
    {
      columnLabel: this._translateService.instant(
        'ReportLogCarrier.Table.QuoteDate'
      ),
      columnValue: 'creationDate',
      functionValue: (item: any) =>
        this._utilsService.formatDate(item.creationDate),
    },
    {
      columnLabel: this._translateService.instant(
        'ReportLogCarrier.Table.User'
      ),
      columnValue: 'userName',
    },
    {
      columnLabel: this._translateService.instant(
        'ReportLogCarrier.Table.Error'
      ),
      columnValue: 'error',
    },
    {
      columnLabel: this._translateService.instant(
        'ReportLogCarrier.Table.Detail'
      ),
      columnValue: 'detail',
      columnIcon: 'search',
    },
  ];
  productsParent: Array<ProductParent> = [];

  //Variables el paginado de la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 10;
  currentPosition: number = 0;

  //Variables relacionadas con modal.
  @ViewChild('detailResponseCarrierModal')
  detailResponseCarrierModal?: TemplateRef<any>;
  modalTitle: string = this._translateService.instant(
    'ReportLogCarrier.Modal.Title'
  );
  detailResponseCarrier!: ResponseCarrirerModel;
  primaryColor: string = '';
  @ViewChildren('errorElements') errorElements!: QueryList<ElementRef>;
  @ViewChild('requestElement') request!: ElementRef;
  @ViewChild('responseElement') response!: ElementRef;

  constructor(
    private _fb: FormBuilder,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _transactionService: TransactionService,
    private _messageService: MessageService,
    private _parametersService: ParametersService,
    private _insuranceService: InsuranceService,
    public modalDialog: MatDialog,
    public _utilsService: UtilsService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getBusinessByCountry();
    this.getProductParent();
    this.getPrimaryColor();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.modalTitle = this._translateService.instant(
        'ReportLogCarrier.Modal.Title'
      );
      this.estructReportLogTable[0].columnLabel =
        this._translateService.instant('ReportLogCarrier.Table.QuoteNumber');
      this.estructReportLogTable[1].columnLabel =
        this._translateService.instant('ReportLogCarrier.Table.ParentProduct');
      this.estructReportLogTable[2].columnLabel =
        this._translateService.instant('ReportLogCarrier.Table.Product');
      this.estructReportLogTable[3].columnLabel =
        this._translateService.instant('ReportLogCarrier.Table.Insurance');
      this.estructReportLogTable[4].columnLabel =
        this._translateService.instant('ReportLogCarrier.Table.QuoteDate');
      this.estructReportLogTable[5].columnLabel =
        this._translateService.instant('ReportLogCarrier.Table.User');
      this.estructReportLogTable[6].columnLabel =
        this._translateService.instant('ReportLogCarrier.Table.Error');
      this.estructReportLogTable[7].columnLabel =
        this._translateService.instant('ReportLogCarrier.Table.Detail');
    });
  }

  //initialize the form.
  initForm() {
    this.formReportLog = this._fb.group({
      idQuote: null,
      insuranceName: '',
      document: '',
      startDate: [null, Validators.required],
      endDate: [null, Validators.required],
      from: 0,
      pageSize: 5,
      businessCountry: 0,
    });

    this.formReportLog.get('startDate')?.valueChanges.subscribe((startDate) => {
      if (startDate) {
        this.formReportLog.get('endDate')?.setValue(null);
        this.formReportLog.get('endDate')?.updateValueAndValidity();
      }
    });
  }

  //check if the form is valid.
  get valid(): boolean {
    return this.formReportLog.valid;
  }

  //check if search button is valid.
  get searchButtonValid(): boolean {
    if (
      (this.valid && this.formReportLog.get('idQuote')?.value) ||
      this.formReportLog.get('document')?.value
    ) {
      return true;
    } else {
      return false;
    }
  }

  //get BusinessByCountry
  async getBusinessByCountry() {
    let dataSetting = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry =
      dataSetting == undefined ? 0 : dataSetting.idBusinessByCountry;
    this.formReportLog
      .get('businessCountry')
      ?.setValue(this.idBusinessByCountry);
    this.getInsuranceCompanyAssociatedCarrier();
  }

  //filter to validate the 3 month range.
  endDateFilter = (d: Date | null): boolean => {
    const startDate = this.formReportLog.get('startDate')?.value;
    if (!d || !startDate) {
      return true;
    }

    const maxDate = new Date(startDate);
    maxDate.setMonth(maxDate.getMonth() + 3);
    maxDate.setDate(maxDate.getDate() - 1); // Para incluir el mismo día al final del rango

    return d <= maxDate && d >= startDate;
  };

  //Controller for table actions.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'detail':
        this.getMResponseCarrierById(event.value.idResponse);
        break;
      default:
        break;
    }
  }

  //Gets the paging events for the table.
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.currentPosition = this.pageIndex * this.pageSize;

    // Asegúrate de que currentPosition no sea negativo
    if (this.currentPosition < 0) {
      this.currentPosition = 0;
    }

    this.getCarrierReportLog(this.currentPosition, this.pageSize);
  }

  //Obtain the carrier logs by certain specific filters (see RequestReportLogModel model).
  getCarrierReportLog(from: number, pageSize: number) {
    let request: RequestReportLogModel = this.formReportLog.value;
    request.from = from;
    request.pageSize = pageSize;
    request.startDate = this._utilsService.formatDate(
      this.formReportLog.get('startDate')?.value
    );
    request.endDate = this._utilsService.formatDate(
      this.formReportLog.get('endDate')?.value
    );
    this._transactionService
      .getCarrierReportLog(request)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataReportLogTable = [];
        } else {
          this.dataReportLogTable = resp.result;
          this.amountRows = resp.rowCount;
        }
      });
  }

  //Get Response Carrier By Id.
  getMResponseCarrierById(idResponse: number) {
    this._transactionService
      .getMResponseCarrierById(idResponse)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.detailResponseCarrier = resp.result;
          this.detailResponseCarrier.vRequest = JSON.parse(
            this.detailResponseCarrier.vRequest
          );
          this.detailResponseCarrier.vResponse = JSON.parse(
            this.detailResponseCarrier.vResponse
          );
          this.openModalDetail();
        }
      });
  }

  //Get parent product list.
  getProductParent() {
    this._parametersService
      .getProductGlobal()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.productsParent = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.productsParent = resp.result;
          }
        }
      });
  }

  //Get Insurance Company Associated The Carrier By idBusinessByCountry.
  getInsuranceCompanyAssociatedCarrier() {
    this._insuranceService
      .getInsuranceCompanyAssociatedCarrier(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.productsParent = [];
        } else {
          this.insurers = resp.result;
        }
      });
  }

  //get Product Parent Name By idProductParent.
  getProductParentName(idProductParent: number): string {
    const foundProductParentName = this.productsParent.find(
      (ProductParent) => ProductParent.pkIIdProductParent === idProductParent
    );
    return foundProductParentName ? foundProductParentName.vProductName : '';
  }

  //search report list by select filter.
  search() {
    this.getCarrierReportLog(this.currentPosition, this.pageSize);
  }

  //Clear search filters.
  cleanFilters() {
    this.formReportLog.reset();
    this.formReportLog.get('document')?.setValue('');
    this.formReportLog.get('insuranceName')?.setValue('');
    this.formReportLog.get('businessCountry')?.setValue(this.idBusinessByCountry);
  }

  //Open Modal Detail Carrier.
  openModalDetail() {
    this.modalDialog.open(this.detailResponseCarrierModal!);
  }

  //Function that detects every time the modal is closed.
  closeModal(event: boolean) {
    this.modalDialog.closeAll();
  }

  //Get the primary color configured in the system.
  getPrimaryColor() {
    this.primaryColor = document.documentElement.style.getPropertyValue(
      '--primary-color-custome'
    );
  }

  //Allows you to copy the indicated text.
  copyContent(caseCopy: string) {
    switch (caseCopy) {
      case 'error':
        const textError = this.errorElements
          .map((el) => el.nativeElement.innerText)
          .join('\n');
        navigator.clipboard
          .writeText(textError)
          .then(() => {
            this._messageService.messageSuccess(
              this._translateService.instant('Copied'),
              ''
            );
          })
          .catch((err) => {
            this._messageService.messageError('Error al copiar el texto.');
          });
        break;
      case 'request':
        const textRequest = this.request.nativeElement.innerText;
        navigator.clipboard
          .writeText(textRequest)
          .then(() => {
            this._messageService.messageSuccess('Copiado', '');
          })
          .catch((err) => {
            this._messageService.messageError('Error al copiar el texto.');
          });
        break;
      case 'response':
        const textResponse = this.response.nativeElement.innerText;
        navigator.clipboard
          .writeText(textResponse)
          .then(() => {
            this._messageService.messageSuccess('Copiado', '');
          })
          .catch((err) => {
            this._messageService.messageError('Error al copiar el texto.');
          });
        break;
      default:
        break;
    }
  }

  //Download the carrier logs report excel file
  downloadCarrierLogReport() {
    let request: RequestReportLogModel = this.formReportLog.value;
    request.startDate = this._utilsService.formatDate(
      this.formReportLog.get('startDate')?.value
    );
    request.endDate = this._utilsService.formatDate(
      this.formReportLog.get('endDate')?.value
    );
    this._transactionService
      .GenerateCarrierLogReport(request)
      .pipe(
        catchError((error) => {

          if(error.error.type){
            this._messageService.messageWaring(
              this._translateService.instant('Message.InformationNotFound'),
              error.error.message
            );

            return of(null);
          }

          if (!error.ok) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of(null);
        })
      )
      .subscribe((resp) => {
        if (resp) {
          const blob = new Blob([resp], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = 'Reporte de Logs.xlsx';
          link.click();
          window.URL.revokeObjectURL(downloadUrl);
        }
      }
    );
  }
}
