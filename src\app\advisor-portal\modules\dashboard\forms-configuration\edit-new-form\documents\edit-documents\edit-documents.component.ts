import {
  Component,
  Input,
  OnInit,
  TemplateRef,
  SimpleChanges,
  OnDestroy,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription, catchError, of } from 'rxjs';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  MatCheckboxChange,
  MatCheckboxModule,
} from '@angular/material/checkbox';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { DocumentModuleModel } from 'src/app/shared/models/configuration-form/document-module';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';

@Component({
  selector: 'app-edit-documents',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    Modal2Component,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    FormsModule,
    TranslateModule,
    ValidationInputFileDirective
  ],
  templateUrl: './edit-documents.component.html',
  styleUrls: ['./edit-documents.component.scss'],
})
export class EditDocumentsComponent implements OnInit {
  @Input() idForm: number = 0;
  @Input() idTable: number = 0;
  @Input() idBusinessCountry: number = 0;
  @Output() getDocumentListByFormIdModule = new EventEmitter<number>();
  @Output() cancelFormPrincipal = new EventEmitter<boolean>();
  @ViewChild('fileInput', { static: false }) fileInputRef?: ElementRef;
  @ViewChild('editTypeFilesModal')
  editTypeFilesModal?: TemplateRef<any>;

  currentModal: MatDialogRef<any> | null = null;

  fileName: string = '';
  showBtnDelete: boolean = false;
  showContOneBtns: boolean = true;
  formDocument: FormGroup = new FormGroup({});
  typeFileList: any[] = [];
  titelModalTypeFile: string = this._translateService.instant(
    'DocumentModule.TitleFileTypes'
  );
  allowedExtensions: string[] = ['xlsx', 'tiff', 'png', 'jpg', 'jpeg', 'docs', 'doc', 'docx', 'pdf'];
  maxFileSizeMB: number = 20;

  constructor(
    private _moduleService: ModuleService,
    private _parameterService: ParametersService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public typeFileDialog: MatDialog,
    public utilsSvc: UtilsService,
    private _fb: FormBuilder,
    private _spinner: SpinnerService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getTypeFileList();

    if (this.idTable > 0) this.getDocumentId(this.idTable);
  }

  initForm() {
    this.formDocument = this._fb.group({
      pkIIdTableModule: [0],
      vName: ['', [Validators.required]],
      vExtension: ['', [Validators.required]],
      iMaximumSize: [[0], [Validators.required, Validators.min(1), Validators.max(20)]],
      bActive: [true],
      bRequired: [false],
      bIsMultipleFiles: [false],
      bDownload: [false],
      bIsDocumentModule: [true],
      fkiIdFormModule: [this.idForm],
      vFileName: [''],
      vFileBase64: [''],
      fkiIdBusinessCountry: [this.idBusinessCountry],
    });
  }

  onChangeDownload(value: any) {
    var idTable = this.formDocument.get('pkIIdTableModule')?.value;
    if (!value.checked) {
      if (idTable > 0) {
        this._msgSvc
          .messageConfirmationAndNegation(
            this._translateService.instant(
              'DocumentModule.DeleteDocumentModuleTitle'
            ),
            this._translateService.instant(
              'DocumentModule.DeleteDocumentModuleSubtitle'
            ),
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.formDocument.get('vFileName')?.setValue('');
              this.formDocument.get('vFileBase64')?.setValue('');
              this.fileName = '';
              this.deleteDocumentModule(idTable);
            } else {
              this.formDocument.get('bDownload')?.setValue(1);
            }
          });
      }
    }
  }

  get valid(): boolean {
    return this.formDocument.valid;
  }

  /// trae la lista de los tipos de archivos
  getTypeFileList() {
    this._parameterService
      .getParameters('Type_Files_DocumenModules')
      .subscribe((resp: any) => {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.typeFileList = resp;
        }
      });
  }

  getDocumentId(idTable: number) {
    this._spinner.show();
    this._moduleService
      .getDocumentId(idTable)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
          this._spinner.hide();
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
            this._spinner.hide();
          } else {
            this.formDocument.patchValue(response.result);
            this.formDocument
              .get('fkiIdFormModule')
              ?.setValue(response.result.fkIIdFormModule);
            this.formDocument
              .get('vFileBase64')
              ?.setValue(response.result.imageBase64);
            this.formDocument
              .get('vFileName')
              ?.setValue(response.result.fileName);
            this.fileName = response.result.fileName;
            this._spinner.hide();
          }
        }
      });
  }

  openModalTypeFile() {
    this.currentModal = this.typeFileDialog.open(this.editTypeFilesModal!, {
      width: '60vh',
      maxHeight: 'auto',
    });
    this.assignValuesTypeFile();
  }

  /// se filtran los tipos de archivo seleccionados
  toggleTypeFile(event: MatCheckboxChange, name: string) {
    if (event !== undefined) {
      let filterTypeFile = this.typeFileList.filter((x) => x.Name == name);

      filterTypeFile.filter((element) => {
        if (element.Name == name) {
          if (event.checked) {
            element.checked = true;
            return true;
          } else {
            element.checked = false;
            return false;
          }
        } else {
          return false;
        }
      });
    }
  }

  /// se guarda en el campo vextension los tipos de archivos seleccionados
  saveTypeFile() {
    let concatenatedFiles = '';
    let arrayCheckSelect = this.typeFileList.filter((x) => x.checked == true);
    arrayCheckSelect.filter((element) => {
      if (element.checked) {
        if (concatenatedFiles == '') concatenatedFiles += element.Name;
        else concatenatedFiles += ',' + element.Name;
        return true;
      } else {
        return false;
      }
    });
    this.formDocument.get('vExtension')?.setValue(concatenatedFiles);
    this.closeModal(true);
  }

  /// assign values to the field array typeFiles from field maximumSize
  assignValuesTypeFile() {
    if (this.formDocument.get('vExtension')?.value !== null) {
      var resulListTypeFile = this.formDocument.get('vExtension')?.value.split(',');
      var arrayListIdState: string[] = [];
      resulListTypeFile.forEach((element: any) => {
        arrayListIdState.push(element);
      });
      const listDataFilter = this.typeFileList.filter(
        x => arrayListIdState.map(y => y).includes(x.Name));

      listDataFilter.forEach( item => {
          if(!item.checked)
                item.checked = true;
      });
    }
  }

  selectFile(event: any) {
    let iMaximumSize = 0;
    iMaximumSize = this.formDocument.get('iMaximumSize')?.value;
    if (iMaximumSize > 0) {
      let extension: string = '';
      extension = this.fileName = event.target.files[0].name.split('.')[1];
      if (this.validationExtension(extension)) {
        const files: FileList = event.target.files;
        this.fileName = event.target.files[0].name.split('.')[0];
        // se calcula el valor MB en Bytes
        let sizeFileBytes = 0;
        sizeFileBytes = iMaximumSize * Math.pow(1024, 2);

        if (files[0].size > sizeFileBytes) {
          // 100MB to Bytes, (100*1024)*1024
          return this._msgSvc.messageError(
            'La imagen excede el peso máximo(100MB)'
          );
        }
        /////////////////////////////////////////////////////////////

        this.readDataImageToString(files[0], extension)
          .then((r) => {
            let base64: string = r.split(',')[1];
            this.formDocument.get('vFileBase64')?.setValue(base64);
          })
          .catch((e) => {
            this._msgSvc.messageError(e);
            this.showContOneBtns = false;
          })
          .finally(() => {
            this.showContOneBtns = false;
          });

        this.formDocument
          .get('vFileName')
          ?.setValue(`${this.fileName}.${extension}`);
        this.showBtnDelete = true;
      } else {
        this._msgSvc.messageInfo(
          'Advertencia',
          'El archivo selecionado no tiene el formato adecuado.'
        );
        this.fileInputRef!.nativeElement.value = '';
        this.showBtnDelete = false;
        this.fileName = '';
      }
    } else {
      this._msgSvc.messageInfo(
        'Advertencia',
        'Debes ingresar el valor del tamaño máximo'
      );
    }
  }

  readDataImageToString(file: File, ext: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async () => {
        const result = reader.result?.toString();
        if (ext === 'png' || ext === 'jpg' || ext === 'jpeg') {
          const image = new Image();
          image.src = result!;
          await new Promise((res) => (image.onload = () => res(null)));
          if (image.width < 20 || image.height < 20)
            reject(
              'Las dimensiones de la imagen deben ser minimo 20 x 20 pixeles'
            );
        }

        resolve(result!);
      };
      reader.readAsDataURL(file);
    });
  }

  validationExtension(ext: string): true | false {
    if (ext === 'xlsx' || ext === 'tiff') return true;
    if (ext === 'png' || ext === 'jpg' || ext === 'jpeg') return true;
    if (ext === 'docs' || ext === 'doc' || ext === 'docx') return true;
    if (ext === 'pdf') return true;
    return false;
  }
  
  validationUploadDocument(isDownload: boolean): true | false {
    if (isDownload) {
      if (
        this.formDocument.get('vFileName')?.value == '' ||
        this.formDocument.get('vFileName')?.value == null
      )
        return false;
      else return true;
    } else return true;
  }

  deleFile() {
    this.showContOneBtns = true;
    this.showBtnDelete = false;
    this.fileName = '';
    this.fileInputRef!.nativeElement.value = '';
  }

  createDocumentModule() {
    if (this.valid) {
      if (
        !this.validationUploadDocument(
          this.formDocument.get('bDownload')?.value
        )
      ) {
        this._msgSvc.messageWaring(
          this._translateService.instant('ThereWasAError'),
          'El campo documento de descarga es obligatorio, porque el checkbox descarga esta seleccionado'
        );
        return;
      }

      let payload: DocumentModuleModel = this.formDocument.getRawValue();
      this._moduleService
        .registerDocumentModule(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageCreated'
                )
              );
              this.getDocumentListByFormIdModule.emit(this.idForm);
              this.closeModalPrincipal();
            }
          }
        });
    }
  }

  updateDocumentModule() {
    if (this.valid) {
      if (
        !this.validationUploadDocument(
          this.formDocument.get('bDownload')?.value
        )
      ) {
        this._msgSvc.messageWaring(
          this._translateService.instant('ThereWasAError'),
          'El campo documento de descarga es obligatorio, porque el checkbox descarga esta seleccionado'
        );
        return;
      }

      let payload: DocumentModuleModel = this.formDocument.getRawValue();
      this._moduleService
        .updateDocumentModule(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageUpdated'
                )
              );

              this.getDocumentListByFormIdModule.emit(this.idForm);
              this.closeModalPrincipal();
            }
          }
        });
    }
  }

  deleteDocumentModule(idTable: number) {
    this._moduleService
      .deleteDocumentModule(idTable)
      .pipe(
        catchError((error) => {
          console.log('error al eliminar el campo');
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            console.log(resp.message);
          }
        }
      });
  }

  closeModalPrincipal() {
    this.formDocument.reset();
    this.formDocument.get('bActive')?.setValue(true);
    this.formDocument.get('pkIIdTableModule')?.setValue(0);
    this.formDocument.get('fkIIdFormModule')?.setValue(this.idForm);
    this.fileName = '';
    this.cancelFormPrincipal.emit(true);
  }

  closeModal(nameComp: boolean) {
    if (this.currentModal) {
      this.currentModal.close();
      this.currentModal = null; // Limpia la referencia después de cerrar el modal
    }
  }

  cancel() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant('Out?'),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this.closeModalPrincipal();
          this.typeFileDialog.closeAll();
        }
      });   
  }
}
