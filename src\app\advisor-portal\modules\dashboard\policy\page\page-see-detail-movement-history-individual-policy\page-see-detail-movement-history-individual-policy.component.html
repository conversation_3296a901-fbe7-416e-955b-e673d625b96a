<div *ngIf="isRenew" class="mt-3">
    <app-details-policy [detailsPolicyData]="detailsPreviousPolicyData" [idWtw]="idWtw" [policyNumber]="previousPolicyNumber"
        [policyStatus]="previousPolicyStatus" [idPolicyType]="idPolicyType"></app-details-policy>
</div>

<!-- Dettale de la póliza -->
<div class="mt-3">
    <app-details-policy [detailsPolicyData]="detailsPolicyData" [idWtw]="idWtw" [policyNumber]="policyNumber"
        [policyStatus]="policyStatus" [idPolicyType]="idPolicyType"></app-details-policy>
</div>

<!-- Tabla Beneficiarios -->
<div class="mt-5">
    <app-beneficiaries-table [estructTable]="estructTableBeneficiaries"
        [beneficiariesData]="beneficiariesData"></app-beneficiaries-table>
</div>

<!-- Carátula de póliza PDF -->
<div class="cont-pdf-viewer">
    <div class="mt-5">
        <h4 class="bold">{{ "Policy.PolicyCover" | translate }}</h4>
    </div>
    <mat-card class="caratula mt-5">
        <div class="row">
            <div class="col-md-10">
                <mat-card-content>
                    <pdf-viewer [src]="pdfSrc" [render-text]="true" [original-size]="true"
                        style="width: 100%; height: 800px"></pdf-viewer>
                </mat-card-content>
            </div>
            <div class="col-md-2">
                <mat-card-actions>
                    <!-- Descargar PDF -->
                    <button mat-raised-button type="button" color="primary" (click)="downloadPDF()">
                        <mat-icon iconPositionEnd fontIcon="arrow_forward"></mat-icon>
                        {{ "Policy.DownloadPDF" | translate }}
                    </button>
                </mat-card-actions>
            </div>
        </div>
    </mat-card>
</div>