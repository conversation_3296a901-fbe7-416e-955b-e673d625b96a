<div class="cont">
  <h5><strong> {{ "Plan.PlanDetails" | translate }}</strong></h5>

  <div class="container-letter mt-2">
    <div class="policy-button-wrappe">
      <h6 *ngIf="productPolicyTypeAndTerms.vPolicyTypeCode == 'IND' " class="policy-button">{{ "Plan.IndividualPolicyProduct" | translate }}</h6>
      <h6 *ngIf="productPolicyTypeAndTerms.vPolicyTypeCode == 'COL' " class="policy-button">{{ "Plan.GroupPolicyProduct" | translate }}</h6>

      <!-- Cuando no se ha configurado el tipo de póliza en la configuración de producto -->
      <h6 *ngIf="!productPolicyTypeAndTerms.vPolicyTypeCode " style="color: rgba(245, 160, 160, 0.651);" class="policy-button">{{ "Plan.ProductWithoutConfiguringThePolicyTypeInProductConfiguration" | translate }}</h6>
    </div>
  </div>
  <form class="mt-4" [formGroup]="formPlans">
    <div class="row">
      <div class="col-12 col-md-12 mb-2">
        <mat-slide-toggle formControlName="bActive" class="mb-2 mx-2">
          {{ "Plan.ActivePlan" | translate }}
        </mat-slide-toggle>
        <mat-slide-toggle formControlName="bActiveConoce" class="mb-2 mx-2">
          {{ "Plan.ActiveConoce" | translate }}
        </mat-slide-toggle>
        <mat-slide-toggle formControlName="bWithoutFinishDate" class="mb-2 mx-2" (change)="updateWithoutFinishedDate()">
          {{ "Plan.WithoutFinishedDate" | translate }}
        </mat-slide-toggle>
      </div>
      <div class="col-12 col-md-12 mb-2">
        <mat-checkbox formControlName="bAsesor">{{ "Plan.VisibleInAdvisoryPortal" | translate }}</mat-checkbox>
        <mat-checkbox formControlName="bCliente">{{ "Plan.VisibleInClientsPortal" | translate }}</mat-checkbox>
      </div>

      <div class="col-12 col-md-12">
        <mat-form-field appearance="outline" class="select-look w-100">
        <mat-label>
            {{ "Product.SelectCoin" | translate }}
        </mat-label>
        <mat-select formControlName="vFormatCoin">
            <mat-option *ngFor="let item of typeFormatCoin" [value]="item.format">
                {{ item.format }}
            </mat-option>
        </mat-select>
    </mat-form-field>

      </div>

      <div class="col-12 col-md-12 mb-3">

        <mat-form-field class="w-100" appearance="fill">
          <mat-label>
            {{ "Category" | translate }}
          </mat-label>
          <mat-select formControlName="fkIIdCategory">
            <mat-option
              *ngFor="let categ of category"
              [value]="categ.pkIIdCategory"
            >
              {{ categ.vName }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              utilsSvc.isControlHasError(
                formPlans,
                'fkIIdCategory',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <mat-form-field class="w-100" appearance="fill">
          <mat-label>
            {{ "Plan.Insurer" | translate }}
          </mat-label>
          <mat-select formControlName="fkIIdInsuranceCompany">
            <mat-option
              *ngFor="let insurer of insurers"
              [value]="insurer.pkIIdInsuranceCompanies"
            >
              {{ insurer.vName }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              utilsSvc.isControlHasError(
                formPlans,
                'fkIIdInsuranceCompany',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="col-12 col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>
            {{ "Plan.PlanName" | translate }}
          </mat-label>
          <input matInput formControlName="vName" PreventionSqlInjector/>
          <mat-error
            *ngIf="utilsSvc.isControlHasError(formPlans, 'vName', 'required')"
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="col-12 col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>
            {{ "Plan.QuotationValidityDays" | translate }}
          </mat-label>
          <input matInput type="number" formControlName="iValidity" />
          <mat-error
            *ngIf="
              utilsSvc.isControlHasError(formPlans, 'iValidity', 'required')
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="col-6 col-md-6 mb-3">
        <mat-form-field class="w-100">
          <mat-label>
            {{ "Plan.ValidityStartDate" | translate }}
          </mat-label>
          <input
            matInput
            [matDatepicker]="dStartValidity"
            formControlName="dStartValidity"
          />
          <!-- <mat-hint>MM/DD/YYYY</mat-hint> -->
          <mat-datepicker-toggle
            matIconSuffix
            [for]="dStartValidity"
          ></mat-datepicker-toggle>
          <mat-datepicker #dStartValidity></mat-datepicker>
        </mat-form-field>
      </div>

      <div class="col-6 col-md-6 mb-3">
        <mat-form-field class="w-100">
          <mat-label>
            {{ "Plan.ValidityEndDate" | translate }}
          </mat-label>
          <input
            matInput
            [matDatepicker]="dEndValidity"
            formControlName="dEndValidity"
          />
          <!-- <mat-hint>MM/DD/YYYY</mat-hint> -->
          <mat-datepicker-toggle
            matIconSuffix
            [for]="dEndValidity"
          ></mat-datepicker-toggle>
          <mat-datepicker #dEndValidity></mat-datepicker>
        </mat-form-field>
      </div>



      <div *ngIf="formPlans.get('bActiveConoce')" class="contenedor-opciones">
        <h3 class="mb-2">{{ "LearnMore" | translate }}</h3>
        <small>
          {{ "Plan.LearnMore.instructions" | translate }}
        </small>
        <br>
        <mat-radio-group formControlName="ilearnMore">
          <mat-radio-button [value]="1">{{ "Plan.LearnMore.pdf" | translate }}</mat-radio-button>
          <mat-radio-button [value]="2">{{ "Plan.LearnMore.details" | translate }}</mat-radio-button>
          <mat-radio-button [value]="3">{{ "Plan.LearnMore.link" | translate }}</mat-radio-button>
        </mat-radio-group>
        <p>
          <ng-container *ngIf="formPlans.get('ilearnMore')?.value === 3">
            <mat-label>Link</mat-label>
            <mat-form-field class="w-100">
              <mat-label>Link</mat-label>
              <input matInput type="text" formControlName="vLearnMoreLink" />
            </mat-form-field>
          </ng-container>

          <ng-container *ngIf="formPlans.get('ilearnMore')?.value === 1">
            <div class="upload-container" matTooltip="Solo se permiten archivos PDF">
              <div class="upload-area" (drop)="onFileDropped($event)" (dragover)="onDragOver($event)">
                <input type="file" (onChangeValidated)="onFileSelected($event)" id="fileUpload"
                  accept="application/pdf" hidden  ValidationInputFile [allowedExtensions]="['pdf']" [maxFileSizeMB]="20"/>
                <label for="fileUpload"  class="upload-label" [ngClass]="{ 'uploaded': showBtnDelete }">
                  <mat-icon  class="mb-5"
                    [style.color]="showBtnDelete ? 'green' : 'inherit'"
                    style="overflow: visible; margin-right: 23px"
                    >{{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}</mat-icon
                  >
                  <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>

                  <p *ngIf="!showBtnDelete && !fileName">
                    Haga clic para cargar o arrastre y suelte aquí los archivos
                  </p>
                  <span>PDF</span>
                </label>
              </div>
              <p class="upload-info">
                Tamaño máximo: 20 MB. Sólo permite un archivo.
              </p>
            </div>
          </ng-container>
        </p>

      </div>

      <div class="mb-8">
          <h3 class="mb-2">{{ "Plan.Premiums" | translate }}</h3>
          <app-input-premium-dynamic *ngIf="planEdit" #child (onChangeCheckPremiums)="onChangeCheckPremiums($event)"
           (openModalPremium)="openModalPremium($event)"></app-input-premium-dynamic>
      </div>

      <div class="mb-8" *ngFor="let item of plansDataTable | keyvalue">
        <h3 class="mb-2">{{ item.key }}</h3>
        <app-table
          [displayedColumns]="estructPlansTable"
          [data]="item.value"
          (iconClick)="controller($event)"
        ></app-table>
      </div>

      <div class="d-flex justify-content-center">
        <div class="mt-3">
          <button *ngIf="!planCreate" class="mx-5" (click)="goBack()" type="button" mat-raised-button>
            {{ "Cancel" | translate }}
          </button>
          <button
            class="mx-5"
            *ngIf="planCreate"
            mat-button
            type="button"
            (click)="goBack()"
          >
            <mat-icon fontIcon="arrow_back"></mat-icon>
            {{ "Back" | translate }}
          </button>
          <button
            [disabled]="!formValid || planCreate"
            (click)="completePlan()"
            type="button"
            mat-raised-button
            color="primary"
          >
            <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
            {{ "SaveChanges" | translate }}
          </button>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- Modal para crear o editar un sectionFielValue -->
<ng-template #createEditSectionItemModal>
  <app-modal2 [titleModal]="titelModal" (closeModal)="eventCloseModal($event)">
    <ng-container body>
      <form [formGroup]="formSectionItem" class="mt-3">
        <div class="row">
          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "Plan.ItemComparator" | translate }}
              </mat-label>
              <input matInput formControlName="itemInComparator" PreventionSqlInjector/>
              <!-- <mat-error
                *ngIf="
                  utilsService.isControlHasError(
                    formSectionItem,
                    'itemInComparator',
                    'required'
                  )
                "
              >
                Este campo es requerido
              </mat-error> -->
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "Plan.Category" | translate }}
              </mat-label>
              <input matInput formControlName="category" PreventionSqlInjector/>
              <!-- <mat-error
                *ngIf="
                  utilsService.isControlHasError(
                    formSectionItem,
                    'category',
                    'required'
                  )
                "
              >
                Este campo es requerido
              </mat-error> -->
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "Plan.ItemType" | translate }}
              </mat-label>
              <input matInput formControlName="vFieldTypeName" PreventionSqlInjector/>
              <!-- <mat-error
                *ngIf="
                  utilsService.isControlHasError(
                    formSectionItem,
                    'vFieldTypeName',
                    'required'
                  )
                "
              >
                Este campo es requerido
              </mat-error> -->
            </mat-form-field>
          </div>

          <div
            class="col-12 col-md-12"
            *ngIf="itemType === 3 || itemType === 2"
          >
            <mat-slide-toggle formControlName="bisCheck" class="mb-3">
              {{ "Plan.Apply" | translate }}
            </mat-slide-toggle>
          </div>

          <div class="col-12 col-md-12" *ngIf="itemType !== 2">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>{{labelItemTypeDinamic}}</mat-label>
              <textarea matInput formControlName="vName" (keyup)="onKeyup($event)" maxlength="2500" ></textarea>
              <!-- <mat-error
                *ngIf="
                  utilsService.isControlHasError(
                    formSectionItem,
                    'vName',
                    'required'
                  )
                "
              >
                Este campo es requerido
              </mat-error> -->
            </mat-form-field>

          </div>

          <div class="col-12 col-md-12" *ngIf="itemType === 4">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "Plan.Deductible" | translate }}
              </mat-label>
              <input matInput formControlName="vDeductible" PreventionSqlInjector/>
              <!-- <mat-error
                *ngIf="
                  utilsService.isControlHasError(
                    formSectionItem,
                    'vDeductible',
                    'required'
                  )
                "
              >
                Este campo es requerido
              </mat-error> -->
            </mat-form-field>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button
        type="button"
        (click)="completeSectionFieldValue()"
        mat-raised-button
        color="primary"
      >
        {{ "Save" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

<ng-template #createPremiumModal>
  <app-modal [titleModal]="titelModal">
    <app-plan-premium
      [planId]="pkIIdPlan"
      [idProducto]="fkIIdProduct"
      [typePremiums]="typePremiums"
      [isCalculated]="isCalculated"
      [isShowDecimals]="isShowDecimals"
      (getPlanById)="getPlanById(pkIIdPlan)"
      [pkIdPlanPremiumValue]="pkIdPlanPremiumValue"
      [arrayDataPremiumValues]="dataPremiumValues"
    >
    </app-plan-premium>
  </app-modal>
</ng-template>
