// .mat-form-field{
//     ::ng-deep mat-form-field-disabled{
//         background-color: red;
//     }
// }
.margin-section {
    margin: 12px 0;
}

.margin-radioButton {
    margin: 0 10px;
}


.form-premium {
    min-width: 150px;
    max-width: 500px;
    width: 100%;
}

.upload-container {
    // text-align: center;
    margin: 20px 0;
  }
  
  .upload-area {
    border: 2px dashed #cccccc;
    padding: 30px;
    border-radius: 8px;
    cursor: pointer;
    display: inline-block;
    width: 100%;
    max-width: 700px;
    position: relative;
  }
  
  .upload-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #007bff;
    font-size: 16px;
    text-align: center;
  }
  
  .upload-label mat-icon {
    font-size: 40px;
    color: #007bff;
  }
  
  .upload-info {
    margin-top: 10px;
    font-size: 14px;
    color: #6c757d;
  }
  
  .container-letter {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

.policy-button {
  display: inline-block;
  padding: 8px 20px;
  font-size: 14px;
  color: #0056b3;
  background-color: #e7f1ff;
  border: 2px solid #98bceb;
  border-radius: 25px;
  text-align: center;
  font-weight: 600;
}

.policy-button-wrapper {
    padding: 8px;
    border-radius: 10px;
    display: inline-block;
}