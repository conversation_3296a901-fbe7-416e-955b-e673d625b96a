import { Routes } from "@angular/router";
import { BusinessComponent } from "./business.component";

export default [
 {
  path:'',
  component: BusinessComponent,
  children: [
    {path: '', loadComponent:()=> import('./all-business/all-business.component').then(c=>c.AllBusinessComponent)},
    {path: 'new', loadComponent:()=> import('./edit-new-business/edit-new-business.component').then(c=>c.EditNewBusinessComponent)},
    {path: 'modify/:id', loadComponent:()=> import('./edit-new-business/edit-new-business.component').then(c=>c.EditNewBusinessComponent)},
    {path: 'new-business-group', loadComponent:()=> import('./edit-new-business-group/edit-new-business-group.component').then(c=>c.EditNewBusinessGroupComponent)},
    {path: 'modify-business-group/:id', loadComponent:()=> import('./edit-new-business-group/edit-new-business-group.component').then(c=>c.EditNewBusinessGroupComponent)}
  ]
 }
] as Routes;