<div class="col-12 col-md-12">
  <div class="d-flex justify-content-start align-items-center mb-2">
    <h3 style="margin: 0"> {{ "FormConfiguration.TabModule.title" | translate }}</h3>
    <mat-icon class="click ml-1" matTooltipPosition="right" matTooltip="{{ 'Tooltips.FormTab' | translate }}">help_outline</mat-icon>

  </div>
  <app-table
    [displayedColumns]="estructTable"
    [data]="dataTableTabsForm"
    (iconClick)="controller($event)"
  ></app-table>
  <button class="mb-2" type="button" mat-raised-button color="primary"
  (click)="openModalCreateEditTabFormModule()"
  >
    {{ "Add" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
</div>


<!-- modal editar-crear pestaña  -->
<ng-template #editNewTabFormModal>
  <app-modal2 [titleModal]="titelModal" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="form">
        <div class="row mt-5">
          <div class="col-md-12 col-sm-12">
            <mat-slide-toggle class="mb-3" formControlName="bActive">
              {{ "FormConfiguration.TabModule.BActive" | translate }}
            </mat-slide-toggle>
          </div>          

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "FormConfiguration.TabModule.StepProgressBarPertain" | translate }}
              </mat-label>
              <mat-select [disabled]="progressBarList.length == 0" formControlName="fkIIdProgressBar">
                <mat-option *ngFor="let item of progressBarList" [value]="item.pkIIdProgressBar">
                  {{ item.vName }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'fkIIdProgressBar', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "FormConfiguration.TabModule.NameTabModule" | translate }}
              </mat-label>
              <input matInput formControlName="vName" />
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'vName', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ "FormConfiguration.TabModule.Order" | translate }}
              </mat-label>
              <mat-select formControlName="iOrder">
                <mat-option *ngFor="let order of orderList" [value]="order">
                  {{ order }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'iOrder', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonCenter>
      <button
       (click)="deleteTab()"
        type="button"
        mat-raised-button
        *ngIf="this.form.get('pkIIdTabModule')?.value > 0"
        class="delete"
      >
        {{ "FormConfiguration.TabModule.DeleteTabs" | translate }}
        <mat-icon class="delete" iconPositionEnd fontIcon="close"></mat-icon>
      </button>
    </ng-container>

    <ng-container customButtonRight>
      <button
        *ngIf="this.form.get('pkIIdTabModule')?.value === 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="createTabModule()"
        [disabled]="!valid"
      >
        {{ "FormConfiguration.TabModule.AddTabButton" | translate }}
      </button>
      <button
        *ngIf="this.form.get('pkIIdTabModule')?.value > 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="editTabModule()"
        [disabled]="!valid"
      >
        {{ "FormConfiguration.TabModule.SaveTabButton" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
