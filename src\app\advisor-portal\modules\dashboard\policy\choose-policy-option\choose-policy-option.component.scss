@import "/src/assets/styles/variables";

h6 {
  font-weight: 700;
  font-size: 1.5rem;
}

.cont-option {
  width: 100%;
  display: flex;
  align-items: start;
  justify-content: center;
  flex-wrap: wrap;
  .option {
    width: 80px;
    text-decoration: none;
    margin: 1.5rem;
    cursor: pointer !important;
    .box {
      width: 80px;
      height: 80px;
      background: $color_2;
      box-shadow: 0px 2px 8px 0px #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
    }
  }
}

.title {
  color: #272a30;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  letter-spacing: -0.32px;
}

.mat-icon {
  height: 50px !important;
  width: 50px !important;
}

.material-symbols-outlined {
  font-size: 50px !important;
  color: #272a30;
}
