import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-modal-send-pdf',
  standalone: true,
  imports: [CommonModule, MatIconModule,MatDialogModule,TranslateModule],
  templateUrl: './modal-send-pdf.component.html',
  styleUrls: ['./modal-send-pdf.component.scss']
})
export class ModalSendPdfComponent {

  filterForm!: FormGroup;
dataF: any =  null;

  constructor(
    public dialogRef: MatDialogRef<ModalSendPdfComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private _translateService: TranslateService
  ) {

    console.log("DATA =>", data);

  }

  close(): void {
    this.dialogRef.close();
  }

  applyFilters(): void {
    this.dialogRef.close(true);
  }
}
