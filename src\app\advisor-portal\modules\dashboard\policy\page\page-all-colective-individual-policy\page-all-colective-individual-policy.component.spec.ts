import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PageAllColectiveIndividualPolicyComponent } from './page-all-colective-individual-policy.component';

describe('PageAllColectiveIndividualPolicyComponent', () => {
  let component: PageAllColectiveIndividualPolicyComponent;
  let fixture: ComponentFixture<PageAllColectiveIndividualPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ PageAllColectiveIndividualPolicyComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PageAllColectiveIndividualPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
