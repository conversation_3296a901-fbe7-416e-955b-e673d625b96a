import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { EditCatalogManagementComponent } from '../catalog-management/edit-catalog-management/edit-catalog-management.component';
import { EditCatalogSheetComponent } from '../catalog-management/edit-catalog-sheet/edit-catalog-sheet.component';
import { EditCatalogColumnComponent } from '../catalog-management/edit-catalog-column/edit-catalog-column.component';
import { UploadCompositeCatalogComponent } from '../catalog-management/upload-composite-catalog/upload-composite-catalog.component';
import { ActivatedRoute } from '@angular/router';
import { TypeTabEnum } from 'src/app/shared/models/composite-catalog/type-tab-enum.model';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-catalog-management',
  standalone: true,
  imports: [
    CommonModule,
    EditCatalogManagementComponent,
    EditCatalogSheetComponent,
    EditCatalogColumnComponent,
    UploadCompositeCatalogComponent,
    MatTabsModule,
    UploadCompositeCatalogComponent,
    TranslateModule
],
  templateUrl: './catalog-management.component.html',
  styleUrls: ['./catalog-management.component.scss'],
})
export class CatalogManagementComponent implements OnInit {
  idCatalog: number = 0;
  idBusinessCountry: number = 0;
  idCountry: number = 0;

  currentTab: TypeTabEnum = TypeTabEnum.Management; // Tab by default
  TypeTabEnum = TypeTabEnum;

  constructor(private _activatedRoute: ActivatedRoute) {}

  ngOnInit(): void {
    this.validateAction();
  }

  validateAction() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idCatalog > 0) this.idCatalog = params.idCatalog;

      this.idBusinessCountry = params.idBusinessCountry;
      this.idCountry = params.idCountry;
    });
  }

  setActiveTabCatalog(item: any) {
    if (item.value !== null || item.value !== '') {
      this.currentTab = item.value;
      this.idCatalog = item.id == 0 ? this.idCatalog : item.id;
    }
  }
}
