<div class="title-customer">
  <h3 class="h3">
    {{'Group.GroupUsers' | translate}}
    <mat-icon class="click" matTooltipPosition="right" matTooltip="{{'Tooltips.UserTitle' | translate}}">help_outline</mat-icon>
  </h3>
  <span> 
    {{'Group.AllChangesWillBeSavedAutomatically' | translate}}
  </span>  
</div>

<div class="row mt-2">
  <div class="col-md-8 col-sm-12">
    <app-select
      [label]="'Group.SearchUser' | translate"
      [data]="dataSelectUser"
      (search)="search($event)"
      (listSelect)="listSelect($event)"
    ></app-select>
  </div>
  <div [formGroup]="form" class="col-md-4 col-sm-12">
    <mat-form-field class="w-100" appearance="fill">
      <mat-label>
        {{ 'Group.SelectLevel' | translate }}
      </mat-label>
      <mat-select formControlName="levelForm">
        <mat-option *ngFor="let level of levels" [value]="level">
          {{ level.vLevelName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
</div>
<div class="row">
  <div class="col-md-12">
    <button
      class="mb-3"
      type="button"
      mat-raised-button
      color="primary"
      (click)="addUsers()"
    >
    {{ 'Group.AddUser' | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
    <button
      class="mx-2 mb-3"
      type="button"
      mat-raised-button
      color="primary"
      (click)="openMasiveUsersAsignationDialog()"
    >
    {{ 'Group.MasiveAsignation' | translate }}
    <mat-icon iconPositionEnd fontIcon="arrow_upward"></mat-icon>
    </button>
    <mat-icon class="click" matTooltipPosition="right" matTooltip="{{'Tooltips.MasiveAsignationButton' | translate}}">help_outline</mat-icon>
  </div>
</div>

<app-table
  [displayedColumns]="estructUsersTable"
  [data]="userTable"
  (iconClick)="controller($event)"
></app-table>


<!-- modal asignacon masiva -->
<ng-template #masiveAsignationModal>
  <app-modal2 (closeModal)="closedModal($event)" [titleModal]="'Group.MasiveAsignation' | translate">
    <ng-container body>

      <app-table
        (iconClick)="addUserController($event)"
        [displayedColumns]="estructAllUsersTable"
        [data]="allUserTable"      
      ></app-table>
      <div class="row">
        <span>
          {{'Group.SelectedUsers' | translate}} : {{selectedUsersCount}}
        </span>
      </div>
      
      <br>
      <div [formGroup]="form" class="col-md-4 col-sm-12">
        <mat-form-field class="w-100" appearance="fill">
          <mat-label>
            {{ 'Group.SelectLevel' | translate }}
          </mat-label>
          <mat-select formControlName="levelModalForm">
            <mat-option *ngFor="let level of levels" [value]="level">
              {{ level.vLevelName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

    </ng-container>
    
    <ng-container customButtonRight>
      <div>
        <button
          class=""
          type="button"
          (click)="massiveUserAsignation()"
          mat-raised-button
          color="primary"
        >
          {{ "Group.AddUsers" | translate }}
        </button>
      </div>
    </ng-container>

  </app-modal2>
</ng-template>

