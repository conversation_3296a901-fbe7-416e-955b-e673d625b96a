<div class="title-secundary m-0">
    <h2 class="h3 m-0">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
        {{ "SettingsHub" | translate }}
    </h2>
</div>
<app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>
<div class="cont-row mt-2">
    <mat-form-field class="w-25 mx-1">
        <mat-label>
            {{ "Search" | translate }}
        </mat-label>
        <input  (keyup.enter)="getListMenuByIdUser()" [(ngModel)]="keyword"  matInput type="text" class="form-control" 
            placeholder="{{ 'Search' | translate }}" PreventionSqlInjector />
        <mat-icon class="hand click" (click)="getListMenuByIdUser()" matSuffix>search</mat-icon>
    </mat-form-field>
    <div class="cont-icon">
        <mat-icon class="click" matTooltipPosition="above" matTooltip="{{ 'Tooltips.SearchIcon' | translate }}">help_outline</mat-icon>
    </div>
</div>
<div *ngFor="let item of menu" class="row my-2">
    <div class="row">
        <h3 class="my-4">
            {{item.Name}}
            <ng-container *ngIf="item.Children.length > 0">
                
                <mat-icon *ngIf="item.Children[0].IParentHub === 14" class="click"  matTooltipPosition="right" matTooltip="{{ 'Tooltips.PortalConfiguration' | translate }}">help_outline</mat-icon>
                <mat-icon *ngIf="item.Children[0].IParentHub === 15" class="click"  matTooltipPosition="right" matTooltip="{{ 'Tooltips.QuoteSettings' | translate }}">help_outline</mat-icon>
                <mat-icon *ngIf="item.Children[0].IParentHub === 16" class="click"  matTooltipPosition="right" matTooltip="{{ 'Tooltips.ConfigurationBack' | translate }}">help_outline</mat-icon>
            </ng-container>
        </h3>
    </div>
    <div class="my-2 container-modules">
        <div *ngFor="let child of item.Children" class="card-menu m-4 align-items-md-center">
            <mat-card (click)="goToChildVTagSon(child.VTagSon)" [routerLink]="['#']">
                <img mat-card-image [src]="child.VImageSon" />
            </mat-card>
            <mat-card-title>{{child.VDescriptionSon}}</mat-card-title>
        </div>
    </div>
</div>