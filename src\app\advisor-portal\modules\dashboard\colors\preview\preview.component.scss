@import "/src/assets/styles/variables";
.btn-test {
  border: 1px solid;
  border-radius: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.cont-btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
}
.box {
  margin-right: 8px;
  p {
    margin: 4px;
  }
}

.cont-tabs {
  display: flex;
}
.tap {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.tap-active {
  width: 404px;
  height: 48px;
}
.tap-inactive {
  width: 404px;
  height: 34px;
  border: 1px solid #bbbec2;
  border-bottom: none;
  margin-top: 16px;
  font-weight: bold;
}

.title-tabs {
  margin-top: 8px;
}

.cont-principal-sidebar {
  h6 {
    margin: 0px;
  }
}

.sidebar {
  width: 240px;
  height: 48px;
  background: #dee3e9;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}
.cont-sidebar {
  border-radius: 4px;
  width: 240px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
}

.item-sidebar-firts {
  width: 3%;
  height: 100%;
}
.item-icon {
  width: 24px;
  height: 24px;
  margin-left: 4px;
}

.product {
  width: 120px;
  height: 120px;
  background: $color_2;
  box-shadow: 0px 5px 6px 3px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-product {
  font-size: 96px;
  width: 100px !important;
  height: 100px !important;
}
.product-name {
  margin-left: 34px;
  margin-top: 12px;
}

.download {
  border: 2px solid black !important;
  box-shadow: none !important;
}

.cont-btns {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 48px;
}
