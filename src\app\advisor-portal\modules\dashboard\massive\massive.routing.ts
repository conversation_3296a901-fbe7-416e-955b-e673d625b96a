import { Routes } from '@angular/router';
import { MassiveComponent } from './massive.component';

export default [
  {
    path: '',
    component: MassiveComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './choose-massive-option/choose-massive-option.component'
          ).then((c) => c.<PERSON><PERSON>MassiveOptionComponent),
      },
      {
        path: 'mass-creation-policy/:idBusinessByCountry',
        loadComponent: () =>
          import('./mass-creation-policy/mass-creation-policy.component').then(
            (c) => c.MassCreationPolicyComponent
          ),
      },
      {
        path: 'mass-management-policy/:idBusinessByCountry',
        loadComponent: () =>
          import(
            './mass-creation-policy/mass-management/mass-management.component'
          ).then((c) => c.MassManagementComponent),
      },
      {
        path: 'load-viewer/:idBusinessByCountry',
        loadComponent: () =>
          import('./load-viewer/load-viewer.component').then(
            (c) => c.<PERSON><PERSON>omponent
          ),
      },
      {
        path: 'mass-creation-tasks/:idBusinessByCountry',
        loadComponent: () =>
          import('./mass-creation-tasks/mass-creation-tasks.component').then(
            (c) => c.MassCreationTasksComponent
          ),
      },
      {
        path: 'mass-creation-state/:idBusinessByCountry',
        loadComponent: () =>
          import('./mass-creation-state/mass-creation-state.component').then(
            (c) => c.MassCreationStateComponent
          ),
      },
      {
        path: 'mass-upload-documents/:idBusinessByCountry',
        loadComponent: () =>
          import(
            './mass-upload-documents/mass-upload-documents.component'
          ).then((c) => c.MassUploadDocumentsComponent),
      },
      {
        path: 'configure-upload-template/:idBusinessByCountry',
        loadComponent: () =>
          import(
            './mass-upload-documents/configure-upload-template/configure-upload-template.component'
          ).then((c) => c.ConfigureUploadTemplateComponent),
      },
      {
        path: 'mass-renewal-policy/:idBusinessByCountry',
        loadComponent: () =>
          import('./mass-renewal-policy/mass-renewal-policy.component').then(
            (c) => c.MassRenewalPolicyComponent
          ),
      },
    ],
  },
] as Routes;
