import { CommonModule } from '@angular/common';
import {
  <PERSON>mponent,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  ItemElementModel,
  ItemListModel,
  SectionPlanModel,
} from 'src/app/shared/models/product-sections';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { SectionService } from 'src/app/shared/services/section/section.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { Location } from '@angular/common';
import { MessageService } from 'src/app/shared/services/message/message.service';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';

@Component({
  selector: 'app-edit-new-characteristics',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    Modal2Component,
    MatSlideToggleModule,
    MatSelectModule,
    TableComponent,
    TranslateModule,
    PreventionSqlInjectorDirective
  ],
  templateUrl: './edit-new-characteristics.component.html',
  styleUrls: ['./edit-new-characteristics.component.scss'],
})
export class EditNewCharacteristicsComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  formCharacteristics: FormGroup = new FormGroup({});
  formElement: FormGroup = new FormGroup({});
  itemType: ItemListModel[] = [];
  @ViewChild('addItemModal') addItemModal?: TemplateRef<any>;
  enterprise: string = '';
  country: string = '';
  product: string = '';
  productId: number = 0;
  characteristicId: number = 0;
  editingCharacteristic: boolean = false;
  btnDelete: boolean = false;
  dataTableItems: ItemElementModel[] = [];
  private readonly _itemsVisibleOnQuoteCard: number = 6;
  fullItemsOnQuoteCard: boolean = false;
  editItemOnQuoteCard: boolean = false;
  showQuoteCardCheck: boolean = false;
  titleModal: string =  this._translateService.instant('ProductSection.AddItem');
  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('ProductSection.ItemName'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('ProductSection.Type'),
      columnValue: 'fkIIdTextTypeField',
      functionValue: (item: ItemElementModel) => this.parseTypeFieldById(item),
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsService.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  constructor(
    public addItemDialog: MatDialog,
    public utilsService: UtilsService,
    private _fb: FormBuilder,
    private _sectionService: SectionService,
    private _settingService: SettingService,
    public router: Router,
    private _activatedRoute: ActivatedRoute,
    private _location: Location,
    private _messageService: MessageService,
    private _translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.getsettingCountryAndCompanySubscription();
    this.getParamsUrl();
    this.initForm();
    this.getSectionTypeFieldList();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {

      /// Title modal section
      this.titleModal = this._translateService.instant('ProductSection.AddItem');

      /// Traduction table
      this.estructTable[0].columnLabel = this._translateService.instant(
        'ProductSection.ItemName'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'ProductSection.Type'
      );
      this.estructTable[2].columnLabel =
        this._translateService.instant('Status');
      this.estructTable[3].columnLabel =
        this._translateService.instant('Modify');
    });
  }

  getsettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.enterprise = response.enterprise.vName;
            this.country = response.country.vCountryName;
          }
        }
      );
  }

  getParamsUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      this.productId = Number(params.id);
      this.product = params.product;
      this.characteristicId = params.pkIIdSectionPlanProduct;
      if (this.characteristicId !== 0 && this.characteristicId) {
        this.editingCharacteristic = true;
        this.getSectionPlanById(this.characteristicId);
        this.getSectionFieldList(this.characteristicId);
      }
    });
  }

  initForm() {
    this.formCharacteristics = this._fb.group({
      vDescription: ['', [Validators.required]],
      fkIIdProduct: [this.productId],
      bActive: [true, [Validators.required]],
      sectionProductField: [[]],
    });

    this.formElement = this._fb.group({
      pkIIdSectionProductField: null,
      fkIIdPlanTypeField: [null, [Validators.required]],
      fkIIdSectionPlanProduct: [0],
      vName: ['', [Validators.required]],
      bActive: [true, [Validators.required]],
      bShowInQuoteCard: [false, [Validators.required]],
    });
  }

  getSectionTypeFieldList() {
    this._sectionService.getSectionTypeFieldList().subscribe({
      next: (response) => {
        this.itemType = response.result;
      },
    });
  }

  getSectionFieldList(characteristicId: number) {
    this._sectionService.getSectionFieldList(characteristicId).subscribe({
      next: (response) => {
        if (!response.error) {
          if (response.result.length > 0) {
            this.dataTableItems = response.result;
            this.formCharacteristics
              .get('sectionProductField')
              ?.setValue(response.result);
          }
        }
      },
    });
  }

  getSectionPlanById(idSection: number) {
    this._sectionService.getSectionPlanById(idSection).subscribe({
      next: (response) => {
        this.formCharacteristics
          .get('vDescription')
          ?.setValue(response.result.vDescription);
        this.formCharacteristics
          .get('bActive')
          ?.setValue(response.result.bActive);
        this.formCharacteristics
          .get('fkIIdProduct')
          ?.setValue(response.result.fkIIdProduct);
      },
    });
  }

  controller(event: IconEventClickModel) {
    this.btnDelete = true;
    this.titleModal = this._translateService.instant('ProductSection.ModifyItem');
    this.showElementToEdit(event.value);
  }

  addItemModalFuction() {
    this.ValidateVisibleItemsOnQuoteCard();
    this.addItemDialog.open(this.addItemModal!);
  }

  saveItem() {
    if (this.editingCharacteristic) {
      this.addItemToDB();
    } else {
      this.addItemToLocal();
    }
  }

  addItem() {
    this._translateService.instant('ProductSection.AddItem');
    this.formElement.reset();
    this.formElement.get('bActive')?.setValue(true);
    this.formElement.get('bShowInQuoteCard')?.setValue(false);
    this.editItemOnQuoteCard = false;
    this.addItemModalFuction();
  }

  addItemToLocal() {
    let item: ItemElementModel = this.formElement.value;

    if (item.pkIIdSectionProductField) {
      let index = this.dataTableItems.findIndex(
        (i: ItemElementModel) =>
          i.pkIIdSectionProductField == item.pkIIdSectionProductField
      );
      this.dataTableItems[index] = item;
    } else {
      item.pkIIdSectionProductField = this.generateUniqueKey();
      this.dataTableItems.push(item);
    }

    this.dataTableItems = this.dataTableItems.slice();
    this.formElement.reset();
    this.formElement.get('bActive')?.setValue(true);

    this.addItemDialog.closeAll();
  }

  addItemToDB() {
    let item: ItemElementModel = this.formElement.value;
    if (this.editingCharacteristic) {
      const id = this.formElement.get('pkIIdSectionProductField')?.value;
      const elementInLocal = !id || id < 0;

      if (elementInLocal) {
        this.registerElement();
      } else {
        this.updateElement();
      }
    } else {
      this.dataTableItems.push(item);
      this.dataTableItems = this.dataTableItems.slice();
    }

    this.addItemDialog.closeAll();
    this.formElement.reset();
    this.formElement.get('bActive')?.setValue(true);
  }

  generateUniqueKey() {
    return Math.floor(Math.random() * -1000000) + 1;
  }

  parseTypeFieldById(item: ItemElementModel) {
    switch (item.fkIIdPlanTypeField) {
      case 1:
        return 'Texto';
      case 2:
        return 'Checkbox';
      case 3:
        return 'Checkbox compuesto';
      case 4:
        return 'Límite y deducible';
      default:
        return 'NONE';
    }
  }

  createCharacteristic() {
    this.formCharacteristics
      .get('sectionProductField')
      ?.setValue(this.dataTableItems);
    const payload = this.formCharacteristics.value;

    payload.sectionProductField = payload.sectionProductField.map(
      (p: ItemElementModel) => {
        p.pkIIdSectionProductField = 0;
        p.fkIIdSectionPlanProduct = 0;

        return p;
      }
    );
    this._sectionService.registerSectionPlan(payload).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            response.message
          );
          this._location.back();
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  updateCharacteristic() {
    let payload: SectionPlanModel = this.formCharacteristics.value;
    payload.pkIIdSectionPlanProduct = Number(this.characteristicId);
    delete payload.sectionProductField;
    if (this.validFormCharacteristics) {
      this._sectionService.updateSectionPlan(payload).subscribe({
        next: (response) => {
          if (!response.error) {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              response.message
            );
            this._location.back();
          } else {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              response.message
            );
          }
        },
      });
    }
  }

  registerElement() {
    let element = this.formElement.value;
    delete element.pkIIdSectionProductField;
    element.fkIIdSectionPlanProduct = Number(this.characteristicId);
    this._sectionService.registerSectionField(element).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            response.message
          );
          this.getSectionFieldList(this.characteristicId);
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  showElementToEdit(element: ItemElementModel) {
    this.formElement
      .get('pkIIdSectionProductField')
      ?.setValue(element.pkIIdSectionProductField);

    this.formElement
      .get('fkIIdSectionPlanProduct')
      ?.setValue(element.fkIIdSectionPlanProduct);
    this.formElement.get('vName')?.setValue(element.vName);
    this.formElement
      .get('fkIIdPlanTypeField')
      ?.setValue(element.fkIIdPlanTypeField);
    this.formElement.get('bActive')?.setValue(element.bActive);
    this.formElement.get('bShowInQuoteCard')?.setValue(element.bShowInQuoteCard ?? false);
    this.editItemOnQuoteCard = this.formElement.get('bShowInQuoteCard')?.value;
    this.addItemModalFuction();
  }

  updateElement() {
    let payload: ItemElementModel = this.formElement.value;
    this._sectionService.updateSectionField(payload).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            response.message
          );
          this.getSectionFieldList(this.characteristicId);
          this.btnDelete = false;
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  get validFormCharacteristics(): boolean {
    return this.formCharacteristics.valid;
  }

  get validFormElement(): boolean {
    return this.formElement.valid;
  }

  goBack() {
    this._location.back();
  }

  deleteItem() {
    if (this.editingCharacteristic) {
      const pkIIdSectionProductField =
        this.formElement.get('pkIIdSectionProductField')?.value || 0;

      this._messageService
        .messageConfirmationAndNegation(
          this._translateService.instant('ProductSection.DeleteConfirmationMessage'),
          this._translateService.instant('ProductSection.WarningMessageForDeletion'),
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            this._sectionService
              .deleteSectionField(pkIIdSectionProductField)
              .subscribe({
                next: (response) => {
                  if (!response.error) {
                    this._messageService.messageSuccess('', response.message);
                    this.getSectionFieldList(this.characteristicId);
                    this.btnDelete = false;
                    this.addItemDialog.closeAll();
                  } else {

                    if (response.message === 'HasAssociatedItem') {
                      this._messageService.messageWaring(
                        this._translateService.instant(
                          'CatalogSetting.MessagesCatalogo.deletedMessageTitle'
                        ),
                        this._translateService.instant(
                          'ProductSection.deletedMessageSubTitle'
                        )
                      );
                    }
                    else{
                      this._messageService.messageInfo(
                        this._translateService.instant('ThereWasAError'),
                        response.message
                      );
                    }                    
                  }
                },
              });
          }
        });
    } else {
      const pkIIdSectionProductField = this.formElement.get(
        'pkIIdSectionProductField'
      )?.value;
      this.dataTableItems = this.dataTableItems.filter(
        (i: ItemElementModel) =>
          i.pkIIdSectionProductField != pkIIdSectionProductField
      );
      this.btnDelete = false;
      this.addItemDialog.closeAll();
    }
  }

  eventCloseModal(event: boolean) {
    this.titleModal = this._translateService.instant('ProductSection.AddItem');
    this.btnDelete = false;
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  onPlanTypeChange(){
    this.formElement.get('bShowInQuoteCard')?.setValue(false);
    this.ValidateQuoteCardCheckVisible();
  }

  ValidateVisibleItemsOnQuoteCard(): void{
    const itemsOnQuoteCard = this.dataTableItems.reduce((count: number, item: ItemElementModel) => item.bShowInQuoteCard ? count + 1 : count, 0);
    this.fullItemsOnQuoteCard = (itemsOnQuoteCard >= this._itemsVisibleOnQuoteCard);
    this.ValidateQuoteCardCheckVisible();
  }

  ValidateQuoteCardCheckVisible(): void{
    if (!this.fullItemsOnQuoteCard || this.editItemOnQuoteCard){
      const planTypeSelected = this.itemType.find((item: ItemListModel) => item.pkIIdPlanTypeField == this.formElement.get('fkIIdPlanTypeField')?.value);
      this.showQuoteCardCheck = planTypeSelected?.bInQuoteCard ?? false;
    }
    else{
      this.showQuoteCardCheck = false;
    }
  }
}
