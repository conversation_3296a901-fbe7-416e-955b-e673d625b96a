<div class="col-12 col-md-12 mt-3">
  <div class="d-flex justify-content-start align-items-center mb-2">
    <h3 class="m-0">{{ "FormConfiguration.PopUp.Title" | translate }}</h3>
    <mat-icon class="click ml-1" matTooltipPosition="right" matTooltip="{{ 'Tooltips.PopUpTitle' | translate }}">help_outline</mat-icon>
  </div>
  <app-table
    [displayedColumns]="estructTable"
    [data]="dataTablePopUp"
    (iconClick)="controller($event)"
  ></app-table>
  <button
    class="mb-2"
    type="button"
    mat-raised-button
    color="primary"
    (click)="openModaleditNewPopUp()"
  >
    {{ "Add" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
</div>

<!-- modal editar-crear un pop-up -->
<ng-template #editNewPopUpModal>
  <app-modal2 [titleModal]="titelModal" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="form">
        <div class="row mt-5">
          <div class="col-md-12 col-sm-12">
            <mat-slide-toggle class="mb-3" formControlName="bActive">
              {{ "FormConfiguration.PopUp.ActivePopUpLabel" | translate }}
            </mat-slide-toggle>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ "FormConfiguration.PopUp.PopUpTypeLabel" | translate }}
              </mat-label>
              <mat-select formControlName="fkiIdPopUpType">
                <mat-option
                  *ngFor="let popUpTypes of listOfPopUpTypes"
                  [value]="popUpTypes.fkiIdPopUpType"
                >
                  {{ popUpTypes.name }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(form, 'fkiIdPopUpType', 'required')
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "FormConfiguration.PopUp.PopUpTitleLabel" | translate }}
              </mat-label>
              <input matInput formControlName="vTitle" />
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'vTitle', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "FormConfiguration.PopUp.PopUpTextLabel" | translate }}
              </mat-label>
              <input matInput formControlName="vText" />
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'vText', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <section class="example-section">
              <mat-radio-group formControlName="bIsSave">
                <mat-radio-button class="example-margin" [value]="true">{{
                  "FormConfiguration.PopUp.ButtonSaveLabel" | translate
                }}</mat-radio-button>
                <mat-radio-button class="example-margin" [value]="false">{{
                  "FormConfiguration.PopUp.ButtonUpdateLabel" | translate
                }}</mat-radio-button>
              </mat-radio-group>
            </section>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonCenter>
      <button
        (click)="deletePopUp()"
        type="button"
        mat-raised-button
        *ngIf="form.get('pkiIdPopUp')?.value > 0"
        class="delete"
      >
        {{ "FormConfiguration.PopUp.DeletePopUpButton" | translate }}
        <mat-icon class="delete" iconPositionEnd fontIcon="close"></mat-icon>
      </button>
    </ng-container>

    <ng-container customButtonRight>
      <button
        *ngIf="form.get('pkiIdPopUp')?.value === 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="createPopUp()"
        [disabled]="!valid"
      >
        {{ "FormConfiguration.PopUp.CreatePopUpButton" | translate }}
      </button>
      <button
        *ngIf="form.get('pkiIdPopUp')?.value > 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="editPopUp()"
        [disabled]="!valid"
      >
        {{ "SaveChanges" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
