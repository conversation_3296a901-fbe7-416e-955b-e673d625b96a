import { CommonModule } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { ActionsToCreate } from 'src/app/shared/models/module-layout/actions.models';
import {
  ProductStage,
  ProductModel,
  ProductApiModel,
} from 'src/app/shared/models/product/product.model';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { AppState } from 'src/app/store/app.reducers';
import {
  getProduct,
  initNewProduct,
  changeStage,
  update,
  createProduct,
} from 'src/app/store/actions/product.actions';
import { StepProductDataComponent } from './step-product-data/step-product-data.component';
import { StepFormDataComponent } from './step-form-data/step-form-data.component';
import { ActionsToCreateComponent } from 'src/app/shared/components/actions-to-create/actions-to-create.component';
import {
  TranslateModule,
  TranslateService,
  LangChangeEvent,
} from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { StepCategoryDataComponent } from './step-category-data/step-category-data.component';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { QuoteFlowComponent } from '../quote-flow/quote-flow.component';

@Component({
  selector: 'app-edit-new-product',
  standalone: true,
  imports: [
    CommonModule,
    StepProductDataComponent,
    QuoteFlowComponent,
    StepFormDataComponent,
    StepCategoryDataComponent,
    ActionsToCreateComponent,
    WizardComponent,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
  ],
  templateUrl: './edit-new-product.component.html',
  styles: [],
})
export class EditNewProductComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  id?: number = NaN;
  idBusinessProduct: number = 0;
  routeSubs?: Subscription;
  productSubs?: Subscription;
  product?: ProductModel;
  previousStage: ProductStage = ProductStage.Product;
  dataForm?: any;
  buttonNextDisabled = false;
  idBusinessCountry: number = 0;

  actions: ActionsToCreate = {
    returnText: this._translateService.instant('Product.Products'),
    backRoute: '/dashboard/products',
    backText: this._translateService.instant('Product.Product'),
    nextText: this._translateService.instant('Form'),
    saveButtonText: this._translateService.instant('SaveChanges'),
  };
  itIsEditingHome: any;
  constructor(
    public router: Router,
    private _activatedRoute: ActivatedRoute,
    private _store: Store<AppState>,
    public utilsSvc: UtilsService,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _messageService: MessageService,
    private _productServices: ProductService,
    private _customRouter: CustomRouterService
  ) {}

  ngOnInit(): void {

    this._store.dispatch(changeStage({ stage: ProductStage.Product }));
    this.validateEditOrNew();
    this.subscribeProduct();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.actions.returnText =
        this._translateService.instant('Product.Products');
    });
  }

  validateEditOrNew() {
    this.routeSubs = this._activatedRoute.params.subscribe((p) => {
      if (p['id']) {
        this.id = Number(p['id']);
        if (isNaN(this.id))
          this._customRouter.navigate(['/dashboard/products']);
        else {
          this._store.dispatch(getProduct({ id: this.id }));
          this.idBusinessProduct = this.id;
          if (this.idBusinessProduct === 0) {
            this._customRouter.navigate([this.actions?.backRoute]);
          }
        }
      } else {
        this.buttonNextDisabled = true;
        this._store.dispatch(initNewProduct());
      }
    });
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
          }
        }
      );
  }

  subscribeProduct() {
    this.productSubs = this._store.select('product').subscribe((p) => {
      console.log('p ->', p);

      if (p.error) return console.log(`Error from product loading ${p.error}`);
      this.product = p.Product;
    });
  }

  nextStep() {
    const isFormValid = this._productServices.getIsValidForm();
    console.log('this.product?', this.product);

    console.log('ProductStage', ProductStage);

    switch (this.product?.stage) {
      case ProductStage.Product:
        this._store.dispatch(changeStage({ stage: ProductStage.Form }));
        this.actions.nextText = this._translateService.instant('Next');
        break;
      case ProductStage.Form:
        if (isFormValid) {
          this._store.dispatch(changeStage({ stage: ProductStage.Category }));
          this.actions.nextText = this._translateService.instant('Next');
          break;
        } else {
          this.buttonNextDisabled = false;
          break;
        }
      case ProductStage.Category:
        if (isFormValid) {
          this._store.dispatch(changeStage({ stage: ProductStage.QuoteFlow }));
          this.actions.nextText = this._translateService.instant('Complete');
          break;
        } else {
          this.buttonNextDisabled = false;
          break;
        }

      case ProductStage.QuoteFlow:
        if (isFormValid) {
          this.actions.nextText = this._translateService.instant('Complete');
          break;
        } else {
          this.buttonNextDisabled = false;
        }
    }
  }

  backStep() {
    switch (this.product?.stage) {
      case ProductStage.Form:
        this._store.dispatch(changeStage({ stage: ProductStage.Product }));
        this.actions.nextText = this._translateService.instant('Next');
        break;
      case ProductStage.Category:
        this._store.dispatch(changeStage({ stage: ProductStage.Form }));
        this.actions.nextText = this._translateService.instant('Next');
        break;
      case ProductStage.QuoteFlow:
        this._store.dispatch(changeStage({ stage: ProductStage.Category }));
        this.actions.nextText = this._translateService.instant('Next');
        break;
    }
  }
/**
 * Validates the selection and configuration of the policy type in the form.
 * 
 * - If `idPolicyType` or `idPolicyCode` are not defined, displays an error message
 *   indicating that a policy type must be selected and halts further execution.
 * - If `idPolicyCode` is 'COL' (collective policy), checks that both start and end
 *   validity dates (`dValidityStart` and `dValidityEnd`) are defined; if not, displays
 *   a specific error message and halts further execution.
 *
 * @returns {boolean} `true` if validation is successful, `false` if required information is missing.
 */
private validatePolicyTypeSelection(): boolean {
  // Checks if the policy type or policy code are not defined
  if (!this.dataForm?.idPolicyType && !this.dataForm?.idPolicyCode) {
      this._messageService.messageError(
          this._translateService.instant('Product.SelectPolicyType')
      );
      return false;
  }

  // Checks if the policy code is 'COL' and that validity dates are defined
  if (
      this.dataForm?.idPolicyCode === 'COL' &&
      (!this.dataForm.dValidityStart || !this.dataForm.dValidityEnd)
  ) {
      this._messageService.messageError(
          this._translateService.instant('Product.ForCOLPolicyTypeSelectStartEndDates')
      );
      return false;
  }

  return true;
}

  save() {
    if (this.dataForm != undefined && this.validatePolicyTypeSelection()) {
      switch (this.product?.stage) {
        case ProductStage.Product:
          this.dataForm.IdBusinessByCountry = this.idBusinessCountry;
          if (Number.isNaN(this.id)) {
            if (this.isFormDataValid()) {
              this._store.dispatch(createProduct({ Product: this.dataForm }));
              this.buttonNextDisabled = false;
            } else {
              this._messageService.messageError(
                'Por favor completa la información.'
              );
            }
          } else {
            this._store.dispatch(update({ product: this.dataForm }));
          }

          break;
      }
    } else {
      this._messageService.messageError('Por favor completa la información.');
    }
  }

  isFormDataValid(): boolean {
    // Validar las claves requeridas
    if (
      this.dataForm.name === null ||
      this.dataForm.description === null ||
      this.dataForm.comments === null ||
      this.dataForm.logo.base64 === null ||
      (this.dataForm.inAdvisorPortal === false &&
        this.dataForm.inCustomerPortal === false) ||
      (this.dataForm.customerPortal &&
        (this.dataForm.coverages.length === 0 ||
          this.dataForm.logoCustomerPortal.base64 === null))
    ) {
      return false;
    }
    return true;
  }

  backToProducts() {
    if (this.product?.stage == ProductStage.Product) {
      this._messageService
        .messageConfirmationAndNegation(
          this._translateService.instant('Out?'),
          this._translateService.instant('UnsavedSettingsWillBeLost'),
          'info',
          this._translateService.instant('Continue'),
          this._translateService.instant('Out')
        )
        .then((response) => {
          if (response) {
            this._customRouter.navigate([this.actions.backRoute]);
          }
        });
    } else {
      this._customRouter.navigate([this.actions.backRoute]);
    }
  }

  ngOnDestroy(): void {
    this.routeSubs?.unsubscribe();
    this.productSubs?.unsubscribe();
  }
}
