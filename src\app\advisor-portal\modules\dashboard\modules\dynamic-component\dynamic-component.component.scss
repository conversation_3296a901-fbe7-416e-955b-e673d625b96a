.mat-elevation-z8 {
  box-shadow: 0px 0px 0px 1px #d9d9d9;
  width: max-content;
}
.horizontal-scroll{
  overflow-x: scroll;
}

.hidden-field{
  padding:0;
  width: 0;
  height: 0;
}

.mat-elevation-table {
  box-shadow: 0px 0px 0px 1px #d9d9d9;
  width: 95%;
  margin: 2%;
}

.section-title{
  font-size: 32px;
  font-weight: 700;
  font-family: Inter;
  line-height: 39px;
  letter-spacing: -.012em;
  text-align: left;
}

.table th, .table td {
  text-align: center;
  vertical-align: middle; 
}

.table td button mat-icon {
  vertical-align: middle;
}

.table-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 5px;
}

.horizontal-scroll {
  overflow-x: auto;
}

.container-hidden-x{
  overflow-x: hidden;
}

.add-row-button{
  margin-left: 2%;
}
