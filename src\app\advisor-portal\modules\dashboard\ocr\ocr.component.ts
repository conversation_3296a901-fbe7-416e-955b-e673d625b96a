import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-ocr',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    BreadcrumbComponent,
    MatIconModule,
    MatTooltipModule,
  ],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/ocr.svg" />
        {{ ocr }}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styles: [],
})
export class OCRComponent {
  start: string = this._translateService.instant('Inicio');
  ocr: string = 'OCR';
  sections: { label: string; link: string }[] = [
    { label: this.start, link: '/dashboard' },
    { label: 'OCR ', link: '/dashboard/ocr' },
  ];

  constructor(private _translateService: TranslateService) {}
}
