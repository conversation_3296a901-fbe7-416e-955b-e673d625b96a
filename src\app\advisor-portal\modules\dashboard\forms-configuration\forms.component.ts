import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
@Component({
  selector: 'app-forms',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule,BreadcrumbComponent,MatTooltipModule,MatIconModule],
  template: `
  <div>
    <h2 class="h3">
      <img src="assets/img/layouts/config_ico.svg" alt="" /> 
      {{"ConfigurarFormulario" | translate}}
      <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.FormConfiguration' | translate }}">help_outline</mat-icon>
    </h2>
  </div>
  <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

  <router-outlet></router-outlet>
`, 
  styleUrls: []
})
export class FormsComponent implements OnInit{
  constructor(
    private _translateService: TranslateService,
  ) {}

  settings: string = this._translateService.instant("Settings")  
  forms: string = this._translateService.instant("Formularios")

  sections: {label: string, link: string}[]=[
    {label: this.settings, link: '/dashboard/configuration'},
    {label: this.forms, link: '/dashboard/forms'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.settings = this._translateService.instant("Settings")
      this.forms = this._translateService.instant("Formularios")
      this.sections[0].label = this.settings
      this.sections[1].label = this.forms
    });
  }
}
