<h5 class="mb-2">{{ "SettingColors.PreviewTitle" | translate }}</h5>
<div class="cont-text-info">
  <h6 class="m-0">{{ "SettingColors.MainButton" | translate }}</h6>
  <p class="m-0">{{ "SettingColors.DescriptionPreview" | translate }}</p>
</div>

<div class="cont-btn mt-2 mb-3">
  <div class="box">
    <p>{{ "SettingColors.Default" | translate }}</p>
    <button
      type="button"
      class="btn-test"
      [style.background]="allColors.primaryColor"
      [style.color]="allColors.secondaryColor"
    >
      <mat-icon class="mx-2" fontIcon="arrow_back"></mat-icon>
      {{ "SettingColors.Button" | translate }}
      <mat-icon class="mx-2" fontIcon="arrow_forward"></mat-icon>
    </button>
    <p>
      {{ "SettingColors.Background" | translate }} {{ allColors.primaryColor }}
    </p>
    <p>{{ "SettingColors.Text" | translate }} {{ allColors.secondaryColor }}</p>
  </div>
  <div class="box">
    <p>{{ "SettingColors.Hover" | translate }}</p>
    <button
      type="button"
      class="btn-test"
      [style.background]="allColors.shadeColor"
      [style.color]="allColors.secondaryColor"
    >
      <mat-icon class="mx-2" fontIcon="arrow_back"></mat-icon>
      {{ "SettingColors.Button" | translate }}
      <mat-icon class="mx-2" fontIcon="arrow_forward"></mat-icon>
    </button>
    <p>
      {{ "SettingColors.Background" | translate }} {{ allColors.shadeColor }}
    </p>
    <p>{{ "SettingColors.Text" | translate }} {{ allColors.secondaryColor }}</p>
  </div>
  <div class="box">
    <p>{{ "SettingColors.HeldDown" | translate }}</p>
    <button
      type="button"
      class="btn-test"
      [style.background]="allColors.shadeColor"
      [style.color]="allColors.primaryColor"
    >
      <mat-icon class="mx-2" fontIcon="arrow_back"></mat-icon>
      {{ "SettingColors.Button" | translate }}
      <mat-icon class="mx-2" fontIcon="arrow_forward"></mat-icon>
    </button>
    <p>
      {{ "SettingColors.Background" | translate }} {{ allColors.shadeColor }}
    </p>
    <p>{{ "SettingColors.Text" | translate }} {{ allColors.primaryColor }}</p>
  </div>
</div>

<h5 class="title-tabs">{{ "SettingColors.TabsTitle" | translate }}</h5>
<div class="cont-tabs">
  <div
    class="tap tap-active"
    [style.background]="allColors.primaryColor"
    [style.color]="allColors.secondaryColor"
  >
    <mat-icon class="mx-2" fontIcon="directions_car"></mat-icon>
    {{ "SettingColors.Vehicle" | translate }}
  </div>
  <div class="tap tap-inactive">{{ "SettingColors.Driver" | translate }}</div>
  <div class="tap tap-inactive">{{ "SettingColors.Headline" | translate }}</div>
</div>

<div class="cont-principal-sidebar mt-3">
  <h5>{{ "SettingColors.SidebarTitle" | translate }}</h5>
  <div class="sidebar">
    <div class="cont-sidebar" [style.background]="allColors.tintColor">
      <div
        class="item-sidebar-firts"
        [style.background]="allColors.primaryColor"
      ></div>
      <div class="item-icon">
        <img src="assets/img/layouts/home_ico.svg" alt="" />
      </div>
      <div class="item-sidebar mx-3">
        <span> <strong>Item</strong> </span>
      </div>
    </div>
  </div>
</div>

<div class="cont-product mt-3">
  <h5>{{ "SettingColors.ProductTitle" | translate }}</h5>
  <div class="product">
    <mat-icon
      class="icon-product"
      [style.color]="allColors.primaryColor"
      fontIcon="directions_car"
    ></mat-icon>
  </div>
  <h5 class="product-name">{{ "SettingColors.CarsTitle" | translate }}</h5>
</div>

<div class="cont-btns">
  <button
    class="download mx-2"
    type="button"
    mat-raised-button
    (click)="resetToDefault()"
  >
    <strong>{{ "SettingColors.ResetToDefaultButton" | translate }}</strong>
    <mat-icon iconPositionEnd fontIcon="refresh"></mat-icon>
  </button>

  <button
    type="button"
    mat-raised-button
    color="primary"
    (click)="saveSetting()"
  >
    {{ "SaveChanges" | translate }}
    <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
  </button>
</div>
