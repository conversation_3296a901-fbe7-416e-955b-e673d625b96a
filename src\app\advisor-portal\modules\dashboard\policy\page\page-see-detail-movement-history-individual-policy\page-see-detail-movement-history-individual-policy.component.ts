import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { BeneficiariesTableComponent } from '../../shared/components/beneficiaries-table/beneficiaries-table.component';
import { DetailsPolicyComponent } from '../../shared/components/details-policy/details-policy.component';
import { BeneficiaryModel } from '../../shared/models';
import { BodyTableModel } from 'src/app/shared/models/table';
import { FileService } from 'src/app/shared/services/file/file.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { catchError, of } from 'rxjs';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';

@Component({
  selector: 'app-page-see-detail-movement-history-individual-policy',
  standalone: true,
  imports: [
    CommonModule,
    DetailsPolicyComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    BeneficiariesTableComponent,
    PdfViewerModule,
    MatCardModule,
  ],
  templateUrl:
    './page-see-detail-movement-history-individual-policy.component.html',
  styleUrls: [
    './page-see-detail-movement-history-individual-policy.component.scss',
  ],
})
export class PageSeeDetailMovementHistoryIndividualPolicyComponent
  implements OnInit
{
  //Variables relacionadas con la póliza.
  @Input() idHistory: number = 0;
  @Input() typeMovement: string = '';
  idWtw: number = 0;
  idParent: number = 0;
  idPolicyRisk: number = 0;
  idPolicyType: number = 1;
  idFilePolicy: number = 0;
  policyNumber: string = '';
  policyStatus: string = '';
  pdfSrc: Uint8Array = new Uint8Array();
  detailsPolicyData: any[] = [];
  detailsPreviousPolicyData: any[] = [];
  isRenew: boolean = true;
  previousPolicyNumber: string = '';
  previousPolicyStatus: string = '';

  //Tabla Beneficiarios
  beneficiariesData: BeneficiaryModel[] = [];
  estructTableBeneficiaries: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Policy.NameOfBeneficiary'),
      columnValue: 'nameComplete',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.DocumentType'),
      columnValue: 'typeDocument',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
      columnValue: 'document',
    },
    {
      columnLabel: this._translateService.instant('Policy.Percentage'),
      columnValue: 'percentage',
    },
    {
      columnLabel: this._translateService.instant('Details'),
      columnValue: 'viewDetails',
      columnIcon: 'search',
    },
  ];

  constructor(
    private _translateService: TranslateService,
    private _fileService: FileService,
    private _utilsSvc: UtilsService,
    private _messageService: MessageService,
    private _transactionService: TransactionService
  ) {}

  ngOnInit(): void {
    this.getPolicyByIdWithProduct();
    this.getBeneficiesByIdPolicy();
  }

  //Obtiene el detalle de una póliza.
  getPolicyByIdWithProduct() {
    this._transactionService
      .getPolicyByIdWithProduct(0, 1, this.idHistory)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.detailsPolicyData = this.transformDataDetails(resp.result);
          if (resp.result.policy) {
            this.policyNumber = resp.result.policy.policyNumber;
            this.idWtw = resp.result.policy.idwtw;
            this.policyStatus = resp.result.policy.statePolicy;
            this.idFilePolicy = resp.result.policy.idFilePolicy;

            if (this.idFilePolicy && this.idFilePolicy > 0) {
              this.getPolicyCover();
            }
          }
          if (resp.result.previousPolicy){
            this.previousPolicyNumber = resp.result.previousPolicy.policyNumber;
            this.previousPolicyStatus = resp.result.previousPolicy.statePolicy;
          }
        }
      });
  }

  //Obtiene los beneficiarios de una póliza.
  getBeneficiesByIdPolicy() {
    this._transactionService
      .getBeneficiesByIdPolicy(0, this.idHistory)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.beneficiariesData = resp.result;
        }
      });
  }

  //Obtiene la carátula de una póliza.
  getPolicyCover() {
    this._fileService
      .getUploadFileById(this.idFilePolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant('')
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.imageBase64) {
            this.pdfSrc = this._utilsSvc.base64ToUint8Array(
              resp.result.imageBase64
            );
          }
        }
      });
  }

  //Transforma la información recibida a un estadarizada para que pueda mostarse el detalle de cualquier tipo de póliza.
  transformDataDetails(dataPolicy: any): any[] {
    const result: any[] = [];

    //Información de la póliza individual a excluir.
    const excludePolicyValues = [
      'statePolicy',
      'idPolicyrisk',
      'idFilePolicy',
      'idParent',
      'fieldKey',
      'fieldName',
      'idwtw',
      'policyNumber',
      'idHistoryForm'
    ];

    // Transformar la sección de `policy`
    if (dataPolicy.policy) {
      const policySection = {
        policy: [
          {
            nameSection: this._translateService.instant('Policy.PolicyData'),
            fields: Object.keys(dataPolicy.policy)
              .filter((key) => !excludePolicyValues.includes(key)) // Filtrar los campos
              .map((key) => {
                let value = dataPolicy.policy[key];
                const regex = /^\d{4}-\d{2}-\d{2}$/;
                if(regex.test(value)){
                  value = this._utilsSvc.formatDate(value, 'DD-MM-YYYY');
                }
                return {
                  name: key,
                  value: value,
                };
              }),
          },
        ],
      };

      result.push(policySection);
    }

    // Transformar la sección de `previousPolicy`
    if (dataPolicy.previousPolicy) {
      this.isRenew = true;
      let includePreviewPolicyValues = [
        'startValidity',
        'endValidity',
        'insurance',
        'endorment',
        'typePolicy',
      ];
      const previousPolicySection = {
        policy: [
          {
            nameSection: this._translateService.instant('Policy.PolicyData'),
            fields: Object.keys(dataPolicy.previousPolicy)
              .filter((key) => includePreviewPolicyValues.includes(key)) // Filtrar los campos
              .map((key) => {
                let value = dataPolicy.previousPolicy[key];
                const regex = /^\d{4}-\d{2}-\d{2}$/;
                if(regex.test(value)){
                  value = this._utilsSvc.formatDate(value, 'DD-MM-YYYY');
                }
                return {
                  name: key,
                  value: value,
                };
              }),
          },
        ],
      };

      this.detailsPreviousPolicyData.push(previousPolicySection);
    }

    // Transformar la sección de `takers`
    if (dataPolicy.takers && dataPolicy.takers.length > 0) {
      const takersSection = {
        takers: dataPolicy.takers.map((taker: any) => ({
          nameSection: this._translateService.instant('Policy.TakerData'),
          fields: taker.fields.map((field: any) => ({
            name: field.name,
            value: field.value,
          })),
        })),
      };
      result.push(takersSection);
    }

    // Transformar la sección de `insurances`
    if (dataPolicy.insurances && dataPolicy.insurances.length > 0) {
      const insurancesSection = {
        insurances: dataPolicy.insurances.map((insurance: any) => ({
          nameSection: this._translateService.instant('Policy.DataSecured'),
          fields: insurance.fields.map((field: any) => ({
            name: field.name,
            value: field.value,
          })),
        })),
      };
      result.push(insurancesSection);
    }

    // Transformar la sección de `others`
    if (dataPolicy.others && dataPolicy.others.length > 0) {
      const othersSection = {
        others: dataPolicy.others.map((other: any) => ({
          nameSection: other.nameSection,
          fields: other.fields.map((field: any) => ({
            name: this.checkAndModifyKeyName(field.name),
            value: field.value,
          })),
        })),
      };
      result.push(othersSection);
    }

    return result;
  }

  checkAndModifyKeyName(keyName: string): string {
    const keysToModify: string[] = ['Plan']
    return keysToModify.includes(keyName) ? keyName + ' ' : keyName;
  }

  // Función para descargar la caratula de la póliza en formato PDF.
  downloadPDF() {
    if (this.idFilePolicy > 0) {
      const blob = new Blob([this.pdfSrc], {
        type: 'application/pdf', // Tipo MIME para PDF
      });
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = 'policy-cover.pdf'; // Nombre del archivo con extensión PDF
      link.click();
      window.URL.revokeObjectURL(downloadUrl);
    }
  }
}
