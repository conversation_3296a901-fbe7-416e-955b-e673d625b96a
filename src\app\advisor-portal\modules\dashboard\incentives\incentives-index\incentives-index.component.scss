// graphs///////////////////////////////////////////////////////

  .select-container{
    display: flex;
    flex-direction: column;
    gap: 16px; /* Espaciado entre los selects */
  }
  .summary {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  

  .icon {
    font-size: 40px;
  }
  
  .info {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    flex-wrap: wrap;
  }
  
  .detail-button {
    align-self: flex-start;
    margin-top: 12px;
    background-color: #9c27b0;
    color: white;
  }

  .custom-select {
    padding-right: 80px;
  }

  .graphs-container{
    
    border-radius: 20px; 
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  // carousel
  .cont-tops {
    width: 100%;
    display: flex;
    padding: 16px 64px 64px 64px;
    justify-content: center;
    align-items: center;
    align-content: center;
    gap: 32px;
    align-self: stretch;
    flex-wrap: wrap;
  }

  .container {
    text-align: center;
  }

  .carousel {
    display: flex;
    overflow: hidden;
    position: relative;
    gap: 16px;
    justify-content: center;
  }

  .incentive-card {
    min-width: 300px;
    max-width: 320px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .scaled-image {
    width: 100%;
    height: auto;
    object-fit: contain;
  }

  .overlay-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  }
  .valid-date, .description {
    margin: 0; 
    padding: 4px 0; 
  }
  
  .description{
    font-weight: bolder;
    color:black;
  }

  .btn-conditions {
    margin-top: 12px;
    background-color: #9c27b0;
    color: white;
  }
  mat-card-actions {
    display: flex;
    justify-content: center; 
    align-items: center; 
  }

  .nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    // background-color: rgba(255, 255, 255, 0.7); 
    // border-radius: 50%;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10; 
  }
  
  .nav-button.left {
    left: 0; 
    margin-left: 10px;
  }
  
  .nav-button.right {
    right: 0; 
     margin-right: 10px;
  }
  
  .nav-button:hover {
    background-color: rgba(255, 255, 255, 0.9);
  }
  

  .summary .mat-icon {
    -webkit-user-select: none;
    user-select: none;
    background-repeat: no-repeat;
    display: inline-block;
    fill: currentColor;
    height: 64px;
    width: 64px;
    overflow: hidden;
    font-size: 35px;
}

.space {
  background: #ffffff;
  padding: 4px 8px; 
}

.info p{
  font-size: x-large;
  font-weight: bold;
}
.labelWhite{
  color: #ffffff;
  padding-right: 100px;
}
.info-icon{
  max-width: 10%;
}





.summary-card {
  width: 100%;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  box-sizing: border-box;
}

.info-summary {
  display: flex;
  align-items: center;
  justify-content: space-between; /* reparte espacio horizontal */
  flex-wrap: wrap;
  width: 100%;
  gap: 2rem;
}

.icon-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.icon-summary mat-icon {
  font-size: 40px;
  color: #333;
}

.icon-text strong {
  font-size: 1.5rem;
  display: block;
}

.icon-text {
  margin: 0;
  font-size: 0.85rem;
  color: black;
}
.desc{
  margin: 0;
  font-size: 0.85rem;
  font-weight: 700;
  color: black;

}

.barra-vertical {
  width: 1px;
  height: 60px;
  background-color: #ccc;
}
.stat-block,
.icon-summary {
  flex: 1 1 0;
  min-width: 150px;
  max-width: 100%;
}
.stat-block {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
}

.stat-block label {
  font-size: 0.8rem;
  color: #555;
  margin-bottom: 0.25rem;
}

.stat-box {
  padding: 0.3rem 0.7rem;
  border-radius: 6px;
  font-weight: bold;
}

.label {
  background-color: #f0f1f4;
}

.ventas {
  background-color: #d5f4f4;
  color: #00796b;
}

.cancelaciones {
  background-color: #ffe0b2;
  color: #bf360c;
}

.incentivos {
  background-color: #bbdefb;
  color: #0d47a1;
}