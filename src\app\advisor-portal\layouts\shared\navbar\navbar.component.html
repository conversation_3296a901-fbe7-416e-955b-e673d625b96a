<div class="nav-container">
  <nav class="navbar d-none d-md-block">
    <!-- desktop menu -->
    <button id="navbar-toggler" class="navbar-toggler" type="button" [class.active]="isNavbarExpanded"
      [attr.aria-expanded]="isNavbarExpanded ? 'true' : 'false'" (click)="toggleNavbar()">
      <a class="navbar-brand">
        <img src="assets/img/layouts/wtw_logo_black.svg" alt="wtw" />
      </a>
      <span class="navbar-toggler-icon">
        <img src="assets/img/layouts/menu_ico.svg" alt="1" />
      </span>
      <span class="btn-close navbar-toggler-close"></span>
    </button>

    <div id="navbarTogglerDemo02" class="collapse navbar-collapse" [class.show]="isNavbarExpanded"
      [attr.aria-expanded]="isNavbarExpanded ? 'true' : 'false'" (mouseleave)="collapseSubMenu(); collapseMenu()"
      #navbarMenu>
      <ul class="navbar-nav mb-2  menu-scroll">
        <ng-container *ngFor="let item of menu">
          <li class="nav-item click">
            <ng-container *ngIf="item.submenus.length > 0">
              <a class="nav-link" [class.active]="selectedParent === item" (click)="toggleSubMenu(item); expandMenu()">
                <img [src]="item.image" alt="" />
                <span>{{ item.parentDescription }}</span>
              </a>
              <ul *ngIf="item.submenus.length > 0" class="collapse sub-menu" [id]="item.parentDescription"
                [class.show]="isSubMenuExpanded && selectedParent === item">
                <li class="nav-item" *ngFor="let submenu of item.submenus">
                  <a [routerLink]="isModuleDynamic(submenu.sonUrl)? [submenu.sonUrl+'/'+submenu.idSonMenu]:[submenu.sonUrl]"
                    class="nav-link" [class.active]="
                      submenu.sonUrl === currentUrl ||
                      validateDynamicRoute(submenu.sonUrl)
                    ">
                    <span>{{ submenu.sonDescription }}</span>
                  </a>
                </li>
              </ul>
            </ng-container>

            <ng-container *ngIf="item.submenus.length === 0">
              <a [routerLink]="isModuleDynamic(item.parentUrl)? [item.parentUrl+'/'+item.idSonMenu]:[item.parentUrl]"
                [class.active]="selectedParent === item" class="nav-link" aria-current="page"
                (click)="expandMenu(); toggleSubMenu(item)">
                <img [src]="item.image" alt="" />
                <span>{{ item.parentDescription }}</span>
              </a>
            </ng-container>
          </li>
        </ng-container>
      </ul>
      <a class="nav-link logout click" (click)="logout()">
        <span class="h7">{{ "Header.LogOut" | translate }}</span>
        <img src="assets/img/layouts/logout_ico.svg" alt="" />
      </a>
    </div>
  </nav>

  <nav class="navbar fixed-top d-block d-md-none">
    <!-- movil menu -->
    <div class="d-flex align-items-center justify-content-between" style="height: 80px">
      <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasNavbar"
        aria-controls="offcanvasNavbar">
        <span class="navbar-toggler-icon"><img src="assets/img/layouts/menu_ico.svg" alt="" /></span>
      </button>
      <!-- <a class="navbar-brand" href="#"
        ><img [src]="assets/img/layouts/sufi_logo.svg" alt=""
      /></a> -->
      <a href="perfil.html" class="user"><img src="assets/img/layouts/profile_icon.svg" alt="" /></a>
    </div>
    <div class="offcanvas offcanvas-start" tabindex="-1" id="offcanvasNavbar" aria-labelledby="offcanvasNavbarLabel">
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="offcanvasNavbarLabel">
          <img src="assets/img/layouts/wtw_logo_black.svg" alt="" />
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <ul class="navbar-nav justify-content-end flex-grow-1 pe-3">
          <li class="nav-item">
            <a [routerLink]="['/dashboard']" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              class="nav-link active" aria-current="page">
              <img src="assets/img/layouts/home_ico.svg" alt="" />
              <span>Inicio</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" data-bs-toggle="collapse" href="#quotesMenu" role="button" aria-expanded="false"
              aria-controls="quotesMenu">
              <img src="assets/img/layouts/cotizar_ico.svg" alt="" />
              <span>Cotizar</span>
            </a>
            <ul class="collapse" id="quotesMenu">
              <li class="nav-item">
                <a href="cotizar.html" class="nav-link">
                  <span>Cotizar</span>
                </a>
              </li>
              <li class="nav-item">
                <a href="mis_cotizaciones.html" class="nav-link">
                  <span>Mis cotizaciones</span>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="clientes.html" class="nav-link" aria-current="page" href="#">
              <img src="assets/img/layouts/perfil.svg" alt="" />
              <span>Clientes</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" data-bs-toggle="collapse" href="#configMenu" role="button" aria-expanded="false"
              aria-controls="configMenu">
              <img src="assets/img/layouts/config_ico.svg" alt="" />
              <span>Configuración</span>
            </a>
            <ul class="collapse" id="configMenu">
              <li class="nav-item">
                <a [routerLink]="['/dashboard/business/']" class="nav-link">
                  <span>Empresas</span>
                </a>
              </li>
              <li class="nav-item">
                <a [routerLink]="['/dashboard/roles/']" class="nav-link">
                  <span>Roles</span>
                </a>
              </li>
              <li class="nav-item">
                <a [routerLink]="['/dashboard/users/']" class="nav-link">
                  <span>Usuarios</span>
                </a>
              </li>
              <li class="nav-item">
                <a [routerLink]="['/dashboard/groups/']" class="nav-link">
                  <span>Grupos</span>
                </a>
              </li>
              <li class="nav-item">
                <a [routerLink]="['/dashboard/insurers/']" class="nav-link">
                  <span>Aseguradoras</span>
                </a>
              </li>
              <li class="nav-item">
                <a [routerLink]="['/dashboard/products']" class="nav-link">
                  <span>Productos</span>
                </a>
              </li>
              <li class="nav-item">
                <a [routerLink]="['/dashboard/plans/']" class="nav-link">
                  <span>Planes</span>
                </a>
              </li>
            </ul>
          </li>
        </ul>
        <a class="nav-link logout click" (click)="logout()">
          <span class="h7">Cerrar sesión</span>
          <img src="assets/img/layouts/logout_ico.svg" alt="" />
        </a>
      </div>
    </div>
  </nav>
</div>