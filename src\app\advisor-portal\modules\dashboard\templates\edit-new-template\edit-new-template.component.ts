// @ts-ignore
import { After<PERSON>iew<PERSON>nit, Component, ElementRef, NgModule, OnDestroy, OnInit, QueryList, Renderer2, TemplateRef, ViewChild, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatInputModule } from '@angular/material/input';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { Subscription, catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { AllMenuModel } from 'src/app/shared/models/menu/all-menu.model';
import { ActivatedRoute, Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { StageByStateModel, StageModel } from 'src/app/shared/models/module';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { PorductModulesModel } from 'src/app/shared/models/product/product-modules.model';
import { MatChipsModule } from '@angular/material/chips';
import { CdkListbox, CdkOption } from '@angular/cdk/listbox';
import { FieldService } from 'src/app/shared/services/field/field.service';
import {
  moveItemInArray,
  transferArrayItem
} from '@angular/cdk/drag-drop';
import { QuillEditorComponent } from 'ngx-quill';
import Quill from 'quill';
import { Range } from 'quill'
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { BusinessComponentModel } from 'src/app/shared/models/business';
import { ClMarubeniCheckButtonsComponent } from "src/app/shared/components/countries/cl/cl-marubeni-check-buttons/cl-marubeni-check-buttons.component";
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { ProcessModel } from 'src/app/shared/models/menu';
import { TaskTrayConfigProductApiRoleModel } from 'src/app/shared/models/task-tray-config';

@Component({
  selector: 'app-edit-new-template',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatInputModule,
    CKEditorModule,
    Modal2Component,
    MatButtonModule,
    MatRadioModule,
    MatIconModule,
    MatChipsModule,
    CdkListbox,
    CdkOption,
    QuillEditorComponent,
    ClMarubeniCheckButtonsComponent
  ],
  templateUrl: './edit-new-template.component.html',
  styleUrls: ['./edit-new-template.component.scss']
})

export class EditNewTemplateComponent implements OnInit, OnDestroy, AfterViewInit {
  private _settingCountryAndCompanySubscription?: Subscription;
  @ViewChild('addFieldModal') addFieldModal?: TemplateRef<any>;
  emailTemplate: string = `
    <p>Hola, &nbsp; &nbsp; &nbsp;</p>
    <p >A continuaci&oacute;n, te hacemos env&iacute;o de la cotizaci&oacute;n para tu veh&iacute;culo &nbsp; &nbsp; &nbsp; &nbsp; de placas &nbsp; &nbsp; &nbsp; </p>
    <p >&nbsp;</p>
    <p >Dentro del archivo adjuntado encontrar&aacute;s los precios, coberturas y amparos de cada plan. Tambi&eacute;n encontraras los documentos que nos debes hacer llegar para continuar con el proceso.</p>
    <p >&nbsp;</p>
    <p >&iexcl;Nos encanta poder ayudarte a proteger lo que m&aacute;s quieres!</p>
    <p >&nbsp;</p>
    <p ><strong>Nombre asesor</strong></p>
    <p ><strong>Empresa</strong></p>
    <p ><strong>Datos de contacto</strong></p>
  `

  //variables para drag an drop y richText editor
  @ViewChild(QuillEditorComponent, { static: true }) quillEditor?: QuillEditorComponent;
  campos: string[] = [];
  quill: any;

  //idTemplate en caso de que se valla a editar
  idTemplate: number = 0;
  titleText: string = '';
  forModules: boolean = false;
  maskMail: boolean = false;
  copy: boolean = false;
  idSpecialTemplate: number = 0;
  hiddenCopy: boolean = false;
  moduleSelectedHasChildren: boolean = false;
  operationType: string = 'create';
  fieldSubs?: Subscription;

  form: FormGroup = new FormGroup({});
  fieldForm: FormGroup = new FormGroup({});

  //variables que guardan las listas que se muestran en los dropDown
  moduleList: AllMenuModel[] = [];
  subModuleList: AllMenuModel[] = [];
  stageList: StageModel[] = [];
  stageByStateList: StageByStateModel[] = [];
  allProductsList: PorductModulesModel[] = [];
  fieldList: any[] = [];

  businessComponent?: BusinessComponentModel;

  @ViewChild('emailSubjectInput', { static: false }) emailSubjectInput?: ElementRef;
  input?: string;

  processes: ProcessModel[] = [];
  productsApiRole: TaskTrayConfigProductApiRoleModel[] = [];
  isEmailModal: boolean = false;


  constructor(
    private fb: FormBuilder,
    private _activatedRoute: ActivatedRoute,
    private _modalDialog: MatDialog,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _moduleService: ModuleService,
    private _parameterService: ParametersService,
    private _roleService: RoleService,
    private _moduleSettingsService: ModulesSettingService,
    private _productService: ProductService,
    public _utilsService: UtilsService,
    public _fieldSvc: FieldService,
    private _businessService: BusinessService,
    private _customRouter: CustomRouterService,
  ) { }

  ngOnInit(): void {
    this.getBusinessComponentByIdBusinessCountry();
    this.getAllProcess();

    this.initForm();
    this.validateEditOrNew();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      if (this.idTemplate == 0) {
        this.titleText = this._translateService.instant('Template.NewTemplate');
      } else {
        this.titleText = this._translateService.instant(
          'Template.ModifyTemplate'
        );
      }
    });
  }

  private _getCanCreateSpecialTemplate(idBusinessByCountry: number) {
    this._parameterService.getCanCreateSpecialTemplate(idBusinessByCountry).subscribe({
      next: (resp => {
        this.idSpecialTemplate = resp.result;
      })
    })
  }

  ngAfterViewInit(): void {
    this.quillEditor?.onEditorCreated.subscribe((editor: Quill) => {
      this.quill = editor;
    });
  }

  initForm() {
    this.form = this.fb.group({
      pkIIdTemplate: [0],
      vName: [null, [Validators.required]],
      vSender: [null,
        [
          Validators.required,
          Validators.pattern(
            /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],
      vEmailSubject: [null, [Validators.required]],
      vMaskEmail: [null,
        [
          Validators.pattern(
            /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],
      vCc: [null,
        [
          Validators.pattern(
            /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],
      vCco: [null,
        [
          Validators.pattern(
            /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],
      vMessage: [this.emailTemplate, Validators.required],
      dDateCreation: [new Date()],
      fkIIdProduct: [0],
      fkIIdStageByState: [0],
      fkIIdBusinessCountry: [0],
      bActive: [true],
      bMaskMail: [false],
      bCopy: [false],
      bHiddenCopy: [false],
      module: [],
      subModule: [],
      product: [],
      stage: [],
      status: [],
      bIsModule: ["0"],
      bIsSendDetail: [false],
      bIsSendInclusion: [false],
      bIsSendQuote: [false],
      bIsAllowFileAttachment: [false],
      bIsAllowComments: [false],
      bIsSendLogUser: [false],
      vMailSendInfo: [null],
      // new
      fkIdProcess: [],
      fkIIdProductModule: [],
      bIsSendTaskCreate: [false],
      bIsSendFileTask: [false],
      bSendToUserAssigned: [false],
      vMailRecipients: [null],
      vMailSendFieldInfo: [null],

    });
    this.form.get('bMaskMail')?.valueChanges.subscribe({
      next: (data) => {
        this.maskMail = data;
        data ? (
          this.form.get('vMaskEmail')?.setValidators(Validators.required),
          this.form.get('vMaskEmail')?.updateValueAndValidity()
        ) : (
          this.form.get('vMaskEmail')?.clearValidators(),
          this.form.get('vMaskEmail')?.updateValueAndValidity(),
          this.form.patchValue({ vMaskEmail: null })
        )
      },
    });

    this.form.get('bCopy')?.valueChanges.subscribe({
      next: (data) => {
        this.copy = data;
        data ? (
          this.form.get('vCc')?.setValidators(Validators.required),
          this.form.get('vCc')?.updateValueAndValidity()
        ) : (
          this.form.get('vCc')?.clearValidators(),
          this.form.get('vCc')?.updateValueAndValidity(),
          this.form.patchValue({ vCc: null })
        )
      },
    });

    this.form.get('bHiddenCopy')?.valueChanges.subscribe({
      next: (data) => {
        this.hiddenCopy = data;
        data ? (
          this.form.get('vCco')?.setValidators(Validators.required),
          this.form.get('vCco')?.updateValueAndValidity()
        ) : (
          this.form.get('vCco')?.clearValidators(),
          this.form.get('vCco')?.updateValueAndValidity(),
          this.form.patchValue({ vCco: null })
        )
      },
    });

    this.form.get('module')?.valueChanges.subscribe({
      next: (data) => {
        this.subModuleList = [];
        this.stageList = [];
        this.stageByStateList = [];
        this.clearModalField();
        this.getSubModules(data.pkIIdMenu)
      },
    });

    this.form.get('subModule')?.valueChanges.subscribe({
      next: (data) => {
        this.stageList = [];
        this.stageByStateList = [];
        this.clearModalField();
        this.getProductsByIdMenu(data.pkIIdMenu)
      },
    });

    this.form.get('stage')?.valueChanges.subscribe({
      next: (data) => {
        this.stageByStateList = [];
        this.clearModalField();
        this.getStageByState(data.pkIIdStage)
      },
    });

    this.form.get('status')?.valueChanges.subscribe({
      next: (data) => {
        this.clearModalField();
        this.form.patchValue({ fkIIdStageByState: data.pkIIdStageByState })
        this.getFieldsByStageByStateId(data.pkIIdStageByState)
      },
    });

    this.form.get('fkIIdProduct')?.valueChanges.subscribe({
      next: (data) => {
        if (data != null && data != 0) this.getFieldsByProductId(data);
      },
    });

    this.form.get('vMailRecipients')?.valueChanges.subscribe(() => {
      this.validateMailRecipients();
    });

    this.validateMailSendInfo();

  }

  validateMailSendInfo() {
    const control: any = this.form.get('vMailSendInfo');
    if (this.businessComponent?.vActualComponent === 'buttonsMarubeni') {
      control.setValidators([
        Validators.required,
        Validators.pattern(
          /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        )
      ]);
    } else {
      control.clearValidators();
    }
    control.updateValueAndValidity();
  }


  initFieldForm() {
    this.fieldForm = this.fb.group({
      idProducto: [[Validators.required]],
      idCampo: [[Validators.required]],
    });
  }

  validateEditOrNew() {
    this._activatedRoute.params.subscribe((e) => {
      if (e['id']) {
        this.idTemplate = e['id'];
        this.operationType = 'edit';
        this.titleText = this._translateService.instant(
          'Template.ModifyTemplate'
        );
        this.getTemplateById(this.idTemplate);
      } else {
        this.idTemplate = 0;
        this.operationType = 'create';
        this.titleText = this._translateService.instant('Template.NewTemplate');
      }
      this.form.patchValue({ fkIIdBusinessCountry: e['idBusinessCountry'] });
      this._getCanCreateSpecialTemplate(e['idBusinessCountry']);
      this.getModules(e['idBusinessCountry']);
      this.getAllProducts(e['idBusinessCountry']);

      this.form.get('fkIdProcess')?.valueChanges.subscribe({
        next: (fkIdProcess) => {
          this.form.get('fkIIdProductModule')?.setValue(null);
          if (fkIdProcess > 0) {
            this.getProductsByIdProcess(fkIdProcess, e['idBusinessCountry']);
          }
        },
      });
    });
  }

  ngOnDestroy(): void {
    this.fieldSubs?.unsubscribe();
  }

  openAddFieldDialog() {
    this.initFieldForm();
    this.isEmailModal = false;

    if (this.forModules) {
      this.getFieldListByProductModule(
        this.form.get('fkIIdProductModule')?.value,
        false
      );
    }

    const dialogRef = this._modalDialog.open(this.addFieldModal!, {
      disableClose: true,
      width: '50vw',
      maxHeight: '90vh'
    });
  }

  onFocus(input: any) {
    this.input = input;
  }

  radioForChange(value: number) {
    this.clearModalField();
    value > 0 ? (
      this.forModules = true
    ) : (
      this.forModules = false
    )
  }

  slideStatusChange(event: MatSlideToggleChange) {
    this.form.patchValue({ bActive: event.checked })
  }

  //metodos pra drag drop
  drop(event: any) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      this.insertContentInQuill(event.container.data[event.currentIndex]);
    }
  }

  insertContentInQuill(content: any) {
    if (this.input === 'vEmailSubject' || this.input === 'vMailSendInfo') {
      const inputValue = this.form.get(this.input)?.value;
      this.form.get(this.input)?.setValue(`${inputValue}, {{${content.pkIIdFieldModule || content.pkIIdField}, ${content.vNameField.trim()}}}`);
    }
    else if (this.isEmailModal) {
      this.input = 'vMailSendFieldInfo';
      const inputControl = this.form.get(this.input);

      if (inputControl) {
        const inputValue = inputControl.value?.trim() || '';

        if (inputValue.length === 0) { // si el campo esta vacio
          if (content.hasOwnProperty('pkIIdFieldModule')) {
            inputControl.setValue(`{{${content.pkIIdFieldModule}, ${content.vNameField.trim()}}}`);
          } else {
            inputControl.setValue(`{{${content.pkIIdField}, ${content.vNameField.trim()}}}`);
          }
        } else {
          if (content.hasOwnProperty('pkIIdFieldModule')) {
            inputControl.setValue(`${inputValue}, {{${content.pkIIdFieldModule}, ${content.vNameField.trim()}}}`);
          }else{
            inputControl.setValue(`${inputValue}, {{${content.pkIIdField}, ${content.vNameField.trim()}}}`);
          }

        }
      }
    }
    else {
      const html = `
        <span style="
          background-color: rgba(128,128,128,.5);
          cursor: move;
          display: flex;
          box-sizing: border-box;
        ">
          <b>
            {{${content.pkIIdFieldModule || content.pkIIdField}, ${content.vNameField.trim()}}}
          </b>
        </span> `;
      const range = this.quill.getSelection(true);
      const cursorPosition = this.quill.getSelection();
      this.quill.clipboard.dangerouslyPasteHTML(range.index, html, 'api');
      this.form.patchValue({ vMessage: this.quill.container.outerHTML })
      const endIndex = cursorPosition?.index ? cursorPosition.index + content.vNameField.length + 9 : content.vNameField.length + 9;
      const range1: Range = {
        index: cursorPosition?.index || 0,
        length: endIndex - (cursorPosition?.index || 0),
      };
      this.quill.setSelection(range1);
    }

    this.input = '';
    this._modalDialog.closeAll();
  }

  removeFieldsFormulas(formula: any): void {
    const index = this.campos.indexOf(formula);
    if (index >= 0) {
      this.campos.splice(index, 1);
    }
  }

  saveTemplateClick() {
    this.form.valid ?
      this.operationType === 'create' ? this.createTemplate() : this.updateTemplate()
      :
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm'),
      );
  }

  goBackClick() {
    this._customRouter.navigate(['dashboard/templates/']);
  }

  clearModalField() {
    this.fieldForm.get('idCampo')?.setValue(0);
    this.fieldList = [];
  }

  eventCloseModal($event: boolean) {
    this.fieldForm.get('idCampo')?.setValue(0);
  }

  //#region metodos que consumen apis
  //obtiene una plantilla especifica por id
  getTemplateById(idTemplate: number) {
    this._parameterService
      .getTemplateById(idTemplate)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');

        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.form.patchValue(resp.result);
            resp.result.fkIIdProduct === 0 ? (
              this.getFieldsByStageByStateId(resp.result.fkIIdStageByState),
              this.forModules = true
            ) :
              (
                this.getFieldsByProductId(resp.result.fkIIdProduct),
                this.forModules = false
              );
            resp.result.vMaskEmail ? (
              this.form.patchValue({ VMaskMail: resp.result.vMaskEmail }),
              this.form.patchValue({ bMaskMail: true })
            ) : this.form.patchValue({ bMaskMail: false })
            resp.result.vCc ? this.form.patchValue({ bCopy: true }) : this.form.patchValue({ bCopy: false })
            resp.result.vCco ? this.form.patchValue({ bHiddenCopy: true }) : this.form.patchValue({ bHiddenCopy: false })
          }
        }
      });
  }
  //llena el dropDwon de modulos
  getModules(idBusinessByCountry: number) {
    this._moduleSettingsService
      .getAllParentMenuFromModules(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.moduleList = resp.result;
          }
        }
      });
  }
  //llena el dropDwon de sub modulos
  getSubModules(idMenu: number) {
    this.stageList = [];
    this._moduleSettingsService
      .getAllChildrenMenuFromModules(idMenu)
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('no hay sub modulos asociados');
          this.moduleSelectedHasChildren = false,
            this.form.get('subModule')?.clearValidators(),
            this.form.get('subModule')?.updateValueAndValidity(),
            this.getProductsByIdMenu(idMenu);
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.form.get('subModule')?.setValidators(Validators.required),
              this.form.get('subModule')?.updateValueAndValidity(),
              this.moduleSelectedHasChildren = true,
              this.subModuleList = resp.result
          }
        }
      });
  }
  //llena dropdown de productospor un id menu o submenu, validar bien al respecto
  getProductsByIdMenu(idMenu: number) {
    this._roleService
      .getProductByIdMenu(idMenu)
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('no hay productos asociados');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            for (let item of resp.result) {
              this.getStages(item.idProductModule);
            }
          }
        }
      });
  }
  //llena el dropDwon de sub etapas
  getStages(idProductModule: number) {
    this._moduleService.getStageByIdProductModule(idProductModule)
      .pipe(
        catchError((error) => {
          console.log('No existen etapas asociadas')
          this.stageList = [];
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.stageList = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            if (this.stageList.length == 0) {
              this.stageList = resp.result
            } else {
              for (let item of resp.result) {
                this.stageList.push(item)
              }
            }
          }
        }
      });

  }
  //llena el dropDwon de estados
  getStageByState(idStage: number) {
    this._moduleService
      .getStageByStateById(idStage)
      .pipe(
        catchError((error) => {
          console.log('No existen estados asociados a esta etapa.');
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.stageByStateList = resp.result;
          }
        }
      });
  }
  //obtiene todos los productos para un id business country
  getAllProducts(idBusinessByCountry: number) {

    this._productService
      .getAllProductByBusiness(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.allProductsList = resp.result;
          }
        }
      });
  }
  //obtiene los campos asignados a un producto
  getFieldsByProductId(idProduct: Number) {
    this.fieldSubs = this._fieldSvc.getFieldsByProductId(idProduct)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      ).subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log("El tipo de datos devueltos es un array vacío.");
          this.fieldList = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message)
          }
          else {
            this.fieldList = resp.result;
          }
        }
      });
  }
  //obtine los campos asignado a una etapa/estado
  getFieldsByStageByStateId(idState: number) {
    this._moduleService
      .getFormModuleByIdState([idState])
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.fieldList = [];
        } else {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + response.message
            );
            this.fieldList = [];
          } else {
            this._moduleService.getFieldsByForm(response.result.pkIIdFormModule)
              .pipe(
                catchError((error) => {
                  this._messageService.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    error.error.message
                  );
                  return of([]);
                })
              ).subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                  console.log("El tipo de datos devueltos es un array vacío.");
                  this.fieldList = [];
                } else {
                  if (resp.error) {
                    this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message)
                  }
                  else {
                    this.fieldList = resp.result;
                  }
                }
              });
          }
        }
      });
  }
  //obtiene los campos asignados a una etapa
  getListFormModuleByIdStage(idStage: number) {
    this._moduleService.getListFormModuleByIdStage(idStage)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.fieldList = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.fieldList = resp.result;
          }
        }
      });
  }
  //Crea una nueva plantilla
  createTemplate() {
    this._parameterService
      .createTemplate(this.form.value)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('Saved'),
              resp.message
            );
            this._customRouter.navigate(['dashboard/templates/']);
          }
        }
      });
  }
  //Actualiza una plantilla
  updateTemplate() {
    this._parameterService
      .updateTemplate(this.form.value)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('Saved'),
              resp.message
            );
            this._customRouter.navigate(['dashboard/templates/']);
          }
        }
      });
  }
  //#endregion


  getBusinessComponentByIdBusinessCountry() {
    this._businessService.getBusinessComponentByIdBusinessCountry()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (!response.error) {
            this.businessComponent = response.result;
          }
        }
      });
  }

  // new
  //Obtiene todos los procesos regsitrados en el sistema.
  getAllProcess() {
    this._parameterService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.processes = resp.result;
        }
      });
  }

  getProductsByIdProcess(idProcess: number, idBusinessCountry: number) {
    this._roleService
      .getProductsByIdProcess(idProcess, idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.form.get('fkIIdProductModule')?.disable();
          this._messageService.messageInfo(
            'Advertencia',
            'No se encontró ningun porducto para este proceso'
          );

        } else {
          if (resp.result.length > 0) {
            this.form.get('fkIIdProductModule')?.enable();
            this.productsApiRole = resp.result;
            // this.productsApiProduct = [];
          }
          if (this.hasZeroValue(resp.result)) {
            if (resp.result[0].pkIIdProductModule) {
              if (resp.result.length > 1) {
                this.productsApiRole.forEach(
                  (product: TaskTrayConfigProductApiRoleModel) => {
                    product.vName = `No Aplica - ${product.pkIIdProductModule}`;
                  }
                );
              } else {
                this.productsApiRole[0].vName = `No Aplica - ${resp.result[0].pkIIdProductModule}`;
                this.form
                  .get('fkIIdProductModule')
                  ?.setValue(this.productsApiRole[0].pkIIdProductModule);
                this.form.get('fkIIdProductModule')?.disable();
              }
            }
          }
        }
      });
  }

  // Función para verificar si algún producto tiene valor 0 en "fkIIdProduct"
  hasZeroValue(array: TaskTrayConfigProductApiRoleModel[]): boolean {
    return array.some(
      (product: TaskTrayConfigProductApiRoleModel) => product.fkIIdProduct === 0
    );
  }

  //Obtine la lista de campos disponibles para un elemento de la bandeja de tarea, asociados por id de producto.
  getFieldListByProductModule(idProductModule: number, isQuote: boolean, isEmail: boolean = false) {
    this._moduleService
      .getFieldListByProductModule(idProductModule, isQuote, isEmail)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.fieldList = resp.result;
        }
      });
  }

  openAddFieldEmailDialog() {
    this.isEmailModal = true;
    this.initFieldForm();

    this.getFieldListByProductModule(
      this.form.get('fkIIdProductModule')?.value,
      false,
      true
    );

    const dialogRef = this._modalDialog.open(this.addFieldModal!, {
      disableClose: true,
      width: '50vw',
      maxHeight: '90vh'
    });
  }

  getTitleForModal(): string {
    return this.isEmailModal ? this._translateService.instant('Template.AddFieldTitle') : this._translateService.instant('Template.AddFieldToMessage');
  }

  // // Método para validar múltiples correos electrónicos
  validateMailRecipients() {
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const value = this.form.get('vMailRecipients')?.value;

    if (!value) {
      this.form.get('vMailRecipients')?.setErrors(null);  // Si el campo está vacío, no hay error
      return;
    }

    // Dividimos el valor por comas y eliminamos los espacios antes y después de cada correo
    const emails: string[] = value.split(',').map((email: string) => email.trim());

    // Validamos cada correo electrónico con la expresión regular
    const invalidEmails = emails.filter((email: string) => !emailPattern.test(email));

    // Si hay correos inválidos, establecemos un error en el campo
    if (invalidEmails.length > 0) {
      this.form.get('vMailRecipients')?.setErrors({ invalidEmails: true }); // Set error if invalid
    } else {
      this.form.get('vMailRecipients')?.setErrors(null); // No errors
    }
  }


}
