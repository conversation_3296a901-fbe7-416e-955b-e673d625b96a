import { Routes } from '@angular/router';
import { MyQuotationComponent } from './my-quotation.component';
import { AllMyQuotationComponent } from './all-my-quotation/all-my-quotation.component';
export default [
  {
    path: '',
    component: MyQuotationComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-my-quotation/all-my-quotation.component').then(
            (c) => c.AllMyQuotationComponent
          ),
      },
      {
        path: 'lis',
        loadComponent: () =>
          import('./all-my-quotation/all-my-quotation.component').then(
            (c) => c.AllMyQuotationComponent
          ),
      },
      {
        path: 'modify/:id',
        loadComponent: () =>
          import('./see-my-quotation/see-my-quotation.component').then(
            (c) => c.SeeMyQuotationComponent
          ),
      },
    ],
  },
] as Routes;