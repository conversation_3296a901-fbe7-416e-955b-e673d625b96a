import { Component, TemplateRef, ViewChild, OnDestroy, OnInit, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { TermsConditionsComponent } from './Terms-Conditions/terms-conditions/terms-conditions.component';


@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [
    CommonModule,
    ModalComponent,
    TermsConditionsComponent,
    RouterModule
   ],
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent implements OnInit, OnDestroy {

//data que se envia al modal de terminos y condiciones
dataTermsConditions= 0

  @ViewChild('TermConditions') TermConditions?: TemplateRef<any>;

  constructor(
    public Dialog: MatDialog,
    
  ){} 

  OpenTermsConditions(){
    const dia = new MatDialogConfig(); 
    this.Dialog.open(this.TermConditions!, {
      width: '55vw',
      height: '1vm',
      panelClass:'no-scroll',
    });

   
  }

  get currentYear(): string {
    const date = new Date();
    return date.getFullYear().toString();
  }
  
  ngOnDestroy(): void {
    
  }
  ngOnInit(): void {}
}
