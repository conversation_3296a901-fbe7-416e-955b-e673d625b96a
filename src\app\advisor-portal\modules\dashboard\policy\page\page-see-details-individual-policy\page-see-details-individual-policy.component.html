<!-- Dettale de la póliza -->
<div class="mt-3">
  <app-details-policy [detailsPolicyData]="detailsPolicyData" [idWtw]="idWtw" [policyNumber]="policyNumber"
  [policyStatus]="policyStatus" [idPolicyType]="idPolicyType" [fieldKeyName]="fieldKeyName"
  [fieldKeyValue]="fieldKeyValue" [isRisk]="isRisk" [motive]="motive"></app-details-policy>
  </div>

  <!-- Botones Modificar y Renovar -->
  <div class="mt-3 mb-5">
    <button class="btn-custom" type="button" mat-raised-button (click)="openModal('selectModificationModal')" [disabled]="isDisabledModifyButton">
        <strong *ngIf="!isRisk" >{{ "Modify" | translate }}</strong>
        <strong *ngIf="isRisk">{{ "Policy.Modification" | translate }}</strong>
    </button>
    <button *ngIf="!isRisk" class="btn-custom mx-3" type="button" mat-raised-button [disabled]="isDisabledRenewalButton"(click)="goToRenewal()">
        <strong>{{ "Policy.Renew" | translate }}</strong>
    </button>
  </div>

  <!-- Tabla Beneficiarios -->
  <div class="mt-5">
    <app-beneficiaries-table [estructTable]="estructTableBeneficiaries"
        [beneficiariesData]="beneficiariesData" (actionTable)="getActionTableBeneficiaries($event, true)"></app-beneficiaries-table>
  </div>

  <!-- Carátula de póliza PDF -->
  <div class="cont-pdf-viewer">
    <div class="mt-5">
        <h4 class="bold">{{ "Policy.PolicyCover" | translate }}</h4>
    </div>
    <mat-card class="caratula mt-5">
        <div class="row">
            <div class="col-md-10">
                <mat-card-content>
                    <pdf-viewer [src]="pdfSrc" [render-text]="true" [original-size]="true"
                        style="width: 100%; height: 800px"></pdf-viewer>
                </mat-card-content>
            </div>
            <div class="col-md-2">
                <mat-card-actions>
                    <!-- Descargar PDF -->
                    <button mat-raised-button type="button" color="primary" (click)="downloadPDF()">
                        <mat-icon iconPositionEnd fontIcon="arrow_forward"></mat-icon>
                        {{ "Policy.DownloadPDF" | translate }}
                    </button>
                </mat-card-actions>
            </div>
        </div>
    </mat-card>
  </div>

  <!-- Tabla Histórico de modificaciones de riesgos -->
  <div class="mt-5">
    <app-history-table [estructTable]="estructTableModificationHistoryRisk" [historyData]="modificationHistoryRiskData"
        [title]="titleModificationHistoryRisk"
        (actionTable)="getActionTableModificationHistoryRisk($event)"></app-history-table>
  </div>

  <!-- Tabla Historico de movimientos de póliza-->
  <div class="mt-5" *ngIf="!isRisk">
    <app-history-table [estructTable]="estructTablePolicyMovementHistory" [historyData]="policyMovementHistoryData"
        [title]="titlePolicyMovementHistory"
        (actionTable)="getActionTableModificationHistory($event)"></app-history-table>
  </div>

  <!-- Botón Regresar -->
  <div class="d-flex justify-content-center mt-3">
    <button mat-raised-button type="button" (click)="goBack()">
        <mat-icon fontIcon="arrow_back"></mat-icon>
        {{ "Back" | translate }}
    </button>
  </div>

  <!-- Modal ver detalles historico de riesgos -->
  <ng-template #viewHistoricalRiskDetails>
    <app-modal2 [titleModal]="modalTitleRiskModificationHistory| translate"
        [showCancelButtonBelow]="!isReadOnlyBeneficiaries">
        <ng-container body>
            <!-- Caso cuándo no es un riesgo y sea editable -->
            <ng-container *ngIf="!isRisk">
                <app-details-risk-modifications [dataForm]="dataForm" [idPolicy]="idWtw" [idParent]="idParent"
                    [customerType]="customerType" [isReadOnly]="isReadOnlyBeneficiaries"
                    [idHistoryPolicy]="idHistoryPolicy" [isHistory]="isHistory"
                    (formValue)="getFormValue($event)"></app-details-risk-modifications>
            </ng-container>
            <!-- Caso cuándo se muestre un riesgo -->
            <ng-container *ngIf="isRisk">
                <app-details-risk-modifications [dataForm]="dataForm" [idPolicy]="idWtw" [idParent]="idWtw"
                    [customerType]="customerType" [isReadOnly]="isReadOnlyBeneficiaries"
                    (formValue)="getFormValue($event)"></app-details-risk-modifications>
            </ng-container>
            <!-- Caso cuándo se modifiquen las primas de un beneficiario -->
            <ng-container *ngIf="modifyPremiumValue || modifiedPremium">
                <div class="col-12 mt-3 mb-2">
                    <!-- Ajuste de prima -->
                      <h3 class="bold pad-field-gen">{{'Policy.PremiumAdjustment' | translate}}</h3>
                      <div class="row">
                          <!-- Valor asegurado -->
                          <div class="col-md-6 col-sm-12 mb-2">
                              <mat-form-field appearance="outline" class="w-100 pad-field-gen">
                                  <mat-label>
                                      {{ "Policy.InsuredValue" | translate }}
                                  </mat-label>
                                  <input matInput PreventionSqlInjector [(ngModel)]="valueInsurance"
                                      [disabled]="isReadOnlyBeneficiaries" (input)="formatMoney($event, false)" />
                              </mat-form-field>
                          </div>
                          <!-- Prima -->
                          <div class="col-md-6 col-sm-12 mb-2">
                              <mat-form-field appearance="outline" class="w-100 pad-field-gen">
                                  <mat-label>
                                      {{ "iPremium" | translate }}
                                  </mat-label>
                                  <input matInput PreventionSqlInjector [(ngModel)]="premium"
                                      [disabled]="isReadOnlyBeneficiaries" (input)="formatMoney($event, false)" />
                              </mat-form-field>
                          </div>
                      </div>
                </div>
            </ng-container>
        </ng-container>

        <!-- Botón Cerrar -->
        <ng-container customButtonRight>
            <button *ngIf="isReadOnlyBeneficiaries" class="w-auto" type="button" mat-raised-button color="primary"
                (click)="closeModalViewHistoricalRiskDetails()">
                {{ "Close" | translate }}
            </button>
            <button *ngIf="!isReadOnlyBeneficiaries" class="w-auto" type="button" mat-raised-button color="primary"
                [disabled]="!formValid" (click)="createAndUpdateBeneficiesByIdPolicy()">
                {{ "Save" | translate }}
            </button>
        </ng-container>
    </app-modal2>
  </ng-template>

  <!-- Modal ver detalles de beneficiarios -->
  <ng-template #viewBeneficiariesModal>
    <app-modal2 [titleModal]="'Policy.Beneficiaries' | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <!-- Tabla Beneficiarios -->
            <app-beneficiaries-table [estructTable]="estructTableBeneficiaries" [showDetailsColumn]="true"
                [beneficiariesData]="beneficiariesDataHistory"
                (actionTable)="getActionTableBeneficiaries($event)"></app-beneficiaries-table>
        </ng-container>

        <!-- Botón Cerrar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary"
                (click)="closeModalviewBeneficiaries()">
                {{ "Close" | translate }}
            </button>
        </ng-container>
    </app-modal2>
  </ng-template>

  <!-- Modal ver historico de movimiento de pólizas -->
  <ng-template #viewHistoryMovementModal>
    <app-modal2 [titleModal]="typeMovement | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <app-page-see-detail-movement-history-individual-policy [idHistory]="idHistoryPolicy"
                [typeMovement]="typeMovement"></app-page-see-detail-movement-history-individual-policy>
        </ng-container>

        <!-- Botón Cerrar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary"
                (click)="closeModalviewHistoryMovement()">
                {{ "Close" | translate }}
            </button>
        </ng-container>
    </app-modal2>
  </ng-template>

  <!-- Modal seleccionar  tipo de modificación de póliza-->
  <ng-template #selectModificationModal>
    <app-modal2 [titleModal]="modificationTitle | translate">
        <ng-container body>
            <!-- Modificar el valor de la prima -->
            <div class="row">
                <mat-slide-toggle class="mb-3" (change)="onToggleChangePremium($event)">
                    {{ "Policy.ModifyPremium" | translate }}
                </mat-slide-toggle>
            </div>
            <div class="row">
                <!-- Modificar -->
                <div class="col-12">
                    <mat-form-field id="iMeasuringFrom" class="w-100">
                        <mat-label>
                            {{ "Modify"| translate }}
                        </mat-label>
                        <mat-select (selectionChange)="changesModificationType($event)">
                            <mat-option *ngFor="let item of modificationOptions" [value]="item.name">{{ item.name
                                }}</mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
        </ng-container>

        <!-- Botón Modificar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary" [disabled]="!modificationType"
                (click)="openModal('modifyPolicyModal')">
                {{ "Modify" | translate }}
            </button>
        </ng-container>
    </app-modal2>
  </ng-template>

  <!-- Modal para modificar una póliza -->
  <ng-template #modifyPolicyModal>
    <app-modal2 [titleModal]="modificationTitleCustom | translate">
        <ng-container body>
            <app-details-risk-modifications [dataForm]="dataForm" [idPolicy]="idWtw" [idParent]="idParent"
                [customerType]="modificationType" (formValue)="getFormValue($event)"></app-details-risk-modifications>
            <ng-container *ngIf="modifyPremiumValue">
                <div class="col-12 mt-3 mb-2">
                    <!-- Ajuste de prima -->
                      <h3 class="bold pad-field-gen">{{'Policy.PremiumAdjustment' | translate}}</h3>
                      <div class="row">
                          <!-- Valor asegurado -->
                          <div class="col-md-6 col-sm-12 mb-2">
                              <mat-form-field appearance="outline" class="w-100 pad-field-gen">
                                  <mat-label>
                                      {{ "Policy.InsuredValue" | translate }}
                                  </mat-label>
                                  <input matInput [(ngModel)]="valueInsurance" (input)="formatMoney($event, false)" />
                              </mat-form-field>
                          </div>
                          <!-- Prima -->
                          <div class="col-md-6 col-sm-12 mb-2">
                              <mat-form-field appearance="outline" class="w-100 pad-field-gen">
                                  <mat-label>
                                      {{ "iPremium" | translate }}
                                  </mat-label>
                                  <input matInput [(ngModel)]="premium" (input)="formatMoney($event, false)" />
                              </mat-form-field>
                          </div>
                      </div>
                </div>
            </ng-container>
        </ng-container>

        <!-- Botón Modificar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary" [disabled]="!formValid"
                (click)="modifyPolicy()">
                {{ "Save" | translate }}
            </button>
        </ng-container>
    </app-modal2>
  </ng-template>

  <!-- Modal para listar los beneficiarios disponibles para Modificar -->
  <ng-template #viewBeneficiariesToEditModal>
    <app-modal2 [titleModal]=" 'Modify' | translate" (closeModal)="closeModalBeneficiaries()" [showCancelButtonBelow]="false" [showCancelButton]="false">
        <ng-container body>
            <!-- Tabla Beneficiarios -->
            <app-beneficiaries-table [estructTable]="estructTableBeneficiariesModify" [showDetailsColumn]="true"
                [beneficiariesData]="beneficiariesDataBpk"
                (actionTable)="getActionTableBeneficiaries($event)"></app-beneficiaries-table>
            <!-- Agregar beneficiario -->
            <button class="w-auto" type="button" mat-raised-button color="primary" (click)="addBeneficiaries()">
                {{ "Policy.AddBeneficiary" | translate }}
                <mat-icon fontIcon="add"></mat-icon>
            </button>
        </ng-container>
        <!-- Guardar Beneficiarios -->
        <ng-container customButtonRight class="flex-gap">
            <button class="w-auto" type="button" mat-raised-button (click)="closeModalBeneficiaries()">
              {{ 'Cancel' | translate }}
            </button>

            <button class="w-auto ml-1" type="button" mat-raised-button color="primary" (click)="saveAllBeneficiaries()">
                {{ "Save" | translate }}
            </button>
        </ng-container>
    </app-modal2>
  </ng-template>
