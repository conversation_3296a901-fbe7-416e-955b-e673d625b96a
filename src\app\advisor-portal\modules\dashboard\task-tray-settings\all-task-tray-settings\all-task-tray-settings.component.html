<app-choose-country-and-company></app-choose-country-and-company>
<div class="row mb-2 mt-2">
  <h4 class="col-md-12">{{ "TaskTraySettings.Subtitle" | translate }}</h4>
</div>
<form [formGroup]="formFilter">
  <div class="row mt-3 mb-2">
    <div class="col-md-4 col-sm-12">
      <mat-form-field class="w-100 mb-2" appearance="fill">
        <mat-label>
          {{ "TaskTraySettings.FormFilter.LabelProcess" | translate }}
        </mat-label>
        <mat-select formControlName="fkIdProcess">
          <mat-option
            *ngFor="let process of processes"
            [value]="process.pkIIdProcessFamily"
          >
            {{ process.vNameProcess }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div class="col-md-4 col-sm-12" *ngIf="productsApiRole.length > 0">
      <mat-form-field class="w-100 mb-2" appearance="fill">
        <mat-label>
          {{ "TaskTraySettings.FormFilter.LabelProduct" | translate }}
        </mat-label>
        <mat-select formControlName="fkIdProduct">
          <mat-option
            *ngFor="let product of productsApiRole"
            [value]="product.pkIIdProductModule"
          >
            {{ product.vName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="col-md-4 col-sm-12" *ngIf="productsApiProduct.length > 0">
      <mat-form-field class="w-100 mb-2" appearance="fill">
        <mat-label>
          {{ "TaskTraySettings.FormFilter.LabelProduct" | translate }}
        </mat-label>
        <mat-select formControlName="fkIdProduct">
          <mat-option
            *ngFor="let product of productsApiProduct"
            [value]="product.pkIIdProduct"
          >
            {{ product.vProductName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
</form>

<div>
  <app-table
    [displayedColumns]="estructTaskTraySettingsTable"
    [data]="taskTraySettingDataTable"
    (iconClick)="controller($event)"
  ></app-table>
</div>

<div class="cont-btn-create">
  <button
    class="mt-2 w-20"
    type="button"
    (click)="openModalCreateEditElement()"
    mat-raised-button
    color="primary"
    [disabled]="!validformFilter"
  >
    {{ "TaskTraySettings.FormFilter.LabelButton" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
</div>

<!-- modal editar-crear submodulos -->
<ng-template #createEditElementModal>
  <app-modal2 [titleModal]="titelModal" (closeModal)="closeModalEvent($event)">
    <ng-container body>
      <form [formGroup]="formCreateEditElement">
        <div class="row">
          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{
                  "TaskTraySettings.FormCreateEditElement.LabelField"
                    | translate
                }}
              </mat-label>
              <mat-select formControlName="fkIIdField">
                <mat-option
                  *ngFor="let field of fields"
                  [value]="field.pkIIdField"
                >
                  {{ field.vNameField }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    formCreateEditElement,
                    'fkIIdField',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{
                  "TaskTraySettings.FormCreateEditElement.LabelOrder"
                    | translate
                }}
              </mat-label>
              <mat-select formControlName="iOrder">
                <mat-option *ngFor="let order of orderList" [value]="order">
                  {{ order }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    formCreateEditElement,
                    'iOrder',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="cont-slide">
            <div class="mx-3">
              <mat-slide-toggle class="mb-3" formControlName="bActive">
                {{
                  "TaskTraySettings.FormCreateEditElement.LabelActive"
                    | translate
                }}
              </mat-slide-toggle>
            </div>

            <div class="mx-3">
              <mat-slide-toggle class="mb-3" formControlName="bIsFilter">
                {{
                  "TaskTraySettings.FormCreateEditElement.bIsFilter" | translate
                }}
              </mat-slide-toggle>
            </div>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="complete()"
        [disabled]="!validformCreateEditElement"
      >
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "Save" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
