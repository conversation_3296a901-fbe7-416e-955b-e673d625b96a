import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {} from '@angular/common/http';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { MatCardModule } from '@angular/material/card';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { ClientService } from 'src/app/shared/services/client/client.service';
import { catchError, of, Subscription } from 'rxjs';
import { CustomerProduct } from 'src/app/shared/models/client';
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { UserService } from 'src/app/shared/services/user/user.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { MatDialog } from '@angular/material/dialog';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { ModalSendPdfComponent } from '../modal-send-pdf/modal-send-pdf.component';
import { SeeDocumentsFieldQuotePolicyComponent } from 'src/app/shared/components/see-documents-field-quote-policy/see-documents-field-quote-policy.component';

@Component({
  standalone: true,
  selector: 'app-see-details-product',
  templateUrl: './see-details-product.component.html',
  styleUrls: ['./see-details-product.component.scss'],
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    RouterModule,
    MatCardModule,
    PdfViewerModule,
    SeeDocumentsFieldQuotePolicyComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SeeDetailsProductComponent implements OnInit, OnDestroy {
  @Input() attachment = '';
  idCliente: string = '';
  idProduct: string = '';
  pdfSrc = '/assets/pdfs/PdfPruebaVisualizacion.pdf';
  @Output() sendSuccess = new EventEmitter<void>();

  lenguaje?: Subscription;
  detailProduct?: any;
  email: string | null = null;
  emailUserLogged: string | null = null;
  nameCompleted: string | null = null;
  nameCompletedUserLogged: string | null = null;
  phoneCompletedUserLogged: string | null = null;
  nameCompletedBussines: string | null = null;
  countryName: string | null = null;
  data?: CustomerProduct | null;
  form: FormGroup = new FormGroup({});
  richText: string = '';
  userIdSession: number = 0;
  emailSubs?: Subscription;
  profile: string = '';
  loggedUserName: string | null = null;
  constructor(
    private _router: Router,
    private translate: TranslateService,
    private _activatedRoute: ActivatedRoute,
    private _msgSvc: MessageService,
    private _clientService: ClientService,
    private _fb: FormBuilder,
    private _userSvc: UserService,
    private _settingService: SettingService,
    private _businessService: BusinessService,
    private _userService: UserService,
    public dialog: MatDialog,
    private _customRouter: CustomRouterService
  ) {}

  ngOnInit() {
    // Subscribe to route parameters to get idClient and idProduct
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idClient) {
        this.idCliente = params.idClient;
      }
      if (params.idProduct) {
        this.idProduct = params.idProduct;
      }

      // Send a request if both idClient and idProduct are present
      if (this.idCliente && this.idProduct) {
        this._clientService.sendIsClientTrue({
          idClient: parseInt(this.idCliente, 10) || 0,
          isClientDetailProduyct: true,
          isDetailClient: false,
        });
      }
    });

    // Retrieve data detail product from the client service
    this.data = this._clientService.getDataDetailProduct();

    this.email = this.data?.email ?? null;
    this.nameCompleted = this.data?.customerName ?? null;

    // Navigate to a different route if data is not available
    if (!this.data) {
      this._customRouter.navigate([`dashboard/clients/detail/${this.idCliente}/`]);
    }

    this.getDataSettingInit();
  }

  /**
   * Fetches initial data settings and sets the country name.
   * Calls getBusinessByCountry with the business ID from the data settings.
   */
  async getDataSettingInit() {
    let data: any = await this._settingService.getDataSettingInit();
    this.countryName = data.countryName;
    this.getBusinessByCountry(data.idBusiness);
  }

  /**
   * Retrieves business details by business ID and sets the business name.
   * Calls getUserId with the business country ID from the response.
   *
   * @param idBusiness - The ID of the business to retrieve details for.
   */
  getBusinessByCountry(idBusiness: number) {
    this._businessService.getBusinessCountryByBusinessId(idBusiness).subscribe({
      next: (response) => {
        this.nameCompletedBussines = response.result[0]?.vBusinessName ?? null;
        this.getUserId(response.result[0].pkIIdBusinessByCountry);
      },
    });
  }

  /**
   * Retrieves the user ID from session data and sets it.
   * Calls getUserPersonalList with the business country ID if user ID is set.
   *
   * @param idbusinessCountry - The ID of the business country to retrieve user list for.
   */
  async getUserId(idbusinessCountry: number) {
    let userIdSession = await this._settingService.getDataSettingInit();
    if (userIdSession) {
      this.userIdSession = Number(userIdSession.idUser);
    }
    if (this.userIdSession) {
      this.getUserPersonalList(idbusinessCountry);
    }
  }

  /**
   * Retrieves the personal list of the user by user ID and business country ID.
   * Sets the email, name, phone, and profile of the logged-in user from the response.
   *
   * @param idbusinessCountry - The ID of the business country to retrieve user list for.
   */
  getUserPersonalList(idbusinessCountry: number) {
    this._userService
      .getUserPersonalList(this.userIdSession, idbusinessCountry)
      .subscribe({
        next: (response) => {
          if (!response.error) {
            this.emailUserLogged = response.result.vEmailUser;
            this.nameCompletedUserLogged = response.result.vPersonName;
            this.phoneCompletedUserLogged = response.result.vPhone;
            this.profile = response.result.vPositionName;
          }
        },
      });
  }

  ngOnDestroy() {
    // Unsubscribe from observable to avoid memory leaks
    this.lenguaje?.unsubscribe();

    // Send a request to update the client service state on component destruction
    this._clientService.sendIsClientTrue({
      idClient: 0,
      isClientDetailProduyct: false,
      isDetailClient: false,
    });
  }

  /**
   * Navigates back to the client detail view.
   */
  back(): void {
    // Navigate to the client detail view using the stored client ID
    this._router
      .navigate(['/dashboard/clients/detail', this.idCliente])
      .catch((error) => {
        // Handle navigation errors (e.g., invalid routes or navigation issues)
        console.error('Navigation error:', error);
      });
  }

  /**
   * Initiates the download of a PDF file from the specified source.
   */
  descargarPDF(): void {
    const link = document.createElement('a');

    link.href = this.pdfSrc;

    // Set the download attribute to the file name extracted from the URL
    link.download = this.pdfSrc.split('/').pop() ?? '';

    link.target = '_blank';

    document.body.appendChild(link);

    link.click();

    // Remove the link from the document body after the download
    document.body.removeChild(link);
  }

  /**
   * Initializes the form with validators for email fields, required fields, and other form controls.
   */
  initForm(): void {
    this.form = this._fb.group({
      // Email address of the recipient
      to: [
        '',
        [
          Validators.required,
          Validators.email,
          Validators.pattern(
            /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],

      // Email address of the sender
      from: [
        '',
        [
          Validators.required,
          Validators.email,
          Validators.pattern(
            /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],

      // Optional mask for additional information or formatting
      mask: [''],

      // Carbon Copy (CC) recipients
      cc: [''],

      // Blind Carbon Copy (BCC) recipients
      cco: [''],

      // Subject of the email
      subject: ['', [Validators.required]],

      // Email message content, including rich text support
      message: [this.richText, [Validators.required]],

      // Optional file attachment
      attachment: [null],

      // Indicates whether the body of the email is HTML
      isBodyHtml: [true],
    });
  }

  /**
   * Sends an email with a PDF attachment.
   * Converts the PDF to Base64, sets form values, and sends the email if the form is valid.
   */
  async sendPDF(): Promise<void> {
    if (await this.assembleEmailMessage()) {
      this.openFilterDialog();
    }
  }

  /**
   * Opens the filter dialog for sending the PDF email.
   */
  private openFilterDialog(): void {
    const dialogRef = this.dialog.open(ModalSendPdfComponent, {
      width: '800px',
      data: {
        isSend: true,
        email: this.email
      },
    });

    dialogRef.afterClosed().subscribe(
      (result: any) => {
        if (result) {
          this.handleDialogResult();
        }
      },
      (error) => {
        console.error('Error opening the filter dialog:', error);
      }
    );
  }

  /**
   * Handles the result of the filter dialog.
   */
  private handleDialogResult(): void {
    this.convertToBase64(this.pdfSrc, (base64) => {
      if (this.form.valid) {
        this.prepareAndSendEmail(base64);
      }
    });
  }

  /**
   * Prepares the email and sends it.
   * @param base64 The Base64 string of the PDF.
   */
  private prepareAndSendEmail(base64: string): void {
    const emailValue = this.email;
    const toObject = [{ email: emailValue }];
    const attachmentObject = [
      {
        attachmentIsBase64String: true,
        attachment: base64.split(',')[1], // Extract the Base64 string of the PDF
        attachmentName: 'Cotización.pdf',
      },
    ];

    this.form.get('to')?.setValue(toObject);
    this.form.get('attachment')?.setValue(attachmentObject);

    this.emailSubs = this._userSvc
      .sendEmail({
        to: [{ email: emailValue }],
        attachment: attachmentObject,
        isBodyHtml: true,
        subject: 'prueba',
        message: this.richText,
      })
      .pipe(
        catchError((error) => {
          this.form.get('to')?.setValue(toObject);
          this._msgSvc.messageWaring(
            this.translate.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        this.handleEmailResponse(resp);
      });
  }

  /**
   * Handles the response from the email sending service.
   * @param resp The response from the email sending service.
   */
  private handleEmailResponse(resp: ResponseGlobalModel | never[]): void {
    if (Array.isArray(resp)) {
      // Handle array response if needed
    } else {
      if (resp.error) {
        this._msgSvc.messageError(
          this.translate.instant('ThereWasAError') + resp.message
        );
      } else {
        this.showConfirmationDialog();
        this.sendSuccess.emit();
      }
    }
  }

  /**
   * Shows the confirmation dialog after a successful email send.
   */
  private showConfirmationDialog(): void {
    const dialogRefD = this.dialog.open(ModalSendPdfComponent, {
      width: '800px',
      data: {
        isConfirmation: true,
        email: this.email
      },
    });

    dialogRefD.afterClosed().subscribe(
      (result: any) => {},
      (error) => {
        console.error('Error opening the confirmation dialog:', error);
      }
    );
  }

  /**
   * Converts a file from a URL to a Base64 string.
   * @param url - The URL of the file to be converted.
   * @param callback - The callback function to handle the Base64 string result.
   */
  convertToBase64(url: string, callback: (base64: string) => void): void {
    const xhr = new XMLHttpRequest();

    // Set up the event handler for when the request completes
    xhr.onload = function () {
      if (xhr.status === 200) {
        // Check if the request was successful
        const reader = new FileReader();

        // Set up the event handler for when the FileReader finishes reading the data
        reader.onloadend = function () {
          // Call the callback function with the Base64 string
          callback(reader.result as string);
        };

        // Read the response as a data URL (Base64)
        reader.readAsDataURL(xhr.response);
      } else {
        console.error('Failed to load file. Status:', xhr.status);
        // Optionally handle the error case here, e.g., by calling the callback with an error message
      }
    };

    // Set up the event handler for when the request fails
    xhr.onerror = function () {
      console.error('An error occurred while making the request.');
      // Optionally handle the error case here
    };

    // Open and send the request
    xhr.open('GET', url);
    xhr.responseType = 'blob'; // Expect a binary response
    xhr.send();
  }

  /**
   * Assembles the email message asynchronously.
   *
   * This method checks if the email and name fields are completed and
   * constructs the email message. It displays error messages if required
   * fields are missing.
   *
   * @returns {Promise<boolean>} - A promise that resolves to true if the email message is assembled, and false if there is an error.
   */
  async assembleEmailMessage(): Promise<boolean> {
    if (!this.email) {
      this._msgSvc.messageError(
        this.translate.instant('Client.Product.ClientNotEmail')
      );
      return false;
    }
    if (!this.nameCompleted) {
      this._msgSvc.messageError(
        this.translate.instant('Client.Product.ClientNotName')
      );
      return false;
    }
    this.richText = `
  <style>
    .email-container {
      font-family: Arial, sans-serif;
      color: #333;
      line-height: 1.6;
    }
    .email-header {
      font-size: 1.2em;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .email-body {
      margin: 20px 0;
    }
    .email-footer {
      margin-top: 30px;
      font-size: 0.9em;
      color: #555;
    }
    .email-signature {
      margin-top: 20px;
    }
    .contact-info span {
      display: inline-block;
      margin-right: 10px;
    }
  </style>
  <div class="email-container">
    <div class="email-header">Hola, ${this.nameCompleted}</div>
    <div class="email-body">
      <p>A continuación te hacemos envío de la cotización para tu <strong>PRODUCTO ${
        this.data?.productName
      }</strong> identificado con el id <strong>${
      this.data?.productId
    }</strong>.</p>
      <p>Dentro del archivo adjuntado, encontrarás los precios, detalles, coberturas y amparos de cada plan. También encontrarás los documentos que nos debes hacer llegar para continuar con el proceso.</p>
      <p>¡Nos encanta poder ayudarte a proteger lo que más quieres!</p>
    </div>
    <div class="email-footer">
      <div class="email-signature">Autor: ${this.nameCompletedUserLogged}</div>
      <div class="contact-info">
        <div><span>Empresa:</span>${this.nameCompletedBussines}</div>
        ${
          this.emailUserLogged
            ? `<div><span>Email:</span>${this.emailUserLogged}</div>`
            : ''
        }
        ${
          this.phoneCompletedUserLogged
            ? `<div><span>Teléfono:</span>${this.phoneCompletedUserLogged}</div>`
            : ''
        }
      </div>
    </div>
  </div>
  `;

    return true;
  }
}
