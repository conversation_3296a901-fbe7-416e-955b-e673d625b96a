import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PolicyCoverage } from 'src/app/shared/models/policy';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
// import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-coverages',
  standalone: true,
  imports: [
    CommonModule,
    MatSlideToggleModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    TableComponent,
  ],
  templateUrl: './coverages.component.html',
  styleUrls: ['./coverages.component.scss'],
})
export class CoveragesComponent implements OnInit, OnDestroy {

  @Input() isConfig: boolean = true;
  @Input() idPolicy: number = 0;
  @Output() datatableChanged = new EventEmitter<any[]>();

  //variable formulario.
  formCoverage: FormGroup = new FormGroup({});

  private _policyDataSubscription?: Subscription;
  //Variables relacionadas con la tabla.
  dataTableCoverage: PolicyCoverage[] = [];
  estructTableCoverage: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.Coverage.Title'
      ),
      columnValue: 'coverageName',
    },
    {
      columnLabel: this._translateService.instant(
        'Insurer.Description'
      ),
      columnValue: 'coverageDescription',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyCoverage',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  idCoverage: number = 0;

  constructor(
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public _modalDialog: MatDialog,
    public _utilsSvc: UtilsService,
    private _fb: FormBuilder,
    private _transactionService: TransactionService,
    private _policyService: PolicyService
  ) { }

  ngOnInit(): void {
    if (this.isConfig) {
      this.getPolicyDataSubscription();
    }
    else {
      this.getCoverageByIdPolicy();
    }
    this.initformCoverage();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTableCoverage[0].columnLabel = this._translateService.instant(
        'PolicyConfiguration.Coverage.Title'
      );
      this.estructTableCoverage[1].columnLabel =
        this._translateService.instant('Delete');
    });
  }

  getPolicyDataSubscription() {
    this._policyDataSubscription =
      this._policyService.currentpolicyData.subscribe({
        next: (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idPolicy = response.idPolicy;
            if (this.idPolicy > 0) {
              this.getCoverageByIdPolicy();
            }
          }
        },
      });
  }

  initformCoverage() {
    this.formCoverage = this._fb.group({
      coverageName: [null, Validators.required],
      coverageDescription: [null],
    });
  }

  //Gets the list of coverages associated with a policy
  getCoverageByIdPolicy() {
    this._transactionService
      .getCoverageByIdPolicy(this.idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTableCoverage = [];
        } else {
          this.dataTableCoverage = resp.result;
        }
      });
  }

  addCoverage() {
    if (!this.formCoverage.valid) {
      return;
    }
    let newCoverage: PolicyCoverage = {
      idPolicy: this.idPolicy,
      coverageName: this.formCoverage.get('coverageName')?.value,
      CoverageDescription: this.formCoverage.get('coverageDescription')?.value,
      idCoverage: this.idCoverage
    };
    this.idCoverage = 0;
    if (!this.isConfig) {
      this.dataTableCoverage = [...this.dataTableCoverage, newCoverage];
      this.datatableChanged.emit(this.dataTableCoverage);
      return;
    }
    this._transactionService
      .createPolicyCoverage(newCoverage)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._msgSvc.messageSuccess(
            'Registro creado',
            'Se creó una nueva cobertura'
          );
          this.getCoverageByIdPolicy();
          this.formCoverage.get('coverageName')?.setValue("")
          this.formCoverage.get('coverageDescription')?.setValue("")
        }
      });
  }

  controller(event: IconEventClickModel) {
    if (event.column == 'delete') {
      this._msgSvc
        .messageConfirmationAndNegation(
          this._translateService.instant('Delete'),
          '¿' +
          this._translateService.instant('Reports.Utils.QuestionDelete') +
          '?',
          'info',
          this._translateService.instant('Accept'),
          this._translateService.instant('Cancel')
        )
        .then((r) => {
          if (r) {
            if (this.isConfig) {
              this.deleteCoverage(event.value?.idCoverage);
            }
            else {
              const index = event?.index;
              if (index !== undefined && index >= 0 && index < this.dataTableCoverage.length) {
                this.dataTableCoverage.splice(index, 1);
                this.dataTableCoverage = [...this.dataTableCoverage];
                this.datatableChanged.emit(this.dataTableCoverage);
              }
            }
          }
        });
    }
    else if (event.column == 'modifyCoverage') {
      this.idCoverage = event.value?.idCoverage;
      this.formCoverage.get('coverageName')?.setValue(event.value?.coverageName);
      this.formCoverage.get('coverageDescription')?.setValue(event.value?.coverageDescription);
    }
  }

  deleteCoverage(idCoverage: number) {
    this._transactionService
      .deleteCoverageByIdPolicy(this.idPolicy, idCoverage)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._msgSvc.messageSuccess(
            'Registro eliminado',
            'Se eliminó una cobertura'
          );
          this.getCoverageByIdPolicy();
        }
      });
  }

  ngOnDestroy(): void {
    this._policyDataSubscription?.unsubscribe();
  }
}
