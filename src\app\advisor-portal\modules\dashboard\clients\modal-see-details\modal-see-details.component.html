<div class="row p-3 ">

  <div class="col-md-6">

    <strong>
      <h3>Detalle de tarea</h3>
    </strong>

  </div>
  <div class="col-md-6 text-end">
    <h2 mat-dialog-title class="p-0" style="margin-top: -14px;">
      <button mat-icon-button class="" (click)="close()">
        <mat-icon>close</mat-icon>
      </button>
    </h2>

  </div>
  <hr>
</div>
<mat-dialog-content>
  <div class="modal-content" style="margin-top: -15px">
    <p><strong>Id Tarea:</strong> {{idTask}}</p>
    <p><strong>Proceso:</strong> {{procedureName}}</p>
    <p><strong>Producto:</strong> {{productName}}</p>
    <p><strong>Etapa:</strong> {{stageName}}</p>
    <p><strong>Estado:</strong> {{stateName}}</p>
    <p><strong><PERSON><PERSON> de solicitud:</strong>{{dProcedureDate}}</p>
    <p><strong>Asignado a:</strong> {{modifiedBy}}</p>
    <p><strong>Creado por:</strong> {{assignedBy}}</p>
    <p *ngIf="fieldNameKey"><strong>{{fieldNameKey}}:</strong> {{fieldValueKey}}</p>
    <div *ngIf="plates.length > 0">
      <div *ngFor="let placa of plates; let i = index">
        <p><strong>Placa:</strong> {{placa}}</p>
      </div>
    </div>
  </div>
  <h4 class="mt-4 mb-2"><strong>Histórico de gestiones</strong></h4>
  <app-table [displayedColumns]="estructTableManagementHistory" [data]="dataTableManagementHistory"
    (iconClick)="controller($event)"></app-table>
</mat-dialog-content>

<mat-dialog-actions align="center">
  <button style="background-color: black; color: white;" mat-raised-button (click)="close()">Cerrar</button>
</mat-dialog-actions>