import { CommonModule, Location } from '@angular/common';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MatCheckboxChange,
  MatCheckboxModule,
} from '@angular/material/checkbox';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  RequestUploadTemplateModel,
  TypeOfManagementModel,
  UploadTemplateModel,
} from 'src/app/shared/models/massive';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';

@Component({
  selector: 'app-configure-upload-template',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    Modal2Component,
    FormsModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatRadioModule,
  ],
  templateUrl: './configure-upload-template.component.html',
  styleUrls: ['./configure-upload-template.component.scss'],
})
export class ConfigureUploadTemplateComponent {
  //Variables relacionadas con la tabla de plantillas.
  dataTableTemplate: UploadTemplateModel[] = [];
  estructTableTemplate: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('UploadDocuments.DocumentName'),
      columnValue: 'templateName',
    },
    {
      columnLabel: this._translateService.instant(
        'UploadDocuments.Type(s)OfDocuments'
      ),
      columnValue: 'format',
    },
    {
      columnLabel: this._translateService.instant('Product.Type'),
      columnValue: 'managementTypeName',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  //Variables relacionadas con la tabla de póliza.
  dataTablePolicy: any[] = [];
  estructTablePolicy: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      ),
      columnValue: 'idPolicy',
    },
    {
      columnLabel: this._translateService.instant('Policy.PolicyType'),
      columnValue: 'policyTypeName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      ),
      columnValue: 'insuranceName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      ),
      columnValue: 'productName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyName'
      ),
      columnValue: 'policyName',
    },
  ];

  //Variables relacionadas con los modales.
  @ViewChild('selectDocumentUploadTypeModal')
  selectDocumentUploadTypeModal?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;
  modalOpenRerefence: string = '';
  modalTitle: string = this._translateService.instant(
    'UploadDocuments.NewLoadingTemplate'
  );

  //Variables relacioandas con los select.
  typeOfLoad: TypeOfManagementModel[] = [];
  processes: any[] = [];
  products: any[] = [];
  stage: any[] = [];
  state: any[] = [];
  fieldDocumentList: any[] = [];
  policyFieldList: any[] = [];

  //Variables relacionadas con el formulario.
  form: FormGroup = new FormGroup({});
  keyword: string | any = null;
  typeFileList: any[] = [];
  idBusinessByCountry: number = 0;
  firstTime: boolean = true;
  idUploadTemplate: number = 0;
  idStateModule: number = 0;
  idTableModule: number = 0;
  isTable: boolean = false;
  isField: boolean = false;

  constructor(
    private _messageService: MessageService,
    private _transactionService: TransactionService,
    private _fieldService: FieldService,
    private _fileService: FileService,
    private _fb: FormBuilder,
    public matDialog: MatDialog,
    private _translateService: TranslateService,
    public _location: Location,
    public modalDialog: MatDialog,
    private _parameterService: ParametersService,
    private _moduleService: ModuleService,
    private _roleService: RoleService,
    private _activatedRoute: ActivatedRoute
  ) {
    this.getUploadTemplates();
  }

  ngOnInit(): void {
    this.getBusinessByCountry();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion de la tabla plantillas de carga.
      this.estructTableTemplate[0].columnLabel = this._translateService.instant(
        'UploadDocuments.DocumentName'
      );
      this.estructTableTemplate[1].columnLabel = this._translateService.instant(
        'UploadDocuments.Type(s)OfDocuments'
      );
      this.estructTableTemplate[2].columnLabel =
        this._translateService.instant('Product.Type');
      this.estructTableTemplate[3].columnLabel =
        this._translateService.instant('Modify');
      this.estructTableTemplate[4].columnLabel =
        this._translateService.instant('Delete');
    });

    //traduccion de la tabla de pólizas
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTablePolicy[0].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      );
      this.estructTablePolicy[1].columnLabel =
        this._translateService.instant('Policy.PolicyType');
      this.estructTablePolicy[2].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      );
      this.estructTablePolicy[3].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      );
      this.estructTablePolicy[4].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyName'
      );
    });

    if (this.idUploadTemplate === 0) {
      this.modalTitle = this._translateService.instant(
        'UploadDocuments.NewLoadingTemplate'
      );
    }

    if (this.idUploadTemplate > 0) {
      this.modalTitle = this._translateService.instant(
        'UploadDocuments.ModifyUploadTemplate'
      );
    }
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
    });
  }

  //Inicializa el formulario.
  initForm() {
    this.form = this._fb.group({
      idUploadTemplate: [{ value: 0, disabled: true }],
      templateName: [null, [Validators.required]],
      idManagementType: [null, [Validators.required]],
      idStateModule: null,
      idFieldModule: null,
      idTableModule: null,
      idPolicyParent: null,
      overwriteDocuments: false,
      idBusinessByCountry: null,
      fkIdProcess: null,
      fkIdProduct: null,
      fkIdStage: null,
      fkIdState: [null, [Validators.required]],
      taskRadio: [null, [Validators.required]],
      idFieldPolicy: null,
    });

    this.form.get('fkIdProcess')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          if (this.firstTime) {
            this.firstTime = !this.firstTime;
          }
          if (this.idUploadTemplate == 0) {
            this.form.get('fkIdProduct')?.setValue(null);
            this.form.get('fkIdStage')?.setValue(null);
            this.form.get('fkIIdState')?.setValue(null);
            this.products = [];
            this.stage = [];
            this.state = [];
          }
          if (this.idUploadTemplate > 0 && !this.firstTime) {
            this.form.get('fkIdProduct')?.setValue(null);
            this.form.get('fkIdStage')?.setValue(null);
            this.form.get('fkIIdState')?.setValue(null);
            this.products = [];
            this.stage = [];
            this.state = [];
          }

          this.getProductsList();
        }
      },
    });

    this.form.get('fkIdProduct')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          if (this.idUploadTemplate == 0) {
            this.form.get('fkIdStage')?.setValue(null);
            this.form.get('fkIIdState')?.setValue(null);
            this.stage = [];
            this.state = [];
          }
          if (this.idUploadTemplate > 0 && !this.firstTime) {
            this.form.get('fkIdStage')?.setValue(null);
            this.form.get('fkIIdState')?.setValue(null);
            this.stage = [];
            this.state = [];
          }
          this.getStageByIdProductModule();
        }
      },
    });

    this.form.get('fkIdStage')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          if (this.idUploadTemplate == 0) {
            this.form.get('fkIIdState')?.setValue(null);
            this.state = [];
          }
          if (this.idUploadTemplate > 0 && !this.firstTime) {
            this.form.get('fkIIdState')?.setValue(null);
            this.state = [];
          }
          this.getStageByStateList();
        }
      },
    });

    this.form.get('fkIdState')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.form.get('idStateModule')?.setValue(data.pkIIdStageByState);
        }
      },
    });

    this.form.get('idManagementType')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          if (this.idUploadTemplate === 0) {
            this.form.get('idUploadTemplate')?.setValue(0);
            this.form.get('templateName')?.setValue(null);
            this.form.get('idStateModule')?.setValue(null);
            this.form.get('idFieldModule')?.setValue(null);
            this.form.get('idTableModule')?.setValue(null);
            this.form.get('idPolicyParent')?.setValue(null);
            this.form.get('overwriteDocuments')?.setValue(false);
            this.form.get('fkIdProcess')?.setValue(null);
            this.form.get('fkIdProduct')?.setValue(null);
            this.form.get('fkIdStage')?.setValue(null);
            this.form.get('fkIdState')?.setValue(null);
            this.form.get('taskRadio')?.setValue(null);
            this.form.get('idFieldPolicy')?.setValue(null);
            if (data === 8) {
              this.form
                .get('idFieldPolicy')
                ?.setValidators(Validators.required);
              this.form.get('idFieldPolicy')?.updateValueAndValidity();
              this.form.get('idTableModule')?.setValidators(null);
              this.form.get('idTableModule')?.updateValueAndValidity();
              this.form.get('fkIdState')?.setValidators(null);
              this.form.get('fkIdState')?.updateValueAndValidity();
              this.form.get('taskRadio')?.setValidators(null);
              this.form.get('taskRadio')?.updateValueAndValidity();
            } else {
              this.form.get('idFieldPolicy')?.setValidators(null);
              this.form.get('idFieldPolicy')?.updateValueAndValidity();
              this.form.get('fkIdState')?.setValidators(Validators.required);
              this.form.get('fkIdState')?.updateValueAndValidity();
              this.form.get('taskRadio')?.setValidators(Validators.required);
              this.form.get('taskRadio')?.updateValueAndValidity();
            }
          } else if (this.idUploadTemplate > 0) {
            if (data === 8) {
              this.form.get('fkIdState')?.setValidators(null);
              this.form.get('fkIdState')?.updateValueAndValidity();
              this.form.get('taskRadio')?.setValidators(null);
              this.form.get('taskRadio')?.updateValueAndValidity();
            } else {
              this.form.get('fkIdState')?.setValidators(Validators.required);
              this.form.get('fkIdState')?.updateValueAndValidity();
              this.form.get('taskRadio')?.setValidators(Validators.required);
              this.form.get('taskRadio')?.updateValueAndValidity();
            }
          }
        }
      },
    });

    this.form.get('taskRadio')?.valueChanges.subscribe({
      next: (data) => {
        this.form.get('idFieldModule')?.setValue(null);
        this.form.get('idTableModule')?.setValue(null);
        if (data === 'field') {
          this.form.get('idFieldModule')?.setValidators(Validators.required);
          this.form.get('idFieldModule')?.updateValueAndValidity();
          this.form.get('idTableModule')?.setValidators(null);
          this.form.get('idTableModule')?.updateValueAndValidity();
          if (this.form.get('fkIdState')?.value) {
            this.getFileTypeFieldsByIdFormModule(
              this.form.get('fkIdState')?.value.fkIdFormModule
            );
          }
        } else if (data === 'documentTable') {
          this.form.get('idTableModule')?.setValidators(Validators.required);
          this.form.get('idTableModule')?.updateValueAndValidity();
          this.form.get('idFieldModule')?.setValidators(null);
          this.form.get('idFieldModule')?.updateValueAndValidity();
          if (this.form.get('fkIdState')?.value) {
            this.getTableDocumentFieldsByIdFormModule(
              this.form.get('fkIdState')?.value.fkIdFormModule
            );
          }
        }
      },
    });
  }

  get idManagementType(): number {
    return this.form.get('idManagementType')?.value;
  }

  get taskRadio(): string {
    return this.form.get('taskRadio')?.value;
  }

  //Controlador de las acciones configuradas en la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'modify':
        this.idUploadTemplate = event.value.idUploadTemplate || 0;
        this.openModal(
          'selectDocumentUploadTypeModal',
          event.value.idUploadTemplate
        );
        break;
      case 'delete':
        this._messageService
          .messageConfirmationAndNegation(
            this._translateService.instant('UploadDocuments.DoYouWantToDelete?'),
            this._translateService.instant(
              'UploadDocuments.IrreversibleAction'
            ),
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.deleteUploadTemplate(event.value.idUploadTemplate);
            }
          });
        break;
      default:
        break;
    }
  }

  searchPolicy() {}

  //Obtiene la lista de plantillas.
  getUploadTemplates(idManagementType?: number) {
    this._fileService
      .getUploadTemplates(idManagementType)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.dataTableTemplate = resp.result;
        }
      });
  }

  //Obtiene la información de una póliza detallada por medio de su id.
  getPolicyById(idPolicy: number) {
    this._transactionService
      .getPolicyById(idPolicy)
      .pipe(
        catchError((error) => {
          this.dataTablePolicy = [];
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTablePolicy = [];
        } else {
          this.dataTablePolicy = Array(resp.result);
          if (this.dataTablePolicy[0].idPolicy) {
            this.getFileTypeFieldsByIdPolicy(this.dataTablePolicy[0].idPolicy);
          }
        }
      });
  }

  //Obtiene los tipos de gestiones registrados en el sistema.
  getAllTypesOfManagement() {
    this._fileService
      .getAllTypesOfManagement()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.typeOfLoad = resp.result;
          this.typeOfLoad = this.getFilterOption();
        }
      });
  }

  // Obtiene la lista de los tipos de archivos.
  getTypeFileList() {
    this._parameterService
      .getParameters('Type_Files_DocumenModules')
      .subscribe((resp: any) => {
        if (resp.error) {
          this._messageService.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          this.typeFileList = resp;
        }
      });
  }

  //Obtiene la lista de los campos de póliza.
  getFileTypeFieldsByIdFormModule(idFormModule: number) {
    this._moduleService
      .getFileTypeFieldsByIdFormModule(idFormModule)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.fieldDocumentList = resp.result;
        }
      });
  }

  //obtiene los campos de tipo cargue documentos en una tabla.
  getTableDocumentFieldsByIdFormModule(idFormModule: number) {
    this._moduleService
      .getTableDocumentFieldsByIdFormModule(idFormModule)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.fieldDocumentList = resp.result;
          if (this.idUploadTemplate > 0) {
            this.form.get('idTableModule')?.setValue(this.idTableModule);
          }
        }
      });
  }

  //Obtiene todos los procesos.
  getAllProcess() {
    this._parameterService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.processes = resp.result;
        }
      });
  }

  //obtiene los productos registrados por proceso y idBusinessByCountry.
  getProductsList() {
    this._roleService
      .getProductsByIdProcess(
        this.form.get('fkIdProcess')?.value,
        this.idBusinessByCountry
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant('TaskTray.Messages.NoProducts')
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.products = [];
          this.form.get('fkIdProduct')?.setValue(null);
        } else {
          if (resp.result.length === 0) {
            this.form.get('fkIdProduct')?.disable();
            this.form
              .get('fkIdProduct')
              ?.setValue(resp.result[0].pkIIdProductModule);
          } else {
            this.form.get('fkIdProduct')?.enable();
            this.products = resp.result;
          }
        }
      });
  }

  //obtiene la lista de etapas asignados a un producto.
  getStageByIdProductModule() {
    if (this.form.get('fkIdProduct')?.value > 0) {
      this._moduleService
        .getStageByIdProductModule(this.form.get('fkIdProduct')?.value)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            this.form.get('fkIIdState')?.setValue(null);
            this.state = [];
          } else {
            this.stage = resp.result;
          }
        });
    }
  }

  //Obtiene la lista de estados asociados a una etapa.
  getStageByStateList() {
    this._moduleService
      .getStagByStateAssociateToFormByIdStage(this.form.get('fkIdStage')?.value)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.state = [];
        } else {
          this.state = resp.result;
          this.firstTime = !this.firstTime;
          if (this.idUploadTemplate > 0) {
            this.form
              .get('fkIdState')
              ?.setValue(this.findStateByidStateModule(this.idStateModule));
            if (this.isField) {
              this.getFileTypeFieldsByIdFormModule(
                this.form.get('fkIdState')?.value.fkIdFormModule
              );
            }
            if (this.isTable) {
              this.getTableDocumentFieldsByIdFormModule(
                this.form.get('fkIdState')?.value.fkIdFormModule
              );
            }
          }
        }
      });
  }

  //Obtiene la lista de campos de una póliza.
  getFileTypeFieldsByIdPolicy(idPolicy: number) {
    this._fieldService
      .getFileTypeFieldsByIdPolicy(idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this.policyFieldList = [];
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.policyFieldList = [];
        } else {
          this.policyFieldList = resp.result;
          this.form
            .get('idPolicyParent')
            ?.setValue(this.dataTablePolicy[0].idPolicy);
        }
      });
  }

  //Busca una plantilla de carga por idUploadTemplate.
  getUploadTemplateById(idUploadTemplate: number) {
    this._fileService
      .getUploadTemplateById(idUploadTemplate)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.form
            .get('idManagementType')
            ?.setValue(resp.result.idManagementType);
          this.idStateModule = resp.result.idStateModule;
          this.idTableModule = resp.result.idTableModule;
          this.isField = resp.result.isField;
          this.isTable = resp.result.isTable;
          if (resp.result.idPolicyParent) {
            this.getPolicyById(resp.result.idPolicyParent);
          }
          this.form.get('fkIdProcess')?.setValue(resp.result.idProcess);
          this.form.get('fkIdProduct')?.setValue(resp.result.idProduct);
          this.form.get('fkIdStage')?.setValue(resp.result.idStage);
          if (resp.result.isField) {
            this.form.get('taskRadio')?.setValue('field');
          }
          if (resp.result.isTable) {
            this.form.get('taskRadio')?.setValue('documentTable');
          }
          this.form.patchValue(resp.result);
        }
      });
  }

  //Crea una plantilla de carga.
  createUploadTemplate(model: RequestUploadTemplateModel) {
    this._fileService
      .createUploadTemplate(model)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess('Plantilla guardada', '');
          this.closeModal();
          this.getUploadTemplates();
        }
      });
  }

  //Actualiza una plantilla de carga.
  updateUploadTemplate(model: RequestUploadTemplateModel) {
    this._fileService
      .updateUploadTemplate(model)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess('Plantilla guardada', '');
          this.closeModal();
          this.getUploadTemplates();
          this.idUploadTemplate = 0;
        }
      });
  }

  //Elimina una plantilla de carga.
  deleteUploadTemplate(idUploadTemplate: number) {
    this._fileService
      .deleteUploadTemplate(idUploadTemplate)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageConfirmatio(
            this._translateService.instant('UploadDocuments.Deleted'),
            '',
            'success',
            this._translateService.instant('Continue')
          );
          this.getUploadTemplates();
        }
      });
  }

  //Filtra los tipos de carga de documento.
  getFilterOption(): TypeOfManagementModel[] {
    return this.typeOfLoad.filter(
      (option: TypeOfManagementModel) => option.fkIIdBulkLoadType === 4
    );
  }

  // Función que abre un modal, dependiendo el selecctor indicado.
  openModal(modalReference: string, idUploadTemplate?: number) {
    if (idUploadTemplate) {
      if (idUploadTemplate === 0) {
        this.modalTitle = this._translateService.instant(
          'UploadDocuments.NewLoadingTemplate'
        );
      } else if (idUploadTemplate > 0) {
        this.modalTitle = this._translateService.instant(
          'UploadDocuments.ModifyUploadTemplate'
        );
      }
    }
    const modalConfiguration = {
      disableClose: false,
      width: '60vw',
      height: 'auto',
    };
    let modal: TemplateRef<any>;
    switch (modalReference) {
      case 'selectDocumentUploadTypeModal':
        this.getAllProcess();
        this.initForm();
        if (idUploadTemplate) {
          this.form.get('idUploadTemplate')?.setValue(idUploadTemplate);
          this.form.get('idManagementType')?.disable();
        }
        if (this.form.get('idUploadTemplate')?.value > 0) {
          this.getUploadTemplateById(this.form.get('idUploadTemplate')?.value);
        }

        this.getAllTypesOfManagement();
        this.modalOpenRerefence = 'selectDocumentUploadTypeModal';
        modal = this.selectDocumentUploadTypeModal!;
        break;
      default:
        return;
    }
    //Abre el modal y guarda la referencia en la variable currentModal.
    this.currentModal = this.modalDialog.open(modal, modalConfiguration);
  }

  //Cierra todos los modales.
  closeModal() {
    this.currentModal = null;
    this.policyFieldList = [];
    this.idUploadTemplate = 0;
    this.dataTablePolicy = [];
    this.idStateModule = 0;
    this.idTableModule = 0;
    this.isTable = false;
    this.isField = false;
    this.form.get('idManagementType')?.enable();
    this.matDialog.closeAll();
    this.modalTitle = this._translateService.instant(
      'UploadDocuments.NewLoadingTemplate'
    );
  }

  /// se filtran los tipos de archivo seleccionados
  toggleTypeFile(event: MatCheckboxChange, name: string) {
    if (event !== undefined) {
      let filterTypeFile = this.typeFileList.filter((x) => x.Name == name);

      filterTypeFile.filter((element) => {
        if (element.Name == name) {
          if (event.checked) {
            element.checked = true;
            return true;
          } else {
            element.checked = false;
            return false;
          }
        } else {
          return false;
        }
      });
    }
  }

  //Obtiene el estado por idStateModule.
  findStateByidStateModule(idStateModule: number) {
    const result = this.state.find(
      (x) => x.pkIIdStageByState === idStateModule
    );
    return result;
  }

  //Funciónq ue guarda el formulario de plantillas.
  save() {
    const request: RequestUploadTemplateModel = {
      idBusinessByCountry: this.idBusinessByCountry,
      idFieldModule: this.form.get('idFieldModule')?.value,
      idFieldPolicy: this.form.get('idFieldPolicy')?.value,
      idManagementType: this.form.get('idManagementType')?.value,
      idPolicyParent: this.getIdPolicyParent(),
      idStateModule: this.form.get('idStateModule')?.value,
      idTableModule: this.form.get('idTableModule')?.value,
      idUploadTemplate: this.form.get('idUploadTemplate')?.value,
      overwriteDocuments: this.form.get('overwriteDocuments')?.value,
      templateName: this.form.get('templateName')?.value,
    };

    if (this.form.valid) {
      if (this.idUploadTemplate > 0) {
        this.updateUploadTemplate(request);
      } else {
        this.createUploadTemplate(request);
      }
    }
  }

  //Valida el tipo de gestióan para detemrinar si asigna o no el id de póliza al request.
  getIdPolicyParent(): number {
    return this.idManagementType === 7 ? 0 : this.dataTablePolicy[0].idPolicy;
  }
  goBack() {
    this._location.back();
  }
}
