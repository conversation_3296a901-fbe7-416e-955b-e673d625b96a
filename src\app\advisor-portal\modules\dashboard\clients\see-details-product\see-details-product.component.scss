.clientes-container {
  padding: 20px;
}

.mat-card {
  margin-bottom: 20px;
}

.poliza-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.poliza-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.poliza-id label {
  font-weight: bold;
}

.poliza-id {
  display: flex;
  align-items: center;
}

.poliza-status {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 5px;
  color: green;
}

.poliza-detalles {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.detalle-item {
  display: flex;
  flex-direction: column;
}

.caratula-content {
  text-align: center;
  margin-bottom: 20px;
}

.mat-card-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}
.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
}