<ng-container *ngIf="news.length > 0">
  <div class="cont-title mb-3">
    <h4 *ngIf="newsObject.vName; else notName">{{ newsObject.vName }}</h4>
    <ng-template #notName>
      <h4>{{ "HomeAdvisors.New.Title" | translate }}</h4>
    </ng-template>
  </div>
  <div class="cont-new">
    <div class="new" *ngFor="let new of news">
      <div class="cont-img">
        <img [src]="new.imageBase64" class="img-new" alt="Imagen Noticia" />
      </div>
      <div class="cont-info">
        <div class="cont-title-new">
          <h5 class="title-new">{{ new.vTitle }}</h5>
        </div>
        <p class="paragraph-info">
          {{ new.vDescription }}
        </p>
        <div class="keep-reading" *ngIf="new.vLink">
          <button
           *ngIf="new.bActiveButton"
            class="mb-2 btn-keep-reading"
            mat-raised-button
            (click)="gotToNew(new.vLink)"
          >
            <mat-icon fontIcon="arrow_forward_ios" iconPositionEnd></mat-icon
            > {{ new.vTextButton }}
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-container>
