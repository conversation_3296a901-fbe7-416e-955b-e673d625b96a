import { Routes } from '@angular/router';
import { GroupsComponent } from './groups.component';

export default [
  {
    path: '',
    component: GroupsComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-groups/all-groups.component').then(
            (c) => c.AllGroupsComponent
          ),
      },
      {
        path: 'new',
        loadComponent: () =>
          import('./edit-new-group/edit-new-group.component').then(
            (c) => c.EditNewGroupComponent
          ),
      },
      {
        path: 'new/:id',
        loadComponent: () =>
          import('./edit-new-group/edit-new-group.component').then(
            (c) => c.EditNewGroupComponent
          ),
      },
    ],
  },
] as Routes;
