import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { catchError, forkJoin, of, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';

import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';

import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { DataService } from 'src/app/shared/services/data/data.service';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';

import { PolicyMappingModel } from 'src/app/shared/models/policy-mapping-table/policy-mapping.model';
import { PolicyMappingFieldModel } from 'src/app/shared/models/policy-mapping-table/policy-mapping-field.model';
import { PolicyMappingTableModel } from 'src/app/shared/models/policy-mapping-table/policy-mapping-table-model';
import { FormMappingFieldModel } from 'src/app/shared/models/policy-mapping-table/form-mapping-field.model';
import { RequesFilterPolicyTableModel } from '../../../policy/shared/models';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers/insurance-company.model';
import { PorductModulesModel } from 'src/app/shared/models/product/product-modules.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { FieldService } from 'src/app/shared/services/field/field.service';



@Component({
  selector: 'app-policies',
  standalone: true,
  imports: [CommonModule, MatSlideToggleModule, FormsModule, MatInputModule, MatButtonModule, MatIconModule, ReactiveFormsModule, TranslateModule, MatSelectModule, MatFormFieldModule, MatDatepickerModule, MatNativeDateModule, MatTabsModule, TableComponent, Modal2Component],
  templateUrl: './policies.component.html',
  styleUrl: './policies.component.scss'
})
export class PoliciesComponent implements OnInit {

  @Input() idForm: number = 0;

  // modal
  @ViewChild('MapElementModal') MapElementModal?: TemplateRef<any>;
  mappingModalForm: FormGroup = new FormGroup({});
  modalTitle: string = this._translateService.instant('MappingForm.ModalTitle');
  selectedTab!: string;

  private _settingCountryAndCompanySubscription?: Subscription;
  createPolicyForm: FormGroup = new FormGroup({});

  listOfInsurances: InsuranceCompanyModel[] = [];
  listOfProducts: PorductModulesModel[] = [];
  listOfWTWIds: any[] = [];
  listOfPolicyStates: any[] = [];

  idBusinessByCountry: number = 0;
  idPolicy: number = 0;
  requestFilterPolicy: RequesFilterPolicyTableModel = {
    idBusinessCountry: 0,
    endorsement: '',
    idInsurance: 0,
    keyword: '',
    idProduct: 0,
    statePolicyId: 0,
    order: 'desc',
    page: 0,
    pageSize: 100,
    idtypePolicy: 0,
  };

  showTab = true;
  policyMappingModel!: PolicyMappingModel;

  formFields: FormMappingFieldModel[] = [];
  policyFields: PolicyMappingFieldModel[] = [];

  insuredFields: PolicyMappingFieldModel[] = [];
  takerFields: PolicyMappingFieldModel[] = [];
  beneficiaryFields: PolicyMappingFieldModel[] = [];
  othersFields: PolicyMappingFieldModel[] = [];

  listOfFormFields: FormMappingFieldModel[] = [];

  policyMappings: PolicyMappingTableModel[] = [];
  insuredMappings: PolicyMappingTableModel[] = [];
  fieldsMappings: PolicyMappingTableModel[] = [];
  takerMappings: PolicyMappingTableModel[] = [];
  beneficiaryMappings: PolicyMappingTableModel[] = [];
  othersMappings: PolicyMappingTableModel[] = [];

  defaultMappings: PolicyMappingTableModel[] = [];

  indexOfRowToChanged!: number;
  activeTabIndex = 0;

  estructMappingTable: BodyTableModel[] = [];
  estructMappingBeneficiaryTable: BodyTableModel[] = [];

  policyDataMappingTable: PolicyMappingTableModel[] = []
  insuredMappingTable: PolicyMappingTableModel[] = []
  takerMappingTable: PolicyMappingTableModel[] = []
  beneficiaryMappingTable: PolicyMappingTableModel[] = []
  othersMappingTable: PolicyMappingTableModel[] = []

  showPolicyTab = true;
  showInsuredTab = true;
  showTakerTab = true;
  showBeneficiaryTab = true;
  showOthersTab = true;
  orderTabs: string[] = [];
  modalLabelTextPolicy: string = this._translateService.instant('MappingForm.ModalPolicyFieldLabel');
  modalLabelTextForm: string = this._translateService.instant('MappingForm.ModalFormFieldLabel');
  showTable: boolean = false;
  beneficiaryTab: number = 0;
  idInsurance!: number;
  allSinAsignar: any;
  tabName: string = 'MappingForm.OthersLabel'

  constructor(
    private _translateService: TranslateService,
    public modalDialog: MatDialog,
    private _formBuilder: FormBuilder,
    public utilsService: UtilsService,
    private _insuranceService: InsuranceService,
    private _productService: ProductService,
    private _messageService: MessageService,
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _settingService: SettingService,
    private _transactionService: TransactionService,
    private _moduleService: ModuleService,
    private _dataService: DataService,
    private _cdr: ChangeDetectorRef,
    private _policyService: PolicyService,
    private _fieldService: FieldService,
  ) { }

  ngOnInit(): void {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessByCountry = response.enterprise.pkIIdBusinessByCountry;
            this.getInsuranceCompanyByIdBusinessByCountry(this.idBusinessByCountry);
            this._dataService.states.subscribe(data => { this.listOfPolicyStates = data; });
          }
        }
      );
    this.setStructureMappingTable();
  }

  setTabVisibility() {
    this.showPolicyTab = this.policyFields && this.policyFields.length > 0;
    this.showInsuredTab = this.insuredFields && this.insuredFields.length > 0;
    this.showTakerTab = this.takerFields && this.takerFields.length > 0;
    this.showBeneficiaryTab = this.beneficiaryFields && this.beneficiaryFields.length > 0;
    this.showOthersTab = this.othersFields && this.othersFields.length > 0;
    this.orderTabs = []
    if (this.showPolicyTab) this.orderTabs.push('Policy')
    if (this.showInsuredTab) this.orderTabs.push('Insured')
    if (this.showTakerTab) this.orderTabs.push('Taker')
    if (this.showBeneficiaryTab) this.orderTabs.push('Beneficiary')
    if (this.showOthersTab) this.orderTabs.push('Others')
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['idForm']) {
      this.idForm = changes['idForm'].currentValue;
      this.idForm != 0 ? this.initCreatePolicyForm() : '';
    }
  }

  setStructureMappingTable() {
    this.estructMappingTable = [
      { columnLabel: this._translateService.instant('MappingForm.StructMappingTableFormFieldColumn'), columnValue: 'vFormField' },
      { columnLabel: this._translateService.instant('MappingForm.StructMappingTablePolicyFieldColumn'), columnValue: 'vPolicyField' },
      { columnLabel: this._translateService.instant('MappingForm.StructMappingTableStatusColumn'), columnValue: 'bState', functionValue: (item: any) => this.utilsService.changeAssignedValue(item) },
      { columnLabel: this._translateService.instant('MappingForm.StructMappingTableMandatoryColumn'), columnValue: 'bMandatory', functionValue: (item: any) => this.parseRequiredField(item) },
      { columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.modify'), columnValue: 'Modify', columnIcon: 'create' },
      { columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.delete'), columnValue: 'Delete', columnIcon: 'delete' }
    ];
  }

  initCreatePolicyForm() {
    this.createPolicyForm = this._formBuilder.group({
      active: [false, [Validators.required]],
      statePolicyList: [null, [Validators.required]],
      insurancesList: ['', [Validators.required]],
      productList: ['', [Validators.required]],
      WTWIdList: ['', [Validators.required]],
    })
  }

  onToggleChange(event: any) { }

  getInsuranceCompanyByIdBusinessByCountry(idBusinessByCountry: number) {
    this._insuranceService.getInsuranceCompanyByIdBusinessByCountry(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.listOfInsurances = resp.result;
        }
      });
  }

  onTabChange(event: MatTabChangeEvent) {
    this.activeTabIndex = event.index;

    if (this.activeTabIndex === this.beneficiaryTab) {
      this.modalLabelTextPolicy = this._translateService.instant('MappingForm.StructMappingTableBenefiaryPolicyColumn');
      this.modalLabelTextForm = this._translateService.instant('MappingForm.StructMappingTableBenefiaryFormColumn');

      this.estructMappingBeneficiaryTable = [
        { columnLabel: this._translateService.instant('MappingForm.StructMappingTableBenefiaryFormColumn'), columnValue: 'vFormField' },
        { columnLabel: this._translateService.instant('MappingForm.StructMappingTableBenefiaryPolicyColumn'), columnValue: 'vPolicyField' },
        { columnLabel: this._translateService.instant('MappingForm.StructMappingTableStatusColumn'), columnValue: 'bState', functionValue: (item: any) => this.utilsService.changeAssignedValue(item) },
        { columnLabel: this._translateService.instant('MappingForm.StructMappingTableMandatoryColumn'), columnValue: 'bMandatory', functionValue: (item: any) => this.parseRequiredField(item) },
        { columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.modify'), columnValue: 'Modify', columnIcon: 'create' },
        { columnLabel: this._translateService.instant('MetricsSettings.moduleSettings.delete'), columnValue: 'Delete', columnIcon: 'delete' }
      ];
    } else {
      this.modalLabelTextPolicy = this._translateService.instant('MappingForm.ModalPolicyFieldLabel');
      this.modalLabelTextForm = this._translateService.instant('MappingForm.ModalFormFieldLabel');
      this.setStructureMappingTable();
    }
  }


  getPoliciesId() {
    this.requestFilterPolicy.idInsurance = this.createPolicyForm.get('insurancesList')?.value;
    this.requestFilterPolicy.idProduct = this.createPolicyForm.get('productList')?.value
    this.requestFilterPolicy.idBusinessCountry = this.idBusinessByCountry

    this._transactionService.getIdWTWs(this.requestFilterPolicy).pipe(
      catchError((error) => {
        this._messageService.messageWaring('', error.error.message);
        this.showTable = false;
        this.resetSelects();
        return of([]);
      })
    )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.resetSelects();
          return
        }

        if (resp.result.length > 0) {
          this.listOfWTWIds = Object.values(resp.result)
        } else {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'),
            this._translateService.instant('AnsConfiguration.Reports.MessageReportNotFound'));
          this.resetSelects();
        }
        this.showTable = false;
      });
  }

  resetSelects() {
    this.createPolicyForm.get('productList')?.setValue(0);
    this.createPolicyForm.get('WTWIdList')?.setValue(0);
    this.listOfWTWIds = [];
    this.showTable = false;
  }

  getAllDataPoliciesId() {
    const idPolicy = this.createPolicyForm.get("WTWIdList")?.value;
    this.getTabsWithFieldsByIdPolicy()
    this._moduleService.getPolicyMapperByIdForm_IdPolicy(this.idForm, idPolicy).pipe(
      catchError((error) => {
        this._messageService.messageWaring('', error.error.message);
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        this.showTable = false;
        return
      }

      if (resp.result) {
        this.showTable = true;
        this._dataService.fields.subscribe(data => { this.formFields = data; });
        this.createPolicyForm.patchValue({
          active: resp.result.bActive,
          statePolicyList: resp.result.fkIIdState != 0 ? resp.result.fkIIdState : this.createPolicyForm.get('statePolicyList')?.value
        })

        this.policyFields = resp.result.standardFields;
        this.insuredFields = resp.result.insuredFields;
        this.takerFields = resp.result.takerFields;
        this.beneficiaryFields = resp.result.beneficiaryFields;
        console.log("🚀 ~ PoliciesComponent ~ ).subscribe ~ this.beneficiaryFields:", this.beneficiaryFields)
        this.allSinAsignar = this.beneficiaryFields.every(item => item.iStatus === 0);
        this.othersFields = resp.result.othersFields;

        this.setTabVisibility();
        this.policyDataMappingTable = this.mappingDataToTable(this.policyFields)
        this.insuredMappingTable = this.mappingDataToTable(this.insuredFields);
        this.takerMappingTable = this.mappingDataToTable(this.takerFields);
        this.beneficiaryMappingTable = this.mappingDataToTable(this.beneficiaryFields);

        console.log("🚀 ~ PoliciesComponent ~ ).subscribe ~ this.allSinAsignar :", this.allSinAsignar)
        this.othersMappingTable = this.mappingDataToTable(this.othersFields);
        this.beneficiaryTab = 0
        if (this.policyDataMappingTable.length > 0) this.beneficiaryTab++;
        if (this.insuredMappingTable.length > 0) this.beneficiaryTab++;
        if (this.takerMappingTable.length > 0) this.beneficiaryTab++;
      } else {
        this.showTable = false;
        this._messageService.messageWaring(this._translateService.instant('ThereWasAError'),
          this._translateService.instant('AnsConfiguration.Reports.MessageReportNotFound'));
        // this.resetSelects();
        this.showTable = false;
      }
    });
  }


  controller(event: IconEventClickModel) {

    switch (event.column) {
      case 'Modify':
        this.modalTitle = "Mapeo de un elemento"// this._translateService.instant('MetricsSettings.moduleSettings.updateScriptLabel');
        this.listOfFormFields = this.formFields
          .filter(field => field.bActive === true)
          .sort((a, b) => a.iOrder - b.iOrder);

        this.indexOfRowToChanged = event.index;
        this.openModal();
        this.mappingModalForm.patchValue({ policyField: event.value.vPolicyField });
        this.mappingModalForm.get("fieldFormList")?.setValue(event.value.iIdFormField);
        break;
      case 'Delete':
        this.indexOfRowToChanged = event.index;
        switch (this.activeTabIndex) {
          case 0:
            this.UnassingedField(this.indexOfRowToChanged, this.policyDataMappingTable);
            break;
          case 1:
            this.UnassingedField(this.indexOfRowToChanged, this.insuredMappingTable);
            break;
          case 2:
            this.UnassingedField(this.indexOfRowToChanged, this.takerMappingTable);
            break;
          case 3:
            this.UnassingedField(this.indexOfRowToChanged, this.beneficiaryMappingTable);
            break;
          case 4:
            this.UnassingedField(this.indexOfRowToChanged, this.othersMappingTable);
            break;
          default:

            break;
        }


        break;
      default:
        break;
    }
  }

  initMappingModalForm() {
    this.mappingModalForm = this._formBuilder.group({
      policyField: [{ value: 0, disabled: true }],
      fieldFormList: [[Validators.required]]

    })
  }

  openModal() {
    this.initMappingModalForm();
    this.modalDialog.open(this.MapElementModal!, {
      width: '720px',
    });
  }

  changesAssigment() {
    if (!this.mappingModalForm.valid) {
      return
    }
    const selectedFieldId = this.mappingModalForm.get('fieldFormList')?.value;
    const selectedField = this.formFields.find(field => field.pkIIdFieldModule === +selectedFieldId);
    const selectedTab = this.orderTabs[this.activeTabIndex]
    switch (selectedTab) {
      case 'Policy':
        this.mappingChanges(selectedField, this.indexOfRowToChanged, this.policyDataMappingTable, this.policyFields);
        break;
      case 'Insured':
        this.mappingChanges(selectedField, this.indexOfRowToChanged, this.insuredMappingTable, this.insuredFields);
        break;
      case 'Taker':
        this.mappingChanges(selectedField, this.indexOfRowToChanged, this.takerMappingTable, this.takerFields);
        break;
      case 'Beneficiary':
        this.mappingChanges(selectedField, this.indexOfRowToChanged, this.beneficiaryMappingTable, this.beneficiaryFields);
        break;
      case 'Others':
        this.mappingChanges(selectedField, this.indexOfRowToChanged, this.othersMappingTable, this.othersFields);
        break;
      default:

        break;
    }
    this.onModalClose();
  }


  onModalClose() {
    this.modalDialog.closeAll();
  }

  areAllFieldsAssigned(): boolean {
    const unassignedStatus = this._translateService.instant('MappingForm.UnassignedStatus');
    const allMappings = [
      this.policyDataMappingTable,
      this.insuredMappingTable,
      this.takerMappingTable,
      this.beneficiaryMappingTable,
      this.othersMappingTable
    ].flat();

    if (allMappings.length === 0) {
      return false;
    }
    return !allMappings.some((mapping: any) => mapping.iIdFormField === null && mapping.bMandatory);

  }

  checkFields() {
    if (!this.createPolicyForm.valid) {
      return this.createPolicyForm.markAllAsTouched()
    }
    if (this.areAllFieldsAssigned()) {
      const mappingPolicyModel: PolicyMappingModel = {
        pkIMappingPolicy: 0,
        fkIIdFormModule: this.idForm,
        fkIIdInsuranceCompany: this.createPolicyForm.get('insurancesList')?.value,
        fkIIdState: this.createPolicyForm.get('statePolicyList')?.value,
        fkIIdProduct: this.createPolicyForm.get('productList')?.value,
        fkIIdPolicy: this.createPolicyForm.get('WTWIdList')?.value,
        bPolicyActive: this.createPolicyForm.get('active')?.value,
        bActive: true,
        standardFields: this.policyFields,
        insuredFields: this.insuredFields,
        takerFields: this.takerFields,
        beneficiaryFields: this.beneficiaryFields,
        othersFields: this.othersFields
      }
      this._moduleService.savePolicyMappings(mappingPolicyModel).pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      ).subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          return
        }

        if (resp.result) {
          this._messageService.messageSaveMapping(this._translateService.instant('MappingForm.SaveMappingMessagePositive'), "", "success");

        } else {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'),
            this._translateService.instant('AnsConfiguration.Reports.MessageReportNotFound'));
          this.resetSelects();
        }
      });
    } else {
      this._messageService.messageSaveMapping(this._translateService.instant('MappingForm.SaveMappingTitleNegative'), this._translateService.instant('MappingForm.SaveMappingMessageNegative'), "warning");
    }
  }



  mappingDataToTable(dataFields: PolicyMappingFieldModel[]) {
    if (dataFields == null) return [];
    this.defaultMappings = [];
    for (let i = 0; i < dataFields.length; i++) {
      const field = dataFields[i];
      const selectedField = this.formFields.find(f => f.pkIIdFieldModule === field.fkIIdFieldModule);
      this.defaultMappings.push({
        iIdFormField: field.fkIIdFieldModule,
        vFormField: selectedField?.vNameField!,
        iIdPolicyField: field.fkIIdFieldModule,
        vPolicyField: field.vNameStandardField,
        bState: field.iStatus == 1 ? this._translateService.instant('MappingForm.AssignedStatus') : this._translateService.instant('MappingForm.UnassignedStatus'),
        bMandatory: field.bIsMandatory
      });
    }
    return this.defaultMappings;
  }


  mappingChanges(selectedField: FormMappingFieldModel | undefined, index: number, mapping: PolicyMappingTableModel[], fields: PolicyMappingFieldModel[]) {
    if (selectedField) {
      mapping[index].iIdFormField = selectedField.pkIIdFieldModule;
      mapping[index].vFormField = selectedField.vNameField;
      mapping[index].bState = this._translateService.instant('MappingForm.AssignedStatus');

      fields[index].fkIIdFieldModule = selectedField.pkIIdFieldModule;
      fields[index].iStatus = 1;
    }
  }

  UnassingedField(index: number, mapping: PolicyMappingTableModel[]) {
    mapping[index].iIdFormField = 0;
    mapping[index].vFormField = '';
    mapping[index].bState = this._translateService.instant('MappingForm.UnassignedStatus')
    this._cdr.detectChanges();
  }

  /**
 * Maps a boolean value.
 * @param { PolicyMappingTableModel } field field
 * @returns { boolean } If the received value is true, it returns yes, if it is false, it returns no
 */
  parseRequiredField(field: PolicyMappingTableModel) {
    return field.bMandatory ? this._translateService.instant('MappingForm.AffirmativeValue') : this._translateService.instant('MappingForm.NegativeValue');
  }

  /**
   * get products by insurance id and business country
   * @returns all products associated to the insurance
   */
  getAllProductsByInsurance() {
    this.idInsurance = this.createPolicyForm.get('insurancesList')?.value;
    this._policyService.getProductsByIdInsuranceCompany(this.idInsurance, this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.resetSelects();
          this.listOfProducts = resp.result;
        }
        this.showTable = false;
      });
  }



  //Obtiene los beneficiarios de una póliza.
  getTabsWithFieldsByIdPolicy() {
    const idPolicy = this.createPolicyForm.get("WTWIdList")?.value;
    this._fieldService
      .getTabsWithFieldsByIdPolicy(idPolicy)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          const filtered = resp.result.filter((item:any) => item.vName !== "Asegurado" && item.vName !== "Beneficiario" && item.vName !== "Tomador");
          this.tabName = filtered[0].vName
        }
      });
  }
}
