@import '/src/assets/styles/variables';


.label-Email{
	font-family: Inter ;
	font-weight: 600;	
	font-size: 16px;	
	letter-spacing: -0.012em;
	float: left;
	line-height: 44px;	
}
.container {
	min-height: calc(100vh - 90px);
	display: flex;
	justify-content: center;
	align-items: center;
	h2 {
		font-family: $font-family_1;
		font-size: 32px;
		font-weight: 700;
		line-height: 39px;
		letter-spacing: -0.012em;
		text-align: left;
	}
	form {
		width: 90%;
		max-width: 450px;
		background: $color_2;
		border: 1px solid $color_1;
		
		justify-content: center;
		padding: 40px 32px;
		margin: 0 auto;
		flex-wrap: wrap;
		margin-top: 32px;
		margin-bottom: 32px;
		position: relative;
		legend {
			font-family: $font-family_1;
			font-size: 24px;
			font-weight: 700;
			line-height: 39px;
			letter-spacing: -0.012em;
			width: 100%;
			display: flex;
			text-align: center;
			justify-content: center;
		}
		legend.h4 {
			font-family: $font-family_1;
			font-size: 24px;
			font-weight: 700;
			line-height: 29px;
			letter-spacing: -0.012em;
			text-align: left;
			justify-content: start;
		}
		a.link {
			font-family: $font-family_1;
			font-size: 16px;
			font-weight: 400;
			line-height: 19px;
			letter-spacing: -0.03em;
			text-align: left;
			width: 100%;
			display: flex;
			justify-content: center;
			text-align: center;
			text-decoration: underline;
			color: $color_1;
		}
		span {
			position: absolute;
			z-index: -1;
		}
		span.top {
			top: -30px;
			right: -30px;
		}
		span.bottom {
			bottom: -30px;
			left: -30px;
		}
	}
	form.md {
		max-width: 653px;
	}
	.register {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		h1 {
			font-size: 30px;
			font-weight: bold;
			line-height: 64px;
			letter-spacing: -0.01em;
			text-align: center;
			color: $color_1;
		}
	}
	.explanation {
		position: relative;
		max-width: 582px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		overflow: hidden;
		height: 100%;
		img {
			position: absolute;
			z-index: 1;
		}
		div {
			position: relative;
			width: 80%;
			max-width: 444px;
			background: $color_2;
			z-index: 2;
			background: $color_2;
			padding: 24px;
		}
		h1 {
			font-family: $font-family_2;
			font-size: 48px;
			font-weight: 500;
			line-height: 64px;
			letter-spacing: -0.01em;
			text-align: center;
			color: $color_1;
		}
		p {
			font-family: $font-family_1;
			font-size: 16px;
			font-weight: 600;
			line-height: 19px;
			letter-spacing: 0em;
			text-align: center;
			margin: 0;
			margin-top: 16px;
		}
		ul {
			width: 100%;
			max-width: 290px;
			margin: 0 auto;
			margin-top: 16px;
			li {
				font-family: $font-family_1;
				font-size: 16px;
				font-weight: 600;
				line-height: 19px;
				letter-spacing: 0em;
				text-align: left;
			}
		}
	}
}