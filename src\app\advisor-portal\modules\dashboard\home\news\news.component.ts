import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SectionIndexModel } from 'src/app/shared/models/configuration-index';
import { NewModel } from 'src/app/shared/models/business';
import { Router } from '@angular/router';

@Component({
  selector: 'app-news-home',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule, TranslateModule],
  templateUrl: './news.component.html',
  styleUrls: ['./news.component.scss'],
})
export class NewsHomeComponent implements OnInit, OnDestroy {
  @Input() newsObject: SectionIndexModel = SectionIndexModel.fromObj({});
  news: NewModel[] = [];
  constructor(
    private _businessService: BusinessService,
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    private _router: Router
  ) {}

  ngOnInit(): void {
    this.getNewsByIdSectionAdvisor();
  }

  getNewsByIdSectionAdvisor() {
    if (this.newsObject.pkIIdSectionIndexBusiness !== 0) {
      this._businessService
        .getNewsByIdSectionAdvisor(this.newsObject.pkIIdSectionIndexBusiness)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this.news = resp.result;
              this.getBase64Format();
            }
          }
        });
    }
  }

  getBase64Format() {
    if (this.news) {
      this.news.forEach((element: NewModel, index: number) => {
        element.imageBase64 = `data:image/${
          element.fileName.split('.')[element.fileName.split('.').length - 1]
        };base64,${element.imageBase64}`;
      });
    }
  }

  gotToNew(link: string) {
    window.open(link, '_blank');
  }

  ngOnDestroy(): void {}
}
