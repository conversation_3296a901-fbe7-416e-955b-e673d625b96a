<ng-container *ngIf="isConfig">
    <div class="row mt-5">
        <div class="col-md-12">
            <h4>{{"PolicyConfiguration.Coverage.CoverageConfiguration" | translate}}</h4>
            <p class="description">{{"PolicyConfiguration.Coverage.OptionalCoverage" | translate}}</p>
        </div>
    </div>
</ng-container>

<ng-container *ngIf="!isConfig">
    <div class="row mt-5">
        <div class="col-md-12">
            <h4>{{"PolicyConfiguration.Coverage.Title" | translate}}</h4>
        </div>
    </div>
</ng-container>
<form [formGroup]="formCoverage">
    <div class="row mt-5">
        <!-- Nombre de la cobertura -->
        <div class="col-md-12 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ "PolicyConfiguration.Coverage.CoverageName" | translate }}
                </mat-label>
                <input matInput formControlName="coverageName" PreventionSqlInjector />
                <mat-error *ngIf="formCoverage.get('coverageName')?.hasError('required')">{{ "ThisFieldIsRequired" |
                    translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <!-- Descripción de la cobertura -->
        <div class="col-md-12 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ "PolicyConfiguration.Coverage.CoverageDescription" | translate }}
                </mat-label>
                <textarea matInput formControlName="coverageDescription" PreventionSqlInjector></textarea>
            </mat-form-field>
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-md-12 w-auto">
            <button (click)="addCoverage()" class="btn-custom w-100" type="submit" mat-raised-button>
                <strong>{{ "PolicyConfiguration.Coverage.AddCoverage" | translate }}</strong>
                <mat-icon fontIcon="add"></mat-icon>
            </button>
        </div>
    </div>
</form>

<div class="row">
    <div class="col-md-12">
        <!-- datatable Pólizas -->
        <div class="row mt-2">
            <app-table *ngIf="true" [displayedColumns]="estructTableCoverage" [data]="dataTableCoverage"
                (iconClick)="controller($event)"></app-table>
        </div>
    </div>
</div>