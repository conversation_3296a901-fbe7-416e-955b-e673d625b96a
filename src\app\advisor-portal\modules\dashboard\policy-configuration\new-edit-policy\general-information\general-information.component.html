<div class="row mt-5">
    <div class="col-md-12">
        <h4>{{"PolicyConfiguration.GeneralInformation.Title" | translate}}</h4>
    </div>
</div>

<form [formGroup]="form">
    <!-- Activar  póliza -->
    <div class="row mt-4 mb-2">
        <div class="col-md-6 col-sm-12">
            <mat-slide-toggle class="mb-3" formControlName="Active" (change)="onToggleChange($event)">
                {{"PolicyConfiguration.GeneralInformation.ActivePolicy" | translate}}
            </mat-slide-toggle>
        </div>
        <!-- Asociada a crédito -->
        <div class="col-md-6">
            <mat-slide-toggle class="mb-3" formControlName="BAssociatedWithCredit">
                {{"Asociada a crédito" | translate}}
            </mat-slide-toggle>
        </div>
    </div>
    <div class="row">
        <!-- Nombre póliza -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ "PolicyConfiguration.GeneralInformation.PolicyName" | translate }}
                </mat-label>
                <input matInput formControlName="PolicyName" PreventionSqlInjector />
                <mat-error *ngIf="utilsSvc.isControlHasError(form, 'PolicyName', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <!-- ID WTW -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ "PolicyConfiguration.GeneralInformation.IdWTW" | translate }}
                </mat-label>
                <input matInput formControlName="IdPolicy" PreventionSqlInjector type="number" />
            </mat-form-field>
        </div>
    </div>
    <div class="row">
        <!-- Producto -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="fill" class="w-100">
                <mat-label>
                    {{ "PolicyConfiguration.GeneralInformation.Product" | translate }}
                </mat-label>
                <mat-select formControlName="IdProduct">
                    <mat-option *ngFor="let product of productList" [value]="product.pkIIdProduct">
                        {{ product.vProductName }}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="utilsSvc.isControlHasError(form, 'IdProduct', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <!-- Número de póliza -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ "PolicyConfiguration.GeneralInformation.PolicyNumber" | translate }}
                </mat-label>
                <input matInput formControlName="PolicyNumber" PreventionSqlInjector />
                <mat-error *ngIf="utilsSvc.isControlHasError(form, 'PolicyNumber', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
    </div>
    <div class="row">
        <!-- Aseguradora -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field class="w-100" appearance="fill">
                <mat-label>
                    {{"PolicyConfiguration.GeneralInformation.Insurance" | translate}}
                </mat-label>
                <mat-select formControlName="IdInsurance">
                    <mat-option *ngFor="let insurance of insuranceList" [value]="insurance.pkIIdInsuranceCompanies">
                        {{ insurance.vName }}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="utilsSvc.isControlHasError(form, 'IdInsurance', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <!-- Documentos soporte -->
        <div class="col-md-6 col-sm-12 mb-1" *ngIf="!showByPolicyType">
            <mat-form-field class="w-100 custom-input">
                <!-- <mat-label>
                    {{"PolicyConfiguration.GeneralInformation.SupportingDocuments" | translate}}
                </mat-label> -->
                <input #fileInput (onChangeValidated)="onFileChange($event)" [multiple]="true" type="file" style="display: none;"
                    ValidationInputFile [allowedExtensions]="fileTypes" [maxFileSizeMB]="maxFileSizeMB"/>
                <div class="cont-items-file mt-2">
                    <div class="cont-btn-name-file">
                        <mat-icon matTooltip="file" class="click" (click)="fileInput.click()" mat-icon-button
                            matSuffix>attach_file</mat-icon>
                        <input formControlName="Files" type="text" readonly matInput [value]="selectedFileName"
                            class="truncate-input">
                    </div>
                    <div>
                        <button *ngIf="showBtnDownload" (click)="downloadDocument()"
                            class="downloand-color click prevent-disabled-icon">
                            <mat-icon class="material-symbols-outlined" mat-icon-button matSuffix>download</mat-icon>
                        </button>
                        <button *ngIf="showBtnDelete" (click)="deleteDocument()"
                            class="downloand-color click prevent-disabled-icon mx-2">
                            <mat-icon class="material-symbols-outlined" mat-icon-button matSuffix>delete</mat-icon>
                        </button>
                    </div>
                </div>
            </mat-form-field>
            <p class="description">
                {{"PolicyConfiguration.GeneralInformation.Files"| translate }} PDF, XLS, SVG, PNG, JPEG,
                {{"PolicyConfiguration.GeneralInformation.MaximumSize"| translate }} 20 MB
            </p>
        </div>
        <!-- Tipo de vigencia -->
        <div class="col-md-6 col-sm-12 mb-2" *ngIf="showByPolicyType">
            <mat-form-field class="w-100" appearance="fill">
                <mat-label>
                    {{"PolicyConfiguration.GeneralInformation.ValidityType" | translate}}
                </mat-label>
                <mat-select formControlName="IdValidityType">
                    <mat-option *ngFor="let item of typeOfValidity" [value]="item.value">
                        {{ item.name }}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="utilsSvc.isControlHasError(form, 'IdValidityType', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
    </div>
    <div class="row" *ngIf="showByPolicyType">
        <!-- Inicio de vigencia -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{"PolicyConfiguration.GeneralInformation.StartValidity" | translate}}
                </mat-label>
                <input matInput [matDatepicker]="StartValidity" formControlName="StartValidity" />
                <mat-datepicker-toggle matIconSuffix [for]="StartValidity"></mat-datepicker-toggle>
                <mat-datepicker #StartValidity></mat-datepicker>
            </mat-form-field>
            <mat-error *ngIf="utilsSvc.isControlHasError(form, 'StartValidity', 'required')">
                {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
        </div>
        <!-- Fin de vigencia -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{"PolicyConfiguration.GeneralInformation.EndValidity" | translate}}
                </mat-label>
                <input matInput [matDatepicker]="EndValidity" formControlName="EndValidity" />
                <mat-datepicker-toggle matIconSuffix [for]="EndValidity"></mat-datepicker-toggle>
                <mat-datepicker #EndValidity></mat-datepicker>
            </mat-form-field>
            <mat-error *ngIf="utilsSvc.isControlHasError(form, 'EndValidity', 'required')">
                {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
        </div>
    </div>
    <div class="row" *ngIf="showByPolicyType">
        <!-- tipo de documento -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="fill" class="w-100">
                <mat-label>
                    {{ 'PolicyConfiguration.GeneralInformation.DocumentType' | translate }}
                </mat-label>
                <mat-select formControlName="IdDocumentType">
                    <mat-option *ngFor="let document of documentsTypes" [value]="document.id">
                        {{ document.name }}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="utilsSvc.isControlHasError(form, 'IdDocumentType', 'required')">
                    {{ 'ThisFieldIsRequired' | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <!-- Número de identificación -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ "PolicyConfiguration.GeneralInformation.DocumentNumber" | translate }}
                </mat-label>
                <input matInput formControlName="DocumentNumber" PreventionSqlInjector />
                <mat-error *ngIf="utilsSvc.isControlHasError(form, 'DocumentNumber', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
    </div>
    <div class="row">
        <!-- Tomador -->
        <div class="col-md-6 col-sm-12" *ngIf="showByPolicyType">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ "PolicyConfiguration.GeneralInformation.PolicyHolder" | translate }}
                </mat-label>
                <input matInput formControlName="PolicyHolder" PreventionSqlInjector />
                <mat-error *ngIf="utilsSvc.isControlHasError(form, 'PolicyHolder', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <!-- Documentos soporte -->
        <div class="col-md-6 col-sm-12 mb-1" *ngIf="showByPolicyType">
            <mat-form-field class="w-100 custom-input">
                <!-- <mat-label>
                    {{"PolicyConfiguration.GeneralInformation.SupportingDocuments" | translate}}
                </mat-label> -->
                <input #fileInput (change)="onFileChange($event)" [multiple]="true" type="file"
                    ValidationInputFile [allowedExtensions]="fileTypes" [maxFileSizeMB]="maxFileSizeMB" style="display: none;"/>
                <div class="cont-items-file mt-2">
                    <div class="cont-btn-name-file">
                        <mat-icon matTooltip="file" class="click" (click)="fileInput.click()" mat-icon-button
                            matSuffix>attach_file</mat-icon>
                        <input formControlName="Files" type="text" readonly matInput [value]="selectedFileName"
                            class="truncate-input">
                    </div>
                    <div>
                        <button *ngIf="showBtnDownload" (click)="downloadDocument()"
                            class="downloand-color click prevent-disabled-icon">
                            <mat-icon class="material-symbols-outlined" mat-icon-button matSuffix>download</mat-icon>
                        </button>
                        <button *ngIf="showBtnDelete" (click)="deleteDocument()"
                            class="downloand-color click prevent-disabled-icon mx-2">
                            <mat-icon class="material-symbols-outlined" mat-icon-button matSuffix>delete</mat-icon>
                        </button>
                    </div>
                </div>
            </mat-form-field>
            <p class="description">
                {{"PolicyConfiguration.GeneralInformation.Files"| translate }} PDF, XLS, SVG, PNG, JPEG,
                {{"PolicyConfiguration.GeneralInformation.MaximumSize"| translate }} 20 MB
            </p>
        </div>
    </div>
</form>