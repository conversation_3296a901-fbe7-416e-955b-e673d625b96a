import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { BodyTableModel } from 'src/app/shared/models/table';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { PdfViewerModule } from 'ng2-pdf-viewer';

@Component({
  selector: 'app-policy-data',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    TranslateModule,
    FormsModule,
    TableComponent,
    MatCardModule,
    MatProgressSpinnerModule,
    PdfViewerModule
  ],
  templateUrl: './policy-data.component.html',
  styleUrls: ['./policy-data.component.scss']
})
export class PolicyDataComponent implements OnInit{
  @Input() quoteId: number = 0;

  columnsPaymentTable: BodyTableModel[] = [];
  paymentData: any[] = [];
  pdfSrc: string = '';
  isPDFLoading: boolean = true;
  activePdfBtn: boolean = true;

  pageIndex: number = 0;
  pageSize: number = 10;
  amountRows: number = 0;

  constructor(
    private _policyService: PolicyService
  ){}
  public ngOnInit(): void {
    this._getPayments();
    this._getPDF();
  }

  private _getPDF(){
    this._policyService.getGeneratePdfPolicy(this.quoteId)
    .subscribe((data: Blob) => {
      if (data) {
        const fileURL = URL.createObjectURL(data);
        this.pdfSrc = fileURL;
      }
      this.isPDFLoading = false;
      this.activePdfBtn = false;
    })
  }
  private _getPayments(){
    this._policyService.getPolicyDataFromQuote(this.quoteId).subscribe({
      next: ((resp: any) => {
        this._formatColumnsPaymentTable(resp.result.vCalendarConfigColumns)
        this._formatPaymentTableData(resp.result.vCalendar)
      })
    })
  }

  public downloadPDF(): void {
    window.open(this.pdfSrc, '_blank');
  }

  private _formatColumnsPaymentTable(paymentColumns: string){
    const jsonData = JSON.parse(paymentColumns)
    jsonData.forEach((element: { Key: any; Value: any; Colors: any | null; }) => {
      let column: any = {
        columnLabel: element.Key,
        columnValue: element.Value
      }
      if (element.Colors)
        column['columnColors'] = element.Colors
      this.columnsPaymentTable.push(column)
    });
  }

  private _formatPaymentTableData(paymentsData: string){
    const data = JSON.parse(paymentsData)
    let result: any[] = []
    let configuredColumns: string[] = []
    this.columnsPaymentTable.forEach(column => {
      configuredColumns.push(column.columnValue)
    })
    data.forEach((element: any) => {
      let value: any = {}
      configuredColumns.forEach(column => {
        if (column in element)
          value[column] = element[column]
      })
      result.push(value)
    })
    this.paymentData = [...data]
  }
}
