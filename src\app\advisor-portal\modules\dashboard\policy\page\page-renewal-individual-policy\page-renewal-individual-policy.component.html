<form [formGroup]="form">    
    <div class="row">
        <!-- ID WTW -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label> {{ "PolicyConfiguration.GeneralInformation.IdWTW" | translate }} </mat-label>
                <input matInput formControlName="IdPolicy" PreventionSqlInjector type="number" />
            </mat-form-field>
        </div>
        <!-- Producto -->
        <div class="col-md-6 col-sm-12 mb-2">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label> {{ "PolicyConfiguration.GeneralInformation.Product" | translate }} </mat-label>
                <input matInput formControlName="ProductName" PreventionSqlInjector type="text" />
            </mat-form-field>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-12">
            <mat-tab-group #tabGroup (selectedTabChange)="onTabChange($event)">
                <mat-tab label="{{ 'Policy.PolicyData' | translate }}">
                    <div class="row mt-5 mb-5">
                        <div class="col-md-12">
                            <h4 class="bold">{{"Policy.PolicyData" | translate}}</h4>
                        </div>
                    </div>
                    <!-- Prorroga y endoso  póliza -->
                    <div class="row mt-4 mb-2">
                        <div class="col-md-6 col-sm-12 mr-2">
                            <mat-slide-toggle class="mb-3" formControlName="Extension"> {{"Policy.Extension" | translate}} </mat-slide-toggle>
                            <mat-slide-toggle class="mb-3 mx-3" formControlName="Endorsement"> {{"Policy.Endorsement" | translate}} </mat-slide-toggle>
                        </div>
                    </div>
                    <div class="row">
                        <!-- Año de Renovación -->
                        <div class="col-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{ "PolicyConfiguration.GeneralInformation.YearRenewal" | translate }} </mat-label>
                                <input matInput formControlName="YearRenewal" PreventionSqlInjector type="number" />
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'YearRenewal', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="row">
                        <!-- Número de póliza -->
                        <div class="col-md col-sm-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{ "PolicyConfiguration.GeneralInformation.PolicyNumber" | translate }} </mat-label>
                                <input matInput formControlName="PolicyNumber" PreventionSqlInjector type="text"/>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'PolicyNumber', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                        <!-- Aseguradora -->
                        <div class="col-md-6 col-sm-12 mb-2">
                            <mat-form-field class="w-100" appearance="fill">
                                <mat-label> {{"PolicyConfiguration.GeneralInformation.Insurance" | translate}} </mat-label>
                                <mat-select formControlName="IdInsurance" (selectionChange)="onInsuranceChange()">
                                    <mat-option *ngFor="let insurance of insuranceList" [value]="insurance.idParent"> {{ insurance.vName }} </mat-option>
                                </mat-select>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'IdInsurance', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="row">                        
                        <!-- Inicio de vigencia -->
                        <div class="col-md-6 col-sm-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{"PolicyConfiguration.GeneralInformation.StartValidity" | translate}} </mat-label>
                                <input matInput [matDatepicker]="StartValidity" formControlName="StartValidity" />
                                <mat-datepicker-toggle matIconSuffix [for]="StartValidity"></mat-datepicker-toggle>
                                <mat-datepicker #StartValidity></mat-datepicker>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'StartValidity', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                        <!-- Fin de vigencia -->
                        <div class="col-md-6 col-sm-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{"PolicyConfiguration.GeneralInformation.EndValidity" | translate}} </mat-label>
                                <input matInput [matDatepicker]="EndValidity" formControlName="EndValidity" [min]="this.form.get('StartValidity')?.value"/>
                                <mat-datepicker-toggle matIconSuffix [for]="EndValidity"></mat-datepicker-toggle>
                                <mat-datepicker #EndValidity></mat-datepicker>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'EndValidity', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="row">
                        <!-- Prima -->
                        <div class="col-md col-sm-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{ "iPremium" | translate }} </mat-label>
                                <input matInput formControlName="Premium" PreventionSqlInjector type="number"/>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'Premium', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                        <!-- Valor asegurado -->
                        <div class="col-md-6 col-sm-12 mb-2">
                            <mat-form-field class="w-100" appearance="fill">
                                <mat-label> {{"Policy.InsuredValue" | translate}} </mat-label>
                                <input matInput formControlName="InsuredValue" PreventionSqlInjector type="number"/>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'InsuredValue', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="row">
                        <!-- Periodicidad de pago -->
                        <div class="col-md col-sm-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{ "vPaymentFrequency" | translate }} </mat-label>
                                <input matInput formControlName="PaymentFrequency" PreventionSqlInjector type="text"/>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'PaymentFrequency', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                        <!-- Número de pagos -->
                        <div class="col-md-6 col-sm-12 mb-2">
                            <mat-form-field class="w-100" appearance="fill">
                                <mat-label> {{"iPaymentsNumber" | translate}} </mat-label>
                                <input matInput formControlName="PaymentsNumber" PreventionSqlInjector type="number"/>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'PaymentsNumber', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="row">
                        <!-- Número de crédito -->
                        <div *ngIf="hasCredit" class="col-md col-sm-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{ "creditNumber" | translate }} </mat-label>
                                <input matInput formControlName="CreditNumber" PreventionSqlInjector type="text"/>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'CreditNumber', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                        <!-- Fecha de desembolso -->
                        <div *ngIf="hasCredit" class="col-md-6 col-sm-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{"dDisbursementDate" | translate}} </mat-label>
                                <input matInput [matDatepicker]="DisbursementDate" formControlName="DisbursementDate" />
                                <mat-datepicker-toggle matIconSuffix [for]="DisbursementDate"></mat-datepicker-toggle>
                                <mat-datepicker #DisbursementDate></mat-datepicker>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'DisbursementDate', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="row">
                        <!-- Fecha final de crédito -->
                        <div *ngIf="hasCredit" class="col-md-6 col-sm-12 mb-2">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label> {{"dDueDate" | translate}} </mat-label>
                                <input matInput [matDatepicker]="DueDate" formControlName="DueDate" [min]="this.form.get('DisbusementDate')?.value"/>
                                <mat-datepicker-toggle matIconSuffix [for]="DueDate"></mat-datepicker-toggle>
                                <mat-datepicker #DueDate></mat-datepicker>
                                <mat-error *ngIf="_utilService.isControlHasError(form, 'DueDate', 'required')"> {{ "ThisFieldIsRequired" | translate }} </mat-error>
                            </mat-form-field>
                        </div>
                        <!-- Carátula -->
                        <div class="col-md-6 col-sm-12 mb-1">
                            <mat-form-field class="w-100 custom-input">
                                <mat-label>
                                    <mat-icon matTooltip="file" class="click" mat-icon-button matSuffix>attach_file</mat-icon>
                                    {{"Policy.PolicyCover" | translate}}
                                </mat-label>
                                <input #fileInput (onChangeValidated)="onFileChange($event)" [multiple]="false" type="file" style="display: none;" ValidationInputFile [allowedExtensions]="['pdf']" [maxFileSizeMB]="20"/>
                                <div class="cont-items-file mt-2">
                                    <div class="cont-btn-name-file">
                                        <input formControlName="Files" type="text" readonly matInput [value]="selectedFileName" class="truncate-input">
                                    </div>
                                    <div>
                                        <button *ngIf="!showBtnDownload && !showBtnDelete" (click)="fileInput.click()" class="downloand-color click prevent-disabled-icon">
                                            <mat-icon class="material-symbols-outlined" mat-icon-button matSuffix>upload</mat-icon>
                                        </button>
                                        <button *ngIf="showBtnDownload" (click)="downloadDocument()" class="downloand-color click prevent-disabled-icon">
                                            <mat-icon class="material-symbols-outlined" mat-icon-button matSuffix>download</mat-icon>
                                        </button>
                                        <button *ngIf="showBtnDelete" (click)="deleteDocument()" class="downloand-color click prevent-disabled-icon mx-2">
                                            <mat-icon class="material-symbols-outlined" mat-icon-button matSuffix>delete</mat-icon>
                                        </button>
                                    </div>
                                </div>
                            </mat-form-field>
                            <p class="description"> {{"PolicyConfiguration.GeneralInformation.Files"| translate }} PDF {{"PolicyConfiguration.GeneralInformation.MaximumSize"| translate }} 20 MB </p>
                        </div>
                    </div>
                </mat-tab>

                <!-- Riesgos -->
                <div *ngFor="let tab of dynamicTabs">
                    <mat-tab label="{{ tab | translate }}">
                        <ng-container *ngIf="tab !== BENEFICIARY; else Beneficiary">
                            <app-details-risk-modifications [dataForm]="getDataFormByCustomerType(tab)" [idPolicy]="idWtw" [idParent]="idParent" [customerType]="tab" (formValue)="getFormValue($event, tab)"></app-details-risk-modifications>
                        </ng-container>
                        <ng-template #Beneficiary>
                            <!-- Tabla Beneficiarios -->
                            <app-beneficiaries-table [estructTable]="estructTableBeneficiariesModify" [showDetailsColumn]="true" [beneficiariesData]="beneficiariesData"
                            (actionTable)="getActionTableBeneficiaries($event)"></app-beneficiaries-table>
                            <!-- Agregar beneficiario -->
                            <button class="w-auto" type="button" mat-raised-button color="primary" (click)="addBeneficiary()">
                                {{ "Policy.AddBeneficiary" | translate }}
                                <mat-icon fontIcon="add"></mat-icon>
                            </button>
                        </ng-template>
                    </mat-tab>
                </div>
                
            </mat-tab-group>
        </div>
    </div>
    <div class="d-flex justify-content-center mt-5">
        <a class="label-button mx-3" mat-button (click)="cancel()"><span>{{"Cancel" | translate}}</span>
            <mat-icon fontIcon="arrow_back"></mat-icon>
        </a>
        <!-- Botón Continuar -->
        <button *ngIf="!isLastTab" type="button" mat-raised-button color="primary" (click)="goToNextTab()">
            <mat-icon iconPositionEnd fontIcon="arrow_forward"></mat-icon>
            {{ "Continue" | translate }}
        </button>

        <!-- Botón Guardar cambios -->
        <button *ngIf="isLastTab" type="button" mat-raised-button color="primary" (click)="saveChange()">
            <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
            {{ "SaveChanges" | translate }}
        </button>
    </div>

</form>

<!-- Modal para beneficiarios -->
<ng-template #beneficiaryModal>
    <app-modal2 [titleModal]="beneficiaryModalTitle | translate">
        <ng-container body>
            <app-details-risk-modifications [dataForm]="riskBeneficiary.valueForm" [idPolicy]="idWtw" [idParent]="idParent" [customerType]="'Beneficiario'" (formValue)="getFormValue($event, 'Beneficiario')"></app-details-risk-modifications>
        </ng-container>

        <!-- Botón Modificar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary" [disabled]="!riskBeneficiary.isValid"
                (click)="createOrUpdateBeneficiary()">
                {{ "Save" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>