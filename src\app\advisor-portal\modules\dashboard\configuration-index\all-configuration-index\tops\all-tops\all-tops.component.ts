import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { EditCreateTopComponent } from '../edit-create-top/edit-create-top.component';
import { MatDialog } from '@angular/material/dialog';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { SectionIndexModel } from 'src/app/shared/models/configuration-index/section-index.model';
import { SectionEnum } from 'src/app/shared/models/configuration-index/section-enum.model';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { TopModel } from 'src/app/shared/models/business/top.model';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';

@Component({
  selector: 'app-all-tops',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    Modal2Component,
    EditCreateTopComponent
  ],
  templateUrl: './all-tops.component.html',
  styleUrls: ['./all-tops.component.scss']
})
export class AllTopsComponent implements OnInit {

  @ViewChild('editNewTopModal') editNewTopModal?: TemplateRef<any>;
  @Input() idBusinessByCountry: number = 0;
  @Input() objetTop: any = {};
  
  topsCount: number = 0;
  topId: number = 0;
  topStyle : string = "one";

  topModel: any;
  listSectionIndex: SectionIndexModel[] = [];
  formValid: boolean = false;
  //Lista de tops que se muestran 
  topList: TopModel[] = []

  constructor(
    private _businessService: BusinessService,
    private _messageService: MessageService,
    private _modalDialog: MatDialog,
    private _translateService: TranslateService
  ) { }

  ngOnInit(): void {
    this.getTops();
  }

  //abre el modal de crear editar top
  openEditNewTopDialog(idTop: number = 0) {    
    this.topId = idTop;
    const dialogRef = this._modalDialog.open(this.editNewTopModal!,{
      width: '40vw',
      maxHeight: '90vh',
    });    
  }

  //obtiene el model del top que viene desde index
  gettopModel(event: any) {
    this.topModel = event;    
  }

  //obtiene el listado de tops
  getTops(){
    this._businessService.getTopsBySection(this.objetTop.pkIIdSectionIndexBusiness)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message            
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('No existe ningún top.');
          this.topsCount = 0;
          this.topList = [];          
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message              
            );
          } else {
            this.topList = resp.result; 
            this.topsCount = this.topList.length;

            for(let item of this.topList){
              item.aJsonElementsArray = [];
              switch(JSON.parse(item.vJsonElements).length)
              {
                case 5:
                  item.aJsonElementsArray.push({positionText : 'one',positionName : JSON.parse(item.vJsonElements)[0]})
                  item.aJsonElementsArray.push({positionText : 'two',positionName : JSON.parse(item.vJsonElements)[1]})
                  item.aJsonElementsArray.push({positionText : 'three',positionName : JSON.parse(item.vJsonElements)[2]})
                  item.aJsonElementsArray.push({positionText : 'four',positionName : JSON.parse(item.vJsonElements)[3]})
                  item.aJsonElementsArray.push({positionText : 'five',positionName : JSON.parse(item.vJsonElements)[4]})
                  break;
                case 4:
                  item.aJsonElementsArray.push({positionText : 'one',positionName : JSON.parse(item.vJsonElements)[0]})
                  item.aJsonElementsArray.push({positionText : 'two',positionName : JSON.parse(item.vJsonElements)[1]})
                  item.aJsonElementsArray.push({positionText : 'three',positionName : JSON.parse(item.vJsonElements)[2]})
                  item.aJsonElementsArray.push({positionText : 'four',positionName : JSON.parse(item.vJsonElements)[3]})
                  break;
                case 3: 
                  item.aJsonElementsArray.push({positionText : 'one',positionName : JSON.parse(item.vJsonElements)[0]})
                  item.aJsonElementsArray.push({positionText : 'two',positionName : JSON.parse(item.vJsonElements)[1]})
                  item.aJsonElementsArray.push({positionText : 'three',positionName : JSON.parse(item.vJsonElements)[2]})
                  break;
                case 2:
                  item.aJsonElementsArray.push({positionText : 'one',positionName : JSON.parse(item.vJsonElements)[0]})
                  item.aJsonElementsArray.push({positionText : 'two',positionName : JSON.parse(item.vJsonElements)[1]})
                  break;
                default: 
                  item.aJsonElementsArray.push({positionText : 'one',positionName : JSON.parse(item.vJsonElements)[0]})
                  break;
              }
            }
          }
        }
      });
  }

  //funcion al dar click en guardar, la cual va a añadir o editar según corresponda
  saveChanges_Click(){
    if(this.topModel != 'Invalid form'){      
      if(this.topId == 0)
      {
        this.addTop();
      }
      else
      {
        this.editTop();
      }
    }
    else{
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm')
      );
    }

  }

  //función que consume el servicio de crear top
  addTop(){    
    this.topModel.fkIIdSectionIndexBusiness = this.objetTop.pkIIdSectionIndexBusiness
    this._businessService.createTop(this.topModel).subscribe((resp) => {
      if (!resp.error) {
        this._messageService.messageSuccess(
          this._translateService.instant("Saved"),
          ''
        );
        this.getTops();
        this._modalDialog.closeAll();
      }
      else {
        this._messageService.messageError(
          this._translateService.instant("ThereWasAError") + ": " + resp.message,
        );
      }
    });    
  }

  //función que consume el servicio de actualizar top
  editTop(){
    this.topModel.pkIIdTop = this.topId;
    this.topModel.fkIIdSectionIndexBusiness = this.objetTop.pkIIdSectionIndexBusiness;
    this._businessService.updateTop(this.topModel).subscribe((resp) => {
      if (!resp.error) {
        this._messageService.messageSuccess(
          this._translateService.instant("Saved"),
          ''
        );
        this.getTops();
        this._modalDialog.closeAll();
      }
      else {
        this._messageService.messageError(
          this._translateService.instant("ThereWasAError") + ": " + resp.message,
        );
      }
    });
  }

  //función que consume el servicio de eliminar top
  removeTop_Click(){
    this._businessService.removeTopFromHome(this.topId).subscribe((resp) =>{
      this._messageService.messageSuccess(
        this._translateService.instant(resp.message),
        ''
      );
      this.getTops();
      this._modalDialog.closeAll();
    })
  }
}
