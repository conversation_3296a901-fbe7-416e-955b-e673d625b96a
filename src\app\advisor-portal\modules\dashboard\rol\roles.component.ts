import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-roles',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule,BreadcrumbComponent, MatIconModule, MatTooltipModule],
  template: `
  <div class="title">
    <h2 class="h3">
      <img src="assets/img/layouts/config_ico.svg" alt="" /> 
      {{"Role.RolesSettings" | translate}}
      <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.RolesSetting' | translate }}">help_outline</mat-icon>
    </h2>
  </div>
  <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

  <router-outlet></router-outlet>
  `,    
  styleUrls: []
})
export class RolesComponent implements OnInit {

  constructor(
    private _translateService: TranslateService,
  ) {}

  inicio: string = this._translateService.instant("Inicio")  
  roles: string = this._translateService.instant("Roles")

  sections: {label: string, link: string}[]=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.roles, link: '/dashboard/roles'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Inicio")
      this.roles = this._translateService.instant("Roles")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.roles
    });
  }
}
