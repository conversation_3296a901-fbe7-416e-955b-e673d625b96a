@import "/src/assets/styles/variables";
.cont-title {
  h4 {
    font-weight: 600 !important;
  }
}

.cont-cards {
  display: flex;
  min-width: 300px;
  max-width: 500px;
  height: 360px;
  flex-direction: column;
  align-items: center;
  flex: 1 0 0;
  border-radius: var(--Sharp, 0px);
  border: 1px solid var(--Black05, #f2f2f2);
  background: var(--wtw-brand-brand-wtw-white, #fff);
  box-shadow: 0px 2px 8px 0px #ccc;
}

.cont-tops {
  width: 100%;
  display: flex;
  padding: 16px 64px 64px 64px;
  justify-content: center;
  align-items: center;
  align-content: center;
  gap: 32px;
  align-self: stretch;
  flex-wrap: wrap;
}
.cont-header {
  display: flex;
  padding: 16px 32px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-bottom: 1px solid #f2f3f6;
}

.cont-item-tops {
  display: flex;
  padding: 10px 64px;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}
.top-number {
  display: flex;
  width: 32px;
  height: 32px;
  padding: var(--Subtle, 4px) 10px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  border-radius: 50px;
  color: var(--wtw-brand-brand-wtw-white, #fff);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.cont-body .cont-item-tops:nth-child(-n + 3) > div:nth-child(2) span {
  color: var(--wtw-brand-brand-wtw-onyx, #2f2c31);
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.216px;
}

.cont-body .cont-item-tops:nth-last-child(-n + 2) > div:first-child {
  background: var(--wtw-brand-brand-wtw-onyx, #2f2c31);
}

.cont-body .cont-item-tops:nth-child(3) > div:first-child {
  background: #a16811;
}

.cont-body .cont-item-tops:nth-child(2) > div:first-child {
  background: #a0a0a0;
}

.cont-body .cont-item-tops:first-child > div:first-child {
  background: #edaf06;
}
