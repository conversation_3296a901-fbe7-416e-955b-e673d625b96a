<div class="row mt-5">
    <h5 class="fw-bold mb-2">
      {{ 'SectionClientPortal.CoveragesTitle' | translate }}
    </h5>
    <mat-label>
      {{ 'SectionClientPortal.CoveragesText' | translate }}
    </mat-label>

    <app-table
      [displayedColumns]="estructTableCoverages"
      [data]="dataTableCoverages"
      (iconClick)="controller($event)"
    ></app-table>

    <div class="row">
      <div class="col">
        <button
          type="button"
          mat-raised-button
          color="primary"
          (click)="openModal()"
        >
          {{ "SectionClientPortal.AddCoverage" | translate }}
        </button>
      </div>
    </div>
  </div>


  <ng-template #createEditCoverage>
    <app-modal2 [titleModal]="(pkCoverageToModify === 0 ? 'SectionClientPortal.CreateCoverageTitle' : 'SectionClientPortal.UpdateCoverageTitle') | translate">
      <ng-container body>
        <form [formGroup]="formCoverage">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'SectionClientPortal.NameCoverage' | translate}}
            </mat-label>
            <input matInput formControlName="vName" PreventionSqlInjector/>
            <mat-error *ngIf="utilsSvc.isControlHasError(formCoverage, 'vName', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'SectionClientPortal.TextCoverage' | translate}}
            </mat-label>
            <textarea matInput formControlName="vText" PreventionSqlInjector cdkTextareaAutosize cdkAutosizeMinRows="3" cdkAutosizeMaxRows="10"></textarea>
            <mat-error *ngIf="utilsSvc.isControlHasError(formCoverage, 'vText', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
          <mat-form-field id="state" class="w-100">
            <mat-label> {{ "SectionClientPortal.TypeCoverage" | translate }} </mat-label>
            <mat-select formControlName="vType">
              <mat-option
                *ngFor="let item of listTypes"
                [value]="item.value"
                >{{ item.name }}</mat-option
              >
            </mat-select>
          </mat-form-field>
          <app-generic-image-picker
            class="col-12 col-md-4"
            [title]="'SectionClientPortal.CoverageIconTitle'| translate"
            [description]="'SectionClientPortal.CoverageIconText'| translate"
            (changeFile)="changeImage($event)"
            [imageSrc]="imageSrc">
        </app-generic-image-picker>
  
        </form>
      </ng-container>
      <ng-container customButtonRight>
        <div class="modal-footer">
          <button mat-raised-button color="primary" type="button" class="" (click)="saveCoverage()">
            {{ "Save" | translate }}
          </button>
        </div>
      </ng-container>
    </app-modal2>
  </ng-template>