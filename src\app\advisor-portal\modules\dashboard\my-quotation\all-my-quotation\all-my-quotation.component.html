<form [formGroup]="formFilters">
  <div class="row mt-2">
    <div class="row mb-2">
      <h4 class="col-md-12">
        {{ "MyQuotation.AllQuotation.HistoricalQuotes" | translate }}
      </h4>
    </div>

    <div class="row mt-3"> <!-- select de producto -->
      <div>
        <mat-form-field class="w-100" appearance="fill">
          <mat-label>
            {{ "TaskTraySettings.FormFilter.LabelProduct" | translate }}
          </mat-label>
          <mat-select formControlName="fkIdProduct">
            <mat-option
              *ngFor="let product of productsApiProduct"
              [value]="product.pkIIdProduct"
            >
              {{ product.vProductName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <div class="row mt-2" [ngClass]="{'disabled': isDisabled}">
      <div class="col-9">
        <mat-form-field class="w-100">
          <mat-label>
            {{ "Search" | translate }}
          </mat-label>
          <input (keyup.enter)="generateSearch()" formControlName="keyword" matInput type="text" class="form-control"
            placeholder="{{ 'Search' | translate }}" />
          <mat-icon class="hand" (click)="!isDisabled && generateSearch()" matSuffix>search</mat-icon>
        </mat-form-field>
      </div>
      <div class="w-auto">
        <button type="button" class="w-auto mr-1"  (click)="openModal()" mat-raised-button color="primary">
          {{ "Filter" | translate }}
          <mat-icon fontIcon="filter_list"></mat-icon>
        </button>
      </div>
      <div class="w-auto">
        <button (click)="onSortClick()" class="w-auto mr-1"  type="button" mat-raised-button color="primary">
          {{ "Order" | translate }}
          <mat-icon fontIcon="sort_by_alpha"></mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Contenido principal -->
  <div *ngIf="amountRows != 0" class="row">
    <div class="col-md-12">
      <app-table [IsStatic]="false" [pageIndex]="pageIndex" [pageSize]="pageSize"
        [displayedColumns]="estructMyQuotationTable" [data]="myQuotationTable" (iconClick)="controller($event)"
        (pageChanged)="onPageChange($event)" [amountRows]="amountRows"></app-table>
      <button (click)="downloadExcel()" type="button" class="btn w-auto" mat-raised-button color="primary">
        {{ "MyQuotation.AllQuotation.DownloadQuotes" | translate }}
        <mat-icon fontIcon="download"></mat-icon>
      </button>
    </div>
  </div>

  <ng-template #filterModal>
    <app-modal [titleModal]="'Filtros'">
      

      <!-- campos dinamicos -->
      <div *ngFor="let item of filterList">
        <div class="row mt-3">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>
              {{ item.vFieldName }}
            </mat-label>
            <input
              matInput
              [formControlName]="item.vFieldName"
              type="text"
            />
          </mat-form-field>
        </div>
      </div>

      <div class="row mt-2">
        <label for="dateStart">
          {{ "MyQuotation.AllQuotation.Date" | translate }}
        </label>
        <mat-form-field id="dateStart">
          <mat-label>
            {{ "MyQuotation.AllQuotation.StartDate" | translate }}
          </mat-label>
          <input min-view="date" auto-close="true" formControlName="startDate"
            (dateChange)="onDateChange($event, 'startDate')" matInput [matDatepicker]="picker" />
          <mat-hint>MM/DD/YYYY</mat-hint>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
        <mat-form-field id="dateStart">
          <mat-label>
            {{ "MyQuotation.AllQuotation.FinalDate" | translate }}
          </mat-label>
          <input formControlName="endDate" matInput [matDatepicker]="pickerEnd"
            (dateChange)="onDateChange($event, 'endDate')" />
          <mat-hint>MM/DD/YYYY</mat-hint>
          <mat-datepicker-toggle matIconSuffix [for]="pickerEnd"></mat-datepicker-toggle>
          <mat-datepicker #pickerEnd></mat-datepicker>
        </mat-form-field>
      </div>
      <div class="row mt-2">
        <label for="status">
          {{ "Status" | translate }}
        </label>
        <mat-radio-group formControlName="active" aria-label="Select an option">
          <mat-radio-button [value]="true">
            {{ "Active" | translate }}
          </mat-radio-button>
          <mat-radio-button [value]="false">
            {{ "Inactive" | translate }}
          </mat-radio-button>
        </mat-radio-group>
      </div>
      <div class="row mt-2">
        <label for="procedure">
          {{ "MyQuotation.AllQuotation.Procedure" | translate }}
        </label>
        <mat-radio-group formControlName="idStatus" aria-label="Select an option">
          <ul class="list-group">
            <li class="list-group-item" *ngFor="let item of filtersByUser.quoteStatuses">
              <mat-radio-button [value]="item.pkIIdQuoteStatus">
                {{ item.vStatus }}</mat-radio-button>
            </li>
          </ul>
        </mat-radio-group>
      </div>
      <div class="row mt-2">
        <label for="customer">
          {{ "MyQuotation.AllQuotation.Adviser" | translate }}
        </label>
        <ul class="list-group" id="customer">
          <li class="list-group-item" *ngFor="let item of filtersByUser.userGroupQuotes">
            <mat-checkbox [checked]="isUserSelected(item.pkIIdUser)" (change)="toggleUserSelection(item.pkIIdUser)">
              {{ item.vPersonName }}
            </mat-checkbox>
          </li>
        </ul>
      </div>
      <div class="row mt-2">
        <label for="group">
          {{ "MyQuotation.AllQuotation.Group" | translate }}
        </label>
        <ul class="list-group" id="group">
          <li class="list-group-item" *ngFor="let item of filtersByUser.groups">
            <mat-checkbox formControlName="idGroups" [value]="item.pkIIdGroup">
              {{ item.vGroupName }}
            </mat-checkbox>
          </li>
        </ul>
      </div>
      <div class="modal-footer mt-2 justify-content-center">
        <div class="modal-footer mt-2">
          <button (click)="resetSearch();closeModal()" type="button"  style="margin-right: 10px;"  mat-raised-button>
            {{ "MyQuotation.AllQuotation.Clean" | translate }}
          </button>
        </div>        
        <div class="modal-footer mt-2">
          <button (click)="generateSearch(); closeModal()" type="button" mat-raised-button color="primary">
            {{ "Apply" | translate }}
          </button>
        </div>
      </div>      
    </app-modal>
  </ng-template>
</form>