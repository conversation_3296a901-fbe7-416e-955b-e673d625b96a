import { Routes } from '@angular/router';
import { TaskTraySettingsComponent } from './task-tray-settings.component';

export default [
  {
    path: '',
    component: TaskTraySettingsComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './all-task-tray-settings/all-task-tray-settings.component'
          ).then((c) => c.AllTaskTraySettingsComponent),
      },
      //     {path: 'all', loadComponent:()=> import('./all-forms/all-forms.component').then(c=>c.AllFormsComponent)},
      //     {path: 'new', loadComponent:()=> import('./edit-new-form/edit-new-form.component').then(c=>c.EditNewFormComponent)},
      //     {path: 'modify/:id', loadComponent:()=> import('./edit-new-form/edit-new-form.component').then(c=>c.EditNewFormComponent)},
    ],
  },
] as Routes;
