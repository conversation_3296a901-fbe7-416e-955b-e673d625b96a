import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>,ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list'
import { Subscription } from 'rxjs';
import { MyQuotationModel } from 'src/app/shared/models/my-quotation/myQuotation.model';
import { CardComponent } from "src/app/shared/components/card/card.component";
import {MatExpansionModule,MatAccordion} from '@angular/material/expansion';
import {MatInputModule} from '@angular/material/input';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';

@Component({
  selector: 'app-see-my-quotation',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule, MatButtonModule, MatListModule,MatExpansionModule,
    MatInputModule,CardComponent],
  templateUrl: './see-my-quotation.component.html',
  styleUrls: ['./see-my-quotation.component.scss']
})
export class SeeMyQuotationComponent implements OnInit, OnDestroy {
  @ViewChild(MatAccordion) acc: MatAccordion;

  private _myQuotationubscription?: Subscription;
  dataMyQuotation!: MyQuotationModel;
  //myQuotationTable: MyQuotationModel[] = [];;
  panelOpenState = false;
  myQuotationTable = [
    {
      text: 'Datos vehículo', cols: 1, rows: 4, datasource: [
        { text: 'Placa Vehículo:', cols: 1, rows: 1, content: 'ABC123' },
        { text: 'Código Fasecolda:', cols: 1, rows: 1, content: '09201267' },
        { text: 'Clase:', cols: 1, rows: 1, content: 'Automovil' },
        { text: 'Uso del vehículo:', cols: 1, rows: 1, content: 'Particular personas' },
        { text: 'Tipo de placa:', cols: 1, rows: 1, content: 'Automovil' },
        { text: 'Marca:', cols: 1, rows: 1, content: 'No aplica' },
        { text: 'Referencia1:', cols: 1, rows: 1, content: 'Jetta' },
        { text: 'Referencia2:', cols: 1, rows: 1, content: 'GLI TP 2000' },
        { text: 'Modelo:', cols: 1, rows: 1, content: '2020' },
        { text: 'Blindaje:', cols: 1, rows: 1, content: 'No aplica' }
      ]
    },
    {
      text: 'Datos personales', cols: 1, rows: 3, datasource: [
        { text: 'Tipo documento', cols: 1, rows: 1, content: 'Cédula de ciudadania' },
        { text: 'Número documento', cols: 1, rows: 1, content: '538782372' },
        { text: 'Nombre', cols: 1, rows: 1, content: 'Juan Perez Rodriguez' },
        { text: 'Edad', cols: 1, rows: 1, content: '50' },
        { text: 'Estado civil', cols: 1, rows: 1, content: 'Soltero' },
        { text: 'Género', cols: 1, rows: 1, content: 'Masculino' }
      ]
    },
    {
      text: 'Datos de contacto', cols: 1, rows: 3, datasource: [
        { text: 'Celular', cols: 1, rows: 1, content: '3002589647' },
        { text: 'Género', cols: 1, rows: 1, content: 'Masculino' },
        { text: 'Correo electrónico', cols: 1, rows: 1, content: '<EMAIL>' },
        { text: '', cols: 1, rows: 1, content: '' },
        { text: 'País', cols: 1, rows: 1, content: 'Colombia' },
        { text: 'Departamento', cols: 1, rows: 1, content: 'Cundinamarca' },
        { text: 'Ciudad', cols: 1, rows: 1, content: 'Bogota' },
        { text: 'Dirección', cols: 1, rows: 1, content: 'Av 300 #200-100' }
      ]
    },
    {
      text: 'Circulación', cols: 1, rows: 2, datasource: [
        { text: 'País', cols: 1, rows: 1, content: 'Colombia' },
        { text: 'Departamento', cols: 1, rows: 1, content: 'Cundinamarca' },
        { text: 'Ciudad', cols: 1, rows: 1, content: 'Bogota' }
      ]
    },
    {
      text: 'Datos compañia', cols: 1, rows: 2, datasource: [
        { text: 'Póliza', cols: 1, rows: 1, content: 'No aplica' },
        { text: 'Bonificación', cols: 1, rows: 1, content: 'No aplica' },
        { text: 'RCE Plan Clásico', cols: 1, rows: 1, content: 'No aplica' },
        { text: 'RCE Plan Global', cols: 1, rows: 1, content: 'No aplica' }
      ]
    }
  ];

  constructor(
    private _transactionService:TransactionService,
  ) {
    this.acc = new MatAccordion;
   }

  ngOnInit(): void {
    this.dataMyQuotation = this._transactionService.getQuotationLocalStorage();
  }

  ngOnDestroy(): void {
    this._myQuotationubscription?.unsubscribe();
  }

  showSummaryQuotation() {
  }
}
