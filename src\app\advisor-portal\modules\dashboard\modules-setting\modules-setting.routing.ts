import { Routes } from '@angular/router';
import { ModulesSettingComponent } from './modules-setting.component';

export default [
  {
    path: '',
    component: ModulesSettingComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-modules-setting/all-modules-setting.component').then(
            (c) => c.AllModulesSettingComponent
          ),
      },
      {
        path: 'new',
        loadComponent: () =>
          import(
            './edit-new-modules-setting/edit-new-modules-setting.component'
          ).then((c) => c.EditNewModulesSettingComponent),
      },
      {
        path: 'edit/:pkIIdMenu',
        loadComponent: () =>
          import(
            './edit-new-modules-setting/edit-new-modules-setting.component'
          ).then((c) => c.EditNewModulesSettingComponent),
      },
      {
        path: 'new-stage',
        loadComponent: () =>
          import(
            './edit-new-modules-setting/edit-create-stages/edit-new-stage/edit-new-stage.component'
          ).then((c) => c.EditNewStageComponent),
      },
      {
        path: 'edit-stage/:pkIIdStage',
        loadComponent: () =>
          import(
            './edit-new-modules-setting/edit-create-stages/edit-new-stage/edit-new-stage.component'
          ).then((c) => c.EditNewStageComponent),
      },
    ],
  },
] as Routes;
