<div class="col-12 col-md-12">
  <!-- datatable tablas -->
  <app-table
    [displayedColumns]="estructTableTables"
    [data]="dataTableTables"
    (iconClick)="controller($event)"
  ></app-table>

  <!-- boton agregar tabla -->
  <button
    class="mb-2"
    type="button"
    mat-raised-button
    color="primary"
    (click)="openEditNewTableModal(0)"
  >
    {{'Table.AddTable' | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
</div>

<!-- modal editar/crear tabla -->
<ng-template #editNewTableModal>
  <app-modal2 [showCancelButton]="true" [titleModal]="'Table.ConfigureTableData'| translate" [subtitle]="'Table.ConfigureTableSubtitle'| translate">
    <ng-container body>
        <app-edit-new-table
            [idFormIn]="idForm"
            [idTableIn]="idTable"
            (resultOut)="getFormResult($event)"
        >
        </app-edit-new-table>
    </ng-container>

    <!-- botones -->
    <ng-container customButtonRight>
      <button
        *ngIf="showButtons"
        class="w-auto mr-3"
        type="button"
        (click)="updateTable()"
        mat-raised-button
        color="primary"
      >
          {{'Table.SaveTable' | translate }} 
          <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>

      <button    
        *ngIf="showButtons"    
        class="w-auto"
        type="button"
        mat-raised-button
        (click)="deleteTable()"
      >
        {{'Table.DeleteTable' | translate }}
        <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>