<div [formGroup]="form" class="row">
  <div class="col-md-4 col-sm-12 mb-2">
    <mat-form-field class="w-100" appearance="fill">
      <mat-label>
        {{ "Clients.FormSearchTypeDoc" | translate }}
      </mat-label>

      <mat-select formControlName="typeDocument">
        <mat-option *ngFor="let doc of documentsTypes" [value]="doc.id">
          {{ doc.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div class="col-md-4 col-sm-12 mb-2">
    <mat-form-field class="w-100" appearance="fill">
      <mat-label>
        {{ "Clients.FormSearchDoc" | translate }}
      </mat-label>
      <input
        matInput
        [placeholder]="'Clients.FormSearchDoc' | translate"
        formControlName="document"
      />
    </mat-form-field>
  </div>
  <div class="col-md-4 col-sm-12 mb-2">
    <button
      [disabled]="!form.valid"
      type="button"
      class="mx-3"
      mat-raised-button
      color="primary"
      (click)="getCustomerByDocument()"
    >
      <mat-icon class="hand" iconPositionEnd>search</mat-icon>
      {{ "Clients.FormSearchButton" | translate }}
    </button>
  </div>
</div>

<!-- datatable clientes -->
<div class="row">
  <app-table
    [IsStatic]="false"
    [displayedColumns]="estructTableClients"
    [data]="dataTableClients"
    [pageIndex]="pageIndex"
    [pageSize]="pageSize"
    [amountRows]="amountRows"
    (pageChanged)="onPageChange($event)"
    (iconClick)="controllerBusiness($event)"
  ></app-table>
</div>

<!-- botonera de clientes -->
<div class="d-flex justify-content-start align-items-end">
  <div class="">
    <button
      type="button"
      class="mx-3"
      mat-raised-button
      color="primary"
      [disabled]="isDisabledButton"
      (click)="downloadCustomer()"
    >
      <mat-icon class="hand" iconPositionEnd>get_app</mat-icon>
      {{ "Clients.ButtonDownload" | translate }}
    </button>
  </div>
  <div class="">
    <button (click)="goToClientsNew()" type="button" class="mx-3" mat-raised-button [routerLink]="['#']"  >
      <mat-icon class="hand" iconPositionEnd>add</mat-icon>

      {{ "Clients.ButtonNewClient" | translate }}
    </button>
  </div>
  <div class="">
    <button (click)="openModalBulkLoad()" type="button" class="mx-3" mat-raised-button>
      <mat-icon class="hand" iconPositionEnd>upload</mat-icon>
      {{ "Clients.ButtonUpload" | translate }}
    </button>
  </div>
</div>


