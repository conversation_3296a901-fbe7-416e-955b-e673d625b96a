<h2 class="h3">
  <img src="assets/img/layouts/metricas.svg" />
  {{ "MetricsSettings.title" | translate }}
</h2>

<app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

<div class="cont-subtitle">
  <form [formGroup]="form" class="mt-2">
    <mat-form-field appearance="outline" class="col-4 p-1">
      <mat-label>{{ "MetricsSettings.subtitle" | translate }}</mat-label>
      <mat-select
        [required]="true"
        (selectionChange)="getEmbeddedResource($event)"
      >
        <mat-option
          *ngFor="let resource of embeddedList"
          [value]="resource.pkIIdContent"
          >{{ resource.vName }}</mat-option
        >
      </mat-select>
      <mat-error
        *ngIf="utilsService.isControlHasError(form, 'fkIIdReport', 'required')"
        >{{ "ThisFieldIsRequired" | translate }}</mat-error
      >
    </mat-form-field>
  </form>

  <div id="iframePBI" style="margin: 0 auto" class="options_PBI">
    <div #reportContainer style="height: 600px; width: 100%"></div>
  </div>
</div>
