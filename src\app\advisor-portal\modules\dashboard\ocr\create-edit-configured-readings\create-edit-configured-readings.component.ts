import { CommonModule } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { DragDropUploadComponent } from 'src/app/shared/components/drag-drop-upload/drag-drop-upload.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  ConfigurationModel,
  FieldHomologationModel,
  HomologationModel,
  OcrTemplateModel,
} from 'src/app/shared/models/OCR';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { ProductService } from 'src/app/shared/services/product/product.service';

import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ErrorHandlingService } from 'src/app/shared/services/error/errorHandlingService';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';

@Component({
  selector: 'app-create-edit-configured-readings',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    Modal2Component,
    DragDropUploadComponent,
    MatRadioModule
  ],
  templateUrl: './create-edit-configured-readings.component.html',
  styleUrl: './create-edit-configured-readings.component.scss',
})
export class CreateEditConfiguredReadingsComponent implements OnInit {
  //Variables relacionadas con el formulario
  formHomologation: FormGroup = new FormGroup({});
  ocrTemplateList: OcrTemplateModel[] = [];
  fielTemplateList: any[] = [];
  idConfiguration = 0;
  idHomologation = 0;
  idUserOCR: any;
  baseName = '';
  idProductAssociated: number = 0;
  iTypeReading: number = 0;
  idBusinessByCountry = 0;
  validFormReading = false;
  products:any[]=[]
  listOfInsurances: InsuranceCompanyModel[] = [];
  
  // Variables relacionadas con la carga de archivos.
  completedTemplate!: File;
  showFileUpload = false;
  showTemplateUpload = false;
  fileNameTemplate = '';
  templateName = '';
  templateSize = 0;

  //Variables relacionadas con la tabla de Homologaciones.
  homologationData: Partial<HomologationModel>[] = [];
  estructHomologationTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('OCR.FiledBase'),
      columnValue: 'baseName',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'create',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  //Variables relacionadas con la Tabla de homologaciones de campo.
  fieldHomologationData: FieldHomologationModel[] = [];
  estructFieldHomologationTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('OCR.Template'),
      columnValue: 'templateNameOCR',
    },
    {
      columnLabel: this._translateService.instant('OCR.Templatefield'),
      columnValue: 'fieldTemplateName',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  //Variables relacionadas con los modales.
  @ViewChild('homologationModal')
  homologationModal?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;
  modalOpenRerefence: string = '';
  modalTitle: string = this._translateService.instant('OCR.NewHomologation');
  

  constructor(
    public utilsSvc: UtilsService,
    public matDialog: MatDialog,
    private _fb: FormBuilder,
    private _translateService: TranslateService,
    private _customeRouter: CustomRouterService,
    private _messageService: MessageService,
    private _parametersService: ParametersService,
    private _activatedRoute: ActivatedRoute,
    private _settingService: SettingService,
    private _productService: ProductService,
    private _errorHandlingService: ErrorHandlingService,
    private _insuranceService: InsuranceService

  ) {}

  ngOnInit(): void {
    this.getIdBusinessByCountry();
    this.getDataUrl();
    this.getIdUserOCR();
    

    //Traducción de la tabla de Homologaciones.
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructHomologationTable[0].columnLabel =
        this._translateService.instant('OCR.FiledBase');
      this.estructHomologationTable[1].columnLabel =
        this._translateService.instant('Modify');
      this.estructHomologationTable[2].columnLabel =
        this._translateService.instant('Delete');
    });

    //Traducción de la tabla de Homologaciones de Campos OCR.
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructFieldHomologationTable[0].columnLabel =
        this._translateService.instant('OCR.Template');
      this.estructFieldHomologationTable[1].columnLabel =
        this._translateService.instant('OCR.Templatefield');
      this.estructFieldHomologationTable[2].columnLabel =
        this._translateService.instant('Delete');
    });
  }

  //Inicializa el formulario.
  initFormHomologation() {
    this.formHomologation = this._fb.group({
      baseField: [null],
      ocrTemplate: [null, [Validators.required]],
      guidAssesmentDoclm: [null, [Validators.required]],
      insurance: [null, [Validators.required]],
      templateField: [null, [Validators.required]],
      idBusinessByCountry: null,
    });

    this.formHomologation.get('ocrTemplate')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.fielTemplateList = [];
          this.formHomologation.get('templateField')?.setValue(null);
          this.getFieldTemplateOCRById(Number(data));
        }
      },
    });

    this.formHomologation.get('guidAssesmentDoclm')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.getFieldTemplateOCRById(0, data);
          this.getInsuranceCompanyByIdBusinessByCountry(this.idBusinessByCountry);
        }
      },
    });
  }
  //Obtiene el valor actual del control ocrTemplate.
  get ocrTemplate(): number {
    return this.formHomologation.get('ocrTemplate')?.value;
  }

  get guidAssesmentDoclm(): string {
    return this.formHomologation.get('guidAssesmentDoclm')?.value;
  }

  //Obtiene el valor actual del control templateField.
  get templateField(): string {
    return this.formHomologation.get('templateField')?.value;
  }

  //Obtiene el valor actual del control baseField.
  get baseField(): string {
    return this.formHomologation.get('baseField')?.value;
  }

  //Obtiene la información enviada por medio de la URL.
  getDataUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idConfiguration) {
        this.idConfiguration = Number(params.idConfiguration);
      }

      if (this.idConfiguration > 0) {
        this.getConfigurationOCRById(this.idConfiguration);
      }
    });
  }

  //Obtiene el idBusinessByCountry de la configuración inicial seleccionada al iniciar sesión.
  async getIdBusinessByCountry() {
    const dataSetting = await this._settingService.getDataSettingInit();
    if (dataSetting.idBusinessByCountry) {
      this.idBusinessByCountry = dataSetting.idBusinessByCountry;
      this.getProductSimpleByBusinessCountry();
    }
  }

  //Obtiene el userId de ORC, parametrizado en parámetros, para realizar las consultas a la api de OCR.
  getIdUserOCR() {
    this._parametersService
      .getParameters('Id_User_OCR')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.idUserOCR = resp;
        }
      });
  }

  //Obtiene el detalle de una lectura por medio del idConfiguration.
  getConfigurationOCRById(idConfiguration: number) {
    this._parametersService
      .getConfigurationOCRById(idConfiguration)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            if (resp.result.homologations) {
              const { homologations, readName, fkIIdProduct, iTypeReading } = resp.result;
              this.homologationData = homologations;
              this.baseName = readName;
              this.idProductAssociated = fkIIdProduct;
              this.iTypeReading = iTypeReading;
              
            }
          }
        }
      });
  }

  //Obtiene la lista de plantilla desde OCR.
  getProjectsUser() {
    this._parametersService
      .getProjectsUser()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            if (resp.result) {
              this.ocrTemplateList = resp.result;
            }
          }
        }
      });
  }

  //Obtiene la lista de campos asociados a una plantilla OCR o Doclm.
  getFieldTemplateOCRById(idFieldTemplateOCR: number, guidAssesmentDoclm?: string) {
    this._parametersService
      .getFieldTemplateOCRById(idFieldTemplateOCR, guidAssesmentDoclm)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            if (resp.result.fields) {
              if (resp.result.fields.length > 0) {
                this.fielTemplateList = resp.result.fields;
              }
            }
          }
        }
      });
  }

  //Obtiene el detalle de una homologación por id de homologación.
  getHomologationById(idHomologation: number) {
    this._parametersService
      .getHomologationById(idHomologation)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.formHomologation.get('baseField')?.setValue(resp.result.baseName);
          const fieldHomologationList = resp.result.json ? JSON.parse(resp.result.json) : [];
          if (fieldHomologationList.length > 0) {
            this.fieldHomologationData = fieldHomologationList;
          }
        }
      });
  }

  //Elimina una Homologación por medio de su PK.
  deleteConfigurationOCR(idHomologation: number) {
    this._parametersService
      .deleteHomologationById(idHomologation)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.getConfigurationOCRById(this.idConfiguration);
          this._messageService.messageConfirmatio(
            this._translateService.instant('Deleted'),
            '',
            'success',
            this._translateService.instant('Continue')
          );
        }
      });
  }

  //Revalida las plantillas desde OCR.
  processPdf(file: FormData, idPlantillaOcr: number, user: number) {
    this._parametersService
      .processPdf(file, idPlantillaOcr ?? 0, user, this.iTypeReading==2, this.guidAssesmentDoclm)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            if (resp.result.fields) {
              if (resp.result.fields.length > 0) {
                this.fielTemplateList = resp.result.fields;
                this.deleteTemplate();
                this.showTemplateUpload = false;
              } else {
                this.deleteTemplate();
                this.showTemplateUpload = false;
                this._messageService.messageWaring(
                  this._translateService.instant('OCR.FieldsNotFoundTitle'),
                  this._translateService.instant('OCR.FieldsNotFoundSubtitle')
                );
              }
            }
          }
        }
      });
  }

  //Pemrite gaurdar o actualizar una lectura.
  createOrUpdateConfigurationOCR() {
    const model: ConfigurationModel = {
      bussinesCountry: this.idBusinessByCountry,
      id: this.idConfiguration,
      name: this.baseName,
      iTypeReading:this.iTypeReading,
      fkIIdProduct:this.idProductAssociated
    };

    if (model.bussinesCountry > 0 && model.name) {
      this._parametersService
        .createOrUpdateConfigurationOCR(model)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageError(error.error.message);
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('Saved'),
              ''
            );
            this.idConfiguration = resp.result.id;
            this.getConfigurationOCRById(this.idConfiguration);
          }
        });
    } else {
    }
  }

  //Pemrite gaurdar o actualizar una homologación.
  createOrUpdateHomologation(model: HomologationModel) {
    this._parametersService
      .createOrUpdateHomologation(model)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess(
            this._translateService.instant('Saved'),
            ''
          );
          this.closeModal();
          this.getConfigurationOCRById(this.idConfiguration);
        }
      });
  }

  getKey(item: { [key: string]: string }): string {
    return Object.keys(item)[0]; // Extrae la clave
  }

  getValue(item: { [key: string]: string }): string {
    return Object.values(item)[0]; // Extrae el valor
  }

  //Admisntrador de acciones de la tabla que lista las homologaciones creadas.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'modify':
        this.openModal('homologationModal', event.value.id);
        this.idHomologation = event.value.id;
        break;
      case 'delete':
        this._messageService
          .messageConfirmationAndNegation(
            this._translateService.instant('Delete') + '?',
            '',
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.deleteConfigurationOCR(event.value.id);
            }
          });
        break;

      default:
        break;
    }
  }

  //Detecta los eventos del componente de cargue de archivos para las plantillas caragadas.
  getFiles(file: File[]) {
    if (this.utilsSvc.validateFileSize(file[0].size, 25)) {
      if (file.length > 0) {
        this.showTemplateUpload = true;
        this.fileNameTemplate = file[0].name;
        this.templateSize = this.utilsSvc.parseBytesToMb(file[0].size);

        const payload = new FormData();
        payload.append(`file`, file[0]);
        this.processPdf(payload, this.ocrTemplate, this.idUserOCR);
      } else {
        this.showTemplateUpload = false;
      }
    } else {
      this._messageService.messageWaring(
        this._translateService.instant('UploadDocuments.ExceededSize'),
        this._translateService.instant('UploadDocuments.ExceededSizeWtw')
      );
    }
  }

  //Elimina el archivo agregado en la opciónd e cargue de plantilla.
  deleteTemplate() {
    this.completedTemplate = new File([], '');
    this.templateSize = 0;
    this.showTemplateUpload = false;
  }

  //Admisntrador de acciones de la tabla del crud de homologaciones.
  controllerCrud(event: IconEventClickModel) {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Delete') + '?',
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this.fieldHomologationData.splice(event.index, 1);
          this.fieldHomologationData = [...this.fieldHomologationData];
        }
      });
  }

  //Función que devuelve al inicio de la aplicación.
  cancel() {
    this._customeRouter.navigate([`dashboard/ocr`]);
  }

  // Función que abre un modal, dependiendo el selecctor indicado.
  openModal(
    modalReference: string,
    idHomologation?: number,
    newHomologation?: boolean
  ) {
    if (newHomologation) {
      this.currentModal = null;
      this.modalTitle = this._translateService.instant('OCR.NewHomologation');
      this.idHomologation = 0;
      this.fieldHomologationData = [];
      this.ocrTemplateList = [];
      this.fielTemplateList = [];
      this.showFileUpload = false;
      this.formHomologation.get('baseField')?.setValue(null);
      this.formHomologation.get('ocrTemplate')?.setValue(null);
      this.formHomologation.get('templateField')?.setValue(null);
    }

    const modalConfiguration = {
      disableClose: false,
      width: '60vw',
      height: 'auto',
    };
    let modal: TemplateRef<any>;
    switch (modalReference) {
      case 'homologationModal':
        this.getProjectsUser();
        this.initFormHomologation();
        this.modalOpenRerefence = 'homologationModal';
        modal = this.homologationModal!;
        if (idHomologation) {
          this.modalTitle = this._translateService.instant(
            'OCR.EditHomologation'
          );
          this.getHomologationById(idHomologation);
        }
        break;
      default:
        return;
    }
    //Abre el modal y guarda la referencia en la variable currentModal.
    this.currentModal = this.matDialog.open(modal, modalConfiguration);
  }

  //Cierra todos los modales.
  closeModal() {
    this.currentModal?.close();
    this.currentModal = null;
    this.idHomologation = 0;
    this.fieldHomologationData = [];
    this.ocrTemplateList = [];
    this.fielTemplateList = [];
    this.modalTitle = this._translateService.instant('OCR.NewHomologation');
  }

  //Revalida una plantilla OCR.
  revalidateTemplate() {
    if (this.ocrTemplate || this.guidAssesmentDoclm) {
      this.showFileUpload = true;
    }
  }

  //Obtiene el nombre de la plantilla OCRT por medio de su ID.
  findTemplateNameOCR(key: string): string | '' {
    const result = this.ocrTemplateList.find((obj) => obj.hasOwnProperty(key));
    return result ? result[key] : '';
  }

  //Añade una homologación de campo  OCR a la tabla.
  addHomologation() {
    const fieldHomologation: FieldHomologationModel = {
      fieldTemplateName: this.templateField,
      templateIdOCR: this.ocrTemplate ?? this.guidAssesmentDoclm,
      templateNameOCR: this.ocrTemplate!=null? this.findTemplateNameOCR(this.ocrTemplate.toString())  : 'TemplateDoclm '+this.formHomologation.get('insurance')?.value
    };

    if (this.doesFieldExist(fieldHomologation)) {
      this._messageService.messageInfo(
        this._translateService.instant('OCR.DuplicateField'),
        ''
      );
    } else {
      this.fieldHomologationData.push(fieldHomologation);
      this.fieldHomologationData = [...this.fieldHomologationData];
    }
  }

  //Valida si un campo ya existe o no en la tabla de homologaciones de campos OCR.
  doesFieldExist(object: FieldHomologationModel): boolean {
    return this.fieldHomologationData.some(
      (item: FieldHomologationModel) =>
        item.templateNameOCR === object.templateNameOCR &&
        item.fieldTemplateName === object.fieldTemplateName
    );
  }

  //Función que guarda una Homologación.
  saveHomologation() {
    const payload: HomologationModel = {
      baseName: this.baseField,
      id: this.idHomologation,
      idConfiguration: this.idConfiguration,
      json: JSON.stringify(this.fieldHomologationData),
    };
    if (this.fieldHomologationData.length > 0 && this.baseField) {
      this.createOrUpdateHomologation(payload);
    }
  }

  //Consult products with basic info by business-country

  getProductSimpleByBusinessCountry() {
    this._productService
      .getProductSimpleByBusinessCountry(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error, false);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this.products=resp.result;
          }
        }
      });
  }

  getInsuranceCompanyByIdBusinessByCountry(idBusinessByCountry: number) {
      this._insuranceService.getInsuranceCompanyByIdBusinessByCountry(idBusinessByCountry)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this.listOfInsurances = resp.result;
          }
        });
    }
}
