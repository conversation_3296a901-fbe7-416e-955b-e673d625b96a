import {
  Component,
  Input,
  OnInit,
  TemplateRef,
  <PERSON>C<PERSON><PERSON>,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';

import { Subscription, catchError, of } from 'rxjs';
import { TableComponent } from 'src/app/shared/components/table/table.component';

import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';

import { DocumentModuleModel } from 'src/app/shared/models/configuration-form/document-module';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { EditDocumentsComponent } from './edit-documents/edit-documents.component';
@Component({
  selector: 'app-documents',
  standalone: true,
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss'],
  imports: [
    CommonModule,
    TableComponent,
    TranslateModule,
    MatIconModule,
    Modal2Component,
    EditDocumentsComponent,
    MatButtonModule,
  ],
})
export class DocumentsComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;

  @Input() idForm: number = 0;
  @ViewChild('editNewDocumentModuleModal')
  editNewDocumentModuleModal?: TemplateRef<any>;

  idBusinessCountry: number = 0;
  dataTableDocumentForm: DocumentModuleModel[] = [];
  action: string = 'create';
  titelModal: string = this._translateService.instant(
    'DocumentModule.TitleModalDocument'
  );
  idTable: number = 0;

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'DocumentModule.DocumentName'
      ),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('DocumentModule.TypeOfFile'),
      columnValue: 'vExtension',
    },
    {
      columnLabel: this._translateService.instant('DocumentModule.MaximumSize'),
      columnValue: 'maximumSizeTable',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'edit',
      columnIcon: 'edit',
    },
  ];

  constructor(
    private _settingService: SettingService,
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public _utilsSvc: UtilsService,
    private _modalDialog: MatDialog
  ) {}

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table modules
      this.estructTable[0].columnLabel = this._translateService.instant(
        'DocumentModule.DocumentName'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'DocumentModule.TypeOfFile'
      );
      this.estructTable[2].columnLabel = this._translateService.instant(
        'DocumentModule.MaximumSize'
      );
      this.estructTable[4].columnLabel =
        this._translateService.instant('Status');
      this.estructTable[5].columnLabel =
        this._translateService.instant('Action');
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['idForm']) {
      this.idForm = changes['idForm'].currentValue;
      this.action = '';
      this.getDocumentListByFormIdModule(this.idForm);
      this.getSettingCountryAndCompanySubscription();
    }
  }

  getDocumentListByFormIdModule(idFormModule: number) {
    this._moduleService
      .getDocumentListByFormIdModule(idFormModule)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.dataTableDocumentForm = response.result;
            this.action = '';
            this._modalDialog.closeAll();
          }
        }
      });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
          }
        }
      );
  }

  /// este método abre el modal de nuevo documento
  openModalCreateEditDocumentModule() {
    if (this.action === 'edit') {
      this.titelModal = this._translateService.instant(
        'DocumentModule.TitleModalDocumentModify'
      );
    } else {
      this.titelModal = this._translateService.instant(
        'DocumentModule.TitleModalDocument'
      );
    }
    this._modalDialog.open(this.editNewDocumentModuleModal!, {
      width: '90vh',
      height: 'auto',
    });
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.idTable = event.value.pkIIdTableModule;
        this.action = 'edit';
        this.openModalCreateEditDocumentModule();
        break;
      default:
        break;
    }
  }

  cancelFormPrincipal(event: boolean) {
    this.action = '';
    this.idTable = 0;
    this.titelModal = '';
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
