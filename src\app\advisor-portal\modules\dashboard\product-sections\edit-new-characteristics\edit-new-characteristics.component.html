<div class="flex">
  <div class="box">
    <span class="title">
      {{'ProductSection.Business'| translate}}
    </span>
    <h4 class="subtitle">{{ enterprise }}</h4>
  </div>
  <div class="box title-customer">
    <span class="title">
      {{'ProductSection.Country'| translate}}
    </span>
    <h4 class="subtitle">{{ country }}</h4>
  </div>
  <div class="box title-customer">
    <span class="title">
      {{'ProductSection.Product'| translate}}
    </span>
    <h4 class="subtitle">{{ product }}</h4>
  </div>
</div>

<form [formGroup]="formCharacteristics">
  <div class="row mt-4 mb-5">
    <div class="col-md-3">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>
          {{'ProductSection.SectionName'| translate}}
        </mat-label>
        <input matInput formControlName="vDescription" PreventionSqlInjector/>
        <mat-error
          *ngIf="
            utilsService.isControlHasError(
              formCharacteristics,
              'vDescription',
              'required'
            )
          "
        >
          {{'ThisFieldIsRequired'| translate}}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-12 col-md-12 mt-1">
      <mat-slide-toggle formControlName="bActive" class="">
        {{'ProductSection.ActiveSection'| translate}}
      </mat-slide-toggle>
    </div>
  </div>
</form>

<div class="row">
  <div class="col-md-12 mb-2">
    <h3>
      {{'ProductSection.SectionItems'| translate}}
    </h3>
  </div>
</div>

<app-table
  *ngIf="dataTableItems.length > 0"
  [displayedColumns]="estructTable"
  [data]="dataTableItems"
  (iconClick)="controller($event)"
></app-table>

<div class="row">
  <div class="col-md-4">
    <button
      (click)="addItem()"
      type="button"
      mat-raised-button
      color="primary"
    >
      {{'ProductSection.AddItem'| translate}}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
</div>

<ng-template #addItemModal>
  <app-modal2 
    [titleModal]="titleModal" 
    (closeModal)="eventCloseModal($event)"
  >
    <ng-container body>
      <form [formGroup]="formElement" class="mt-3">
        <div class="row">
          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{"ProductSection.ItemName"| translate}}
              </mat-label>
              <input matInput formControlName="vName" PreventionSqlInjector/>
              <mat-error
                *ngIf="
                  utilsService.isControlHasError(
                    formElement,
                    'vName',
                    'required'
                  )
                "
              >
                {{'ThisFieldIsRequired'| translate}}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{"ProductSection.Type"| translate}}
              </mat-label>
              <mat-select formControlName="fkIIdPlanTypeField" (selectionChange)="onPlanTypeChange()">
                <mat-option
                  *ngFor="let item of itemType"
                  [value]="item.pkIIdPlanTypeField"
                >
                  {{ item.vName }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsService.isControlHasError(
                    formElement,
                    'fkIIdPlanTypeField',
                    'required'
                  )
                "
              >
                {{'ThisFieldIsRequired'| translate}}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="col-12 col-md-12">
          <mat-slide-toggle class="mb-2 mr-4" formControlName="bActive">
            {{"ProductSection.ItemeActive"| translate}}
          </mat-slide-toggle>
          <mat-slide-toggle class="mb-2" formControlName="bShowInQuoteCard" *ngIf="showQuoteCardCheck">
            {{"ProductSection.ShowInOfferCard"| translate}}
          </mat-slide-toggle>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonCenter>
      <button
        (click)="deleteItem()"
        type="button"
        mat-raised-button
        *ngIf="btnDelete"
        class="delete"
      >
        {{"ProductSection.DeleteItem" | translate}}
        <mat-icon class="delete" iconPositionEnd fontIcon="close"></mat-icon>
      </button>
    </ng-container>

    <ng-container customButtonRight>
      <button
        (click)="saveItem()"
        type="button"
        mat-raised-button
        color="primary"
        [disabled]="!validFormElement"
      >
        {{"Add" | translate}} 
        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

<div class="d-flex justify-content-center mt-3">
  <button (click)="goBack()" mat-raised-button class="mx-4">
    <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
    {{'Back'| translate}}
  </button>
  <button
    (click)="createCharacteristic()"
    [disabled]="!validFormCharacteristics"
    type="button"
    mat-raised-button
    color="primary"
    *ngIf="!editingCharacteristic"
  >
    {{'ProductSection.CreateCharacteristic'| translate}}
  </button>
  <button
    *ngIf="editingCharacteristic"
    type="button"
    mat-raised-button
    color="primary"
    (click)="updateCharacteristic()"
    [disabled]="!validFormCharacteristics"
  >
    {{'SaveChanges'| translate}}
    <mat-icon fontIcon="save"></mat-icon>
  </button>
</div>
