import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-task-reassignment',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent],
  template: `
    <div class="">
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
        {{ 'ReassignTasks.Title' | translate }}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <h3 style="font-weight:bold" class="mt-3 mb-3">{{ 'TaskFilter.Title' | translate }}</h3>
    <span class="mt-3 mb-3">{{ 'TaskFilter.Description' | translate }}</span>

    <router-outlet></router-outlet>
  `,
  styles: [],
})
export class TaskReassignmentComponent implements OnInit {
  constructor(private _translateService: TranslateService) {}

  inicio: string = this._translateService.instant('Inicio');
  // taskReassignment: string = this._translateService.instant('TaskTraySettings.Title');
  taskReassignment: string = 'Reasignar tareas';

  sections: { label: string; link: string }[] = [
    { label: this.taskReassignment, link: '/dashboard/task-reassignment' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.taskReassignment = this._translateService.instant(
        'ReassignTasks.Title'
      );
      this.sections[0].label = this.taskReassignment;
    });
  }
}
