import { CommonModule } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import {
  SectionEnum,
  SectionIndexModel,
} from 'src/app/shared/models/configuration-index';
import { PorductHomeModel } from 'src/app/shared/models/product';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { NewsHomeComponent } from './news/news.component';
import { OrganizationChartHomeComponent } from './organization-chart/organization-chart.component';
import { ProductsHomeComponent } from './products/products.component';
import { TopsHomeComponent } from './tops/tops.component';
import { IncentivesHomeComponent } from "./incentives/incentives.component";

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatCardModule,
    ProductsHomeComponent,
    NewsHomeComponent,
    TopsHomeComponent,
    OrganizationChartHomeComponent,
    IncentivesHomeComponent
],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements OnInit, OnDestroy {
  productList: PorductHomeModel[] = [];
  listSectionIndex: SectionIndexModel[] = [];
  idBusinessByCountry: number = 0;
  productsObject: SectionIndexModel = SectionIndexModel.fromObj({});
  topsObject: SectionIndexModel = SectionIndexModel.fromObj({});
  newsObject: SectionIndexModel = SectionIndexModel.fromObj({});
  organizationChartObject: SectionIndexModel = SectionIndexModel.fromObj({});
  idNews: number = 0;
  userIdSession: number = 0;
  userSessionName: string = "";

  constructor(
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    private _businessService: BusinessService,
    private _settingService: SettingService,
    private _userService: UserService,
  ) {
    this.listSectionIndex.sort((a, b) => a.iOrder - b.iOrder);
  }

  ngOnInit(): void {
    this.getSettingInit();
  }

  async getSettingInit() {
    let dataSettingInit = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = dataSettingInit.idBusinessByCountry;
    this.getSectionByBusinessCountry();
    this.getUserId(this.idBusinessByCountry);
  }

  getSectionByBusinessCountry() {
    this._businessService
      .getSectionByBusinessCountry(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.listSectionIndex = resp.result;
            this.productsObject = this.searchObjetProducts();
            this.topsObject = this.searchObjetTops();
            this.newsObject = this.searchObjetNews();
            this.organizationChartObject = this.searchObjetOrganizationChart();
          }
        }
      });
  }

  //Función para buscar objeto de productos
  searchObjetProducts() {
    let foundObject = this.listSectionIndex.find(
      (section: SectionIndexModel) =>
        section.fkIdSectionIndex === SectionEnum.Product
    );
    if (foundObject) {
      return foundObject;
    } else {
      return SectionIndexModel.fromObj({});
    }
  }
  //Función para buscar objeto de topes
  searchObjetTops() {
    let foundObject = this.listSectionIndex.find(
      (section: SectionIndexModel) =>
        section.fkIdSectionIndex === SectionEnum.Tops
    );
    if (foundObject) {
      return foundObject;
    } else {
      return SectionIndexModel.fromObj({});
    }
  }

  //Función para buscar objeto de noticia
  searchObjetNews() {
    let foundObject = this.listSectionIndex.find(
      (section: SectionIndexModel) =>
        section.fkIdSectionIndex === SectionEnum.News
    );
    if (foundObject) {
      return foundObject;
    } else {
      return SectionIndexModel.fromObj({});
    }
  }

  //Función para buscar objeto de organigrama
  searchObjetOrganizationChart() {
    let foundObject = this.listSectionIndex.find(
      (section: SectionIndexModel) =>
        section.fkIdSectionIndex === SectionEnum.OrganizationChart
    );
    if (foundObject) {
      return foundObject;
    } else {
      return SectionIndexModel.fromObj({});
    }
  }

  //Obtiene la información del usuario en sesión
  getUserPersonalList(idbusinessCountry: number) {
    this._userService
      .getUserPersonalList(this.userIdSession, idbusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          const userSessionName =  resp.result.vPersonName ?  resp.result.vPersonName.split(' '): '';
          this.userSessionName = userSessionName[0];
        }
      });
  }

  async getUserId(idbusinessCountry: number) {
    let userIdSession = await this._settingService.getDataSettingInit();
    if (userIdSession) {
      this.userIdSession = Number(userIdSession.idUser);
    }
    if (this.userIdSession) {
      this.getUserPersonalList(idbusinessCountry);
    }
  }

  ngOnDestroy(): void {}
}
