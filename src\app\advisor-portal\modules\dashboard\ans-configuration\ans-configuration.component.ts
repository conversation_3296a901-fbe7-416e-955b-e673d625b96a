import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatTabsModule } from '@angular/material/tabs';
import { AllAnsComponent } from './all-ans/all-ans.component';
import { HolidaysComponent } from './holidays/holidays.component';
import { ReportComponent } from './report/report.component';

@Component({
  selector: 'app-ans-configuration',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    BreadcrumbComponent,
    MatTabsModule,
    AllAnsComponent,
    HolidaysComponent,
    ReportComponent,
  ],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
        {{ 'AnsConfiguration.Title' | translate }}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <mat-tab-group>
      <mat-tab label="{{ 'AnsConfiguration.Tabs.ANS' | translate }}">
        <app-all-ans></app-all-ans>
      </mat-tab>
      <mat-tab label="{{ 'AnsConfiguration.Tabs.Holidays' | translate }}">
        <app-holidays></app-holidays>
      </mat-tab>
      <mat-tab label="{{ 'AnsConfiguration.Tabs.Report' | translate }}">
        <app-report></app-report>
      </mat-tab>
    </mat-tab-group>

    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class AnsConfigurationComponent {
  constructor(private _translateService: TranslateService) {}

  ans: string = this._translateService.instant('ANS');

  sections: { label: string; link: string }[] = [
    { label: this.ans, link: '/dashboard/ans-configuration' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.ans = this._translateService.instant('ANS');
      this.sections[0].label = this.ans;
    });
  }
}
