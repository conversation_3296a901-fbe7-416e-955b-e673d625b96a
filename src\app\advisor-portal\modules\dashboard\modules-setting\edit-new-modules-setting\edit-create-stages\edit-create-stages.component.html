<div class="mt-2 cont-replicate-stages">
    <button
    class="mx-1"
    type="button"
    (click)="openCloneDialog()" 
    mat-raised-button
    color="primary"
    [disabled]="isButtonDisabled()"
  >
    {{ 'Stage.ReplicateStages' | translate }}  
    <mat-icon iconPositionEnd fontIcon="flip_to_front"></mat-icon>
  </button>
  <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.ReplicateStage' | translate }}">help_outline</mat-icon>
</div>

<form [formGroup]="form"> 
    
    <!-- seleccion de submodulos/productos  -->
    <div class="row mt-3">
        <!-- seleccion de submodulos -->
        <mat-form-field *ngIf="objetModule.hasChildren" appearance="outline" class="col-4 mb-2">
            <mat-label> 
                {{'Stage.SubmoduleSelection' | translate}}
            </mat-label>
            <mat-select formControlName="fkIIdSubModule">
                <mat-option
                    *ngFor="let item of subModulesList"
                    [value]="item"
                   
                >
                    {{ item.vDescription }}
                </mat-option>
            </mat-select>
            <mat-error
            *ngIf="
                _utilsService.isControlHasError(form, 'fkIIdSubModule', 'required')
            "
            >
            {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>
        </mat-form-field>

         <!-- seleccion de producto -->
         <mat-form-field *ngIf="notProduct" appearance="outline" class="col-4 mb-2">
            <mat-label> 
                {{'Stage.ProductSelection' | translate}}
            </mat-label>
            <mat-select formControlName="fkIIdProductModule">
                <mat-option
                    *ngFor="let item of productsList"
                    [value]="item"
                    
                >
                    {{item.productName}}
                </mat-option>
            </mat-select>
            <mat-error
            *ngIf="notProduct &&
                _utilsService.isControlHasError(form, 'fkIIdProductModule', 'required')
            "
            >
            {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>
        </mat-form-field>
    </div>
    
    <!-- datatable etapas -->
    <div class="row mt-2">
        <div class="row mb-2">
        <h4 class="col-md-12">
            {{'Stage.Stages' | translate }}
            <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.StageTitle' | translate }}">help_outline</mat-icon>
        </h4>
        </div>
        <app-table
            [displayedColumns]="estructTableStages"
            [data]="dataTableStages"
            (iconClick)="controller($event)"
        ></app-table>
    </div>

    <!-- botón añadir etapa -->
    <div>
        <button
            class="w-auto mr-2"
            type="button"
            color="primary"
            (click)="openEditNewStageDialog()"
            mat-raised-button
        >
            {{'Stage.AddStage' | translate }}
            <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
        </button>
    </div>

</form>

<!-- modal crear/editar etapa -->
<ng-template #editNewStageModal>
    <app-modal2 (closeModal)="closedModal($event)" [titleModal]="'Stage.StageSettings' | translate">
        <ng-container body>
            <app-edit-new-stage
                [idProductModuleIn]="idProductModuleOut"
                [idStageIn]="idStageOut"
                [breadCrumHistoryIn]="breadCrumHistory"
                (resultOut)="getFormResult($event)"
            >
            </app-edit-new-stage>
        </ng-container>

    </app-modal2>
</ng-template>


<!-- modal duplicar etapa -->
<ng-template #cloneStageModal>
    <app-modal2 [titleModal]= "'Stage.ReplicateStages' | translate">
        <ng-container body> 
            <form [formGroup]="formReplicate"> 
                <app-choose-country-and-company
                    (valueForm)="validForm($event)"
                ></app-choose-country-and-company>
                <div class="row mt-3">
                    <!-- seleccion de modulos -->
                    <mat-form-field appearance="outline" class="col-4 mb-2">
                        <mat-label> 
                            {{'Stage.ModuleSelection' | translate}}
                        </mat-label>
                        <mat-select formControlName="idModule">
                            <mat-option
                                *ngFor="let item of modulesList"
                                [value]="item.pkIIdMenu"
                                (onSelectionChange)="getSubModules(item.pkIIdMenu, true)"
                            >
                                {{ item.vDescription }}
                            </mat-option>
                        </mat-select>
                        <mat-error
                        *ngIf="
                            _utilsService.isControlHasError(formReplicate, 'idModule', 'required')
                        "
                        >
                        {{ 'ThisFieldIsRequired' | translate }}
                        </mat-error>
                    </mat-form-field>

                    <!-- seleccion de submodulos -->
                    <mat-form-field *ngIf="moduleSelectedHasChildren" appearance="outline" class="col-4 mb-2">
                        <mat-label> 
                            {{'Stage.SubmoduleSelection' | translate}}
                        </mat-label>
                        <mat-select formControlName="idSubModule">
                            <mat-option
                                *ngFor="let item of subModulesListReplicate"
                                [value]="item.pkIIdMenu"
                                (onSelectionChange)="getProductByIdMenu(item.pkIIdMenu, true)"                                
                            >
                                {{ item.vDescription }}
                            </mat-option>
                        </mat-select>
                        <mat-error
                        *ngIf="
                            _utilsService.isControlHasError(formReplicate, 'idSubModule', 'required')
                        "
                        >
                        {{ 'ThisFieldIsRequired' | translate }}
                        </mat-error>
                    </mat-form-field>

                    <!-- seleccion de producto -->
                    <mat-form-field *ngIf="notProductReplicate" appearance="outline" class="col-4 mb-2">
                        <mat-label> 
                            {{'Stage.ProductSelection' | translate}}
                        </mat-label>
                        <mat-select formControlName="idProductModule">
                            <mat-option
                                *ngFor="let item of productsListReplicate"
                                [value]="item.idProductModule"      
                                                   
                            >
                                {{item.productName}}
                            </mat-option>
                        </mat-select>
                        <mat-error
                        *ngIf="
                            _utilsService.isControlHasError(formReplicate, 'idProductModule', 'required')
                        "
                        >
                        {{ 'ThisFieldIsRequired' | translate }}
                        </mat-error>
                    </mat-form-field>
                </div>
            </form>
        </ng-container>
        <!-- boton replicar etapas -->
        <ng-container customButtonRight>
            <button
            [disabled]="!formReplicate.valid"
            class="w-auto"
            type="button"
            (click)="cloneStage()"
            mat-raised-button
            color="primary"
        >
            {{'Stage.ReplicateStages' | translate }} 
        </button>
        </ng-container>    
    </app-modal2>
</ng-template>
<!-- end modal duplicar rol -->