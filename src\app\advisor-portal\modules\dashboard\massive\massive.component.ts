import { Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterModule,
} from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { filter, Subscription } from 'rxjs';

@Component({
  selector: 'app-massive',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    BreadcrumbComponent,
    MatIconModule,
    MatTooltipModule,
  ],
  template: `
    <div>
      <h2 class="h3">
        <ng-container *ngIf="currentUrl === 'massives' || currentUrl === ''">
          <img src="assets/img/layouts/masivos.svg" />
        </ng-container>
        <ng-container *ngIf="currentUrl === 'renewal'">
          <img src="assets/img/layouts/cached.svg" />
        </ng-container>
        {{ title | translate }}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styles: [],
})
export class MassiveComponent implements OnInit, OnDestroy {
  subscriptionUrl!: Subscription;
  currentUrl: string = '';
  idBusinesByCountry: number = 0;
  constructor(
    private _translateService: TranslateService,
    private _router: Router
  ) {}

  start: string = this._translateService.instant('Inicio');
  massive: string = this._translateService.instant('BulkUpload.Massives.Title');
  title: string = this._translateService.instant('BulkUpload.Massives.Title');
  policyLoading: string = this._translateService.instant(
    'BulkUpload.Massives.MassCreationPolicy.Title'
  );
  createTask: string = this._translateService.instant(
    'BulkUpload.Massives.MassCreationTasks.Title'
  );

  uploadDocuments: string = this._translateService.instant(
    'UploadDocuments.Title'
  );

  stateManagement: string = this._translateService.instant(
    'BulkUpload.Massives.MenuOptions.StateManagement'
  );

  policyRenewal: string = this._translateService.instant(
    'RenewalPolicy.Subtitle'
  );

  sections: { label: string; link: string }[] = [
    { label: this.start, link: '/dashboard' },
    { label: this.massive, link: '/dashboard/massive' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.start = this._translateService.instant('Inicio');
      this.massive = this._translateService.instant(
        'BulkUpload.Massives.Title'
      );
      console.log('aaaaaaaaaaaaaaaaaaa', this.currentUrl);
      switch (this.currentUrl) {
        case 'massives':
          this.title = this._translateService.instant(
            'BulkUpload.Massives.Title'
          );
          break;

        case 'policy':
          this.title = this._translateService.instant(
            'BulkUpload.Massives.MassCreationPolicy.Title'
          );
          break;
        case 'tasks':
          this.title = this._translateService.instant(
            'BulkUpload.Massives.MassCreationTasks.Title'
          );
          break;
        case 'documents':
          this.title = this._translateService.instant('UploadDocuments.Title');
          break;
        case 'renewal':
          this.title = this._translateService.instant('RenewalPolicy.Subtitle');
          break;
        case 'state':
          this.title = this._translateService.instant(
            'BulkUpload.Massives.MenuOptions.StateManagement'
          );
          break;

        default:
          this.title = this._translateService.instant(
            'BulkUpload.Massives.Title'
          );
          break;
      }
    });

    this.subscriptionUrl = this._router.events
      .pipe(
        filter(
          (event: any): event is NavigationEnd => event instanceof NavigationEnd
        )
      )
      .subscribe((event: NavigationEnd) => {
        this.getPrincipalTitle(event.urlAfterRedirects);
      });
  }

  //Obtiene el titulo principal según la ruta.
  getPrincipalTitle(url: string) {
    // Busca la sección 'dashboard/' en la URL
    const dashboardIndex = url.indexOf('dashboard/');
    // Extrae todo lo que está después de 'dashboard/'
    const afterDashboard = url.substring(dashboardIndex + 'dashboard/'.length);

    // Divide la cadena resultante en partes utilizando '/'
    const routeParts = afterDashboard.split('/');

    if (routeParts.length > 1) {
      if (routeParts[1].includes('renewal-policy')) {
        this.currentUrl = 'renewal';
        this.title = this.policyRenewal;
      } else if (routeParts[1].includes('policy')) {
        this.currentUrl = 'policy';
        this.title = this.policyLoading;
      } else if (routeParts[1].includes('tasks')) {
        this.currentUrl = 'tasks';
        this.title = this.createTask;
      } else if (routeParts[1].includes('documents')) {
        this.currentUrl = 'documents';
        this.title = this.uploadDocuments;
      } else if (routeParts[1].includes('state')) {
        this.currentUrl = 'state';
        this.title = this.stateManagement;
      }
    } else {
      this.title = this.massive;
      this.currentUrl = 'massives';
    }
  }

  ngOnDestroy(): void {
    this.subscriptionUrl.unsubscribe();
  }
}
