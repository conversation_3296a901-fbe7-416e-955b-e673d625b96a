<app-choose-country-and-company></app-choose-country-and-company>

<div class="row mb-2">
  <h4 class="col-md-12"> {{ "ConfigurationIndex.ModuleConfigure" | translate }} </h4>
</div>

<div class="col-12 col-md-12 mb-3" *ngIf="false">
  <h4>{{ "ConfigurationIndex.Index" | translate }}</h4>
  <mat-form-field>
    <mat-label>{{ "ConfigurationIndex.Select" | translate }}</mat-label>
    <mat-select (selectionChange)="userTypeChange($event)">
      <mat-option *ngFor="let item of userType" [value]="item.pkIIdUserType">
        {{ item.vName }}
      </mat-option>
    </mat-select>
  </mat-form-field>
</div>

<app-configuration-advisor *ngIf="idUserType == 1"></app-configuration-advisor>
<app-configuration-client *ngIf="idUserType == 2"></app-configuration-client>
