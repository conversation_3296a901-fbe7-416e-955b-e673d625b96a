import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { ClientModel } from 'src/app/shared/models/client';
import { SelectModel } from 'src/app/shared/models/select';
import { ClientService } from 'src/app/shared/services/client/client.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-new-client',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSlideToggleModule,
    TranslateModule,
    MatButtonModule,
    MatOptionModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRadioModule,
    MatIconModule,
    RouterModule
  ],
  templateUrl: './new-client.component.html',
  styleUrls: ['./new-client.component.scss']
})
export class NewClientComponent implements OnInit {

  formTypePerson: FormGroup = new FormGroup({});
  formClient: FormGroup = new FormGroup({});
  formBusiness: FormGroup = new FormGroup({});
  documentsTypes: SelectModel[] = [];
  documentsTypesFiltered: SelectModel[] = [];
  originalDocumentsTypesFiltered: SelectModel[] = [];
  typeClients: SelectModel[] = [];
  typeClient: any = 1;
  public emailPattern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';

  //variables relacionadas con empresa pais.
  idBusinessCountry: number = 0;


  constructor(
    private _fb: FormBuilder,
    public _utilsService: UtilsService,
    public _messageService: MessageService,
    private _parametersService: ParametersService,
    public _translateService: TranslateService,
    private _clientService: ClientService,
    private _customRouter: CustomRouterService,
    private _router: Router,
    private _settingService: SettingService,
  ) {
    this.typeClients = [
      { id: 1, name: 'Persona' },
      { id: 2, name: 'Empresa' }
    ]
  }

  ngOnInit(): void {
    this.getDataSettingInit();
    this.initForm();
    this.getListDocumentType();
  }

  initForm() {
    this.formTypePerson = this._fb.group({
      vTypePerson: [1, [Validators.required]],
      isClose: [true]
    })


    this.formClient = this._fb.group({
      iIdDocumentType: ['', [Validators.required]],
      vDocumentNumber: ['', [Validators.required, Validators.pattern("^[0-9-]*$")]],
      vFirstName: ['', [Validators.required]],
      vSecondName: ['', []],
      vSurname: ['', [Validators.required]],
      vSecondSurname: ['', []],
      VEmailUser: ['', [Validators.required, this.emailValidator]],
      vCellPhone: ['', [Validators.required, Validators.pattern("^[0-9]*$")]],
    });

    this.formBusiness = this._fb.group({
      iIdDocumentType: ['', [Validators.required]],
      vDocumentNumber: ['', [Validators.required, Validators.pattern("^[0-9]*$")]],
      vVerificationDigit: ['', [Validators.pattern("^[0-9]*$")]],
      vBusinessName: ['', [Validators.required]],
      VEmailUser: ['', [Validators.pattern(this.emailPattern)]],
      vCellPhone: ['', [Validators.pattern("^[0-9]*$")]],

      iIdDocumentTypeContact: ['', []],
      vDocumentNumberContact: ['', [Validators.pattern("^[0-9]*$")]],
      vFirstNameContact: ['', []],
      vSecondNameContact: ['', []],
      vSurnameContact: ['', []],
      vSecondSurnameContact: ['', []],
      VEmailUserContact: ['', [Validators.pattern(this.emailPattern)]],
      vCellPhoneContact: ['', [Validators.pattern("^[0-9]*$")]],
      vRoleContact: ['', []]
    });
  }

  emailValidator(control: FormControl): { [key: string]: any } | null {
    const emailRegex: RegExp = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const valid = emailRegex.test(control.value);
    return valid ? null : { 'invalidEmail': true };
  }

  getListDocumentType() {
    this._parametersService.getListCatalogGlobalDocumentTypes().subscribe({
      next: (response) => {
        if (!response.error) {

          this.originalDocumentsTypesFiltered = response.result;
          this.documentsTypes = response.result;
          this.documentsTypes = this.documentsTypes.filter((x: any) =>
            (x.name === 'Cédula de Ciudadanía' || x.name === 'Cédula de Extranjería' || x.name === 'Pasaporte')
          );
          if (this.typeClient === 1) {
            this.documentsTypesFiltered = this.documentsTypes.filter((x: any) =>
              x.name !== 'Nit' && (x.name === 'Cédula de Ciudadanía' || x.name === 'Cédula de Extranjería' || x.name === 'Pasaporte')
            );

          } else {
            this.documentsTypesFiltered = this.documentsTypes.filter((x: any) => x.name === 'Nit');
          }
        }
      },
    });
  }

  registerPersonDinamically() {

    let newClient: ClientModel = {
      pkIIdCustomer: 0,
      iIdDocumentType: this.typeClient === 1 ? this.formClient.value.iIdDocumentType : this.formBusiness.value.iIdDocumentType,
      vDocumentNumber: this.typeClient === 1 ? this.formClient.value.vDocumentNumber : this.formBusiness.value.vDocumentNumber,
      vCellPhone: this.typeClient === 1 ? this.formClient.value.vCellPhone : this.formBusiness.value.vCellPhone,
      vEmailUser: this.typeClient === 1 ? this.formClient.value.VEmailUser : this.formBusiness.value.VEmailUser,
      vTypePerson: this.typeClient === 1 ? this.formTypePerson.value.vTypePerson : this.formTypePerson.value.vTypePerson,
      bActive: 1,
      bIsRestricted: this.formTypePerson.value.isClose == 1 ? 0 : 1,
      FkIIdBusinessByCountry : this.idBusinessCountry 
    };

    if (this.typeClient === 1) {
      Object.assign(newClient, {
        vFirstName: this.formClient.value.vFirstName,
        vSecondName: this.formClient.value.vSecondName,
        vSurname: this.formClient.value.vSurname,
        vSecondSurname: this.formClient.value.vSecondSurname
      });
    } else {
      Object.assign(newClient, {
        vVerificationDigit: this.formBusiness.value.vVerificationDigit,
        vFullName: this.formBusiness.value.vBusinessName,
        contactCustomer: {
          pkIIdTypePerson: 0,
          vEmailUser: this.formBusiness.value.VEmailUserContact,
          bActive: 1,
          vPhone: this.formBusiness.value.vCellPhoneContact,
          fkIIdCustomer: 0,
          iIdDocumentType: this.formBusiness.value.iIdDocumentTypeContact,
          vDocumentNumber: this.formBusiness.value.vDocumentNumberContact,
          vFirstName: this.formBusiness.value.vFirstNameContact,
          vSecondName: this.formBusiness.value.vSecondNameContact,
          vSurname: this.formBusiness.value.vSurnameContact,
          vSecondSurname: this.formBusiness.value.vSecondSurnameContact,
          vRole: this.formBusiness.value.vRoleContact
        }
      });
    }
    
    this._clientService
      .saveCostumerWithContact(newClient)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp : any) => {

        if(!resp.error){
          this._messageService.messageSuccess(
            this._translateService.instant('Saved'),
            ''
          );
          this._customRouter.navigate(['/dashboard/clients']);
        }else{
          this._messageService.messageError(
            this._translateService.instant('ThereWasAError') +
            ': ' +
            resp.message
          );
        }
      });

  }

  changeForm(type: any) {

    if (type === 1) {
      this.documentsTypesFiltered = this.documentsTypes.filter((x: any) =>
        x.name !== 'Nit' && (x.name === 'Cédula de Ciudadanía' || x.name === 'Cédula de Extranjería' || x.name === 'Pasaporte')
      );

    } else {
      this.documentsTypesFiltered = this.originalDocumentsTypesFiltered.filter((x: any) => x.name === 'Nit');
    }
    this.typeClient = type;
    this.formBusiness.reset();
    this.formClient.reset();
  }

  goToClient(){
    this._customRouter.navigate(["/dashboard/clients"]);
  }
  
  //Get BusinessByCountry Initial Configuration.
  async getDataSettingInit() {
    let data = await this._settingService.getDataSettingInit();
    this.idBusinessCountry = data.idBusinessByCountry;
  }
}
