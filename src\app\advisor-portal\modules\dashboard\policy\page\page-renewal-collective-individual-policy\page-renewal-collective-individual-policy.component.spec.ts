import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PageRenewalCollectiveIndividualPolicyComponent } from './page-renewal-collective-individual-policy.component';

describe('PageRenewalCollectiveIndividualPolicyComponent', () => {
  let component: PageRenewalCollectiveIndividualPolicyComponent;
  let fixture: ComponentFixture<PageRenewalCollectiveIndividualPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PageRenewalCollectiveIndividualPolicyComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PageRenewalCollectiveIndividualPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
