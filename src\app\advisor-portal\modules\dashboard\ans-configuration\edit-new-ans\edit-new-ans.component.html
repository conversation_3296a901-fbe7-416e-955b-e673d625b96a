<form [formGroup]="formAddAns">
  <mat-tab-group>
    <mat-tab
      label="{{ 'AnsConfiguration.ModalAns.GeneralsTab.Title' | translate }}"
    >
      <div class="row mt-5">
        <div class="row">
          <!-- Select de Proceso -->
          <div class="col-md-6">
            <mat-form-field id="fkIdProcess" class="w-100">
              <mat-label>
                {{ "AnsConfiguration.Table.Process" | translate }}
              </mat-label>
              <mat-select formControlName="fkIdProcess">
                <mat-option
                  *ngFor="let item of processes"
                  [value]="item.pkIIdProcessFamily"
                  >{{ item.vNameProcess }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>
          <!-- Select de Producto -->
          <div class="col-md-6">
            <mat-form-field id="fkIdProduct" class="w-100">
              <mat-label>
                {{ "AnsConfiguration.Table.Product" | translate }}
              </mat-label>
              <mat-select formControlName="fkIdProduct">
                <mat-option
                  *ngFor="let item of products"
                  [value]="item.pkIIdProductModule"
                  >{{ item.vName }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="row">
          <!-- Select de Etapa -->
          <div class="col-md-6">
            <mat-form-field id="stage" class="w-100">
              <mat-label>
                {{ "AnsConfiguration.Table.Stage" | translate }}
              </mat-label>
              <mat-select formControlName="fkIdStage">
                <mat-option
                  *ngFor="let item of stage"
                  [value]="item.pkIIdStage"
                  >{{ item.vNameStage }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>
          <!-- Select de Estado -->
          <div class="col-md-6">
            <mat-form-field id="state" class="w-100">
              <mat-label> {{ "Status" | translate }} </mat-label>
              <mat-select formControlName="fkIIdState">
                <mat-option
                  *ngFor="let item of state"
                  [value]="item.pkIIdStageByState"
                  >{{ item.vState }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="row mt-2 mb-2">
          <h5>{{ "ANS" | translate }}</h5>
        </div>
        <!-- ANS Activo -->
        <div class="row">
          <mat-slide-toggle class="mb-3" formControlName="bActive">
            {{ "AnsConfiguration.ModalAns.GeneralsTab.AnsActive" | translate }}
          </mat-slide-toggle>
        </div>
        <div class="row mt-3">
          <!-- Validez inicial de ANS -->
          <mat-form-field appearance="outline" class="w-50">
            <mat-label>
              {{
                "AnsConfiguration.ModalAns.GeneralsTab.InitialValidityOfAns"
                  | translate
              }}
            </mat-label>
            <input
              matInput
              [matDatepicker]="initialValidityAns"
              formControlName="dDateInitial"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="initialValidityAns"
            ></mat-datepicker-toggle>
            <mat-datepicker #initialValidityAns></mat-datepicker>
          </mat-form-field>

          <!-- Validez final de ANS -->
          <mat-form-field appearance="outline" class="w-50">
            <mat-label>
              {{
                "AnsConfiguration.ModalAns.GeneralsTab.FinalValidityOfAns"
                  | translate
              }}
            </mat-label>
            <input
              matInput
              [matDatepicker]="finalValidityAns"
              formControlName="dDateEnd"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="finalValidityAns"
            ></mat-datepicker-toggle>
            <mat-datepicker #finalValidityAns></mat-datepicker>
          </mat-form-field>
        </div>

        <!-- Modificar condiciones generales -->
        <div class="row">
          <mat-slide-toggle class="mb-3" formControlName="bSpecificCondition">
            {{
              "AnsConfiguration.ModalAns.GeneralsTab.ModifyGeneralConditions"
                | translate
            }}
          </mat-slide-toggle>
        </div>

        <!--Condiciones Genrales Component-->
        <div class="row" *ngIf="bSpecificCondition">
          <div class="col md-12">
            <app-general-conditions
              [ansToEdit]="ansToEdit"
              (currentFormValue)="getGeneralConditionsFormValue($event)"
            ></app-general-conditions>
          </div>
        </div>
      </div>
    </mat-tab>
    <mat-tab
      label="{{ 'AnsConfiguration.ModalAns.MeasurementTab.Title' | translate }}"
    >
      <div class="row">
        <h6>
          {{
            "AnsConfiguration.ModalAns.MeasurementTab.InductorSection"
              | translate
          }}
        </h6>
        <p class="description">
          {{
            "AnsConfiguration.ModalAns.MeasurementTab.InductorDescription"
              | translate
          }}
        </p>
      </div>
      <!-- Sección Inductor -->
      <div class="row">
        <!-- Cantidad Inductor -->
        <div class="col-md-6">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{
                "AnsConfiguration.ModalAns.MeasurementTab.AmountLabel"
                  | translate
              }}
            </mat-label>
            <input matInput formControlName="iCantInstructor" type="number" />
            <mat-error
              *ngIf="
                utilsService.isControlHasError(
                  formAddAns,
                  'iCantInstructor',
                  'required'
                )
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- Select de Medición Inductor -->
        <div class="col-md-6">
          <mat-form-field id="vUnitMeasurementInstructor" class="w-100">
            <mat-label>
              {{ "AnsConfiguration.ModalAns.MeasurementTab.Title" | translate }}
            </mat-label>
            <mat-select formControlName="vUnitMeasurementInstructor">
              <mat-option
                *ngFor="let item of measurement"
                [value]="item.Name"
                >{{ item.Name }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div class="row">
        <!--Medición tomada desde:-->
        <div class="col-md-6">
          <mat-form-field id="iMeasuringFrom" class="w-100">
            <mat-label>
              {{
                "AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenFrom"
                  | translate
              }}
            </mat-label>
            <mat-select formControlName="iMeasuringFrom">
              <mat-option
                *ngFor="let item of measurementTakenFrom"
                [value]="item.value"
                >{{ item.name }}</mat-option
              >
            </mat-select>
          </mat-form-field>
          <p class="description">
            {{
              "AnsConfiguration.ModalAns.MeasurementTab.DescriptionMeasurement"
                | translate
            }}
          </p>
        </div>

        <!--Medición tomada hasta:-->
        <div class="col-md-6">
          <mat-form-field id="iMeasuringUntil" class="w-100">
            <mat-label>
              {{
                "AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenUpTo"
                  | translate
              }}
            </mat-label>
            <mat-select formControlName="iMeasuringUntil">
              <mat-option
                *ngFor="let item of measurementTakenUpTo"
                [value]="item.value"
                >{{ item.name }}</mat-option
              >
            </mat-select>
          </mat-form-field>
          <p class="description">
            {{
              "AnsConfiguration.ModalAns.MeasurementTab.DescriptionMeasurement"
                | translate
            }}
          </p>
        </div>
      </div>

      <div class="row mt-2">
        <h6>
          {{
            "AnsConfiguration.ModalAns.MeasurementTab.GoalSection" | translate
          }}
        </h6>
        <p class="description">
          {{
            "AnsConfiguration.ModalAns.MeasurementTab.GoalDescription"
              | translate
          }}
        </p>
      </div>
      <!-- Sección Meta -->
      <div class="row">
        <!-- Cantidad Meta -->
        <div class="col-md-6">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{
                "AnsConfiguration.ModalAns.MeasurementTab.AmountLabel"
                  | translate
              }}
            </mat-label>
            <input matInput type="number" formControlName="iCantGoal" />
            <mat-error
              *ngIf="
                utilsService.isControlHasError(
                  formAddAns,
                  'iCantGoal',
                  'required'
                )
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- Select de Medición Meta -->
        <div class="col-md-6">
          <mat-form-field id="vUnitMeasurementGoal" class="w-100">
            <mat-label>
              {{ "AnsConfiguration.ModalAns.MeasurementTab.Title" | translate }}
            </mat-label>
            <mat-select formControlName="vUnitMeasurementGoal">
              <mat-option
                *ngFor="let item of measurement"
                [value]="item.Name"
                >{{ item.Name }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div class="row">
        <h6>
          {{
            "AnsConfiguration.ModalAns.MeasurementTab.AlertSection" | translate
          }}
        </h6>
        <p class="description">
          {{
            "AnsConfiguration.ModalAns.MeasurementTab.AlertDescription"
              | translate
          }}
        </p>
      </div>
      <!-- Sección Alerta -->
      <div class="row">
        <!-- Cantidad Alerta -->
        <div class="col-md-6">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{
                "AnsConfiguration.ModalAns.MeasurementTab.AmountLabel"
                  | translate
              }}
            </mat-label>
            <input matInput type="number" formControlName="iCantAlert" />
            <mat-error
              *ngIf="
                utilsService.isControlHasError(
                  formAddAns,
                  'iCantAlert',
                  'required'
                )
              "
            >
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- Select de Medición Alerta -->
        <div class="col-md-6">
          <mat-form-field id="vUnitMeasurementAlert" class="w-100">
            <mat-label>
              {{ "AnsConfiguration.ModalAns.MeasurementTab.Title" | translate }}
            </mat-label>
            <mat-select formControlName="vUnitMeasurementAlert">
              <mat-option
                *ngFor="let item of measurement"
                [value]="item.Name"
                >{{ item.Name }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</form>
