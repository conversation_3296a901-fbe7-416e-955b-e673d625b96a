.cont-fieles {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
  /* height: 100vh; */
}

.cont-template,
.cont-upload {
  width: 100%; /* Ajusta según tus necesidades */
  max-width: 30dvw; /* Tamaño máximo para el contenido */
  margin-bottom: 20px; /* Espacio entre los divs */
}

.cont-template {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid #c9cdd0;
}

.cont-info-file {
  flex-grow: 1; /* Ocupa todo el espacio disponible en el contenedor */
  margin-right: 1rem; /* Espacio entre el texto y el icono */
  overflow: hidden;
}

.cont-info-file p {
  margin: 0;
  padding: 0;
  white-space: nowrap; /* No permitir el salto de línea */
  overflow: hidden; /* Ocultar el texto que se desborde */
  text-overflow: ellipsis; /* Mostrar los tres puntos (...) cuando el texto es muy largo */
  max-width: 100%; /* Asegura que no se desborde */
}

.cont-info-file .description {
  font-size: 0.8rem;
  color: #666;
}

.cont-msg-info {
  justify-content: start;
  align-items: center;
  width: auto;
  padding: 4px;
  height: 20px;
  border: 1px solid, #1d418e;
  background: #e1efff;
  color: #1d418e;
  padding: 0;
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: -0.24px;
  padding-left: 4px;
  padding-right: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
}

.label-button {
  font-size: 1.2rem !important;
  font-weight: bold;
  text-decoration: none;
}

.title-local {
  font-weight: bold;
}

.cont-footer-table {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

::ng-deep .swal2-cancel {
  background-color: #808080 !important;
}
