import { Routes } from '@angular/router';
import { ProductSectionsComponent } from './product-sections.component';

export default [
  {
    path: '',
    component: ProductSectionsComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-product/all-product.component').then(
            (c) => c.AllProductComponent
          ),
      },
      {
        path: 'all-characteristics/:id/:product',
        loadComponent: () =>
          import('./all-characteristics/all-characteristics.component').then(
            (c) => c.AllCharacteristicsComponent
          ),
      },
      {
        path: 'create-characteristics/:id/:product',
        loadComponent: () =>
          import(
            './edit-new-characteristics/edit-new-characteristics.component'
          ).then((c) => c.EditNewCharacteristicsComponent),
      },
      {
        path: 'edit-characteristics/:id/:product/:pkIIdSectionPlanProduct',
        loadComponent: () =>
          import(
            './edit-new-characteristics/edit-new-characteristics.component'
          ).then((c) => c.EditNewCharacteristicsComponent),
      },
    ],
  },
] as Routes;
