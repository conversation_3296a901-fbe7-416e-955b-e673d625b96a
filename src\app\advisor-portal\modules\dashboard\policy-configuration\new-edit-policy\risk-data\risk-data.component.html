<div class="row mt-5">
  <div class="col-md-12">
      <h4>{{"PolicyConfiguration.RiskData.Title" | translate}}</h4>
  </div>
</div>
<form [formGroup]="formRiskData">
  <div class="row mt-2">
      <!-- Pestañas -->
      <div class="col-md-4 col-sm-4 mb-2">
          <mat-form-field appearance="fill" class="select-look w-100">
              <mat-label>
                  {{ "Product.Tabs" | translate }}
              </mat-label>
              <mat-select formControlName="tab">
                  <mat-option *ngFor="let item of tabList" [value]="item">
                      {{ item.vName }}
                  </mat-option>
              </mat-select>
          </mat-form-field>
          <mat-error *ngIf="utilsSvc.isControlHasError(formRiskData, 'tab', 'required')">
              {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
      </div>
      <!-- Cantidad máxima de beneficiarios -->
      <div class="col-md-4 col-sm-4 mb-2" *ngIf="showBeneficiary">
          <mat-form-field appearance="fill" class="select-look w-100">
              <mat-label>
                  {{ "PolicyConfiguration.RiskData.BeneficiariesNumber" | translate }}
              </mat-label>
              <mat-select formControlName="maximumNumberBenefici">
                  <mat-option *ngFor="let item of maximumNumberBeneficiaries" [value]="item">
                      {{ item }}
                  </mat-option>
              </mat-select>
              <mat-error *ngIf="utilsSvc.isControlHasError(formRiskData, 'maximumNumberBenefici', 'required')">
                  {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
          </mat-form-field>
      </div>
      <!-- Guardar Cantidad máxima de beneficiarios -->
      <div class="col-md-4 col-sm-4 mb-2 mt-1" *ngIf="showBeneficiary">
          <button [disabled]="!validFormRiskData" class="w-auto mx-3" type="button" mat-raised-button color="primary"
              (click)="saveMaximumNumberBenefici()">
              {{ "Save" | translate }}
              <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
          </button>
      </div>
      <!-- Nombre de la pestaña -->
      <div class="col-md-4 col-sm-4 mb-2" *ngIf="showTabName">
          <mat-form-field appearance="outline" class="w-100">
              <mat-label>
                  {{ "Product.TabName" | translate }}
              </mat-label>
              <input matInput formControlName="nameTab" PreventionSqlInjector />
              <mat-error *ngIf="utilsSvc.isControlHasError(formRiskData, 'nameTab', 'required')">
                  {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
          </mat-form-field>
      </div>
      <!-- Guardar Nombre de la pestaña -->
      <div class="col-md-4 col-sm-4 mb-2 mt-1" *ngIf="showTabName">
          <button [disabled]="!validFormRiskData" class="w-auto mx-3" type="button" mat-raised-button color="primary"
              (click)="saveTabName()">
              {{ "Save" | translate }}
              <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
          </button>

      </div>
  </div>
</form>

<div *ngIf="idTab != 0" class="row mb-2">
  <div class="col-12 col-md-12">
      <div class="row mb-2">
          <h3 class="col-md-12">
              {{ "Product.Sections" | translate }}
          </h3>
      </div>
      <app-table [displayedColumns]="estructTableSections" [data]="dataTableSection"
          (iconClick)="EditSection($event)"></app-table>
      <button class="mx-3 mb-2" type="button" mat-raised-button color="primary"
          (click)="open('editNewSectionModal'); isEditingSection = false">
          {{ "Add" | translate }}
          <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
  </div>
</div>

<div *ngIf="idTab != 0" class="row mb-2">
  <div class="col-12 col-md-12">
      <div class="row mb-2">
          <h3 class="col-md-12">
              {{ "Product.Fields" | translate }}
          </h3>
      </div>
      <app-table [displayedColumns]="estructTableFields" [data]="dataTableFields"
          (iconClick)="EditField(true, $event)"></app-table>
      <button class="mx-3 mb-2" type="button" mat-raised-button color="primary"
          (click)="open('editNewFieldModal'); isEditingField = false">
          {{ "Add" | translate }}
          <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
  </div>
</div>

<!-- Modal de secciones -->
<ng-template #editNewSectionModal>
  <app-modal2 [showCancelButtonBelow]="false" [titleModal]="
      isEditingSection
        ? ('Product.UpdateSection' | translate)
        : ('Product.NewSection' | translate)
    ">
      <ng-container body>
          <form [formGroup]="formSection">
              <mat-slide-toggle class="mb-3" formControlName="b_Active">
                  {{ "Product.ActiveForm" | translate }}
              </mat-slide-toggle>
              <mat-form-field appearance="outline" class="w-100 mb-2">
                  <mat-label>
                      {{ "Product.SectionName" | translate }}
                  </mat-label>
                  <input matInput formControlName="v_Name" PreventionSqlInjector />
                  <mat-error *ngIf="utilsSvc.isControlHasError(formSection, 'v_Name', 'required')">
                      {{ "ThisFieldIsRequired" | translate }}
                  </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="select-look w-100">
                  <mat-label>
                      {{ "Product.TabWhichBelongs" | translate }}
                  </mat-label>
                  <mat-select formControlName="fk_i_idTab" id="fk_i_idTab" required>
                      <mat-option *ngFor="let item of tabList" [value]="item.pkIIdTab">
                          {{ item.vName }}
                      </mat-option>
                  </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline" class="select-look w-100">
                  <mat-label>
                      {{ "Product.Order" | translate }}
                  </mat-label>
                  <mat-select formControlName="i_Order" id="i_Order" required>
                      <mat-option *ngFor="let order of orderListSection" [value]="order">
                          {{ order }}
                      </mat-option>
                  </mat-select>
              </mat-form-field>
          </form>
      </ng-container>
      <!-- botones -->
      <ng-container customButtonRight>
          <a class="w-auto mr-3" mat-button (click)="closeModal()"><span class="label-button">{{'Cancel'|
                  translate}}</span></a>

          <button *ngIf="isEditingSection && !standarSection" class="mx-3 mb-2" type="button" mat-raised-button
              (click)="deleteSection()">
              {{ ["Product.DeleteSection" | translate] }}
              <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
          </button>
          <button class="mx-3 mb-2" type="button" mat-raised-button color="primary" (click)="saveSection()">
              {{
              isEditingSection
              ? ["Product.UpdateSection" | translate]
              : ["Product.AddSection" | translate]
              }}
              <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
          </button>
      </ng-container>
  </app-modal2>
</ng-template>

<!-- Modal de campos -->
<ng-template #editNewFieldModal>
  <app-modal2 [showCancelButtonBelow]="false" [titleModal]="
      isEditingField
        ? ('Product.UpdateField' | translate)
        : ('Product.NewField' | translate)
    ">
      <ng-container body>
          <div class="row mb-2">
              <div class="border col-md-12 col-sm-12">
                  <form [formGroup]="formField">
                      <div class="row">
                          <mat-checkbox formControlName="bUsingExistent" class="my-4">
                              {{ "Product.ConfigureExistingField" | translate }}
                          </mat-checkbox>
                          <mat-form-field *ngIf="formField.get('bUsingExistent')?.value" appearance="outline"
                              class="select-look w-100">
                              <mat-label>
                                  {{ "Product.FieldType" | translate }}
                              </mat-label>
                              <mat-select formControlName="fkIdFieldExistent" id="idField">
                                  <mat-option *ngFor="let item of allFields" [value]="item.pkIIdField">
                                      {{ item.vNameField }}
                                  </mat-option>
                              </mat-select>
                          </mat-form-field>
                      </div>
                      <mat-slide-toggle class="my-4" formControlName="bActive">
                          {{ "Product.ActiveForm" | translate }}
                      </mat-slide-toggle>
                      <mat-form-field appearance="outline" class="select-look w-100">
                          <mat-label>
                              {{ "Product.FieldType" | translate }}
                          </mat-label>
                          <mat-select formControlName="fkIIdFieldType" id="fkIIdFieldType"
                              (ngModelChange)="resetFieldSetDefaultValue()" required>
                              <mat-option *ngFor="let item of fieldTypes" [value]="item.pkIIdFieldType">
                                  {{ item.vName }}
                              </mat-option>
                          </mat-select>
                      </mat-form-field>
                      <mat-form-field appearance="outline" class="w-100 mb-2">
                          <mat-label>
                              {{ "Product.FieldName" | translate }} (visible)
                          </mat-label>
                          <input matInput formControlName="vNameField" PreventionSqlInjector />
                          <mat-error *ngIf="utilsSvc.isControlHasError(formField, 'vNameField', 'required')">
                              {{ "ThisFieldIsRequired" | translate }}
                          </mat-error>
                      </mat-form-field>
                      <mat-form-field appearance="outline" class="w-100 mb-2">
                          <mat-label>
                              {{ "Product.NameInDataBase" | translate }}
                          </mat-label>
                          <input matInput formControlName="vNameFieldDb" [readonly]="true" PreventionSqlInjector />
                          <mat-error *ngIf="
                        utilsSvc.isControlHasError(formField, 'vNameFieldDb', 'required')
                      ">
                              {{ "ThisFieldIsRequired" | translate }}
                          </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline" class="select-look w-100 mb-2">
                          <mat-label>
                              {{ "Product.Order" | translate }}
                          </mat-label>
                          <mat-select formControlName="iOrder" id="iOrder" required>
                              <mat-option *ngFor="let order of orderListFields" [value]="order">
                                  {{ order }}
                              </mat-option>
                          </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline" class="w-100 mb-2">
                          <mat-label>
                              {{ "Product.Description" | translate }}
                          </mat-label>
                          <input matInput formControlName="vDescription" PreventionSqlInjector />
                          <mat-error *ngIf="
                        utilsSvc.isControlHasError(formField, 'vDescription', 'required')
                      ">
                              {{ "ThisFieldIsRequired" | translate }}
                          </mat-error>
                      </mat-form-field>
                      <mat-form-field appearance="outline" class="select-look w-100">
                          <mat-label>
                              {{ "Product.Tab" | translate }}
                          </mat-label>
                          <mat-select formControlName="idTab" (ngModelChange)="updateSectionInfo()" required>
                              <mat-option *ngFor="let item of tabList" [value]="item.pkIIdTab">
                                  {{ item.vName }}
                              </mat-option>
                          </mat-select>
                      </mat-form-field>
                      <mat-form-field appearance="outline" class="select-look w-100">
                          <mat-label>
                              {{ "Product.Section" | translate }}
                          </mat-label>
                          <mat-select formControlName="idSection" id="idSection" required>
                              <mat-option *ngFor="let item of selectedTabSections" [value]="item.pkIIdSection">
                                  {{ item.vName }}
                              </mat-option>
                          </mat-select>
                      </mat-form-field>

                      <mat-checkbox formControlName="bRequired" class="my-4">
                          {{ "Product.Required" | translate }}
                      </mat-checkbox>
                      <mat-checkbox formControlName="bisKeyField" class="my-4">
                          {{ "Product.FieldKey" | translate }}
                      </mat-checkbox>
                      <mat-checkbox formControlName="bIsPerson" class="my-4">
                          {{ "FormConfiguration.Fields.PersonField" | translate }}
                      </mat-checkbox>
                      <mat-checkbox formControlName="bIsDependent" class="my-4">
                        {{ "Product.DependentField" | translate }}
                      </mat-checkbox>

                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.DropDownList" formControlName="bIsMultiple" class="my-4">
                        {{ "Selección multiple" | translate }}
                      </mat-checkbox>


                      <ng-container *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Numeric ||  formField.get('fkIIdFieldType')?.value === typeField.Money ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Text || formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.TextArea">
                          <mat-card
                              [ngClass]="{'group-flow': formField.get('fkIIdFieldType')?.value === typeField.TextArea}">
                              <mat-card-title><strong>{{ "Product.FieldRules" | translate }}</strong>
                              </mat-card-title>
                              <mat-card-content>
                                  <div class="row my-2">
                                      <div class="col-6 col-md-6">
                                          <!--Visible for numeric, text field, text area. Set size min that a input has to have-->
                                          <mat-form-field *ngIf="
                                                  formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Text ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.TextArea
                                                " appearance="outline" class="w-100 mb-2">
                                              <mat-label>
                                                  {{ "Product.MinimumSize" | translate }}
                                              </mat-label>
                                              <input matInput type="number" formControlName="iMinLength" />
                                          </mat-form-field>
                                      </div>
                                      <div class="col-6 col-md-6">
                                          <!--Visible for numeric, text field, UploadDocument, text area.
                                                      Set size max that a input has to have and set max size for a file -->
                                          <mat-form-field
                                              *ngIf=" formField.get('fkIIdFieldType')?.value === typeField.Numeric ||  formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                         formField.get('fkIIdFieldType')?.value === typeField.Text || formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments ||
                                                         formField.get('fkIIdFieldType')?.value === typeField.TextArea"
                                              appearance="outline" class="w-100 mb-2">
                                              <mat-label>
                                                  {{ "Product.MaximumSize" | translate }}
                                              </mat-label>
                                              <input matInput type="number" formControlName="iMaxLength"
                                                  [max]="maxLengthText" />
                                              <mat-hint align="start"
                                                  *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Text">
                                                  {{ "Product.Maximun" | translate }} {{ maxTextOnlyLength }}
                                                  {{ "Product.MaxLengthText" | translate }}
                                              </mat-hint>
                                              <mat-error
                                                  *ngIf=" formField.get('iMaxLength')?.value < formField.get('iMinLength')?.value">
                                                  {{ "Product.MaximumSizeMustBeGreaterThanMinimumSize" |
                                                  translate }}
                                              </mat-error>
                                              <mat-error *ngIf="formField.get('iMaxLength')?.hasError('max')">
                                                  {{ "Product.MaxCharacters" | translate }} {{ maxLengthText
                                                  }}
                                              </mat-error>
                                          </mat-form-field>
                                      </div>
                                  </div>
                                  <div class="row my-2">
                                      <div class="col-12 col-md-12">
                                          <mat-checkbox
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric"
                                              formControlName="bIsEmail" class="my-4">
                                              {{ "Product.CheckIsEmail" | translate }}
                                          </mat-checkbox>
                                      </div>
                                  </div>
                                  <div class="row my-2">
                                      <div class="col-6 col-md-6">
                                          <mat-checkbox
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                                          formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                                          formField.get('fkIIdFieldType')?.value === typeField.Money"
                                              formControlName="bIsValueMax" class="my-4">
                                              {{ "Product.MaximumValue" | translate }}
                                          </mat-checkbox>
                                      </div>
                                      <div class="col-6 col-md-6">
                                          <mat-form-field
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                                      formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                                      formField.get('fkIIdFieldType')?.value === typeField.Money"
                                              appearance="outline" class="w-100 mb-2">
                                              <mat-label>
                                                  {{ "Product.EnterMaximumValue" | translate }}
                                              </mat-label>
                                              <input matInput type="number" formControlName="vValueMax" />
                                              <mat-error
                                                  *ngIf="formField.get('vValueMax')?.value < formField.get('vValueMin')?.value">
                                                  {{
                                                  "Product.MaximumSizeMustBeGreaterThanMinimumSize" |
                                                  translate
                                                  }}
                                              </mat-error>
                                              <mat-error
                                                  *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                                  <span
                                                      *ngIf="utilsSvc.isControlHasError(formField, 'vValueMax', 'required')">
                                                      {{ 'ThisFieldIsRequired' | translate }}
                                                  </span>
                                              </mat-error>
                                          </mat-form-field>
                                      </div>
                                  </div>
                                  <div class="row my-2">
                                      <div class="col-6 col-md-6">
                                          <mat-checkbox
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                                          formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                                          formField.get('fkIIdFieldType')?.value === typeField.Money"
                                              formControlName="bIsValueMin" class="my-4">
                                              {{ "Product.MinimumValue" | translate }}
                                          </mat-checkbox>
                                      </div>
                                      <div class="col-6 col-md-6">
                                          <mat-form-field
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                                      formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                                      formField.get('fkIIdFieldType')?.value === typeField.Money"
                                              appearance="outline" class="w-100 mb-2">
                                              <mat-label>
                                                  {{ "Product.EnterMinimumValue" | translate }}
                                              </mat-label>
                                              <input matInput type="number" formControlName="vValueMin" />
                                              <mat-error
                                                  *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                                  <span
                                                      *ngIf="utilsSvc.isControlHasError(formField, 'vValueMin', 'required')">
                                                      {{ 'ThisFieldIsRequired' | translate }}
                                                  </span>
                                              </mat-error>
                                          </mat-form-field>
                                      </div>
                                  </div>
                              </mat-card-content>
                          </mat-card>
                      </ng-container>


                      <!--Visible for dependent field-->
                      <ng-container *ngIf="formField.get('bIsDependent')?.value">
                        <mat-form-field  appearance="outline"
                            class="select-look w-100">
                            <mat-label>
                                {{ "Product.DependentField" | translate }}
                            </mat-label>
                            <mat-select formControlName="fkIIdParent" id="idParent" required>
                                <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdFieldModule">
                                    {{ item.vNameField }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field *ngIf="catalogs.length>0"  appearance="outline"
                            class="select-look w-100">
                            <mat-label>
                                Opción a la que es dependiente
                            </mat-label>
                            <mat-select formControlName="iOptionDependent" multiple id="iOptionDependent">
                                <mat-option [value]="null">
                                    Opción a la que es dependiente
                                </mat-option>
                                <mat-option *ngFor="let item of catalogs" [value]="item.id">
                                    {{ item.name }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        </ng-container>

                      <mat-form-field class="w-100" *ngIf="
                      formField.get('fkIIdFieldType')?.value ===
                      typeField.UploadDocuments
                    ">
                          <mat-label>{{ "Product.TypeFile" | translate }}</mat-label>
                          <div class="input-container">
                              <input matInput formControlName="selectedFileTypes" [readonly]="true"
                                  (click)="open('typeUpload')" placeholder="Seleccione los tipos de archivo" />
                              <button mat-icon-button (click)="open('typeUpload')">
                                  <mat-icon iconPositionEnd fontIcon="edit"></mat-icon>
                              </button>
                          </div>
                      </mat-form-field>
                      <mat-checkbox *ngIf="
                      formField.get('fkIIdFieldType')?.value ===
                      typeField.UploadDocuments
                    " formControlName="bAllowMultipleUploads" class="my-4">
                          {{ "Product.MultipleFile" | translate }}
                      </mat-checkbox>

                      <mat-form-field *ngIf="
                      formField.get('fkIIdFieldType')?.value === typeField.Radio
                    " appearance="outline" class="w-100 mb-2">
                          <mat-label>
                              {{ "Product.OptionNumber" | translate }}
                          </mat-label>
                          <input (focusout)="updateOptions()" matInput type="number" formControlName="options" />
                      </mat-form-field>
                      <div *ngIf="
                      formField.get('fkIIdFieldType')?.value === typeField.Radio
                    " formArrayName="optionValues">
                          <div *ngFor="let option of orderItems.controls; let i = index">
                              <mat-form-field appearance="outline" class="w-100 mb-2">
                                  <mat-label>
                                      {{ "Product.Option" | translate }} {{ i + 1 }}
                                  </mat-label>
                                  <input id="optionInput-{{ i }}" matInput type="text" [formControlName]="i"
                                      (focusout)="updateOptionValue(i, $event.target)" />
                              </mat-form-field>
                          </div>
                      </div>

                      <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                          appearance="outline" class="select-look w-100">
                          <mat-label>
                              {{ "Product.FormatDate" | translate }}
                          </mat-label>
                          <mat-select formControlName="vFormat" [required]="
                        formField.get('fkIIdFieldType')?.value === typeField.Date
                      ">
                              <mat-option *ngFor="let item of typeFormatDate" [value]="item.format">
                                  {{ item.format }}
                              </mat-option>
                          </mat-select>
                      </mat-form-field>
                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                          formControlName="bShowYear" class="my-4">
                          {{ "Product.ShowYear" | translate }}
                      </mat-checkbox>
                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                          formControlName="bShowMonth" class="my-4">
                          {{ "Product.ShowMonth" | translate }}
                      </mat-checkbox>
                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                          formControlName="bShowDay" class="my-4">
                          {{ "Product.ShowDay" | translate }}
                      </mat-checkbox>
                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                          formControlName="bShowHour" class="my-4">
                          {{ "Product.ShowHour" | translate }}
                      </mat-checkbox>

                      <ng-container *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date">
                          <mat-card>
                              <mat-card-title><strong>{{ "Product.FieldRules" | translate }}</strong>
                              </mat-card-title>
                              <mat-card-content>
                                  <div class="row my-3">
                                      <div class="col-4 col-md-4">
                                          <mat-checkbox
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                              formControlName="bIsMinDateRequerid" class="my-4">
                                              {{ "Product.MinimumDateRequired" | translate }}
                                          </mat-checkbox>
                                      </div>
                                      <div class="col-4 col-md-4">
                                          <mat-form-field
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                              appearance="outline" class="select-look w-100">
                                              <mat-label>
                                                  {{ "Product.Type" | translate }}
                                              </mat-label>
                                              <mat-select formControlName="vTypeDateMin"
                                                  (ngModelChange)="onChangeTypeMinDate($event)"
                                                  [required]="formField.get('fkIIdFieldType')?.value === typeField.Date">
                                                  <mat-option *ngFor="let item of typeDateMinFilter"
                                                      [value]="item.Name">
                                                      {{ item.Name }}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>
                                      </div>
                                      <div class="col-4 col-md-4">
                                          <!-- Campo de fecha -->
                                          <mat-form-field appearance="outline" class="w-100 mb-2">
                                              <mat-label>{{ "Product.EnterMinimumDate" | translate }}
                                              </mat-label>
                                              <input matInput [mode]="modeDatePicker" [dpDayPicker]="config"
                                                  theme="dp-material" formControlName="dMinDateRequerid"
                                                  [placeholder]="placeholderText">
                                              <mat-icon class="click" *ngIf="!clockTypeIcon" mat-icon-button
                                                  matSuffix>today</mat-icon>
                                              <mat-icon class="click" *ngIf="clockTypeIcon" mat-icon-button
                                                  matSuffix>query_builder</mat-icon>
                                              <mat-error
                                                  *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                                  <span
                                                      *ngIf="utilsSvc.isControlHasError(formField, 'dMinDateRequerid', 'required')">
                                                      {{ 'ThisFieldIsRequired' | translate }}
                                                  </span>
                                              </mat-error>
                                              <mat-error
                                                  *ngIf="formField.get('dMinDateRequerid')?.hasError('dateRangeInvalid')">
                                                  {{ "Product.MinimumDateWarning" | translate }}
                                              </mat-error>
                                          </mat-form-field>
                                      </div>
                                  </div>
                                  <div class="row my-3">
                                      <div class="col-4 col-md-4">
                                          <mat-checkbox
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                              formControlName="bIsMaxDateRequerid" class="my-4">
                                              {{ "Product.MaximumDateRequired" | translate }}
                                          </mat-checkbox>
                                      </div>
                                      <div class="col-4 col-md-4">
                                          <mat-form-field
                                              *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                              appearance="outline" class="select-look w-100">
                                              <mat-label>
                                                  {{ "Product.Type" | translate }}
                                              </mat-label>
                                              <mat-select formControlName="vTypeDateMax"
                                                  (ngModelChange)="onChangeTypeMaxDate($event)"
                                                  [required]="formField.get('fkIIdFieldType')?.value === typeField.Date">
                                                  <mat-option *ngFor="let item of typeDateMaxFilter"
                                                      [value]="item.Name">
                                                      {{ item.Name }}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>
                                      </div>
                                      <div class="col-4 col-md-4">
                                          <!-- Campo de fecha -->
                                          <mat-form-field appearance="outline" class="w-100 mb-2">
                                              <mat-label>{{ "Product.EnterMaxiumDate" | translate
                                                  }}</mat-label>
                                              <input matInput [mode]="modeDatePicker" [dpDayPicker]="config"
                                                  theme="dp-material" formControlName="dMaxDateRequerid"
                                                  [placeholder]="placeholderText">
                                              <mat-icon class="click" *ngIf="!clockTypeIcon" mat-icon-button
                                                  matSuffix>today</mat-icon>
                                              <mat-icon class="click" *ngIf="clockTypeIcon" mat-icon-button
                                                  matSuffix>query_builder</mat-icon>
                                              <mat-error>
                                                  <span
                                                      *ngIf="utilsSvc.isControlHasError(formField, 'dMaxDateRequerid', 'required')">
                                                      {{ 'ThisFieldIsRequired' | translate }}
                                                  </span>
                                              </mat-error>
                                              <mat-error
                                                  *ngIf="formField.get('dMaxDateRequerid')?.hasError('dateRangeInvalid')">
                                                  {{ "Product.MaximumDateWarning" | translate }}
                                              </mat-error>
                                          </mat-form-field>
                                      </div>
                                  </div>
                              </mat-card-content>
                          </mat-card>
                      </ng-container>

                      <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Money"
                          appearance="outline" class="select-look w-100">
                          <mat-label>
                              {{ "Product.SelectCoin" | translate }}
                          </mat-label>
                          <mat-select formControlName="vFormat" [required]="
                        formField.get('fkIIdFieldType')?.value === typeField.Money
                      ">
                              <mat-option *ngFor="let item of typeFormatCoin" [value]="item.format">
                                  {{ item.format }}
                              </mat-option>
                          </mat-select>
                      </mat-form-field>
                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Money"
                          formControlName="bShowCoin" class="my-4">
                          {{ "Product.ShowCoin" | translate }}
                      </mat-checkbox>

                      <!--Visible for person field-->
                      <ng-container *ngIf="formField.get('bIsPerson')?.value">
                          <mat-checkbox formControlName="bIsGrouper" class="my-4">
                              {{ "Product.IsGroupField" | translate }}
                          </mat-checkbox>

                          <mat-form-field *ngIf="!formField.get('bIsGrouper')?.value" appearance="outline"
                              class="select-look w-100">
                              <mat-label>
                                  {{ "Product.GroupField" | translate }}
                              </mat-label>
                              <mat-select formControlName="fkIGrouperField" id="idParent">
                                  <mat-option *ngFor="let item of fieldGroupers" [value]="item.id">
                                      {{ item.name }}
                                  </mat-option>
                              </mat-select>
                          </mat-form-field>

                          <mat-form-field appearance="outline" class="select-look w-100">
                              <mat-label>
                                  {{ "Product.EquivalenceItemTablePerson" | translate }}
                              </mat-label>
                              <mat-select formControlName="vEquivalentField" id="idParent">
                                  <mat-option *ngFor="let item of nameColumnsCustomerTable" [value]="item">
                                      {{ item }}
                                  </mat-option>
                              </mat-select>
                          </mat-form-field>
                      </ng-container>

                      <ng-container *ngIf="
                  formField.get('fkIIdFieldType')?.value ===
                  typeField.DropDownList ">

                          <mat-form-field appearance="outline" class="select-look w-100">
                              <mat-label>
                                  {{ "Product.SelectCatalog" | translate }}
                              </mat-label>
                              <mat-select formControlName="fkIIdCatalog" id="fkIIdCatalog">
                                  <mat-option *ngFor="let item of catalogTable" [value]="item.pkIIdCatalog">
                                      {{ item.vName }}
                                  </mat-option>
                              </mat-select>
                          </mat-form-field>

                          <mat-form-field appearance="outline" class="select-look w-100">
                              <mat-label>
                                  {{ "Product.SelectCatalogField" | translate }}
                              </mat-label>
                              <mat-select formControlName="fkIIdFieldCatalog" id="fkIIdFieldCatalog">
                                  <mat-option *ngFor="let item of catalogFields" [value]="item.pkIIdCatalogField">
                                      {{ item.vName }}
                                  </mat-option>
                              </mat-select>
                          </mat-form-field>
                      </ng-container>
                  </form>
              </div>
          </div>
          <!-- botones -->
          <ng-container customButtonRight>
              <div class="button-container">
                  <a class="w-auto mr-3" mat-button (click)="closeModal()" style="margin: 10px;"><span
                          class="label-button">{{'Cancel'|
                          translate}}</span></a>

                  <button *ngIf="isEditingField && !standarField" class="w-auto mr-3" type="button" mat-raised-button
                      (click)="deleteField()">
                      {{ ["Delete" | translate] }}
                      <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
                  </button>
                  <button class="w-auto" type="button" mat-raised-button color="primary" (click)="saveField()">
                      {{
                      isEditingField
                      ? ["Product.UpdateCharacteristics" | translate]
                      : ["Product.SaveCharacteristics" | translate]
                      }}
                      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
                  </button>
              </div>
          </ng-container>



      </ng-container>
  </app-modal2>
</ng-template>
