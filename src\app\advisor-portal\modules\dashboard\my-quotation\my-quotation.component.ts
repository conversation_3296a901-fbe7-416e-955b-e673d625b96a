import { Component,OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-my-quotation',
  standalone: true,
  imports: [CommonModule,RouterModule,TableComponent,BreadcrumbComponent, TranslateModule],
  template:  `
  <div>
    <h2 class="h3">
      <img src="assets/img/layouts/config_ico.svg" alt="" />
      {{ "MyQuotation.AllQuotation.MyQuotes" | translate }}
    </h2>
  </div>
  <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>
  <router-outlet></router-outlet>
`,
  styleUrls: []
})
export class MyQuotationComponent implements OnInit {
  constructor(
    private _translateService: TranslateService,
  ) {}
  
  inicio: string = this._translateService.instant("Inicio")  
  misCotizaciones: string = this._translateService.instant("Mis Cotizaciones")

  sections: {label: string, link: string}[]=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.misCotizaciones, link: '/dashboard/my-quotation'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Inicio")
      this.misCotizaciones = this._translateService.instant("Mis Cotizaciones")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.misCotizaciones
    });
  }
  
}
