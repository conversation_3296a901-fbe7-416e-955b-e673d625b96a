import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SeeQuotationComponent } from 'src/app/client-portal/modules/dashboard/see-quotation/see-quotation.component';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { ClientManagementComponent } from 'src/app/shared/components/client-management/client-management.component';
import { HeaderQuoteInfoComponent } from 'src/app/shared/components/header-quote-info/header-quote-info.component';
import { QuotationComponent } from 'src/app/shared/components/quotation/quotation.component';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { UserService } from 'src/app/shared/services/user/user.service';

@Component({
  selector: 'app-quotation-management-tabs',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    ClientManagementComponent,
    QuotationComponent,
    SeeQuotationComponent,
    HeaderQuoteInfoComponent,
    MatIconModule,
    TranslateModule,
    BreadcrumbComponent
  ],
  templateUrl: './quotation-management-tabs.component.html',
  styleUrls: ['./quotation-management-tabs.component.scss']
})
export class QuotationManagementTabsComponent implements OnInit {

  idProduct: number = 0;
  isClient: boolean = false;
  idCostumer: string = '';
  hideTabs: boolean = true;

  inicio: string = this._translateService.instant('Inicio');
  cotizar: string = this._translateService.instant('Cotizar');
  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.cotizar, link: '/dashboard/quotation' },
  ];

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _customRouter: CustomRouterService,
    private _usservice: UserService,
    private _translateService: TranslateService,
  ) {}

  async ngOnInit(): Promise<void> {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params['idProduct']) {
        this.idProduct = +params['idProduct'];
      }
      // Si existe el parámetro 'isClient', ajusta la variable
      if (params['isClient']) {
        this.isClient = params['isClient'] === '1'; // Convierte '1' a true y cualquier otro valor a false
      }
    });
    this.idCostumer = (await this._usservice.getUserIdSesion()).toString();
  }

  goBack() {
    this._customRouter.isClientPortal() ?
      this._customRouter.navigate(['/dashboard/my-quotations/']) :
      this._customRouter.navigate(['/dashboard/myQuotation']);
  }

}
