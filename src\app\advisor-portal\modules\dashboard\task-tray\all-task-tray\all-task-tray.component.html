<app-toast 
  *ngIf="toastConfigurationError.showToast" 
  [toastConfiguration]="toastConfigurationError" 
  (closeToastEmiter)="onToastClose()">
</app-toast>
<app-toast 
  *ngIf="toastConfigurationWaring.showToast" 
  [toastConfiguration]="toastConfigurationWaring" 
  (closeToastEmiter)="onToastClose()">
</app-toast>
<form [formGroup]="form">
  <div class="row mt-3">
    <!-- seleccion de proceso -->
    <mat-form-field appearance="outline" class="col-3">
      <mat-label>
        {{ "TaskTraySettings.FormFilter.LabelProcess" | translate }}
      </mat-label>
      <mat-select formControlName="fkIIdProcess">
        <mat-option *ngFor="let item of processList" [value]="item">
          {{ item.vNameProcess }}
        </mat-option>
      </mat-select>
      <mat-error
        *ngIf="utilsService.isControlHasError(form, 'fkIIdProcess', 'required')"
      >
        {{ "ThisFieldIsRequired" | translate }}
      </mat-error>
    </mat-form-field>

    <!-- seleccion de producto -->
    <mat-form-field appearance="outline" class="col-3">
      <mat-label>
        {{ "TaskTraySettings.FormFilter.LabelProduct" | translate }}
      </mat-label>
      <mat-select formControlName="fkIIdProduct">
        <mat-option *ngFor="let item of productsList" [value]="item">
          {{ item.vName }}
        </mat-option>
      </mat-select>
      <mat-error
        *ngIf="utilsService.isControlHasError(form, 'fkIIdProduct', 'required')"
      >
        {{ "ThisFieldIsRequired" | translate }}
      </mat-error>
    </mat-form-field>

    <!-- boton que abre el modal de filtrar -->
    <div class="w-auto">
      <button
        class="w-auto mr-2"
        type="button"
        color="primary"
        (click)="openFilterDialog()"
        mat-raised-button
        [disabled]="disabledFilter"
      >
        {{ "Filter" | translate }}
        <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
      </button>
    </div>
  </div>

  <!-- datatable tareas -->
  <div class="row mt-2" *ngIf="estructTableTaskTray.length > 1">
    <app-table
      [displayedColumns]="estructTableTaskTray"
      [data]="dataTableTaskTray"
      [IsStatic]="false"
      [pageIndex]="pageIndex"
      [pageSize]="pageSize"
      [amountRows]="amountRows"
      (pageChanged)="onPageChange($event)"
      (iconClick)="controller($event)"
    ></app-table>
  </div>
  <!-- boton crear tarea -->
  <div class="row">
    <button
      [disabled]="idProductModuleOut == 0"
      class="w-auto mr-2"
      type="button"
      color="primary"
      mat-raised-button
      (click)="addTaskTary()"
    >
      {{ "TaskTraySettings.CreateTask" | translate }}
      <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
</form>

<!-- modal filtros -->
<ng-template #filtersModal>
  <app-modal2 [titleModal]="'Filter' | translate" [showCancelButtonBelow]="false" (closeModal)="closeModal()">
    <ng-container body>
      <form [formGroup]="formFilter">
        <!-- id de la tarea -->
        <div class="row mt-3">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>
              {{ "TaskTraySettings.TaskId" | translate }}
            </mat-label>
            <input matInput formControlName="PKIIdTask" type="number" />
          </mat-form-field>
        </div>
        <!-- select de estados -->
        <div class="row mt-3">
          <!-- Estado -->
          <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
              {{ "Status" | translate }}
            </mat-label>
            <mat-select formControlName="fkIIdState">
              <mat-option
                *ngFor="let item of stateList"
                [value]="item.pkIIdStageByState"
              >
                {{ item.vState }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <!-- select de Asignados -->
        <div class="row mt-3">
          <!-- Asignados -->
          <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
              {{ "AssignedUser" | translate }}
            </mat-label>
            <mat-select formControlName="fkIIdAssignedUser">
              <mat-option
                *ngFor="let item of assignedUserList"
                [value]="item.pkIIdUser"
              >
                {{ item.vPersonName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <!-- campos dinamicos -->
        <div *ngFor="let item of filterList">
          <div class="row mt-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>
                {{ item.vFieldName }}
              </mat-label>
              <input
                matInput
                formControlName="{{ item.vFieldName }}"
                type="text"
              />
            </mat-form-field>
          </div>
        </div>
        <h5>{{ "TaskTraySettings.DateRange" | translate }}</h5>

        <!-- fecha inicio -->
        <div class="row mt-3">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>
              {{ "MyQuotation.AllQuotation.StartDate" | translate }}
            </mat-label>
            <input
              matInput
              formControlName="dStartDate"
              [matDatepicker]="picker"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="picker"
            ></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>

        <!-- fecha fin -->
        <div class="row mt-3">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>
              {{ "MyQuotation.AllQuotation.FinalDate" | translate }}
            </mat-label>
            <input
              matInput
              formControlName="dEndDate"
              [matDatepicker]="picker1"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="picker1"
            ></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>
        </div>
      </form>
    </ng-container>
    <!-- botones limpiar/aplicar filtros -->
    <ng-container customButtonRight>
      <button  class="mx-5" (click)="cancel()" type="button" mat-raised-button>
        {{ "Cancel" | translate }}
      </button>

      <button
        class="w-auto mr-2"
        type="button"
        mat-raised-button
        (click)="cleanFilterForm()"
      >
        {{ "MyQuotation.AllQuotation.Clean" | translate }}
      </button>

      <button
        class="w-auto"
        type="button"
        mat-raised-button
        color="primary"
        (click)="applyFilters()"
      >
        {{ "Apply" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
<!-- end modal filtros -->
