import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MenuConfigurationModel } from 'src/app/shared/models/menu';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UserService } from 'src/app/shared/services/user/user.service';

@Component({
  selector: 'app-configuration',
  standalone: true,
  imports: [
    CommonModule,
    BreadcrumbComponent,
    MatCardModule,
    RouterModule,
    MatInputModule,
    TranslateModule,
    MatIconModule,
    FormsModule,
    PreventionSqlInjectorDirective,
    MatTooltipModule
  ],
  templateUrl: './configuration.component.html',
  styleUrls: ['./configuration.component.scss'],
})
export class ConfigurationComponent implements OnInit, OnDestroy {
  menu: MenuConfigurationModel[] = [];
  Childrens: MenuConfigurationModel[] = [];

  keyword: string | any = null;
  userIdSession: number = 0;
  index: string = this._translateService.instant('Inicio');
  configuration: string = this._translateService.instant('Configuración');
  sections: { label: string; link: string }[] = [
    { label: this.index, link: '/dashboard' },
    { label: this.configuration, link: '/dashboard/configuration' },
  ];
  idBusinessByCountry: number = 0;

  constructor(
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _userService: UserService,
    private _customRouter: CustomRouterService

  ) {}

  ngOnInit(): void {
    this.getDataSettingInit();
    //traduce en caso de cambiar el idioma ya dentro de la pantalla
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.index = this._translateService.instant('Inicio');
      this.configuration = this._translateService.instant('Configuración');
      this.sections[0].label = this.index;
      this.sections[1].label = this.configuration;

      this.menu.forEach((menu, index) => {
        this.menu[index].Name = this._translateService.instant(menu.Name);
        const ItemMenu = this.getTraslateItem(menu.Name);
        this.menu[index].Name = ItemMenu;
        this.Childrens = menu.Children;
        this.Childrens.forEach((subM, index) => {
          this.Childrens[index].VDescriptionSon =
            this._translateService.instant(subM.VDescriptionSon);
          const ItemSubMenu = this.getTraslateItem(subM.VDescriptionSon);
          this.Childrens[index].VDescriptionSon = ItemSubMenu;
        });
      });
    });
  }

  ngOnDestroy(): void {}

  async getIdUserSession() {
    let userIdSession = await this._settingService.getDataSettingInit();
    if (userIdSession) {
      this.userIdSession = Number(userIdSession.idUser);
    }
    if (this.userIdSession != 0) {
      this.getListMenuByIdUser();
    }
  }

  async getDataSettingInit() {
    let data = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = data.idBusinessByCountry;
    this.getIdUserSession();
  }

  getListMenuByIdUser() {
    this._userService
      .getListMenuByIdUser(
        this.userIdSession,
        this.idBusinessByCountry,
        true,
        this.keyword
      )
      .subscribe({
        next: (response) => {
          this.menu = response.result.Menu;
          this.menu.forEach((menu, index) => {
            this.menu[index].Name = this._translateService.instant(menu.Name);
            const ItemMenu = this.getTraslateItem(menu.Name);
            this.menu[index].Name = ItemMenu;
            this.Childrens = menu.Children;
            this.Childrens.forEach((subM, index) => {
              this.Childrens[index].VDescriptionSon =
                this._translateService.instant(subM.VDescriptionSon);
              const ItemSubMenu = this.getTraslateItem(subM.VDescriptionSon);
              this.Childrens[index].VDescriptionSon = ItemSubMenu;
            });
          });
        },
        error: (error) => {
          console.log(error);
        },
      });
  }

  getTraslateItem(key: string): string {
    const translation = this._translateService.instant(key);
    return translation ? translation : key;
  }

  goToChildVTagSon(url:any){
    console.log("url", url);
    
      this._customRouter.navigate([url]);
  }
}
