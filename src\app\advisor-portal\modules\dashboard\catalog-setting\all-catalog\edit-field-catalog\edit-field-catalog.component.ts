import {
  Compo<PERSON>,
  OnInit,
  ViewChild,
  Template<PERSON>ef,
  On<PERSON><PERSON><PERSON>,
} from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  MatSlideToggleChange,
  MatSlideToggleModule,
} from '@angular/material/slide-toggle';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import {
  TranslateModule,
  TranslateService,
  LangChangeEvent,
} from '@ngx-translate/core';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  FormsModule,
  ReactiveFormsModule,
  Validators,
  FormControl,
} from '@angular/forms';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { Subscription, catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CatalogFieldModel } from 'src/app/shared/models/catalog-setting';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { Router, ActivatedRoute } from '@angular/router';
import { MatTooltipModule } from '@angular/material/tooltip';
import { OptionModel } from 'src/app/shared/models/options/optionmodel';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';

@Component({
  selector: 'app-edit-catalog',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    TranslateModule,
    ReactiveFormsModule,
    TableComponent,
    Modal2Component,
    FormsModule,
    MatTooltipModule,
    ValidationInputFileDirective
  ],
  templateUrl: './edit-field-catalog.component.html',
  styleUrls: ['./edit-field-catalog.component.scss'],
})
export class EditFieldCatalogComponent implements OnInit, OnDestroy {
  private _dataCatalogFieldSubscription?: Subscription;
  @ViewChild('editOptionFieldModal') editOptionFieldModal?: TemplateRef<any>;
  @ViewChild('editLoadOptionModal') editLoadOptionModal?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;
  archivoSeleccionado: File | null = null;
  form: FormGroup = new FormGroup({});
  formJsonOption: FormGroup = new FormGroup({});

  estructTableOption: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'CatalogSetting.TableCatalogFieldOption.Option'
      ),
      columnValue: 'name',
    },
    {
      columnLabel: this._translateService.instant('CatalogSetting.Id'),
      columnValue: 'id',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  optionTable: any[] = [];
  optionLodad: OptionModel[] = [];
  fieldDependent: any[] = [];
  optionCatalogFields: any[] = [];
  selectedOptionField: any[] = [];
  titelModal: string = this._translateService.instant(
    'CatalogSetting.AddOption'
  );
  titleLoadOptionModal: string = this._translateService.instant(
    'CatalogSetting.LoadOptions'
  );
  titleDuplicateOptios: string = this._translateService.instant(
    'CatalogSetting.TitleBadOptions'
  );

  fileName: string = '';
  showBtnDelete: boolean = false;
  btnRequeridDependent: boolean = false;
  btnRequeridOption: boolean = false;
  isOptionDependent: boolean = false;
  originalArray: any[] = [];
  isEditing: boolean = false;
  idCatalogField: number = 0;

  dataFieldcatalog: CatalogFieldModel = {
    pkIIdCatalogField: 0,
    vCatalogCode: '',
    vName: '',
    vJson: [],
    bActive: false,
    bIsDependent: false,
    fkIIdCatalogField: 0,
    fkIIdCatalog: 0,
    vIdJson: 0,
  };
  allowedExtensions: string[] = ['xlsx'];

  constructor(
    private _translateService: TranslateService,
    public _utilsSvc: UtilsService,
    private _fb: FormBuilder,
    public modalDialog: MatDialog,
    public _location: Location,
    private _catalogService: CatalogSettingService,
    private _parameterService: ParametersService,
    private _fileService: FileService,
    private _msgSvc: MessageService,
    private _activatedRoute: ActivatedRoute
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.validateAction();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      /// traduction titles modals
      this.titelModal = this._translateService.instant(
        'CatalogSetting.AddOption'
      );
      this.titleLoadOptionModal = this._translateService.instant(
        'CatalogSetting.LoadOptions'
      );

      //traduccion data table catalog
      this.estructTableOption[0].columnLabel = this._translateService.instant(
        'CatalogSetting.TableCatalogFieldOption.Option'
      );
      (this.estructTableOption[1].columnLabel =
        this._translateService.instant('CatalogSetting.Id')),
        (this.estructTableOption[2].columnLabel =
          this._translateService.instant('Action'));
    });
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdCatalogField: [0],
      vCatalogCode: [null],
      vName: ['', [Validators.required]],
      VJson: [[]],
      bActive: [true],
      bIsDependent: [false],
      fkIIdCatalogField: [0],
      fkIIdCatalog: [0, [Validators.required]],
      vIdJson: [],
    });

    this.formJsonOption = this._fb.group({
      id: [0],
      name: ['', [Validators.required]],
    });

    this.form.get('fkIIdCatalogField')?.valueChanges.subscribe((data) => {
      this.getListCatalogGlobalByIdCatalogField(data);
      this.optionTable = [];
      this.originalArray = [];
      this.form.get('VJson')?.setValue([]);
    });

    this.form.get('vIdJson')?.valueChanges.subscribe((data) => {
      if (data && data > 0) {
        if (this.isEditing) {
          this.optionTable = this.originalArray;
        }
        this.optionTable = this.filterByDependentFieldId(data);
        this.assignIsDependant(data);
      }
    });
  }

  get optionToWhichItBelongs() {
    if (this.btnRequeridOption) return this.form.get('vIdJson')?.value;
  }

  validateAction() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idCatalog) {
        this.idCatalogField = params.idCatalogField;
        this.getCatalogById(params.idCatalog);
        this.getCatalogFieldById(params.idCatalogField);
        this.form.get('fkIIdCatalog')?.setValue(params.idCatalog);
      } else {
        this.form.get('fkIIdCatalog')?.setValue(params.idCatalog);
      }
      if (params.idCatalogField) {
        this.isEditing = true;
      }
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  get validOptionField(): boolean {
    return this.formJsonOption.valid;
  }

  changeIsDependent(event: MatSlideToggleChange) {
    if (this.isOptionDependent)
      if (!event.checked) {
        if (this.form.get('fkIIdCatalogField')?.value > 0) {
          this._msgSvc.messageInfo(
            this._translateService.instant(
              'CatalogSetting.MessagesCatalogo.Warning'
            ),
            this._translateService.instant(
              'CatalogSetting.MessagesCatalogo.ChangeDependentSubTitle'
            )
          );
          this.form.get('bIsDependent')?.setValue(1);
        }
      }
  }

  getCatalogFieldById(idCatalogField: number) {
    this._catalogService
      .getCatalogFieldById(idCatalogField)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.form.patchValue(response.result);
            if (response.result != undefined) {
              if (response.result.vJson != undefined) {
                this.optionTable = response.result.vJson;
                this.originalArray = response.result.vJson;
              }
              if (response.result.vIdJson != undefined) {
                this.deleteQuotationMarksArrayIdJson([response.result.vIdJson]);
              }

              if (response.result.fkIIdCatalogField > 0) {
                this.isOptionDependent = true;
              } else this.isOptionDependent = false;
            }
          }
        }
      });
  }

  getCatalogById(idCatalog: number) {
    this._catalogService
      .getCatalogFieldByCatalogId(idCatalog)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.fieldDependent = response.result;

            if (response.result != undefined) {
              let filter = this.fieldDependent.filter(
                (x) =>
                  x.fkIIdCatalogField == 0 &&
                  x.pkIIdCatalogField == this.idCatalogField
              );

              if (filter.length > 0) {
                this.btnRequeridDependent == false;
                this.btnRequeridOption = false;
              } else if (
                response.result.length > 0 &&
                (this.idCatalogField == 0 || this.idCatalogField == undefined)
              ) {
                this.btnRequeridOption = true;
                this.btnRequeridDependent = true;
              } else if (
                response.result.length > 1 &&
                this.idCatalogField > 0
              ) {
                this.btnRequeridOption = true;
                this.btnRequeridDependent = true;
              } else this.btnRequeridDependent = false;
            } else this.btnRequeridDependent = false;
          }
        }
      });
  }

  getListCatalogGlobalByIdCatalogField(idCatalogField: number) {
    this._parameterService
      .getListCatalogGlobalByIdCatalogField(idCatalogField)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.optionCatalogFields = response.result;
            // this.form.get('vIdJson')?.setValue(this.selectedOptionField);
          }
        }
      });
  }

  controller(event: IconEventClickModel) {
    this._msgSvc
      .messageConfirmationAndNegationReverseButton(
        this._translateService.instant(
          'FormConfiguration.ProgressBar.DeleteMessageConfirm'
        ),
        '',
        'warning',
        this._translateService.instant('Cancel'),
        this._translateService.instant('Confirm')
      )
      .then((result) => {
        if (result) {
          this.delteOptionArrayoriginal(event.value.id);
          const index = this.optionTable.findIndex(
            (user) => user.id === event.value.id
          );
          this.optionTable.splice(index, 1);
          this.optionTable = this.optionTable.slice();
        }
      });
  }

  /// elimina las comillas iniciales de la data devuelta en viJson
  deleteQuotationMarksArrayIdJson(arrayIdJson: any[]) {
    let arrayWithoutCommas = arrayIdJson
      .map((_element) => _element.split(','))
      .flat();
    let arrayWithoutQuotationMarks = arrayWithoutCommas.map((elemento) =>
      parseFloat(elemento)
    );
    this.selectedOptionField = arrayWithoutQuotationMarks;
  }

  /// Open the modal
  open(component: string) {
    var sizeConfiguration = {
      disableClose: false,
      width: '60vw',
      height: 'auto',
    };
    var modal: TemplateRef<any>;

    switch (component) {
      case 'modalOptionField':
        this.formJsonOption.reset();
        modal = this.editOptionFieldModal!;
        this.assignId();
        break;
      case 'modalLoadOption':
        modal = this.editLoadOptionModal!;
        sizeConfiguration.width = '30vw';
        break;
      default:
        return;
    }

    // Open modal and save reference in the property currentModal
    this.currentModal = this.modalDialog.open(modal, sizeConfiguration);
  }


  documentUploadIdError() {
    this._msgSvc
      .messageConfirmationAndNegation(
        this._translateService.instant(
          'CatalogSetting.TitleBadOptions'
        ),
        this._translateService.instant(
          'CatalogSetting.BadIdDuplicateOptions'
        ),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this.open("modalLoadOption");
        } else{
          this.modalDialog.closeAll();
        }
      });
  }


  selectFile(event: any) {
    let extension: string = '';
    extension = this.fileName = event.target.files[0].name.split('.')[1];
    if (extension === 'xlsx') {
      this.fileName = event.target.files[0].name;
      this.archivoSeleccionado = event.target.files[0];
      this.showBtnDelete = true;
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant(
          'CatalogSetting.MessagesCatalogo.Warning'
        ),
        this._translateService.instant(
          'CatalogSetting.MessagesCatalogo.SelectFileSubTitle'
        )
      );
      this.showBtnDelete = false;
      this.fileName = '';
    }
  }

  /// Converts the excel into json and adds it to the field option data
  massiveCatalog() {

    this.optionLodad = this.optionTable.map(option => new OptionModel(option.id, option.name));

    if (this.archivoSeleccionado) {
      this._fileService.massiveCatalog(this.archivoSeleccionado,this.optionLodad).subscribe({
        next: (response) => {
          if (!response.error) {
            if (response.result) {
              if (
                this.optionTable.length > 0 ||
                this.originalArray.length > 0
              ) {
                let arrayAux: any[] = [];
                arrayAux = response.result;
                arrayAux.forEach((element: any) => {
                  element.id = +element.id;
                });
                this.optionTable = this.optionTable.concat(response.result);
                arrayAux.forEach((element) => {
                  element.isDependant = this.form.get('vIdJson')?.value;
                  element.id = +element.id;
                });
                this.originalArray = this.originalArray.concat(arrayAux);
              } else {
                this.optionTable = response.result;
                this.originalArray = this.optionTable;
                this.assignIsDependant(this.form.get('vIdJson')?.value);
              }
              this.modalDialog.closeAll();
              this.showBtnDelete = false;
              this.fileName = '';
            } else {
            }
          } else {
            this._msgSvc.messageInfo(
              this._translateService.instant('ThereWasAError'),
              response.message
            );
          }
        },
        error: (err) => {

          if (err.error.message === "ID repetidos") {
            this.documentUploadIdError();
          }else{
            this._msgSvc.messageError(err.error.message);
          }
          
        },
      });
    }
  }

  /// Delete File xlsx
  deleteFile() {
    this.showBtnDelete = false;
    this.archivoSeleccionado = null;
    this.fileName = '';
  }

  /// New option is added to the array of the field options table
  addOption() {
    let newOptionField = this.formJsonOption.getRawValue();

    ///// Validated if the item already exists, if it exists it returns
    ////  indicating that the item already exists
    let result = this.originalArray.filter(
      (value) => value.name == newOptionField.name
    );

    if (result != undefined)
      if (result.length > 0) {
        this._msgSvc.messageInfo(
          this._translateService.instant('ThereWasAError'),
          this._translateService.instant(
            'CatalogSetting.MessagesCatalogo.SubTitleAddOption'
          )
        );
        return;
      }
    newOptionField.isDependant = this.form.get('vIdJson')?.value;
    if (this.isEditing) {
      /// Add new value the array optionTable
      this.optionTable.push(newOptionField);
      this.optionTable = this.optionTable.slice();

      this.originalArray.push(newOptionField);
      this.originalArray = this.originalArray.slice();
    } else {
      this.optionTable.push(newOptionField);
      this.optionTable = this.optionTable.slice();
      this.originalArray.push(newOptionField);
    }
    /// Close modal after of add values to the array
    this.modalDialog.closeAll();
  }

  validationDependent() {
    if (this.form.get('bIsDependent')?.value) {
      if (this.form.get('fkIIdCatalogField')?.value === 0) {
        this._msgSvc.messageInfo(
          this._translateService.instant('ThereWasAError'),
          this._translateService.instant(
            'CatalogSetting.MessagesCatalogo.SubTitleValidationDependentRequired'
          )
        );
        return false;
      }
    } else {
      if (this.btnRequeridDependent) {
        this._msgSvc.messageInfo(
          this._translateService.instant('ThereWasAError'),
          this._translateService.instant(
            'CatalogSetting.MessagesCatalogo.SubTitleValidationDependentRequired2'
          )
        );
        return false;
      }
    }
    return true;
  }

  dataAssignmentFieldJson(action: string) {
    if (this.selectedOptionField != undefined) {
      let validatedSelectIdJson =
        this.selectedOptionField.length == 0 ? null : this.selectedOptionField;
      this.form.get('vIdJson')?.setValue(validatedSelectIdJson);
    } else {
      // this.form.get('vIdJson')?.setValue(null);
    }
    this.form.get('VJson')?.setValue(this.optionTable);

    if (action == 'edit') this.updateCatalogField();
    else this.saveCatalogField();
  }

  saveCatalogField() {
    if (this.valid && this.validationDependent()) {
      this.dataFieldcatalog = {
        pkIIdCatalogField: 0,
        vCatalogCode: this.form.get('vCatalogCode')?.value,
        vName: this.form.get('vName')?.value,
        vJson: this.originalArray,
        bActive: this.form.get('bActive')?.value,
        bIsDependent: this.form.get('bIsDependent')?.value,
        fkIIdCatalogField: this.form.get('fkIIdCatalogField')?.value,
        vIdJson: this.form.get('vIdJson')?.value,
        fkIIdCatalog: this.form.get('fkIIdCatalog')?.value,
      };
      this._catalogService
        .registerCatalogField(this.dataFieldcatalog)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              if (resp.message === 'hasAssociatedItems') {
                this._msgSvc.messageWaring(
                  this._translateService.instant(
                    'CatalogSetting.MessagesCatalogo.hasAssociatedItemsTitle'
                  ),
                  this._translateService.instant(
                    'CatalogSetting.MessagesCatalogo.hasAssociatedItemsDescription'
                  )
                );
              }
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'CatalogSetting.MessagesCatalogo.SuccessfulMessageCreated'
                )
              );
              this.goBack();
            }
          }
        });
    }
  }

  updateCatalogField() {
    if (this.valid && this.validationDependent()) {
      this.dataFieldcatalog = {
        pkIIdCatalogField: this.form.get('pkIIdCatalogField')?.value,
        vCatalogCode: this.form.get('vCatalogCode')?.value,
        vName: this.form.get('vName')?.value,
        vJson: this.removeDuplicatesById(this.originalArray),
        bActive: this.form.get('bActive')?.value,
        bIsDependent: this.form.get('bIsDependent')?.value,
        fkIIdCatalogField: this.form.get('fkIIdCatalogField')?.value,
        vIdJson: this.form.get('vIdJson')?.value,
        fkIIdCatalog: this.form.get('fkIIdCatalog')?.value,
      };

      this._catalogService
        .updateCatalogField(this.dataFieldcatalog)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              if (resp.message === 'hasAssociatedItems') {
                this._msgSvc.messageWaring(
                  this._translateService.instant(
                    'CatalogSetting.MessagesCatalogo.hasAssociatedItemsTitle'
                  ),
                  this._translateService.instant(
                    'CatalogSetting.MessagesCatalogo.hasAssociatedItemsDescription'
                  )
                );
              }
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'CatalogSetting.MessagesCatalogo.SuccessfulMessageCreated'
                )
              );
              this.goBack();
            }
          }
        });
    }
  }

  //Función que verifica que no existan opciones duplicadas.
  removeDuplicatesById = (array: any[]): any[] => {
    const result: any[] = [];
    const seen: { [key: string]: boolean } = {};

    array.forEach((obj) => {
      const key: string = `${obj.id}-${obj.isDependant !== undefined ? obj.isDependant : 'undefined'}`;
      if (!seen[key]) {
        seen[key] = true;
        result.push(obj);
      }
    });

    return result;
  };

  /// Delete catalog field by id
  deleteCatalogField() {
    this._msgSvc
    .messageConfirmationAndNegationReverseButton(
      this._translateService.instant(
        'FormConfiguration.ProgressBar.DeleteMessageConfirm'
      ),
      '',
      'warning',
      this._translateService.instant('Cancel'),
      this._translateService.instant('Confirm')
    )
    .then((result) => {
      if (result) {
        let idCatalogF = 0;
        idCatalogF = this.form.get('pkIIdCatalogField')?.value;
        if (idCatalogF > 0) {
          this._catalogService
            .deleteCatalogField(idCatalogF)
            .pipe(
              catchError((error) => {
                if (
                  error.error.error &&
                  error.error.message === 'hasAssociatedItems'
                ) {
                  this._msgSvc.messageInfo(
                    this._translateService.instant(
                      'CatalogSetting.MessagesCatalogo.deletedMessageTitle'
                    ),
                    this._translateService.instant(
                      'CatalogSetting.MessagesCatalogo.deletedMessageSubTitle'
                    )
                  );
                } else {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    error.error.message
                  );
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
              } else {
                if (resp.error) {
                  this._msgSvc.messageInfo(
                    this._translateService.instant('ThereWasAError'),
                    resp.message
                  );
                } else {
                  this._msgSvc.messageSuccess(
                    '',
                    this._translateService.instant('DeleteMessage')
                  );
                  this.goBack();
                }
              }
            });
        }
      }
    });
    
  }

  closeModal(event: boolean) {
    if (this.currentModal) {
      this.currentModal.close();
      this.currentModal = null; // Limpia la referencia después de cerrar el modal
    }
  }

  goBack() {
    this._location.back();
    this.form.reset();
    this.formJsonOption.reset();
  }

  //Evento que se dispara cada vez que el usuario depliegue la lista del select (Campo del que es dependiente).
  //Se ejecuta solo cuándo se esté editando.
  onSelectOpened() {
    if (this.form.get('pkIIdCatalogField')?.value > 0) {
      this._msgSvc
        .messageConfirmationAndNegation(
          '¿Desea cambiar la dependencia?',
          'Al cambiar la dependencia, se eliminan los elementos configurados previamente.',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
          }
        });
    }
  }

  //Asigna el idIsDependant del campo al que perteneceran las opciones, se llama siempre que cambie el valor en el select de:
  //(Opción a la que pertenece).
  assignIsDependant(idIsDependant: number) {
    this.optionTable.forEach((element) => {
      element.isDependant = idIsDependant;
      element.id = Number(element.id);
    });
  }

  // Filtra el array para obtener un nuevo array sin el objeto con el id proporcionado.
  // Esta función solo elimina el originalArray con la finalidad de mantenerlo actualizado con los valores que se van agg o borrando.
  delteOptionArrayoriginal(id: number) {
    this.originalArray = this.originalArray.filter(
      (objeto) => objeto.id !== id
    );
  }

  //Filtra el array temporal que suministra la data de la tabla, dependiendo el valor seleccionado en el select (Opción a la que pertenece).
  filterByDependentFieldId(idToFilter: number) {
    const newArray = this.originalArray.filter(
      (item) => item.isDependant === idToFilter
    );
    return newArray;
  }

  //Función para descargar la plantilla de excel para la carga masiva de opciones.
  getCatalogTemplate() {
    this._fileService.getCatalogTemplate().subscribe((resp) => {
      const blob = new Blob([resp], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;',
      });
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = 'plantilla-masiva-catalogos.xlsx';
      link.click();
      window.URL.revokeObjectURL(downloadUrl);
    });
  }

  assignId() {
    /// The last array is obtained and with the id an increment is made for the next array
    var idIncrement: any = {};
    if (this.originalArray.length === 0) idIncrement = 1;
    else {
      idIncrement = this.originalArray[this.originalArray.length - 1];
      idIncrement = idIncrement.id + 1;
    }
    /// The value is assigned to the id
    this.formJsonOption.get('id')?.setValue(idIncrement);
  }

  ngOnDestroy(): void {
    this._dataCatalogFieldSubscription?.unsubscribe();
  }
}
