import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { SelectComponent } from 'src/app/shared/components/select/select.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { LevelModel, UserGroupLevelModel } from 'src/app/shared/models/groups';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { UserModel } from 'src/app/shared/models/user';
import { GroupsService } from 'src/app/shared/services/groups/groups.service';
import { LevelsService } from 'src/app/shared/services/levels/levels.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [
    CommonModule,
    MatSelectModule,
    SelectComponent,
    TableComponent,
    Modal2Component,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    PreventionSqlInjectorDirective,
    MatTooltipModule
  ],
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss'],
})
export class UsersComponent implements OnInit, OnDestroy {
  addUsersList: UserModel[] = [];

  userTable: UserModel[] = [];
  estructUsersTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Group.Email'),
      columnValue: 'vEmailUser',
    },
    {
      columnLabel: this._translateService.instant('Group.Name'),
      columnValue: 'vPersonName',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'vActive',
    },
    {
      columnLabel: this._translateService.instant('Group.Role'),
      columnValue: 'vRoleName',
    },
    {
      columnLabel: this._translateService.instant('Group.PermissionLevel'),
      columnValue: 'vLevelName',
    },
    {
      columnLabel: this._translateService.instant('Group.Company'),
      columnValue: 'vBusinessName',
    },
    {
      columnLabel: this._translateService.instant('Group.Country'),
      columnValue: 'vCountryName',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  allUserTable: UserModel[] = [];
  estructAllUsersTable: BodyTableModel[] = [
    {
      columnLabel: '',
      columnValue: '1',
      check: true,
    },
    {
      columnLabel: this._translateService.instant('Group.Email'),
      columnValue: 'vEmailUser',
    },
    {
      columnLabel: this._translateService.instant('Group.Name'),
      columnValue: 'vPersonName',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'vActive',
    },
    {
      columnLabel: this._translateService.instant('Group.Role'),
      columnValue: 'vRoleName',
    },
    {
      columnLabel: this._translateService.instant('Group.Company'),
      columnValue: 'vBusinessName',
    },
    {
      columnLabel: this._translateService.instant('Group.Country'),
      columnValue: 'vCountryName',
    },
  ];

  @ViewChild('masiveAsignationModal') masiveAsignationModal?: TemplateRef<any>;
  dataSelectUser: UserModel[] = [];
  @Output() listUserToAdd: EventEmitter<UserGroupLevelModel> =
    new EventEmitter();
  @Output() idUserToDelete: EventEmitter<number> = new EventEmitter();
  @Input() action: string = '';
  @Input() idGroup: number = 0;
  previousSelectData: UserModel[] = [];
  levels: LevelModel[] = [];
  form: FormGroup = new FormGroup({});
  private _settingCountryAndCompanySubscription?: Subscription;
  private _groupDataSubscription?: Subscription;
  private _idBisnessByCountry: number = 0;
  selectedUsersCount: number = 0;
  userSelect!: UserModel;

  constructor(
    private _userService: UserService,
    private _levelsService: LevelsService,
    private _groupsService: GroupsService,
    private _settingService: SettingService,
    private _fb: FormBuilder,
    private _messageService: MessageService,
    public _masiveAsignationDialog: MatDialog,
    private _router: Router,
    public _translateService: TranslateService,
    private _utilsService: UtilsService
  ) {}
  ngOnInit(): void {
    this.initForm();
    this.getIdBisnessByCountry();
    this.getListLevels();
    this.getListUserSelect();
    this.getListUsers();
    this.getListLevels();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table business
      this.estructUsersTable[0].columnLabel =
        this._translateService.instant('Group.Email');
      this.estructUsersTable[1].columnLabel =
        this._translateService.instant('Group.Name');
      this.estructUsersTable[2].columnLabel =
        this._translateService.instant('Status');
      this.estructUsersTable[3].columnLabel =
        this._translateService.instant('Group.Role');
      this.estructUsersTable[4].columnLabel = this._translateService.instant(
        'Group.PermissionLevel'
      );
      this.estructUsersTable[5].columnLabel =
        this._translateService.instant('Group.Company');
      this.estructUsersTable[6].columnLabel =
        this._translateService.instant('Group.Country');
      this.estructUsersTable[7].columnLabel =
        this._translateService.instant('Delete');
    });
  }

  initForm() {
    this.form = this._fb.group({
      levelForm: ['', [Validators.required]],
      levelModalForm: [''],
    });
  }

  getListUsers() {
    this._userService.getUserGroupList(this.idGroup).subscribe({
      next: (response) => {
        this.userTable = response.result;
      },
    });
  }

  getListUserSelect() {
    this._userService.getUserListSearch(this._idBisnessByCountry).subscribe({
      next: (response) => {
        this.dataSelectUser = response.result;
        this.previousSelectData = response.result;
        this.allUserTable = response.result;
      },
    });
  }

  getIdBisnessByCountry() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this._idBisnessByCountry =
              response.enterprise.pkIIdBusinessByCountry;
          }
        }
      );
  }

  getListLevels() {
    this.levels = [];
    this._levelsService.getListLvelsByIdGroup(Number(this.idGroup)).subscribe({
      next: (response) => {
        if (!response.error) {
          this.levels = response.result;
        }
      },
    });
  }

  search(event: string) {
    if (event === '') {
      this.dataSelectUser = this.previousSelectData;
    } else if(!this._utilsService.sqlInjectionValidation(event)) {
      this._userService
        .getUserListSearch(this._idBisnessByCountry, event)
        .subscribe({
          next: (response) => {
            if (response.result.length > 0) {
              this.dataSelectUser = response.result;
            } else {
              this.dataSelectUser = this.previousSelectData;
            }
          },
        });
    }
  }

  listSelect(event: UserModel) {
    this.userSelect = event;
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'delete':
        let existUser: boolean = false;
        if (event.value.vGroupName === '') {
          existUser = true;
        } else {
          existUser = false;
        }
        this.deleteUser(event.value, existUser);
        break;
      default:
        break;
    }
  }

  addUserController(event: IconEventClickModel) {
    if(event.value == 'ALL')
    {
      this.addUsersList = this.allUserTable;
    }
    else if(event.value == 'NONE')
    {
      this.addUsersList = [];
    }
    else
    {
      if ((<HTMLInputElement>event.event.target).checked) {
        this.addUsersList.push(event.value);
      } else {
        this.addUsersList = this.addUsersList.filter(
          (user) => user.pkIIdUser != event.value.pkIIdUser
        );
      }
    }    
    this.selectedUsersCount = this.addUsersList.length;
  }

  addUsers() {
    let newUserTable: UserModel[] = [];
    this.userTable.forEach((element) => {
      newUserTable.push(element);
    });
    if (this.form.valid && this.userSelect) {
      let payload: UserGroupLevelModel = {
        pkIIdUserLevel: 0,
        bActive: true,
        fkIIdLevel: this.form.value.levelForm.pkIIdLevel,
        fkIIdUser: this.userSelect.pkIIdUser,
      };
      let userToAdd: UserModel = this.userSelect;
      userToAdd.vLevelName = this.form.value.levelForm.vLevelName;
      userToAdd.vGroupName = '';
      if (this.validateUser(newUserTable, userToAdd)) {
        this._messageService.messageInfo(
          this._translateService.instant('ThereWasAError'),
          this._translateService.instant('Group.UserAlreadyAdded')
        );
      } else {
        this.userTable.push(userToAdd);
        newUserTable.push(userToAdd);
        this.userTable = newUserTable;
        this.form.get('levelForm')?.setValidators(Validators.required);
        this.form.get('levelForm')?.updateValueAndValidity();
        this.form.get('levelModalForm')?.clearValidators();
        this.form.get('levelModalForm')?.updateValueAndValidity();
        this.listUserToAdd.emit(payload);
      }
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('ThereWasAError'),
        this._translateService.instant(
          'PleaseCompleteAllTheInformationOnTheForm'
        )
      );
    }
  }

  massiveUserAsignation() {
    if (this.form.value.levelModalForm && this.addUsersList.length > 0) {
      for (let item of this.addUsersList) {
        this.userSelect = item;
        this.form.patchValue({ levelForm: this.form.value.levelModalForm });
        this.addUsers();
      }

      this.form.patchValue({ levelForm: null });
      this.form.patchValue({ levelModalForm: null });
      this._messageService.messageSuccess(
        this._translateService.instant('Group.Added'),
        this._translateService.instant('Group.Added')
      );
      this._masiveAsignationDialog.closeAll();
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('ThereWasAError'),
        this._translateService.instant(
          'PleaseCompleteAllTheInformationOnTheForm'
        )
      );
    }
  }

  openMasiveUsersAsignationDialog() {
    this.selectedUsersCount = 0;
    this.addUsersList = [];
    this.getListUserSelect();
    this.form.get('levelModalForm')?.setValidators(Validators.required);
    this.form.get('levelModalForm')?.updateValueAndValidity();
    this.form.get('levelForm')?.clearValidators();
    this.form.get('levelForm')?.updateValueAndValidity();
    const dialogRef = this._masiveAsignationDialog.open(
      this.masiveAsignationModal!
    );
  }

  validateUser(users: UserModel[], user: UserModel): boolean {
    for (let i = 0; i < users.length; i++) {
      if (users[i].pkIIdUser === user.pkIIdUser) {
        return true;
      }
    }
    return false;
  }

  deleteUser(userToDelete: UserModel, existUser: boolean) {
    this.idUserToDelete.emit(userToDelete.pkIIdUser);
    let payload: UserGroupLevelModel = {
      bActive: false,
      fkIIdLevel: userToDelete.pkIIdLevel,
      fkIIdUser: userToDelete.pkIIdUser,
      pkIIdUserLevel: 0,
    };

    if (existUser) {
      let newUserTable = this.userTable.filter(
        (user: UserModel) => user.pkIIdUser !== userToDelete.pkIIdUser
      );
      this.userTable = newUserTable;
    } else {
      this._groupsService.deleteUserGroup(payload).subscribe({
        next: (response) => {
          if (!response.error) {
            let newUserTable = this.userTable.filter(
              (user: UserModel) => user.pkIIdUser !== userToDelete.pkIIdUser
            );
            this.userTable = newUserTable;
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('Group.UserSuccessfullyDeleted')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        },
      });
    }
  }

  closedModal(event: any) {
    this.form.get('levelModalForm')?.clearValidators();
    this.form.get('levelModalForm')?.updateValueAndValidity();
    this.form.get('levelForm')?.setValidators(Validators.required);
    this.form.get('levelForm')?.updateValueAndValidity();
  }

  ngOnDestroy(): void {
    this._groupDataSubscription?.unsubscribe();
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
