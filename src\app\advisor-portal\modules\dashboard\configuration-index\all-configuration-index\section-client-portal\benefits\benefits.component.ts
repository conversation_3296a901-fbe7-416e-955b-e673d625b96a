import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { BenefitService } from 'src/app/shared/services/benefits/benefit.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-benefits',
  standalone: true,
  imports: [
    MatInputModule,
    Modal2Component,
    PreventionSqlInjectorDirective,
    TranslateModule,
    ReactiveFormsModule,
    CommonModule,
    TableComponent,
    MatButtonModule
  ],
  templateUrl: './benefits.component.html',
  styleUrls: ['./benefits.component.scss']
})
export class BenefitsComponent implements OnInit{

  @ViewChild('createEditBenefit') createEditBenefit?: TemplateRef<any>;
  _currentModal: MatDialogRef<any> | null = null;
  @Input() idBusinessByCountry: number = 0;

  formSubs?: Subscription;

  dataTableBenefits: any[] = [];
  maxItems: number = 6;
  pkBenefitToModify: number = 0;


  estructTableBenefits: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('SectionClientPortal.Benefit'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyBenefit',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteBenefit',
      columnIcon: 'delete',
    },
  ];

  formBenefit: FormGroup = this._fb.group({
    vName: [null, Validators.required],
  })

  constructor(
    public utilsSvc: UtilsService,
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    private _modalDialog: MatDialog,
    private _cdr: ChangeDetectorRef,
    private _benefitService: BenefitService,
    private _messageService: MessageService,
  ){
  }
  ngOnInit(): void {
    this._loadBenefits()
  }

  private _loadBenefits(){
    this._benefitService.getBenefits(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error)
          {
            this.dataTableBenefits = [...resp.result];
            this.pkBenefitToModify = 0;
            this._cdr.detectChanges()
          }
      })
    })
  }

  openModal(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this._currentModal = this._modalDialog.open(this.createEditBenefit!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formBenefit.reset();
      })
    })
  }

  saveBenefit(){
    if (this.formBenefit.invalid)
      return this.formBenefit.markAllAsTouched()

    if (this.pkBenefitToModify === 0)
      this._benefitService.createBenefit({
        ...this.formBenefit.value,
        FkIIdBusinessByCountry: this.idBusinessByCountry
      }).subscribe({
          next: (response) => {
            if (!response.error) {
              this._loadBenefits()
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.BenefitCreatedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          }
      })
    else
      this._benefitService.modifyBenefit(
        this.pkBenefitToModify, 
        {...this.formBenefit.value}).subscribe({
          next: (response) => {
            if (!response.error) {
              this._loadBenefits()
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.BenefitModifiedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          }
        })
    this.closeModal()
  }

  closeModal() {
    if (this._currentModal) {
      this._currentModal.close();
      this._currentModal = null;
      this.formSubs?.unsubscribe();
    }
  }


  controller(evt: IconEventClickModel) {
    if (evt.column === 'deleteBenefit')
        {
          this._benefitService.deleteBenefit(evt.value.pkIIdBenefit).subscribe({
            next: (response) => {
              if (!response.error) {
                this._loadBenefits()
                this._messageService.messageSuccess(
                  this._translateService.instant('DataSavedSuccessfully'),
                  this._translateService.instant('SectionClientPortal.BenefitDeletedSucessfully')
                );
              }
            },
            error: (error) => {
              this._messageService.messageInfo(
                this._translateService.instant('ThereWasAError'),
                error
              );
            }
          })
        }
    if (evt.column === 'modifyBenefit')
      {
        this.pkBenefitToModify = evt.value.pkIIdBenefit
        this.formBenefit.patchValue({
          vName: evt.value.vName
        })
        this.openModal()
      }
    
  }
}
