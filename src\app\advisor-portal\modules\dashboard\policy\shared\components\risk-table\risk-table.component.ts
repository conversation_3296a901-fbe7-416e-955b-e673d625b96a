import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { PageEvent } from '@angular/material/paginator';
import { SelectModel } from 'src/app/shared/models/select';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { MatDialog } from '@angular/material/dialog';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { RequestGetRiskModel, RiskTableDataModel } from '../../models';
import { FileService } from 'src/app/shared/services/file/file.service';
import { PolicyTableService } from '../../service/policy-table.service';

@Component({
  selector: 'app-risk-table',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    Modal2Component,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
  ],
  templateUrl: './risk-table.component.html',
  styleUrls: ['./risk-table.component.scss'],
})
export class RiskTableComponent implements OnInit {
  //Variables para compartir información entre componentes.
  @Input() estructTable: BodyTableModel[] = [];
  @Input() idtypePolicy: number = 0;
  @Input() idPolicy: number = 0;
  @Input() idHistoryPolicy: number = 0;
  @Output() actionTable = new EventEmitter<any>();
  //Variables relacionadas con los modales.
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;
  policyStatusValue: number = 0;

  //Variable empresa país.
  idBusinessByCountry: number = 0;

  //Variables relacioandas con la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;
  currentPosition: number = 0;
  dataTableRisk: RiskTableDataModel[] = [];

  //Variables para filtrar la data de la tabla.
  keyword: string = '';
  policyStatus: SelectModel[] = [];
  requestFilterRiskTable: RequestGetRiskModel = {
    keyword: '',
    statePolicyId: 0,
    order: 'desc',
    page: 0,
    pageSize: 5,
    idPolicy: 0,
    idHistoryPolicy: 0,
  };

  pdfSrc: Uint8Array = new Uint8Array();
  keyField: string = '';

  constructor(
    private _messageService: MessageService,
    private _translateService: TranslateService,
    public matDialog: MatDialog,
    private _transactionService: TransactionService,
    private _customeRouter: CustomRouterService,
    private _fileService: FileService,
    private _policyTableService: PolicyTableService,
  ) {}

  ngOnInit() {
    this.requestFilterRiskTable.idPolicy = this.idPolicy;
    this.requestFilterRiskTable.idHistoryPolicy = this.idHistoryPolicy;
    this.getRisk(this.requestFilterRiskTable);
  }

  //Obtiene todos los riesgos de una póliza.
  getRisk(request: RequestGetRiskModel) {
    this._transactionService
      .getRisks(request)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTableRisk = [];
        } else {
          this.dataTableRisk = resp.result.item1;
          this.amountRows = resp.result.item2;
          this.keyField = this.dataTableRisk[0].keyField;
          this.chageStructureTable();
          // this.matDialog.closeAll();
        }
      });
  }

  //Obtiene todas los estados de pólizas por idBusinessCountry.
  getStatePolicies() {
    this._transactionService
      .getStatePolicies()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          const stateIdValid = [1, 2, 5];
          this.policyStatus = resp.result.filter((x: SelectModel) =>
            stateIdValid.includes(x.id)
          );
        }
      });
  }

  //Descarga una reporte del riesgo de una póliza.
  generateRiskReport(idPolicy: number, idHistoryPolicy?: number) {
    this._transactionService
      .generateRiskReport(idPolicy, idHistoryPolicy)
      .pipe(
        catchError((error) => {
          if (error.status == 404) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant(
                'AnsConfiguration.Reports.MessageReportNotFound'
              )
            );
          } else if (!error.ok) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant(
                'AnsConfiguration.Reports.MessageReportFailed'
              )
            );
          }
          return of(null);
        })
      )
      .subscribe((resp) => {
        if (resp) {
          const blob = new Blob([resp], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;',
          });
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = 'risk-report.xlsx';
          link.click();
          window.URL.revokeObjectURL(downloadUrl);
        }
      });
  }

  //Función que abre el modal para filtrar los riesgos.
  openFilterDialog() {
    this.getStatePolicies();
    this.matDialog.open(this.filtersModal!, {
      width: '45vw',
      maxHeight: '90vh',
    });
  }

  //Función que se ejecuta al dar enter o click en el icono de busqueda, del input buscar.
  search(keyword: string) {
    this.requestFilterRiskTable.keyword = keyword;
    this.requestFilterRiskTable.page = 0;
    this.getRisk(this.requestFilterRiskTable);
  }

  //Función que ordena los datos de la tabla.
  onSortClick() {
    if (this.requestFilterRiskTable.order === 'asc') {
      this.requestFilterRiskTable.order = 'desc';
      this.getRisk(this.requestFilterRiskTable);
    } else {
      this.requestFilterRiskTable.order = 'asc';
      this.getRisk(this.requestFilterRiskTable);
    }
  }

  //Detecta los cambios en la paginación de la tabla.
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.currentPosition = this.pageIndex * this.pageSize;
    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 0;
    }
    this.requestFilterRiskTable.page = this.currentPosition;
    this.requestFilterRiskTable.pageSize = this.pageSize;
    let payload: RequestGetRiskModel = this.requestFilterRiskTable;
    this.getRisk(payload);
  }

  //Controlador de las acciones configuradas en la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'manage':
        this._customeRouter.navigate([
          `dashboard/policy/see-details-individual-policy/${this.idPolicy}/${
            this.idtypePolicy
          }/${event.value.idPolicyRisk}/${0}`,
        ]);
        break;
      case 'documents':
        if (event?.value?.idFile && event.value.idFile !== 0) {
          this.getPolicyCover(event.value.idFile);
        }
        break;
      default:
        break;
    }
  }

  //Genera la busqueda sobre la tabla de riesgos con los filtros seleccionados.
  applyFilters() {
    this.requestFilterRiskTable.statePolicyId = this.policyStatusValue;
    this.getRisk(this.requestFilterRiskTable);
  }

  //Elimina los filtros aplicados a la tabla de riesgos.
  cleanFilterForm() {
    this.policyStatusValue = 0;
    this.requestFilterRiskTable = {
      keyword: '',
      statePolicyId: 0,
      order: 'desc',
      page: 0,
      pageSize: 5,
      idPolicy: this.idPolicy,
    };
    this.getRisk(this.requestFilterRiskTable);
  }

  //Obtiene la carátula de una póliza.
  getPolicyCover(idFilePolicy: any) {
    this._fileService.getUploadFileById(idFilePolicy).subscribe({
      next: (fileData) => {
        const filePath = fileData.result?.vFilePath;
        const fileName = fileData.result?.vFileName;
        if (filePath) {
          this.downloadFileByFilePath(filePath, fileName);
        } else {
          this._messageService.messageWaring(
          '',
          'vFilePath no está disponible en la respuesta:'
          )
        }
      },
      error: (err) => {
        this._messageService.messageWaring(
          '',
          'Error retrieving file data:' + err
          )
      }
    });
  }

  //Descargar archivo
  downloadFileByFilePath(filePath: string, title: string) {
    this._fileService.downloadFileByFilePath(filePath).subscribe({
      next: (blob) => {
        const a = document.createElement('a');
        const objectUrl = URL.createObjectURL(blob);
        a.href = objectUrl;
        a.download = title || 'archivo';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(objectUrl);
      },
      error: (err) => {
        this._messageService.messageWaring(
          '',
          'Error al descargar el archivo'
        );
      }
    });
  }

  //Cambio de estructura de tabla
    chageStructureTable(){
      if(this.idtypePolicy === 2){
        this.estructTable = [
          {
            columnLabel: this.keyField,
            columnValue: 'value',
          },
          {
            columnLabel: this._translateService.instant('Policy.NameSecured'),
            columnValue: 'nameCustomer',
          },
          {
            columnLabel: this._translateService.instant('MyProfile.DocumentType'),
            columnValue: 'typeDocument',
          },
          {
            columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
            columnValue: 'numberDocument',
          },

          {
            columnLabel: this._translateService.instant('GeneralInformation.StartValidity'),
            columnValue: 'dStartValidity',
          },
          {
            columnLabel: this._translateService.instant('GeneralInformation.EndValidity'),
            columnValue: 'dEndValidity',
          },

          {
            columnLabel: this._translateService.instant('Status'),
            columnValue: 'state',
            functionValue: (item: any) =>
              this._policyTableService.changeRiskStatusValue(item),
          },
          {
            columnLabel: this._translateService.instant(
              'ProceduresViewerSettings.Manage'
            ),
            columnValue: 'manage',
            columnIcon: 'search',
          },
          {
            columnLabel: this._translateService.instant(
              'DocumentModule.TitleDocument'
            ),
            columnValue: 'documents',
            columnIcon: 'download',
          },
        ];
      }else{
        this.estructTable = [
          {
            columnLabel: 'Id ' + this._translateService.instant('Reports.Policy'),
            columnValue: 'idPolicyRisk',
          },
          {
            columnLabel: this._translateService.instant('Policy.NameSecured'),
            columnValue: 'nameCustomer',
          },
          {
            columnLabel: this._translateService.instant('MyProfile.DocumentType'),
            columnValue: 'typeDocument',
          },
          {
            columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
            columnValue: 'numberDocument',
          },

          {
            columnLabel: this._translateService.instant('GeneralInformation.StartValidity'),
            columnValue: 'dStartValidity',
          },
          {
            columnLabel: this._translateService.instant('GeneralInformation.EndValidity'),
            columnValue: 'dEndValidity',
          },

          {
            columnLabel: this._translateService.instant('Status'),
            columnValue: 'state',
            functionValue: (item: any) =>
              this._policyTableService.changeRiskStatusValue(item),
          },
          {
            columnLabel: this._translateService.instant(
              'ProceduresViewerSettings.Manage'
            ),
            columnValue: 'manage',
            columnIcon: 'search',
          },
          {
            columnLabel: this._translateService.instant(
              'DocumentModule.TitleDocument'
            ),
            columnValue: 'documents',
            columnIcon: 'download',
          },
        ];
      }
    }
}
