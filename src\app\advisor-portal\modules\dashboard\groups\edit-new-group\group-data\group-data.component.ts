import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import {
  TranslateModule
} from '@ngx-translate/core';
import { Subscription, debounceTime } from 'rxjs';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { LevelModel, UserGroupLevelModel } from 'src/app/shared/models/groups';
import { EditNewGroupData } from 'src/app/shared/models/groups/edit-new-group-data.model';
import { GroupsService } from 'src/app/shared/services/groups/groups.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-group-data',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    TranslateModule,
    PreventionSqlInjectorDirective,
    MatTooltipModule,
    MatIconModule
  ],
  templateUrl: './group-data.component.html',
  styleUrls: ['./group-data.component.scss'],
})
export class GroupDataComponent implements OnInit, OnDestroy {
  @Output() formChange: EventEmitter<any> = new EventEmitter();
  @Input() action: string = '';
  @Input() idGroup: number = 0;
  form: FormGroup = new FormGroup({});
  private _settingCountryAndCompanySubscription?: Subscription;
  private _editNewGroupDataSubscription?: Subscription;

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    private _groupsService: GroupsService,
    private _settingService: SettingService,
    private _router: Router
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    if (this.action === 'edit') {
      this.getGroupById();
    } else {
      this.getGroupById();
      this.getsettingCountryAndCompanySubscription();
    }
  }

  getsettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            if (this.action === 'create') {
              this.form
                .get('fkIIdBusinessByCountry')
                ?.setValue(response.enterprise.pkIIdBusinessByCountry);
            }
          }
        }
      );
  }

  getGroupById() {
    if (this.idGroup !== 0) {
      this._groupsService.getGroupById(this.idGroup).subscribe({
        next: (response) => {
          if (!response.error) {
            this.form.get('pkIIdGroup')?.setValue(response.result.pkIIdGroup);
            this.form
              .get('fkIIdBusinessByCountry')
              ?.setValue(response.result.fkIIdBusinessByCountry);
            this.form.get('vGroupName')?.setValue(response.result.vGroupName);
            this.form
              .get('vDescription')
              ?.setValue(response.result.vDescription);
            this.form.get('bActive')?.setValue(response.result.bActive);
          }
        },
      });
    }
  }

  initForm() {
    this.form = this._fb.group({
      pkIIdGroup: [0],
      fkIIdBusinessByCountry: [0],
      vGroupName: [null, [Validators.required]],
      vDescription: [null, [Validators.required]],
      bActive: [true, [Validators.required]],
    });
    this.form.valueChanges.pipe(debounceTime(600)).subscribe({
      next: (data) => {
        let payload: EditNewGroupData;
        let level: LevelModel[] = [
          {
            bActive: false,
            fkIIdGroup: 0,
            iOrder: 0,
            pkIIdLevel: 0,
            vLevelName: '',
          },
        ];
        let user: UserGroupLevelModel[] = [
          {
            bActive: false,
            fkIIdLevel: 0,
            fkIIdUser: 0,
            pkIIdUserLevel: 0,
          },
        ];
        payload = {
          group: data,
          formgroupValid: this.form.valid,
          level: level,
          forLevelValid: false,
          user: user,
          forUserValid: false,
          typeAction: this.action,
        };
        this._groupsService.setCurrentEditNewGroupData(payload);
      },
    });
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
    this._editNewGroupDataSubscription?.unsubscribe();
  }
}
