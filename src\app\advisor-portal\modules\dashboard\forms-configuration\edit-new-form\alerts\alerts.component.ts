import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatDialog } from '@angular/material/dialog';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { WarningModel } from 'src/app/shared/models/configuration-form/warnings/warning.model';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { EditNewAlertComponent } from './edit-new-alert/edit-new-alert.component';
import { MessageService } from 'src/app/shared/services/message/message.service';

@Component({
  selector: 'app-alerts',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    Modal2Component,
    EditNewAlertComponent
  ],
  templateUrl: './alerts.component.html',
  styleUrls: ['./alerts.component.scss'],
})
export class AlertsComponent implements OnInit, OnChanges {
  @Input() idForm: number = 0;
  //texto que se muestra en el titulo del modal y en boton guardar segun si se va a crear o a editar
  tiitleText:string=''
  saveButtonText:string=''
  //idWarning en caso de que se este editando
  idWarning:number=0;
  //variable que valida si el formulario de crear/editar alerta es valido
  isFormValid:boolean=false;
  //modelo de alerta que guarda el resultado del formulario.
  warningModel?:WarningModel;

  @ViewChild('editNewWarningModal') editNewWarningModal?: TemplateRef<any>;
  //data table de tablas
  estructTableWarnings: BodyTableModel[] = [
    { 
      columnLabel: this._translateService.instant('Warnings.WarningName'), 
      columnValue: 'vName' 
    },
    { 
      columnLabel: this._translateService.instant('Warnings.CalculatedFrom'), 
      columnValue: 'vNameField' 
    },
    { 
      columnLabel: this._translateService.instant('Warnings.CalculatedIn'), 
      columnValue: 'calculated' 
    },
    { 
      columnLabel: this._translateService.instant('Warnings.Time'), 
      columnValue: 'iValue' 
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsService.changeStatusValue(item)
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    }
  ];
  dataTableWarnings: WarningModel[] = [];

  constructor(
    private _moduleService: ModuleService,
    private _translateService: TranslateService, 
    private _editNewWarningDialog: MatDialog,
    private _messageService:MessageService,
    public utilsService: UtilsService
  ){}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['idForm']) {
      this.idForm = changes['idForm'].currentValue;
      this.idForm!=0?this.getWarnings(this.idForm):''
    }
  }
  
  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTableWarnings[0].columnLabel = this._translateService.instant(
        'Warnings.WarningName'
      );
      this.estructTableWarnings[1].columnLabel = this._translateService.instant(
        'Warnings.CalculatedFrom'
      );
      this.estructTableWarnings[2].columnLabel = this._translateService.instant(
        'Warnings.CalculatedIn'
      );
      this.estructTableWarnings[3].columnLabel = this._translateService.instant(
        'Warnings.Time'
      );
      this.estructTableWarnings[4].columnLabel = this._translateService.instant(
        'Status'
      );
      this.estructTableWarnings[5].columnLabel = this._translateService.instant(
        'Action'
      );
    });
  }

  controller(evt:IconEventClickModel){
    this.openEditNewAlertModal(evt.value.pkIIdWarningModule);
  }

  openEditNewAlertModal(idWarning: number){
    this.isFormValid=false;
    this.idWarning = idWarning
    idWarning===0?(
      this.tiitleText=this._translateService.instant('Warnings.NewWarning'),
      this.saveButtonText=this._translateService.instant('Warnings.CreateWarning')
    ):(
      this.tiitleText=this._translateService.instant('Warnings.ModifyWarning'),
      this.saveButtonText=this._translateService.instant('Save')
    )
    const dialogRef = this._editNewWarningDialog.open(this.editNewWarningModal!, {
      disableClose: false,
      width: '80vh',
      maxHeight: '90vh'
    });
  }

  getFormResult(event: any) {    
    if(event=='invalid')
    {
      this.isFormValid= false
    }
    else{
      this.warningModel=event;
      this.isFormValid= true    
    }    
  }

  saveWarningClick(){
    this.isFormValid?(
      this.idWarning==0?this.createWarning():this.updateWarning()
    ):(
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm')
      )
    )
  }

  getWarnings(idForm: number){
    this._moduleService.getWarningList(idForm)
    .pipe(
      catchError((error) => {
        if (error.error.error) {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          this.dataTableWarnings = [];
        }        
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
      } else {
        if (resp.error) {
          this._messageService.messageError(
            this._translateService.instant('ThereWasAError') +
            resp.message
          );
        } else {
          resp.result?this.dataTableWarnings = resp.result:this.dataTableWarnings=[];
        }
      }
    });
  }

  createWarning(){
    this._moduleService.createWarning(this.warningModel!)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              ''
            );
            this.getWarnings(this.idForm)
            this._editNewWarningDialog.closeAll();
          }
        }
    });
  }

  updateWarning(){
    this._moduleService.updateWarning(this.warningModel!)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              ''
            );
            this.getWarnings(this.idForm)
            this._editNewWarningDialog.closeAll();
          }
        }
    });
  }
}
