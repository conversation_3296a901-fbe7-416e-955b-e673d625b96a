<form [formGroup]="formReportLog">
    <div class="row">
        <div class="col-md-3">
            <mat-form-field class="example-full-width w-100" appearance="fill">
                <mat-label>{{"ReportLogCarrier.StartDateLabel" | translate}}</mat-label>
                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                <mat-datepicker-toggle matIconSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
                <mat-error *ngIf="_utilsService.isControlHasError(formReportLog, 'startPicker', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-md-3">
            <mat-form-field class="example-full-width w-100" appearance="fill">
                <mat-label>{{"ReportLogCarrier.FinalDateLabel" | translate}}</mat-label>
                <input matInput [matDatepicker]="endPicker" [matDatepickerFilter]="endDateFilter"
                    formControlName="endDate">
                <mat-datepicker-toggle matIconSuffix [for]="endPicker"></mat-datepicker-toggle>
                <mat-datepicker #endPicker></mat-datepicker>
                <mat-error *ngIf="_utilsService.isControlHasError(formReportLog, 'endDate', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-md-3">
            <mat-form-field class="w-100" appearance="fill">
                <mat-label>
                    {{"ReportLogCarrier.InsuranceLabel" | translate}}
                </mat-label>
                <mat-select formControlName="insuranceName">
                    <mat-option *ngFor="let insurer of insurers" [value]="insurer.vName">
                        {{ insurer.vName }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div class="col-md-3">
            <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>
                    {{"ReportLogCarrier.DocumentLabel" | translate}}
                </mat-label>
                <input matInput formControlName="document" PreventionSqlInjector />
            </mat-form-field>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>
                    {{"ReportLogCarrier.QuoteNumberLabel" | translate}}
                </mat-label>
                <input matInput formControlName="idQuote" PreventionSqlInjector />
            </mat-form-field>
        </div>
    </div>
    <div class="cont-btn mx-3">
        <div class="row">
            <button type="button" class="w-auto mr-1 mb-1" mat-raised-button color="primary" (click)="search()"
                [disabled]="!searchButtonValid">
                {{"ReportLogCarrier.SearchButton" | translate}}
                <mat-icon iconPositionEnd fontIcon="search"></mat-icon>
            </button>
            <button type="button" class="w-auto mr-1 mb-1" mat-raised-button color="primary" (click)="cleanFilters()">
                {{"ReportLogCarrier.CleanFilterButton" | translate}}
                <mat-icon iconPositionEnd class="material-symbols-outlined">cleaning_services</mat-icon>
            </button>
        </div>
        <div class="row">
            <button type="button" class="w-auto mr-1" mat-raised-button (click)="downloadCarrierLogReport()" color="primary" [disabled]="!valid">
                {{"ReportLogCarrier.DownloadReportButton" | translate}}
                <mat-icon iconPositionEnd class="material-symbols-outlined">download</mat-icon>
            </button>
        </div>
    </div>
</form>

<div class="row mt-2">
    <app-table [displayedColumns]="estructReportLogTable" [data]="dataReportLogTable" [IsStatic]="false"
        [pageIndex]="pageIndex" [pageSize]="pageSize" [amountRows]="amountRows" (pageChanged)="onPageChange($event)"
        (iconClick)="controller($event)"></app-table>
</div>

<!-- Modal Ver Detalles -->
<ng-template #detailResponseCarrierModal>
    <app-modal2 [titleModal]="modalTitle" [showCancelButton]="true" [showCancelButtonBelow]="false"
        (closeModal)="closeModal($event) ">
        <ng-container body>
            <div class="row mb-4">
                <div class="cont-details-copy">
                    <div>
                        <h4>{{"ReportLogCarrier.Modal.QuoteResponse" | translate}}</h4>
                        <div *ngFor="let response of detailResponseCarrier.vResponse">
                            <div *ngFor="let error of response.errors">
                                <p #errorElements>{{ error.Description }}</p>
                            </div>
                        </div>
                    </div>
                    <div>
                        <button class="copy" type="button" mat-raised-button [ngStyle]="{color: primaryColor}"
                            (click)="copyContent('error')">
                            <strong>{{"Copy" | translate}}</strong>
                            <mat-icon iconPositionEnd fontIcon="copy_all"></mat-icon>
                        </button>
                    </div>
                </div>
            </div>
            <div class="row mb-5">
                <div class="cont-details-copy">
                    <div>
                        <h4>{{"ReportLogCarrier.Modal.Request" | translate}}</h4>
                        <p #requestElement>{{detailResponseCarrier.vRequest | json}}</p>
                    </div>
                    <div>
                        <button class="copy" type="button" mat-raised-button [ngStyle]="{color: primaryColor}"
                            (click)="copyContent('request')">
                            <strong>{{"Copy" | translate}}</strong>
                            <mat-icon iconPositionEnd fontIcon="copy_all"></mat-icon>
                        </button>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="cont-details-copy">
                    <div>
                        <h4>{{"ReportLogCarrier.Modal.Response" | translate}}</h4>
                        <p #responseElement>{{detailResponseCarrier.vResponse | json}}</p>
                    </div>
                    <div>
                        <button class="copy" type="button" mat-raised-button [ngStyle]="{color: primaryColor}"
                            (click)="copyContent('response')">
                            <strong>{{"Copy" | translate}}</strong>
                            <mat-icon iconPositionEnd fontIcon="copy_all"></mat-icon>
                        </button>
                    </div>
                </div>
            </div>
        </ng-container>

        <ng-container customButtonRight>
            <button type="button" mat-raised-button color="primary" (click)="closeModal(true)">
                {{"Close" | translate}}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>