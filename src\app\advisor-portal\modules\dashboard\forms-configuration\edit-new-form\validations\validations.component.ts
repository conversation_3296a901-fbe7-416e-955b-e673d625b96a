import { ChangeDetectorRef, Component,  Input,  OnInit,  SimpleChanges,  TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { ValidationTypeModel } from 'src/app/shared/models/validations/validationType.model';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AbstractControl, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { DatePipe, NgFor, NgIf } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { MatButton } from '@angular/material/button';
import { ValidationTableModel } from 'src/app/shared/models/validations/validationTable.model';
import { MatOptionSelectionChange } from '@angular/material/core';
import { Logger } from 'html2canvas/dist/types/core/logger';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { DataService } from 'src/app/shared/services/data/data.service';
import { ChildrenStateListModel } from 'src/app/shared/models/module/children-state-list.model';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ErrorHandlingService } from 'src/app/shared/services/error/errorHandlingService';

@Component({
  selector: 'app-validations',
  standalone: true,
  imports: [TranslateModule,TableComponent,MatIconModule,Modal2Component,MatFormFieldModule, MatInputModule,MatSelectModule,ReactiveFormsModule,NgIf,MatDatepickerModule,NgFor,MatButton,DatePipe,MatSlideToggleModule],
  templateUrl: './validations.component.html',
  styleUrl: './validations.component.scss'
})
export class ValidationsComponent implements OnInit {

  @Input() idForm: number = 0;

  idContent:number=0;
  modalTitle:string = this._translateService.instant('ValidationForm.AddValidation');;
  dataValidationTable: ValidationTableModel[]=[];
  estructValidationTable: BodyTableModel[] = [     
    {
      columnLabel: this._translateService.instant('ValidationForm.StructTableIdColumn'),
      columnValue: 'pkIIdValidationModule', 
      hidenColumn:true       
    },
    {
      columnLabel: this._translateService.instant('ValidationForm.StructTableValidationNameColumn'),
      columnValue: 'vNameValidation',        
    },  
    {
      columnLabel: this._translateService.instant('ValidationForm.StructTableValidationOrderColumn'),
      columnValue: 'iOrder',        
    },  
    {
      columnLabel: this._translateService.instant('ValidationForm.StructTableValidationStatusColumn'),
      columnValue: 'bActive',  
      functionValue: (item: any) => this._utilsService.changeStatusValue(item),      
    },   
    {
      columnLabel: this._translateService.instant('ValidationForm.StructTableValidationModifyColumn'),
      columnValue: 'Modify',
      columnIcon: 'create',
    },
    {
      columnLabel: this._translateService.instant('ValidationForm.StructTableValidationDeleteColumn'),
      columnValue: 'Delete',
      columnIcon: 'delete',
    }
  ];

   //Variables para modal
   @ViewChild('AddValidationModal') AddValidationModal?: TemplateRef<any>;
   modalFormAddValidation: FormGroup = new FormGroup({});
   dateFormatList:ValidationTypeModel[]=[];

   dependentFieldsList:ValidationTypeModel[]=[];
   typeOfValidationList:ValidationTypeModel[]=[];
   typeOfDateList:ValidationTypeModel[]=[];   
   typeOfActionList:ValidationTypeModel[]=[];
   stageByStateChildrenList: ValidationTypeModel[] = [];

   isDefinedInputShowed: boolean= false;
   selectedFormat!: string ;
   orderList:number[]=[]
  
  constructor(private _translateService: TranslateService,
            public modalDialog: MatDialog,
            private _formBuilder: FormBuilder,
            public _utilsService: UtilsService,
            private _parametersService: ParametersService,
            private _messageService: MessageService,
            private datePipe: DatePipe,            
            private _dataService: DataService,
            private _moduleService: ModuleService,
            private _errorHandlingService: ErrorHandlingService          
           ) {} 


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['idForm']) {
      this.idForm = changes['idForm'].currentValue;             
    }
  }

  ngOnInit(): void {
    this._dataService.fields.subscribe(data => {this.dependentFieldsList = data;});
    this._dataService.states.subscribe(data => {this.stageByStateChildrenList = data;});
   
    this.getValidationsModuleListByFormId(this.idForm);
    this.getParameters();
    this.getActionTypeParameters();    
    this.setOrder(false);    
  }
 
  onToggleChange(event: any) {} 


  getParameters() {
  this._parametersService.getParameters('Type_Validation_Form')
    .pipe(
      catchError((error) => {
        if (error.error.error) {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
        }
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {               
       this.typeOfValidationList = resp;
          
      }
    });
  }

  getActionTypeParameters(){
    this._parametersService.getParameters('ActionType_Validation_Form')
    .pipe(
      catchError((error) => {
        if (error.error.error) {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message );
        }
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {              
       this.typeOfActionList = resp;
          
      }
    });
  }


  getDateTypeParameters(){
    this._parametersService.getParameters('DateType_Validation_Form')
    .pipe(
      catchError((error) => {
        if (error.error.error) {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message );
        }
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {      
       this.typeOfDateList = resp;
       this.getDateFormatParameters();          
      } 
    });
  }

  setOrder(edit: boolean){
    if(edit){
      this.orderList = this._utilsService.generarArrayOrderList(this.dataValidationTable.length
      );
    }
    else{
      this.orderList = this._utilsService.generarArrayOrderList(this.dataValidationTable.length + 1 );
    }
  }

  onFormatChange(format: string) {
    this.selectedFormat = format;   
    const currentDate = new Date();
    let formattedDate: string | null = '';    
   // Asignar formato según la selección
   switch (format) {    
    case 'VARIABLE_DATE':           
      formattedDate = this.datePipe.transform(currentDate, 'MM-dd-yyyy');
      break;
    default:
      formattedDate = this.datePipe.transform(currentDate, 'dd-MM-yyyy');
  }

    // Asignar la fecha formateada al input
    this.modalFormAddValidation.controls['dateFormat'].setValue(formattedDate);
  }

  getDateFormatParameters(){
    this._parametersService.getParameters('Date_Format_Validation_Form')
    .pipe(
      catchError((error) => {
        if (error.error.error) {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'), error.error.message );
        }
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {              
       this.dateFormatList = resp;
          
      }
    });
  }
  

  validationTypeSelectChange(event: any) {        
    switch (event) {
      case 4:
      case 3:
      case 2:
      case 1:
        this.isDefinedInputShowed=true;
        this.modalFormAddValidation.get('dateFormat')?.setValue('');
        this.modalFormAddValidation.get('dateTypeList')?.setValue(0);
        break;    
      default:
        this.isDefinedInputShowed=false;
        this.getDateTypeParameters();
        this.modalFormAddValidation.get('definedValue')?.setValue('');
        break;
    }
    
  }
 

  getValidationsModuleListByFormId(idFormModule: number) {
    this._moduleService.getValidationModuleListByFormId(idFormModule)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(this._translateService.instant('Warning'), error.error.message);
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._messageService.messageError(this._translateService.instant('ThereWasAError') + response.message);
          } else {
            this.dataValidationTable = response.result;
            if (response.result.length > 0) {
              this.orderList = this._utilsService.generarArrayOrderList(response.result.length + 1);
            } else {
              this.orderList = this._utilsService.generarArrayOrderList(1);
            }                       
          }
        }
      });
  }

  controller(event: IconEventClickModel) {       
    switch (event.column) {
      case 'Modify':
        this.modalTitle = this._translateService.instant('ValidationForm.ModalModifyTitle');        
        this.openModal();                                
        this.idContent=event.value.pkIIdValidationModule;
        this.getValidationById(event.value.pkIIdValidationModule)                                               
        break;
      case 'Delete':          
        this.deleteValidation(event.value.pkIIdValidationModule);
        break;
      default:
        break;
    }
  }



  addValidation() {            
    if(!this.modalFormAddValidation.valid){
      return          
    }
    else{     
      var newValidation:ValidationTableModel={
        fkIIdFormModule:this.idForm,
        fkIIdFieldModule: this.modalFormAddValidation.get('actionDependFieldList')?.value,
        fkIIdNewState:this.modalFormAddValidation.get('stateTaskChangeList')?.value,
        vNameValidation:this.modalFormAddValidation.get('validationName')?.value,
        iTypeValidation:this.modalFormAddValidation.get('validationTypeList')?.value,
        vDefinedValue:this.modalFormAddValidation.get('definedValue')?.value,        
        iTypeDate:this.modalFormAddValidation.get('dateTypeList')?.value,
        dDateComparation:this.modalFormAddValidation.get('dateFormat')?.value,
        iTypeAction:this.modalFormAddValidation.get('actionTypeList')?.value,
        iOrder:this.modalFormAddValidation.get('validationOrder')?.value,
        bActive:this.modalFormAddValidation.get('validationStatus')?.value,
        bIsDelete:true        
      };    
      this._moduleService.createValidationForm(newValidation).pipe(
        catchError((error) => {
          this._errorHandlingService.handleError(error);
          return of([]);
        }))
        .subscribe((resp: ResponseGlobalModel | never[]) => {
            if (Array.isArray(resp)) {
              console.log('El tipo de datos devueltos es un array vacío.');
            } else {
              if (resp.error) {
                this._messageService.messageError(this._translateService.instant('Warning') + resp.message);
              } else {
                this._messageService.messageSuccess(this._translateService.instant(resp.message),this._translateService.instant(resp.message));
                this.getValidationsModuleListByFormId(this.idForm);
                this.modalDialog.closeAll();
              }
            }
          });  
    } 
  }

  getValidationById(validationId: number){
   this._moduleService.getValidationById(validationId).pipe(
        catchError((error) => {
          this._messageService.messageWaring(this._translateService.instant('Warning'),error.error.message);
          return of([]);
        })
      ).subscribe((response: ResponseGlobalModel | never[]) => {
    if (Array.isArray(response)) {
    } else {
      if (response.error) {
      } else {
       
        this.modalFormAddValidation.patchValue(
          {
            actionDependFieldList:response.result.fkIIdFieldModule,
            validationName: response.result.vNameValidation,
            validationStatus:response.result.bActive,           
            validationOrder:response.result.iOrder,           
            validationTypeList:response.result.iTypeValidation, 
            definedValue:response.result.vDefinedValue, 
            actionTypeList:response.result.iTypeAction, 
            stateTaskChangeList:response.result.fkIIdNewState, 
            dateTypeList:response.result.iTypeDate, 
            dateFormat:response.result.dDateComparation, 
          });

          this.validationTypeSelectChange(response.result.iTypeValidation);

      }
    }
  });

  }

  ediValidation() {    
    if(!this.modalFormAddValidation.valid){
      return          
    }else{
      var newValidation:ValidationTableModel={
        fkIIdFormModule:this.idForm,
        fkIIdFieldModule: this.modalFormAddValidation.get('actionDependFieldList')?.value,
        fkIIdNewState:this.modalFormAddValidation.get('stateTaskChangeList')?.value,
        vNameValidation:this.modalFormAddValidation.get('validationName')?.value,
        iTypeValidation:this.modalFormAddValidation.get('validationTypeList')?.value,
        vDefinedValue:this.modalFormAddValidation.get('definedValue')?.value,        
        iTypeDate:this.modalFormAddValidation.get('dateTypeList')?.value,
        dDateComparation:this.modalFormAddValidation.get('dateFormat')?.value,
        iTypeAction:this.modalFormAddValidation.get('actionTypeList')?.value,
        iOrder:this.modalFormAddValidation.get('validationOrder')?.value,
        bActive:this.modalFormAddValidation.get('validationStatus')?.value,
        bIsDelete:true        
      };    
      this._moduleService.updateValidationForm(this.idContent,newValidation)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(this._translateService.instant('ThereWasAError'),error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess('',this._translateService.instant('Modified'));
          this.modalDialog.closeAll();
          this.getValidationsModuleListByFormId(this.idForm);
        }
      });
    }
  }

  deleteValidation(pkIIdValidationModule:number){
    this._moduleService.deleteValidationForm(pkIIdValidationModule).subscribe({
      next: (response) => {
        if (!response.error) {
            this._messageService.messageSuccess(this._translateService.instant('DataSavedSuccessfully'), response.message );   
            this.getValidationsModuleListByFormId(this.idForm)              
        } else {
          this._messageService.messageInfo(this._translateService.instant('ThereWasAError'),response.message );

        }
      },
    });
  }

  openModal() {
    this.initFormModalValidation();
    this.modalDialog.open(this.AddValidationModal!, {
      width: '720px',
    });
  }

  requiredIfNotNull(control: AbstractControl) {
    return control.value > 0 && control.value !== null && control.value === '' ? { required: true } : 0;
  }

  initFormModalValidation() {
    this.modalFormAddValidation = this._formBuilder.group({
      validationStatus:[true],
      validationName:['',[Validators.required]],
      validationOrder:[0,[Validators.required]],
      actionDependFieldList:['', [Validators.required]],
      validationTypeList:['', [Validators.required]],
      definedValue:[null, [this.requiredIfNotNull]],
      actionTypeList:['', [Validators.required]],
      stateTaskChangeList:['', [Validators.required]],
      dateTypeList:[0, [this.requiredIfNotNull]],
      dateFormat:[null, [this.requiredIfNotNull]],
    });    
  }
  
  closeModal(event: boolean) {
    this.idContent=0; 
  }
}
