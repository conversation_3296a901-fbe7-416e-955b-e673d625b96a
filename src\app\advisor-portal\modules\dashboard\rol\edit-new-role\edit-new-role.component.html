<form [formGroup]="formEditNewRole" (ngSubmit)="complete()">
    
    <div class="row">
        <!-- role estandar -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
            <mat-label>
                {{ 'Role.SelectRole' | translate }} 
            </mat-label>
            <mat-select  formControlName="roleStandarId">
                <mat-option
                    *ngFor="let role of roleStandarList"
                    [value]="role.pk_i_IdRole"
                    (onSelectionChange)="selectRoleStandar_Change(role)"
                >
                    {{ role.v_RoleName }}
                </mat-option>
            </mat-select>
            <mat-error
            *ngIf="
                _utilsService.isControlHasError(formEditNewRole, 'roleStandarId', 'required')
            "
            >
                {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>
        </mat-form-field>

        <!-- nombre del rol -->
        <mat-form-field appearance="outline" class="w-25 mb-2">
            <mat-label>
                {{ 'Role.RoleName' | translate }} 
            </mat-label>
            <input
                matInput
                formControlName="vRoleName"
                required
                type="text"
                PreventionSqlInjector
            />
            <mat-error
                *ngIf="_utilsService.isControlHasError(formEditNewRole, 'vRoleName', 'required')"
            >
                {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>
        </mat-form-field>
    
        <!-- descripcion del rol -->
        <mat-form-field appearance="outline" class="col-4 mb-2">
            <mat-label>
                {{ 'Role.RoleDescription' | translate }}
            </mat-label>
            <input
                matInput
                formControlName="vDescription"   
                required
                type="text"
                PreventionSqlInjector
            />
            <mat-error
                *ngIf="_utilsService.isControlHasError(formEditNewRole, 'vDescription', 'required')"
            >
                {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>            
        </mat-form-field>

        <!-- estado y solo lectura slides -->
        <div class="col-2">
            <mat-slide-toggle
                class="w-100 mb-2"
                formControlName="bActive">
                {{ 'Role.ActiveRole' | translate }}
            </mat-slide-toggle>

            <mat-slide-toggle 
                class="w-100 mb-2"
                formControlName="bEdit">
                {{ 'ReadOnly' | translate }}
            </mat-slide-toggle> 
        </div>
    </div>

    <div class="row">

        <!-- card modulos -->
        <div class="w-50">
            <div class="row">
                <div class="col-9">
                    <h4>
                        {{ 'Role.StandardRoleModules' | translate }}
                    </h4>
                </div>
                <div class="col-3">
                    <b>
                        <a class="link" (click)="selectAll_Click()"> 
                            {{ 'SelectAll' | translate }}
                        </a>
                    </b>
                </div>
            </div>
            
            <mat-card >
                <mat-card-content>
                    <ul *ngFor="let module of moduleList" >

                        <!-- modulos que contienen submodulos -->
                        <div *ngIf="module.subModules.length > 1; else other_Modules">
                            <cdk-accordion class="">
                                <cdk-accordion-item             
                                #accordionItem="cdkAccordionItem"
                                class="example-accordion-item"
                                role="button"
                                tabindex="0"
                                [expanded]="accordionExpanded"
                                [attr.aria-expanded]="accordionItem.expanded"
                                >                                
                                    <div 
                                    class="example-accordion-item-header row" 
                                    (click)="accordionItem.toggle()">
                                        <div class="form-check col-8">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                {{module.description | translate}}
                                            </label>
                                        </div>  
                                        <div class="col-4">
                                            <span class="example-accordion-item-description">
                                                {{ accordionItem.expanded ? 'close' : 'open' }}
                                            </span>
                                        </div>
                                    </div>

                                    <div
                                    *ngFor="let subModule of module.subModules"
                                    class="example-accordion-item-body"
                                    [style.display]="accordionItem.expanded ? '' : 'none'">
                                        <div class="form-check">   
                                            <input 
                                                type="checkbox" 
                                                value={{subModule.idSonMenu}} 
                                                id="CheckSubModule{{subModule.idSonMenu}}"
                                                (change)="checkSubModule_Change(subModule, $event)"
                                                [checked] = "subModule.checked"
                                            />
                                            <label 
                                                class="form-check-label"  
                                                (click)="labelCheckSubModule_Click(subModule.idSonMenu)"
                                            >
                                                {{subModule.sonDescription | translate}}
                                            </label>
                                        </div>        
                                    </div>

                                </cdk-accordion-item>

                            </cdk-accordion>                                    
                        </div>  

                        <!-- modulos que no contienen submodulos -->
                        <ng-template #other_Modules>
                            <div *ngFor="let subModule of module.subModules">
                                <div class="form-check">   
                                    <input 
                                        type="checkbox" 
                                        class="form-check-input" 
                                        value={{module.idModule}} 
                                        id="CheckSubModule{{subModule.idSonMenu}}"
                                        (change)="checkSubModule_Change(subModule, $event)"
                                        [checked] = "subModule.checked"
                                    />
                                    <label 
                                        class="form-check-label" 
                                        (click)="labelCheckSubModule_Click(subModule.idSonMenu)"
                                        
                                    >
                                        {{subModule.sonDescription }}
                                    </label>    
                                </div>
                            </div>                                     
                        </ng-template>       

                    </ul>
                </mat-card-content>
            </mat-card>
        </div>

        <!-- card funcionalidades -->
        <div class="w-50">
            <h4>
                {{ 'Role.Functionalities' | translate }}
            </h4>
            <mat-card >
                <mat-card-content>
                    <h5>{{funcionalidadesText}}</h5>
                    <ul *ngFor="let funcionalidad of actionsList">
                        <div class="form-check">   
                            <input 
                                type="checkbox" 
                                class="form-check-input" 
                                value={{funcionalidad.pk_i_IdAction}} 
                                id="CheckAction{{funcionalidad.pk_i_IdAction}}"
                                [checked]="funcionalidad.checked"
                                (change)="CheckAction_Change(funcionalidad, $event)">
                            <label class="form-check-label" for="CheckAction{{funcionalidad.pk_i_IdAction}}">
                                {{funcionalidad.v_Name | translate }}
                            </label>
                        </div> 
                    </ul>
                </mat-card-content>
            </mat-card>
        </div>

    </div>

    <br/>
    
    <div class="row">
        <div class="d-flex justify-content-center mb-3">
            <button
              class="col-2"
              mat-raised-button
              color="primary"
              type="submit"
            >
                {{saveButtonText}}
            </button>
        </div>
    </div>
    
    <!-- <div class="modal-footer d-flex justify-content-center">
        <button  type="submit"   class="btn btn-primary" >{{labelButton}} rol</button>	  
    </div> -->

</form>		
    
