import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';

import { PageEvent } from '@angular/material/paginator';
import { catchError, of, map } from 'rxjs';
import {
  DataObject,
  ProcedureBodyModel,
  ProcedureHead,
} from 'src/app/shared/models/procedures';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Router } from '@angular/router';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { DetailTaskProcedureComponent } from '../detail-task-procedure/detail-task-procedure.component';

@Component({
  selector: 'app-all-procedures',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    TableComponent,
    DetailTaskProcedureComponent,
    Modal2Component,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatNativeDateModule,
  ],
  templateUrl: './all-procedures.component.html',
  styleUrls: ['./all-procedures.component.scss'],
})
export class AllProceduresComponent implements OnInit {
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;
  @ViewChild('searchModal') searchModal?: TemplateRef<any>;
  @ViewChild('seeDetailsTaskModal') seeDetailsTaskModal?: TemplateRef<any>;

  //Variables para filtrar la data.
  keyword: string = '';
  disabledFilter: boolean = true;
  listSelectedStates: string[] = [];
  externalSearchPaginator: boolean = false;
  disabledDetailTask: boolean = false;
  dataDetailTask: any = {};
  cleanData: ProcedureBodyModel = {
    startDate: null,
    endDate: null,
    from: 0,
    idBusinessCountry: 0,
    stateName: [],
    idUser: 0,
    pageSize: 5,
  };
  lastGeneralFilterBody: ProcedureBodyModel = {
    startDate: null,
    endDate: null,
    from: 0,
    idBusinessCountry: 0,
    stateName: [],
    idUser: 0,
    pageSize: 0,
  };
  lastBodyExternalSearch: ProcedureBodyModel = {
    idTask: null,
    startDate: null,
    endDate: null,
    from: 0,
    pageSize: 0,
    idBusinessCountry: 0,
  };

  proceduresHead: ProcedureHead[] = [];

  //Estructura de la tabla.
  estructTableProcedures: BodyTableModel[] = [];
  dataTableProcedures: any[] = [];

  //listas para los checks
  alertList = [
    { id: 1, checked: false, name: 'Vencido' },
    { id: 2, checked: false, name: 'Por vencer' },
    { id: 3, checked: false, name: 'En curso' },
  ];
  statusList = [
    { id: 1, checked: false, name: 'Abierto' },
    { id: 2, checked: false, name: 'En curso' },
    { id: 3, checked: false, name: 'Cerrado' },
  ];
  //variable formulario.
  formFilter: FormGroup = new FormGroup({});
  formSearch: FormGroup = new FormGroup({});

  //Variables de empresa pais seleccionada.
  idBusinessByCountry: number = 0;
  idUser: number = 0;

  //Variables el paginado de la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;
  currentPosition: number = 0;

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _transactionService: TransactionService,
    private _translateService: TranslateService,
    private _utilsService: UtilsService,
    public matDialog: MatDialog,
    private _settingService: SettingService,
    private _customRouter: CustomRouterService,
    private _taskTayConfigService: TaskTayConfigService,
    private _router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  async ngOnInit(): Promise<void> {
    await this.getSettingInit();
    this.fetchHeaderTableConfig();
    this.subscribeToLanguageChanges();
  }

  /**
   * Fetches the table header configuration and processes it.
   * Subscribes to the service to get the table header configuration and then processes the results.
   * The processed data is used to create the table configuration.
   */
  private fetchHeaderTableConfig(): void {
    this._taskTayConfigService
      .GetHeaderTableViewerProcedures(this.idBusinessByCountry)
      .subscribe((r) => {
        this.proceduresHead = this.processProceduresHead(r.result);
        this.estructTableProcedures = this.createTableConfig(
          this.proceduresHead
        );
      });
  }

  /**
   * Processes the table header configuration data.
   * Removes spaces from the field names and adjusts specific field names.
   * @param proceduresHead - The array of procedures head data to be processed.
   * @returns The processed array of procedures head data.
   */
  private processProceduresHead(proceduresHead: any[]): any[] {
    return proceduresHead.map((res: any) => ({
      ...res,
      vFieldName:
        res.vFieldName === 'Id de tarea'
          ? 'IddeTarea'
          : res.vFieldName.replace(/\s+/g, ''),
    }));
  }

  /**
   * Subscribes to language changes and updates the column labels.
   * Listens for language change events and updates the table column labels accordingly.
   */
  private subscribeToLanguageChanges(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      if (this.proceduresHead) {
        this.updateColumnLabels();
      }
    });
  }

  /**
   * Updates the column labels with the corresponding translations.
   * Iterates through the table configuration and updates each column label based on the current language.
   */
  private updateColumnLabels(): void {
    this.estructTableProcedures.forEach((procedure, index) => {
      procedure.columnLabel = this._translateService.instant(
        this.proceduresHead[index].vFieldName
      );
    });
  }

  /**
   * Creates the table configuration based on the provided procedures head data.
   * Verifies and adds "Details" and "Manage" fields if they do not exist.
   * Maps the objects and translates the labels.
   *
   * @param proceduresHead - The array of procedures head data to be processed.
   * @returns An array of BodyTableModel objects representing the table configuration.
   */
  private createTableConfig(proceduresHead: ProcedureHead[]): BodyTableModel[] {
    // Verificar y agregar los campos "Details" y "Manage" si no existen
    if (!proceduresHead.some((head) => head.vFieldName === 'Manage')) {
      proceduresHead.push({
        vFieldName: 'Details',
        iOrder: proceduresHead.length + 1,
      });
      proceduresHead.push({
        vFieldName: 'Manage',
        iOrder: proceduresHead.length + 2,
      });
    }

    // Mapear los objetos y traducir los labels
    return proceduresHead.map((head) => ({
      columnLabel: this._translateService.instant(head.vFieldName),
      columnValue: head.vFieldName,
      ...(head.vFieldName === 'Estado' && {
        functionValue: (item: any) =>
          this._utilsService.changeStatusTaskValue(item),
      }),
      ...(head.vFieldName === 'Details' && {
        columnIcon: 'search',
      }),
      ...(head.vFieldName === 'Manage' && {
        columnIcon: 'arrow_right_alt',
      }),
    }));
  }

  //Obtiene la información de la empresa pais, que se encuentra en la configuración inicial.
  async getSettingInit() {
    let dataSettingInit = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = dataSettingInit.idBusinessByCountry;
    this.idUser = dataSettingInit.idUser;
    let payload: ProcedureBodyModel = {
      endDate: null,
      from: 0,
      idBusinessCountry: this.idBusinessByCountry,
      stateName: null,
      idUser: this.idUser,
      pageSize: 5,
      startDate: null,
    };
    if (this.idBusinessByCountry && this.idUser) {
      this.getProceduresList(payload);
    }
  }

  //Delcaración del formulario formFilter.
  initFilterForm() {
    this.formFilter = this._fb.group({
      startDate: null,
      endDate: null,
      from: this.pageIndex,
      idBusinessCountry: this.idBusinessByCountry,
      stateName: [],
      idUser: this.idUser,
      pageSize: this.pageSize,
    });
  }

  //Delcaración del formulario formSearch.
  initSearchForm() {
    this.formSearch = this._fb.group({
      idBusinessCountry: this.idBusinessByCountry,
      idTask: null,
      from: this.pageIndex,
      pageSize: this.pageSize,
      startDate: null,
      endDate: null,
    });
  }

  //Función que agg los estados a un array de string para poder filtrar.
  checkedChange(name: string) {
    const stateName = name;
    //Cambiamos el valor de la propiedad checked en el array original para que al momento de borrar el filtro, pueda verse reflejado el cambio a nivel visual.
    let statusFound = this.statusList.find(
      (status) => status.name === stateName
    );
    this.statusList.forEach((estado) => {
      if (estado.name === statusFound?.name) {
        estado.checked = !estado.checked;
      }
    });
    //Comprobamos si el estado ya esta agg, si lo está, lo elimina, sino lo agg.
    if (stateName) {
      const index = this.listSelectedStates.indexOf(stateName);
      if (index !== -1) {
        this.listSelectedStates.splice(index, 1);
      } else {
        this.listSelectedStates.push(stateName);
      }
    }
    this.formFilter.get('stateName')?.setValue(this.listSelectedStates);
  }

  //Función que abre el modal de filtros generales.
  openFilterDialog() {
    const dialogRef = this.matDialog.open(this.filtersModal!, {
      width: '30vw',
      maxHeight: '90vh',
    });
    this.initFilterForm();
    this.formFilter.patchValue(this.lastGeneralFilterBody);
  }

  //Función que abre el modal de búsqueda externa.
  openSearchDialog() {
    const dialogRef = this.matDialog.open(this.searchModal!, {
      width: '50vw',
      maxHeight: '90vh',
    });
    this.initSearchForm();
    this.formSearch.patchValue(this.lastBodyExternalSearch);
  }

  //Función que abre el modal de ver detalles de un trámite.
  seeDetailsTask() {
    const dialogRef = this.matDialog.open(this.seeDetailsTaskModal!, {
      width: '50vw',
      maxHeight: '90vh',
    });
  }

  //Fubnción que borra los filtros aplicados a el formulario formFilter.
  cleanFilterForm(resetAll?: boolean) {
    this.formFilter.reset();
    this.formFilter.get('from')?.setValue(0);
    this.formFilter
      .get('idBusinessCountry')
      ?.setValue(this.idBusinessByCountry);
    this.formFilter.get('idUser')?.setValue(this.idUser);
    this.formFilter.get('pageSize')?.setValue(this.pageSize);
    if (resetAll){
      this.statusList.forEach((estado) => (estado.checked = false));
      this.listSelectedStates = [];
      this.lastGeneralFilterBody = {
        startDate: null,
        endDate: null,
        from: 0,
        idBusinessCountry: 0,
        stateName: [],
        idUser: 0,
        pageSize: 5,
      };
    }
  }

  //Fubnción que borra los filtros aplicados a el formulario formSearch.
  cleanFilterFormSearch() {
    this.formSearch.reset();
    this.formSearch.get('from')?.setValue(0);
    this.formSearch.get('idBusinessCountry')?.setValue(this.idBusinessByCountry);
    this.formSearch.get('pageSize')?.setValue(this.pageSize);
    this.lastBodyExternalSearch = {
      idTask: null,
      startDate: null,
      endDate: null,
      from: 0,
      pageSize: 5,
      idBusinessCountry: 0,
    };
  }

  //Función que aplica los filtros seleccionados en el modal de filtros generales.
  applyFilters() {
    this.pageIndex = 0;
    let payload: ProcedureBodyModel = {
      startDate: this._utilsService.formatDate(
        this.formFilter.get('startDate')?.value
      ),
      endDate: this._utilsService.formatDate(
        this.formFilter.get('endDate')?.value
      ),
      from: this.pageIndex,
      idBusinessCountry: this.idBusinessByCountry,
      stateName: this.listSelectedStates,
      idUser: this.idUser,
      pageSize: this.pageSize,
    };
    this.lastGeneralFilterBody = payload;
    this.getProceduresList(payload);
  }

  //Función que aplica los filtros seleccionados en el modal de búsqueda externa.
  externalSearch() {
    this.pageIndex = 0;
    let payload: ProcedureBodyModel = {
      startDate: this._utilsService.formatDate(
        this.formSearch.get('startDate')?.value
      ),
      endDate: this._utilsService.formatDate(
        this.formSearch.get('endDate')?.value
      ),
      from: this.pageIndex,
      pageSize: this.pageSize,
      idBusinessCountry: this.idBusinessByCountry,
      idTask: this.formSearch.get('idTask')?.value,
    };
    this.lastBodyExternalSearch = payload;
    this.listExternalProcedures(payload);
  }

  //Función que se ejecuta al dar enter o click en el icono de busqueda, del input buscar.
  search(keyword: string) {
    this.pageIndex = 0;
    if (keyword.trim() === '') {
        let payload: ProcedureBodyModel = {
            startDate: this._utilsService.formatDate(
                this.cleanData.startDate
            ),
            endDate: this._utilsService.formatDate(
                this.cleanData.endDate
            ),
            from: this.cleanData.from,
            idBusinessCountry: this.idBusinessByCountry,
            stateName: this.cleanData.stateName,
            idUser: this.idUser,
            pageSize: this.pageSize,
        };
        this.cleanFilterForm(true);
        this.getProceduresList(payload, keyword);
    } else if (this.externalSearchPaginator) {
        let payload: ProcedureBodyModel = {
            startDate: this._utilsService.formatDate(
                this.lastBodyExternalSearch.startDate
            ),
            endDate: this._utilsService.formatDate(
                this.lastBodyExternalSearch.endDate
            ),
            from: this.pageIndex,
            pageSize: this.pageSize,
            idBusinessCountry: this.lastBodyExternalSearch.idBusinessCountry,
            idTask: this.lastBodyExternalSearch.idTask,
        };
        this.listExternalProcedures(payload, keyword);
    } else {
        let payload: ProcedureBodyModel = {
            startDate: this._utilsService.formatDate(
                this.lastGeneralFilterBody.startDate
            ),
            endDate: this._utilsService.formatDate(
                this.lastGeneralFilterBody.endDate
            ),
            from: this.pageIndex,
            idBusinessCountry: this.idBusinessByCountry,
            stateName: this.listSelectedStates,
            idUser: this.idUser,
            pageSize: this.pageSize,
        };
        this.getProceduresList(payload, keyword);
    }
  }

  //Obtiene los tramites filtrados por empresa pais y otros filtros opcionales.
  getProceduresList(body: ProcedureBodyModel, keyword?: string) {
    this.externalSearchPaginator = false;
    this._transactionService
      .listProcedures(body, keyword)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          // this.listSelectedStates = [];
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTableProcedures = [];
        } else {
          this.estructTableProcedures[
            this.estructTableProcedures.length - 1
          ].columnIcon = 'arrow_right_alt';
          this.dataTableProcedures = resp.result;

          this.dataTableProcedures = this.dataTableProcedures.map(
            this.removeSpacesFromKeys
          );

          this.amountRows = resp.rowCount;
          // this.listSelectedStates = [];
          this.matDialog.closeAll();
          this.cleanFilterForm();
        }
      });
  }

  removeSpacesFromKeys = (obj: DataObject): DataObject => {
    return Object.keys(obj).reduce((acc: DataObject, key: string) => {
      const newKey = key.replace(/\s+/g, '');
      acc[newKey] = obj[key];
      return acc;
    }, {});
  };

  //Obtiene los tramites externos filtrados por empresa pais y otros filtros opcionales.
  listExternalProcedures(body: ProcedureBodyModel, keyword?: string) {
    this.externalSearchPaginator = true;
    this._transactionService
      .listExternalProcedures(body, keyword)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTableProcedures = [];
        } else {
          this.estructTableProcedures[
            this.estructTableProcedures.length - 1
          ].columnIcon = '';
          this.dataTableProcedures = resp.result;
          this.dataTableProcedures = this.dataTableProcedures.map(
            this.removeSpacesFromKeys
          );
          this.amountRows = resp.rowCount;
          this.matDialog.closeAll();
        }
      });
  }

  //Detecta los cambios en la paginación de la tabla.
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.currentPosition = this.pageIndex * this.pageSize;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 0;
    }
    if (this.externalSearchPaginator) {
      let payload: ProcedureBodyModel = {
        startDate: this._utilsService.formatDate(
          this.addDayToDate(this.lastBodyExternalSearch.startDate)
        ),
        endDate: this._utilsService.formatDate(
          this.addDayToDate(this.lastBodyExternalSearch.endDate)
        ),
        from: this.currentPosition,
        pageSize: this.pageSize,
        idBusinessCountry: this.lastBodyExternalSearch.idBusinessCountry,
        idTask: this.lastBodyExternalSearch.idTask,
      };
      this.listExternalProcedures(payload, this.keyword);
    } else {
      let payload: ProcedureBodyModel = {
        startDate: this._utilsService.formatDate(
          this.addDayToDate(this.lastGeneralFilterBody.startDate)
        ),
        endDate: this._utilsService.formatDate(
          this.addDayToDate(this.lastGeneralFilterBody.endDate)
        ),
        from: this.currentPosition,
        idBusinessCountry: this.idBusinessByCountry,
        stateName: this.listSelectedStates,
        idUser: this.idUser,
        pageSize: this.pageSize,
      };
      this.getProceduresList(payload, this.keyword);
    }
  }

  controller(evt: IconEventClickModel) {
    switch (evt.column) {
      case 'Details':
        this.disabledDetailTask = true;
        this.dataDetailTask = evt.value;
        this.seeDetailsTask();
        break;
      case 'Manage':
        if (evt.value.canManage) {
          this._customRouter.navigate([
            `/dashboard/task-tray/new/${evt.value.idProductModule}/${evt.value.Proceso}/${evt.value.Producto}/${evt.value['IddeTarea']}/${evt.value.idTaskState}/${evt.value.idStateModule}`,
          ]);
        } else {
          this._messageService.messageInfo(
            this._translateService.instant(
              'ProceduresViewerSettings.NotAuthorized'
            ),
            this._translateService.instant(
              'ProceduresViewerSettings.AlertDetail'
            )
          );
        }
        break;
      default:
        break;
    }
  }

  addDayToDate(date: string): string{
    if (!date) {
      return '';
    }
    let newDate = new Date(date);
    newDate.setDate(newDate.getDate() + 1);
    return newDate.toISOString().split('T')[0];
  }
}
