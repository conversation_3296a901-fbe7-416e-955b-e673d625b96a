import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { PreviewComponent } from './preview/preview.component';
import { SettingColorsComponent } from './setting-colors/setting-colors.component';
import { ColorsSettingService } from 'src/app/shared/services/colors/colors-setting.service';

@Component({
  selector: 'app-color',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    BreadcrumbComponent,
    TranslateModule,
    SettingColorsComponent,
    PreviewComponent,
    MatIconModule,
    MatButtonModule,
    MatTabsModule
  ],
  templateUrl: './colors.component.html',
  styleUrls: ['./colors.component.scss'],
})
export class ColorComponent implements OnInit, OnDestroy {
  inicio: string = 'Inicio';
  colors: string = 'Colores';
  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.colors, link: '/dashboard/colors' },
  ];
  allColorsAdvisor: any = {};
  allColorsClient: any = {};
  showPreviewAdvisor: boolean = false;
  showPreviewClient: boolean = false;
  actionAdvisor: string = '';
  actionClient: string = '';

  defaultTabIndex: number = 0;
  activeTab: number = 0;

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    private _translateService: TranslateService,
    private _colorsSettingService: ColorsSettingService
  ) { }

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant('Inicio');
      this.colors = this._translateService.instant('Colors');
      this.sections[0].label = this.inicio;
      this.sections[1].label = this.colors;
    });
  }

  getAllColors(event: any, portal: string) {
    if (portal === 'Advisor') {
      this.allColorsAdvisor = event;
    } else {
      this.allColorsClient = event;
    }

    if (event.primaryColor !== '' && event.secondaryColor !== '' && (event.font !== null && event.font !== '')) {
      if (portal === 'Advisor') {
        this.showPreviewAdvisor = true;
      } else {
        this.showPreviewClient = true;
      }

      this._colorsSettingService.setThemeColors(
        event.primaryColor,
        event.secondaryColor,
        event.shadeColor,
        event.tintColor
      );
    }

    if (event.primaryColor === '' || event.secondaryColor === '' && (event.font === null || event.font === '')) {
      if (portal === 'Advisor') {
        this.showPreviewAdvisor = false;
      } else {
        this.showPreviewClient = false;
      }
    }
  }

  getAction(event: string, portal: string) {
    switch (event) {
      case 'save':
      case 'reset':
        this.validEvtPortal(event, portal);
        break;
      default:
        break;
    }
  }

  getActionFinished(event: string, portal: string) {
    this.validEvtPortal('', portal);
  }

  onTabChanged(index: number) {
    this.activeTab = index;
  }

  validEvtPortal(evt: string, portal: string) {
    if (portal === 'Advisor') {
      this.actionAdvisor = evt;
    } else {
      this.actionClient = evt;
    }
  }

  ngOnDestroy(): void { }
}
