import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChooseCountryAndCompanyComponent } from '../../../../../shared/components/choose-country-and-company/choose-country-and-company.component';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { catchError, of, Subscription} from 'rxjs';
import { ResponseGlobalModel } from '../../../../../shared/models/response';
import { RoleService } from '../../../../../shared/services/role/role.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MessageService } from '../../../../../shared/services/message/message.service';
import { MatTabsModule } from '@angular/material/tabs';
import { AllPestanasComponent } from 'src/app/advisor-portal/modules/dashboard/client-portal/all-configuration-portal/pestañas/all-pestanas/all-pestanas.component'
import { AllSeccionesComponent } from 'src/app/advisor-portal/modules/dashboard/client-portal/all-configuration-portal/secciones/all-secciones/all-secciones.component'
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Router } from '@angular/router';
import { ConfigFooterComponent } from 'src/app/advisor-portal/modules/dashboard/client-portal/all-configuration-portal/footer/config-footer/config-footer.component'
@Component({
  selector: 'app-all-configuration-portal',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    MatInputModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    TranslateModule,
    MatTabsModule,
    AllPestanasComponent,
    AllSeccionesComponent,
    ConfigFooterComponent
  ],
  templateUrl: './all-configuration-portal.component.html',
  styleUrls: ['./all-configuration-portal.component.scss']
})
export class AllConfigurationPortalComponent {
  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessByCountry: number = 0;

constructor(
  private _settingService: SettingService,
  private _router: Router,
){
  this.getSettingCountryAndCompanySubscription();
}
  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            setTimeout(() => {
              if (!(Object.keys(response).length === 0)) {
                this.idBusinessByCountry = response.enterprise.pkIIdBusinessByCountry;
              }
            }, 500);
            
          }
        }
      );
  }
}
