import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';

@Component({
  selector: 'app-history-table',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    TranslateModule,
  ],
  templateUrl: './history-table.component.html',
  styleUrls: ['./history-table.component.scss'],
})
export class HistoryTableComponent implements OnInit {
  @Input() estructTable: BodyTableModel[] = [];
  @Input() historyData: any[] = [];
  @Input() title: string = this._translateService.instant(
    'Policy.PolicyMovementHistory'
  );
  @Output() actionTable = new EventEmitter<any>();

  constructor(private _translateService: TranslateService) {}

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.title = this._translateService.instant(
        'Policy.PolicyMovementHistory'
      );
    });
  }
  //Controlador de las acciones configuradas en la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'detailsRisk':
        this.actionTable.emit(event.value);
        break;
      case 'detailsMovement':
        this.actionTable.emit(event.value);
        break;
      default:
        break;
    }
  }
}
