import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';


@Component({
  selector: 'app-formalities-tray-settings',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent],
  template: `
  <div>
    <h2 class="h3">
      <img src="assets/img/layouts/config_ico.svg" alt="" />
      {{ 'ProceduresViewerSettings.Title' | translate }}
    </h2>
  </div>
  <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

  <router-outlet></router-outlet>
`,
  styleUrls: []
})
export class FormalitiesTraySettingsComponent implements OnInit{
  constructor(private _translateService: TranslateService) {}

  inicio: string = this._translateService.instant('Inicio');
  proceduresViewer: string = this._translateService.instant('ProceduresViewerSettings.ProceduresViewer');

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.proceduresViewer, link: '/dashboard/formalities-tray-setting' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant('Inicio');
      this.proceduresViewer= this._translateService.instant('ProceduresViewerSettings.ProceduresViewer');
      this.sections[0].label = this.inicio;
      this.sections[1].label = this.proceduresViewer;
    });
  }

}
