import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ConfigurationModel } from 'src/app/shared/models/OCR';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';

@Component({
  selector: 'app-all-configured-readings',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
  ],
  templateUrl: './all-configured-readings.component.html',
  styleUrl: './all-configured-readings.component.scss',
})
export class AllConfiguredReadingsComponent implements OnInit {
  //Variables relacionadas con la tabla de Lecturas.
  estructReadingsTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('OCR.ReadingName'),
      columnValue: 'name',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'create',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];
  readingsData: ConfigurationModel[] = [];

  constructor(
    private _translateService: TranslateService,
    private _customeRouter: CustomRouterService,
    private _parametersService: ParametersService,
    private _messageService: MessageService
  ) {}

  ngOnInit(): void {
    this.getConfigurationOCR();

    //Traducción de la tabla de Lecturas.
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructReadingsTable[0].columnLabel =
        this._translateService.instant('OCR.ReadingName');
      this.estructReadingsTable[1].columnLabel =
        this._translateService.instant('Modify');
      this.estructReadingsTable[2].columnLabel =
        this._translateService.instant('Delete');
    });
  }

  //Obtiene todas las lecturas registradas en el sistema.
  getConfigurationOCR() {
    this._parametersService
      .getConfigurationOCR()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.readingsData = resp.result;
          }
        }
      });
  }

  //Elimina una Lectura por medio de su PK.
  deleteConfigurationOCR(idConfiguration: number) {
    this._parametersService
      .deleteConfigurationOCR(idConfiguration)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageError(error.error.message);
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.message === 'Dependence') {
            this._messageService.messageInfo(
              this._translateService.instant('OCR.DependenceTitle'),
              this._translateService.instant('OCR.DependenceSubtitle')
            );
          } else {
            this.getConfigurationOCR();
            this._messageService.messageConfirmatio(
              this._translateService.instant('Deleted'),
              '',
              'success',
              this._translateService.instant('Continue')
            );
          }
        }
      });
  }

  //Admisntrador de acciones de la tabla de lecturas.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'modify':
        this._customeRouter.navigate([`dashboard/ocr/edit/${event.value.id}`]);
        break;
      case 'delete':
        this._messageService
          .messageConfirmationAndNegation(
            this._translateService.instant('Delete') + '?',
            '',
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              this.deleteConfigurationOCR(event.value.id);
            }
          });
        break;

      default:
        break;
    }
  }

  //Función que envía a la ruta de creación de lecturas.
  newReadings() {
    this._customeRouter.navigate([`dashboard/ocr/new`]);
  }
}
