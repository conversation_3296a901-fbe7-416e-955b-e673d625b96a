import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { ClientService } from 'src/app/shared/services/client/client.service';

@Component({
  selector: 'app-clients',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/clients.svg" alt="" />
        {{ 'Clients.Title' | translate }}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class ClientsComponent implements OnInit, OnDestroy {
  isClientDetailProduct: boolean = false;
  isClientDetail: boolean = false;
  isClientDetailQuote: boolean = false;

  idClient: number = 0;

  inicio: string = this._translateService.instant('Inicio');
  clientes: string = this._translateService.instant('Clients.Title');
  clientDetail: string = this._translateService.instant('Clients.ClientDetail');
  detailClient: string = this._translateService.instant('DetailClient');
  clientDetailProduct: string = this._translateService.instant(
    'Client.ClientDetailProduct'
  );
  clientDetailQuote: string = this._translateService.instant(
    'Client.ClientDetailQuote'
  );

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.clientes, link: '/dashboard/clients' },
  ];

  clienteSubs?: Subscription;

  constructor(
    private _translateService: TranslateService,
    private _clientService: ClientService,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Initializes the component by subscribing to client service updates and setting up event listeners.
   *
   * This method handles the setup of the component when it is initialized, including subscribing to
   * client service changes and language change events. It updates the section labels based on the
   * client status and the current language setting.
   */
  ngOnInit(): void {
    this.subscribeToClientService();
    this.subscribeToLanguageChanges();
  }

  /**
   * Subscribes to client service updates to determine the client details and update section labels.
   *
   * This method subscribes to the client service to retrieve client details and updates the section
   * labels accordingly. It also handles the detection of changes to ensure that the UI reflects the
   * latest client information.
   */
  private subscribeToClientService(): void {
    this.clienteSubs = this._clientService
      .getIsClient()
      .subscribe((isCliente) => {
        this.isClientDetailProduct = isCliente.isClientDetailProduyct;
        this.isClientDetail = isCliente.isDetailClient;
        this.isClientDetailQuote = isCliente.isClientDetailQuote ?? false;
        this.idClient = isCliente.idClient;
        this.setupSections();
        this.cdr.detectChanges(); // Forzar la detección de cambios
      });
  }

  /**
   * Subscribes to language change events to update section labels dynamically.
   *
   * This method listens for language change events and updates the section labels accordingly. It
   * ensures that the UI is updated with the correct labels based on the selected language.
   */
  private subscribeToLanguageChanges(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.updateLabels();
      this.cdr.detectChanges(); // Forzar la detección de cambios
    });
  }

  /**
   * Sets up the section labels based on the client status.
   *
   * This method configures the section labels for navigation based on the current client status.
   * It updates the `sections` array to reflect whether the user is viewing client details,
   * product details, or quotes.
   */
  private setupSections(): void {
    if (this.isClientDetailProduct) {
      this.sections = [
        { label: this.inicio, link: '/dashboard' },
        { label: this.clientes, link: '/dashboard/clients' },
        {
          label: this.detailClient,
          link: '/dashboard/clients/detail/' + this.idClient,
        },
        { label: this.clientDetailProduct, link: '' },
      ];
    } else if (this.isClientDetail) {
      this.sections = [
        { label: this.inicio, link: '/dashboard' },
        { label: this.clientes, link: '/dashboard/clients' },
        { label: this.detailClient, link: '' },
      ];
    } else if (this.isClientDetailQuote) {
      this.sections = [
        { label: this.inicio, link: '/dashboard' },
        { label: this.clientes, link: '/dashboard/clients' },
        {
          label: this.detailClient,
          link: '/dashboard/clients/detail/' + this.idClient,
        },
        { label: this.clientDetailQuote, link: '' },
      ];
    } else {
      this.sections = [
        { label: this.inicio, link: '/dashboard' },
        { label: this.clientes, link: '/dashboard/clients' },
      ];
    }
  }

  /**
   * Updates the section labels based on the current language setting.
   *
   * This method refreshes the labels in the `sections` array whenever the language changes.
   * It ensures that the section labels are displayed in the selected language.
   */
  private updateLabels(): void {
    this.inicio = this._translateService.instant('Inicio');
    this.clientes = this._translateService.instant('Clients.Title');
    this.detailClient = this._translateService.instant('DetailClient');
    this.clientDetailProduct = this._translateService.instant(
      'Client.ClientDetailProduct'
    );
    this.clientDetailQuote = this._translateService.instant(
      'Client.ClientDetailQuote'
    );

    this.setupSections(); // Re-configure sections with updated labels
  }

  /**
   * Cleanup logic when the component is destroyed.
   *
   */
  ngOnDestroy(): void {
    this.clienteSubs?.unsubscribe();
  }
}
