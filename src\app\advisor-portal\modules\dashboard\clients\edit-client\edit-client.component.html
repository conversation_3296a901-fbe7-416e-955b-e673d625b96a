<div class="modal-body ">

  <ng-container
    *ngIf="this.client.vTypePerson === 1; then thenTemplate; else elseTemplate"
  >
  </ng-container>

  <ng-template #thenTemplate>
    <form [formGroup]="formClient">

      <div class="row">
        <!-- Abierto - Cerrado -->
        <mat-radio-group formControlName="bActive" class="mb-2">
          <mat-radio-button class="example-margin" [value]="true">{{'Abierto'}}</mat-radio-button>
          <mat-radio-button class="example-margin" [value]="false">{{'Cerrado'}}</mat-radio-button>
        </mat-radio-group>
      </div>

      <div class="row">
        <!-- tipo de documento -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Tipo de documento" }} </mat-label>
          <mat-select formControlName="iIdDocumentType">
            <mat-option
              *ngFor="let document of documentsTypesFiltered"
              [value]="document.id"
            >
              {{ document.name }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'iIdDocumentType',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- numero de documento -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Número de documento" }} </mat-label>
          <input
            matInput
            placeholder="Número de documento"
            formControlName="vDocumentNumber"
            required
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'vDocumentNumber',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>

          <mat-error
            *ngIf="formClient.get('vDocumentNumber')?.hasError('pattern')"
          >
            {{ "Clients.InvalidNumber" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row">
        <!-- primer nombre -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Primer nombre" }} </mat-label>
          <input
            matInput
            [placeholder]="'Primer nombre'"
            formControlName="vFirstName"
            required
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'vFirstName',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- segundo nombre -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Segundo nombre" }} </mat-label>
          <input
            matInput
            [placeholder]="'Segundo nombre'"
            formControlName="vSecondName"
            type="text"
          />
        </mat-form-field>
      </div>

      <div class="row">
        <!-- primer apellido -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Primer apellido" }} </mat-label>
          <input
            matInput
            [placeholder]="'Primer segundo'"
            formControlName="vSurname"
            required
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formClient,
                'vSurname',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- segundo nombre -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Segundo apellido" }} </mat-label>
          <input
            matInput
            [placeholder]="'Segundo apellido'"
            formControlName="vSecondSurname"
            type="text"
          />
        </mat-form-field>
      </div>

      <div class="row">
        <!-- email -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Correo electrónico" }} </mat-label>
          <input
            matInput
            [placeholder]="'Correo electrónico'"
            formControlName="vEmailUser"
            type="email"
          />

          <mat-error *ngIf="formClient.get('vEmailUser')?.hasError('pattern')">
            {{ 'Clients.InvalidEmail' | translate }}
          </mat-error>
        </mat-form-field>

        <!-- celular -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Celular" }} </mat-label>
          <input
            matInput
            [placeholder]="'Celular'"
            formControlName="vCellPhone"
            type="text"
          />

          <mat-error *ngIf="formClient.get('vCellPhone')?.hasError('pattern')">
            {{ "Clients.InvalidNumber" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

    </form>
  </ng-template>

  <ng-template #elseTemplate>


    <form [formGroup]="formBusiness">
      <div class="row">
        <!-- Abierto - Cerrado -->
        <mat-radio-group formControlName="bActive" class="mb-2">
          <mat-radio-button class="example-margin" [value]="true">{{'Abierto'}}</mat-radio-button>
          <mat-radio-button class="example-margin" [value]="false">{{'Cerrado'}}</mat-radio-button>
        </mat-radio-group>
      </div>

      <div class="mb-3">
        <h3>{{ "Empresa" }}</h3>
      </div>

      <div class="row">
        <!-- tipo de documento -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Clients.TypeDocument" | translate }} </mat-label>
          <mat-select formControlName="iIdDocumentType">
            <mat-option
              *ngFor="let document of documentsTypesFiltered"
              [value]="document.id"
            >
              {{ document.name }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'iIdDocumentType',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- numero de documento -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ "Clients.FormSearchDoc" | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.FormSearchDoc' | translate"
            formControlName="vDocumentNumber"
            required
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'vDocumentNumber',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>

          <mat-error
            *ngIf="formBusiness.get('vDocumentNumber')?.hasError('pattern')"
          >
            {{ "Clients.InvalidNumber" | translate }}
          </mat-error>
        </mat-form-field>


      </div>

      <div class="row">
        <!-- Dígito de verificación -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.VerificationDigit' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.VerificationDigit' | translate"
            formControlName="vVerificationDigit"
            type="text"
          />

          <mat-error
            *ngIf="formBusiness.get('vVerificationDigit')?.hasError('pattern')"
          >
            {{ "Clients.InvalidNumber" | translate }}
          </mat-error>
        </mat-form-field>

        <!-- Razón Social -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{'Clients.CompanyName' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.CompanyName' | translate"
            formControlName="vBusinessName"
            type="text"
          />
          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'vBusinessName',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row">
        <!-- email -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{'Clients.EmailClient' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.EmailClient' | translate"
            formControlName="vEmailUser"
            type="email"
          />

          <mat-error *ngIf="formBusiness.get('vEmailUser')?.hasError('pattern')">
            {{ 'Clients.InvalidEmail' | translate }}
          </mat-error>

          <!-- <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'VEmailUser',
                'invalidEmail'
              )
            "
          >
            El correo electrónico no es válido.
          </mat-error> -->
        </mat-form-field>

        <!-- celular -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.CellPhone' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.CellPhone' | translate"
            formControlName="vCellPhone"
            type="text"
          />

          <mat-error
            *ngIf="formBusiness.get('vCellPhone')?.hasError('pattern')"
          >
            {{ 'Clients.InvalidNumber' | translate }}
          </mat-error>

          <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'vCellPhone',
                'required'
              )
            "
          >
            {{ "ThisFieldIsRequired" | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="mb-3">
        <h3>{{ 'Clients.Contact' | translate }}</h3>
      </div>

      <div class="row">
        <!-- tipo de documento -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.TypeDocument' | translate }} </mat-label>
          <mat-select formControlName="iIdDocumentTypeContact">
            <mat-option
              *ngFor="let document of documentsTypes"
              [value]="document.id"
            >
              {{ document.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- numero de documento -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.FormSearchDoc' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.FormSearchDoc' | translate"
            formControlName="vDocumentNumberContact"
            type="text"
          />

          <mat-error
            *ngIf="
              formBusiness.get('vDocumentNumberContact')?.hasError('pattern')
            "
          >
            {{ 'Clients.InvalidNumber' | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row">

        <!-- primer nombre -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.FirstName' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.FirstName' | translate "
            formControlName="vFirstNameContact"
            type="text"
          />
        </mat-form-field>

        <!-- segundo nombre -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.SecondName' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.SecondName' | translate"
            formControlName="vSecondNameContact"
            type="text"
          />
        </mat-form-field>

      </div>

      <div class="row">

        <!-- primer apellido -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.Surname' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.Surname' | translate"
            formControlName="vSurnameContact"
            type="text"
          />
        </mat-form-field>

        <!-- segundo nombre -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.SecondSurname' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.SecondSurname' | translate"
            formControlName="vSecondSurnameContact"
            type="text"
          />
        </mat-form-field>

      </div>

      <div class="row">


        <!-- email -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.EmailClient' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.EmailClient' | translate"
            formControlName="vEmailUserContact"
            type="email"
          />

          <mat-error *ngIf="formBusiness.get('vEmailUserContact')?.hasError('pattern')">
           {{  'Clients.InvalidEmail' | translate  }}
          </mat-error>

          <!-- <mat-error
            *ngIf="
              _utilsService.isControlHasError(
                formBusiness,
                'VEmailUserContact',
                'invalidEmail'
              )
            "
          >
            El correo electrónico no es válido.
          </mat-error> -->
        </mat-form-field>

        <!-- celular -->
        <mat-form-field appearance="outline" class="w-50 mb-2">
          <mat-label> {{ 'Clients.CellPhone' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.CellPhone' | translate"
            formControlName="vCellPhoneContact"
            type="text"
          />

          <mat-error
            *ngIf="formBusiness.get('vCellPhoneContact')?.hasError('pattern')"
          >
            {{ 'Clients.InvalidNumber' | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="row">

        <!-- role -->
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label> {{ 'Clients.Role' | translate }} </mat-label>
          <input
            matInput
            [placeholder]="'Clients.Role' | translate"
            formControlName="vRoleContact"
            type="text"
          />


        </mat-form-field>
      </div>
    </form>
  </ng-template>


</div>

<!-- save button -->
<div class="d-flex buttonAlign alignSubmit">
  <button
    type="button"
    [disabled]="client.vTypePerson == 1 ? !formClient.valid : !formBusiness.valid"
    (click)="updateClient()"
    mat-raised-button
    color="primary"
  >
    {{ 'save' | translate }}
    <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
  </button>
</div>
