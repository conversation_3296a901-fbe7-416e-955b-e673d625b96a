import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy, Output, EventEmitter, Input } from '@angular/core';
import { ReactiveFormsModule, FormsModule, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription, catchError, of } from 'rxjs';
import { DragulaService, DragulaModule } from 'ng2-dragula';

import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTabsModule } from '@angular/material/tabs';

import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { FieldComponent } from 'src/app/shared/components/field-generator/field.component';



import { MessageService } from 'src/app/shared/services/message/message.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-preview-form',
    standalone: true,
    imports: [
        CommonModule,
        MatInputModule,
        MatFormFieldModule,
        MatSlideToggleModule,
        ReactiveFormsModule,
        FormsModule,
        MatButtonModule,
        MatIconModule,
        MatCheckboxModule,
        MatTabsModule,
        FieldComponent,
        DragulaModule,
        TranslateModule
    ],
    templateUrl: './preview-form.component.html',
    styleUrls: ['./preview-form.component.scss']

})
export class PreviewFormComponent implements OnInit, OnDestroy {


    @Input() idProduct: number = 0;
    fieldSubs?: Subscription;
    private readonly group: string = 'bag-one';

    //Se agrega esta variable para evitar error por falta de instanciar un form en el componente field.
    formBuild: FormGroup = new FormGroup([]);

    amountColumns: number = 1;
    form: any;

    constructor(
        private dragulaService: DragulaService,
        public router: Router,
        public utilsSvc: UtilsService,
        public _fieldSvc: FieldService,
        private _msgSvc: MessageService,
        private _translateService: TranslateService
    ) {
        if (!this.dragulaService.find(this.group)) {
            this.dragulaService.createGroup(this.group, {
                direction: 'vertical'
            });
        }
    }


    ngOnInit(): void {
        this.getCompleteForm(this.idProduct);
    }

    ngOnDestroy(): void {

    }


    getCompleteForm(idProduct: number) {
        this.fieldSubs = this._fieldSvc.getCompleteForm(idProduct, false).pipe(
            catchError((error) => {
                this._msgSvc.messageWaring(this._translateService.instant('Warning'), error.error.message)
                return of([]);
            })
        ).subscribe((resp: ResponseGlobalModel | never[]) => {
            if (Array.isArray(resp)) {
                console.log("El tipo de datos devueltos es un array vacío.");
            } else {
                if (resp.error) {
                    this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
                }
                else {
                    this.form = resp.result[0]
                }
            }
        });

    }
}
