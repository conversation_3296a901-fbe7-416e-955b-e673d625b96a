import { CommonModule } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { PageEvent } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Router } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  AnsTableModel,
  FilterCustomerAnsModel,
  ReportAnsFileModel,
  ReportAnsTableModel,
  StateSelectModel,
} from 'src/app/shared/models/ans';
import { ProcessModel } from 'src/app/shared/models/menu';
import { StageModel } from 'src/app/shared/models/module';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TaskTrayConfigProductApiRoleModel } from 'src/app/shared/models/task-tray-config';
import { UserListModel } from 'src/app/shared/models/user';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { WorkFlowService } from 'src/app/shared/services/work-flow/work-flow.service';

@Component({
  selector: 'app-report',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSelectModule,
    MatSlideToggleModule,
    TranslateModule,
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatInputModule,
    TableComponent,
    Modal2Component,
    MatCheckboxModule,
  ],
  templateUrl: './report.component.html',
  styleUrls: ['./report.component.scss'],
})
export class ReportComponent implements OnInit {
  //Variables relacionadas con la tabla.
  dataTableReport: ReportAnsTableModel[] = [];
  dataTableAns: AnsTableModel[] = [];
  ansIdReporting: number[] = [];
  estructTableReport: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Reports.DateCreation'
      ),
      columnValue: 'dDateCreation',
      functionValue: (item: any) =>
        this._utilsService.formatDate(item.dDateCreation, 'DD-MM-YYYY'),
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Reports.StartDateLabel'
      ),
      columnValue: 'dStartDate',
      functionValue: (item: any) =>
        this._utilsService.formatDate(item.dStartDate, 'DD-MM-YYYY'),
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Reports.EndDateLabel'
      ),
      columnValue: 'dEndDate',
      functionValue: (item: any) =>
        this._utilsService.formatDate(item.dEndDate, 'DD-MM-YYYY'),
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Reports.Download'
      ),
      columnValue: 'download',
      columnIcon: 'get_app',
    },
  ];
  estructTableAns: BodyTableModel[] = [
    {
      columnLabel: '',
      columnValue: '1',
      check: true,
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Process'
      ),
      columnValue: 'nameProcess',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Product'
      ),
      columnValue: 'nameProduct',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.Stage'
      ),
      columnValue: 'nameStage',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.TaskStatus'
      ),
      columnValue: 'nameState',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.MaximumResponseTime'
      ),
      columnValue: 'vUnitMeasurementInstructor',
    },
    {
      columnLabel: this._translateService.instant(
        'AnsConfiguration.Table.AnsStatus'
      ),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsService.changeStatusValue(item),
    },
  ];

  //Variables para el paginado de la tabla de reportes previos.
  amountRowsReport: number = 0;
  pageIndexReport: number = 0;
  pageSizeReport: number = 10;

  //Variables para el paginado de la tabla de ANS.
  amountRowsAns: number = 0;
  pageIndexAns: number = 0;
  pageSizeAns: number = 10;
  orderAns: string = 'asc';

  //Variables relacionadas con los modales.
  @ViewChild('filterModal') filterModal?: TemplateRef<any>;
  @ViewChild('selectAns') selectAns?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;
  modalOpenRerefence:string = '';

  //Variables relacionadas con el buscador.
  keyword: string | any = null;

  //Variables relacionadas con el formulario.
  formReportAns: FormGroup = this._fb.group({});
  formFilterAns: FormGroup = this._fb.group({});
  validFormAddAns: boolean = false;
  idBusinessByCountry: number = 0;
  showBtnEdit: boolean = false;
  validateFormGeneralConditions: boolean = false;
  applyFilterModal: boolean = false;
  modifyAll: boolean = false;
  disableANSselect: boolean = false;

  //Variables para los select.
  processes: ProcessModel[] = [];
  products: TaskTrayConfigProductApiRoleModel[] = [];
  stage: StageModel[] = [];
  state: StateSelectModel[] = [];
  users: UserListModel[] = [];

  //Variables relacionadas con la subscription
  private _settingCountryAndCompanySubscription?: Subscription;
  formValidBusinessByCountry: boolean = false;

  //Variable usuario
  idUser: any;

  constructor(
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    private _workFlowService: WorkFlowService,
    private _moduleService: ModuleService,
    private _roleService: RoleService,
    private _utilsService: UtilsService,
    private _parametersService: ParametersService,
    private _transactionService: TransactionService,
    private _fileService: FileService,
    public modalDialog: MatDialog,
    private _settingService: SettingService,
    private _router: Router,
  ) {}

  ngOnInit(): void {
    this.getDataBusinnesByCountry();
    this.getIdUserSession();
    this.initFormReport();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table ANS
      this.estructTableAns[0].columnLabel = '';
      this.estructTableAns[1].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Process'
      );
      this.estructTableAns[2].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Product'
      );
      this.estructTableAns[3].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.Stage'
      );
      this.estructTableAns[4].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.TaskStatus'
      );
      this.estructTableAns[5].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.MaximumResponseTime'
      );
      this.estructTableAns[6].columnLabel = this._translateService.instant(
        'AnsConfiguration.Table.AnsStatus'
      );

      //traduccion data table Reportes Previos.
      this.estructTableReport[0].columnLabel = this._translateService.instant(
        'AnsConfiguration.Reports.DateCreation'
      );
      this.estructTableReport[1].columnLabel = this._translateService.instant(
        'AnsConfiguration.Reports.StartDateLabel'
      );
      this.estructTableReport[2].columnLabel = this._translateService.instant(
        'AnsConfiguration.Reports.EndDateLabel'
      );
      this.estructTableReport[2].columnLabel = this._translateService.instant(
        'AnsConfiguration.Reports.Download'
      );
    });
  }

  //Evento que obtiene los valores del select empresa país.
  getValueForm(event: boolean) {
    this.formValidBusinessByCountry = event;
  }

  //Función que obtiene la información de empresa país cada vezs que esta cambia.
  getDataBusinnesByCountry() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessByCountry =
                response.enterprise.pkIIdBusinessByCountry;
                this.disableANSselect = this.formValidBusinessByCountry;
                this.formReportAns
                  .get('idBusinessCountry')
                  ?.setValue(response.enterprise.pkIIdBusinessByCountry);
                this.dataTableReport = [];
                this.getANSReportHistory(response.enterprise.pkIIdBusinessByCountry, 0, 10);
            }
          }
        }
      );
  }

  //Obtiene el usuario en sesion
  async getIdUserSession() {
    let userIdSession = await this._settingService.getDataSettingInit();
    if (userIdSession) {
      this.idUser = userIdSession.idUser;
    }
  }

  //Función que inicializa el formulario.
  initFormReport() {
    this.formReportAns = this._fb.group({
      idBusinessCountry: [null],
      idUser: [null],
      startDate: [null],
      endDate: [null],
      idGeneralANS: [null],
      isSelectedAll: [false],
      filterByUser: [{ value: '', disabled: true } ],
    });

    this.formReportAns.get('filterByUser')?.valueChanges.subscribe({
      next: (data) => {
        data
          ? this.getTaskTrayUserList()
          : this.formReportAns.get('idUser')?.setValue(null);
      },
    });

    this.formReportAns.get('startDate')?.valueChanges.subscribe({
      next:(data)=>{
        if(data && this.formReportAns.get('endDate')?.value){
          this.formReportAns.get('filterByUser')?.enable();
        } else {
          this.formReportAns.get('filterByUser')?.disable();
        }
      }
    })

    this.formReportAns.get('endDate')?.valueChanges.subscribe({
      next:(data)=>{
        if(data && this.formReportAns.get('startDate')?.value){
          this.formReportAns.get('filterByUser')?.enable();
        } else {
          this.formReportAns.get('filterByUser')?.disable();
        }
      }
    })
  }

  //Función que inicializa el formulario.
  initFormFilter() {
    this.formFilterAns = this._fb.group({
      active: [false],
      inactive: [false],
      idProcess: [0],
      idProduct: [0],
      idStage: [0],
      idState: [0],
    });

    this.formFilterAns.get('idProcess')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.formFilterAns.get('idProduct')?.setValue(null);
          this.formFilterAns.get('idStage')?.setValue(null);
          this.formFilterAns.get('idState')?.setValue(null);
          this.products = [];
          this.stage = [];
          this.state = [];

          this.getProductsList();
        }
      },
    });

    this.formFilterAns.get('idProduct')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.formFilterAns.get('idStage')?.setValue(null);
          this.formFilterAns.get('idState')?.setValue(null);
          this.stage = [];
          this.state = [];

          this.getStageByIdProductModule();
        }
      },
    });

    this.formFilterAns.get('idStage')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          this.formFilterAns.get('idState')?.setValue(null);
          this.state = [];

          this.getStageByStateList();
        }
      },
    });
  }

  //Obtiene el valor de validez del formulario de reportes.
  get formReportValid(): boolean {
    if (
      (this.formReportAns.get('startDate')?.value &&
        this.formReportAns.get('endDate')?.value) ||
      this.ansIdReporting.length > 0
    ) {
      return true;
    } else {
      return false;
    }
  }

  //Obtiene el valor del check del formulario con control filterByUser.
  get filterByUser() {
    return this.formReportAns.get('filterByUser')?.value;
  }

  //Obtiene todos los usuarios disponibles por empresa país.
  getANSReportHistory(
    idBusinessByCountry: number,
    page: number,
    pageSize: number
  ) {
    this._transactionService
      .getANSReportHistory(idBusinessByCountry, page + 1, pageSize)
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTableReport = [];
        } else {
          this.dataTableReport = resp.result;
          this.amountRowsReport = resp.rowCount;
        }
      });
  }

  //Obtiene lista de usuarios disponibles para filtrar una ANS.
  getTaskTrayUserList() {
    this._roleService
      .getTaskTrayUserList(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.users = resp.result;
        }
      });
  }

  //Obtiene los productos registrados por proceso y idBusinessByCountry.
  getProductsList() {
    this._roleService
      .getProductsByIdProcess(
        this.formFilterAns.get('idProcess')?.value,
        this.idBusinessByCountry
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant('TaskTray.Messages.NoProducts')
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.products = [];
          this.formFilterAns.get('idProduct')?.setValue(null);
        } else {
          if (resp.result.length === 1) {
            this.formFilterAns.get('idProduct')?.disable();
            this.formFilterAns
              .get('idProduct')
              ?.setValue(resp.result[0].pkIIdProductModule);
          } else {
            this.formFilterAns.get('idProduct')?.enable();
            this.products = resp.result;
          }
        }
      });
  }

  //obtiene la lista de etapas asignados a un producto.
  getStageByIdProductModule() {
    if (this.formFilterAns.get('idProduct')?.value > 0) {
      this._moduleService
        .getStageByIdProductModule(this.formFilterAns.get('idProduct')?.value)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            this.formFilterAns.get('idStage')?.setValue(null);
            this.state = [];
          } else {
            this.stage = resp.result;
          }
        });
    }
  }

  //Obtiene la lista de estados asociados a una etapa.
  getStageByStateList() {
    this._moduleService
      .getStageByStateById(this.formFilterAns.get('idStage')?.value)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.state = [];
        } else {
          this.state = resp.result;
        }
      });
  }

  //Obtiene todos los procesos.
  getAllProcess() {
    this._parametersService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.processes = resp.result;
        }
      });
  }

  //Controlador para las acciones de la tabla.
  controllerReport(event: IconEventClickModel) {
    switch (event.column) {
      case 'download':
        this.downdloadIndividualReport(event.value.fkIIdUploadFile);
        break;
      default:
        break;
    }
  }

  //Controlador para las acciones de la tabla.
  controllerAns(event: IconEventClickModel) {
    if (event.value === 'ALL') {
      //Limpiamos el array y mandamos la llave isSelectedAll en true.
      this.formReportAns.get('isSelectedAll')?.setValue(true);
      this.dataTableAns.forEach((element: AnsTableModel) => {
        this.addOrRemoveAns(element.pkIIdGeneralAns, true);
      });
    } else if (event.value === 'NONE') {
      this.formReportAns.get('isSelectedAll')?.setValue(false);
      this.ansIdReporting = [];
    } else if (event.value.pkIIdGeneralAns) {
      this.addOrRemoveAns(event.value.pkIIdGeneralAns);
      this.formReportAns.get('isSelectedAll')?.setValue(false);
    }
  }

  //Permite descargar un archivo en formato excel, de forma individual en la opción de descargar de la tabla de historico de reporte.
  downdloadIndividualReport(idUploadFile: number) {
    this._fileService
      .getFileDownloadAnsHistory(idUploadFile)
      .pipe(
        catchError((error) => {
          if (!error.ok) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant(
                'AnsConfiguration.Reports.MessageReportFailed'
              )
            );
          }
          return of(null);
        })
      )
      .subscribe((resp) => {
        if (resp) {
          const blob = new Blob([resp], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;',
          });
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = 'reporte-de-ANS.xlsx';
          link.click();
          window.URL.revokeObjectURL(downloadUrl);
        }
      });
  }

  //Descarga el reporte general con filtros de ANS.
  downloadANSReport() {
    let idGeneralANS: number[] = [];
    if (this.formReportAns.get('isSelectedAll')?.value) {
      this.formReportAns.get('idGeneralANS')?.setValue(null);
    } else {
      idGeneralANS = this.formReportAns.get('idGeneralANS')?.value;
    }
    let payload: ReportAnsFileModel = {
      idBusinessCountry: this.idBusinessByCountry,
      idUser: this.formReportAns.get('idUser')?.value,
      startDate: this._utilsService.formatDate(
        this.formReportAns.get('startDate')?.value,
        'YYYY-MM-DD'
      ),
      endDate: this._utilsService.formatDate(
        this.formReportAns.get('endDate')?.value,
        'YYYY-MM-DD'
      ),
      idGeneralANS: idGeneralANS,
      isSelectedAll: this.formReportAns.get('isSelectedAll')?.value,
      idRequestingUser: this.idUser
    };
    this._fileService
      .downloadANSReport(payload)
      .pipe(
        catchError((error) => {
          if(error.status == 404){
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant(
              'AnsConfiguration.Reports.MessageReportNotFound'
            ));
          }
          else if (!error.ok) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant(
                'AnsConfiguration.Reports.MessageReportFailed'
              )
            );
          }
          return of(null);
        })
      )
      .subscribe((resp) => {
        if (resp) {
          const blob = new Blob([resp], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;',
          });
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = 'reporte-de-ANS.xlsx';
          link.click();
          window.URL.revokeObjectURL(downloadUrl);
          this.formReportAns.get('idGeneralANS')?.setValue(null);
          this.formReportAns.get('isSelectedAll')?.setValue(false);
          this.getANSReportHistory(this.idBusinessByCountry, 0, 10);
        }
      });
  }

  //Obtiene los eventos de paginación de la tabla.
  onPageChangeReport(event: PageEvent) {
    this.pageIndexReport = event.pageIndex;
    this.pageSizeReport = event.pageSize;
    const adjustedPageIndex = this.pageIndexReport + 1;
    this.getANSReportHistory(
      this.idBusinessByCountry,
      adjustedPageIndex - 1,
      this.pageSizeReport
    );
  }

  //Obtiene los eventos de paginación de la tabla.
  onPageChangeAns(event: PageEvent) {
    this.pageIndexAns = event.pageIndex + 1;
    this.pageIndexAns = this.pageIndexAns - 1;
    this.pageSizeAns = event.pageSize;
    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndexAns < 0) {
      this.pageIndexAns = 1;
    }
    if (this.applyFilterModal) {
      const payload: FilterCustomerAnsModel = {
        idBusinessCountry: this.idBusinessByCountry,
        idProcess: this.formFilterAns.get('idProcess')?.value,
        idProduct: this.formFilterAns.get('idProduct')?.value,
        idStage: this.formFilterAns.get('idStage')?.value,
        idState: this.formFilterAns.get('idState')?.value,
        order: this.orderAns,
        page: this.pageIndexAns,
        pageSize: this.pageSizeAns,
        bActive: this.getAnsStatus(
          this.formFilterAns.get('active')?.value,
          this.formFilterAns.get('inactive')?.value
        ),
        keyword: this.keyword,
      };
      this.filterGeneralANSData(payload);
    } else {
      this.getGeneralANSByIdBusineesCountry(
        this.idBusinessByCountry,
        this.pageIndexAns + 1
      );
    }
  }

  //Función que añade ans al filtro del reporte
  addAns() {
    this.formReportAns.get('idGeneralANS')?.setValue(this.ansIdReporting);
    this.currentModal?.close();
    this.modalDialog.closeAll()
  }

  //Función que ordena la tabla de ANS.
  orderTable(order: number) {
    switch (order) {
      case 0:
        this.orderAns = 'asc';
        this.pageIndexAns = 0;
        this.pageSizeAns = 10;
        if (this.applyFilterModal) {
          const payload: FilterCustomerAnsModel = {
            idBusinessCountry: this.idBusinessByCountry,
            idProcess: this.formFilterAns.get('idProcess')?.value,
            idProduct: this.formFilterAns.get('idProduct')?.value,
            idStage: this.formFilterAns.get('idStage')?.value,
            idState: this.formFilterAns.get('idState')?.value,
            order: this.orderAns,
            page: 0,
            pageSize: 10,
            bActive: this.getAnsStatus(
              this.formFilterAns.get('active')?.value,
              this.formFilterAns.get('inactive')?.value
            ),
            keyword: this.keyword,
          };
          this.filterGeneralANSData(payload);
        } else {
          this.getGeneralANSByIdBusineesCountry(
            this.idBusinessByCountry,
            this.pageIndexAns
          );
        }

        break;
      case 1:
        this.orderAns = 'desc';
        this.pageIndexAns = 0;
        this.pageSizeAns = 10;
        if (this.applyFilterModal) {
          const payload: FilterCustomerAnsModel = {
            idBusinessCountry: this.idBusinessByCountry,
            idProcess: this.formFilterAns.get('idProcess')?.value,
            idProduct: this.formFilterAns.get('idProduct')?.value,
            idStage: this.formFilterAns.get('idStage')?.value,
            idState: this.formFilterAns.get('idState')?.value,
            order: this.orderAns,
            page: 0,
            pageSize: 10,
            bActive: this.getAnsStatus(
              this.formFilterAns.get('active')?.value,
              this.formFilterAns.get('inactive')?.value
            ),
            keyword: this.keyword,
          };
          this.filterGeneralANSData(payload);
        } else {
          this.getGeneralANSByIdBusineesCountry(
            this.idBusinessByCountry,
            this.pageIndexAns
          );
        }
        break;
      default:
        break;
    }
  }

  // Función que abre un modal, dependiendo el selecctor indicado.
  openModal(component: string) {
    let sizeConfiguration = {
      disableClose: false,
      width: '',
    };
    let modal: TemplateRef<any>;
    switch (component) {
      case 'selectAns':
        this.modalOpenRerefence = 'selectAns';
        modal = this.selectAns!;
        sizeConfiguration.width = '90vw';
        this.getGeneralANSByIdBusineesCountry(this.idBusinessByCountry, 1);
        this.formReportAns.get('idGeneralANS')?.setValue(null);
        this.ansIdReporting = [];
        break;
      case 'filterModal':
        this.modalOpenRerefence = 'filterModal';
        modal = this.filterModal!;
        this.initFormFilter();
        this.getAllProcess();
        this.applyFilterModal = true;
        sizeConfiguration.width = '720px';
        break;
      default:
        return;
    }
    //Abre el modal y guarda la referencia en la variable currentModal.
    this.currentModal = this.modalDialog.open(modal, sizeConfiguration);
  }

  //Evento que detecta cuándo se ha cerrado un modal.
  closeModal(event: boolean) {
    if (this.currentModal) {
      this.currentModal.close();
      this.currentModal = null; // Limpia la referencia después de cerrar el modal
    }
  }

  //Limpia los filtros del modal filtrar.
  resetSearch() {
    this.formFilterAns.reset();
    this.applyFilterModal = false;
    this.applyFilter();
  }

  //Función que aplica el los filtros a la tabla de ANS.
  applyFilter() {
    const payload: FilterCustomerAnsModel = {
      idBusinessCountry: this.idBusinessByCountry,
      idProcess: this.formFilterAns.get('idProcess')?.value,
      idProduct: this.formFilterAns.get('idProduct')?.value,
      idStage: this.formFilterAns.get('idStage')?.value,
      idState: this.formFilterAns.get('idState')?.value,
      order: this.orderAns,
      page: 0,
      pageSize: 10,
      bActive: this.getAnsStatus(
        this.formFilterAns.get('active')?.value,
        this.formFilterAns.get('inactive')?.value
      ),
      keyword: this.keyword,
    };
    this.filterGeneralANSData(payload);
  }

  //Detecta los cambios en el input de filtrar por.
  onInputChange(value: string) {
    if (value === '') {
      this.getGeneralANSByIdBusineesCountry(this.idBusinessByCountry, 1);
    }
  }

  //Función que se ejecuta al rpesionar enter o dar click en el icono de buscar.
  searchAns() {
    const payload: FilterCustomerAnsModel = {
      idBusinessCountry: this.idBusinessByCountry,
      idProcess: this.formFilterAns.get('idProcess')?.value,
      idProduct: this.formFilterAns.get('idProduct')?.value,
      idStage: this.formFilterAns.get('idStage')?.value,
      idState: this.formFilterAns.get('idState')?.value,
      order: this.orderAns,
      page: 0,
      pageSize: 10,
      bActive: this.getAnsStatus(
        this.formFilterAns.get('active')?.value,
        this.formFilterAns.get('inactive')?.value
      ),
      keyword: this.keyword,
    };
    this.filterGeneralANSData(payload);
  }

  //Obtiene la lista de ANS por medio del filtro especifico aplicado.
  filterGeneralANSData(model: FilterCustomerAnsModel) {
    this._workFlowService
      .filterGeneralANSData(model)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this.dataTableAns = [];
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTableAns = [];
        } else {
          this.dataTableAns = [];
          this.dataTableAns = resp.result;
          if(this.modalOpenRerefence === 'filterModal')
            this.currentModal?.close();
        }
      });
  }

  //Obtiene las ANS paginadas, filtrando por idBusinessByCountry.
  getGeneralANSByIdBusineesCountry(
    idBusinessByCountry: number,
    pageIndex: number
  ) {
    this._workFlowService
      .getGeneralANSByIdBusineesCountry(
        idBusinessByCountry,
        pageIndex,
        this.pageSizeAns,
        this.orderAns
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.dataTableAns = resp.result;
          this.amountRowsAns = resp.rowCount;
        }
      });
  }

  //Función que depende los check seleccioando devuelve el valor esperado por el back.
  getAnsStatus(active: boolean, inactive: boolean) {
    if (active && inactive) {
      return null;
    }
    if (!active && !inactive) {
      return null;
    }
    if (active && !inactive) {
      return true;
    }
    if (!active && inactive) {
      return false;
    }

    return null;
  }

  //Añade o elimina el id de un ANS a un array, según sea el caso.
  addOrRemoveAns(idGeneralAns: number, allValue?: boolean): void {
    const index = this.ansIdReporting.indexOf(idGeneralAns);
    if (index === -1) {
      this.ansIdReporting.push(idGeneralAns);
    } else {
      //Validamos que no esté agregando todos los valores por medio del check de seleccionar todos los ans en la tabla, para evitar que borre id que ya estén previamente agregados.
      if (!allValue) {
        this.ansIdReporting.splice(index, 1);
      }
    }
  }
}
