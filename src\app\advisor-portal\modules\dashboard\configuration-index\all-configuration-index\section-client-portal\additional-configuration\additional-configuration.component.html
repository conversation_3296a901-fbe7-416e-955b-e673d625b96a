<div class="row mt-5">
    <h3 class="fw-bold mb-2">
        {{ 'SectionClientPortal.BasicConfigurationTitle' | translate }}
    </h3>
    <form [formGroup]="formConfigurations">
        <h5 class="fw-bold mb-2">
            {{ 'SectionClientPortal.TitleLanding' | translate }}
        </h5>
        <div class="row">
            <mat-form-field appearance="outline" class="col-md-6 col-sm-12 mb-2">
                <mat-label>
                  {{'SectionClientPortal.TitleLandingText' | translate}}
                </mat-label>
                <input matInput formControlName="vTitleLanding" PreventionSqlInjector/>
                <mat-error *ngIf="utilsSvc.isControlHasError(formConfigurations, 'vTitleLanding', 'required')" >
                  {{'ThisFieldIsRequired' | translate}}
                </mat-error>
              </mat-form-field>
              <mat-form-field appearance="outline" class="col-md-6 col-sm-12 mb-2">
                <mat-label>
                  {{'SectionClientPortal.SubTitleLanding' | translate}}
                </mat-label>
                <textarea matInput formControlName="vSubTitleLanding" PreventionSqlInjector cdkTextareaAutosize cdkAutosizeMinRows="1" cdkAutosizeMaxRows="10"></textarea>
                <mat-error *ngIf="utilsSvc.isControlHasError(formConfigurations, 'vSubTitleLanding', 'required')" >
                  {{'ThisFieldIsRequired' | translate}}
                </mat-error>
              </mat-form-field>
        </div>
        <h5 class="fw-bold mb-2">
            {{ 'SectionClientPortal.TitleActionBtn' | translate }}
        </h5>
        <div class="row">
            <mat-form-field appearance="outline" class="col-md-6 col-sm-12 mb-2">
                <mat-label>
                  {{'SectionClientPortal.TitleButtonLanding' | translate}}
                </mat-label>
                <input matInput formControlName="vTextButtonLanding" PreventionSqlInjector/>
                <mat-error *ngIf="utilsSvc.isControlHasError(formConfigurations, 'vTextButtonLanding', 'required')" >
                  {{'ThisFieldIsRequired' | translate}}
                </mat-error>
            </mat-form-field>
            <mat-form-field appearance="outline" class="col-md-6 col-sm-12 mb-2">
                <mat-label>
                  {{'SectionClientPortal.UrlButtonLanding' | translate}}
                </mat-label>
                <input matInput formControlName="vUrlButtonLanding" PreventionSqlInjector/>
                <mat-error *ngIf="utilsSvc.isControlHasError(formConfigurations, 'vUrlButtonLanding', 'required')" >
                  {{'ThisFieldIsRequired' | translate}}
                </mat-error>
            </mat-form-field>
        </div>
        <app-generic-image-picker
            class="col-4 col-md-4"
            [title]="'SectionClientPortal.BtnLandingTitle'| translate"
            [description]="'SectionClientPortal.BtnLandingText'| translate"
            (changeFile)="changeImage($event, 'fkIIdUploadedFileButtonLanding')"
            [imageSrc]="imageSrcButton">
        </app-generic-image-picker>
        <button mat-raised-button color="primary" type="button" *ngIf="imageSrcButton" (click)="deleteImage('fkIIdUploadedFileButtonLanding')">
          {{ "Delete" | translate }}
        </button>

        
        <h5 class="fw-bold mb-2 mt-5">
            {{ 'SectionClientPortal.TitleBenefitsSection' | translate }}
        </h5>
        <app-generic-image-picker
            class="col-12 col-md-4"
            [title]="'SectionClientPortal.BenefitsIconTitle'| translate"
            [description]="'SectionClientPortal.BenefitsIconText'| translate"
            (changeFile)="changeImage($event, 'fkIIdUploadedBenefitsLanding')"
            [imageSrc]="imageSrcBenefits">
        </app-generic-image-picker>
        <button mat-raised-button color="primary" type="button" *ngIf="imageSrcBenefits" (click)="deleteImage('fkIIdUploadedBenefitsLanding')">
          {{ "Delete" | translate }}
        </button>

        <h5 class="fw-bold mb-2 mt-5">
          {{ 'SectionClientPortal.TitleFooterSection' | translate }}
        </h5>
        <app-generic-image-picker
            class="col-12 col-md-4"
            [title]="'SectionClientPortal.FooterIconTitle'| translate"
            [description]="'SectionClientPortal.FooterIconText'| translate"
            (changeFile)="changeImage($event, 'fkIIdUploadedFooterLanding')"
            [imageSrc]="imageSrcFooter">
        </app-generic-image-picker>
        <button mat-raised-button color="primary" type="button" *ngIf="imageSrcFooter" (click)="deleteImage('fkIIdUploadedFooterLanding')">
          {{ "Delete" | translate }}
        </button>
        

      </form>
      <div class="row">
        <div class="col">
          <button
            type="button"
            mat-raised-button
            color="primary"
            (click)="saveConfiguration()"
          >
            {{ "Save" | translate }}
          </button>
        </div>
      </div>
</div>