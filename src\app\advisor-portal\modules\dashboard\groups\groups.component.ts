import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-groups',
  standalone: true,
  imports: [CommonModule, RouterModule,TranslateModule,BreadcrumbComponent, MatTooltipModule,
    MatIconModule],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
        {{"Group.GroupsSettings" | translate}}
        <mat-icon class="click" matTooltipPosition="right"  matTooltip="{{ 'Tooltips.GroupsSettingsTilte' | translate }}">help_outline</mat-icon>
      </h2>
    </div>
  <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class GroupsComponent implements OnInit {
  constructor(
    private _translateService: TranslateService,
  ) {}
  
  inicio: string = this._translateService.instant("Inicio")  
  grupos: string = this._translateService.instant("Grupos")

  sections: {label: string, link: string}[]=[
    {label: this.inicio, link: '/dashboard'},
    {label: this.grupos, link: '/dashboard/groups'}
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant("Inicio")
      this.grupos = this._translateService.instant("Grupos")
      this.sections[0].label = this.inicio
      this.sections[1].label = this.grupos
    });
  }
}
