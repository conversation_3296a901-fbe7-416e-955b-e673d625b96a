@import "/src/assets/styles/variables";
.nav-container {
  width: auto;
  display: inline-block;
  height: auto;
  position: fixed;
  background: $color_2;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  z-index: 999;
  border-bottom: 1px solid $border_1;
  nav.navbar {
    background: var(--sds-light-base-sds-light-base-50, #f2f3f6);
    border-right: 1px solid var(--sds-light-base-sds-light-base-100, #d7dbe0);
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.15);

    .container-fluid {
      padding: 0;
    }
  }
  .navbar-toggler {
    height: 80px;

    .navbar-toggler-icon {
      background-image: none;
    }
    .navbar-brand {
      display: none;
    }
    .navbar-toggler-close {
      display: none;
    }
    &:focus {
      outline: 0;
      box-shadow: none;
    }
  }
  .navbar-toggler[aria-expanded="true"] {
    width: 225px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    outline: 0 !important;
    .navbar-toggler-icon {
      display: none;
    }
    .navbar-toggler-close {
      display: block;
    }
    .navbar-brand {
      display: inline-block;
    }
  }
  .navbar-collapse {
    display: flex;
    padding-bottom: 20px;
    height: calc(100vh - 100px);
    display: flex;
    flex-wrap: wrap;
    align-content: space-between;
    max-width: 55px;
    overflow: hidden;
    transition: color 0.05s ease-in, background-color 0.05s ease-in,
      border-color 0.05s ease-in;
    .navbar-nav {
      .nav-item {
        .nav-link {
          transition: color 0.05s ease-in, background-color 0.05s ease-in,
            border-color 0.05s ease-in;
          span {
            font-family: $font-family_1;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 19px;
            color: $color_1;
          }
        }
        .nav-link.active {
          position: relative;
          border-radius: 4px;
          background: $bg_1;
          border-left: 6px solid $color_1;
        }
        .nav-link[aria-expanded="true"] {
          border-radius: 4px 4px 0 0;
          background: $bg_1;
          border-left: 6px solid $color_1;
        }
        ul {
          width: 100%;
          padding: 0;
          margin: 0;
          overflow: hidden;
          li.nav-item {
            height: 50px;
            padding: 0;
            padding-right: 8px;
            .nav-link {
              height: 50px;
              border-radius: 0;
              background: $bg_1;
              border-left: 6px solid $color_1;
              padding-left: 16px;
            }
            &:last-child {
              .nav-link {
                border-radius: 0 0 4px 4px;
              }
            }
            .nav-link.active {
              height: 50px;
              border-radius: 0;
              background: #d7dbe0 !important;
              border-left: 6px solid $color_1;
              padding-left: 16px;
            }
          }
        }
      }
      .nav-link.logout {
        color: $color_2;
        span {
          font-family: $font-family_1;
          font-size: 14px;
          font-weight: 700;
          line-height: 17px;
          letter-spacing: -0.012em;
          text-align: center;
          color: $color_1;
        }
      }
    }
  }
  .navbar-collapse.collapse {
    .navbar-nav {
      .nav-item {
        width: 55px;
        height: 55px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        .nav-link {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            opacity: 0;
            width: 0;
            overflow: hidden;
          }
          img{
            height: 24px;
            width: 24px;
          }

        }
        .nav-link.active {
          position: relative;
          border-radius: 4px;
          background: $bg_1;
          border-left: 6px solid $color_1;
        }
      }
    }
    .nav-link.logout {
      width: 55px;
      height: 55px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        display: none;
      }
    }
  }
  .navbar-collapse.show {
    max-width: 225px;
    .navbar-nav {
      .nav-item {
        width: 225px;
        height: auto;
        padding: 8px;
        .nav-link {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: start;
          padding-left: 12px;
          img {
            margin-right: 12px;
            width: 24px;
            height: 24px;
          }
          span {
            width: auto;
            opacity: 1;
          }
        }
        .nav-link.active {
          padding-left: 6px;
        }
      }
    }
    .nav-link.logout {
      width: 100%;
      justify-content: center;
      img {
        margin-left: 12px;
      }
      span {
        display: block;
        color: $color_1;
      }
    }
  }
}

@media (max-width: 766px) {
  .nav-container {
    .navbar {
      padding: 0;
      .navbar-toggler {
        height: 80px;
      }
      .user {
        width: 56px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .offcanvas {
        .offcanvas-body {
          display: flex;
          flex-wrap: wrap;
          align-content: space-between;
          .navbar-nav {
            width: 100%;
            .nav-item {
              height: auto;
              .nav-link {
                height: 56px;
                display: flex;
                align-items: center;
                padding-left: 6px;
                img {
                  margin-right: 16px;
                  height: 24px;
                  width: 24px;
                }
                span {
                  font-family: $font-family_1;
                  font-style: normal;
                  font-weight: 600;
                  font-size: 16px;
                  line-height: 19px;
                  color: $color_1;
                }
              }
              .nav-link.active {
                position: relative;
                border-radius: 4px;
                background: $bg_1;
                border-left: 6px solid $color_1;
              }
              .nav-link[aria-expanded="true"] {
                border-radius: 4px 4px 0 0;
                background: $bg_1;
                border-left: 6px solid $color_1;
              }
              ul {
                width: 100%;
                padding: 0;
                margin: 0;
                overflow: hidden;
                li.nav-item {
                  height: 50px;
                  padding: 0;
                  .nav-link {
                    height: 50px;
                    border-radius: 0;
                    background: $bg_1;
                    border-left: 6px solid $color_1;
                    padding-left: 16px;
                  }
                  &:last-child {
                    .nav-link {
                      border-radius: 0 0 4px 4px;
                    }
                  }
                }
              }
            }
          }
          .nav-link.logout {
            color: $color_2;
            width: 100%;
            display: flex;
            align-items: center;
            height: 56px;
            justify-content: center;
            span {
              font-family: $font-family_1;
              font-size: 14px;
              font-weight: 700;
              line-height: 17px;
              letter-spacing: -0.012em;
              text-align: center;
              color: $color_1;
            }
          }
        }
      }
    }
  }
}
::ng-deep .navbar-toggler {
  border: none !important;
}

.test {
  background-color: blue;
}
.navbar-collapse .navbar-nav .nav-link.active {
  // background: blue !important;
}

.menu-scroll{
  max-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}