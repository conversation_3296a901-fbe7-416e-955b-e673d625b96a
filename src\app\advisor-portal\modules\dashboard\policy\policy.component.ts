import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterModule,
} from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { filter, Subscription } from 'rxjs';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-policy',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    BreadcrumbComponent,
    MatIconModule,
    MatTooltipModule,
  ],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/polizas.svg" />
        {{ title | translate }}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styles: [],
})
export class PolicyComponent {
  start: string = this._translateService.instant('Inicio');
  title: string = this._translateService.instant('Policy.PolicyTitle');
  policy: string = this._translateService.instant('Policy.PolicyTitle');
  individualPolicy: string = this._translateService.instant(
    'Policy.IndividualPolicies'
  );
  groupPolicys: string = this._translateService.instant('Policy.GroupPolicys');
  individualColectivePolicy: string = this._translateService.instant(
    'Policy.IndividualCollective'
  );
  colectivePolicy: string =
    this._translateService.instant('Policy.GroupPolicy');
  risk: string = this._translateService.instant('Policy.Risk');
  subscriptionUrl!: Subscription;
  currentUrl: string = '';
  idParent: number = 1;
  isAllIndividualPolicy: boolean = false;
  idPolicyType: number = 0;

  constructor(
    private _translateService: TranslateService,
    private _router: Router
  ) {}

  sections: { label: string; link: string }[] = [
    { label: this.start, link: '/dashboard' },
    { label: this.policy, link: '/dashboard/policy' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.start = this._translateService.instant('Inicio');
      this.policy = this._translateService.instant('Policy.PolicyTitle');

      //Traducción dínamica del titulo según el tipo de póliza a gestionar.
      switch (this.currentUrl) {
        case 'individual-policy':
          this.title = this._translateService.instant(
            'Policy.IndividualPolicies'
          );
          break;
        case 'colective-individual-policy':
          this.title = this._translateService.instant('Policy.GroupPolicys');
          break;
        case 'individual-coelctive-policy':
          this.title = this._translateService.instant(
            'Policy.IndividualCollective'
          );
          break;
        case 'policy':
          this.title = this._translateService.instant('Policy.PolicyTitle');
          break;
        case 'risk':
          this.title = this._translateService.instant('Policy.Risk');
          break;
        default:
          this.title = this._translateService.instant('Policy.PolicyTitle');
          break;
      }
    });

    this.subscriptionUrl = this._router.events
      .pipe(
        filter(
          (event: any): event is NavigationEnd => event instanceof NavigationEnd
        )
      )
      .subscribe((event: NavigationEnd) => {
        this.getPrincipalTitle(event.urlAfterRedirects);
      });
  }

  //Obtiene el titulo principal según la ruta.
  getPrincipalTitle(url: string) {
    // Busca la sección 'dashboard/' en la URL
    const dashboardIndex = url.indexOf('dashboard/');
    // Extrae todo lo que está después de 'dashboard/'
    const afterDashboard = url.substring(dashboardIndex + 'dashboard/'.length);

    // Divide la cadena resultante en partes utilizando '/'
    const routeParts = afterDashboard.split('/');
    const routePartsLength = routeParts.length;
    switch (routePartsLength) {
      case 1:
        this.currentUrl = 'policy';
        this.title = this.policy;
        break;
      case 3:
        if (routeParts[1] === 'individual-policy') {
          this.currentUrl = 'individual-policy';
          this.title = this.individualPolicy;
        }
        if (routeParts[1] === 'colective-individual-policy') {
          this.currentUrl = 'colective-policy';
          this.title = this.groupPolicys;
        }
        break;
      case 4:
        if (routeParts[1] === 'see-details-colective-individual-policy') {
          if (routeParts[3] === '2') {
            this.currentUrl = 'colective-policy';
            this.title = this.colectivePolicy;
          }
          if (routeParts[3] === '3') {
            this.currentUrl = 'individual-coelctive-policy';
            this.title = this.individualColectivePolicy;
          }
        }
        break;
      case 6:
        if (routeParts[5] === '0') {
          this.currentUrl = 'risk';
          this.title = this.risk;
        } else {
          this.currentUrl = 'policy';
          this.title = this.policy;
        }
        break;
      default:
        this.currentUrl = 'policy';
        this.title = this.policy;
        break;
    }
  }

  ngOnDestroy(): void {
    this.subscriptionUrl.unsubscribe();
  }
}
