<!-- <PERSON><PERSON><PERSON><PERSON> póliza -->
<div class="row">
    <h5 class="title-local mb-1">{{ 'BulkUpload.MassManagementPolicy.MassManagement' | translate }}</h5>
</div>

<!-- datatable Pólizas -->
<div class="row mt-2">
    <app-table [displayedColumns]="estructTablePolicy" [data]="dataTablePolicy"></app-table>
</div>

<!-- Tipo de gestión -->
<div class="row mt-3">
    <div class="col-md-12">
        <mat-form-field class="w-100" appearance="fill">
            <mat-label>{{ 'BulkUpload.MassManagementPolicy.ManagementOfType' | translate }}</mat-label>
            <mat-select (selectionChange)="onTypeManagement($event)">
                <mat-option *ngFor="let item of getFilterOption()" [value]="item.pkIIdManagementType">
                    {{ item.vName }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
</div>

<!-- Catálogo de observaciones-->
<div class="row mt-3" *ngIf="TypeOfManagementSelected === 2 || TypeOfManagementSelected === 4 ">
    <div class="col-md-12">
        <mat-form-field class="w-100" appearance="fill">
            <mat-label>{{ 'BulkUpload.MassManagementPolicy.CatalogueBbservations' | translate }}</mat-label>
            <mat-select (selectionChange)="onCatalog($event)">
                <mat-option *ngFor="let item of catalogList" [value]="item.pkIIdCatalog">
                    {{ item.vName }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
</div>

<ng-container *ngIf="showFileUpload">
    <div class="cont-fieles mt-3">
        <div class="cont-template">
            <div class="cont-info-file">
                <p class="m-0 p-0">{{templateName}}</p>
                <span class="description">25 MB</span>
            </div>
            <div class="cont-download-icon">
                <span (click)="downloadTemplate()" class="material-symbols-outlined click">
                    download
                </span>
            </div>
        </div>
        <div class="cont-template" *ngIf="files.length > 0">
            <div class="cont-info-file">
                <p class="m-0 p-0">{{fileName}}</p>
                <span class="description">25 MB</span>
            </div>
            <div class="cont-download-icon">
                <span class="material-symbols-outlined click" (click)="deleteFile()">
                    delete
                </span>
            </div>
        </div>
        <div class="cont-upload" *ngIf="files.length === 0">
            <app-drag-drop-upload [fileAccept]="'.xlsx'" [isOneFile]="true" [message]="''"
                (saveFile)="saveFiles($event)"></app-drag-drop-upload>
        </div>
    </div>
</ng-container>

<div class="d-flex justify-content-center mt-3">
    <a class="label-button" mat-button (click)="close()"><span>{{'Close'| translate}}</span></a>
    <button *ngIf="TypeOfManagementSelected === 3 " [disabled]="files.length < 1" class="mx-3" type="button"
        color="primary" (click)="loadPolic()" mat-raised-button>
        {{ "BulkUpload.MassManagementPolicy.UploadPolicy" | translate }}
    </button>
    <button *ngIf="TypeOfManagementSelected === 4" [disabled]="files.length < 1" class="mx-3" type="button"
        color="primary" (click)="loadPolic()" mat-raised-button>
        {{ "BulkUpload.MassManagementPolicy.CancelPolicy" | translate }}
    </button>
    <button *ngIf="TypeOfManagementSelected === 1" [disabled]="files.length < 1" class="mx-3" type="button"
        color="primary" (click)="loadPolic()" mat-raised-button>
        {{ "BulkUpload.MassManagementPolicy.UploadRisks" | translate }}
    </button>
    <button *ngIf="TypeOfManagementSelected === 2" [disabled]="files.length < 1" class="mx-3" type="button"
        color="primary" (click)="loadPolic()" mat-raised-button>
        {{ "BulkUpload.MassManagementPolicy.ExcludeRisks" | translate }}
    </button>
</div>