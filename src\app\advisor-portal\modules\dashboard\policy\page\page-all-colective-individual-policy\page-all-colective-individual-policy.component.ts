import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BodyTableModel } from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { AllPolicyTableComponent } from '../../shared/components/all-policy-table/all-policy-table.component';
import { PolicyModel } from '../../shared/models';
import { PolicyTableService } from '../../shared/service/policy-table.service';
import { ActivatedRoute } from '@angular/router';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-page-all-colective-individual-policy',
  standalone: true,
  imports: [
    CommonModule,
    AllPolicyTableComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './page-all-colective-individual-policy.component.html',
  styleUrls: ['./page-all-colective-individual-policy.component.scss'],
})
export class PageAllColectiveIndividualPolicyComponent {
  idPolicyType: number = 2 | 3;
  idBusinessByCountry: number = 0;
  estructTableColectiveIndividualPolicy: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      ),
      columnValue: 'idwtw',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyNumber'
      ),
      columnValue: 'policyNumber',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      ),
      columnValue: 'insurance',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      ),
      columnValue: 'product',
    },
    {
      columnLabel: this._translateService.instant('Policy.StartOfValidity'),
      columnValue: 'startValidity',
      functionValue: (item: PolicyModel) => this._utilsService.formatDate(item.startValidity, 'DD-MM-YYYY')
    },
    {
      columnLabel: this._translateService.instant('Policy.EndOfValidity'),
      columnValue: 'endValidity',
      functionValue: (item: PolicyModel) => this._utilsService.formatDate(item.endValidity, 'DD-MM-YYYY')
    },
    {
      columnLabel: this._translateService.instant('Tipo de póliza'),
      columnValue: 'typePolicy',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'statePolicy',
      functionValue: (item: PolicyModel) =>
        this._policyTableService.changePolicyStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Gestionar'),
      columnValue: 'manage',
      columnIcon: 'search',
    },
  ];

  constructor(
    private _customeRouter: CustomRouterService,
    private _translateService: TranslateService,
    private _policyTableService: PolicyTableService,
    private _activatedRoute: ActivatedRoute,
    private _utilsService: UtilsService
  ) {
    this.getDataUrl();
  }

  //Obtiene los valores de las variables enviadas por medio de la URL.
  getDataUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
    });
  }

  getActionTable(event: PolicyModel) {
    switch (event.typePolicy) {
      case 'Colectiva':
        this._customeRouter.navigate([
          `dashboard/policy/see-details-colective-individual-policy/${
            event.idwtw
          }/${2}`,
        ]);
        break;
      case 'Individual colectiva':
        this._customeRouter.navigate([
          `dashboard/policy/see-details-colective-individual-policy/${
            event.idwtw
          }/${3}`,
        ]);
        break;

      default:
        break;
    }
  }

  //Funcicón que redirecciona al usuario al submódulo de cargue amsivo de pólizas, dentro del módulo de masivos.
  goToMassive() {
    this._customeRouter.navigate([
      `dashboard/massive/mass-creation-policy/${this.idBusinessByCountry}`,
    ]);
  }
}
