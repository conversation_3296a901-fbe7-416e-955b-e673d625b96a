import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import {
  INewReportProcesoModel,
  INewReportProductoModel,
} from 'src/app/shared/models/reports';
import { catchError, finalize, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { StageModel } from 'src/app/shared/models/module';
import { ActivatedRoute } from '@angular/router';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { DragDropUploadComponent } from 'src/app/shared/components/drag-drop-upload/drag-drop-upload.component';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { UserService } from 'src/app/shared/services/user/user.service';


@Component({
  selector: 'app-mass-creation-tasks',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    DragDropUploadComponent,
    MatCheckboxModule
  ],
  templateUrl: './mass-creation-tasks.component.html',
  styleUrls: ['./mass-creation-tasks.component.scss'],
})
export class MassCreationTasksComponent implements OnInit {
  //Variables para el formulario.
  formFilter: FormGroup = new FormGroup({});
  listProcess: INewReportProcesoModel[] = [];
  listProduct: INewReportProductoModel[] = [];
  stageList: StageModel[] = [];
  statusList: any[] = [];
  idBusinessByCountry: number = 0;
  idUser: number = 0;
  showFileUpload: boolean = false;
  fileName: string = '';
  templateName: string = 'Creación_masiva_tarea';
  uploadedFile: File[] = [];
  template!: File;

  constructor(
    private _parametersService: ParametersService,
    private _fb: FormBuilder,
    private _msgSvc: MessageService,
    private _activatedRoute: ActivatedRoute,
    private _translateService: TranslateService,
    private _roleService: RoleService,
    private _moduleService: ModuleService,
    private _messageService: MessageService,
    private _utilsService: UtilsService,
    private _customeRouter: CustomRouterService,
    private _spinnerService: SpinnerService,
    private _fileService: FileService,
    private _userService: UserService
  ) {
    this.getBusinessByCountry();
  }

  async ngOnInit() {
    this.getAllProcess();
    this.initFormFilter();
    this.idUser = await this._userService.getUserIdSesion();
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
    });
  }

  initFormFilter() {
    this.formFilter = this._fb.group({
      fkIIdprocess: [],
      fkIIdProduct: [],
      fkIdStage: [{ value: null, disabled: true }],
      fkIdState: [{ value: null, disabled: true }],
      isDisabledCommunications: [false]
    });

    this.formFilter.get('fkIIdprocess')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formFilter.get('fkIIdProduct')?.setValue(null);
        this.formFilter.get('fkIdStage')?.setValue(null);
        this.formFilter.get('fkIdState')?.setValue(null);
        this.listProduct = [];
        this.stageList = [];
        this.statusList = [];
        let fileNull!: File;
        this.template = fileNull;
        this.uploadedFile = [];
        this.getProductsList(value, this.idBusinessByCountry);
      }
    });

    this.formFilter.get('fkIIdProduct')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formFilter.get('fkIdStage')?.setValue(null);
        this.formFilter.get('fkIdState')?.setValue(null);
        this.stageList = [];
        this.statusList = [];
        let fileNull!: File;
        this.template = fileNull;
        this.uploadedFile = [];
        this.getStageByIdProductModule(value);
      }
    });

    this.formFilter.get('fkIdStage')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formFilter.get('fkIdState')?.setValue(null);
        this.statusList = [];
        this.getStagByStateAssociateToFormByIdStage(value);
      }
    });
  }

  //Obtiene la lista de Procesos.
  getAllProcess() {
    this._parametersService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant(error.error.message)
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.listProcess = resp.result;
        }
      });
  }

  //obtiene los productos registrados por proceso y idBusinessCountry
  getProductsList(idProcess: number, idBusinessByCountry: number) {
    this._roleService
      .getProductsByIdProcess(idProcess, idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant(error.error.message)
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.listProduct = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageInfo(
              this._translateService.instant('ThereWasAError'),
              resp.message
            );
          } else {
            this.listProduct = resp.result;
          }
        }
      });
  }

  goBackMassive() {
    this._customeRouter.navigate([`dashboard/massive`]);
  }

  //obtiene la lista de etapas asignadas a un producto.
  getStageByIdProductModule(pkIIdProductModule: number) {
    if (pkIIdProductModule) {
      this._moduleService
        .getStageByIdProductModule(pkIIdProductModule)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.result != undefined) {
              if (resp.result.length > 0) {
                this.stageList = resp.result;
                this.formFilter
                  .get('fkIdStage')
                  ?.setValue(this.stageList[0].pkIIdStage);
              }
            }
          }
        });
    }
  }

  //obtiene la lista de estados asignados a una etapa.
  getStagByStateAssociateToFormByIdStage(idStage: number) {
    if (idStage > 0) {
      this._moduleService
        .getStagByStateAssociateToFormByIdStage(idStage)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.result != undefined) {
              if (resp.result.length > 0) {
                this.statusList = resp.result;
                console.log(this.statusList);

                this.formFilter
                  .get('fkIdState')
                  ?.setValue(this.statusList[0].pkIIdStageByState);
              }
            }
          }
        });
    }
  }

  //Función que se encarga de llamar a la función que devuelve el base64 con el template.
  downloadTemplate() {
    const idStageByState = this.formFilter.get('fkIdState')?.value;
    if (idStageByState > 0) {
      this._moduleService
        .getJSONTaskModule(idStageByState)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.result != undefined) {
              this.downloadTemplateByBase64(resp.result, this.templateName);
              const file = this._utilsService.base64ToFile(
                resp.result,
                this.templateName
              );
              if (file) {
                this.template = file;
              }
            }
          }
        });
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant(
          'BulkUpload.Massives.MassCreationTasks.MandatoryFiltersTitle'
        ),
        this._translateService.instant(
          'BulkUpload.Massives.MassCreationTasks.MandatoryFiltersSubtitle'
        )
      );
    }
  }

  //Detecta los eventos de componente de cargue de archivos.
  getFiles(files: File[]) {
    if (this._utilsService.validateFileSize(files[0].size, 25)) {
      this.uploadedFile = files;
      if (this.uploadedFile.length > 0) {
        this.fileName = this.uploadedFile[0].name;
      }
    } else {
      this._msgSvc.messageInfo(
        this._translateService.instant('MaximumFileSizeTitle'),
        this._translateService.instant('MaximumFileSizeSubtitle')
      );
    }
  }
  //Elimina el file cargado.
  deleteFile() {
    this.uploadedFile = [];
    this.fileName = '';
  }

  //Función que descarga une xcel, basado en un base64.
  downloadTemplateByBase64(base64Data: string, fileName: string) {
    // Convierte el base64 a un Blob
    const byteCharacters = atob(base64Data); // Decodifica base64
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    // Crea un enlace de descarga
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = fileName; // El nombre que tendrá el archivo descargado
    link.click();
  }

  //Compara 2 archivos excel para verificar si las cabeceras son iguales.
  comparerExcelFiles(files: FormData) {
    this._fileService
      .comparerExcelFiles(files)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe(async (resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.length > 0) {
            this._msgSvc.messageErrorCustomer(
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadTitle'
              ),
              this._translateService.instant(
                'BulkUpload.PopUp.IncorrectLoadSubtitle'
              )
            );
          } else {
            await this.uploadMassivePolicyTask();
          }
        }
      });
  }

  async validConfirmMessage() {
    try {
      if (!this.formFilter.value.isDisabledCommunications) {
        const resultCommunications = await this._messageService.messageConfirmationAndNegation(
          this._translateService.instant('BulkUpload.Massives.MassCreationStates.SomeStagesAndStatusesGenerateCommunications'),
          this._translateService.instant('BulkUpload.Massives.MassCreationStates.DoYouWantToDisableCommunications'),
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        );

        if (resultCommunications) {

        } else {
          return; // Detener la secuencia si el usuario cancela
        }
      }

      const resultStateChange = await this._messageService.messageConfirmationAndNegation(
        this._translateService.instant('BulkUpload.Massives.MassCreationStates.DoYouWantToConfirmStateChange'),
        this._translateService.instant('BulkUpload.Massives.MassCreationStates.VerifyThatTheInformationIsCorrect'),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      );

    } catch (error) {
      console.error("Se produjo un error en la secuencia de confirmaciones:", error);
    }
  }

  //Guarda la carga masiva en la BD.
  async uploadMassivePolicyTask() {
    await this._spinnerService.show();
    const payload = new FormData();
    let jsonAditional: any = {
      idStateModule: this.formFilter.get('fkIdState')?.value,
      disableCommunications: this.formFilter.get('isDisabledCommunications')?.value,
    };
    payload.append(`IdManagementType`, (5).toString());
    payload.append(`IdUser`, this.idUser.toString());
    payload.append(`File`, this.uploadedFile[0]);
    payload.append(`JsonAditional`, JSON.stringify(jsonAditional));
    this._spinnerService.show();
    this._fileService
      .uploadMassivePolicyTask(payload)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        }),
        finalize(() => {
          this._spinnerService.hide();
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result) {
            this._msgSvc
              .messageConfirmatioCustomer(
                `${this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadTitle'
                )} ${resp.result}`,
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadSubtitle'
                ),
                'success',
                this._translateService.instant(
                  'BulkUpload.PopUp.SuccessfulUploadBtnLeft'
                ),
                this._translateService.instant('Continuar')
              )
              .then((result) => {
                if (result) {
                  this._customeRouter.navigate([
                    `dashboard/massive/load-viewer/${this.idBusinessByCountry}`,
                  ]);
                }
              });
          }
        }
      });
  }

  //Guarda la carga masiva en la BD.
  async loadTask() {

    try {
      if (!this.formFilter.value.isDisabledCommunications) {
        const resultCommunications = await this._messageService.messageConfirmationAndNegation(
          this._translateService.instant('BulkUpload.Massives.MassCreationStates.SomeStagesAndStatusesGenerateCommunications'),
          this._translateService.instant('BulkUpload.Massives.MassCreationStates.DoYouWantToDisableCommunications'),
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        );

        if (resultCommunications) {

        } else {
          return; // Detener la secuencia si el usuario cancela
        }
      }

      const resultStateChange = await this._messageService.messageConfirmationAndNegation(
        this._translateService.instant('BulkUpload.Massives.MassCreationStates.DoYouWantToConfirmStateChange'),
        this._translateService.instant('BulkUpload.Massives.MassCreationStates.VerifyThatTheInformationIsCorrect'),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      );

      if (this.template && this.uploadedFile.length > 0) {
        this._msgSvc
          .messageConfirmationAndNegation(
            this._translateService.instant('BulkUpload.PopUp.ValidateLoadTitle'),
            '',
            'warning',
            this._translateService.instant('Confirm'),
            this._translateService.instant('Cancel')
          )
          .then((result) => {
            if (result) {
              const fileToCompare: File[] = [this.template, this.uploadedFile[0]];
              const payload = new FormData();

              fileToCompare.forEach((element) => {
                payload.append(`Files`, element);
              });
              this.comparerExcelFiles(payload);
            }
          });
      }

    } catch (error) {
      console.error("Se produjo un error en la secuencia de confirmaciones:", error);
    }


  }
}
