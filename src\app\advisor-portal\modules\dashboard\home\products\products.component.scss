@import "/src/assets/styles/variables";

.cont-title {
  h4 {
    font-weight: 600 !important;
  }
}

.product-type {
  width: 100%;
  display: flex;
  align-items: start;
  justify-content: center;
  flex-wrap: wrap;
  .product {
    width: 160px;
    text-decoration: none;
    margin: 1.5rem;
    cursor: pointer !important;
    .box {
      width: 160px;
      height: 160px;
      background: $color_2;
      box-shadow: 0px 5px 6px 3px rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
    }
    h5 {
      width: 100%;
      text-align: center;
      color: $color_1;
    }
  }
}

.box-min {
  width: 160px !important;
  height: 160px !important;
}
.box-max {
  width: 120px !important;
  height: 120px !important;
}

.logo-min {
  width: 100px;
  height: 100px;
}

.logo-max {
  width: 80px;
  height: 80px;
}

.product-name {
  font-size: 20px !important;
}
.product-name-size {
  width: 120px !important;
}
