import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { TopModel } from 'src/app/shared/models/business/top.model';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';

@Component({
  selector: 'app-edit-create-top',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    TranslateModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    PreventionSqlInjectorDirective
  ],
  templateUrl: './edit-create-top.component.html',
  styleUrls: ['./edit-create-top.component.scss']
})
export class EditCreateTopComponent implements OnInit {
  @Output() topModelOut: EventEmitter<any> = new EventEmitter();
  @Input() idSectionIndexBusinessIn: number = 0;
  @Input() topIdIn: number = 0;

  form: FormGroup = new FormGroup({});

  topModel?: TopModel;
  //variables que permiten validar las filas que se muestran así como los botones. 
  showTwo: boolean = false
  showThree: boolean = false
  showFour: boolean = false
  showFive: boolean = false

  constructor(
    private fb: FormBuilder,
    public _businessService: BusinessService,
    public _utilsService: UtilsService,
    public _translateService: TranslateService
  ){}


  ngOnInit(): void {
    this.initForm();
    if(this.topIdIn!= 0)
    {
      this.getTop();
    }    
  }

  initForm() {
    this.form = this.fb.group({
      pkIIdTop: [],
      vTopTittle: ['', [Validators.required]],
      vQuote1: ['', [Validators.required]],
      vQuote2: [''],
      vQuote3: [''],
      vQuote4: [''],
      vQuote5: ['']
    });
  }

  //obtiene el top a partir del id
  getTop()
  {
    this._businessService.getTopsBySection(this.idSectionIndexBusinessIn).subscribe((resp) =>{
      let topModelAct : TopModel = resp.result.filter(
        (t: TopModel) => t.pkIIdTop == this.topIdIn
      )[0];
      
      switch(JSON.parse(topModelAct.vJsonElements).length)
      {
        case 5:
          this.showFive = true;
          this.showFour = true;
          this.showThree = true;
          this.showTwo = true;
          break;
        case 4:
          this.showFive = false;
          this.showFour = true;
          this.showThree = true;
          this.showTwo = true;
          break;
        case 3: 
          this.showFive = false;
          this.showFour = false;
          this.showThree = true;
          this.showTwo = true;
          break;
        case 2:
          this.showFive = false;
          this.showFour = false;
          this.showThree = false;
          this.showTwo = true;
          break;
        default: 
          this.showFive = false;
          this.showFour = false;
          this.showThree = false;
          this.showTwo = false;  
          break;
      }
      this.form.patchValue({ pkIIdTop: topModelAct.pkIIdTop })
      this.form.patchValue({ vTopTittle: topModelAct.vTitle })
      this.form.patchValue({ vQuote1: JSON.parse(topModelAct.vJsonElements)[0]})
      this.form.patchValue({ vQuote2: JSON.parse(topModelAct.vJsonElements)[1]})
      this.form.patchValue({ vQuote3: JSON.parse(topModelAct.vJsonElements)[2]})
      this.form.patchValue({ vQuote4: JSON.parse(topModelAct.vJsonElements)[3]})
      this.form.patchValue({ vQuote5: JSON.parse(topModelAct.vJsonElements)[4]})
    })
  }

  //añade una fila segun el caso y añade las validaciones al campo correspondiente
  addRow(actRow: number = 0){
    switch ( actRow ) {
      case 1:
        this.showTwo = true;
        this.form.get('vQuote2')?.setValidators(Validators.required);
        this.form.get('vQuote2')?.updateValueAndValidity();        
        break;
      case 2:
        this.showThree = true;
        this.form.get('vQuote3')?.setValidators(Validators.required);
        this.form.get('vQuote3')?.updateValueAndValidity(); 
          break;
      case 3:
        this.showFour = true;
        this.form.get('vQuote4')?.setValidators(Validators.required);
        this.form.get('vQuote4')?.updateValueAndValidity(); 
          break;
      case 4: 
        this.showFive = true;
        this.form.get('vQuote5')?.setValidators(Validators.required);
        this.form.get('vQuote5')?.updateValueAndValidity(); 
          break;
    }    
    this.emitModel();
  }
  
  //remueve una fila segun el caso, remueve las validaciones al campo correspondiente y deja el campo removido en vacio
  removeRow(actRow: number = 0){
    switch ( actRow ) {
      case 2:
        this.showTwo = false;        
        this.form.get('vQuote2')?.clearValidators();
        this.form.get('vQuote2')?.updateValueAndValidity();
        this.form.patchValue({vQuote2: ''})
          break;
      case 3:
        this.showThree = false;
        this.form.get('vQuote3')?.clearValidators();
        this.form.get('vQuote3')?.updateValueAndValidity();
        this.form.patchValue({vQuote3: ''})
          break;
      case 4:
        this.showFour = false;
        this.form.get('vQuote4')?.clearValidators();
        this.form.get('vQuote4')?.updateValueAndValidity();
        this.form.patchValue({vQuote4: ''})
          break;
      case 5: 
        this.showFive = false;
        this.form.get('vQuote5')?.clearValidators();
        this.form.get('vQuote5')?.updateValueAndValidity();
        this.form.patchValue({vQuote5: ''})
          break;
    }
    this.emitModel();
  }

  //emite el valor del modelo.
  emitModel()
  {
    if(this.form.valid)
    {
      let TopRows : string[] = [];
      TopRows.push(this.form.value.vQuote1)
      if(this.showTwo){
        TopRows.push(this.form.value.vQuote2)
      }
      if(this.showThree){
        TopRows.push(this.form.value.vQuote3)
      }
      if(this.showFour){
        TopRows.push(this.form.value.vQuote4)
      }
      if(this.showFive){
        TopRows.push(this.form.value.vQuote5)
      }

      let topModel = {
        pkIIdTop : 0,
        vTitle: this.form.value.vTopTittle,
        vJsonElements: JSON.stringify(TopRows),
        fkIIdSectionIndexBusiness:0
      }
      this.topModelOut.emit(topModel);
    }
    else{
      this.topModelOut.emit('Invalid form')
    }    
  }
}
