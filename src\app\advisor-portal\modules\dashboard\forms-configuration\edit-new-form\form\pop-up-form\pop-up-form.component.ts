import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  PopUpModel,
  PopUpTypeModel,
} from 'src/app/shared/models/configuration-form';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-pop-up-form',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    Modal2Component,
    MatSelectModule,
    MatCheckboxModule,
    MatRadioModule,
    MatTooltipModule
  ],
  templateUrl: './pop-up-form.component.html',
  styleUrls: ['./pop-up-form.component.scss'],
})
export class PopUpFormComponent implements OnInit {
  @Input() idForm: number = 0;
  form: FormGroup = new FormGroup({});

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.PopUp.FieldIdRowTable'
      ),
      columnValue: 'pkiIdPopUp',
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.PopUp.TitleRowTable'
      ),
      columnValue: 'vTitle',
    },
    {
      columnLabel: this._translateService.instant(
        'FormConfiguration.PopUp.TitleRowFieldDependent'
      ),
      columnValue: 'bIsSave',
      functionValue: (item: any) => this.changeDependentFieldValue(item),
    },

    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'edit',
      columnIcon: 'edit',
    },
  ];
  dataTablePopUp: PopUpModel[] = [];
  listOfPopUpTypes: PopUpTypeModel[] = [];
  @ViewChild('editNewPopUpModal')
  editNewPopUpModal?: TemplateRef<any>;
  titelModal: string = 'Nuevo pop-up';
  action: string = 'create';

  constructor(
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    public modalDialog: MatDialog,
    public utilsSvc: UtilsService,
    private _fb: FormBuilder
  ) {
    this.listOfPopUpTypes = [
      { fkiIdPopUpType: 1, name: 'Confirmación' },
      { fkiIdPopUpType: 2, name: 'Alerta' },
      { fkiIdPopUpType: 3, name: 'Información' },
    ];
  }

  ngOnInit(): void {
    this.initForm();
    if (this.idForm !== 0) {
      this.getPopUpList(this.idForm);
    }
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table modules
      this.estructTable[0].columnLabel = this._translateService.instant(
        'FormConfiguration.PopUp.FieldIdRowTable'
      );
      this.estructTable[1].columnLabel = this._translateService.instant(
        'FormConfiguration.PopUp.TitleRowTable'
      );

      this.estructTable[2].columnLabel = this._translateService.instant(
        'FormConfiguration.PopUp.TitleRowFieldDependent'
      );
      this.estructTable[3].columnLabel =
        this._translateService.instant('Status');

      this.estructTable[4].columnLabel =
        this._translateService.instant('Action');
    });
  }

  initForm() {
    this.form = this._fb.group({
      pkiIdPopUp: [0],
      fkiIdFormModule: [this.idForm],
      bIsSave: [true, [Validators.required]],
      vTitle: ['', [Validators.required]],
      vText: [''],
      bActive: [true],
      fkiIdPopUpType: [null, [Validators.required]],
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  getPopUpList(idFormModule: number) {
    this._moduleService
      .getPopUpList(idFormModule)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
          this.dataTablePopUp = [];
        } else {
          if (response.error) {
            this.dataTablePopUp = [];
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.dataTablePopUp = response.result;
          }
        }
      });
  }

  getPopUpById(idPopUp: number) {
    this._moduleService
      .getPopUpById(idPopUp)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.form.patchValue(resp.result);
          }
        }
      });
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'edit':
        this.action = 'edit';
        this.getPopUpById(event.value.pkiIdPopUp);
        this.openModaleditNewPopUp();
        break;
      default:
        break;
    }
  }

  openModaleditNewPopUp() {
    if (this.action === 'edit') {
      this.titelModal = this._translateService.instant(
        'FormConfiguration.PopUp.TitleModalEdit'
      );
    } else {
      this.titelModal = this._translateService.instant(
        'FormConfiguration.PopUp.TitleModalCreate'
      );
    }
    this.modalDialog.open(this.editNewPopUpModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  closeModal(event: boolean) {
    this.form.reset();
    this.form.get('bActive')?.setValue(true);
    this.form.get('bIsSave')?.setValue(true);
    this.form.get('pkiIdPopUp')?.setValue(0);
    this.form.get('fkiIdFormModule')?.setValue(this.idForm);
    this.action = '';
  }

  createPopUp() {
    if (this.valid) {
      let payload: PopUpModel = this.form.getRawValue();
      this._moduleService
        .registerPopUp(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageCreated'
                )
              );
              this.getPopUpList(this.idForm);
              this.modalDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  editPopUp() {
    if (this.valid) {
      let payload: PopUpModel = this.form.getRawValue();
      this._moduleService
        .updatePopUp(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageUpdated'
                )
              );
              this.getPopUpList(this.idForm);
              this.modalDialog.closeAll();
              this.closeModal(true);
            }
          }
        });
    }
  }

  deletePopUp() {
    this._msgSvc
      .messageConfirmationAndNegationReverseButton(
        this._translateService.instant(
          'FormConfiguration.ProgressBar.DeleteMessageConfirm'
        ),
        '',
        'warning',
        this._translateService.instant('Cancel'),
        this._translateService.instant('Confirm')
      )
      .then((result) => {
        if (result) {
          this._moduleService
            .deletePopUp(this.form.get('pkiIdPopUp')?.value)
            .pipe(
              catchError((error) => {
                this._msgSvc.messageWaring(
                  this._translateService.instant('Warning'),
                  error.error.message
                );
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
                console.log('El tipo de datos devueltos es un array vacío.');
              } else {
                if (resp.error) {
                  this._msgSvc.messageError(
                    this._translateService.instant('Warning') + resp.message
                  );
                } else {
                  this._msgSvc.messageSuccess(
                    '',
                    this._translateService.instant(
                      'FormConfiguration.ProgressBar.DeleteMessageSuccess'
                    )
                  );
                  this.getPopUpList(this.idForm);
                  this.modalDialog.closeAll();
                  this.closeModal(true);
                }
              }
            });
        }
      });
  }

  changeDependentFieldValue(item: PopUpModel) {
    switch (item.bIsSave) {
      case true:
        return '<span>' + this._translateService.instant('Save') + '</span>';
      case false:
        return (
          '<span>' +
          this._translateService.instant('ModulesSetting.Modules.Update') +
          '</span>'
        );
      default:
        return '<span>' + '' + '</span>';
    }
  }
}
