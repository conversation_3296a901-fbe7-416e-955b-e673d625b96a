import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-report-carrier-insurance',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
        {{ 'ReportLogCarrier.TitleCarrierInsurance' | translate }}
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>
    <router-outlet></router-outlet>
  `,
  styles: [],
})
export class ReportCarrierInsuranceComponent {
  constructor(private _translateService: TranslateService) {}

  logReport: string = this._translateService.instant('ReportLogCarrier.TitleCarrierInsurance');
  cotizar: string = this._translateService.instant('Cotizar');

  sections: { label: string; link: string }[] = [
    { label: this.cotizar, link: '/dashboard' },
    { label: this.logReport, link: '/dashboard/report-log' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.cotizar = this._translateService.instant('Cotizar');
      this.logReport = this._translateService.instant('ReportLogCarrier.TitleCarrierInsurance');
      this.sections[0].label = this.cotizar;
      this.sections[1].label = this.logReport;
    });
  }
}
