import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
@Component({
  selector: 'app-filter-modal-clients',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDialogModule,
    MatIconModule,
    MatSelectModule,
    TranslateModule
  ],
  templateUrl: './filter-modal-clients.component.html',
  styleUrls: ['./filter-modal-clients.component.scss'],
})
export class FilterModalClientsComponent {
  filterForm: FormGroup;

  esEmitido: boolean = false;
  esSinEmitir: boolean = false;
  inactivaQuote: boolean = false;
  activaQuote: boolean = false;
  abierto: boolean = false;
  cerrado: boolean = false;
  enCurso: boolean = false;

  isProceure: boolean = false;
  isProduct: boolean = false;
  isQuote: boolean = false;

  sinEmitirString: string = 'SIN EMITIR';
  emitidoString: string = 'EMITIDO';
  activoString: string = 'ACTIVO';
  inactivoString: string = 'INACTIVO';
  abiertoString: string = 'ABIERTO';
  cerradoString: string = 'CERRADO';
  enCursoString: string = 'EN CURSO';

  constructor(
    public dialogRef: MatDialogRef<FilterModalClientsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private _translateService: TranslateService
  ) {
    if (data.states && Array.isArray(data.states)) {

      data.states.forEach((element: string) => {

        if (element.toUpperCase() == this.sinEmitirString) {
          this.esSinEmitir = true;
        }

        if (element.toUpperCase() == this.emitidoString) {
          this.esEmitido = true;
        }

        if (element.toUpperCase() == this.activoString) {
          this.activaQuote = true;
        }

        if (element.toUpperCase() == this.inactivoString) {
          this.inactivaQuote = true;
        }

        if (element.toUpperCase() == this.abiertoString) {
          this.abierto = true;
        }

        if (element.toUpperCase() == this.cerradoString) {
          this.cerrado = true;
        }

        if (element.toUpperCase() == this.enCursoString) {
          this.enCurso = true;
        }
      });
    }

    if (data.isProduct) {
      this.isProduct = true;
    }

    if (data.isProcedure) {
      this.isProceure = true;
    }

    if (data.isQuote) {
      this.isQuote = true;
    }

    this.filterForm = this.fb.group({
      product: [''],
      insurer: [''],
      procedure: [''],
      quoteNumber: 0,
      active: [false],
      inactive: [false],
      emitida: [false],
      sinEmitir: [false],
      abierto: [false],
      cerrado: [false],
      enCurso: [false],
    });
  }

  close(): void {
    this.dialogRef.close();
  }

  applyFilters(): void {
    this.dialogRef.close(this.filterForm.value);
  }
}
