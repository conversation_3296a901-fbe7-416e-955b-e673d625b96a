import { CommonModule } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription, catchError, of } from 'rxjs';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { EditCreateModuleComponent } from './edit-create-module/edit-create-module.component';
import { EditCreateStagesComponent } from './edit-create-stages/edit-create-stages.component';
import { EditCreateSubmoduleComponent } from './edit-create-submodule/edit-create-submodule.component';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ModulesSettingModel } from 'src/app/shared/models/menu/modules-setting-model';
import { WizardModuleSettingModel } from 'src/app/shared/models/modules-setting';
import { CompanyCountryHistoryComponent } from 'src/app/shared/components/company-country-history/company-country-history.component';
import { FileService } from 'src/app/shared/services/file/file.service';
import {
  ImageUpdateUpload,
  ImageUploadModel,
} from 'src/app/shared/models/file';
import { ImageModel } from 'src/app/shared/models/image-picker';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-edit-new-modules-setting',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    WizardComponent,
    MatIconModule,
    MatButtonModule,
    EditCreateSubmoduleComponent,
    EditCreateModuleComponent,
    EditCreateStagesComponent,
    CompanyCountryHistoryComponent,
    TranslateModule,
  ],
  templateUrl: './edit-new-modules-setting.component.html',
  styleUrls: ['./edit-new-modules-setting.component.scss'],
})
export class EditNewModulesSettingComponent implements OnInit, OnDestroy {
  steps: string[] = [
    this._translateService.instant('ModulesSetting.Steps.ModuleData'),
    this._translateService.instant('ModulesSetting.Steps.Submodules'),
    this._translateService.instant('ModulesSetting.Steps.Stages'),
  ];
  // steps: string[] = ['Datos de módulos', 'Submódulos', 'Etapas'];
  currentStep: number = 0;
  buttonNextDisabled: boolean = true;
  private _editNewModulesSettingSubscription?: Subscription;
  positionTwoSteps: string = '';
  pkIIdMenu: number = 0;
  formModuleValue!: ModulesSettingModel;
  objetImageModule!: ImageModel;
  isImagenEdit: boolean = false;

  constructor(
    private _translateService: TranslateService,
    private _modulesSettingService: ModulesSettingService,
    private _messageService: MessageService,
    private _fileService: FileService,
    private _customRouter: CustomRouterService

  ) {}

  ngOnInit(): void {
    this.getDataModulesSettingSubscription();
    this.positionTwoSteps = this.steps[1];
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data steps
      this.steps[0] = this._translateService.instant(
        'ModulesSetting.Steps.ModuleData'
      );
      this.steps[1] = this._translateService.instant(
        'ModulesSetting.Steps.Submodules'
      );
      this.steps[2] = this._translateService.instant(
        'ModulesSetting.Steps.Stages'
      );
    });
  }

  currentStepChange(event: number) {
    this.currentStep = event;
  }

  getDataModulesSettingSubscription() {
    this._editNewModulesSettingSubscription =
      this._modulesSettingService.currentModulesSetting.subscribe({
        next: (response) => {
          if (!(Object.keys(response).length === 0)) {
            if (this.pkIIdMenu === 0) {
              this.pkIIdMenu = response.pkIdMenu;
            }
            switch (this.currentStep) {
              case 0:
                break;
              case 1:
                break;
              default:
                break;
            }
          }
        },
      });
  }

  next(event: string) {}

  createModule() {
    this.modifyProductId();
    if (this.formModuleValue) {
      if (this.formModuleValue.hasChildren) {
        this.formModuleValue.vTag = '/';
      } else {
        this.formModuleValue.vTag = this.generateModulePath(
          this.formModuleValue.vDescription
        );
      }
      this._modulesSettingService
        .createMenu(this.formModuleValue)
        .pipe(
          catchError((error) => {
            if (error.error.message === 'ConflictAlreadyExists') {
              this._messageService.messageWaring(
                '',
                this._translateService.instant(
                  'ModulesSetting.ConflictAlreadyExists'
                )
              );
            }
            else {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this._messageService.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageCreated'
                )
              );
              this.pkIIdMenu = resp.result;
              this.formModuleValue.idMenu = this.pkIIdMenu;
              let payload: WizardModuleSettingModel = {
                pkIdMenu: resp.result,
              };
              this._modulesSettingService.setCurrentEditNewModulesSetting(
                payload
              );
            }
          }
        });
    }
  }

  editModule() {
    this.modifyProductId();
    if (this.formModuleValue) {
      if (this.formModuleValue.hasChildren) {
        this.formModuleValue.vTag = '/';
      } else {
        this.formModuleValue.vTag = this.generateModulePath(
          this.formModuleValue.vDescription
        );
      }
      this._modulesSettingService
        .updateMenu(this.formModuleValue)
        .pipe(
          catchError((error) => {
            if (error.error.message === 'ConflictAlreadyExists') {
              this._messageService.messageWaring(
                '',
                this._translateService.instant(
                  'ModulesSetting.ConflictAlreadyExists'
                )
              );
            }
            else {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this.pkIIdMenu = this.pkIIdMenu;
              this.formModuleValue = this.formModuleValue;
              this._messageService.messageSuccess(
                '',
                this._translateService.instant(
                  'ModulesSetting.SuccessfulMessageUpdated'
                )
              );
            }
          }
        });
    }
  }

  back(event: string) {}

  goToBack() {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Out?'),
        this._translateService.instant('UnsavedSettingsWillBeLost'),
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this._customRouter.navigate(['/dashboard/modules-setting']);
          let payload: WizardModuleSettingModel = {
            pkIdMenu: 0,
          };
          this._modulesSettingService.setCurrentEditNewModulesSetting(payload);
        }
      });
  }

  hasSubmodules(event: boolean) {
    if (event) {
      if (this.steps.length !== 3) {
        this.steps.splice(1, 0, this.positionTwoSteps);
      }
    } else {
      if (this.steps.length === 3) {
        this.steps.splice(1, 1);
      }
    }
  }

  getObjetModule(event: any) {
    this.buttonNextDisabled = !event.formModuleValid;
    this.formModuleValue = event.formModuleValue;
  }

  getObjetImageModule(event: ImageModel) {
    this.objetImageModule = event;
  }

  saveImage() {
    let imageName: string =
      this.objetImageModule.dataJson[0].name.split('.')[0];
    let extension: string =
      this.objetImageModule.dataJson[0].type.split('/')[1];
    let base64: string = this.objetImageModule.dataString.split(',')[1];
    let payload: ImageUploadModel = {
      fileName: `${imageName}.${extension}`,
      fkIIdBusinessByCountry: this.formModuleValue.idBusinessCountry,
      imageBase64: base64,
      pkIIdInsuranceCompanies: 0,
      type: 7,
    };
    if (this.formModuleValue) {
      this._fileService
        .uploadImage(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this.formModuleValue.idFile = resp.result;
              this.createModule();
            }
          }
        });
    }
  }

  editImage() {
    this.modifyProductId();
    if (this.isImagenEdit) {
      let imageName: string =
        this.objetImageModule.dataJson[0].name.split('.')[0];
      let extension: string =
        this.objetImageModule.dataJson[0].type.split('/')[1];
      let base64: string = this.objetImageModule.dataString.split(',')[1];
      let payload: ImageUpdateUpload = {
        fileName: `${imageName}.${extension}`,
        imageBase64: base64,
        isImageEdit: true,
        pkIIdInsuranceCompanies: 0,
        type: 7,
        pkIIdUploadFile: this.formModuleValue.idFile,
      };
      if (this.formModuleValue && payload.pkIIdUploadFile > 0) {
        this._fileService
          .updateUploadImage(payload)
          .pipe(
            catchError((error) => {
              if (error.error.error) {
                this._messageService.messageWaring(
                  this._translateService.instant('ThereWasAError'),
                  error.error.message
                );
              }
              return of([]);
            })
          )
          .subscribe((resp: ResponseGlobalModel | never[]) => {
            if (Array.isArray(resp)) {
            } else {
              if (resp.error) {
              } else {
                this.editModule();
              }
            }
          });
      } else {
        let imageName: string =
          this.objetImageModule.dataJson[0].name.split('.')[0];
        let extension: string =
          this.objetImageModule.dataJson[0].type.split('/')[1];
        let base64: string = this.objetImageModule.dataString.split(',')[1];
        let payload: ImageUploadModel = {
          fileName: `${imageName}.${extension}`,
          fkIIdBusinessByCountry: this.formModuleValue.idBusinessCountry,
          imageBase64: base64,
          pkIIdInsuranceCompanies: 0,
          type: 7,
        };
        if (this.formModuleValue) {
          this._fileService
            .uploadImage(payload)
            .pipe(
              catchError((error) => {
                if (error.error.error) {
                  this._messageService.messageWaring(
                    this._translateService.instant('ThereWasAError'),
                    error.error.message
                  );
                }
                return of([]);
              })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
              if (Array.isArray(resp)) {
              } else {
                if (resp.error) {
                } else {
                  this.formModuleValue.idFile = resp.result;
                  this.editModule();
                }
              }
            });
        }
      }
    } else {
      if (this.formModuleValue) {
        this.editModule();
      }
    }
  }

  getIsImagenEdit(event: boolean) {
    this.isImagenEdit = event;
  }

  modifyProductId() {
    if (this.formModuleValue.idProducts) {
      if (typeof this.formModuleValue.idProducts === 'string') {
      } else {
        let modifiedIds = JSON.stringify(this.formModuleValue.idProducts);
        this.formModuleValue.idProducts = modifiedIds;
      }
    } else {
      this.formModuleValue.idProducts = '[]';
    }
  }

  generateModulePath(moduleName: string): string {
    const lowerCaseModuleName = moduleName.toLowerCase();
    const moduleNameWithHyphens = lowerCaseModuleName.replace(/\s/g, '-');
    const modulePath = `modules/${moduleNameWithHyphens}`;
    return modulePath;
  }

  ngOnDestroy(): void {
    this._editNewModulesSettingSubscription?.unsubscribe();
  }
}
