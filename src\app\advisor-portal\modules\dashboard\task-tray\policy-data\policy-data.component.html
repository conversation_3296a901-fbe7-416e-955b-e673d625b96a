<mat-card class="caratula mt-5">
    <mat-card-header>
      <h4 class="mt-2; mb-3">{{ "Client.Product.PolicyCover" | translate }}</h4> <!-- Carátula de póliza -->
    </mat-card-header>
  
    <div class="row">
      <div class="col-md-10" >
        <div  class="center-pdf">
          <mat-spinner *ngIf="isPDFLoading" diameter="50"></mat-spinner>
        </div>
        <mat-card-content *ngIf="!isPDFLoading">
          <pdf-viewer [src]="pdfSrc" [render-text]="true" [original-size]="true" 
            style="width: 100%; height: 800px"></pdf-viewer>
        </mat-card-content>
      </div>
      <div class="col-md-2">
        <mat-card-actions>
          <button mat-raised-button [disabled]="activePdfBtn" color="accent" style="width: 200px;" (click)="downloadPDF()"><strong>{{ 'Download' |
              translate }} PDF</strong></button>
        </mat-card-actions>
      </div>
    </div>
  </mat-card>
<mat-card class="caratula mt-5" *ngIf="columnsPaymentTable.length > 0 && false">
    <mat-card-header>
      <h4 class="mt-2; mb-3">{{ "Client.Product.Payments" | translate }}</h4>
    </mat-card-header>
    <div class="row m-2" *ngIf="paymentData.length > 0">
      <app-table
        [displayedColumns]="columnsPaymentTable"
        [data]="paymentData"
        [IsStatic]="true"
        [pageIndex]="pageIndex"
        [pageSize]="pageSize"
        [amountRows]="amountRows"
      ></app-table>
    </div>
  </mat-card>