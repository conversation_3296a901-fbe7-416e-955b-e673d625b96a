import { CommonModule } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { ImageModel } from 'src/app/shared/models/image-picker';
import { InsuranceGlobalModel } from 'src/app/shared/models/insurers/insurace-global.model';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-edit-create-insurers',
  standalone: true,
  imports: [
    CommonModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatIconModule,
    GenericImagePickerComponent,
    TranslateModule,
    PreventionSqlInjectorDirective
  ],
  templateUrl: './edit-create-insurers.component.html',
  styleUrls: ['./edit-create-insurers.component.scss'],
})
export class EditCreateInsurersComponent implements OnInit, OnDestroy {
  form: FormGroup = new FormGroup({});
  insurers: InsuranceGlobalModel[] = [];
  operationType: string = '';
  private _settingCountryAndCompanySubscription?: Subscription;
  imageSrc: string = '';

  constructor(
    private _fb: FormBuilder,
    private _parametersService: ParametersService,
    private _insuranceService: InsuranceService,
    private _messageService: MessageService,
    private _activatedRoute: ActivatedRoute,
    public utilsSvc: UtilsService,
    private _settingService: SettingService,
    private _fileService: FileService,
    public _translateService: TranslateService,
    private _customRouter: CustomRouterService

  ) {
    this.initForm();
    this.getInsurersGlobal();
  }

  ngOnInit(): void {
    this.validateAction();
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.form
              .get('fkIIdBusinessByCountry')
              ?.setValue(response.enterprise.pkIIdBusinessByCountry);
          }
        }
      );
    if (this.form.get('fkIIdBusinessByCountry')?.value == 0) {
      this._customRouter.navigate(['/dashboard/insurers/']);
    }
  }

  validateAction() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.id) {
        this.operationType = 'edit';
        this.getInsuranceCompanyById(params.id);
      } else {
        this.operationType = 'create';
      }
    });
  }

  initForm() {
    if (this.operationType === 'create') {
      this.form = this._fb.group({
        pkIIdInsuranceCompanies: [0],
        vName: [null, [Validators.required]],
        vDescription: [null, [Validators.required]],
        vRegistrationNumber: [null, [Validators.required]],
        vCode: [''],
        fkIIdInsuranceGlobal: [null, Validators.required],
        bActive: [true, [Validators.required]],
        vImageName: [null, [Validators.required]],
        vImageBase64: [null, [Validators.required]],
        fkIIdBusinessByCountry: [0],
        fkIIdUploadFile: [0],
      });
    } else {
      this.form = this._fb.group({
        pkIIdInsuranceCompanies: [0],
        vName: [null, [Validators.required]],
        vDescription: [null, [Validators.required]],
        vRegistrationNumber: [null, [Validators.required]],
        vCode: [''],
        fkIIdInsuranceGlobal: [null, Validators.required],
        bActive: [true, [Validators.required]],
        vImageName: [null, [Validators.required]],
        vImageBase64: [null, [Validators.required]],
        fkIIdBusinessByCountry: [0],
        fkIIdUploadFile: [0],
        isImageEdit: [false],
      });
    }
  }

  get valid(): boolean {
    return this.form.valid;
  }

  getInsurersGlobal() {
    this._parametersService.getInsuranceGlobal().subscribe({
      next: (response) => {
        this.insurers = response.result;
      },
    });
  }

  getInsuranceCompanyById(id: number) {
    this._insuranceService.getInsuranceCompanyById(id).subscribe({
      next: (response) => {
        this.form
          .get('pkIIdInsuranceCompanies')
          ?.setValue(response.result.pkIIdInsuranceCompanies);
        this.form.get('vName')?.setValue(response.result.vName);
        this.form.get('vDescription')?.setValue(response.result.vDescription);
        this.form
          .get('vRegistrationNumber')
          ?.setValue(response.result.vRegistrationNumber);
        this.form.get('vCode')?.setValue(response.result.vCode);
        this.form
          .get('fkIIdInsuranceGlobal')
          ?.setValue(response.result.fkIIdInsuranceGlobal);
        this.form.get('bActive')?.setValue(response.result.bActive);
        this.form
          .get('fkIIdBusinessByCountry')
          ?.setValue(response.result.fkIIdBusinessByCountry);
        this.form
          .get('fkIIdUploadFile')
          ?.setValue(response.result.fkIIdUploadFile);
        this.getImage();
      },
    });
  }

  clickSelect(event: boolean) {}

  goBack() {
    this._customRouter.navigate(['dashboard/insurers/']);
  }
  complete() {
    if (this.operationType === 'create') {
      this.createInsurace();
    } else {
      this.editInsurace();
    }
  }

  createInsurace() {
    if (this.valid) {
      this._insuranceService.createInsurace(this.form.value).subscribe({
        next: (response) => {
          if (!response.error) {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              response.message
            );
            this._customRouter.navigate(['/dashboard/insurers/']);
          }
        },
      });
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant(
          'PleaseCompleteAllTheInformationOnTheForm'
        )
      );
    }
  }

  editInsurace() {
    this._insuranceService.updateInsurace(this.form.value).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            response.message
          );
          this._customRouter.navigate(['/dashboard/insurers/']);
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  getImage() {
    let extension: string = '';
    this._fileService
      .getUploadFileById(this.form.get('fkIIdUploadFile')?.value)
      .subscribe({
        next: (response) => {
          if (response.result.vFileName && response.result.imageBase64) {
            extension = response.result.vFileName.split('.');
            extension =
              extension[response.result.vFileName.split('.').length - 1];
            this.imageSrc = `data:image/${extension};base64,${response.result.imageBase64}`;
            this.form.get('vImageName')?.setValue(response.result.vFileName);
            this.form
              .get('vImageBase64')
              ?.setValue(response.result.imageBase64);
          } else {
            this._messageService.messageInfo(
              this._translateService.instant('Warning'),
              this._translateService.instant(
                'ThisIsurerHasNotAssociatedPicture'
              )
            );
          }
        },
      });
  }

  changeImage(event: ImageModel) {
    if (this.operationType === 'edit') {
      this.form.get('isImageEdit')?.setValue(true);
    }
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString.split(',')[1];
    this.form.get('vImageName')?.setValue(`${imageName}.${extension}`);
    this.form.get('vImageBase64')?.setValue(base64);
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
