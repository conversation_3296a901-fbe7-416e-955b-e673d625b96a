import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  TemplateRef,
  ViewChild,
  Input,
  EventEmitter,
  Output,
} from '@angular/core';

import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatRadioModule } from '@angular/material/radio';
import { MatTableDataSource } from '@angular/material/table';
import { of, catchError } from 'rxjs';
import { CdkListbox, CdkOption } from '@angular/cdk/listbox';
import {
  CdkDrag,
  CdkDragDrop,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { DatePipe } from '@angular/common';

import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { PlanPremiumValuesModel } from 'src/app/shared/models/plans/plan-premium-values.model';
import { FieldModelCreate } from 'src/app/shared/models/form/form.model';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TableDragComponent } from 'src/app/shared/components/table-drag/table-drag.component';
import { PlansService } from 'src/app/shared/services/plans/plans.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

@Component({
  selector: 'app-plan-premium',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatRadioModule,
    FormsModule,
    ReactiveFormsModule,
    MatChipsModule,
    MatDialogModule,
    CdkOption,
    CdkListbox,
    CdkDrag,
    CdkDropList,
    MatDatepickerModule,
    TableDragComponent,
    TranslateModule,
    MatDividerModule,
    PreventionSqlInjectorDirective,
    MatSlideToggleModule
  ],
  templateUrl: './plan-premium.component.html',
  styleUrls: ['./plan-premium.component.scss'],
})
export class PlanPremiumComponent implements OnInit {
  @Input() planId: number = 0;
  @Input() idProducto: number = 0;
  @Input() pkIdPlanPremiumValue: number = 0;
  @Input() typePremiums: number = 0;
  @Input() isCalculated: boolean = false;
  @Input() isShowDecimals: boolean = false;
  @Input() arrayDataPremiumValues: PlanPremiumValuesModel[] = [];
  @Output() getPlanById = new EventEmitter<number>();

  formPlanPremium: FormGroup = new FormGroup({});
  fieldsPar: Array<FieldModelCreate> = [];
  formulas: any[] = [];
  listPlanPremium: PlanPremiumValuesModel[] = [];
  dataPlanPremium: PlanPremiumValuesModel[] = [];
  listAllPlanPremiums: PlanPremiumValuesModel[] = [];
  titelModal: string = '';
  isEditTypeList: boolean = false;
  isEdit: boolean = false;
  titleModal: string = '';
  isButtonCalculated: boolean = false;

  elements: any[] = [
    { vNameField: 'IF' },
    { vNameField: 'AND' },
    { name: 'OR' },
    { vNameField: '=' },
    { vNameField: '>' },
    { vNameField: '<' },
    { vNameField: '*' },
    { vNameField: '/' },
    { vNameField: '+' },
    { vNameField: '-' },
    { vNameField: '(' },
    { vNameField: ')' },
    { vNameField: '{' },
    { vNameField: '}' },
  ];

  estructPlansPremiumTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('PlanPremium.Order'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('PlanPremium.NoRule'),
      columnValue: 'pkIIdPlanPremiumValue',
    },
    {
      columnLabel: this._translateService.instant('PlanPremium.Description'),
      columnValue: 'vDescription',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'edit',
      columnIcon: 'create',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];

  constructor(
    private _fb: FormBuilder,
    public _fieldSvc: FieldService,
    private _planService: PlansService,
    private _translateService: TranslateService,
    private _messageService: MessageService,
    private _msgSvc: MessageService,
    private _utilsSvc: UtilsService,
    private datePipe: DatePipe,
    public modalDialog: MatDialog,
    public _utilsService: UtilsService,
  ) { }

  ngOnInit(): void {
    this.validationInitial();
    this.getFieldsByProductId(this.idProducto);
    this.getPlanPremiumValueById(this.pkIdPlanPremiumValue);
    this.formPlanPremium = this._fb.group({
      pkIIdPlanPremiumValue: [this.pkIdPlanPremiumValue],
      fkIIdPlan: [this.planId],
      fkIIdPremiumType: [this.typePremiums],
      vValue: ['', Validators.required],
      bIsCalculated: [this.isCalculated],
      vExpression: [''],
      iOrder: [0],
      vDescription: [''],
      vfieldtext: [''],
      vfieldnumber: [0],
      vfielddate: [''],
      vSymbol: ['$', Validators.required],
      bIsShowDecimals: [this.isShowDecimals]
    });

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table plan premiums
      this.estructPlansPremiumTable[0].columnLabel =
        this._translateService.instant('PlanPremium.Order');
      this.estructPlansPremiumTable[1].columnLabel =
        this._translateService.instant('PlanPremium.NoRule');
      this.estructPlansPremiumTable[2].columnLabel =
        this._translateService.instant('PlanPremium.Description');
      this.estructPlansPremiumTable[3].columnLabel =
        this._translateService.instant('Modify');
      this.estructPlansPremiumTable[4].columnLabel =
        this._translateService.instant('Delete');
    });
  }

  ngAfterViewInit() {
    this.formPlanPremium.get('bIsCalculated')?.setValue(0);
  }

  ///This event performs the initial validations of the form  ///
  validationInitial() {
    if (this.typePremiums == 1)
      this.titleModal = this._translateService.instant('Plan.PremiumYear');
    else if (this.typePremiums == 5)
      this.titleModal = this._translateService.instant('Plan.PremiumOnly');
    else
      this.titleModal = this._translateService.instant('Plan.PremiumMonthly');

    if (this.isCalculated) this.isButtonCalculated = false;
    else this.isButtonCalculated = true;
  }

  changeOptionsPremium(event: boolean) {
    if (!Boolean(Number(event))) {
      this.formPlanPremium.get('bIsCalculated')?.setValue(0);
      if (this.formPlanPremium.get('pkIIdPlanPremiumValue')?.value == 0) {
        this.formPlanPremium.get('pkIIdPlanPremiumValue')?.setValue('');
        this.pkIdPlanPremiumValue = 0;
      }

      if (this.listAllPlanPremiums.length > 0) {
        let result = this.listAllPlanPremiums.filter(
          (value) => value.bIsCalculated == false
        );
        result.forEach((item) => {
          this.formPlanPremium.get('pkIIdPlanPremiumValue')?.setValue(item.pkIIdPlanPremiumValue);
          this.formPlanPremium.get('vValue')?.setValue(item.vValue);
          this.formPlanPremium.get('vSymbol')?.setValue(item.vSymbol);
        });
      }

      this.isButtonCalculated = true;

      this.checkValidator(true);
    } else {
      this.formPlanPremium.get('bIsCalculated')?.setValue(1);
      if (this.arrayDataPremiumValues != undefined)
        if (this.arrayDataPremiumValues.length == 0)
          this.formPlanPremium.get('vValue')?.setValue('');

      this.formPlanPremium.get('pkIIdPlanPremiumValue')?.setValue('');
      this.getPlanPremiumValueByIdPlan(this.planId, this.typePremiums);
      this.isButtonCalculated = false;

      this.checkValidator(false);
    }
  }

  checkValidator(isRequired: boolean) {
    const value = this.formPlanPremium.get('vValue');
    const vSymbol = this.formPlanPremium.get('vSymbol');
    if (isRequired) {
      value?.setValidators(Validators.required);
      vSymbol?.setValidators(Validators.required);
    }
    else {
      value?.clearValidators();
      vSymbol?.clearValidators();
      value?.updateValueAndValidity();
      vSymbol?.updateValueAndValidity();
    }
  }

  getFieldsByProductId(idProduct: number) {
    this._fieldSvc
      .getFieldsByProductId(idProduct)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.fieldsPar = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.fieldsPar = resp.result;
          }
        }
      });
  }

  getPlanPremiumValueByIdPlan(idPlan: number, idPremiumType: number) {
    this._planService.getFieldsByProductIdPlan(idPlan, idPremiumType).pipe(
      catchError((error) => {
        if (
          !error.error.error &&
          error.error.message === 'NoExist'
        ) {
          this._msgSvc.messageInfo(
            "",
            this._translateService.instant(
              'PlanPremium.NoExistPlanPremiumCalculated'
            )
          );
        } else {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
        }
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
        this.listPlanPremium = [];
        this.clear();
        this.formPlanPremium.get('pkIIdPlanPremiumValue')?.setValue('');
        this.getPlanById.emit(this.planId);

      } else {
        if (resp.error) {
          this._msgSvc.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.listPlanPremium = resp.result;

          if (this.listPlanPremium.length > 0)
            this.isEditTypeList = true;
        }
      }
    });
  }

  getPlanPremiumValueById(idPlanPremiumValue: number) {
    this._planService.getPlanPremiumValueById(idPlanPremiumValue).pipe(
      catchError((error) => {
        if (
          error.error.error &&
          error.error.message === 'NoExist'
        ) {
          this._msgSvc.messageInfo(
            "",
            this._translateService.instant(
              'PlanPremium.NoExistPlanPremiumCalculated'
            )
          );
        } else {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
        }
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log('El tipo de datos devueltos es un array vacío.');
      } else {
        if (resp.error) {
          this._msgSvc.messageError(
            this._translateService.instant('ThereWasAError') + resp.message
          );
        } else {
          if (resp.result != null) {
            this.formPlanPremium.get('vValue')?.setValue(resp.result.vValue);
            this.formPlanPremium.get('vSymbol')?.setValue(resp.result.vSymbol);
            this.formPlanPremium
              .get('pkIIdPlanPremiumValue')
              ?.setValue(resp.result.pkIIdPlanPremiumValue);
            this.formPlanPremium
              .get('fkIIdPremiumType')
              ?.setValue(resp.result.fkIIdPremiumType);
            this.formPlanPremium
              .get('bIsCalculated')
              ?.setValue(Number(resp.result.bIsCalculated));
            this.formPlanPremium.get('bIsShowDecimals')?.setValue(resp.result.bIsShowDecimals);

            if (resp.result.bIsCalculated)
              this.getPlanPremiumValueByIdPlan(this.planId, this.formPlanPremium.get('fkIIdPremiumType')?.value);
            this.getListAllPlanPremiumsById(this.planId, this.formPlanPremium.get('fkIIdPremiumType')?.value);
            this.changeOptionsPremium(this.formPlanPremium.get('bIsCalculated')?.value);
            this.changeDecimalValues();
          }
        }
      }
    });
  }

  getListAllPlanPremiumsById(idPlan: number, idPlanPremium: number) {
    this._planService.getListAllPlanPremiumsById(idPlan, idPlanPremium)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.listAllPlanPremiums = response.result;
          }
        }
      });
  }

  dropField(event: CdkDragDrop<any[]>) {
    moveItemInArray(this.formulas, event.previousIndex, event.currentIndex);
  }

  listPlanPremiumToUpdate(event: MatTableDataSource<any>) {
    this.listPlanPremium = event.filteredData.slice();
    this.isEditTypeList = true;
    this.isButtonCalculated = true;
  }

  controller(event: IconEventClickModel) {
    if (event.column === 'edit') {
      this.formulas = [];
      this.formPlanPremium
        .get('pkIIdPlanPremiumValue')
        ?.setValue(event.value.pkIIdPlanPremiumValue);
      this.formPlanPremium
        .get('fkIIdPremiumType')
        ?.setValue(event.value.fkIIdPremiumType);
      this.formPlanPremium.get('vValue')?.setValue(event.value.vValue);
      this.formPlanPremium
        .get('bIsCalculated')
        ?.setValue(Number(event.value.bIsCalculated));
      this.formPlanPremium.get('iOrder')?.setValue(event.value.iOrder);
      this.formPlanPremium
        .get('vDescription')
        ?.setValue(event.value.vDescription);
      this.formPlanPremium
        .get('vExpression')
        ?.setValue(event.value.vExpression);
      this.convertStringToArray(event.value.vExpression);
      this.isEdit = true;
      this.isEditTypeList = false;
    }
    if (event.column === 'delete') {
      if (event.value.pkIIdPlanPremiumValue) {
        this.deletePlanPremiumValues(event.value.pkIIdPlanPremiumValue);
      }
    }
  }

  addFieldforFormula($event: any) {
    // Add our Formulas by Field
    if (($event.vNameField || '').trim()) {
      this.formulas.push({
        vNameField: $event.vNameField.trim(),
        id:
          '[' +
          $event.pkIIdField +
          '_' +
          $event.vNameField.trim().replace(' ', '~') +
          ']',
      });
    }
  }

  addElementsforFormula($event: any) {
    // Add our Formulas by Elements
    if (($event.vNameField || '').trim()) {
      this.formulas.push({
        vNameField: $event.vNameField.trim(),
        id: $event.vNameField.trim(),
      });
    }
  }

  addNewFields($event: any) {
    // Add our Formulas by new field
    if (($event.vfieldtext || '').trim()) {
      this.formulas.push({
        vNameField: $event.vfieldtext.trim(),
        id: '^' + $event.vfieldtext.trim() + '^',
      });
      this.formPlanPremium.get('vfieldtext')?.setValue('');
    }

    if ($event.vfieldnumber || '') {
      this.formulas.push({
        vNameField: $event.vfieldnumber,
        id: '(parseFloat(' + $event.vfieldnumber + '))',
      });
      this.formPlanPremium.get('vfieldnumber')?.setValue('');
    }

    if ($event.vfielddate || '') {
      let date = this.datePipe.transform($event.vfielddate, 'yyyy-MM-dd');
      this.formulas.push({ vNameField: date, id: '(Date(' + date + '))' });
      this.formPlanPremium.get('vfielddate')?.setValue('');
    }
  }

  convertStringToArray(expression: string) {
    let arrayIdFormulas = expression.split(' ');
    let nameFormulaReplace = '';
    arrayIdFormulas.forEach((item) => {
      nameFormulaReplace = '';
      if (item !== null && item !== undefined) {
        nameFormulaReplace = item
          .replaceAll('(parseFloat(', '')
          .replaceAll('(Date(', '')
          .replaceAll('^', '')
          .replaceAll('))', '');

        nameFormulaReplace = nameFormulaReplace.replace(
          /\[(\d+)_([^\]]+)\]/g,
          function (fieldName) {
            let nuevoString = fieldName.replace(/^[^_]*_/, '');
            nuevoString = nuevoString
              .replace('~', ' ')
              .replaceAll('[', '')
              .replaceAll(']', '');
            return nuevoString;
          }
        );
      }
      this.formulas.push({ vNameField: nameFormulaReplace.trim(), id: item });
    });
  }

  saveRules() {
    if (this.formulas != undefined) {
      if (this.formulas.length > 0) {
        if (
          this.isEdit &&
          this.formPlanPremium.get('pkIIdPlanPremiumValue')?.value > 0
        ) {
          this.updatePremiumValueCalculated();
        } else {
          this.savePremiumValueCalculated();
        }
      } else {
        this._messageService.messageWaring(
          this._translateService.instant(
            'Para añadir una nueva regla, se debe ingresar una fórmula de cálculo previamente'
          ),
          ''
        );
      }
    }
  }

  savePlanPremiumValues() {
    if (this.formPlanPremium.get('bIsCalculated')?.value) {
      if (
        this.isEdit &&
        this.formPlanPremium.get('pkIIdPlanPremiumValue')?.value > 0
      ) {
        this.updatePremiumValueCalculated();
      } else if (this.isEditTypeList) this.updateCalculatedList();
      else {
        this.clear();
        this.getPlanById.emit(this.planId);
        this.modalDialog.closeAll();
        this._msgSvc.messageInfo(
          this._translateService.instant(
            'PlanPremium.MessageFieldCalculatedRequired'
          ), "");
      }
    } else {
      if (this.formPlanPremium.get('pkIIdPlanPremiumValue')?.value > 0) {
        this.updatePremiumValueFixed();
      } else this.savePremiumValueFixed();
    }
  }

  savePremiumValueFixed() {
    if (this.formPlanPremium.valid) {
      if (this.formPlanPremium.get('vValue')?.value != 0) {
        let payloads: PlanPremiumValuesModel = {
          pkIIdPlanPremiumValue: 0,
          fkIIdPlan: this.planId,
          fkIIdPremiumType: this.typePremiums,
          vValue: this.formPlanPremium.get('vValue')?.value,
          bIsCalculated: this.formPlanPremium.get('bIsCalculated')?.value,
          vExpression: "",
          iOrder: 0,
          vDescription: "",
          vSymbol: this.formPlanPremium.get('vSymbol')?.value,
          bIsShowDecimals: this.formPlanPremium.get('bIsShowDecimals')?.value
        };

        this._planService.registerPlanPremiumValues(payloads).subscribe({
          next: (response) => {
            if (!response.error) {
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                response.message
              );
              this.clear();
              this.getPlanById.emit(this.planId);
              this.modalDialog.closeAll();
            }
          }
        });
      } else {
        this._msgSvc.messageInfo(
          this._translateService.instant(
            'PlanPremium.FieldRequired'
          ), "");
      }
    }
    else {
      this.formPlanPremium.markAllAsTouched();
    }
  }

  savePremiumValueCalculated() {
    let expression = '';
    this.formulas.forEach(function (item, index) {
      if (index > 0) expression += ' ' + item.id;
      else expression += item.id;
    });

    let payloads: PlanPremiumValuesModel = {
      pkIIdPlanPremiumValue: 0,
      fkIIdPlan: this.planId,
      fkIIdPremiumType: this.typePremiums,
      vValue: '',
      bIsCalculated: this.formPlanPremium.get('bIsCalculated')?.value,
      vExpression: expression,
      iOrder: 0,
      vDescription: this.formPlanPremium.get('vDescription')?.value,
      vSymbol: '$',
      bIsShowDecimals: this.formPlanPremium.get('bIsShowDecimals')?.value
    };

    this._planService
      .registerPlanPremiumValues(payloads)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              resp.message
            );
            this.getPlanPremiumValueByIdPlan(this.planId, this.typePremiums);
            this.changeOptionsPremium(
              this.formPlanPremium.get('bIsCalculated')?.value
            );
            this.clear();
            this.formPlanPremium.get('pkIIdPlanPremiumValue')?.setValue('');
            this.isEdit = false;
            this.getPlanById.emit(this.planId);
          }
        }
      });
  }

  updateCalculatedList() {
    this.dataPlanPremium = this.listPlanPremium;
    const showDecimals = this.formPlanPremium.get('bIsShowDecimals')?.value ?? false;

    this.dataPlanPremium = this.dataPlanPremium.map((item: any) => ({
      ...item,
      bIsShowDecimals: showDecimals
    }));
    this._planService
      .updatePlanPremiumValues(this.dataPlanPremium)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              resp.message
            );
            this.getPlanPremiumValueByIdPlan(this.planId, this.typePremiums);
            this.getPlanPremiumValueById(
              this.formPlanPremium.get('pkIIdPlanPremiumValue')?.value
            );
            this.clear();
            this.getPlanById.emit(this.planId);
            this.modalDialog.closeAll();
          }
        }
      });
  }

  updatePremiumValueCalculated() {
    let expression = '';
    if (this.isEditTypeList) {
      this.dataPlanPremium = this.listPlanPremium;
    } else {
      this.formulas.forEach(function (item, index) {
        if (index > 0) expression += ' ' + item.id;
        else expression += item.id;
      });

      this.dataPlanPremium.push({
        pkIIdPlanPremiumValue: this.formPlanPremium.get('pkIIdPlanPremiumValue')
          ?.value,
        fkIIdPlan: this.planId,
        fkIIdPremiumType: this.typePremiums,
        vValue: '',
        bIsCalculated: this.formPlanPremium.get('bIsCalculated')?.value,
        vExpression: expression,
        iOrder: this.formPlanPremium.get('iOrder')?.value,
        vDescription: this.formPlanPremium.get('vDescription')?.value,
        vSymbol: '$',
        bIsShowDecimals: this.formPlanPremium.get('bIsShowDecimals')?.value ?? false
      });
    }

    this._planService
      .updatePlanPremiumValues(this.dataPlanPremium)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              resp.message
            );
            this.getPlanPremiumValueByIdPlan(this.planId, this.typePremiums);
            this.getPlanPremiumValueById(
              this.formPlanPremium.get('pkIIdPlanPremiumValue')?.value
            );
            this.clear();
            this.changeOptionsPremium(
              this.formPlanPremium.get('bIsCalculated')?.value
            );
            this.getPlanById.emit(this.planId);
          }
        }
      });
  }


  updatePremiumValueFixed() {
    if (this.formPlanPremium.valid) {
      if (this.formPlanPremium.get('vValue')?.value != 0) {
        this.dataPlanPremium.push(
          {
            pkIIdPlanPremiumValue: this.formPlanPremium.get('pkIIdPlanPremiumValue')?.value,
            fkIIdPlan: this.planId,
            fkIIdPremiumType: this.typePremiums,
            vValue: this.formPlanPremium.get('vValue')?.value,
            bIsCalculated: this.formPlanPremium.get('bIsCalculated')?.value,
            vExpression: "",
            iOrder: 0,
            vDescription: "",
            vSymbol: this.formPlanPremium.get('vSymbol')?.value,
            bIsShowDecimals: this.formPlanPremium.get('bIsShowDecimals')?.value ?? false
          });
        this._planService.updatePlanPremiumValues(this.dataPlanPremium).subscribe({
          next: (response) => {
            if (!response.error) {
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                response.message
              );
              this.clear();
              this.getPlanById.emit(this.planId);
              this.modalDialog.closeAll();
            }
          }
        });
      } else {
        this._msgSvc.messageInfo(
          this._translateService.instant(
            'PlanPremium.FieldRequired'
          ), "");
      }
    }
    else {
      this.formPlanPremium.markAllAsTouched();
    }
  }

  deletePlanPremiumValues(idPlanPremiumValue: number) {
    this._planService.deletePlanPremiumValues(idPlanPremiumValue).subscribe({
      next: (response) => {
        if (!response.error) {
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            response.message
          );
          this.getPlanPremiumValueByIdPlan(this.planId, this.typePremiums);
          this.getPlanPremiumValueById(
            this.formPlanPremium.get('pkIIdPlanPremiumValue')?.value
          );
          this.clear();
          this.getPlanById.emit(this.planId);
        } else {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            response.message
          );
        }
      },
    });
  }

  removeFieldsFormulas(formula: any): void {
    const index = this.formulas.indexOf(formula);
    if (index >= 0) {
      this.formulas.splice(index, 1);
    }
  }

  clear() {
    this.formPlanPremium.get('pkIIdPlanPremiumValue')?.setValue('');
    this.formPlanPremium.get('vExpression')?.setValue('');
    this.formPlanPremium.get('vDescription')?.setValue('');
    this.formPlanPremium.get('vValue')?.setValue('');
    this.formPlanPremium.get('vSymbol')?.setValue('');
    this.formPlanPremium.get('bIsShowDecimals')?.setValue('');
    this.formulas = [];
    this.isEdit = false;
    this.isEditTypeList = false;
    this.isButtonCalculated = false;
  }

  eventCloseModal(event: boolean) {
    console.log('se cerró el modal');
    this.formPlanPremium.reset();
    this.titelModal = '';
  }

  changeDecimalValues() {
    let valor = this.formPlanPremium.get('vValue')?.value;
    const bIsShowDecimals = this.formPlanPremium.value.bIsShowDecimals;

    if (valor) {
      if (!valor.includes('.')) {
        if (bIsShowDecimals) {
          valor = valor + '.00';
        }
      } else {
        if (!bIsShowDecimals) {
          valor = valor.split('.')[0];
        }

        valor = valor.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }

      this.formPlanPremium.get('vValue')?.setValue(valor);
    }

  }


  onInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value;

    // Eliminar cualquier carácter no válido (permitir solo números, comas y puntos)
    value = value.replace(/[^0-9,\.]/g, '');

    // Si `bIsShowDecimals` es verdadero, permitir el punto como separador decimal
    const bIsShowDecimals = this.formPlanPremium.value.bIsShowDecimals;

    if (!bIsShowDecimals) {
      // Si hay un punto, formatear con comas y punto decimal
      value = this.formatWithCommasAndDot(value);
    } else {
      // Si no hay decimales, formatear solo con comas para miles
      value = this.formatWithCommasOnly(value);
    }

    // Establecer el valor formateado en el input
    input.value = value;

    // Actualizar el valor en el formulario
    this.formPlanPremium.get('vValue')?.setValue(value, { emitEvent: false });
  }

  // Función para formatear con comas como separador de miles y punto como decimal
  formatWithCommasAndDot(value: string): string {
    // Eliminar comas antes de formatear
    let rawValue = value.replace(/,/g, '');

    // Si tiene más de un punto decimal, solo dejar el primero
    if (rawValue.indexOf('.') !== rawValue.lastIndexOf('.')) {
      rawValue = rawValue.substring(0, rawValue.lastIndexOf('.'));
    }

    // Separar la parte entera y decimal
    const [integer, decimal] = rawValue.split('.');

    // Formatear la parte entera con comas
    const formattedInteger = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    // Si existe parte decimal, agregarlo después de la parte entera
    const formattedValue = decimal ? `${formattedInteger}.${decimal}` : formattedInteger;

    return formattedValue;
  }

  // Función para formatear solo con comas como separador de miles (sin decimales)
  formatWithCommasOnly(value: string): string {
    // Eliminar comas antes de formatear
    let rawValue = value.replace(/,/g, '');

    // Si tiene más de un punto decimal, solo dejar el primero
    if (rawValue.indexOf('.') !== rawValue.lastIndexOf('.')) {
      rawValue = rawValue.substring(0, rawValue.lastIndexOf('.'));
    }

    // Formatear la parte entera con comas
    const formattedInteger = rawValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return formattedInteger;
  }

}
