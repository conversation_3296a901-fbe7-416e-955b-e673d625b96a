import {
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatDialog } from '@angular/material/dialog';
import { SectionIndexModel } from 'src/app/shared/models/configuration-index';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { SettingCompanyModel } from 'src/app/shared/models/setting-global';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-organization-chart-home',
  standalone: true,
  imports: [CommonModule, MatButtonModule, Modal2Component, TranslateModule],
  templateUrl: './organization-chart.component.html',
  styleUrls: ['./organization-chart.component.scss'],
})
export class OrganizationChartHomeComponent implements OnInit, OnD<PERSON>roy {
  @Input() organizationChartObject: SectionIndexModel =
    SectionIndexModel.fromObj({});
  @ViewChild('openOrganizationChartModal')
  openOrganizationChartModal?: TemplateRef<any>;
  titelModal: string = '';
  imageSrc: string = '';

  constructor(
    public modalDialog: MatDialog,
    private _settingService: SettingService
  ) {}

  ngOnInit(): void {
    this.getDataSettingInit();
    this.getBase64Format();
  }

  openModal() {
    this.modalDialog.open(this.openOrganizationChartModal!);
  }

  eventCloseModal(event: boolean) {}

  //Setea el base 64 al formato requerido para renderizar la imagen en el DOM.
  getBase64Format() {
    if (
      this.organizationChartObject.vImageBase64 &&
      this.organizationChartObject.vFileName
    ) {
      this.imageSrc = this.organizationChartObject.vImageBase64 = `data:image/${
        this.organizationChartObject.vFileName.split('.')[
          this.organizationChartObject.vFileName.split('.').length - 1
        ]
      };base64,${this.organizationChartObject.vImageBase64}`;
    }
  }

  //Obtiene la información de la configuración seleccionada inicialmente.
  async getDataSettingInit() {
    let data: SettingCompanyModel = await this._settingService.getDataSettingInit();
    this.titelModal = data.businessName || '';
  }

  ngOnDestroy(): void {}
}
