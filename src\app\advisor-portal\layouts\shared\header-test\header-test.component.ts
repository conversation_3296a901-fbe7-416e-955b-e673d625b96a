import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserService } from 'src/app/shared/services/user/user.service';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';

@Component({
  selector: 'app-header-test',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule],
  templateUrl: './header-test.component.html',
  styleUrls: ['./header-test.component.scss'],
})
export class HeaderTestComponent {
  constructor(
    private _userService: UserService,
    public _translateService: TranslateService,
    private _customRouter: CustomRouterService,
    private _settingService: SettingService
  ) {}

  logout() {
    let token = this._userService.getTokenLocalStorage();
    this._userService.logout(token).subscribe({
      next: (response) => {
        if (response) this._userService.removeTokenLocalStorage();
        this._settingService.removeDataSettingInit();
        this._customRouter.deleteNavigationData();
        this._customRouter.navigate([`/auth/login`]);
        localStorage.setItem('autoLoginMSAL', 'false');
      },
    });
  }

  changeCompany() {
    this._customRouter.navigate(['/onboard/choose-company']);
  }

  switchLang = (lang: string) => {
    this._translateService.use(lang);
  };
}
