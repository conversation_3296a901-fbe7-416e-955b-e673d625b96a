<form [formGroup]="form">
  <div class="row mb-2 mb-1">
    <div class="col-md-12">
      <h4>
        {{
          "AnsConfiguration.ModalAns.GeneralsTab.GeneralConditions" | translate
        }}
      </h4>
    </div>
  </div>
  <div class="d-flex">
    <mat-slide-toggle class="mb-3 mx-3" formControlName="bIncludeRestDay">
      {{
        "AnsConfiguration.ModalAns.GeneralsTab.IncludeRestDuringTheDay"
          | translate
      }}
    </mat-slide-toggle>
    <mat-slide-toggle class="mb-3" formControlName="bIncludeHolidays">
      {{ "AnsConfiguration.ModalAns.GeneralsTab.IncludeHoliDays" | translate }}
    </mat-slide-toggle>
  </div>

  <div class="row">
    <!-- Select de Tipo de Días -->
    <div class="col-md-6">
      <mat-form-field id="typeDays" class="w-100">
        <mat-label>
          {{ "AnsConfiguration.ModalAns.GeneralsTab.TypeOfDays" | translate }}
        </mat-label>
        <mat-select formControlName="iTypeDays">
          <mat-option *ngFor="let item of typeDays" [value]="item.Name">{{
            item.Name
          }}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <!-- Select de Zona horaria -->
    <div class="col-md-6">
      <mat-form-field id="timeZone" class="w-100">
        <mat-label>
          {{ "AnsConfiguration.ModalAns.GeneralsTab.TimeZone" | translate }}
        </mat-label>
        <mat-select formControlName="iTimeZone">
          <mat-option *ngFor="let item of timeZone" [value]="item.value">{{
            item.name
          }}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <div formArrayName="daysOfWeek">
    <div *ngFor="let day of daysOfWeek; let i = index" [formGroupName]="i">
      <div class="cont-week-days mt-4 mb-2">
        <div class="cont-check">
          <mat-checkbox formControlName="isChecked" class="">{{
            getDayName(i)
          }}</mat-checkbox>
        </div>
        <div class="cont-select" *ngIf="isDayOfWeekChecked(i)">
          <p class="label">
            {{
              "AnsConfiguration.ModalAns.GeneralsTab.StartOfTheDay" | translate
            }}
          </p>
          <mat-form-field class="w-75">
            <mat-label>{{
              "AnsConfiguration.ModalAns.GeneralsTab.StartOfTheDay" | translate
            }}</mat-label>
            <mat-select formControlName="startOfTheDay">
              <mat-option
                *ngFor="let item of startOfTheDay"
                [value]="item.Name"
                >{{ item.Name }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
        <div class="cont-select" *ngIf="isDayOfWeekChecked(i)">
          <p class="label">
            {{ "AnsConfiguration.ModalAns.GeneralsTab.EndOfDay" | translate }}
          </p>
          <mat-form-field class="w-75">
            <mat-label>{{
              "AnsConfiguration.ModalAns.GeneralsTab.EndOfDay" | translate
            }}</mat-label>
            <mat-select formControlName="endOfTDay">
              <mat-option *ngFor="let item of endOfTDay" [value]="item.Name">{{
                item.Name
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="cont-select" *ngIf="isDayOfWeekChecked(i) && !showRest">
          <p class="label">
            {{ "AnsConfiguration.ModalAns.GeneralsTab.RestStart" | translate }}
          </p>
          <mat-form-field class="w-75">
            <mat-label>{{
              "AnsConfiguration.ModalAns.GeneralsTab.RestStart" | translate
            }}</mat-label>
            <mat-select formControlName="restStart">
              <mat-option *ngFor="let item of restStart" [value]="item.Name">{{
                item.Name
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="cont-select" *ngIf="isDayOfWeekChecked(i) && !showRest">
          <p class="label">
            {{ "AnsConfiguration.ModalAns.GeneralsTab.EndOfRest" | translate }}
          </p>
          <mat-form-field class="w-75">
            <mat-label>
              {{
                "AnsConfiguration.ModalAns.GeneralsTab.EndOfRest" | translate
              }}</mat-label
            >
            <mat-select formControlName="endOfRest">
              <mat-option *ngFor="let item of endOfRest" [value]="item.Name">{{
                item.Name
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>

  <div class="cont-holdays" *ngIf="showCountryHoliday">
    <div class="row">
      <div class="col-md-12 mb-2">
        <h4>
          {{ "AnsConfiguration.ModalAns.GeneralsTab.HoliDays" | translate }}
        </h4>
      </div>
      <div class="col-md-12">
        <mat-form-field class="w-50">
          <mat-label>{{ "MyProfile.Country" | translate }}</mat-label>
          <mat-select formControlName="fkIIdCountryHoliday">
            <mat-option
              *ngFor="let item of countries"
              [value]="item.pk_i_IdCountry"
              >{{ item.v_CountryName }}</mat-option
            >
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>
</form>
