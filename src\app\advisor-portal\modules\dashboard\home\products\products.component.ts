import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { SectionIndexModel } from 'src/app/shared/models/configuration-index';
import { PorductHomeModel } from 'src/app/shared/models/product';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { initNewQuote } from 'src/app/store/actions';
import { AppState } from 'src/app/store/reducers';

@Component({
  selector: 'app-products-home',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss'],
})
export class ProductsHomeComponent implements OnInit, OnDestroy {
  @Input() productsObject: SectionIndexModel = SectionIndexModel.fromObj({});
  idBusinessByCountry: number = 0;
  productList: PorductHomeModel[] = [];
  constructor(
    private _productService: ProductService,
    private _store: Store<AppState>,
    private _msgSvc: MessageService,
    private _translateService: TranslateService,
    private _settingService: SettingService,
    private _customRouter: CustomRouterService,
    private _spinnerService: SpinnerService
  ) {}

  ngOnInit(): void {
    this.getSettingInit();
  }

  async getSettingInit() {
    let dataSettingInit = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = dataSettingInit.idBusinessByCountry;
    this.getProductsByBusinessCountryHome();
  }

  goToQuotation(pkIIdProduct: number) {
    this._store.dispatch(initNewQuote());
    this._customRouter.navigate([`/dashboard/quotation/${pkIIdProduct}`]);
  }

  getProductsByBusinessCountryHome() {
    this._productService
      .getProductsByBusinessCountryHome(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }

          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.productList = resp.result;
          }
        }
      });
  }

  getImage(array: PorductHomeModel[]) {
    let src = '';
    array.forEach((element: PorductHomeModel, index: number) => {
      src = `data:image/${
        element.vFilePath.split('.')[element.vFilePath.split('.').length - 1]
      };base64,${element.imageBase64}`;
      this.productList[index].src = src;
    });
  }
  ngOnDestroy(): void {}
}
