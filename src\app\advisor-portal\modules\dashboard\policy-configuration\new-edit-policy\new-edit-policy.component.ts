import { CommonModule } from '@angular/common';
import {
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  Validators,
  ReactiveFormsModule
} from '@angular/forms';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import {
  MatTabChangeEvent,
  MatTabGroup,
  MatTabsModule,
} from '@angular/material/tabs';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { CompanyCountryHistoryComponent } from 'src/app/shared/components/company-country-history/company-country-history.component';
import {
  InsurancePolicyModel,
  ProductPolicyModel,
  RequestGeneralInfoModel,
  SharedInformationPolicymodel,
} from 'src/app/shared/models/policy';
import { PolicyTypeModel } from 'src/app/shared/models/policy/policy-type.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CoveragesComponent } from './coverages/coverages.component';
import { GeneralInformationComponent } from './general-information/general-information.component';
import { RiskDataComponent } from './risk-data/risk-data.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { RequesFilterPolicyTableModel } from '../../policy/shared/models';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';


@Component({
  selector: 'app-new-edit-policy',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    Modal2Component,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    CompanyCountryHistoryComponent,
    MatCheckboxModule,
    GeneralInformationComponent,
    RiskDataComponent,
    CoveragesComponent,
    MatTooltipModule
  ],
  templateUrl: './new-edit-policy.component.html',
  styleUrls: ['./new-edit-policy.component.scss'],
})
export class NewEditPolicyComponent implements OnInit, OnDestroy {
  //variable formulario.
  form: FormGroup = new FormGroup({});
  @ViewChild(MatTabGroup) tabGroup!: MatTabGroup;
  selectedIndexTab: number = 0;
  idBusinessByCountry: number = 0;
  idCountry: number = 0;
  idForm: number = 0;
  idPolicy: number = 0;
  idUser: number = 0;
  editedFiles: boolean = false;

  informationGeneralFormData = new FormData();
  forminformationGeneralValid: boolean = false;
  formRiskDataValid: boolean = false;

  //listas para los checks
  policyTypes: PolicyTypeModel[] = [];
  selectedPolicyType: number = 0;

// replicar polizas 
 modalForm: FormGroup = new FormGroup({});
 idsPolicies: number[]=[];
 requestFilterPolicy: RequesFilterPolicyTableModel = {
  idBusinessCountry: 0,
  endorsement: '',
  idInsurance: 0,
  keyword: '',
  idProduct: 0,
  statePolicyId: 0,
  order: 'desc',
  page: 0,
  pageSize: 5,
  idtypePolicy: 0,
};
isDisabled:boolean=false;
isPolicyReplicated?:boolean;
policyNumberToReplicate?: number
disabledButton:boolean=false;
newPolicy!:boolean;

@ViewChild('copyExistingParametersModal')
copyExistingParametersModal?: TemplateRef<any>;
titleModal="";
productList: ProductPolicyModel[] = [];
insuranceList: InsurancePolicyModel[] = [];

private localStorageService = inject(LocalStorageService);

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _transactionService: TransactionService,
    private _translateService: TranslateService,
    private _utilService: UtilsService,
    private _policyService: PolicyService,
    private _activatedRoute: ActivatedRoute,
    private _customeRouter: CustomRouterService,
    private _userService: UserService,
    private modalDialog: MatDialog
  ) {}

  async ngOnInit() {
    this.idUser = await this._userService.getUserIdSesion();   
    this.isDisabled=true;  
    this.disabledButton=true;  
    this.getBusinessByCountry();
    this.getAllPolicyType();
  }

  //Obtiene los tramites filtrados por empresa pais y otros filtros opcionales.
  getAllPolicyType() {   
    this._transactionService
      .getAllPolicyType()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.policyTypes = resp.result;
        }
      });
  }

  //Obtiene la lista de tipo de identificación registrados en el sistema.
  getPolicyDetailById(idPolicy: number) {
    this._transactionService
      .getPolicyDetailById(idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          const dataForm: SharedInformationPolicymodel = {
            idForm: resp.result.idForm,
            idPolicy: resp.result.idPolicy,
            idPolicyType: resp.result.idPolicyType,
            filesCreated: resp.result.filesCreated,
          };
          this._policyService.setCurrentPolicyData(dataForm);                                      
        }
      });
  }

  //Dececta el cambio entre tabs.
  onPolicyTypeChange(pkIIdPolicyType: number, event: any) {    
    if (event.checked) {
      const sharedInformation: SharedInformationPolicymodel = {
        idForm: 0,
        idPolicy: 0,
        idPolicyType: pkIIdPolicyType,
      };
      // this.sharedInformation = sharedInformation;
      this._policyService.setCurrentPolicyData(sharedInformation);
      this.selectedPolicyType = pkIIdPolicyType;
      this.isDisabled=false;
      this.disabledButton=false;
    } else {
      this.selectedPolicyType = 0;     
      this.isDisabled=true;
    }
  }

  //Crea una póliza con la información general.
  createPolicy(policy: FormData) {    
    // policy.append('isAReplicate',this.isPolicyReplicated!.toString());
    // policy.append('idPolicyToReplicate',this.policyNumberToReplicate!.toString())
    
    if (this.isPolicyReplicated !== undefined && this.isPolicyReplicated !== null) {
      policy.append('isAReplicate', this.isPolicyReplicated.toString());
    }
    if (this.policyNumberToReplicate !== undefined && this.policyNumberToReplicate !== null) {
      policy.append('idPolicyToReplicate', this.policyNumberToReplicate.toString());
    } 
    this._transactionService.createPolicy(policy)
      .pipe(
        catchError((error) => {
          this.tabGroup.selectedIndex = 0;
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.idForm = resp.result.idForm;
          this.idPolicy = resp.result.idPolicy;
          const result: SharedInformationPolicymodel = {
            idForm: resp.result.idForm,
            idPolicyType: this.selectedPolicyType || 0,
            idPolicy: resp.result.idPolicy,
            filesCreated: resp.result.filesCreated,
          };
          this._policyService.setCurrentPolicyData(result);
          this._messageService.messageSuccess(
            '',
            this._translateService.instant('Saved')
          );
        }
      });
  }

  //Edita una póliza con la información general.
  updatePolicy(policy: FormData) {
    this._transactionService
      .updatePolicy(policy)
      .pipe(
        catchError((error) => {
          this.tabGroup.selectedIndex = 0;
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.idForm = resp.result.idForm;
          this.idPolicy = resp.result.idPolicy;
          this._messageService.messageSuccess(
            '',
            this._translateService.instant('Saved')
          );
          const result: SharedInformationPolicymodel = {
            idForm: resp.result.idForm,
            idPolicyType: this.selectedPolicyType || 0,
            idPolicy: resp.result.idPolicy,
            filesCreated: resp.result.filesCreated,
          };
          this._policyService.setCurrentPolicyData(result);
        }
      });
  }

  cancel() {
    this._customeRouter.navigate([`dashboard/policy-configuration`]);
  }

  continue() {
    const selectedIndex = this.tabGroup.selectedIndex ?? 0;
    const nextIndex = selectedIndex + 1;

    if (nextIndex < this.tabGroup._tabs.length) {
      this.tabGroup.selectedIndex = nextIndex;
      this.selectedIndexTab = nextIndex;
    }
    switch (this.tabGroup.selectedIndex) {
      case 0:
        if (this.idPolicy > 0) {
          this.updatePolicy(this.informationGeneralFormData);
        } else {
          this.createPolicy(this.informationGeneralFormData);
        }
        this.editedFiles = false;
        break;
      case 1:
        break;
      case 2:
        this._customeRouter.navigate([`dashboard/policy-configuration`]);
        this._messageService.messageSuccess(
          this._translateService.instant('Saved'),
          ''
        );
        break;
      default:
        break;
    }
  }

  onTabChange(event: MatTabChangeEvent) {
    this.selectedIndexTab = event.index;
  }

  //Obtiene la información del formulario información general y la convierte en formData.
  getFormGeneralInfo(event: RequestGeneralInfoModel) {
    this.isDisabled=false;
    if (event.IdPolicyType) {
      if (event.IdPolicyType > 0) {
        this.idPolicy = event.IdPolicy;
        this.selectedPolicyType = event.IdPolicyType;
      }
    }
    this.forminformationGeneralValid = event.formValid || false;
    this.formRiskDataValid = event.formValid || false;
    const payload: FormData = new FormData();
    payload.append('IdPolicyType', this.selectedPolicyType.toString() || '');
    payload.append(
      'IdBusinessCountry',
      this.idBusinessByCountry.toString() || ''
    );
    if (this.idPolicy > 0) {
      payload.append('IdPolicy', this.idPolicy.toString());
    }
    if (event.PolicyName) {
      payload.append('PolicyName', event.PolicyName.toString() || '');
    }
    if (event.IdProduct) {
      payload.append('IdProduct', event.IdProduct.toString() || '');
    }
    if (event.IdInsurance) {
      payload.append('IdInsurance', event.IdInsurance.toString() || '');
    }
    if (event.Active) {
      payload.append('Active', event.Active.toString() || '');
    } else {
      payload.append('Active', event.Active.toString() || '');
    }
    if (event.IdValidityType) {
      payload.append('IdValidityType', event.IdValidityType.toString() || '');
    }
    if (event.PolicyNumber) {
      payload.append('PolicyNumber', event.PolicyNumber.toString() || '');
    }
    if (event.StartValidity) {
      payload.append(
        'StartValidity',
        this._utilService.formatDate(event.StartValidity).toString() || ''
      );
    }
    if (event.EndValidity) {
      payload.append(
        'EndValidity',
        this._utilService.formatDate(event.EndValidity).toString() || ''
      );
    }
    if (event.PolicyHolder) {
      payload.append('PolicyHolder', event.PolicyHolder.toString() || '');
    }
    if (event.IdDocumentType) {
      payload.append('IdDocumentType', event.IdDocumentType.toString() || '');
    }
    if (event.DocumentNumber) {
      payload.append('DocumentNumber', event.DocumentNumber.toString() || '');
    }
    if (event.BAssociatedWithCredit) {
      payload.append(
        'BAssociatedWithCredit',
        event.BAssociatedWithCredit.toString() || ''
      );
    } else {
      payload.append(
        'BAssociatedWithCredit',
        event.BAssociatedWithCredit.toString() || ''
      );
    }

    if (event.Files) {
      if (event.Files.length > 0) {
        if (this.idPolicy > 0 && this.editedFiles) {
          const EditedFiles: boolean = true;
          const files: File[] = event.Files;
          if (files && files.length > 0) {
            files.forEach((file) => {
              const fileObject = file;
              payload.append(`Files`, fileObject); // Agregar cada archivo al FormData
            });
            payload.append('EditedFiles', EditedFiles.toString());
          }
        } else {
          const EditedFiles: boolean = false;
          const files: File[] = event.Files;
          if (files && files.length > 0) {
            files.forEach((file) => {
              const fileObject = file;
              payload.append(`Files`, fileObject); // Agregar cada archivo al FormData
            });
            payload.append('EditedFiles', EditedFiles.toString());
          }
        }
      }
    }
    if (event.EditedFiles) {
      payload.append('EditedFiles', event.EditedFiles.toString());
    }
    payload.append(`IdUser`, this.idUser.toString());
    this.informationGeneralFormData = payload;
  }

  getEditedFiles(event: boolean) {
    this.editedFiles = event;
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
      if (params.idCountry) {
        this.idCountry = Number(params.idCountry);
      }
    });
  }

  ngOnDestroy(): void {}

  openModalcopyExistingParameters() {        
    this.initModalForm();
    this.getProductsWithAssociatedPolicies();
    this.getInsurancesWithAssociatedPolicies();    
    this.titleModal = this._translateService.instant('PolicyReplicationConfiguration.ButtonTitle' );
    this.modalDialog.open(this.copyExistingParametersModal!, {
      width: '50vw',
      height: 'auto',
    });
    
  }

  initModalForm() {
    this.modalForm = this._fb.group({
      policyTypesSelect: [{value:0, disabled:true}, [Validators.required]],
      productSelect: [0, [Validators.required]],
      insurancesSelect: [0, [Validators.required]],
      policyIdsSelect: [0, [Validators.required]],      
    });

    if (this.selectedPolicyType>0) {           
      this.modalForm.get('policyTypesSelect')?.setValue(this.selectedPolicyType);
    }                
  }

  getInsurancesWithAssociatedPolicies() {
    this._transactionService.getInsurancesWithAssociatedPolicies(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'),this._translateService.instant('PolicyReplicationConfiguration.AnyPolicyFound'));
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.insuranceList = resp.result;
        }
      });
  }

  getProductsWithAssociatedPolicies() {
    this._transactionService.getProductsWithAssociatedPolicies(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(this._translateService.instant('ThereWasAError'),this._translateService.instant('PolicyReplicationConfiguration.AnyPolicyFound'));
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.productList = resp.result;
        }
      });
  }

  getPoliciesId(){
    this.requestFilterPolicy.idtypePolicy = this.selectedPolicyType;
    this.requestFilterPolicy.idProduct=this.modalForm.get('productSelect')?.value
    this.requestFilterPolicy.idInsurance=this.modalForm.get('insurancesSelect')?.value;
    this.requestFilterPolicy.idBusinessCountry=this.idBusinessByCountry
      
    this._transactionService.getIdWTWsForReplicatedPolicies(this.requestFilterPolicy)
    .pipe(
      catchError((error) => {       
        this._messageService.messageWaring('', error.error.message);      
        return of([]);
      })
    )
    .subscribe((resp: ResponseGlobalModel | never[]) => {    
      if (Array.isArray(resp)) {       
       return
      }     

      if (resp.result.length>0) {
          this.idsPolicies = resp.result 
      }else{
        this._messageService.messageWaring(this._translateService.instant('ThereWasAError'),
                                          this._translateService.instant('AnsConfiguration.Reports.MessageReportNotFound'));
        this.resetSelects();
      }                               
    }); 
  }

  resetSelects() {        
    this.modalForm.get('insurancesSelect')?.setValue(0);
    this.idsPolicies=[];
  }

  clonePolicyById() {    
   const policyId = this.modalForm.get('policyIdsSelect')?.value;   
   this.getPolicyDetailById(policyId);   
   this.localStorageService.setItem('cloneFlag','True');
   this.isPolicyReplicated=true;   
   this.policyNumberToReplicate = policyId;   
   this.newPolicy = true;         
  }

  onModalClose() {    
    this.localStorageService.removeItem('cloneFlag')
  }
}
