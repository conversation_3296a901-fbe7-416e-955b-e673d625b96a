import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PageSeeDetailsColectiveIndividualPolicyComponent } from './page-see-details-colective-individual-policy.component';

describe('PageSeeDetailsColectiveIndividualPolicyComponent', () => {
  let component: PageSeeDetailsColectiveIndividualPolicyComponent;
  let fixture: ComponentFixture<PageSeeDetailsColectiveIndividualPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ PageSeeDetailsColectiveIndividualPolicyComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PageSeeDetailsColectiveIndividualPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
