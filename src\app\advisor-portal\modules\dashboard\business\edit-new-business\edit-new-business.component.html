<div class="row">
  <h3 class="h4">
    {{'Business.BusinessData'| translate}}
  </h3>
  <form action="" id="business" [formGroup]="form">
    <div class="data mt-5">

      <div class="row">
        <!-- estado -->
        <div class="cont-mat-slide-toggle mb-2 col-md-4">
          <div class="mt-2">
            <mat-slide-toggle class="mb-3" formControlName="b_Active" (change)="slideStatus_Change($event)">
              {{'Business.ActiveBusiness'|translate}}
            </mat-slide-toggle>
          </div>
          <div style="margin-top: -3px; margin-left: 10px;">
            <mat-icon class="click" matTooltipPosition="right"
              matTooltip=" {{'Tooltips.ActiveCompany' | translate}}">help_outline</mat-icon>
          </div>
        </div>

        <!-- estado -->
        <div class="cont-mat-slide-toggle mb-2 col-md-4">
          <div class="mt-2">
            <mat-slide-toggle class="mb-3" formControlName="b_EnableClientPortal"
              (change)="slideStatusChangePortalClient($event)">
              {{'Business.EnableCustomerPortalForThisCompany'|translate}}
            </mat-slide-toggle>
          </div>
          <div style="margin-top: -3px; margin-left: 10px;">
            <mat-icon class="click" matTooltipPosition="right"
              matTooltip=" {{'Tooltips.EnableCustomerPortalForThisCompany' | translate}}">help_outline</mat-icon>
          </div>
        </div>

      </div>

      <div class="row col-12">

        <div class="col-lg-7 col-12">

          <!-- nombre empresa -->
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'Business.Name'|translate}}
            </mat-label>
            <input matInput formControlName="v_Name" PreventionSqlInjector />
            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'v_Name', 'required')">
              {{'ThisFieldIsRequired'|translate}}
            </mat-error>
          </mat-form-field>

          <!-- pais -->
          <mat-form-field appearance="outline" class="select-look w-100">
            <mat-label>
              {{ "SelectCountry" | translate }}
            </mat-label>
            <mat-select formControlName="country" #country required>
              <mat-option *ngFor="let country of countries" [value]="country.pk_i_IdCountry">
                {{ country.v_CountryName }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'country', 'required')">
              {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
          </mat-form-field>

          <!-- numero identificacion -->
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'Business.IdentificationNumber'|translate}}
            </mat-label>
            <input matInput formControlName="v_Document" PreventionSqlInjector />
            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'v_Document', 'required')">
              {{'ThisFieldIsRequired'|translate}}
            </mat-error>
          </mat-form-field>

          <!-- razon social -->
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'Business.BusinessName'|translate}}
            </mat-label>
            <input matInput formControlName="v_BusinessName" PreventionSqlInjector />
            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'v_BusinessName', 'required')">
              {{'ThisFieldIsRequired'|translate}}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'Business.SubDomainAdvisorPortal'|translate}}
            </mat-label>
            <input matInput formControlName="v_SubDomainAdvisorPortal" />
            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'v_SubDomainAdvisorPortal', 'required')">
              {{'ThisFieldIsRequired'|translate}}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-100 mb-2" *ngIf="form.value.b_EnableClientPortal">
            <mat-label>
              {{'Business.SubDomainClientPortal'|translate}}
            </mat-label>
            <input matInput formControlName="v_SubDomainClientPortal" />
            <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'v_SubDomainClientPortal', 'required')">
              {{'ThisFieldIsRequired'|translate}}
            </mat-error>
          </mat-form-field>

          <div class="form-check mb-3" *ngIf="form.value.b_EnableClientPortal">
            <input class="form-check-input" type="checkbox" id="b_HasLogin"
                   (change)="checkHasLogin_Change($event)" [checked]="hasLogin_Checked"/>
            <label class="form-check-label" for="flexCheckDefault">
              {{'Business.HasLogin' | translate}}
            </label>
          </div>

          <div class="form-check mb-3" *ngIf="form.value.b_EnableClientPortal">
            <input class="form-check-input" type="checkbox" id="b_HasRegister"
                   (change)="checkHasRegister_Change($event)" [checked]="hasRegister_Checked" />
            <label class="form-check-label" for="flexCheckDefault">
              {{'Business.HasRegister' | translate}}
            </label>
          </div>

          <div class="form-check mb-3" *ngIf="form.value.b_EnableClientPortal">
            <input class="form-check-input" type="checkbox" id="b_HasButtonIwantIt"
                   (change)="checkHasButtonWantIt_Change($event)" [checked]="hasButtonIWantIt_Checked" />
            <label class="form-check-label" for="flexCheckDefault">
              {{'Business.HasButtonIWantIt' | translate}}
            </label>
          </div>


          <!-- check asosiada a grupo empresarial -->
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="checkBusinessGroup"
                   (change)="checkBusinessGroup_Change($event)" [checked]="businessGroup_Checked" />
            <label class="form-check-label" for="flexCheckDefault">
              {{'Business.BusinessAssociatedWithBusinessGroup' | translate}}
            </label>
            <mat-icon class="click" matTooltipPosition="right"
                      matTooltip="{{ 'Tooltips.CheckBusinessGroup' | translate }}">help_outline</mat-icon>
          </div>

          <!-- select grupo empresarial, se carga solo si se activa el check -->
          <div *ngIf="businessGroup_Checked" class="row">
            <mat-form-field appearance="outline" class="select-look w-100">
              <mat-label>
                {{'Business.BusinessGroup' | translate}}
              </mat-label>
              <mat-select formControlName="fk_i_IdBusinessGroup" id="fk_i_IdBusinessGroup">
                <mat-option *ngFor="let businessGroup of businessGroups" [value]="businessGroup.pk_i_IdBusinessGroup">
                  {{businessGroup.v_BusinessGroupName}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="_utilsSvc.isControlHasError(form, 'fk_i_IdBusinessGroup', 'required')">
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>

            <!-- new business group button -->
            <div class="row">
              <button class="mx-3 mt-2 col-4 w-auto" type="button" (click)="openEditNewBusinessGroupDialog()"
                      mat-raised-button color="primary">
                {{'Business.AddBusinessGroup' | translate}}
                <img src="assets/img/utils/plus.svg" alt="">
              </button>
            </div>
          </div>

        </div>

        <!-- selector de imagen, logo -->
        <div class="col-lg-5 col-12 mb-5">

          <app-generic-image-picker class="col-12 col-md-5" [title]="'Business.BusinessLogo'| translate"
            (changeFile)="logoImage_Change($event)" [imageSrc]="imageSrc.length > 0 ? imageSrc[0].src : null">
          </app-generic-image-picker>

          <div class="mt-5" *ngIf="form.value.b_EnableClientPortal">
            <app-generic-image-picker class="col-12 col-md-5" [title]="'Business.ClientPortalLogo'| translate"
              (changeFile)="logoImageClientPortalLogo_Change($event)" [imageSrc]="imageSrcClientPortalLogo.length > 0 ? imageSrcClientPortalLogo[0].src : null" >
            </app-generic-image-picker>

          </div>

        </div>

        <!-- Slider Image Picker -->
        <div class="row mt-5" *ngIf="form.value.b_EnableClientPortal">
          <br>
          <br>
          <h4>{{'Business.SelectUpToThreeImagesForTheClientPortalHomeSlider'| translate}}</h4>
          <div *ngFor="let image of sliderImages; let i = index" class="col-4">
            <app-generic-image-picker class="col-12" [title]="'Business.SliderImage' +' '+ (i+1) +'/3' | translate"
              [imageSrc]="image.imageSrc?? ''" (changeFile)="onSliderImageChange($event, i)">
            </app-generic-image-picker>

            <!-- Delete Button to remove the image -->
            <button *ngIf="sliderImages.length > 1" type="button" mat-button color="warn"
              (click)="removeSliderImage(i)">
              {{'Business.RemoveImage' | translate}}
            </button>
          </div>

          
        </div>


      </div>
    </div>
  </form>
</div>

<div class="row mt-5">
  <div class="col-12 col-md-8 d-flex gap-3 flex-column flex-lg-row justify-content-between mt-3">
    <button class="w-auto" mat-raised-button type="button" style="
        background-color: transparent;" (click)="goBack_Click()">
      <mat-icon style="margin-right: 10px;" fontIcon="arrow_back"></mat-icon>
      {{'Business.ReturnToBusiness'|translate}}
    </button>

    <div class="d-flex gap-3 flex-column flex-lg-row" *ngIf="operationType === 'create'">
      <button class="w-auto" type="button" mat-raised-button color="primary" (click)="saveBusiness_Click()">
        {{ "Business.CreateBusiness" | translate }}
      </button>
    </div>
    <div class="d-flex gap-3 flex-column flex-lg-row" *ngIf="operationType === 'edit'">
      <button class="w-auto mr-2" type="button" mat-raised-button color="primary" (click)="saveBusiness_Click()">
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "SaveChanges" | translate }}
      </button>
    </div>
  </div>
</div>


<!-- modal crear editar grupo empresarial -->
<ng-template #editNewBusinessGroupModal>
  <app-modal [titleModal]="'Business.NewBusinessGroup'| translate">
    <app-edit-new-business-group (submitOut)="getSubmintData($event)" [dataBusinessGroupId]="dataBusinessGroupId">
    </app-edit-new-business-group>
  </app-modal>
</ng-template>
<!-- end modal crear editar grupo empresarial -->
