import { Component, Input, OnChanges, OnInit, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { BodyTableModel } from 'src/app/shared/models/table/body-table.model';
import { IconEventClickModel } from 'src/app/shared/models/table';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { Subscription, catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { StageModel } from 'src/app/shared/models/module';
import { ModulesSettingModel } from 'src/app/shared/models/menu/modules-setting-model';
import { ModulesSettingService } from 'src/app/shared/services/module-setting/modules-setting.service';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatDialog } from '@angular/material/dialog';
import { EditNewStageComponent } from './edit-new-stage/edit-new-stage.component';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { AllMenuModel } from 'src/app/shared/models/menu/all-menu.model';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-edit-create-stages',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    ModalComponent,
    TranslateModule,
    TableComponent,
    MatIconModule,
    Modal2Component,
    EditNewStageComponent,
    ChooseCountryAndCompanyComponent,
    MatTooltipModule
  ],
  templateUrl: './edit-create-stages.component.html',
  styleUrls: ['./edit-create-stages.component.scss']
})
export class EditCreateStagesComponent implements OnInit, OnChanges {

  private _settingCountryAndCompanySubscription?: Subscription;
  // idMenu que se acaba de crear o editar
  @Input() idMenuIn: number = 0;
  //modulo que se está creando y o editando
  @Input() objetModule!: ModulesSettingModel;
  //modal crear/editar etapa
  @ViewChild('editNewStageModal') editNewStageModal?: TemplateRef<any>;
  //modal replicar estapas
  @ViewChild('cloneStageModal') cloneStageModal?: TemplateRef<any>;

  //id menu y producto qie se envian a la vista crear/editar según correspondan
  idProductModuleOut: number = 0;
  //idStage que se envía en caso de que se valla a editar. 
  idStageOut: number = 0;

  subModulesList: any[] = []
  productsList: any[] = [];
  modulesList: AllMenuModel[] = [];
  subModulesListReplicate: any[] = []
  productsListReplicate: any[] = [];
  moduleSelectedHasChildren: boolean = false;
  breadCrumHistory: any = {};
  notProduct: boolean = true;
  notProductReplicate: boolean = true;

  form: FormGroup = new FormGroup({});
  formReplicate: FormGroup = new FormGroup({});

  //boton replicar
  selectedProduct: any;

  estructTableStages: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'Stage.StageName'
      ),
      columnValue: 'vNameStage',
    },
    {
      columnLabel: this._translateService.instant(
        'CreationDate'
      ),
      columnValue: 'dDateCreate',
    },
    {
      columnLabel: this._translateService.instant(
        'Action'
      ),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  dataTableStages: StageModel[] = [];

  constructor(
    private fb: FormBuilder,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _router: Router,
    private _roleService: RoleService,
    private _moduleService: ModuleService,
    private _moduleSettingsService: ModulesSettingService,
    private _settingService: SettingService,
    private _modulesSettingService: ModulesSettingService,
    private _parametersService: ParametersService,
    public _utilsService: UtilsService,
    public _editNewStageDialog: MatDialog,
    public _cloneDialog: MatDialog

  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes["objetModule"]) {
      this.objetModule.hasChildren
        ? (this.form.get("fkIIdSubModule")?.setValidators(Validators.required),
          this.form.get("fkIIdSubModule")?.updateValueAndValidity(),
          this.getSubModules(this.objetModule.idMenu))
        : (this.form.get("fkIIdSubModule")?.clearValidators(),
          this.form.get("fkIIdSubModule")?.updateValueAndValidity(),
          this.getProductByIdMenu(this.objetModule.idMenu),
          this.getAllProcessByIdMenu(this.objetModule.idMenu));
    }
  }

  ngOnInit(): void {
    this.initForm();
    this.initFormReplicate();
    
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.getModules(response.enterprise.pkIIdBusinessByCountry)
          }
        }
      );
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  initForm() {
    this.form = this.fb.group({
      fkIIdSubModule: [''],
      fkIIdProductModule: [''],
      processName: ['']
    });

    
    this.form.get('fkIIdSubModule')?.valueChanges.subscribe({
      next: (data) => {
        if(data.pkIIdMenu)
        this.getProductByIdMenu(data.pkIIdMenu);
      },
    });

    this.form.get('fkIIdProductModule')?.valueChanges.subscribe({
      next: (data) => {
        this.getStages(data.idProductModule)
        this.selectedProduct = data;
      },
    });
  }

  isButtonDisabled(): boolean {
    return !this.selectedProduct;
  }

  initFormReplicate() {
    this.formReplicate = this.fb.group({
      idModule: ['', Validators.required],
      idSubModule: ['', Validators.required],
      idProductModule: ['', Validators.required],
    });
  }

  controller(evt: IconEventClickModel) {
    if (evt.column === 'modify' && !evt.value.bCanModified)
    {
      this._messageService.messageWaring(
        this._translateService.instant('Warning'),
        'Esta etapa no puede ser modificada'
      );
      return
    }
      this.openEditNewStageDialog(evt.value.pkIIdStage)
  }

  validForm(event: any) {
    this.modulesList = [];
    this.subModulesListReplicate = [];
    this.productsListReplicate = [];
    this.formReplicate.patchValue({ idModule: '' })
    this.formReplicate.patchValue({ idSubModule: '' }),
      this.formReplicate.patchValue({ idProductModule: '' })
  }

  openEditNewStageDialog(idStage: number = 0) {
    this.breadCrumHistory = {
      module: this.objetModule.vDescription,
      subModule: this.form.value.fkIIdSubModule.vDescription,
      product: this.form.value.fkIIdProductModule.productName,
      process: this.form.value.processName,
    }
    if (this.form.valid) {
      this.idStageOut = idStage;
      const dialogRef = this._editNewStageDialog.open(
        this.editNewStageModal!
      );
    }
    else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm')
      );
    }
  }

  openCloneDialog() {
    if (this.form.valid) {
      this.modulesList = [];
      const dialogRef = this._cloneDialog.open(this.cloneStageModal!, {
        disableClose: true,
        width: 'auto',
        maxHeight: '90vh'
      });
    }
    else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm')
      );
    }
  }

  cloneStage() {
    this._moduleService
      .cloneStageByIdProductModule(this.formReplicate.value.idProductModule, this.idProductModuleOut)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant('Role.Replicated'),
              ''
            )
            this.getStages(resp.result.id);
            this._cloneDialog.closeAll();
          }
        }
      });
  }

  closedModal(event: any) {
    this._editNewStageDialog.closeAll();
  }

  getFormResult(event: number) {
    event != 0 ? this.getStages(event) : ''
  }

  //obtiene los modulos asignados un grupo/empresa/pais, para replicar una etapa
  getModules(idBusinessByCountry: number) {
    this.formReplicate.patchValue({ idModule: '' })
    this.formReplicate.patchValue({ idSubModule: '' }),
      this.formReplicate.patchValue({ idProductModule: '' })
    this.subModulesListReplicate = [];
    this.productsListReplicate = [];
    this._modulesSettingService
      .getAllParentMenuFromModules(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.modulesList = resp.result;
          }
        }
      });
  }

  //obtiene los submodulos para llenar el select ya sea de replicar o el de la vista principal según corresponda
  getSubModules(idMenu: number, fromReplicate: boolean = false) {
    fromReplicate ? (
      this.subModulesListReplicate = [],
      this.productsListReplicate = [],
      this.formReplicate.patchValue({ idSubModule: '' }),
      this.formReplicate.patchValue({ idProductModule: '' })
    ) : ''
    this._moduleSettingsService
      .getAllChildrenMenuFromModules(idMenu)
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('no hay sub modulos asociados');
          fromReplicate ? (
            this.moduleSelectedHasChildren = false,
            this.productsListReplicate = [],
            this.formReplicate.get('idSubModule')?.clearValidators(),
            this.formReplicate.get('idSubModule')?.updateValueAndValidity(),
            this.getProductByIdMenu(idMenu, true)
          ) : this.subModulesList = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            fromReplicate ?
              (
                this.productsListReplicate = [],
                this.formReplicate.get('idSubModule')?.setValidators(Validators.required),
                this.formReplicate.get('idSubModule')?.updateValueAndValidity(),
                this.moduleSelectedHasChildren = true,
                this.subModulesListReplicate = [...resp.result]
              )
              : (this.subModulesList = [...resp.result])
          }
        }
      });
  }

  //obtiene la lista de productos asignados a un menu y llena el dropdwon de productos ya sea de replicar o el de la vista principal según corresponda
  getProductByIdMenu(idMenu: number, fromReplicate: boolean = false) {
    fromReplicate ? (
      this.productsListReplicate = [],
      this.formReplicate.patchValue({ idProductModule: '' })
    )
    : (
      this.productsList = [],
      this.dataTableStages = []
    )
    this._roleService
      .getProductByIdMenu(idMenu)
      .pipe(
        catchError((error) => {
          console.log(error.message)
          fromReplicate ? this.productsListReplicate = [] : this.productsList = []
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          fromReplicate ? this.productsListReplicate = [] : this.productsList = []
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            resp.result[0].idProduct?(
              fromReplicate ? (
                this.productsListReplicate = resp.result,
                this.notProductReplicate = true
              ):(
                this.productsList = resp.result,
                this.notProduct = true
              )
            ):(
              fromReplicate?(
                this.notProductReplicate = false,
                this.formReplicate.patchValue({idProductModule :resp.result[0].idProductModule})
              ):(
                this.notProduct = false,
                this.selectedProduct = true,
                this.getStages(resp.result[0].idProductModule)
              )
            )
            this.getAllProcessByIdMenu(idMenu);
            //this.verifyNulity();
          }
        }
      });
  }

  //obtiene las etpas asignadas a un idProductModule y llena la tabla de etapas o las duplica según corresponda
  getStages(idProductModule: number, fromReplicate?: boolean) {
    this.idProductModuleOut = 0;
    this.idProductModuleOut = idProductModule;
    this._moduleService.getStageByIdProductModule(idProductModule)
      .pipe(
        catchError((error) => {
          console.log('No existen etapas asociadas')
          this.dataTableStages = [];
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.dataTableStages = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataTableStages = resp.result;
          }
        }
      });

  }

  //obtiene el nombre de un proceso a partir de un idMenu
  getAllProcessByIdMenu(idMenu: number) {
    this._parametersService
      .getAllProcessByIdMenu(idMenu)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.form.patchValue({ processName: resp.result.vNameProcess })
          }
        }
      });
  }

  verifyNulity() {
    var elements = this.productsList.find(x => x.idProduct == null);
    this.notProduct = elements.length == 0;
    if (elements != undefined) {
      this.form.get('fkIIdProductModule')?.setValue(elements)
    }
  }
}
