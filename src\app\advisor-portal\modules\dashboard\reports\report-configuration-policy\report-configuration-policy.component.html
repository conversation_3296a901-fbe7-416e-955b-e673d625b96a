<form [formGroup]="configuration" *ngIf="!isAddorModyReport">
  <div class="row mb-3 mt-3">
    <!--  Reportes Configurados -->
    <h3 class="col-md-12">
      {{ "Reports.Utils.ConfiguredReports" | translate }}
    </h3>
  </div>

  <!-- Tabla Reportes Configurados  -->
  <div class="row mt-3">
    <div class="col-md-12">
      <app-table [displayedColumns]="estructTable" [data]="dataTableReport"
        (iconClick)="controllerReportsGenerated($event)"></app-table>
      <button class="mb-3" type="button" mat-raised-button color="primary" (click)="open('AddReport');">
        {{ "Add" | translate }} {{" "}} {{ "+" }}
      </button>
    </div>
  </div>
</form>



<form [formGroup]="fromConfiguration" *ngIf="isAddorModyReport">
  <div class="row mt-5">

    <div class="col-md-6 col-sm-12 mb-3">
      <!-- Nombre del reporte -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>
          {{ "MyProfile.Name" | translate }}
        </mat-label>
        <input matInput formControlName="vName" PreventionSqlInjector />
        <mat-error *ngIf="utilsSvc.isControlHasError(fromConfiguration, 'vName', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
        <mat-error *ngIf="fromConfiguration.get('vName')?.hasError('nameExists')">
          {{ "NameAlreadyExistsError" | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-md-6 col-sm-12 mb-3">
      <!-- Descripción del reporte -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>
          {{ "Group.Description" | translate }}
        </mat-label>
        <input matInput formControlName="vDescription" PreventionSqlInjector />
        <mat-error *ngIf="utilsSvc.isControlHasError(configuration, 'vDescription', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-md-6 col-sm-12 mb-3">
      <!-- Aseguradora -->
      <mat-form-field class="w-100">
        <mat-label>Aseguradora</mat-label>
        <mat-select formControlName="fkIIdprocess" required="true">
          <mat-option *ngFor="let option of listProcess" [value]="option.pkIIdProcessFamily">{{ option.vNameProcess
            }}</mat-option>
        </mat-select>
        <mat-error *ngIf="fromConfiguration.get('fkIIdprocess')?.hasError('required')">{{ "ThisFieldIsRequired" |
          translate
          }}</mat-error>
      </mat-form-field>
    </div>

    <div class="col-md-6 col-sm-12">
      <!-- Producto aseguradora -->
      <mat-form-field class="w-100">
        <mat-label>{{ "Reports.NewReport.Product" | translate }}</mat-label>
        <mat-select formControlName="fkIIdProduct" multiple>
          <mat-option *ngFor="let option of listProduct" [value]="option">{{ option.vName
            }}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

  </div>


  <!-- Filtros de reporte  -->
  <div class="row mb-3 mt-3">
    <h3 class="col-md-12">
      <!-- {{ "Reports.Utils.ReportFilters" | translate }} -->

      <h4 class="mt-4 mb-0">Campos del reporte.</h4>
      <p class="descrption">Define las características del reporte a generar y los campos a ver.</p>

    </h3>
  </div>

  <div class="row">
    <div class="col-md-9">
      <!-- Seleccionar campo estandar para añadir -->
      <mat-form-field class="w-100">
        <mat-label>{{ "Reports.Utils.selectField" | translate }}</mat-label>
        <mat-select formControlName="fkIdFieldStandar">
          <mat-option *ngFor="let option of dataStaticTableReportFieldStandard" [value]="option.value">{{
            option.vFieldNameStandard }}</mat-option>
        </mat-select>
        <mat-error *ngIf="fromConfiguration.get('fkIdFieldStandar')?.hasError('required')">{{ "ThisFieldIsRequired" |
          translate }}</mat-error>
      </mat-form-field>
    </div>

    <div class="col-md-3">
      <div class="text-center mb-2">
        <!-- Agregar campo estandar -->
        <button [disabled]="fromConfiguration.get('fkIdFieldStandar')?.value < 1" (click)="addStandardFields()"
          class="btn-custom w-75" type="button" mat-raised-button>
          <!-- <strong>{{ "Reports.NewReport.AddField" | translate }}</strong> -->
          <strong>Añadir campo estándar</strong>
          <mat-icon iconPositionEnd class="ml-1">add</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Tabla de campos estandar -->
  <div class="row mb-5">
    <div class="col-12 col-md-12">
      <div class="row mb-2">
      </div>
      <app-table-drag (iconClick)="controllerStandardFields($event)" [displayedColumns]="estructTableField"
        [data]="dataTableReportFieldStandard"></app-table-drag>
    </div>
    <span class="cont-msg-info">
      {{"Reports.Utils.MsgInfoTable" | translate}}
    </span>
  </div>


  <!-- Sección campos dinámicos -->
  <section class="mt-2">
    <ng-template #editTemplate>
      <h3>{{ "Reports.NewReport.EditReport" | translate }}</h3>
    </ng-template>

    <form [formGroup]="fromConfiguration" class="mt-8">
      <div class="row mb-3">
        <!-- Dettalle de reporte -->
        <div class="col-md-12">
          <h4 class="mt-4 mb-0">{{ "Reports.NewReport.ReportDetail" | translate }}.</h4>
          <!-- <p class="descrption">{{ "Reports.NewReport.DefinesCharacteristicsOfReport" | translate }}.</p> -->
          <p class="descrption">Los detalles de reporte solo pueden ser activos seleccionando un único producto.</p>
        </div>
      </div>
      <div class="row mb-3">
        <!-- Activar Detalle Reportes -->
        <div class="col-md-12">
          <mat-checkbox formControlName="CheckDetaillReport">{{"Reports.Utils.ActivateDetailReports" |
            translate}}</mat-checkbox>
        </div>
      </div>
      <div class="row mb-3" *ngIf="isEditDetailReport">
        <!-- Etapa -->
        <div class="col-md-6 col-sm-12">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.Stage" | translate }}</mat-label>
            <mat-select formControlName="fkIdStage" required="true">
              <mat-option *ngFor="let option of stageList" [value]="option">{{ option.vNameStage }}</mat-option>
            </mat-select>
            <mat-error *ngIf="fromConfiguration.get('fkIdStage')?.hasError('required')">{{ "ThisFieldIsRequired" |
              translate
              }}</mat-error>
          </mat-form-field>
        </div>
        <!-- Estado -->
        <div class="col-md-6 col-sm-12">
          <mat-form-field class="w-100">
            <mat-label>{{ "Reports.NewReport.State" | translate }}</mat-label>
            <mat-select formControlName="fkIdState" required="true">
              <mat-option *ngFor="let option of statusListCopyParameters" [value]="option.pkIIdStageByState">{{
                option.vState }}</mat-option>
            </mat-select>
            <mat-error *ngIf="fromConfiguration.get('fkIdState')?.hasError('required')">{{ "ThisFieldIsRequired" |
              translate
              }}</mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="row mb-3" *ngIf="isEditDetailReport">
        <div class="row">
          <!-- Seleccionar campo dinámico para añadir -->
          <div class="col-md-9">
            <mat-form-field class="w-100">
              <mat-label>{{ "Reports.NewReport.Field" | translate }}</mat-label>
              <mat-select formControlName="fkIdField" required="true" (selectionChange)="onFieldChange($event.value)">
                <mat-option *ngFor="let option of listField" [value]="option">{{ option.vNameField }}</mat-option>
              </mat-select>
              <mat-error *ngIf="fromConfiguration.get('fkIdField')?.hasError('required')">{{ "ThisFieldIsRequired" |
                translate }}</mat-error>
            </mat-form-field>
          </div>
          <!-- Añadir campo dinámico -->
          <div class="col-md-3">
            <div class="text-center mb-2">
              <button (click)="addDynamicFields()" class="btn-custom w-75" type="button" mat-raised-button>
                <!-- <strong>{{ "Reports.NewReport.AddField" | translate }}</strong> -->
                <strong>Agregar campo dinámico</strong>
                <mat-icon iconPositionEnd class="ml-1">add</mat-icon>
              </button>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <!-- Tabla campos dinámicos-->
            <app-table-drag *ngIf="isEditDetailReport" (iconClick)="controllerFieldsDinamic($event)"
              [displayedColumns]="estructNewReportsTable" [data]="dataTableReportFieldDinamic"></app-table-drag>
          </div>
        </div>
        <span class="cont-msg-info">
          {{"Reports.Utils.MsgInfoTable" | translate}}
        </span>
      </div>

    </form>
  </section>





  <!-- CONFIGURACION FILTROS ADICIONALES -->
  <section class="mt-2">

    <form [formGroup]="fromConfiguration" class="mt-8">
      <div class="row mb-3">
        <!-- Dettalle de reporte -->
        <div class="col-md-12">
          <h4 class="mt-4 mb-0">Configuración filtros para generar reporte.</h4>
          <!-- <p class="descrption">{{ "Reports.NewReport.DefinesCharacteristicsOfReport" | translate }}.</p> -->
          <p class="descrption">Solo se pueden configurar filtros con los campos tipo lista, fechas o radio button</p>
        </div>
      </div>
      <div class="row mb-3">
        <!-- Activar Detalle Reportes -->
        <div class="col-md-3">
          <mat-checkbox formControlName="CheckFilterReport">Activar configuración de filtros</mat-checkbox>
        </div>
        <div class="col-md-3">
          <mat-checkbox formControlName="CheckFilterDateReport">Filtrar por fecha</mat-checkbox>
        </div>
      </div>

      <!-- Activar configuración de filtros -->
      <div class="row mb-3" *ngIf="isEditFilterReport">
        <div class="row">

          <!-- Seleccionar campo para añadir -->
          <div class="col-md-9">
            <mat-form-field class="w-100">
              <mat-label>Seleccionar campo para añadir</mat-label>
              <mat-select formControlName="fkIdFieldFilter" required="true" (selectionChange)="onFieldChange($event.value)">
                <mat-option *ngFor="let option of listField" [value]="option">{{ option.vNameField }}</mat-option>
              </mat-select>
              <mat-error *ngIf="fromConfiguration.get('fkIdField')?.hasError('required')">{{ "ThisFieldIsRequired" |
                translate }}</mat-error>
            </mat-form-field>
          </div>

          <!-- Añadir campo filtro -->
          <div class="col-md-3">
            <div class="text-center mb-2">
              <button (click)="addDynamicFields()" class="btn-custom w-75" type="button" mat-raised-button>
                <!-- <strong>{{ "Reports.NewReport.AddField" | translate }}</strong> -->
                <strong>Añadir filtro</strong>
                <mat-icon iconPositionEnd class="ml-1">add</mat-icon>
              </button>
            </div>
          </div>

        </div>


        <div class="row">
          <div class="col-md-12">
            <!-- Tabla campos filtro-->
            <app-table-drag *ngIf="isEditFilterReport" (iconClick)="controllerFieldsDinamic($event)"
              [displayedColumns]="estructNewReportsTable" [data]="dataTableReportFieldDinamic"></app-table-drag>
          </div>
        </div>
        <span class="cont-msg-info">
          {{"Reports.Utils.MsgInfoTable" | translate}}
        </span>

      </div>



      <!-- Activar configuración de filtros tipo fecha-->
      <div class="row mb-3" *ngIf="isEditFilterDateReport">
        <div class="row">

          <!-- Seleccionar campo para añadir tipo fecha-->
          <div class="col-md-9">
            <mat-form-field class="w-100">
              <mat-label>Seleccionar campo tipo fecha</mat-label>
              <mat-select formControlName="fkIdFieldFilterDate" required="true" (selectionChange)="onFieldChange($event.value)">
                <mat-option *ngFor="let option of listField" [value]="option">{{ option.vNameField }}</mat-option>
              </mat-select>
              <mat-error *ngIf="fromConfiguration.get('fkIdField')?.hasError('required')">{{ "ThisFieldIsRequired" |
                translate }}</mat-error>
            </mat-form-field>
          </div>

          <!-- Añadir campo filtro tipo fecha-->
          <div class="col-md-3">
            <div class="text-center mb-2">
              <button (click)="addDynamicFields()" class="btn-custom w-75" type="button" mat-raised-button>
                <!-- <strong>{{ "Reports.NewReport.AddField" | translate }}</strong> -->
                <strong>Añadir campo fecha</strong>
                <mat-icon iconPositionEnd class="ml-1">add</mat-icon>
              </button>
            </div>
          </div>

        </div>


        <div class="row">
          <div class="col-md-12">
            <!-- Tabla campos filtro-->
            <app-table-drag *ngIf="isEditFilterDateReport" (iconClick)="controllerFieldsDinamic($event)"
              [displayedColumns]="estructNewReportsTable" [data]="dataTableReportFieldDinamic"></app-table-drag>
          </div>
        </div>
        <span class="cont-msg-info">
          {{"Reports.Utils.MsgInfoTable" | translate}}
        </span>

      </div>




      <div class="d-flex align-items-center justify-content-center mb-5">
        <!-- Botón Cancelar -->
        <div class="mx-1">
          <button (click)="cancel()" type="button" mat-raised-button color="">
            {{ "Reports.NewReport.Cancel" | translate }}
          </button>
        </div>
        <!-- Botón Generar reporte -->
        <div class="mx-1">
          <button
            [ngClass]="!(dataTableReportFieldDinamic.length + dataTableReportFieldStandard.length > 0) ? 'disabled' : ''"
            (click)="generateReport()"
            [disabled]="!(dataTableReportFieldDinamic.length + dataTableReportFieldStandard.length > 0)" type="button"
            mat-raised-button color="primary">
            {{ titleBtngenerateReport | translate }}
            <mat-icon class="ml-1">area_chart</mat-icon>
          </button>

        </div>
      </div>
      
    </form>
  </section>

</form>
