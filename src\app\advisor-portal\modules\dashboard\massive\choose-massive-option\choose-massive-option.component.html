<app-choose-country-and-company></app-choose-country-and-company>
<ng-container *ngIf="optionList.length > 0">
    <div class="cont-option">
        <a (click)="goToOption(option.id)" class="option click" *ngFor="let option of optionList">
            <ng-container *ngIf="option.bActive">
                <div class="box">
                    <mat-icon iconPositionEnd class="material-symbols-outlined">{{option.icon}}</mat-icon>
                </div>
                <div class="cont-title">
                    <p class="title-option">{{ option.name }}</p>
                </div>
            </ng-container>
        </a>
    </div>
</ng-container>