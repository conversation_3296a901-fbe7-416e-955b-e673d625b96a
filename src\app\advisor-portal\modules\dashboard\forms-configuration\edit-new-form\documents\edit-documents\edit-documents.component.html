<form [formGroup]="formDocument">
  <div class="row mt-5">
    <div class="col-12 col-md-12">
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label> {{ "DocumentModule.DocumentName" | translate }} </mat-label>
        <input matInput formControlName="vName" />
        <mat-error
          *ngIf="utilsSvc.isControlHasError(formDocument, 'vName', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-12 col-md-12">
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{ "DocumentModule.TypeOfFileRequerid" | translate }}
        </mat-label>
        <input matInput readonly formControlName="vExtension" />
        <mat-icon (click)="openModalTypeFile()" matSuffix>create</mat-icon>
        <mat-error
          *ngIf="
            utilsSvc.isControlHasError(formDocument, 'vExtension', 'required')
          "
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-12 col-md-12">
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label> {{ "DocumentModule.MaximumSize" | translate }} </mat-label>
        <input type="number" matInput formControlName="iMaximumSize" />
        <mat-error
          *ngIf="
            utilsSvc.isControlHasError(formDocument, 'iMaximumSize', 'required') "
        >
        {{ "ThisFieldIsRequired" | translate }} 
        </mat-error>
        <mat-error
          *ngIf="
              utilsSvc.isControlHasError(formDocument, 'iMaximumSize', 'max') "
        >
           {{ "DocumentModule.MaximumSizeMessage" | translate }} 
        </mat-error>
        <mat-error
          *ngIf="
              utilsSvc.isControlHasError(formDocument, 'iMaximumSize', 'min') "
        >
           {{ "DocumentModule.MiniumSizeMessage" | translate }} 
        </mat-error>
      </mat-form-field>
    </div>
    <div><p>{{ "DocumentModule.TitleFieldMaximumSize" | translate }}</p></div>    

    <div class="col-12 col-md-12">
     <br><br> <mat-slide-toggle class="mb-3" formControlName="bActive">{{
        "DocumentModule.ActiveDocument" | translate
      }}</mat-slide-toggle>
    </div>
    <div class="col-12 col-md-12">
      <mat-slide-toggle class="mb-3" formControlName="bRequired">{{
        "DocumentModule.DocumentRequired" | translate
      }}</mat-slide-toggle>
    </div>
    <div class="col-12 col-md-12">
      <mat-slide-toggle class="mb-3" formControlName="bIsMultipleFiles">{{
        "DocumentModule.MultipleFiles" | translate
      }}</mat-slide-toggle>
    </div>
    <div class="col-12 col-md-12">
      <mat-slide-toggle class="mb-3" formControlName="bDownload"  (change)="onChangeDownload($event)">{{
        "DocumentModule.DownloadDocument" | translate
      }}</mat-slide-toggle>
    </div>

    <!-- documento para descarga -->
    <div class="row mt-3" *ngIf="formDocument.value.bDownload">
      <mat-form-field style="width: 100%">
        <mat-label>
          {{ "DocumentModule.DocumentForDownload" | translate }}
        </mat-label>
        <input type="text" readonly matInput formControlName="vFileName" />
        <input
          type="file"
          multiple
          hidden
          #f_input
          (onChangeValidated)="selectFile($event)"
          ValidationInputFile
          [allowedExtensions]="allowedExtensions"
          [maxFileSizeMB]="maxFileSizeMB"
        />
        <button mat-icon-button matPrefix (click)="f_input.click()">
          <mat-icon>publish</mat-icon>
        </button>
      </mat-form-field>
    </div>
  </div>
  <div class="d-flex justify-content-center">
    <div class="mt-3">
      <button class="mx-2" (click)="cancel()" type="button" mat-raised-button>
        {{ "Cancel" | translate }}
      </button>
      <button
        *ngIf="this.formDocument.get('pkIIdTableModule')?.value === 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="createDocumentModule()"
        [disabled]="!valid"
      >
        {{ "DocumentModule.AddDocument" | translate }}
      </button>
      <button
        *ngIf="this.formDocument.get('pkIIdTableModule')?.value > 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="updateDocumentModule()"
        [disabled]="!valid"
      >
        {{ "DocumentModule.UpdateDocument" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </div>
  </div>
</form>

<!-- modal de tipos de archivos  -->
<ng-template #editTypeFilesModal>
  <app-modal2
    [titleModal]="titelModalTypeFile"
    (closeModal)="closeModal($event)"
  >
    <ng-container body>
      <section>
        <mat-checkbox
          *ngFor="let types of typeFileList"
          [checked]="types.checked"
          (change)="toggleTypeFile($event, types.Name)"
        >
          {{ types.Name }}
        </mat-checkbox>
      </section>
    </ng-container>
    <ng-container customButtonRight>
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="saveTypeFile()"
      >
        {{ "DocumentModule.SaveTypeFile" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
