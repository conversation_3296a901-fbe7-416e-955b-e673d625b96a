<div class="row">
  <div class="col-md-10">
    <app-choose-country-and-company></app-choose-country-and-company>
  </div>
  <div class="col-md-2">
    <mat-slide-toggle class="mb-2" (change)="changeAloneCountry($event)">
      {{ "CatalogSetting.CountryOnly" | translate }}</mat-slide-toggle
    >
  </div>
</div>

<div class="row mb-2">
  <h3 class="col-md-12">{{ "CatalogSetting.CatalogFields" | translate }}
    <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.CatalogFieldSubtitle' | translate }}">help_outline</mat-icon>
  </h3>
</div>

<app-table
  [displayedColumns]="estructTable"
  [data]="catalogTable"
  (iconClick)="controller($event)"
></app-table>

<button
  class="mb-2"
  type="button"
  mat-raised-button
  color="primary"
  (click)="createNew()"
>
  {{ "FormConfiguration.Createnew" | translate }}
  <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
</button>

<button
  class="mb-2"
  type="button"
  mat-raised-button
  color="primary"
  (click)="catalogComposite()"
>
  Carga de cátalogos compuestos
  <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
</button>

<ng-template #editViewPreviousModal>
  <app-modal2
    [titleModal]="titelModalViewPrevious"
    (closeModal)="eventCloseModal($event)"
  >
    <ng-container body #addSelector>
      <ng-template> </ng-template>
    </ng-container>
  </app-modal2>
</ng-template>
