import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { InsuranceCompanyModel } from 'src/app/shared/models/insurers';
import { RequestReportCarrierInsuranceModel } from 'src/app/shared/models/report-carrier-insurance';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-all-report-carrier-insurance',
  standalone: true,
  imports: [
    CommonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule,
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    PreventionSqlInjectorDirective,
    TableComponent,
    Modal2Component,
  ],
  templateUrl: './all-report-carrier-insurance.component.html',
  styleUrls: ['./all-report-carrier-insurance.component.scss'],
})
export class AllReportCarrierInsuranceComponent implements OnInit {
  //Variables for forms.
  formReportLog: FormGroup = this._fb.group({});
  idBusinessByCountry: number = 0;
  insurers: InsuranceCompanyModel[] = [];

  //Table related variables.
  dataReportLogTable: any[] = [];
  estructReportLogTable: BodyTableModel[] = [];

  constructor(
    private _fb: FormBuilder,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _transactionService: TransactionService,
    private _messageService: MessageService,
    private _insuranceService: InsuranceService,
    public modalDialog: MatDialog,
    public _utilsService: UtilsService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getBusinessByCountry();
  }

  //initialize the form.
  initForm() {
    this.formReportLog = this._fb.group({
      idQuote: [null, Validators.required],
      insuranceName: [null, Validators.required],
      startDate: [null, Validators.required],
      endDate: [null, Validators.required],
      idBusinessByCountry: 0,
    });

    this.formReportLog.get('startDate')?.valueChanges.subscribe((startDate) => {
      if (startDate) {
        this.formReportLog.get('endDate')?.setValue(null);
        this.formReportLog.get('endDate')?.updateValueAndValidity();
      }
    });
  }

  //check if the form is valid.
  get validForm(): boolean {
    return this.formReportLog.valid;
  }

  //get BusinessByCountry
  async getBusinessByCountry() {
    let dataSetting = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry =
      dataSetting == undefined ? 0 : dataSetting.idBusinessByCountry;
    this.formReportLog
      .get('idBusinessByCountry')
      ?.setValue(this.idBusinessByCountry);
    this.getInsuranceCompanyAssociatedCarrier();
  }

  //filter to validate the 3 month range.
  endDateFilter = (d: Date | null): boolean => {
    const startDate = this.formReportLog.get('startDate')?.value;
    if (!d || !startDate) {
      return true;
    }

    const maxDate = new Date(startDate);
    maxDate.setMonth(maxDate.getMonth() + 3);
    maxDate.setDate(maxDate.getDate() - 1); // Para incluir el mismo día al final del rango

    return d <= maxDate && d >= startDate;
  };

  //Controller for table actions.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'detail':
        break;
      default:
        break;
    }
  }

  //Get Insurance Company Associated The Carrier By idBusinessByCountry.
  getInsuranceCompanyAssociatedCarrier() {
    this._insuranceService
      .getInsuranceCompanyAssociatedCarrier(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          const insurances: InsuranceCompanyModel[] = resp.result;
          insurances.length > 0 ? (this.insurers = insurances.filter((insurance: InsuranceCompanyModel) => insurance.vName !== 'WTW')) : (this.insurers = []);
        }
      });
  }

  //Get the insurance carrier report through a filter.
  getQuotesLogsInsuranceCarrier(filter: RequestReportCarrierInsuranceModel) {
    this.estructReportLogTable = [];
    this.dataReportLogTable = [];
    this._transactionService
      .getQuotesLogsInsuranceCarrier(filter)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.estructReportLogTable = [];
          this.dataReportLogTable = [];
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.getColunmNameTable(resp.result);
            this.dataReportLogTable = resp.result;
          }
        }
      });
  }

  //search report list by select filter.
  search() {
    let request: RequestReportCarrierInsuranceModel = this.formReportLog.value;
    request.idQuote = Number(request.idQuote);
    request.startDate = this._utilsService.formatDate(
      this.formReportLog.get('startDate')?.value
    );
    request.endDate = this._utilsService.formatDate(
      this.formReportLog.get('endDate')?.value
    );
    request.idBusinessByCountry = this.idBusinessByCountry;
    if (this.validForm)
      this.getQuotesLogsInsuranceCarrier(this.formReportLog.value);
  }

  //Clear search filters.
  cleanFilters() {
    this.formReportLog.reset();
    this.formReportLog
      .get('idBusinessByCountry')
      ?.setValue(this.idBusinessByCountry);
    this.dataReportLogTable = [];
  }

  //Function that generates an excel file based on the result of the search applied.
  downloadReport() {
    let request: RequestReportCarrierInsuranceModel = this.formReportLog.value;
    request.idQuote = Number(request.idQuote);
    request.startDate = this._utilsService.formatDate(
      this.formReportLog.get('startDate')?.value
    );
    request.endDate = this._utilsService.formatDate(
      this.formReportLog.get('endDate')?.value
    );
    request.idBusinessByCountry = this.idBusinessByCountry;
    this._transactionService
      .generateCarrierInsuranceLogReport(request)
      .pipe(
        catchError((error) => {
          if (!error.ok) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of(null);
        })
      )
      .subscribe((resp) => {
        if (resp) {
          const blob = new Blob([resp], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = 'Reporte de Carrier - Aseguradora.xlsx';
          link.click();
          window.URL.revokeObjectURL(downloadUrl);
        }
      });
  }

  //Function that generates the dynamic names of the columns, based on the names of the keys that the carrier responds to, for the reporting table.
  getColunmNameTable(data: any) {
    const columnName = Object.keys(data[0]);
    columnName.forEach((element: string) => {
      const column: BodyTableModel = {
        columnLabel: element,
        columnValue: element,
      };
      this.estructReportLogTable.push(column);
    });
  }
}
