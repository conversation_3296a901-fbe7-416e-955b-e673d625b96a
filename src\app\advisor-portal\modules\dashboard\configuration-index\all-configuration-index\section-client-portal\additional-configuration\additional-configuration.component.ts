import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { ImageUploadModel } from 'src/app/shared/models/file';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ConfigClientPortalService } from 'src/app/shared/services/additional-config-client-portal/additional-config-client-portal.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-additional-configuration',
  standalone: true,
  imports: [
    MatInputModule,
    PreventionSqlInjectorDirective,
    TranslateModule,
    ReactiveFormsModule,
    CommonModule,
    MatButtonModule,
    GenericImagePickerComponent
  ],
  templateUrl: './additional-configuration.component.html',
  styleUrls: ['./additional-configuration.component.scss']
})
export class AdditionalConfigurationComponent implements OnInit{

  @Input() idBusinessByCountry: number = 0;
  imageSrcBenefits: string = '';
  imageSrcButton: string = '';
  imageSrcFooter: string = '';
  formConfigurations: FormGroup = this._fb.group({
    vTitleLanding: [null, Validators.required],
    vSubTitleLanding: [null, Validators.required],
    fkIIdUploadedFileButtonLanding: [null],
    vTextButtonLanding: [null, Validators.required],
    vUrlButtonLanding: [null],
    fkIIdUploadedBenefitsLanding: [null],
    fkIIdUploadedFooterLanding: [null],
  })

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    private _additionalConfigService: ConfigClientPortalService,
    private _fileService: FileService,
    private _translateService: TranslateService,
    private _messageService: MessageService
  ){}

  ngOnInit(): void {
    this._loadConfiguration()
  }

  private _loadImage(idFile: number, source: 'btn' | 'benefits' | 'footer'){
    this._fileService.getUploadFileById(idFile).subscribe({
      next: (response => {
        if (response.result.vFileName && response.result.imageBase64) {
          let extension = response.result.vFileName.split('.');
          extension = extension[response.result.vFileName.split('.').length - 1];
          if (source === 'btn')
            this.imageSrcButton = `data:image/${extension};base64,${response.result.imageBase64}`;
          else if (source === 'footer')
            this.imageSrcFooter = `data:image/${extension};base64,${response.result.imageBase64}`;
          else
            this.imageSrcBenefits =`data:image/${extension};base64,${response.result.imageBase64}`;
        }
      })
    })
  }

  private _loadConfiguration(){
    this._additionalConfigService.getConfiguration(this.idBusinessByCountry).subscribe({
      next: (resp => {
        this.formConfigurations.patchValue({...resp.result})
        resp.result.fkIIdUploadedFileButtonLanding !== 0 && this._loadImage(resp.result.fkIIdUploadedFileButtonLanding, 'btn')
        resp.result.fkIIdUploadedBenefitsLanding !== 0 && this._loadImage(resp.result.fkIIdUploadedBenefitsLanding, 'benefits')
        resp.result.fkIIdUploadedFooterLanding !== 0 && this._loadImage(resp.result.fkIIdUploadedFooterLanding, 'footer')
      })
    })
  }

  async saveImage(formControl: any): Promise<number> {
    return new Promise((resolve) => {
      if (typeof formControl?.value === 'number')
          return resolve(formControl?.value)
      let payload: ImageUploadModel = {
        fileName: formControl?.value.imageName,
        fkIIdBusinessByCountry: this.idBusinessByCountry,
        imageBase64: formControl?.value.base64.split(',')[1],
        pkIIdInsuranceCompanies: 0,
        type: 0
      };
      this._fileService
        .uploadImage(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              resolve(resp.result)
            }
          }
        });
    });
    
  }

  changeImage(event: any, field: string) {
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString;
    this.formConfigurations.get(field)?.setValue({ base64, imageName:`${imageName}.${extension}` });
    if (field === 'fkIIdUploadedFileButtonLanding')
      this.imageSrcButton = base64;
    else if (field === 'fkIIdUploadedFooterLanding')
      this.imageSrcFooter = base64;
    else
      this.imageSrcBenefits = base64;

  }

  deleteImage(field: string){
    this.formConfigurations.get(field)?.setValue(0);
    if (field === 'fkIIdUploadedFileButtonLanding')
      this.imageSrcButton = '';
    else if (field === 'fkIIdUploadedFooterLanding')
      this.imageSrcFooter = '';
    else
      this.imageSrcBenefits = '';
  }

   async saveConfiguration(){
    if (this.formConfigurations.invalid)
      return this.formConfigurations.markAllAsTouched()

    let payload = {
      ...this.formConfigurations.value,
      FkIIdUploadedBenefitsLanding: await this.saveImage(this.formConfigurations?.get('fkIIdUploadedBenefitsLanding')),
      FkIIdUploadedFileButtonLanding: await this.saveImage(this.formConfigurations?.get('fkIIdUploadedFileButtonLanding')),
      FkIIdUploadedFooterLanding: await this.saveImage(this.formConfigurations?.get('fkIIdUploadedFooterLanding')),
    }
    delete payload['fkIIdUploadedFileButtonLanding']
    delete payload['fkIIdUploadedFooterLanding']
    delete payload['fkIIdUploadedBenefitsLanding']
    this._additionalConfigService.modifyConfiguration(this.idBusinessByCountry, payload).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadConfiguration()
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.ConfigurationModifiedSucessfully')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
  }
}
