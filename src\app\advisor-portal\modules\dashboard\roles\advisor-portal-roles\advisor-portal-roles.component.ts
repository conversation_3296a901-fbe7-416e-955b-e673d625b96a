import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { EditNewRoleComponent } from '../edit-new-role/edit-new-role.component';
import { ReplicateRoleComponent } from '../replicate-role/replicate-role.component';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { Router, RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { Subscription } from 'rxjs';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { RolesListModel } from 'src/app/shared/models/role';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatDialog } from '@angular/material/dialog';
import { ResponseGlobalModel } from 'src/app/shared/models/response';

@Component({
  selector: 'app-portal-roles',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TableComponent,
    EditNewRoleComponent,
    ReplicateRoleComponent,
    ModalComponent,
    RouterModule,
    MatIconModule,
    TranslateModule,
    MatButtonModule,
    MatTooltipModule,
    MatTabsModule
  ],
  templateUrl: './advisor-portal-roles.component.html',
  styleUrls: ['./advisor-portal-roles.component.scss']
})
export class RolesListComponent implements OnInit{
  @Input() clientPortal: boolean = false;
  private _settingCountryAndCompanySubscription?: Subscription;

  formValid: boolean = false;
  idBusinessCountry: number = 0;
  tittleModalText: string = "";
  messageResponse: string = "";
  //data que se envia al modal de replicate rol
  dataReplicateRole= 0

  //data que se envia al crear o editar un rol
  dataEditNewRole={
    idBusinessCountry: 0,
    idRole: 0,
    toClientPortal: false
  };

  estructTableRoles: BodyTableModel[] = [
    { 
      columnLabel: this._translateService.instant('Role.RoleName'), 
      columnValue: 'vRoleName' },
    {
      columnLabel: this._translateService.instant('Category'),
      columnValue: 'bEditText'
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsService.changeStatusValue(item)
    },
    {
      columnLabel: this._translateService.instant('CreationDate'),
      columnValue: 'dCreationDate',
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  dataTableRoles: RolesListModel[] = [];

  @ViewChild('replicateRoleModal') replicateRoleModal?: TemplateRef<any>;
  @ViewChild('editNewRoleModal') editNewRoleModal?: TemplateRef<any>;

  constructor(
    private _roleService: RoleService,
    private _settingService: SettingService,      
    private _messageService: MessageService,
    private _router: Router,

    public _utilsService: UtilsService, 
    public _editNewRoleDialog: MatDialog,
    public _replicateDialog: MatDialog,
    public _translateService: TranslateService
  ){

  }
  ngOnInit(): void {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if(this._router.url == response.currentModule)
          {
            if(!(Object.keys(response).length === 0)){
              this.dataTableRoles = [];
              this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
              this.getRole(
                response.enterprise.pkIIdBusinessByCountry,

              );
            }
          }
        }
      );

      this._translateService.onLangChange.subscribe(
        (event: LangChangeEvent) => { 
          this.estructTableRoles[0].columnLabel = this._translateService.instant('Role.RoleName')
          this.estructTableRoles[1].columnLabel = this._translateService.instant('Category')
          this.estructTableRoles[2].columnLabel = this._translateService.instant('Status')
          this.estructTableRoles[3].columnLabel = this._translateService.instant('CreationDate')
          this.estructTableRoles[4].columnLabel = this._translateService.instant('Action')
        }
      );
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }


  controller(evt:IconEventClickModel){
    this.openEditNewRoleDialog(evt.value.pkIIdRole);
    this.tittleModalText = this._translateService.instant("Role.EditRole");
  }

  openEditNewRoleDialog(idRole: number = 0) {
    if (this.formValid) {
      this.tittleModalText = this._translateService.instant("Role.CreateRole");
      this.dataEditNewRole = {
        idBusinessCountry: this.idBusinessCountry,
        idRole: idRole,
        toClientPortal: this.clientPortal
      };
      const dialogRef = this._editNewRoleDialog.open(this.editNewRoleModal!, {
        disableClose: false,
        width: '70vw',
        maxHeight: '90vh'
      });
    } else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('YouMustSelectACountryAndABusiness')
      );
    }       
  }

  openReplicateDialog()
  {
    if(this.formValid)
    {
      this.dataReplicateRole = this.idBusinessCountry
      const dialogRef = this._replicateDialog.open(this.replicateRoleModal!, {
        disableClose: true,
        width: '50vw',
        maxHeight: '90vh'
      });
    }
    else {
      this._messageService.messageInfo(
        this._translateService.instant('InvalidForm'),
        this._translateService.instant('YouMustSelectACountryAndABusiness')
      );
    }  
  }
  
  getSubmitReplicateData(event: ResponseGlobalModel)
  {
    if(!event.error)
    {
      this._replicateDialog.closeAll();
      this.getRole(event.result[0].fkIIdBusinessByCountry)
      this._messageService.messageSuccess(
        this._translateService.instant('Role.Replicated'),
        ''
      )
    }
    else
    {
      this._messageService.messageError(
        this._translateService.instant('ThereWasAError') + event.message
      )
    }
    
  }

  getSubmitEditNewData(event: ResponseGlobalModel)
  {
    if(!event.error)
    {
      console.log("entra", event.message.includes("actualiza") )
      if( event.message.includes("actualiza") ){
        this.messageResponse = "Modified";
      }else{
        this.messageResponse = "Created";
      }
      this._editNewRoleDialog.closeAll();
      this.getRole(this.idBusinessCountry)
      this._messageService.messageSuccess(
        this._translateService.instant( this.messageResponse),
        ''
      )
    }
    else
    {
      this._messageService.messageError(
        this._translateService.instant('ThereWasAError') + event.message
      )
    }
  }

  getRole(id:number)
  {
    this._roleService
      .getRole(id, this.clientPortal)
      .subscribe({
        next: (resp) => {
          this.dataTableRoles = resp.result;
          for(let item of this.dataTableRoles)
          {
            let index = this.dataTableRoles.indexOf(item)
            if(!item.bEdit)
            {
              this.dataTableRoles[index].bEditText = 'Edición'
            }
            else
            {
              this.dataTableRoles[index].bEditText = 'Solo lectura'
            }
            // this.dataTableRoles[index].bActive = item.bActive
            // functionValue: (item: any) => this._utilsService.changeStatusValue(item)
          }
        },
      });
  }

  validForm(event: boolean) {
    this.formValid = event;
  }
}
