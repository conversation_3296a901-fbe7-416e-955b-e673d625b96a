<form [formGroup]="form">
  <mat-slide-toggle class="mb-3" formControlName="active">
    {{'Product.ActiveProduct' | translate}}
  </mat-slide-toggle>
  <mat-slide-toggle class="mb-3 ml-2" formControlName="showCustomerPortal" (change)="updateValidators($event.checked)">
    {{'Product.CustomerPortal' | translate}}
  </mat-slide-toggle>

  <div class="row">
    <div class="col-12 col-md-8">
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{'Product.ParentProduct' | translate}}
        </mat-label>
        <mat-select formControlName="IdProductParent">
          <mat-option
            *ngFor="let product of productsParent"
            [value]="product.pkIIdProductParent"
          >
            {{ product.vProductName }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="utilsSvc.isControlHasError(form, 'IdProductParent', 'required')" >
          {{'ThisFieldIsRequired' | translate}}
        </mat-error>
      </mat-form-field>

      <!-- Type Policy -->
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{'Product.PolicyType' | translate}}
        </mat-label>
        <mat-select formControlName="fkIIdPolicyType">
          <mat-option
            *ngFor="let type of policyTypes"
            [value]="type.pkIIdPolicyType"
          >
            {{ type.vPolicyTypeName }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="utilsSvc.isControlHasError(form, 'fkIIdPolicyType', 'required')" >
          {{'ThisFieldIsRequired' | translate}}
        </mat-error>
      </mat-form-field>

      
      <!-- fecha inicio -->
      <div *ngIf="isCollectivePolicy">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>
            {{ "PolicyConfiguration.GeneralInformation.StartValidity" | translate }}
          </mat-label>
          <input
            matInput
            formControlName="dValidityStart"
            [matDatepicker]="picker"
          />
          <mat-datepicker-toggle
            matIconSuffix
            [for]="picker"
          ></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
      </div>

      <!-- fecha fin -->
      <div *ngIf="isCollectivePolicy">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>
            {{ "PolicyConfiguration.GeneralInformation.EndValidity" | translate }}
          </mat-label>
          <input
            matInput
            formControlName="dValidityEnd"
            [matDatepicker]="picker1"
          />
          <mat-datepicker-toggle
            matIconSuffix
            [for]="picker1"
          ></mat-datepicker-toggle>
          <mat-datepicker #picker1></mat-datepicker>
        </mat-form-field>
      </div>





      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{'Product.ProductName' | translate}}
        </mat-label>
        <input matInput formControlName="name" PreventionSqlInjector/>
        <mat-error *ngIf="utilsSvc.isControlHasError(form, 'name', 'required')" >
          {{'ThisFieldIsRequired' | translate}}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{'Product.ProductDescription' | translate}}
        </mat-label>
        <input matInput formControlName="description" PreventionSqlInjector/>
        <mat-error *ngIf="utilsSvc.isControlHasError(form, 'description', 'required')" >
          {{'ThisFieldIsRequired' | translate}}
        </mat-error>
      </mat-form-field>

      <h5 class="fw-bold mb-2">
        {{ 'Product.OfferSelection' | translate }}
      </h5>
      <mat-label>
        {{'Product.OfferSelectionText' | translate}}
      </mat-label>
      <div class="mt-2">
        <div class="form-check mb-3">
          <input class="form-check-input" 
            type="checkbox" 
            id="checkAdvisorPortal"
            formControlName="inAdvisorPortal"
            />
          <label class="form-check-label" for="checkAdvisorPortal">
            {{'Product.OptionAdvisorPortal' | translate}}
          </label>
        </div>
        <div class="form-check mb-3">
          <input class="form-check-input" 
            type="checkbox" 
            id="checkCustomerPortal"
            formControlName="inCustomerPortal"
            />
          <label class="form-check-label" for="checkCustomerPortal">
            {{'Product.OptionCustomerPortal' | translate}}
          </label>
        </div>
      </div>
      <mat-error *ngIf="utilsSvc.isControlHasError(form, 'name', 'required')" >
        {{'ThisFieldIsRequired' | translate}}
      </mat-error>

      <h5 class="fw-bold mb-2">
        {{ 'Product.QuotationPDF' | translate }}
      </h5>

      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>
          {{'Product.ModifyPdfInfo' | translate}}
        </mat-label>
        <textarea matInput formControlName="comments" cdkTextareaAutosize cdkAutosizeMinRows="3" cdkAutosizeMaxRows="10"></textarea>
      </mat-form-field>
    </div>

    <app-generic-image-picker
        class="col-12 col-md-4"
        [title]="'Product.ProductLogo'| translate"
        [description]="'Product.ProductLogoDescription'| translate"
        (changeFile)="changeImage($event)"
        [imageSrc]="form.get('logo')?.value.base64">
    </app-generic-image-picker>
  </div>

  <div *ngIf="form.get('showCustomerPortal')?.value">
    <!-- <div class="row">
      <mat-label>
        {{'Product.OnlyCustomerPortal' | translate}}
      </mat-label>
      <app-generic-image-picker
          class="col-12 col-md-4"
          [title]="'Product.ProductImage'| translate"
          [description]="'Product.ProductImageText'| translate"
          (changeFile)="changeImage($event, 'logoCustomerPortal')"
          [imageSrc]="form.get('logoCustomerPortal')?.value.base64">
      </app-generic-image-picker>
    </div> -->

    <div class="row mt-4">
      <mat-label>
        {{'Product.OnlyCustomerPortal' | translate}} ({{ 'Product.Minimum' | translate }} {{minItems}} - {{ 'Product.Maximun' | translate }} {{maxItems}})
      </mat-label>
      <h5 class="fw-bold mb-2">
        {{ 'Product.CoveragesTitle' | translate }}
      </h5>
      <mat-label>
        {{ 'Product.CoveragesText' | translate }}
      </mat-label>

      <app-table
        [displayedColumns]="estructTableCoverages"
        [data]="dataTableCoverages"
        (iconClick)="controller($event)"
      ></app-table>
      <mat-error *ngIf="utilsSvc.isControlHasError(form, 'listCoverages', 'minlength')" >
        {{'ThisFieldIsRequired' | translate}}
      </mat-error>

      <div class="row" *ngIf="dataTableCoverages.length < maxItems">
        <div class="col">
          <button
            type="button"
            mat-raised-button
            color="primary"
            (click)="openModal('coverage')"
          >
            {{ "Product.AddCoverage" | translate }}
          </button>
        </div>
      </div>
    </div>

    <div class="row mt-4 mb-4">
      <mat-label>
        {{'Product.OnlyCustomerPortal' | translate}} - {{ 'Product.Maximun' | translate }} {{maxItems}}
      </mat-label>
      <h5 class="fw-bold mb-2">
        {{ 'Product.FrequentQuestionsTitle' | translate }}
      </h5>
      <mat-label>
        {{ 'Product.FrequentQuestionsText' | translate }}
      </mat-label>

      <app-table
        [displayedColumns]="estructTableQuestions"
        [data]="dataTableQuestions"
        (iconClick)="controller($event)"
      ></app-table>

      <div class="row" *ngIf="dataTableQuestions.length < maxItems">
        <div class="col">
          <button
            type="button"
            mat-raised-button
            color="primary"
            (click)="openModal('question')"
          >
            {{ "Product.AddQuestion" | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>

</form>

<ng-template #createEditCoverage>
  <app-modal2 [titleModal]="( formCoverage.get('id')?.value ? 'Product.UpdateCoverageTitle' : 'Product.AddCoverageTitle') | translate">
    <ng-container body>
      <form [formGroup]="formCoverage">
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{'Product.Coverage' | translate}}
          </mat-label>
          <input matInput formControlName="name" PreventionSqlInjector/>
          <mat-error *ngIf="utilsSvc.isControlHasError(form, 'name', 'required')" >
            {{'ThisFieldIsRequired' | translate}}
          </mat-error>
        </mat-form-field>
        <app-generic-image-picker
          class="col-12 col-md-4"
          [title]="'Product.CoverageIconTitle'| translate"
          [description]="'Product.CoverageIconText'| translate"
          (changeFile)="changeImageCoverage($event)"
          [imageSrc]="imageSrc">
        </app-generic-image-picker>
      </form>
    </ng-container>
    <ng-container customButtonRight>
      <div class="modal-footer">
        <button mat-raised-button color="primary" type="button" (click)="saveCoverage()">
          {{ "Save" | translate }}
        </button>
      </div>
    </ng-container>
  </app-modal2>
</ng-template>


<ng-template #createEditQuestion>
  <app-modal2 [titleModal]="(formQuestion.get('id')?.value ? 'Product.UpdateCoverageTitle' : 'Product.AddCoverageTitle') | translate">
    <ng-container body>
      <form [formGroup]="formQuestion">
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{'Product.Question' | translate}}
          </mat-label>
          <input matInput formControlName="question" PreventionSqlInjector/>
          <mat-error *ngIf="utilsSvc.isControlHasError(form, 'name', 'required')" >
            {{'ThisFieldIsRequired' | translate}}
          </mat-error>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{'Product.Answer' | translate}}
          </mat-label>
          <textarea matInput formControlName="answer" PreventionSqlInjector cdkTextareaAutosize cdkAutosizeMinRows="3" cdkAutosizeMaxRows="10"></textarea>
          <mat-error *ngIf="utilsSvc.isControlHasError(form, 'name', 'required')" >
            {{'ThisFieldIsRequired' | translate}}
          </mat-error>
        </mat-form-field>

      </form>
    </ng-container>
    <ng-container customButtonRight>
      <div class="modal-footer">
        <button mat-raised-button color="primary" type="button" class="" (click)="saveQuestion()">
          {{ "Save" | translate }}
        </button>
      </div>
    </ng-container>
  </app-modal2>
</ng-template>