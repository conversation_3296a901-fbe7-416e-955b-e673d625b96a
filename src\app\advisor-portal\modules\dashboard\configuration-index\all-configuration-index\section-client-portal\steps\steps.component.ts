import { Cdk<PERSON>ragDrop, Cdk<PERSON><PERSON><PERSON><PERSON>, CdkDrag, moveItemInArray } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { StepsIndexModel } from 'src/app/shared/models/configuration-index/steps.model';
import { ImageUploadModel } from 'src/app/shared/models/file';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { StepService } from 'src/app/shared/services/steps/steps.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-steps',
  standalone: true,
  imports: [
    MatIconModule,
    CommonModule,
    CdkDropList,
    CdkDrag,
    PreventionSqlInjectorDirective,
    Modal2Component,
    TranslateModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    GenericImagePickerComponent
  ],
  templateUrl: './steps.component.html',
  styleUrls: ['./steps.component.scss']
})
export class StepsComponent implements OnInit {
  @ViewChild('createEditStep') createEditStep?: TemplateRef<any>;
  private _editStepModalSubscription?: Subscription;
  _currentModal: MatDialogRef<any> | null = null;

  @Input() idBusinessByCountry: number = 0;
  listSteps: StepsIndexModel[] = [];
  imageSrc: string = '';
  isCreate: boolean = true;

  formStep: FormGroup = this._fb.group({
    vName: [null, Validators.required],
    vText: [null, Validators.required],
    vLogo: ['', Validators.required],
  })
  constructor(
    private _translateService: TranslateService,
    private _dialog: MatDialog,
    private _messageService: MessageService,
    private _stepsService: StepService,
    private _fb: FormBuilder,
    private _fileService: FileService,
    public utilsSvc: UtilsService
  ){

  }
  ngOnInit(): void {
    this._getSteps()
  }

  changeImage(event: any) {
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString;
    this.formStep.get('vLogo')?.setValue({ base64, imageName:`${imageName}.${extension}` });
    this.imageSrc = base64;
  }

  async saveImage(): Promise<number> {
    return new Promise((resolve) => {
      let payload: ImageUploadModel = {
        fileName: this.formStep.get('vLogo')?.value.imageName,
        fkIIdBusinessByCountry: this.idBusinessByCountry,
        imageBase64: this.formStep.get('vLogo')?.value.base64.split(',')[1],
        pkIIdInsuranceCompanies: 0,
        type: 0
      };
      this._fileService
        .uploadImage(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              resolve(resp.result)
            }
          }
        });
    });
    
  }

  private _getSteps(){
    this._stepsService.getSteps(this.idBusinessByCountry).subscribe({
      next: (resp => {
        this.listSteps = resp.result
      })
    })
  }

  saveStep(){
    this._currentModal?.close();
    this._currentModal = null;
    this._editStepModalSubscription?.unsubscribe();
  }

  deleteStep(index: number){
    this.listSteps.splice(index, 1)
  }

  openModal(step: StepsIndexModel | null){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this.imageSrc = ''
    this.formStep.reset()
    this.isCreate = true
    if (step !== null)
      {
        this.isCreate = false;
        this.formStep.patchValue({
        vName: step.vName,
        vText: step.vText
      })
      this._fileService.getUploadFileById(step.fkIIdUploadFile).subscribe({
        next: (response => {
          if (response.result.vFileName && response.result.imageBase64) {
            let extension = response.result.vFileName.split('.');
            extension = extension[response.result.vFileName.split('.').length - 1];
            this.formStep.get('vLogo')?.setValue(`data:image/${extension};base64,${response.result.imageBase64}`);
            this.imageSrc = `data:image/${extension};base64,${response.result.imageBase64}`;
          }
        })
      })
      }
    this._currentModal = this._dialog.open(this.createEditStep!, sizeConfiguration);
    this._editStepModalSubscription = this._currentModal.beforeClosed().subscribe({
      next: ( async _ => {
        if (step !== null){
          const auxStep = {
            pkIIdStep: step.pkIIdStep,
            bActive: step.bActive,
            fkIIdUploadFile: await this.saveImage(),
            iOrder: step.iOrder,
            vName: this.formStep?.get('vName')?.value,
            vText: this.formStep?.get('vText')?.value,
          }
          this.listSteps.splice(step.iOrder -1 , 1, auxStep)
        }
        else {
          const auxStep = {
            pkIIdStep: 0,
            bActive: true,
            fkIIdUploadFile: await this.saveImage(),
            iOrder: this.listSteps.length + 1,
            vName: this.formStep?.get('vName')?.value,
            vText: this.formStep?.get('vText')?.value,
          }
          this.listSteps.push(auxStep)
        }
        this.formStep.reset();
      })
    })
  }

  updateSteps() {
    const modifiedSteps = this.listSteps.map(step => {
      return {
        bActive: step.bActive,
        fkIIdUploadFile: step.fkIIdUploadFile,
        iOrder: step.iOrder,
        vName: step.vName,
        vText: step.vText,
        FkIIdBusinessByCountry: this.idBusinessByCountry
      }
    })
    this._stepsService.modifySteps(this.idBusinessByCountry, modifiedSteps).subscribe({
      next: (response) => {
        if (!response.error) {
          this._getSteps()
          this._messageService.messageSuccess(
            this._translateService.instant('DataSavedSuccessfully'),
            this._translateService.instant('SectionClientPortal.StepModifiedSucessfully')
          );
        }
      },
      error: (error) => {
        this._messageService.messageInfo(
          this._translateService.instant('ThereWasAError'),
          error
        );
      },
    })
  }

    dropOrderIndex(event: CdkDragDrop<string[]>) {
      moveItemInArray(this.listSteps, event.previousIndex, event.currentIndex);
      this.listSteps.forEach((item, idx) => {
        item.iOrder = idx + 1;
      });
      this.listSteps = this.listSteps.slice();
    }

    activateYDeactivateProducts(event: any) {
      event.bActive = !event.bActive;
    }

}
