import { Component, OnInit,Input } from '@angular/core';
import { CommonModule,DatePipe } from '@angular/common';
import { Subscription, of, catchError } from 'rxjs';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';

import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';

import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

import { TableComponent } from 'src/app/shared/components/table/table.component';
import { BodyTableModel } from 'src/app/shared/models/table';
import { DetailTaskProcedureModel } from 'src/app/shared/models/procedures';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SettingService } from 'src/app/shared/services/setting/setting.service';

@Component({
  selector: 'app-detail-task-procedure',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    TranslateModule,
  ],
  templateUrl: './detail-task-procedure.component.html',
  styleUrls: ['./detail-task-procedure.component.scss'],
})
export class DetailTaskProcedureComponent implements OnInit {
  @Input() dataDetailTask: any={};
  form: FormGroup = new FormGroup({});
  dataTaskProcedureTable: DetailTaskProcedureModel[] = [];

  dataSetting: any = {};
  idUser: number = 0;

  estructTaskProcedureTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'ProceduresViewerSettings.DetailsTaskProcedure.WhoModifies'
      ),
      columnValue: 'nameUserCreation',
    },
    {
      columnLabel: this._translateService.instant(
        'ProceduresViewerSettings.DetailsTaskProcedure.DateModify'
      ),
      columnValue: 'dDateCreation',
      functionValue: (item: any) => this.formateDate(item.dDateCreation),
    },
    {
      columnLabel: this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.Stage'),
      columnValue: 'nameStage',
    },
    {
      columnLabel: this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.State'),
      columnValue: 'nameStatus',
    },
    {
      columnLabel: this._translateService.instant(
        'ProceduresViewerSettings.DetailsTaskProcedure.Management'
      ),
      columnValue: 'vCategory',
      functionValue: (item: any) => this.validatedManagement(item.vCategory),
    },
  ];

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _taskTayConfigService: TaskTayConfigService,
    public utilsService: UtilsService,
    private _userService: UserService,
    private datePipe: DatePipe,
    private _settingService: SettingService
  ) {}

  ngOnInit(): void {
    this.getIdUserSession();
    this.initForm();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table management history
      this.estructTaskProcedureTable[1].columnLabel =
        this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.WhoModifies');
      this.estructTaskProcedureTable[2].columnLabel =
        this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.DateModify');
      this.estructTaskProcedureTable[3].columnLabel =
        this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.Stage');
      this.estructTaskProcedureTable[4].columnLabel =
        this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.State');
      this.estructTaskProcedureTable[5].columnLabel =
        this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.Management');
    });
  }

  initForm() {
    this.dataDetailTask = {
      idTarea: this.dataDetailTask["IddeTarea"],
      proceso: this.dataDetailTask.Proceso,
      producto: this.dataDetailTask.Producto,
      etapa: this.dataDetailTask.Etapa,
      estado: this.dataDetailTask.Estado,
      fechaSolicitud: this.dataDetailTask['Fechadesolicitud'],
      creadoPor: this.dataDetailTask['CreadoPor'],
      asignadoA: this.dataDetailTask['AsignadoA'],
      vNameField: this.dataDetailTask['vNameField'],
    };
  }

async getIdUserSession() {
    this.dataSetting = await this._settingService.getDataSettingInit();
    this.idUser = this.dataSetting == undefined ? 0 : this.dataSetting.idUser;
    if (this.idUser) {
      this.getDataUserByProcess(this.dataDetailTask.idTarea);
    }
  }

  getDataUserByProcess(idTask: number) {
    this._taskTayConfigService
      .getManagementHistoryFormalities(idTask)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTaskProcedureTable = [];
        } else {
          this.dataTaskProcedureTable = resp.result;

        const sortedData = this.dataTaskProcedureTable.sort((a, b) => {
          return new Date(a.dDateCreation).getTime() - new Date(b.dDateCreation).getTime();
        });

        
        const oldestEntry = sortedData.find(x => x.fieldValue != null && x.fieldValue != '');

        this.dataDetailTask.fieldName = oldestEntry?.fieldName;
        this.dataDetailTask.fieldValue = oldestEntry?.fieldValue;
        }
      });
  }

  validatedManagement(vCategory: any): string {
    if (vCategory == 'Abierto')
      return this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.TaskOpening');
    else if (vCategory == 'En curso')
      return this._translateService.instant(
        'ProceduresViewerSettings.DetailsTaskProcedure.StatusChange'
      );
    else if (vCategory == 'Cerrado')
      return this._translateService.instant('ProceduresViewerSettings.DetailsTaskProcedure.TaskClosure');
    else return '';
  }

  // format dateCreation 
  formateDate(vDate:any):any{
    if (!vDate) {
      return '';
    }
    var date =  this.datePipe.transform(vDate, 'dd-MM-yyyy HH:mm:ss');
    return date?.toString();
  }
}
