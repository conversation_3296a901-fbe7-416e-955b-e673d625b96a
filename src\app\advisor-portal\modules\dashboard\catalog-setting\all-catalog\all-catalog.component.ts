import {
  Component,
  ViewChild,
  TemplateRef,
  OnInit,
  OnDestroy,
  ComponentFactoryResolver,
  ViewContainerRef,
  ComponentRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import {
  MatSlideToggleChange,
  MatSlideToggleModule,
} from '@angular/material/slide-toggle';
import { MatDialog } from '@angular/material/dialog';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { CatalogModel } from 'src/app/shared/models/catalog-setting/catalog.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SelectDynamicComponent } from 'src/app/shared/components/select-dynamic/select-dynamic.component';
import { Router } from '@angular/router';
import { catchError, of, Subscription } from 'rxjs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-all-catalog',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TableComponent,
    SelectDynamicComponent,
    Modal2Component,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatFormFieldModule,
    MatTooltipModule
  ],
  templateUrl: './all-catalog.component.html',
  styleUrls: ['./all-catalog.component.scss'],
})
export class AllCatalogComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  private _selectDynamicComponenteSubscription?: Subscription;

  form: FormGroup = this._fb.group({});
  @ViewChild('editViewPreviousModal') editViewPreviousModal?: TemplateRef<any>;
  @ViewChild('addSelector', { read: ViewContainerRef })
  viewContainerRef?: ViewContainerRef;
  componentsReferences = Array<ComponentRef<SelectDynamicComponent>>();
  private: number = -1;
  dataField: any[] = [];
  catalogTable: CatalogModel[] = [];
  titelModalViewPrevious: string = this._translateService.instant(
    'CatalogSetting.Preview'
  );
  fieldList: any[] = [];
  idBusinessCountry: number = 0;
  idCountry: number = 0;
  idCatalogField: number = 0;
  idJson: number = 0;
  isCountry: boolean = false;
  private dynamicSelectComponents: any[] = [];
  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'CatalogSetting.TableCatalog.Field'
      ),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('CatalogSetting.Preview'),
      columnValue: 'see',
      columnIcon: 'search',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  valuesOption: any[] = [];
  dynamicSelect: any[] = [];

  constructor(
    private _translateService: TranslateService,
    private _utilsSvc: UtilsService,
    public _catalogService: CatalogSettingService,
    private _settingService: SettingService,
    private _msgSvc: MessageService,
    public modalDialog: MatDialog,
    private _fb: FormBuilder,
    public _router: Router,
    private componentFactoryResolver: ComponentFactoryResolver,
    private _customRouter: CustomRouterService,

  ) {}

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      /// traduction title modal
      this.titelModalViewPrevious = this._translateService.instant(
        'CatalogSetting.Preview'
      );

      //traduccion data table catalog
      this.estructTable[0].columnLabel = this._translateService.instant(
        'CatalogSetting.TableCatalog.Field'
      );
      this.estructTable[1].columnLabel =
        this._translateService.instant('Action');
      this.estructTable[2].columnLabel =
        this._translateService.instant('Action');
    });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.idCountry = response.enterprise.pkIIdCountry;

            this.getListCatalog(this.idBusinessCountry, 0);
          }
        }
      );
  }

  getListCatalog(idBusinessCountry: number, idCountry: number) {
    this._catalogService
      .getListCatalog(idBusinessCountry, idCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.catalogTable = response.result;
          }
        }
      });
  }

  changeAloneCountry($event: MatSlideToggleChange) {
    this.isCountry = $event.checked;
    if ($event.checked) this.getListCatalog(0, this.idCountry);
    else this.getListCatalog(this.idBusinessCountry, 0);
  }

  getCatalogFieldByCatalogId(idCatalog: number) {
    this._catalogService
      .getCatalogFieldByCatalogId(idCatalog)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            let value = response.result[0];
            this.valuesOption.push(value);
            this.addSelect(value.iOrder);
          }
        }
      });
  }

  getCatalogDependent(event: any) {
    this.idCatalogField = event.idCatalogField;
    this._selectDynamicComponenteSubscription = this._catalogService
      .getCatalogDependent(event.idCatalogField, event.idJson)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
          this.valuesOption = [];
          this.addSelect(event.order);
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.valuesOption = [];
            this.valuesOption.push(response.result);
            this.addSelect(event.order);
          }
        }
      });
  }

  addSelect(currentLevel: number): void {
    this.dynamicSelect = this.valuesOption;

    // Elimina los selects por encima del nivel actual
    while (currentLevel < this.dynamicSelectComponents.length) {
      const removedComponent = this.dynamicSelectComponents.pop();
      removedComponent.destroy();
    }

    // Crea el nuevo select
    const componentFactory =
      this.componentFactoryResolver.resolveComponentFactory(
        SelectDynamicComponent
      );
    const viewContainerRef =
      this.viewContainerRef?.createComponent(componentFactory);

    if (viewContainerRef) {
      const instance: SelectDynamicComponent =
        viewContainerRef.instance as SelectDynamicComponent;

      // Pasa las nuevas opciones al componente
      instance.valuesOption = this.dynamicSelect;

      // Almacena la instancia para su posterior eliminación
      this.dynamicSelectComponents.push(viewContainerRef);

      //Observa los cambios para buscar si tiene hijos dependientes.
      instance.getCatalogDependent.subscribe((data: any) => {
        this.getCatalogDependent(data);
      });
    }
  }

  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'see':
        this.valuesOption = [];
        this.dynamicSelect = [];
        this.dynamicSelectComponents = [];
        this.getCatalogFieldByCatalogId(event.value.pkIIdCatalog);
        this.openModalViewPrevious();
        break;
      case 'modify':
        if (this.isCountry) {
          this._customRouter.navigate([
            `/dashboard/catalog-setting/modifyCatalog/${
              event.value.pkIIdCatalog
            }/${0}/${this.idCountry}`,
          ]);
        } else {
          this._customRouter.navigate([
            `/dashboard/catalog-setting/modifyCatalog/${
              event.value.pkIIdCatalog
            }/${this.idBusinessCountry}/${0}`,
          ]);
        }

        break;
      default:
        break;
    }
  }

  openModalViewPrevious() {
    this.modalDialog.open(this.editViewPreviousModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  createNew() {
    if (this.isCountry) {
      this._customRouter.navigate([
        `/dashboard/catalog-setting/new/${0}/${this.idCountry}`,
      ]);
    } else {
      this._customRouter.navigate([
        `/dashboard/catalog-setting/new/${this.idBusinessCountry}/${0}`,
      ]);
    }
  }

  catalogComposite(){
    if (this.isCountry) {
      this._customRouter.navigate([
        `/dashboard/catalog-setting/newComposite/${0}/${this.idCountry}`,
      ]);
    } else {
      this._customRouter.navigate([
        `/dashboard/catalog-setting/newComposite/${this.idBusinessCountry}/${0}`,
      ]);
    }
  }
  
  eventCloseModal($event: boolean) {}

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
    this._selectDynamicComponenteSubscription?.unsubscribe();
  }
}
