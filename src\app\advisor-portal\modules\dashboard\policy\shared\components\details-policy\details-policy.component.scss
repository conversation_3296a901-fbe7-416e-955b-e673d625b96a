.cont-detail {
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  width: 100%;
  height: auto;
}

.cont-header-detail {
  background: #f2f3f6;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 700;
}
.cont-data {
  display: flex;
  align-items: center;
  justify-content: space-around;
  gap: 1rem;
  padding: 1rem;
}

.number-customer {
  background: #d7dbe0;
  padding: 0.6rem;
  border-radius: 6%;
}

.status {
  padding: 0.6rem;
  border-radius: 6%;
  background: #d7dbe0;
}

.active {
  background: #dff9f6;
  color: #007d61;
}

.activo {
  background: #dff9f6;
  color: #007d61;
}

.expired {
  background: #ffecc0;
  color: #b97500;
}

.vencido {
  background: #ffecc0;
  color: #b97500;
}

.cancelled {
  background: #fff0f0;
  color: #b2242e;
}

.excluded {
  background: #fff0f0;
  color: #b2242e;
}

.cont-policy-data {
  padding: 1rem;
  p {
    margin: 0px;
  }
}

.label {
  font-weight: 700;
}

.border-custom {
  border-bottom: 2px solid #acacac;
}

.end-border {
  border-bottom: 1px solid #dddcdc;
}

.status-container {
  position: relative;
  display: inline-block;
}

.status-container {
  position: relative;
  display: inline-block;
}

.fixed-tooltip {
  background-color: #c62828; /* rojo */
  color: white;
  font-size: 13px;
  padding: 6px 10px;
  border-radius: 4px;
  position: absolute;
  top: -40px; /* puedes ajustar la posición */
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  z-index: 10;
}

.tooltip-arrow {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #c62828;
}

