import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  OnDestroy,
  OnInit,
  ViewChild,
  Output,
  inject
} from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { MenuModel } from 'src/app/shared/models/menu';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';
import { SizeService } from 'src/app/shared/services/nav-bar/size.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UserService } from 'src/app/shared/services/user/user.service';

declare const bootstrap: any;

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule],
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
})
export class NavbarComponent implements OnInit, OnDestroy{
  private resizeObserver!: ResizeObserver;
  menu: MenuModel[] = [];
  submenu: MenuModel[] = [];
  @ViewChild('navbarMenu', { static: true }) navbarMenu!: ElementRef;
  isNavbarExpanded = false;
  IsOpenWithButton = false;
  isSubMenuExpanded = false;
  isSidebarOpen = false;
  selectedParent: any = null;
  idBusinessByCountry: number = 0;
  currentUrl: string = '';

  private _settingCompanySubscription?: Subscription;
  private localStorageService = inject(LocalStorageService);

  constructor(
    private _userService: UserService,
    private _router: Router,
    private _settingService: SettingService,
    public _translateService: TranslateService,
    private _customRouter: CustomRouterService,
    private elRef: ElementRef,
    private _sizeService: SizeService
  ) {}

  ngAfterViewInit(): void {
    const navbar = this.elRef.nativeElement.querySelector('nav');
    if (navbar) {
      this.resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const width = entry.contentRect.width;
          this._sizeService.updateSize({ width });
        }
      });

      this.resizeObserver.observe(navbar);
    }
  }

  ngOnInit(): void {
    this.currentUrl = this._router.url;

    this.getDataSettingInit();
    this.getIdUserSession();

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.menu.forEach((menu, index) => {
        this.menu[index].parentDescription = this._translateService.instant(
          menu.parentDescription
        );
        const ItemMenu = this.getTraslateItem(menu.parentDescription);
        this.menu[index].parentDescription = ItemMenu;
        this.submenu = menu.submenus;

        this.menu.forEach((List, index) => {
          this.menu[index].sonDescription = this._translateService.instant(
            List.sonDescription
          );
          const ItemMenu = this.getTraslateItem(List.sonDescription);
          this.menu[index].sonDescription = ItemMenu;
          this.localStorageService.setItem(
            'nameModule' + this.menu[index].idSonMenu,
            this.menu[index].sonDescription
          );
          this.localStorageService.setItem(
            'imageModule' + this.menu[index].idSonMenu,
            this.menu[index].image
          );
        });
        this.submenu.forEach((subM, index) => {
          this.submenu[index].sonDescription = this._translateService.instant(
            subM.sonDescription
          );
          const ItemSubMenu = this.getTraslateItem(subM.sonDescription);
          this.submenu[index].sonDescription = ItemSubMenu;
          this.localStorageService.setItem(
            'nameModule' + this.submenu[index].idSonMenu,
            this.submenu[index].sonDescription
          );
        });
      });
    });
  }

  async getIdUserSession() {
    let userIdSession = await this._settingService.getDataSettingInit();
    if (userIdSession) {
      this.getListMenuByIdUser(userIdSession.idUser);
    }
  }

  async getDataSettingInit() {
    let data = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = data.idBusinessByCountry;
  }

  getListMenuByIdUser(id: number) {
    const fullUrl = this._router.url;
    const parts = fullUrl.split('/');
    this._userService
      .getListMenuByIdUser(id, this.idBusinessByCountry, false)
      .subscribe({
        next: (response) => {
          this.menu = this.organizeMenuItems(response.result);

          this.validateMenuUrl();
          this.menu.forEach((menu, index) => {
            if (
              this.menu[index].parentUrl &&
              !this.menu[index].parentUrl.includes('dashboard')
            ) {
              this.menu[
                index
              ].parentUrl = `dashboard/${this.menu[index].parentUrl}`;
            }

            this.menu[index].parentUrl = this.menu[index].sonUrl
              ? `${this._customRouter.getNavigationData()}/${
                  this.menu[index].parentUrl
                }`
              : this.menu[index].parentUrl;

            this.menu[index].parentDescription = this._translateService.instant(
              menu.parentDescription
            );
            const ItemMenu = this.getTraslateItem(menu.parentDescription);
            this.menu[index].parentDescription = ItemMenu;
            this.localStorageService.setItem(
              'nameModule' + this.menu[index].idSonMenu,
              this.menu[index].sonDescription
            );
            this.localStorageService.setItem(
              'imageModule' + this.menu[index].idSonMenu,
              this.menu[index].image
            );

            this.submenu = menu.submenus;
            this.submenu.forEach((subM, index) => {
              if (
                this.submenu[index].sonUrl &&
                !this.submenu[index].sonUrl.includes('dashboard')
              ) {
                this.submenu[
                  index
                ].sonUrl = `/dashboard/${this.submenu[index].sonUrl}`;
              }

              this.submenu[index].sonUrl = this.submenu[index].sonUrl
                ? `${this._customRouter.getNavigationData()}${
                    this.submenu[index].sonUrl
                  }`
                : this.submenu[index].sonUrl;

              this.submenu[index].sonDescription =
                this._translateService.instant(subM.sonDescription);
              const ItemSubMenu = this.getTraslateItem(subM.sonDescription);
              this.submenu[index].sonDescription = ItemSubMenu;
              this.localStorageService.setItem(
                'nameModule' + this.submenu[index].idSonMenu,
                this.submenu[index].sonDescription
              );
            });
          });
        },
      });
  }

  getTraslateItem(key: string): string {
    const translation = this._translateService.instant(key);
    return translation ? translation : key;
  }

  logout() {
    let token = this._userService.getTokenLocalStorage();
    this._userService.logout(token).subscribe({
      next: (response) => {
        if (response) {
          this._userService.removeTokenLocalStorage();
          this._settingService.removeDataSettingInit();
          this._customRouter.deleteNavigationData();  
          localStorage.setItem('autoLoginMSAL', 'false');
        }
        this._customRouter.navigate(['/auth/login']);
      },
    });
  }

  organizeMenuItems(items: any[]): any[] {
    const parentIds = items.map((item) => item.idParentMenu);
    const uniqueParentIds = [...new Set(parentIds)];

    const organizedItems = uniqueParentIds
      .map((uniqueId) => {
        const parentItem = items.find((item) => item.idParentMenu === uniqueId);
        if (!parentItem) {
          return null;
        }

        const submenus = items.filter((item) => {
          return (
            item.idParentMenu === uniqueId &&
            (item.parentDescription !== item.sonDescription ||
              item.parentDescription === 'Cotizar')
          );
        });

        return {
          ...parentItem,
          submenus: submenus.length > 0 ? submenus : [],
        };
      })
      .filter((item) => item !== null)
      .filter((item) => item.parentUrl != 'modules/Cotizar');
    return organizedItems;
  }

  toggleNavbar() {
    this.isNavbarExpanded = !this.isNavbarExpanded;
    this.IsOpenWithButton = !this.IsOpenWithButton;
    if (!this.isNavbarExpanded) {
      this.collapseSubMenu();
    }
  }

  toggleSubMenu(item: any) {
    this.currentUrl = this._router.url;
    this.expandSubMenu(item);
  }

  expandSubMenu(item: any) {
    this.selectedParent = item;
    this.isSubMenuExpanded = true;
  }

  collapseSubMenu() {
    // this.selectedParent = null;
    if (this.IsOpenWithButton == false) {
      this.isSubMenuExpanded = false;
    } else {
      this.currentUrl = this._router.url;
    }
  }

  expandMenu() {
    if (!this.isNavbarExpanded) {
      this.isNavbarExpanded = true;
      const menu = this.navbarMenu.nativeElement;
      const bootstrapCollapse = new bootstrap.Collapse(menu);
      bootstrapCollapse.show();
    }
  }

  collapseMenu() {
    if (!this.isSubMenuExpanded && this.IsOpenWithButton == false) {
      this.isNavbarExpanded = false;
      //this.selectedParent = null;
      const menu = this.navbarMenu.nativeElement;
      const bootstrapCollapse = new bootstrap.Collapse(menu);
      bootstrapCollapse.hide();
    }
  }

  //Valida la URL actual y le pone la clase active al respectivo item en el menú.
  validateMenuUrl() {
    let foundItem = this.menu.find(
      (item: MenuModel) => item.sonUrl === this._router.url
    );
    if (foundItem) {
      this.expandMenu();
      this.toggleSubMenu(foundItem);
    }
  }

  //This function validate when the module is a dynamic module, but taking into account that this modules
  //don't includes the word dashboard, so we need validate with the currentUrl just for active the page when it's clicked.
  validateDynamicRoute(currentUrlModule: string): boolean {
    if (
      this.currentUrl.includes('/dashboard/modules/') &&
      currentUrlModule !== '/dashboard'
    ) {
      return this.currentUrl.includes(currentUrlModule);
    } else {
      return false;
    }
  }

  //It's just a validation similar to "validateDynamicRoute", but in this i don't consider validate what's the currently page
  //this is necesary for sending idMenu and drawing the form.
  isModuleDynamic(currentUrlModule: string): boolean {
    if (currentUrlModule != null) {
      return currentUrlModule.includes('modules');
    }
    return false;
  }

  ngOnDestroy(): void {
    this._settingCompanySubscription?.unsubscribe();
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
}
