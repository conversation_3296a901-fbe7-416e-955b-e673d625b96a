import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { catchError, of, firstValueFrom, Subscription } from 'rxjs';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { StageByStateModel, StageModel } from 'src/app/shared/models/module';
import { MatTabsModule } from '@angular/material/tabs';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { DynamicComponentComponent } from 'src/app/advisor-portal/modules/dashboard/modules/dynamic-component/dynamic-component.component';
import { FormsConfigurationHistoryComponent } from 'src/app/shared/components/forms-configuration-history/forms-configuration-history.component';
import { ManagementHistoryTableComponent } from '../management-history-table/management-history-table.component';
import { ManagementHistoryModel } from 'src/app/shared/models/task';

import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { QuotationFormComponent } from '../../../../../shared/components/quotation/quotation-form/quotation-form.component';
import { SettingService } from '../../../../../shared/services/setting/setting.service';
import { TransactionService } from '../../../../../shared/services/transaction/transaction.service';
import { MatInputModule } from '@angular/material/input';
import { PlansService } from 'src/app/shared/services/plans/plans.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { PolicyDataComponent } from '../policy-data/policy-data.component';

@Component({
  selector: 'app-edit-new-task-tray',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    TranslateModule,
    FormsConfigurationHistoryComponent,
    ManagementHistoryTableComponent,
    DynamicComponentComponent,
    QuotationFormComponent,
    FormsModule,
    MatInputModule,
    PolicyDataComponent
  ],
  templateUrl: './edit-new-task-tray.component.html',
  styleUrls: ['./edit-new-task-tray.component.scss']
})
export class EditNewTaskTrayComponent implements OnInit {
  //array que llena el breadCrum
  arrayCompanyHistoryOut: any[] = [];

  //variables que llegan desde la lista
  idProductModuleIn: number = 0;
  process: string = '';
  product: string = '';
  idTaskState: number = 0;
  idTask: number = 0;
  idStage: number = 0;
  idState: number = 0;
  selectedIndex: number = 0; // Variable para controlar la pestaña seleccionada
  taskTrayList?: Subscription;
  //listas y modelos dinamicos
  stageList: StageModel[] = [];
  stateList: StageByStateModel[] = [];
  formModel?: any;
  progressBar: string[] = [];
  //Variable que almacena los datos de la tabla.
  dataTableManagementHistory: ManagementHistoryModel[] = [];
  isConsultingForm = false
  isViewing = false
  isCreationTask = false
  idBusinessByCountry: number = 0;
  idQuotation: number = 0;
  idProduct: number = 0;
  objQuote: any = null;
  quoteInsurance: string = '';
  quotePlan: string = '';
  quotNumber: string = '';
  formEmitId: number = 0;
  idInsuranceCompany: number = 0;
  formValueEmitId: number = 0;
  statesListComplete: StageByStateModel[] = []

  form: FormGroup = this._fb.group({});
  formQuote: FormGroup = this._fb.group({});
  formulasCalculated: any[] = [];

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _moduleService: ModuleService,
    private _translateService: TranslateService,
    public utilsSvc: UtilsService,
    private _taskTayConfigService: TaskTayConfigService,
    private _settingService: SettingService,
    private _transactionService: TransactionService,
    private _plansService: PlansService,
    private _fieldService: FieldService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.initForm();
    let dataSettingInit = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = dataSettingInit.idBusinessByCountry;

    this._activatedRoute.params.subscribe(async ({ idProductModule, process, product, idTask, idTaskState, idStateModule, idProduct }) => {
      if (idTask && idTaskState && idStateModule) {
        this.idTaskState = idTaskState
        this.idTask = idTask
        this.idState = idStateModule
        this.idProductModuleIn = idProductModule;
        this.isViewing = true;
        await this.getManagementHistory(idTask)
      }
      if (idProductModule && !this.isViewing) {
        this.idProductModuleIn = idProductModule;
        await this.getStages(this.idProductModuleIn);
      }

      if (this.arrayCompanyHistoryOut.length == 0) {
        if (process) {
          this.process = process;
          this.arrayCompanyHistoryOut.push(
            {
              title: this._translateService.instant('FormsConfigurationHistory.Process'),
              subtitle: this.process
            }
          )
        }

        if (product) {
          this.product = product.replace('%', ' ');
          this.arrayCompanyHistoryOut.push(
            {
              title: this._translateService.instant('FormsConfigurationHistory.Product'),
              subtitle: this.product
            }
          )
        }

        if (process == 'Cotización') {
          this.idProduct = idProduct ?? 0;
          this.formQuote = this._fb.group({
            quotePlan: [{value:'', disabled: true}],
            quotNumber: [{ value: '', disabled: true }],
            quoteInsurance: [{ value: '', disabled: true }]
          });

          await this.getQuotationIdByIdTask(idTask);
        }
      }
    });


    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.arrayCompanyHistoryOut[0].title = this._translateService.instant('FormsConfigurationHistory.Product');
      this.arrayCompanyHistoryOut[1].title = this._translateService.instant('FormsConfigurationHistory.Process');
    })


  }

  initForm() {
    this.form = this._fb.group({
      stage: [0, [Validators.required]],
      state: [0, [Validators.required]],
    });

    this.form.get('state')?.valueChanges.subscribe({
      next: (data) => {
        if (data > 0) {
          this.isConsultingForm = false
          
          if (this.dataTableManagementHistory.find(x => x.fkIIdStateModule == data) == undefined) {
            this.isViewing = false
          }
          console.log("data ", data)
          this.getCompleteFormByStage(data)
        }
      },
    });
  }

  //obtiene las etapas por idProductModule
  async getStages(idProductModule: number): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      const response = await firstValueFrom(
        this._moduleService.getStageByIdProductModule(idProductModule).pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }

            resolve(false);
            return of([]); // Retornar un arreglo vacío en caso de error
          })
        )
      );

      if (Array.isArray(response)) {
        resolve(false);
        return; // Manejo del caso en que la respuesta es un arreglo vacío
      } else {
        this.stageList = response.result;
        this.stageList = this.stageList.filter(item => {
          const isInManagementHistory = this.dataTableManagementHistory.some(history => history.fkIIdStage === item.pkIIdStage);
          return isInManagementHistory || item.bActive;
        });
        for (let item of this.stageList) {
          item.bHasParent = false;
          await this.getChildrenStageByIdStageParent(item.pkIIdStage);
        }
        this.selectTabByStageId(this.idStage)
        resolve(true);
      }
    });
  }


  //obtiene las estapas dependientes por idStage
  getChildrenStageByIdStageParent(idStageParent: number) {
    return new Promise((resolve, reject) => {
      this._moduleService
        .getChildrenStageByIdStageParent(idStageParent)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }

            resolve(false);
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            for (let item of resp.result) {
              //deshabilita las etapas dependientes, usar con false al final para habilitarlas al terminar el formulario.   
              if (this.idStage != item.fkIIdStageParent) {
                let stage = this.stageList.filter((s) => s.pkIIdStage == item.fkIIdStageChild)
                if (stage.length > 0)
                  stage[0]['bHasParent'] = true
              }
            }
          }

          resolve(true);
        });
    });
  }

  restartViewing() {
    this.isViewing = false;
  }
  //obtiene los estados al cambiar la pestaña de etapa
  getStageByState(event: any) {
  
    let idStage = (event.tab._implicitContent._declarationLView[15][0].id)
    if(( this.idStage == idStage) || (this.idStage != idStage && this.stateList.length>0) || this.idStage==0){
      this.stateList = [];
    if (this.idStage == idStage) {
      if (!this.isViewing) {
        this.isConsultingForm = true
      }

      var result = this.stageList.find(x => x.pkIIdStage == this.idStage)
      if (result) {
        result.bHasParent = false
      }
    }
    else {
      //this.isViewing = false;
      var result = this.stageList.find(x => x.pkIIdStage == this.idStage)
      if (result && !this.isViewing) {
        result.bHasParent = true
      }

      this.isConsultingForm = false

    }
    this._moduleService
      .getStageByStateById(idStage)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            'No existen estados asociados a la estapa'
          );
        } else {
          this.stateList = resp.result;
          this.statesListComplete = resp.result;
          if (!this.idTask) {
            //Inicializar en el primer estado
            this.form.get('state')?.setValue(this.stateList[0].pkIIdStageByState)
            this.isCreationTask = true
          }
          this.stateList = this.stateList.filter(state => {
            const matchingStage = this.stageList.find(stageItem => {
              return this.dataTableManagementHistory.some(history => history.fkIIdStage === stageItem.pkIIdStage)
                && !stageItem.bActive;
            });
            const hasMatchingState = this.dataTableManagementHistory.some(history => history.fkIIdState === state.pkIIdStageByState);
            return !matchingStage || hasMatchingState;
          });
          for (let item of this.stateList) {
            item.bHasParent = false;
            var filled = this.dataTableManagementHistory.find(x => x.fkIIdStateModule == item.pkIIdStageByState)
            if (filled) {
              item.bHasParent = true;
            }
            this.getChildrenStateByIdStateParent(item.pkIIdStageByState);
          }
        }
      });}
  }

  public isStateSelected(needStage: string, needState: string, actualStage: string){
    const emissionState = this.statesListComplete.find(s => s.vCode === needState)
    return actualStage === needStage && this.form.get('state')?.value === emissionState?.pkIIdStageByState
  }

  //obtiene los estados dependientes por idState
  getChildrenStateByIdStateParent(idStateParent: number) {

    this._moduleService
      .getChildrenStateByIdStateParent(idStateParent)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          const stateMap = new Map(this.stateList.map(state => [state.pkIIdStageByState, state]));
          const historyMap = new Map(this.dataTableManagementHistory.map(history => [history.fkIIdStateModule, history]));
          const processedStates = new Set<number>();

          for (let item of resp.result) {
              const state = stateMap.get(item.fkIIdStateChild);

              if (state && !processedStates.has(state.pkIIdStageByState)) {
                  if (historyMap.has(state.pkIIdStageByState)) {
                      state.bHasParent = true;
                      processedStates.add(state.pkIIdStageByState);
                      continue;
                  }

                  let anyParentInHistoryMap = false;

                  const parentStatesForChild = resp.result.filter((res: { fkIIdStateChild: any; }) => res.fkIIdStateChild === item.fkIIdStateChild);

                  for (let parentItem of parentStatesForChild) {
                      if (historyMap.has(parentItem.fkIIdStateParent)) {
                          anyParentInHistoryMap = true;
                          break;
                      }
                  }

                  state.bHasParent = anyParentInHistoryMap ? false : true;

                  processedStates.add(state.pkIIdStageByState);
              }
          }       
        }
      });
  }

  //obtiene el formulario al cambiar el estado
  getCompleteFormByStage(idState: number) {
    this._moduleService
      .getCompleteFormByStage(idState, true, this.isViewing)
      .pipe(
        catchError((error) => {

          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this._messageService.messageWaring(
            this._translateService.instant('ThereWasAError'),
            'No existen formularios asociados a este estado'
          );
        } else {      
        
          this.progressBar = [];
          this.formModel = resp.result;
          this.isConsultingForm = true;
          const filterFormulas = resp.result.flatMap((p: { fieldCalculateds: any; }) => p.fieldCalculateds ?? []);
          this.formulasCalculated = filterFormulas;

        }
      });
  }

  //obtiene la etapa según el estado.
  getStageByStateByIdState(idState: number) {
    return new Promise((resolve, reject) => {
      this._moduleService
        .getStageByStateByIdState(idState)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            resolve(false);
            return of([]);
          })
        )
        .subscribe(async (resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this.idStage = resp.result.fkIIdStage
            this.form.get('state')?.setValue(resp.result.pkIIdStageByState);
            await this.getStages(this.idProductModuleIn);
          }

          resolve(true);
        });
    });
  }

  selectTabByStageId(idStage: number): void {
    const index = this.stageList.findIndex(stage => stage.pkIIdStage === idStage);
    if (index !== -1) {
      this.selectedIndex = index;
    }
  }

  get stateSelected() {
    return this.form.get('state')?.value
  }


  //Obtiene la lista de la tabla de historico de gestiones por medio del id de tarea.
  getManagementHistory(idTask: number) {
    return new Promise((resolve, reject) => {
      this.taskTrayList = this._taskTayConfigService
        .getManagementHistory(idTask)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            resolve(false);
            return of([]);
          })
        )
        .subscribe(async (resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this.dataTableManagementHistory = resp.result;
              // this.dataTableManagementHistory = resp.result.map((item: any) => {
              //   const vTextCapturedObj = JSON.parse(item.vTextCaptured);
              //   const Observation = vTextCapturedObj.Observation;
              //   return {
              //     ...item,
              //     Observation: Observation
              //   };
              // });
              await this.getStageByStateByIdState(this.idState);
            }
          }
          resolve(true);
        });
    });
  }

  //Get IdQuote By idTask
  getQuotationIdByIdTask(_idTask: number) {
    return new Promise((resolve, reject) => {
      this._transactionService.GetIdQuoteByIdTask(_idTask)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            resolve(false);
            return of([]);
          })
        )
        .subscribe(async (resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this.idQuotation = resp.result;
              await this.getQuoteById(this.idQuotation);
              await this._getInsuranceIdQuotation();
            }
          }
          resolve(true);
        });
    });
  }

  private _getInsuranceIdQuotation(){
    this._transactionService
      .getQuoteWithHeaderById(this.idQuotation)
      .subscribe(async (resp) => {
        let planId = resp?.result?.fkIidPlan
        this._plansService.getPlanById(planId, true)
          .subscribe(async (resp) => {
            this.idInsuranceCompany = resp.result.fkIIdInsuranceCompany;
            this.getFormByProductByCodeTypeForm();
          })
      });
  }

  getFormByProductByCodeTypeForm() {
    if (this.idProduct != 0 && this.idInsuranceCompany != 0) {
      this._fieldService
        .getFormByProductByCodeTypeForm(this.idProduct, 'ISF')
        .pipe(
          catchError((error) => {
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('Warning') + resp.message
              );
            }
            else{
              this.formEmitId = resp.result.pkIIdForm;
            }        
          }
        });
    }
  }

  //Get Quote by id
  getQuoteById(_idQuote: number) {
    return new Promise((resolve, reject) => {
      this._transactionService.GetQuoteById(_idQuote)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            resolve(false);
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this.objQuote = resp.result ?? null;

              if (resp.result) {
                let json = this.objQuote.vSelectedPlanJson ? JSON.parse(this.objQuote.vSelectedPlanJson) : null;
                this.formValueEmitId = this.objQuote.fkIIdFormValueStageEmit

                if (json) {
                  const keyInsurance = json.Insurance;
                  const insurance = Object.keys(keyInsurance)[0];
                  const planKey = keyInsurance[insurance].Plan;
                  const plan = Object.keys(planKey)[0];

                  this.formQuote.patchValue({
                    quotePlan: plan,
                    quotNumber: `${this.idQuotation}`,
                    quoteInsurance: insurance
                  });
                }
              } else {
                this.formQuote.reset();
              }
            }
          }
          resolve(true);
        });
    });
  }
}
