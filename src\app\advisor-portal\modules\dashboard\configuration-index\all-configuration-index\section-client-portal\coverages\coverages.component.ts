import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { ImageUploadModel } from 'src/app/shared/models/file';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { CoverageService } from 'src/app/shared/services/coverages/coverage.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-coverages',
  standalone: true,
  imports: [
    MatInputModule,
    Modal2Component,
    PreventionSqlInjectorDirective,
    TranslateModule,
    ReactiveFormsModule,
    CommonModule,
    TableComponent,
    MatButtonModule,
    MatSelectModule,
    GenericImagePickerComponent
  ],
  templateUrl: './coverages.component.html',
  styleUrls: ['./coverages.component.scss']
})
export class CoveragesComponent implements OnInit{
  @Input() idBusinessByCountry: number = 0;
  @ViewChild('createEditCoverage') createEditCoverage?: TemplateRef<any>;
  _currentModal: MatDialogRef<any> | null = null;
  formSubs?: Subscription;
  
  dataTableCoverages: any[] = [];
  pkCoverageToModify: number = 0;
  imageSrc: string = ''
  formCoverage: FormGroup = this._fb.group({
    vName: [null, Validators.required],
    vType: [null],
    vText: [null, Validators.required],
    vLogo: [null],
  })
  formImage: FormData = new FormData();

  listTypes: any[] = [
    {
      name: 'Tradicional',
      value: ''
    },
    {
      name: 'Normal',
      value: 'normal'
    },
    {
      name: 'Básica',
      value: 'basic'
    },
    {
      name: 'Premium',
      value: 'premium'
    },

  ]

  estructTableCoverages: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('SectionClientPortal.Name'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('SectionClientPortal.Text'),
      columnValue: 'vText',
    },
    {
      columnLabel: this._translateService.instant('SectionClientPortal.Type'),
      columnValue: 'vType',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyCoverage',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteCoverage',
      columnIcon: 'delete',
    },
  ];

  constructor(
    public utilsSvc: UtilsService,
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    private _modalDialog: MatDialog,
    private _cdr: ChangeDetectorRef,
    private _coverageService: CoverageService,
    private _fileService: FileService,
    private _messageService: MessageService
  ){

  }
  
  ngOnInit(): void {
    this._loadCoverages()
  }

  private _loadCoverages(){
    this._coverageService.getCoverages(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error)
          {
            this.dataTableCoverages = [...resp.result];
            this.pkCoverageToModify = 0;
            this._cdr.detectChanges()
          }
      })
    })
  }

  openModal(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this._currentModal = this._modalDialog.open(this.createEditCoverage!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formCoverage.reset();
      })
    })
  }
  changeImage(event: any) {
    let imageName: string = event.dataJson[0].name.split('.')[0];
    let extension: string = event.dataJson[0].type.split('/')[1];
    let base64: string = event.dataString;
    this.formCoverage.get('vLogo')?.setValue({ base64, imageName:`${imageName}.${extension}` });
    this.imageSrc = base64;
  }

  async saveImage(): Promise<number> {
    return new Promise((resolve) => {
      let payload: ImageUploadModel = {
        fileName: this.formCoverage.get('vLogo')?.value.imageName,
        fkIIdBusinessByCountry: this.idBusinessByCountry,
        imageBase64: this.formCoverage.get('vLogo')?.value.base64.split(',')[1],
        pkIIdInsuranceCompanies: 0,
        type: 0
      };
      this._fileService
        .uploadImage(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              resolve(resp.result)
            }
          }
        });
    });
    
  }

  async saveCoverage(){
    if (this.formCoverage.invalid)
      return this.formCoverage.markAllAsTouched()
    let data = {
      ...this.formCoverage.value,
      FkIIdBusinessByCountry: this.idBusinessByCountry,
      FkIIdUploadFile: await this.saveImage()
    }
    delete data['logo']
    if (this.pkCoverageToModify === 0)
      this._coverageService.createCoverage(data).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadCoverages()
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.CoverageCreatedSucessfully')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    else
      this._coverageService.modifyCoverage(
        this.pkCoverageToModify, 
        data
      ).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadCoverages()
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.CoverageModifiedSucessfully')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    this.closeModal()
  }

  closeModal() {
    if (this._currentModal) {
      this._currentModal.close();
      this._currentModal = null;
      this.formSubs?.unsubscribe();
    }
  }

  controller(evt: IconEventClickModel) {
    if (evt.column === 'deleteCoverage')
        {
          this._coverageService.deleteCoverage(evt.value.pkIIdCoverage).subscribe({
              next: (response) => {
                if (!response.error) {
                  this._loadCoverages()
                  this._messageService.messageSuccess(
                    this._translateService.instant('DataSavedSuccessfully'),
                    this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
                  );
                }
              },
              error: (error) => {
                this._messageService.messageInfo(
                  this._translateService.instant('ThereWasAError'),
                  error
                );
              },
            })
        }
    if (evt.column === 'modifyCoverage')
      {
        this.pkCoverageToModify = evt.value.pkIIdCoverage
        this._fileService.getUploadFileById(evt.value.fkIIdUploadFile).subscribe({
          next: (response => {
            if (response.result.vFileName && response.result.imageBase64) {
              let extension = response.result.vFileName.split('.');
              extension = extension[response.result.vFileName.split('.').length - 1];
              this.formCoverage.get('vLogo')?.setValue(`data:image/${extension};base64,${response.result.imageBase64}`);
              this.imageSrc = `data:image/${extension};base64,${response.result.imageBase64}`;
            }
          })
        })
        this.formCoverage.patchValue({
          vName: evt.value.vName,
          vText: evt.value.vText,
          vType: evt.value.vType
        })
        this.openModal()
      }
    
  }
}
