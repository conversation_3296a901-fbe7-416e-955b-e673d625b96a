import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { CdkDragDrop, CdkDropList, CdkDrag, moveItemInArray } from '@angular/cdk/drag-drop';
import { Subscription, catchError, of } from 'rxjs';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { SectionIndexModel } from 'src/app/shared/models/configuration-index/section-index.model';
import { SectionEnum } from 'src/app/shared/models/configuration-index';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { FormsModule } from '@angular/forms';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AllActiveNewsComponent } from '../active-news/all-active-news/all-active-news.component';
import { AllTopsComponent } from '../tops/all-tops/all-tops.component';
import { OrganizationChartComponent } from '../organization-chart/organization-chart.component';

@Component({
  selector: 'app-configuration-advisor',
  templateUrl: './configuration-advisor.component.html',
  styleUrls: ['./configuration-advisor.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatInputModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    CdkDropList,
    CdkDrag,
    TableComponent,
    AllActiveNewsComponent,
    AllTopsComponent,
    TranslateModule,
    OrganizationChartComponent,
    MatDialogModule, Modal2Component, FormsModule,
    PreventionSqlInjectorDirective,
    MatTooltipModule
  ],
})
export class ConfigurationAdvisorComponent implements OnDestroy {
  @ViewChild('editNameCustomerModal') editNameCustomerModal?: TemplateRef<any>;
  private _settingCountryAndCompanySubscription?: Subscription;
  listSectionIndex: SectionIndexModel[] = [];
  listSectIndexUpdat: SectionIndexModel[] = [];

  idBusinessByCountry: number = 0;
  userType: any[] = [];
  nameProduct: string = "";
  titelModal: string = this._translateService.instant('Modify');

  constructor(private _router: Router,
    private _msgSvc: MessageService,
    private _settingService: SettingService,
    private _translateService: TranslateService,
    private _businessService: BusinessService,
    public dialog: MatDialog) {
    this.getSettingCountryAndCompanySubscription();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.titelModal = this._translateService.instant('Modify');
    });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.getSectionByBusinessCountry(
                response.enterprise.pkIIdBusinessByCountry
              );
              this.idBusinessByCountry =
                response.enterprise.pkIIdBusinessByCountry;
            }
          }
        }
      );
  }

  getSectionByBusinessCountry(idBusinessByCountry: number) {
    this._businessService
      .getSectionByBusinessCountry(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
          this.listSectionIndex = [];
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.listSectIndexUpdat = [];
            this.listSectionIndex = resp.result;
            this.searchOrgChartId();
            this.searchActNewsId();
            this.searchTopId();
          }
        }
      });
  }

  /// This event changes the position of the sections in the view and the array
  dropOrderIndex(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.listSectionIndex, event.previousIndex, event.currentIndex);
    this.listSectionIndex.forEach((item, idx) => {
      item.iOrder = idx + 1;
    });
    this.listSectionIndex = this.listSectionIndex.slice();
  }

  /// Change the name of the product
  editNameSectionIndex(event: any) {
    this.nameProduct = event.vName;
    const dialogRef = this.dialog.open(this.editNameCustomerModal!, {
      width: '35vw'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result != undefined) {
        event.vName = result;
        event.isModificatedName = 1
      }
    });
  }

  /// activate and deactivate products
  activateYDeactivateProducts(event: any) {
    event.bActive = !event.bActive;
  }

  updateSectionIndexByBusiness() {
    const list = Array.from(this.listSectionIndex);
    this.listSectionIndex.forEach(val => this.listSectIndexUpdat.push(Object.assign({}, val)));

    this.listSectIndexUpdat.forEach((element) => {
      if (element.isModificatedName == undefined)
        element.vName = "";
    });
    this._businessService
      .updateSectionIndexByBusiness(this.listSectIndexUpdat)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('ThereWasAError'),
            error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.getSectionByBusinessCountry(this.idBusinessByCountry);
            this._msgSvc.messageSuccess(this._translateService.instant('DataSavedSuccessfully'), "");
          }
        }
      });
  }

  //Función para buscar PK del item organigrama
  searchOrgChartId() {
    let foundObject = this.listSectionIndex.find(
      (section: SectionIndexModel) =>
        section.fkIdSectionIndex === SectionEnum.OrganizationChart
    );

    if (foundObject) {
      return foundObject;
    } else {
      return SectionIndexModel.fromObj({});
    }
  }

  //Función para buscar PK del item noticias activas
  searchActNewsId() {
    let foundObject = this.listSectionIndex.find(
      (section: SectionIndexModel) => section.fkIdSectionIndex === SectionEnum.News
    );

    return foundObject;
  }

  //Función para buscar PK del item top
  searchTopId() {
    let foundObject = this.listSectionIndex.find(
      (section: SectionIndexModel) => section.fkIdSectionIndex === SectionEnum.Tops
    );
    return foundObject;
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  eventCloseModal(event: any) {
    console.log('se cerró el modal');
    this.nameProduct = "";
  }
}
