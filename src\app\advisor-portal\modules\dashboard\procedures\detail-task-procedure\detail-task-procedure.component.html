<form>
  <div class="row mt-3 mb-2">
    <mat-label
      ><strong>
        {{
          "ProceduresViewerSettings.DetailsTaskProcedure.IdTask" | translate
        }}</strong
      >
      &nbsp;{{ dataDetailTask.idTarea }} </mat-label
    >
  </div>
  <div class="row mt-3 mb-2">
    <mat-label
      ><strong>{{
        "ProceduresViewerSettings.DetailsTaskProcedure.Process" | translate
      }}</strong
      >&nbsp;{{ dataDetailTask.proceso }}</mat-label
    >
  </div>
  <div class="row mt-3 mb-2">
    <mat-label
      ><strong>{{
        "ProceduresViewerSettings.DetailsTaskProcedure.Product" | translate
      }}</strong
      >&nbsp;{{ dataDetailTask.producto }} </mat-label
    >
  </div>
  <div class="row mt-3 mb-2">
    <mat-label
      ><strong>{{
        "ProceduresViewerSettings.DetailsTaskProcedure.Stage" | translate
      }}</strong
      >&nbsp;{{ dataDetailTask.etapa }} </mat-label
    >
  </div>
  <div class="row mt-3 mb-2">
    <mat-label
      ><strong>{{
        "ProceduresViewerSettings.DetailsTaskProcedure.State" | translate
      }}</strong
      >&nbsp;{{ dataDetailTask.estado }} </mat-label
    >
  </div>
  <div class="row mt-3 mb-2">
    <mat-label
      ><strong>{{
        "ProceduresViewerSettings.DetailsTaskProcedure.DateRequest" | translate
      }}</strong>
      &nbsp;{{ dataDetailTask.fechaSolicitud }} </mat-label
    >
  </div>
  <div class="row mt-3 mb-2">
    <mat-label
      ><strong>{{
        "ProceduresViewerSettings.DetailsTaskProcedure.AssignedTo" | translate
      }}</strong>&nbsp;{{ dataDetailTask.asignadoA }} </mat-label
    >
  </div>
  <div class="row mt-3 mb-2">
    <mat-label
      ><strong>{{
        "ProceduresViewerSettings.DetailsTaskProcedure.CreatedBy" | translate
      }}</strong
      >&nbsp;{{ dataDetailTask.creadoPor }} </mat-label
    >
  </div>

  <div *ngIf="dataDetailTask.fieldName || dataDetailTask.vNameField" class="row mt-3 mb-2">
    <mat-label>
      <strong>{{ dataDetailTask.fieldName || dataDetailTask.vNameField }}:</strong>
      &nbsp;{{ dataDetailTask.fieldValue || '' }}
    </mat-label>
  </div>

  <br />
</form>

<div class="row mt-2">
  <div class="row mb-2">
    <h4 class="col-md-12">
      <strong>{{
        "ProceduresViewerSettings.DetailsTaskProcedure.ManagementHistory"
          | translate
      }}</strong>
    </h4>
  </div>
  <app-table
    [displayedColumns]="estructTaskProcedureTable"
    [data]="dataTaskProcedureTable"
  ></app-table>
</div>
