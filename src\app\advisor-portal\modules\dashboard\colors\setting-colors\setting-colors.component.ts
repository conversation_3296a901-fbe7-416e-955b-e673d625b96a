import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription, catchError, of } from 'rxjs';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { UpdateColorsModel, UserType } from 'src/app/shared/models/colors';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { FontService } from '../../../../../shared/services/font/font.service';
import { FontModel } from '../../../../../shared/models/font/font.model';
import { MatSelectModule } from '@angular/material/select';

@Component({
  selector: 'app-setting-colors',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    TranslateModule,
    PreventionSqlInjectorDirective,
    MatSelectModule
  ],
  templateUrl: './setting-colors.component.html',
  styleUrls: ['./setting-colors.component.scss'],
})
export class SettingColorsComponent implements OnInit, OnDestroy {
  form: FormGroup = new FormGroup({});
  shadeColor: string = '';
  tintColor: string = '';
  primaryColor: string = '';
  secondaryColor: string = '';
  idBusinessByCountry: number = 0;
  settingDefault!: UpdateColorsModel;
  fontList: FontModel[] = [];

  @Output() colors = new EventEmitter<any>();
  @Output() actionFinished = new EventEmitter<any>();
  @Input() actions: string = '';
  @Input() isAdvisor: boolean = false;
  private _settingCountryAndCompanySubscription?: Subscription;

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    private _businessService: BusinessService,
    private _settingService: SettingService,
    private _router: Router,
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    private _fontService: FontService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.getIdBusinessByCountry();
    this.getFonts();
  }

  initForm() {
    this.form = this._fb.group({
      primaryColor: ['', [Validators.required]],
      secondaryColor: ['', [Validators.required]],
      font: [null, [Validators.required]],
    });
  }

  getPrimaryColor() {
    let primaryColor = this.form.get('primaryColor')?.value;
    primaryColor = primaryColor.replace(/^#/, '');
    this.primaryColor = `#${primaryColor}`;
  }

  getSecondaryColor() {
    let secondaryColor = this.form.get('secondaryColor')?.value;
    secondaryColor = secondaryColor.replace(/^#/, '');
    this.secondaryColor = `#${secondaryColor}`;
  }

  getShade() {
    let rbgColorPrimary = this.hexToRgb(this.form.get('primaryColor')?.value);
    let rbgColorTint = {
      r: Math.round(rbgColorPrimary.r * 0.7),
      g: Math.round(rbgColorPrimary.g * 0.7),
      b: Math.round(rbgColorPrimary.b * 0.7),
    };
    this.shadeColor = this.rgbToHex(
      rbgColorTint.r,
      rbgColorTint.g,
      rbgColorTint.b
    );
    this.shadeColor = this.rgbToHex(
      rbgColorTint.r,
      rbgColorTint.g,
      rbgColorTint.b
    );
  }

  getTint() {
    let rbgColorPrimary = this.hexToRgb(this.form.get('primaryColor')?.value);
    let rbgColorTint = {
      r: Math.round(rbgColorPrimary.r + (255 - rbgColorPrimary.r) * 0.8),
      g: Math.round(rbgColorPrimary.g + (255 - rbgColorPrimary.g) * 0.8),
      b: Math.round(rbgColorPrimary.b + (255 - rbgColorPrimary.b) * 0.8),
    };
    this.tintColor = this.rgbToHex(
      rbgColorTint.r,
      rbgColorTint.g,
      rbgColorTint.b
    );
    this.tintColor = this.rgbToHex(
      rbgColorTint.r,
      rbgColorTint.g,
      rbgColorTint.b
    );
  }

  getAllColors() {
    let colors = {
      primaryColor: this.primaryColor,
      secondaryColor: this.secondaryColor,
      shadeColor: this.shadeColor,
      tintColor: this.tintColor,
      font: this.form.get('font')?.value
    };
    return colors;
  }

  hexToRgb(hex: string) {
    // Elimina el símbolo '#' si está presente
    hex = hex.replace(/^#/, '');

    // Asegúrate de que el número hexadecimal tenga una longitud par
    if (hex.length % 2 !== 0) {
      hex = '0' + hex;
    }

    let pares = [];
    for (let i = 0; i < hex.length; i += 2) {
      pares.push(hex.substring(i, i + 2));
    }
    // Convierte los componentes hexadecimales en números decimales
    let r = parseInt(pares[0], 16);
    let g = parseInt(pares[1], 16);
    let b = parseInt(pares[2], 16);

    // Retorna un objeto con los componentes RGB
    return { r: r, g: g, b: b };
  }

  rgbToHex(r: number, g: number, b: number) {
    r = Math.min(255, Math.max(0, r));
    g = Math.min(255, Math.max(0, g));
    b = Math.min(255, Math.max(0, b));

    // Convertir los valores RGB a hexadecimal
    let rHex = r.toString(16).padStart(2, '0');
    let gHex = g.toString(16).padStart(2, '0');
    let bHex = b.toString(16).padStart(2, '0');

    // Combinar los valores hexadecimales para formar el color completo
    let colorHexadecimal = '#' + rHex + gHex + bHex;

    return colorHexadecimal;
  }

  showColors() {
    if (this.form.get('primaryColor')?.value !== '') {
      this.getPrimaryColor();
      this.getShade();
      this.getTint();
      this.colors.emit(this.getAllColors());
    } else {
      this.primaryColor = '';
      this.shadeColor = '';
      this.tintColor = '';
    }
  }

  showSecondaryColor() {
    if (this.form.get('secondaryColor')?.value !== '') {
      this.getSecondaryColor();
      this.colors.emit(this.getAllColors());
    } else {
      this.secondaryColor = '';
    }
  }

  getColorByBusinessCountry(pkIIdBusinessByCountry: number) {
    let idUserTye = (this.isAdvisor) ? UserType.advisor : UserType.client;

    this._businessService
      .getColorByBusinessCountry(pkIIdBusinessByCountry, idUserTye)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
          this.resetValues();
        } else {
          if (response.error) {
            this.resetValues();
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
              response.message
            );
          } else {
            this.settingDefault = response.result;
            this.form
              .get('primaryColor')
              ?.setValue(response.result.v_ColorPrimary);
            this.form
              .get('secondaryColor')
              ?.setValue(response.result.v_ColorSecondary);
            this.form
              .get('font')
              ?.setValue(response.result.fk_i_IdFont);
            if (
              response.result.v_ColorPrimary !== '' &&
              response.result.v_ColorSecondary
            ) {
              this.showColors();
              this.showSecondaryColor();
            }
          }
        }
      });
  }

  getIdBusinessByCountry() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessByCountry =
                response.enterprise.pkIIdBusinessByCountry;
              this.getColorByBusinessCountry(
                response.enterprise.pkIIdBusinessByCountry
              );
            }
          }
        }
      );
  }

  resetValues() {
    this.form.get('primaryColor')?.setValue('');
    this.form.get('secondaryColor')?.setValue('');
    this.form.get('font')?.setValue(null);
    this.primaryColor = '';
    this.secondaryColor = '';
    this.shadeColor = '';
    this.tintColor = '';
    this.colors.emit(this.getAllColors());
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['actions']) {
      this.controller(changes['actions'].currentValue);
    }
  }

  controller(action: string) {
    switch (action) {
      case 'save':
        this.saveSetting();
        break;
      case 'reset':
        this.resetToDefault();
        break;

      default:
        break;
    }
  }

  saveSetting() {
    let paylaod: UpdateColorsModel = {
      pk_i_IdBusinessCountry: this.idBusinessByCountry,
      v_ColorPrimary: this.primaryColor,
      v_ColorSecondary: this.secondaryColor,
      v_ChildShadow: this.shadeColor,
      v_ChildTint: this.tintColor,
      fk_i_IdUserType: (this.isAdvisor) ? UserType.advisor : UserType.client,
      fk_i_IdFont: this.form.get('font')?.value
    };
    this._businessService
      .updateColorByBusinessCountry(paylaod)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(this._translateService.instant('Warning'),error.error.message);
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(this._translateService.instant(response.message));
          } else {
            this.actionFinished.emit('');
            this._msgSvc.messageSuccess('', this._translateService.instant(response.message));
          }
        }
      });
  }

  getFonts() {
    this._fontService.getFonts()
      .subscribe((resp: ResponseGlobalModel) => {
        if (!resp.error) {
          this.fontList = resp.result;
        }
      });
  }

  fontChange() {
    this.colors.emit(this.getAllColors());
  }

  resetToDefault() {
    this.resetValues();
    this.getIdBusinessByCountry();
    this.actionFinished.emit('');
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
