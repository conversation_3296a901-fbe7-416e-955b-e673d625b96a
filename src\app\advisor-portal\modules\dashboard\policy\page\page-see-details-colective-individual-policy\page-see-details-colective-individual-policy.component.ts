import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { DetailsPolicyComponent } from '../../shared/components/details-policy/details-policy.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { HistoryTableComponent } from '../../shared/components/history-table/history-table.component';
import { RiskTableComponent } from '../../shared/components/risk-table/risk-table.component';
import { BodyTableModel } from 'src/app/shared/models/table';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { catchError, of } from 'rxjs';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ActivatedRoute } from '@angular/router';
import { PolicyTableService } from '../../shared/service/policy-table.service';
import { PolicyMovementHistoryModel } from '../../shared/models';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent } from '../page-see-detail-movement-history-colective-individual-policy/page-see-detail-movement-history-colective-individual-policy.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-page-see-details-colective-individual-policy',
  standalone: true,
  imports: [
    CommonModule,
    DetailsPolicyComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    HistoryTableComponent,
    RiskTableComponent,
    Modal2Component,
    PageSeeDetailMovementHistoryColectiveIndividualPolicyComponent,
  ],
  templateUrl: './page-see-details-colective-individual-policy.component.html',
  styleUrls: ['./page-see-details-colective-individual-policy.component.scss'],
})
export class PageSeeDetailsColectiveIndividualPolicyComponent
  implements OnInit
{
  //Variables relacionadas con los modales.
  @ViewChild('viewHistoryMovementModal')
  viewHistoryMovementModal?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;
  modalOpenRerefence: string = '';
  typeMovement: string = '';

  //Variables relacionadas con la póliza.
  detailsPolicyData: any[] = [];
  policyNumber: string = '';
  idWtw: number = 0;
  idPolicyRisk: number = 0;
  idPolicyType: number = 0;
  idHistoryPolicy: number = 0;
  policyStatus: string = '';
  estructTableRisk: BodyTableModel[] = [
    {
      columnLabel: 'Id ' + this._translateService.instant('Reports.Policy'),
      columnValue: 'idPolicyRisk',
    },
    {
      columnLabel: this._translateService.instant('Policy.NameSecured'),
      columnValue: 'nameCustomer',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.DocumentType'),
      columnValue: 'typeDocument',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
      columnValue: 'numberDocument',
    },

    {
      columnLabel: this._translateService.instant('GeneralInformation.StartValidity'),
      columnValue: 'dStartValidity',
    },
    {
      columnLabel: this._translateService.instant('GeneralInformation.EndValidity'),
      columnValue: 'dEndValidity',
    },

    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'state',
      functionValue: (item: any) =>
        this._policyTableService.changeRiskStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant(
        'ProceduresViewerSettings.Manage'
      ),
      columnValue: 'manage',
      columnIcon: 'search',
    },
    {
      columnLabel: this._translateService.instant(
        'DocumentModule.TitleDocument'
      ),
      columnValue: 'documents',
      columnIcon: 'download',
    },
  ];

  constructor(
    private _translateService: TranslateService,
    private _transactionService: TransactionService,
    private _messageService: MessageService,
    private _activatedRoute: ActivatedRoute,
    private _location: Location,
    private _policyTableService: PolicyTableService,
    public modalDialog: MatDialog,
    private _utilsSvc: UtilsService,
    private _customeRouter: CustomRouterService,
  ) {
    this.getDataUrl();
  }

  ngOnInit() {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //Traducción de los datos de la tabla que lista las pólizas individuales.
      this.estructTableRisk[0].columnLabel =
        this._translateService.instant('Policy.NameSecured');
      this.estructTableRisk[1].columnLabel = this._translateService.instant(
        'MyProfile.DocumentType'
      );
      this.estructTableRisk[2].columnLabel = this._translateService.instant(
        'MyProfile.DocumentNumber'
      );
      this.estructTableRisk[3].columnLabel =
        this._translateService.instant('Status');
      this.estructTableRisk[4].columnLabel = this._translateService.instant(
        'ProceduresViewerSettings.Manage'
      );
      this.estructTableRisk[5].columnLabel = this._translateService.instant(
        'DocumentModule.TitleDocument'
      );

      //Traducción de los datos de la tabla Historico de movimientos de póliza.
      this.estructTablePolicyMovementHistory[0].columnLabel =
        this._translateService.instant('Policy.TypeOfMovement');
      this.estructTablePolicyMovementHistory[1].columnLabel =
        this._translateService.instant(
          'PolicyConfiguration.GeneralInformation.PolicyId'
        );
      this.estructTablePolicyMovementHistory[2].columnLabel =
        this._translateService.instant('Policy.UserWhoUpdates');
      this.estructTablePolicyMovementHistory[3].columnLabel =
        this._translateService.instant('Policy.UpdateDate');
      this.estructTablePolicyMovementHistory[4].columnLabel =
        this._translateService.instant('Details');
    });
  }

  //Obtiene los valores de las variables enviadas por medio de la URL.
  getDataUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idPolicy) {
        this.idWtw = Number(params.idPolicy);
        this.getPolicyByIdWithProduct();
        this.getHistoryPolicy();
      }

      if (params.idPolicyType) {
        this.idPolicyType = Number(params.idPolicyType);
      }
    });
  }

  //Tabla Historico de movimientos de póliza.
  titlePolicyMovementHistory: string = this._translateService.instant(
    'Historico de movimientos de póliza'
  );
  policyMovementHistoryData: any[] = [];
  estructTablePolicyMovementHistory: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Policy.TypeOfMovement'),
      columnValue: 'movementsPolicyName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyId'
      ),
      columnValue: 'idPolicy',
    },
    {
      columnLabel: this._translateService.instant('Policy.UserWhoUpdates'),
      columnValue: 'user',
    },
    {
      columnLabel: this._translateService.instant('Policy.UpdateDate'),
      columnValue: 'dateUpdate',
      functionValue: (item: any) => this._utilsSvc.formatDate(item.dateUpdate, 'DD-MM-YYYY'),
    },
    {
      columnLabel: this._translateService.instant('Details'),
      columnValue: 'detailsMovement',
      columnIcon: 'search',
    },
  ];

  //Obtiene el detalle de una póliza.
  getPolicyByIdWithProduct() {
    this._transactionService
      .getPolicyByIdWithProduct(this.idWtw, this.idPolicyType)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.detailsPolicyData = this.transformData(resp.result);

          if (resp.result.policy) {
            this.policyNumber = resp.result.policy.policyNumber;
            this.idWtw = resp.result.policy.idwtw;
            this.policyStatus = resp.result.policy.statePolicy;
          }
        }
      });
  }

  //Obtiene el detalle de una póliza.
  getHistoryPolicy() {
    this._transactionService
      .getHistoryPolicy(this.idWtw)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.policyMovementHistoryData = resp.result;
        }
      });
  }

  ///Obtiene la data de la acción ver detalles en la tabla  Historico de movimientos de póliza.
  getActionTableModificationHistory(event: PolicyMovementHistoryModel) {
    this.idHistoryPolicy = event.id;
    this.typeMovement = event.movementsPolicyName;
    this.openModal('viewHistoryMovementModal');
  }

  //Transforma la información recibida a un estadarizada para que ´pueda mostarse el detalle de cualquier tipo de póliza.
  transformData(dataPolicy: any): any[] {
    const result: any[] = [];

    //Información de la póliza individual a excluir.
    const excludePolicyValues = [
      'policyNumber',
      'statePolicy',
      'idwtw',
      'dDisbursementDate',
      'dDueDate',
      'idPolicyrisk',
      'idParent',
      'endorment',
      'vPaymentFrequency',
      'iPaymentsNumber',
      'iInsuredValue',
      'creditNumber',
      'iPremium',
      'idFilePolicy',
      'fieldKey',
      'fieldName',
      'idHistoryForm',
      "idCatalog",
      "idNovelty",
      "motive",
      "iCreditNumber",
      "dCancelCreditDate",
      "vIdStateCredit",
      "iDisbursementValue",
      "dNoveltyDate",
      "fPremiumYear"
    ];

    // Transformar la sección de `policy`
    if (dataPolicy.policy) {
      const policySection = {
        policy: [
          {
            nameSection: this._translateService.instant('Policy.PolicyData'),
            fields: Object.keys(dataPolicy.policy)
              .filter((key) => !excludePolicyValues.includes(key)) // Filtrar los campos
              .map((key) => {
                let value = dataPolicy.policy[key];
                const regex = /^\d{4}-\d{2}-\d{2}$/;
                if(regex.test(value)){
                  value = this._utilsSvc.formatDate(value, 'DD-MM-YYYY');
                }
                return {
                  name: key,
                  value: value,
                };
              }),
          },
        ],
      };

      result.push(policySection);
    }

    // Transformar la sección de `takers`
    if (dataPolicy.takers && dataPolicy.takers.length > 0) {
      const takersSection = {
        takers: dataPolicy.takers.map((taker: any) => ({
          nameSection: this._translateService.instant('Datos tomador'),
          fields: taker.fields.map((field: any) => ({
            name: field.name,
            value: field.value,
          })),
        })),
      };
      result.push(takersSection);
    }

    // Transformar la sección de `insurances`
    if (dataPolicy.insurances && dataPolicy.insurances.length > 0) {
      const insurancesSection = {
        insurances: dataPolicy.insurances.map((insurance: any) => ({
          nameSection: this._translateService.instant('Datos asegurado'),
          fields: insurance.fields.map((field: any) => ({
            name: field.name,
            value: field.value,
          })),
        })),
      };
      result.push(insurancesSection);
    }

    // Transformar la sección de `others`
    if (dataPolicy.others && dataPolicy.others.length > 0) {
      const othersSection = {
        others: dataPolicy.others.map((other: any) => ({
          nameSection: other.nameSection,
          fields: other.fields.map((field: any) => ({
            name: this.checkAndModifyKeyName(field.name),
            value: field.value,
          })),
        })),
      };
      result.push(othersSection);
    }

    return result;
  }

  checkAndModifyKeyName(keyName: string): string {
    const keysToModify: string[] = ['Plan']
    return keysToModify.includes(keyName) ? keyName + ' ' : keyName;
  }

  // Función que abre un modal, dependiendo el selecctor indicado.
  openModal(modalReference: string) {
    const modalConfiguration = {
      disableClose: false,
      width: '90vw',
      height: 'auto',
    };
    let modal: TemplateRef<any>;
    switch (modalReference) {
      case 'viewHistoryMovementModal':
        this.modalOpenRerefence = 'viewHistoryMovementModal';
        modal = this.viewHistoryMovementModal!;
        break;
      default:
        return;
    }
    //Abre el modal y guarda la referencia en la variable currentModal.
    this.currentModal = this.modalDialog.open(modal, modalConfiguration);
  }

  //Cierra el modal que contiene el hsitorico de movimientos de una póliza.
  closeModalviewHistoryMovement() {
    this.modalDialog.closeAll();
  }

  //Redirige a la página de renovación de pólizas.
  goToRenewal() {
    this._transactionService
      .validateRenewalInProcess(this.idWtw)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result == true){
            this._messageService.messageConfirmatio(
              this._translateService.instant('Policy.RenovationInProgess'),
              '', 'info', 'OK');
          }
          else{
            this._messageService
            .messageConfirmationAndNegation(
              this._translateService.instant('Policy.StartRenovation'),
              '',
              '',
              this._translateService.instant('Continue'),
              this._translateService.instant('Cancel')
            )
            .then((result) => {
              if (result) {
                this._customeRouter.navigate([
                  `dashboard/policy/renewal-colective-individual-policy/${this.idWtw}/${this.idPolicyType}`,
                ]);
              }
            });
          }
        }
      });
  }

  //Redirige a la página anteriror.
  goBack() {
    this._location.back();
  }
}
