<form [formGroup]="form"> 
    <!-- campos categoria, estado y color -->
    <div class="row">
        <!-- Categoria de estado general -->
        <mat-form-field appearance="outline" class="col-4">
            <mat-label>
                {{'Stage.GeneralStatusCategory' | translate}} 
            </mat-label>
            <mat-select  
                (selectionChange)="EmitForm()"
                formControlName="vCategory">
                <mat-option
                    *ngFor="let item of categoriesList"
                    [value]="item.Name"
                >
                    {{ item.Name }}
                </mat-option>
            </mat-select>
            <mat-error
            *ngIf="
                utilsService.isControlHasError(form, 'vCategory', 'required')
            "
            >
                {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>
        </mat-form-field>

        <!-- Estado asgnado -->
        <mat-form-field appearance="outline" class="col-4">
            <mat-label>
                {{ 'Stage.AssignedStatus' | translate }} 
            </mat-label>
            <input
                (change)="EmitForm()"
                matInput
                formControlName="vState"
                required
                type="text"
                PreventionSqlInjector
            />
            <mat-error
                *ngIf="utilsService.isControlHasError(form, 'vState', 'required')"
            >
                {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>
        </mat-form-field>

        <!-- Color de identificacion -->
        <mat-form-field appearance="outline" class="col-4">
            <mat-label>
                {{ 'Stage.IdentificationColor' | translate }}
            </mat-label>
            <mat-select 
                (selectionChange)="EmitForm()"
                formControlName="vColors">
                <mat-option
                    *ngFor="let item of colorsList"
                    [value]="item.Name"
                >
                    {{ item.Name }}
                </mat-option>
            </mat-select>
            <mat-error
            *ngIf="
                utilsService.isControlHasError(form, 'vColors', 'required')
            "
            >
                {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>
        </mat-form-field>        
    </div>      

    <!-- visibilidad del estado -->
    <div class="row mb-2">
        <h5> {{ 'Stage.Visibility' | translate }} </h5>
        <P> {{ 'Stage.ChooseTheVisibilityOfThisStatusInTheProceduresModule' | translate }} </P>
        
        <mat-form-field appearance="outline" class="w-100">
            <mat-label>
                {{ 'Stage.Visibility' | translate }} 
            </mat-label>
            <mat-select
                formControlName="listRoleBusiness"
                multiple
            >
                <mat-option #statusRoleOption
                    *ngFor="let item of roleList"
                    [value]="item"
                    (onSelectionChange)="listRoleBusinessChange(item, statusRoleOption.selected)"
                >
                {{ item.vRoleName }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <!-- boton guardar -->
    <div class="row mb-2" *ngIf="showSaveButton">
        <button
        [disabled]="!form.valid"
        class="ml-1 w-auto"
        type="button"
        (click)="createStageByState()"
        mat-raised-button
        color="primary"
        >
            {{ "Stage.CreateStatus" | translate }}
        </button>
    </div>    
</form>

<!-- dependencias -->
<div class="row mb-2">
    <div class="row mt-2 mb-2">
        <h4 class="col-md-12">
            {{ 'Stage.Dependencies' | translate }}
        </h4>
    </div>    
    <!-- slide estado dependiente -->
    <div class="row mb-3">
        <mat-slide-toggle
            #dependentStage
            [disabled]="!stageByStateModelIn"
            class="w-100"
            (change)="dependentStateChange(dependentStage.checked)"
            [checked]="parentStateList.length>0"
        >
            {{ 'Stage.DependentStatus' | translate }}
        </mat-slide-toggle>
    </div>
    <!-- seleccionar estado padre y botón añadir -->
    <div *ngIf="isDependentState" class="row mb-2">
        <form [formGroup]="dependenciesForm">                
            <h6> {{ 'Stage.SelectStatus' | translate }} </h6>
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>
                    {{ 'Stage.SelectStatus' | translate }} 
                </mat-label>
                <mat-select
                    formControlName="listidStateChild"
                    multiple
                >
                    <mat-option #dependecyOption
                        *ngFor="let item of stateList" 
                        [value]="item.pkIIdStageByState"
                        (onSelectionChange)="selectDependenciesChange(item.pkIIdStageByState, dependecyOption.selected)"
                    >
                        {{ item.vState }}
                    </mat-option>
                </mat-select>
            </mat-form-field>                
        </form>
    </div>
</div>
