import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MatDialog } from '@angular/material/dialog';
import { catchError, Observable, of,finalize } from 'rxjs';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { TaskTrayConfigListModel } from 'src/app/shared/models/task-tray-config';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import {
  AlertModel,
  DynamicFieldsModel,
  FilterTaskTrayModel,
} from 'src/app/shared/models/task';
import { PageEvent } from '@angular/material/paginator';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { ToastComponent } from 'src/app/shared/components/toast/toast.component';
import { ToastModel } from 'src/app/shared/models/toast';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { ErrorHandlingService } from 'src/app/shared/services/error/errorHandlingService';

@Component({
  selector: 'app-all-task-tray',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    TranslateModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    Modal2Component,
    TableComponent,
    MatDatepickerModule,
    MatNativeDateModule,
    ToastComponent,
  ],
  templateUrl: './all-task-tray.component.html',
  styleUrls: ['./all-task-tray.component.scss'],
})
export class AllTaskTrayComponent implements OnInit {
  //referencia a modal de filtros
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;
  //idProductModule que se envia a la vista de crear
  idProductModuleOut: number = 0;

  //variables que se usan para idBusinessCountry.
  dataSetting: any = {};
  idBusinessByCountry: number = 0;
  idProductModule: number = 0;
  idUser:number = 0;

  //Variables que llenan las listas.
  processList: any[] = [];
  productsList: any[] = [];
  filterList: TaskTrayConfigListModel[] = [];
  stateList: any[] = [];
  assignedUserList: any[] = [];

  //variable formulario.
  form: FormGroup = new FormGroup({});
  formFilter: FormGroup = new FormGroup({});

  //variable alertas
  currentAlert: AlertModel | null = null;

  //Estructura y datos de la tabla.
  estructTableTaskTray: BodyTableModel[] = [
    {
      columnLabel: 'Gestionar',
      columnValue: 'manage',
      columnIcon: 'search',
    },
  ];

  //Variables para el apartado de filtros.
  dataTableTaskTray: any[] = [];
  disabledFilter: boolean = true;
  dynamicFields: DynamicFieldsModel[] = [];
  filterRequest: FilterTaskTrayModel = {
    idTarea: 0,
    idStateModule: 0,
    idAssignedUser:0,
    idProcess: 0,
    idProduct: 0,
    idRequestingUser: 0,
    jsonString: '',
    startDate: '',
    endDate: '',
    from: 1,
    pageSize: 5,
  };

  //Variables el paginado de la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;

  //Variables para las alertas.
  toastConfigurationWaring: ToastModel = {
    title: '',
    contBody: '',
    hour: '',
    showToast: false,
    type: '',
    iconClass: 'warning',
  };
  toastConfigurationError: ToastModel = {
    title: '',
    contBody: '',
    hour: '',
    showToast: false,
    type: '',
    iconClass: '',
  };

  alerts: AlertModel[] = [];
  fieldName?: string = '';

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _moduleService: ModuleService,
    private _parametersService: ParametersService,
    private _translateService: TranslateService,
    private _roleService: RoleService,
    public utilsService: UtilsService,
    public filtersDialog: MatDialog,
    private _taskTayConfigService: TaskTayConfigService,
    private _settingService: SettingService,
    private _customRouter: CustomRouterService,
    private _spinner: SpinnerService,
    private _userService: UserService,
    private _errorService: ErrorHandlingService
  ) { }

  ngOnInit(): void {
    this.getIdUserSession();
    this.getProcessList();
    this.initForm();
  }

  initForm() {
    this.form = this._fb.group({
      fkIIdProcess: [''],
      fkIIdProduct: [''],
    });

    this.form.get('fkIIdProcess')?.valueChanges.subscribe({
      next: (data) => {
        this.productsList = [];
        this.estructTableTaskTray = [];
        this.form.get('fkIIdProduct')?.setValue('');
        if (data.pkIIdProcessFamily > 0) {
          this.getProductsList(
            data.pkIIdProcessFamily,
            this.idBusinessByCountry
          );
        }
      },
    });

    this.form.get('fkIIdProduct')?.valueChanges.subscribe({
      next: (data) => { 
        if (data.pkIIdProductModule > 0) { 
          this.cleanfilterRequest();
          this.formFilter.reset();
          this.estructTableTaskTray = [];
          this.idProductModule = data.pkIIdProductModule;
          this.pageIndex = 0;
          this.getStateList(data.pkIIdProductModule);
          this.getAlertsTrakTray(this.filterRequest)
          this.getTaskTrayConfigList(
            this.form.get('fkIIdProcess')?.value.pkIIdProcessFamily,
            data.pkIIdProductModule
          );
          this.getMyTaskConfigFilter(
            this.filterRequest,
            data.pkIIdProductModule,
            this.pageIndex,
            this.pageSize
          );
          this.getUserGroupQuote();
          this.idProductModuleOut = data.pkIIdProductModule;
        }
        
      },
    });
  }

  //Obtine una alerta con sus respectivos estados y ID de tareas.
  getAlertsTrakTray(filterForm: FilterTaskTrayModel) {
    this._taskTayConfigService
      .getAlertsTrakTray(filterForm, this.idBusinessByCountry, this.idProductModule)
      .pipe(
        catchError((error) => {
          // if (error.error.error) {
          //   this._messageService.messageWaring(
          //     this._translateService.instant('ThereWasAError'),
          //     error.error.message
          //   );
          // }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.alerts = resp.result;
          this.alerts.sort((a: AlertModel, b: AlertModel) => {
            if (a.color === 'error' && b.color !== 'error') return -1;
            if (a.color === 'warning' && b.color !== 'warning') return 1;
            return 0;
          });
          this.showAlertInSequence(this.alerts);
        }
      });
  }

  showAlertInSequence(alerts: AlertModel[]) {
    if (alerts.length === 0) return;

    const alert = alerts.shift();

    if (alert) {
      this.currentAlert = alert;

      if (alert.color === 'warning') {
        this.toastConfigurationWaring.showToast = true;
        this.toastConfigurationWaring.title = alert.title;
        this.toastConfigurationWaring.contBody = alert.description;
        this.toastConfigurationWaring.type = alert.color;
        this.toastConfigurationWaring.hour = alert.actualTime;
        this.toastConfigurationWaring.iconClass = 'warning';
      } else if (alert.color === 'error') {
        this.toastConfigurationError.showToast = true;
        this.toastConfigurationError.title = alert.title;
        this.toastConfigurationError.contBody = alert.description;
        this.toastConfigurationError.type = alert.color;
        this.toastConfigurationError.hour = alert.actualTime;
        this.toastConfigurationError.iconClass = 'error_outline';
      }
    }
  }

  onToastClose() {
    if (this.currentAlert?.color === 'warning') {
      this.toastConfigurationWaring.showToast = false;
    } else if (this.currentAlert?.color === 'error') {
      this.toastConfigurationError.showToast = false;
    }

    this.currentAlert = null;

    this.showAlertInSequence(this.alerts);
  }

  initFilterForm() {
    this.formFilter = this._fb.group({
      PKIIdTask: [this.formFilter.get('PKIIdTask')?.value ?? 0],
      fkIIdState: [this.formFilter.get('fkIIdState')?.value ?? 0],
      dStartDate: [this.formFilter.get('dStartDate')?.value ?? ''],
      dEndDate: [this.formFilter.get('dEndDate')?.value ?? ''],
      fkIIdAssignedUser: [this.formFilter.get('fkIIdAssignedUser')?.value ?? 0]
    });

    for (let item of this.filterList) {
      this.formFilter.addControl(item.vFieldName, new FormControl(''));
    }
  }

  //limpia el formulario de filros
  cleanFilterForm() {
    this.dynamicFields = [];
    this.cleanfilterRequest();
    this.formFilter.reset();
    this.formFilter.get('PKIIdTask')?.setValue(0);
    this.formFilter.get('fkIIdState')?.setValue(0);
    for (let item of this.filterList) {
      this.formFilter.setControl(item.vFieldName, new FormControl(''));
    }

    this.getMyTaskConfigFilter(
      this.filterRequest,
      this.idProductModule,
      this.pageIndex,
      this.pageSize
    );
    this.closeModal();
    this.filtersDialog.closeAll();
  }

  //accion al dar en boton gestionar
  controller(event: IconEventClickModel) {
    if (event.column === 'manage') {
      this._customRouter.navigate([
        `/dashboard/task-tray/new/` +
        this.idProductModuleOut +
        '/' +
        this.form.value.fkIIdProcess.vNameProcess +
        '/' +
        this.form.value.fkIIdProduct.vName +
        '/' +
        event.value.Id_de_tarea +
        '/' +
        event.value.pk_i_IdTaskState +
        '/' +
        event.value.fk_i_IdStateModule,
        '/' +
        this.form.value.fkIIdProduct.fkIIdProduct,
      ]);
    }
  }

  openFilterDialog() {
    const dialogRef = this.filtersDialog.open(this.filtersModal!, {
      disableClose: true,
      width: '50vw',
      maxHeight: '90vh',
    });
    this.initFilterForm();
  }

  async getIdUserSession() {
    this.dataSetting = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry = this.dataSetting.idBusinessByCountry;
    this.idUser = this.dataSetting.idUser;
  }

  addTaskTary() {
    this._customRouter.navigate([
      `/dashboard/task-tray/new/` +
      this.idProductModuleOut +
      '/' +
      this.form.value.fkIIdProcess.vNameProcess +
      '/' +
      this.form.value.fkIIdProduct.vName,
    ]);
  }

  //Obtiene todos los procesos regsitrados en el sistema
  getProcessList() {
    this._parametersService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            this.processList = resp.result;
          }
        }
      });
  }

  //obtiene los productos registrados por proceso y idBusinessCountry
  getProductsList(idProcess: number, idBusinessCountry: number) {
    this._roleService
      .getProductsByIdProcess(idProcess, idBusinessCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant('TaskTray.Messages.NoProducts')
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.form.get('fkIIdProduct')?.disable();
        } else {
          if (resp.error) {
          } else {
            if (resp.result.length > 0) {
              this.form.get('fkIIdProduct')?.enable();
              this.productsList = resp.result;
              this.dataTableTaskTray = [];
            }
          }
        }
      });
  }

  //obtiene la lista de tareas por idProductModule
  async getMyTaskConfigFilter(
    filterForm: FilterTaskTrayModel,
    idProductModule: number,
    from?: number,
    pageSize?: number
  ) {
    this.dataTableTaskTray = [];
    await this._spinner.show();
    this._taskTayConfigService
      .getMyTaskConfigFilterCustomer(
        filterForm,
        idProductModule,
        this.idBusinessByCountry, 
        false
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this.validateModalButton();
            this.dataTableTaskTray = [];
            this.estructTableTaskTray = [];
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        }),
        finalize(() => {
          this._spinner.hide();
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this._messageService.messageWaring(
            this._translateService.instant('WarningMessage'),
            this._translateService.instant('TaskTray.Messages.NoDataProduct')
          );
          this.dataTableTaskTray = [];
          this.validateModalButton();
        } else {
          this.dataTableTaskTray = resp.result;
          //Post proccesing data.
          this.replaceCatalogValues(this.filterFieldsDynamically(resp.result));
          this.amountRows = resp.rowCount;
          if (this.dataTableTaskTray.length > 0) {
            this.disabledFilter = false;

            this.processTaskData();

          } else {
            this.disabledFilter = true;
          }
        }
      });
  }

  //obtiene la lista de estados por producto
  getStateList(idProduct: number) {
    this.stateList = [];
    this._moduleService
      .getStageByIdProductModule(idProduct)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this.disabledFilter = true;
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.disabledFilter = true;
        } else {
          for (let item of resp.result) {
            this._moduleService
              .getStageByStateById(item.pkIIdStage)
              .pipe(
                catchError((error) => {
                  if (error.error.error) {
                    this.disabledFilter = true;
                    this._messageService.messageWaring(
                      this._translateService.instant('ThereWasAError'),
                      error.error.message
                    );
                  }
                  return of([]);
                })
              )
              .subscribe((resp2: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp2)) {
                  this.disabledFilter = true;
                } else {
                  for (let item of resp2.result) {
                    this.stateList.push(item);
                  }
                }
              });
          }
        }
      });
  }

  //Obtiene la configuración de bandeja de tareas, para armar dinamicamente la tabla de bandeja de tareas.
  getTaskTrayConfigList(idProcess: number, idProduct: number) {
    this.filterList = [];
    this.dynamicFields = [];
    this._taskTayConfigService
      .getTaskTrayConfigTray(this.idBusinessByCountry, idProcess, idProduct)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.generateTablestructureDynamically(resp.result);
          resp.result.forEach((element: TaskTrayConfigListModel) => {
            if (element.bIsFilter) {
              this.filterList.push(element);
            }
          });
        }
      });
  }


  //Obtiene la configuración de bandeja de tareas, para armar dinamicamente la tabla de bandeja de tareas.
  getUserGroupQuote() {
    this._userService
      .getUserGroupQuote(this.idUser, this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.assignedUserList = resp.result;
        }
      });
  }

  //Función que construye dinamicamente, las columnas de la tabla en base a la configuración realizada por el usuario.
  generateTablestructureDynamically(data: TaskTrayConfigListModel[]) {
    let firstColumn = {
      columnLabel: 'Gestionar',
      columnValue: 'manage',
      columnIcon: 'search',
    };
    this.estructTableTaskTray = [];
    this.estructTableTaskTray.push(firstColumn);
    data.forEach((element: TaskTrayConfigListModel) => {
      let field: BodyTableModel = {
        columnLabel: element.vFieldName,
        columnValue: this.generateColumnValue(element),
      };
      if (this.estructTableTaskTray.length < 16)
        if (element.vFieldName === 'Estado') {
          field.functionValue = (item: any) => this.utilsService.changeStatusTaskValue(item)
        }
        this.estructTableTaskTray.push(field);
      
    });
    let ans: BodyTableModel = {
      columnLabel: 'ANS',
      columnValue: 'ANS',
    };
    let Tiempo_Restante_ANS: BodyTableModel = {
      columnLabel: 'Tiempo Restante ANS',
      columnValue: 'Tiempo_Restante_ANS',
    };
    this.estructTableTaskTray.push(ans);
    this.estructTableTaskTray.push(Tiempo_Restante_ANS);
  }

  //Función que genera el nombre de llave del valor que tomará el registro en la tabla.
  generateColumnValue(data: TaskTrayConfigListModel): string {
    let result: string = '';
    if (data.fkIIdField) {
      result = `${data.fkIIdField}_${data.vFieldName}`;
    } else {
      result = this.generateNameNotDb(data.vFieldName);
    }
    return result;
  }

  generateNameNotDb(name: string): string {
    return name.replace(/\s/g, '_');
  }

  getDataDynamicFields() {
    //Asignamos el valor del formulario en una variable temporal para poder procesar la información.
    let formValue = this.formFilter.value;

    //Guardamos los nombres y el id de los campos dinamicos.
    this.filterList.forEach((element: TaskTrayConfigListModel) => {
      let fieldDynamic: DynamicFieldsModel = {
        idValue: element.fkIIdField,
        vName: element.vFieldName,
        valueField: '',
      };
      this.dynamicFields.push(fieldDynamic);
    });

    //Verificamos si los campos dinamicos son estandares o adicionales.
    this.dynamicFields.forEach((element: DynamicFieldsModel) => {
      if (element.vName in formValue) {
        element.valueField = this.formFilter.get(element.vName)?.value;
      }
    });

    //Obtenemos la data de los campos dinamicos estandares.
    this.getDataStaticFields();
    this.filterDynamicFields();
  }

  getDataStaticFields() {
    this.filterRequest = {
      idTarea: this.formFilter.get('PKIIdTask')?.value,
      idStateModule: this.formFilter.get('fkIIdState')?.value,
      idAssignedUser: this.formFilter.get('fkIIdAssignedUser')?.value,
      idProcess: 0,
      idProduct: 0,
      idRequestingUser: 0,
      jsonString: '',
      startDate: this.formFilter.get('dStartDate')?.value,
      endDate: this.formFilter.get('dEndDate')?.value,
      from: this.pageIndex,
      pageSize: this.pageSize,
    };
    let formValue = this.formFilter.value;

    if ('Proceso' in formValue) {
      this.filterRequest.idProcess = Number(
        this.formFilter.get('Proceso')?.value
      );
    }
    if ('Tipo de producto' in formValue) {
      this.filterRequest.idProduct = Number(
        this.formFilter.get('Tipo de producto')?.value
      );
    }
    if ('Creador de tarea' in formValue) {
      this.filterRequest.idRequestingUser =
        this.formFilter.get('Creador de tarea')?.value;
    }
  }

  //Verificamos si los campos dinamicos son estandares o adicionales.
  filterDynamicFields() {
    let dynamicFieldsFilter = this.dynamicFields.filter(
      (obj) => obj.idValue !== null
    );

    let auxField: DynamicFieldsModel[] = [];

    dynamicFieldsFilter.forEach((element: DynamicFieldsModel) => {
      if (element.valueField) {
        auxField.push(element);
      } else {
        this.filterRequest.jsonString = '';
      }
    });
    this.filterRequest.jsonString = JSON.stringify(auxField);
  }

  applyFilters() {
    this.pageIndex = 0;
    this.getDataDynamicFields();
    this.filterRequest.from = 1;
    this.getMyTaskConfigFilter(
      this.filterRequest,
      this.idProductModuleOut,
      this.pageIndex,
      this.pageSize
    );
    this.closeModal();
    this.filtersDialog.closeAll();
  }

  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 1;
    }
    this.filterRequest.from = this.pageIndex + 1;
    this.filterRequest.pageSize = this.pageSize;
    this.getMyTaskConfigFilter(
      this.filterRequest,
      this.idProductModuleOut,
      this.pageIndex + 1,
      this.pageSize
    );
  }

  //Función que ejecuta acciones personalizadas, cada vez que se cierre el modal de filtro.
  closeModal() {
    this.filterList = [];
    this.dynamicFields = [];
    this.getTaskTrayConfigList(
      this.form.get('fkIIdProcess')?.value.pkIIdProcessFamily,
      this.form.get('fkIIdProduct')?.value.pkIIdProductModule
    );
  }

  //Función que realiza ciertas validación para definir si el boton de aplicar filtros se habilita o no.
  validateModalButton() {
    if (
      this.form.get('fkIIdProcess')?.value.pkIIdProcessFamily > 0 &&
      this.form.get('fkIIdProduct')?.value.pkIIdProductModule > 0
    ) {
      this.disabledFilter = false;
    } else {
      this.disabledFilter = true;
    }
  }

  processTaskData() {
 
    // Verificar si algún registro tiene FieldValue con valor
    const hasFieldValue = this.dataTableTaskTray.some(task => task.FieldValue !== null);
  
    if (hasFieldValue) {
      // Obtener el FieldName de la tarea que tiene valor en FieldValue
      this.fieldName = this.dataTableTaskTray.find(task => task.FieldValue !== null)?.FieldName;
  
      if (this.fieldName) {
        // Ahora que tienes el FieldName (clave), añades una columna más a la estructura ya creada
        let fieldKey: BodyTableModel = {
          columnLabel: this.fieldName,
          columnValue: 'FieldValue',
        };
  
        // Verificar si la columna ya existe
        const columnExists = this.estructTableTaskTray.some(column => column.columnValue === fieldKey.columnValue);
        if (!columnExists) {
          // Encontrar la posición de la columna 'id de tarea'
          const indexIdTarea = this.estructTableTaskTray.findIndex(column => column.columnValue === 'Id_de_tarea');
  
          // Insertar la nueva columna después de la columna 'id de tarea'
          if (indexIdTarea !== -1) {
            this.estructTableTaskTray.splice(indexIdTarea + 1, 0, fieldKey);
          } else {
            // Si no se encuentra la columna 'idTarea', simplemente agregar al inicio
            this.estructTableTaskTray.unshift(fieldKey);
          }
  
          // Clonar el array para forzar la detección de cambios en Angular
          this.estructTableTaskTray = [...this.estructTableTaskTray];
          console.log("estructura actualizada ", this.estructTableTaskTray);
        } else {
          console.log("La columna ya existe, no se añadirá de nuevo.");
        }
      }
    }
  }

  cleanfilterRequest(){
    this.pageIndex = 0;
    this.filterRequest = {
      idTarea: 0,
      idStateModule: 0,
      idAssignedUser:0,
      idProcess: 0,
      idProduct: 0,
      idRequestingUser: 0,
      jsonString: '',
      startDate: '',
      endDate: '',
      from: 1,
      pageSize: 5,
    };
  }

  cancel(){    
    this.closeModal();
    this.filtersDialog.closeAll();
  }


  filterFieldsDynamically(taskConfigData: any[]): Record<string, any>[]{
    const listTaskFiltered=[]
  
    for (const task of taskConfigData) {
      const result: Record<string, any> = {};
      for (const key in task) {
        const parts = key.split('_');
  
        if (parts.length === 2) {
          const idField = parseInt(parts[0], 10);
          if (!isNaN(idField)) {
            result[key] = task[key];
          }
        }
      }
      listTaskFiltered.push(result)
    }
    return listTaskFiltered;
  }

  replaceCatalogValues(listTask:Record<string, any>[]) {
    this._moduleService
      .replaceCatalogValues(listTask)
      .pipe(
        catchError((error) => {
          this._errorService.handleError(error, false);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            this.dataTableTaskTray = this.mergeJsonData(this.dataTableTaskTray, resp.result);
          }
        }
      });
  }

  mergeJsonData(originalJson: any, newJson: any): any {
    for (let i = 0; i < newJson.length; i++) {
      for (const key in newJson[i]) {
        originalJson[i][key] = newJson[i][key];  // Mezcla el valor del segundo JSON en el primero
     }
    }
    return originalJson;
  }
  
  

}
