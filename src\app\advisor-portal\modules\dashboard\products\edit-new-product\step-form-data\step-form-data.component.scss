.group-flow{
    background-color: #F2F3F6;
}

:host ::ng-deep .mat-mdc-tab-body-content {
    height: 100%;
    overflow: hidden;
}

.button-container {
    display: flex;
    justify-content: center;
    gap: 10px; /* Espacio entre botones */
}

.button-container button {
      margin: 10px;
}

.label-button {
    font-size: 1.2rem !important;
    font-weight: bold;
}

a {
    text-decoration: none;
}

mat-hint{
    color: gray;
}


.upload-container {
    margin: 20px 0;
  }
  .upload-area {
    border: 2px dashed #cccccc;
    padding: 30px;
    border-radius: 8px;
    cursor: pointer;
    display: inline-block;
    width: 100%;
    max-width: 700px;
    position: relative;
  }
  .upload-area.dragover {
    border-color: #4caf50;
  }
  .upload-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #007bff;
    font-size: 16px;
    text-align: center;
  }
  .upload-label mat-icon {
    font-size: 40px;
    color: #007bff;
  }
  .upload-info {
    margin-top: 10px;
    font-size: 14px;
    color: #6c757d;
  }
  