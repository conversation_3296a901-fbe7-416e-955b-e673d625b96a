import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PageRenewalIndividualPolicyComponent } from './page-renewal-individual-policy.component';

describe('PageRenewalIndividualPolicyComponent', () => {
  let component: PageRenewalIndividualPolicyComponent;
  let fixture: ComponentFixture<PageRenewalIndividualPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PageRenewalIndividualPolicyComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PageRenewalIndividualPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
