import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { TableComponent } from "../../../../../shared/components/table/table.component";
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatDialog } from '@angular/material/dialog';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { catchError, of, Subscription } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ClientManagementConfigModel } from 'src/app/shared/models/client-management/client-management-config.model';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Router } from '@angular/router';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';

@Component({
  standalone: true,
  selector: 'app-all-client-management-setting',
  templateUrl: './all-client-management-setting.component.html',
  styleUrls: ['./all-client-management-setting.component.scss'],
  imports: [
    CommonModule,
    MatInputModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    TableComponent,
    TranslateModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    Modal2Component,
    MatSlideToggleModule,
  ]
})
export class AllClientManagementSettingComponent implements OnInit {

  private _settingCountryAndCompanySubscription?: Subscription;
  @ViewChild('createEditElementStateModal')
  createEditElementStateModal?: TemplateRef<any>;

  @ViewChild('createEditElementMethodModal')
  createEditElementMethodModal?: TemplateRef<any>;
  
  formFilter: FormGroup = new FormGroup({});
  formCreateEditElementState: FormGroup = new FormGroup({});
  formCreateEditElementMethod: FormGroup = new FormGroup({});
  estructStateSettingsTable: BodyTableModel[] = [];
  // taskTraySettingDataTable: QuotationConfigListModel[] = [];
  estructMethodSettingsTable: BodyTableModel[] = [];
  pageSize: number = 5;
  titleModalState: string = 'Estados de gestión de cliente';
  // fields: FieldTaskTrayConfigModel[] = [];
  stateTable: ClientManagementConfigModel[] = [];
  methodTable: ClientManagementConfigModel[] = [];
  colorsList: any[] = [];
  codTypeEC: string = 'EC';
  codTypeMC: string = 'MC';
  idBusinessByCountry: number = 0;

  constructor(
    private _fb: FormBuilder,
    public _matDialog: MatDialog,
    private _messageService: MessageService,
    private _parametersService: ParametersService,
    public router: Router,
    private _settingService: SettingService,
    public utilsSvc: UtilsService,
    private _transactionService: TransactionService,
    private _translateService: TranslateService,
  ) { }

  ngOnInit() {
    this.estrucTable();

    this._settingCountryAndCompanySubscription =
    this._settingService.currentSettingCountryAndCompany.subscribe(
      (response) => {
        if (this.router.url == response.currentModule) {
          if (!(Object.keys(response).length === 0)) {

            this.idBusinessByCountry = response.enterprise.pkIIdBusinessByCountry;

            this.stateTable = [];
            this.methodTable= [];
            this.getClientStatusList(this.codTypeEC, this.idBusinessByCountry);
            this.getClientStatusList(this.codTypeMC, this.idBusinessByCountry);

          }
        }
      }
    );

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table configuración de state.
      this.estructStateSettingsTable[0].columnLabel =
        this._translateService.instant('SettingsQuotes.Management.StateName');

      this.estructStateSettingsTable[1].columnLabel =
        this._translateService.instant('SettingsQuotes.Management.State');

      this.estructStateSettingsTable[2].columnLabel =
        this._translateService.instant('SettingsQuotes.Management.modify');

      this.estructStateSettingsTable[3].columnLabel =
        this._translateService.instant('SettingsQuotes.Management.eliminate');


      //traduccion data table configuración de method
      this.estructMethodSettingsTable[0].columnLabel =
        this._translateService.instant('SettingsQuotes.Management.Modal.MethodName');

      this.estructMethodSettingsTable[1].columnLabel =
        this._translateService.instant('SettingsQuotes.Management.State');

      this.estructMethodSettingsTable[2].columnLabel =
        this._translateService.instant('SettingsQuotes.Management.modify');

      this.estructMethodSettingsTable[3].columnLabel =
        this._translateService.instant('SettingsQuotes.Management.eliminate');

    });

  }

  // Definición de las columnas
  estrucTable(){
    this.estructStateSettingsTable = [
      { columnLabel: this._translateService.instant('SettingsQuotes.Management.StateName'), columnValue: 'value',}, // Nombre de estado
      { columnLabel: this._translateService.instant('SettingsQuotes.Management.State'), columnValue: 'active', functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),}, // Estado
      { columnLabel: this._translateService.instant('SettingsQuotes.Management.modify'), columnValue: 'editState', columnIcon: 'create',}, // Modificar
      { columnLabel: this._translateService.instant('SettingsQuotes.Management.eliminate'), columnValue: 'deleteState', columnIcon: 'delete',}, // Eliminar
    ];

    this.estructMethodSettingsTable = [
      { columnLabel: this._translateService.instant('SettingsQuotes.Management.Modal.MethodName'), columnValue: 'value',}, // Método de contacto
      { columnLabel: this._translateService.instant('SettingsQuotes.Management.State'), columnValue: 'active', functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),}, // Estado
      { columnLabel: this._translateService.instant('SettingsQuotes.Management.modify'), columnValue: 'editMethod', columnIcon: 'create',}, // Modificar
      { columnLabel: this._translateService.instant('SettingsQuotes.Management.eliminate'), columnValue: 'deleteMethod', columnIcon: 'delete',}, // Eliminar
    ];
  }


  getClientStatusList(codType: string, idBusinessByCountry: number) {
    this._transactionService
      .getManagementOptionsConfigList(codType, idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
          } else {
            if (codType == this.codTypeEC) {
              this.stateTable = resp.result;
            } else {
              this.methodTable= resp.result;
            }
          }
        }
      });
  }

  getColorsList(){
    this._parametersService
      .getParameters('ColorsStates_Stages').subscribe(
        (resp: any) => {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.colorsList = resp;
          }
        }
      );
  }

  //Busca un elemento de la configiracion de mi cotizacion por el id.
  getManagementOptionsConfigById(idManagementOptions: number, codigo : string) {
    this._transactionService
      .getManagementOptionsConfigById(idManagementOptions)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (this.codTypeEC == codigo){
            this.formCreateEditElementState.patchValue(resp.result);
          } else {
            this.formCreateEditElementMethod.patchValue(resp.result);
          }
        }
      });
  }

  async controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'editState':
          this.getManagementOptionsConfigById(event.value.idManagementOptions, this.codTypeEC);
          this.openModalCreateEditElementState();
          break;
      case 'editMethod':
        this.getManagementOptionsConfigById(event.value.idManagementOptions, this.codTypeMC);
        this.openModalCreateEditElementMethod();
        break;
      case 'deleteState':
        this.initFormCreateEditElementState();
        this.deleteOptions(this.codTypeEC, event.value);
        break;
      case 'deleteMethod':
        this.initFormCreateEditElementMethod();
        this.deleteOptions(this.codTypeMC, event.value);
        break;
      default:
        break;
    }


  }

  initFormCreateEditElementState() {
    this.formCreateEditElementState = this._fb.group({
      idManagementOptions: [0],
      value: [''],
      codType: [this.codTypeEC],
      active: [true, [Validators.required]],
      vColor: [''],
      isDeleted: [false, [Validators.required]],
      idBusinessByCountry: [0],
    });
  }

  initFormCreateEditElementMethod() {
    this.formCreateEditElementMethod = this._fb.group({
      idManagementOptions: [0],
      value: [''],
      codType: [this.codTypeMC],
      active: [true, [Validators.required]],
      isDeleted: [false, [Validators.required]],
      idBusinessByCountry: [0],
    });
  }


  openModalCreateEditElementState() {
  
    this.initFormCreateEditElementState();
    this.getColorsList();

    this._matDialog.open(this.createEditElementStateModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  openModalCreateEditElementMethod() {
  
    this.initFormCreateEditElementMethod();

    this._matDialog.open(this.createEditElementMethodModal!, {
      width: '60vw',
      height: 'auto',
    });
  }

  closeModalEvent(event: boolean) {

  }

  deleteOptions(codigo: string, value: any) {
    const form = codigo === this.codTypeEC ? this.formCreateEditElementState : this.formCreateEditElementMethod;
    form.get('idManagementOptions')?.setValue(value.idManagementOptions);
    form.get('value')?.setValue(value.value);
    form.get('codType')?.setValue(value.codType);
    form.get('active')?.setValue(value.active);
    form.get('vColor')?.setValue(value.vColor);
    form.get('idBusinessByCountry')?.setValue(this.idBusinessByCountry);
    form.get('isDeleted')?.setValue(true);


    this._messageService
      .messageConfirmationAndNegation(
        '¿'+this._translateService.instant('Delete') + '?',
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this.editElement(codigo, true)
        }
      });
      
  }

  completeState() {
    if (this.formCreateEditElementState.get('idManagementOptions')?.value > 0) {
      this.editElement(this.codTypeEC);
    } else {
      this.createElement(this.codTypeEC);
    }
  }

  completeMethod() {
    if (this.formCreateEditElementMethod.get('idManagementOptions')?.value > 0) {
      this.editElement(this.codTypeMC);
    } else {
      this.createElement(this.codTypeMC);
    }
  }

  // crear options gestion cliente 
  createElement(codigo: string) {
   
    const form = codigo === this.codTypeEC ? this.formCreateEditElementState : this.formCreateEditElementMethod;
    form.get('idBusinessByCountry')?.setValue(this.idBusinessByCountry);
    
    if (this.validformCreateEditElement) {
      let payload: ClientManagementConfigModel =
      form.getRawValue();
      this._transactionService
        .createManagementOptionsConfig(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              '',
              this._translateService.instant(
                'ModulesSetting.SuccessfulMessageCreated'
              )
            );
            this.getClientStatusList(codigo, this.idBusinessByCountry);
            this._matDialog.closeAll();
          }
        });
    }
  }

  // actualizar options gestion cliente 
  editElement(codigo: string, isDelete : boolean = false) {
    const form = codigo === this.codTypeEC ? this.formCreateEditElementState : this.formCreateEditElementMethod;

    if (this.validformCreateEditElement) {
      let payload: ClientManagementConfigModel =
      form.getRawValue();
      this._transactionService
        .updateManagementOptionsConfig(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            let message = isDelete ? 'Eliminado' : this._translateService.instant('TaskTraySettings.FormCreateEditElement.Messages.Modified')
            this._messageService.messageSuccess(
              '',
              message
            );
            
            this.getClientStatusList(codigo, this.idBusinessByCountry);
            this._matDialog.closeAll();
          }
        });
    }
  }

  get validformFilter(): boolean {
    return this.formFilter.valid;
  }

  get validformCreateEditElement(): boolean {
    return this.formCreateEditElementState.valid;
  }


}
