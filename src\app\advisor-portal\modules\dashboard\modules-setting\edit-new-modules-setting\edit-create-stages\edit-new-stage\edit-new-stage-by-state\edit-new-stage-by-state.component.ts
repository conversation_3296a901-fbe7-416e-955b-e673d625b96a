import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { RolesListModel } from 'src/app/shared/models/role';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { Subscription, catchError, of, take } from 'rxjs';
import { ChildrenStateModel, StageByStateModel } from 'src/app/shared/models/module';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';

@Component({
  selector: 'app-edit-new-stage-by-state',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    TranslateModule,
    MatSlideToggleModule,
    MatSelectModule,
    PreventionSqlInjectorDirective
  ],
  templateUrl: './edit-new-stage-by-state.component.html',
  styleUrls: ['./edit-new-stage-by-state.component.scss']
})
export class EditNewStageByStateComponent implements OnInit, OnDestroy{
  
  @Input() idStageIn: number = 0;
  @Input() idisMultipleStateIn: boolean = false;
  //modelo de stageByState en caso de que se valla a editar. 
  @Input() stageByStateModelIn?: StageByStateModel;

  @Output() resultOut = new EventEmitter<any>();
  form: FormGroup = new FormGroup({});  
  dependenciesForm: FormGroup = new FormGroup({});
  //listas de categorias y colores que se muestran en el html de crear estado. 
  categoriesList: any[] = [];
  colorsList: any[] = [];
  stateList: any[] = [];
  //lista de todos los roles que se muestran para escoger visibilidad 
  roleList: RolesListModel[] = [];  
  //lista de roles seleccionados de forma ordenada en la que lo recibe le modelo en lisRoleBusiness para despues enviar, ya sea en creación o en edicon, por esto en any
  selectedRolesList: any[] = [];

  private _settingCountryAndCompanySubscription?: Subscription;
  //idBusinessCountry de la suscripción
  idBusinessByCountry: number = 0;
  modelInConst?:StageByStateModel;
  isDependentState: boolean = false;
  ///lista para dependencias
  parentStateList: any[] = [];
  //varibale que valida si mostrar o no el boton guardar
  showSaveButton: boolean = true;
  isPatching = false;

  constructor(
    private _fb: FormBuilder,
    private _settingService: SettingService,
    private _parametersService: ParametersService,
    private _messageService: MessageService,    
    private  _translateService: TranslateService,
    private _roleService : RoleService,
    public utilsService: UtilsService,
    private _moduleService: ModuleService
  ){}

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this.getCategoriesList();
    this.getColorsList(); 
    this.getStageByStateById();
    this.getRoleList();
    this.initForm();  
    this.initDependenciesForm();
  }
  
  initForm(){
    this.form = this._fb.group({
      pkIIdStageByState: [0],
      fkIIdStage: [this.idStageIn],
      vCategory: ['', [Validators.required]],
      vState: ['', [Validators.required]],
      vColors: ['', [Validators.required]],
      isMultipleState:[this.idisMultipleStateIn],
      listRoleBusiness:[RolesListModel],
    })    
  }
  
  initDependenciesForm(){    
    this.dependenciesForm = this._fb.group({
      pkIIdChildrenState: [0],
      fkIIdStateParent:[this.stageByStateModelIn?.pkIIdStageByState],
      listidStateChild: [],
      bActive: [true]
    });
  }

  EmitForm(fromEdit: boolean = false){  
    if(this.form.valid){
      let model: StageByStateModel = this.form.value;
      !fromEdit?(    
        model.listRoleBusiness = this.selectedRolesList
      )
      :(
        this.modelInConst = model,
        this.selectedRolesList=this.stageByStateModelIn!.listRoleBusiness,
        model.listRoleBusiness=this.stageByStateModelIn!.listRoleBusiness
      )
      this.resultOut.emit(model)
    }
  }

  validateEditOrNew(){
    if(this.stageByStateModelIn){
      this.showSaveButton = false;
      this.getChildrenStateByStage();  
      this.form.patchValue(this.stageByStateModelIn);    
      let listRoleBusiness: any[] = this.stageByStateModelIn.listRoleBusiness;
      let selectedRoles: RolesListModel[] = [];
      for(let item of listRoleBusiness)
      { 
        selectedRoles.push(this.roleList.filter((r) => r.pkIIdRole == item.fkIIdRoleBusiness)[0])
      }
      this.form.patchValue({listRoleBusiness: selectedRoles});
      this.EmitForm(true);
    }
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessByCountry = response.enterprise.pkIIdBusinessByCountry;

            }
          }
      );
  }

  listRoleBusinessChange(role: RolesListModel, ischecked : boolean){
    //en este caso ya esxite un estado, por lo que se envia segun la estructura del api de update
    if(this.stageByStateModelIn){
      const listRoleBusiness: any  = this.stageByStateModelIn!.listRoleBusiness;
      //en este caso ya hay un estado creado y tiene relación con roles, por lo que se compara para enviar correctamente el dato listRoleBusiness
      if(this.stageByStateModelIn.listRoleBusiness.length>0){      
        //en este caso ya hay calores en la lista final, de no ser así recien llegó de update e ingresa al hacer el patch value, por lo que no se hace nada en else
        if(this.selectedRolesList.length>0){
          if(ischecked){
            //en este caso esta agregando uno que no existe en el modelo, por lo que se agrega con pkIIdStateByUserType en 0
            if(listRoleBusiness.filter((r:any)=> r.fkIIdRoleBusiness==role.pkIIdRole).length==0){
              this.selectedRolesList.push({
                pkIIdStateByUserType: 0,
                fkIIdRoleBusiness: role.pkIIdRole
              });
              this.EmitForm();
            }
          }
          //se elimina el elemento de la lista si fue desseleccionado
          else
          {
            this.selectedRolesList = this.selectedRolesList.filter((r)=> r.fkIIdRoleBusiness != role.pkIIdRole);
            this.EmitForm();
          }
        }          
      }
      //en este caso ya hay un estado creado, pero no tiene ninguna relación con roles
      else{
        //si se estan agregando se agrengan con pkIIdStateByUserType en 0
        ischecked?
        this.selectedRolesList.push(
          {
            pkIIdStateByUserType: 0,
            fkIIdRoleBusiness: role.pkIIdRole
          }
        ):
        //si se va a eliminar se quita el rol de la lista final. 
        this.selectedRolesList= this.selectedRolesList.filter((r) => r.fkIIdRoleBusiness != role.pkIIdRole)
        this.EmitForm();
      }
    }
    //en este caso se está creando un nuevo estado, por lo que se ennvía un lista de número segun la estructura del api de create
    else{
      ischecked?this.selectedRolesList.push(role.pkIIdRole):this.selectedRolesList=this.selectedRolesList.filter((r) => r!= role.pkIIdRole)
      this.EmitForm();
    }
    
  }

  dependentStateChange(ischecked : boolean){
    if(ischecked){
      this.isDependentState = ischecked;
    }else
    {
      for(let item of this.parentStateList)
      {
        this.deleteChildrenState(item.pkIIdChildrenState)
      }
    }
    
  }

  selectDependenciesChange(idParentState: number, checked: boolean) {
    if (this.isPatching) {
      return;
    }

    if (checked) {
        const exists = this.parentStateList.some(p => p.fkIIdStateParent === idParentState);

        if (!exists) {
            this.createChildrenStateByStage(idParentState);
        }
    } else {
        const itemToRemove = this.parentStateList.find(p => p.fkIIdStateParent === idParentState);

        if (itemToRemove) {
            const childId = itemToRemove.pkIIdChildrenState;
            this.deleteChildrenState(childId);
        }
    }
  }

  //#region metodos que consumen el servicio
    getCategoriesList(){
      this._parametersService
        .getParameters('CategoryStates_Stages').subscribe(
          (resp: any) => {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.categoriesList = resp;
          }
        }
      );
    }

    getColorsList(){
      this._parametersService
        .getParameters('ColorsStates_Stages').subscribe(
          (resp: any) => {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.colorsList = resp;
            }
          }
        );
    }

    getRoleList() {      
      this._roleService      
      .getRole(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {} 
          else {
            this.roleList = resp.result;
            this.stageByStateModelIn?this.validateEditOrNew():''
            
          }
        }
      });
    }

    getStageByStateById(){
      this._moduleService      
      .getStageByStateById(this.idStageIn)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {} 
          else {
            this.stateList = resp.result.filter((r: any) => r.pkIIdStageByState!=(this.stageByStateModelIn?.pkIIdStageByState))            
            this._moduleService      
              .getStageByStateByStageDependencies(this.idStageIn)
              .pipe(
                catchError((error) => {
                  if (error.error.error) {
                    this._messageService.messageWaring(
                      this._translateService.instant('ThereWasAError'),
                      error.error.message
                    );
                  }
                  return of([]);
                })
              )
              .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                } else {
                  if (resp.error) {} 
                  else {
                    for(let item of resp.result){
                      this.stateList.push(item)
                    }      
                  }
                }
              }); 
          }
        }
      });
    }

    getChildrenStateByStage(idCreatedChild: number = 0){
      this._moduleService      
      .getChildrenStateByStage(this.idStageIn)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {this.isDependentState = false} 
          else {
            if(idCreatedChild!=0){
              let item = resp.result.filter((r: any) => r.fkIIdStateChild == this.stageByStateModelIn?.pkIIdStageByState && r.fkIIdStateParent == idCreatedChild)[0]
              this.parentStateList.push(item);
              this.isDependentState = true;
            }
            else{
              this.parentStateList = resp.result.filter((r: any) => r.fkIIdStateChild== this.stageByStateModelIn?.pkIIdStageByState)
              if(this.parentStateList.length > 0){
                this.isDependentState = true;
                let selectedStages: number[] = []
                for(let item of this.parentStateList) 
                {
                  selectedStages.push(item.fkIIdStateParent)
                }  
                this.patchDependenciesForm(selectedStages);
              }
              else
              {
                this.isDependentState = false
              }
   
            }
          }
        }
      });
    }

    patchDependenciesForm(selectedStages: number[]) {
      this.isPatching = true; 
  
      this.dependenciesForm.patchValue({
          listidStateChild: selectedStages
      });

      this.dependenciesForm.valueChanges
      .pipe(take(1))
      .subscribe(() => {
          this.isPatching = false;
      });
    }

    createStageByState()
    {
      let roleList:number[] = []
      this.stageByStateModelIn = this.form.value;
      let list: any[] = this.stageByStateModelIn!.listRoleBusiness
      this.form.patchValue({listRoleBusiness:[]});
      for(let item of list){
        roleList.push(item.pkIIdRole)
      }
      this.form.patchValue({listRoleBusiness: roleList})
      this._moduleService
        .createStageByState(this.form.value)
        .pipe(
          catchError((error) => {
            this.showSaveButton = true;
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            this.showSaveButton = true;
            console.log('El tipo de datos devueltos es un array vacío.');
            
          } else {
            if (resp.error) {
              this.showSaveButton = true;
              this._messageService.messageError(
                resp.result
              );
            } else {
              this.showSaveButton = false;
              this.form.patchValue(this.stageByStateModelIn!)
              this.stageByStateModelIn = this.form.value
              this.stageByStateModelIn!.pkIIdStageByState = resp.result.result.idStage;
              this.resultOut.emit(this.stageByStateModelIn)
              this._messageService.messageSuccess(
                this._translateService.instant('Saved'),
                resp.message              
              );           
            }
          }
        });
    }

    createChildrenStateByStage(idParentState: number){
      let model: ChildrenStateModel ={
        pkIIdChildrenState:0,
        fkIIdStateParent: idParentState,
        listidStateChild:[this.stageByStateModelIn!.pkIIdStageByState],
        bActive:true
      }
      this._moduleService
        .createChildrenStateByStage(model)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
            
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.getChildrenStateByStage(idParentState);
              //el proceso fue correcto pero no se muestra ninguna alerta por solicitud
            }
          }
        });
    }

    deleteChildrenState(idChildrenState: number)
    {
      this._moduleService
        .deleteChildrenStateByStage(idChildrenState)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              const index = this.parentStateList.findIndex(p => p.pkIIdChildrenState === idChildrenState);
              if (~index) this.parentStateList.splice(index, 1);
              //el proceso fue correcto pero no se muestra ninguna alerta por solicitud
            }
          }
        });
    }
  //#endregion
  


}
