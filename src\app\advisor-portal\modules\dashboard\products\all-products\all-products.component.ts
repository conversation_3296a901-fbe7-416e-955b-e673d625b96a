import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { Subscription, of, catchError } from 'rxjs';

import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ProductParent } from 'src/app/shared/models/product/product.model';

import { ProductService } from 'src/app/shared/services/product/product.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';

import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';



@Component({
  selector: 'app-all-products',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TableComponent,
    MatButtonModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './all-products.component.html',
  styleUrls: ['./all-products.component.scss']
})
export class AllProductsComponent {
  productList?: Subscription;
  productParentSubs?: Subscription;
  productsParent: Array<ProductParent> = [];
  private _settingCountryAndCompanySubscription?: Subscription;
  dataTableProduct: any[] = [];

  estructTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.Product'),
      columnValue: 'vProductName'
    },
    {
      columnLabel: this._translateService.instant('Product.ParentProduct'),
      columnValue: 'fkIIdProductParent',
      functionValue: (item: any) => this.getProductParentNameById(item.fkIIdProductParent)

    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item)
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  constructor(public router: Router,
    private _productService: ProductService,
    private _settingService: SettingService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _utilsSvc: UtilsService,
    private _parameterService: ParametersService,
    private _customRouter: CustomRouterService,

  ) {}

  ngOnInit(): void {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this.router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.dataTableProduct = [];
              this.getProductParent();
              this.getProductByBusiness(
                response.enterprise.pkIIdBusinessByCountry
              );

            }
          }
        }
      );

    this._translateService.onLangChange.subscribe(
      (event: LangChangeEvent) => {
        this.estructTable[0].columnLabel = this._translateService.instant('Product.Product')
        this.estructTable[1].columnLabel = this._translateService.instant('Product.ParentProduct')
        this.estructTable[2].columnLabel = this._translateService.instant('Status')
        this.estructTable[3].columnLabel = this._translateService.instant('Action')
      }
    );
  }

  goToNewProduct(){
    this._customRouter.navigate(['/dashboard/products/new']);
  }

  ngOnDestroy(): void {
    this.productList?.unsubscribe();
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }


  getProductByBusiness(idBusinessByCountry: number) {
    this.productList = this._productService.getAllProductByBusiness(idBusinessByCountry).pipe(
      catchError((error) => {
        if (error.status !== 404) {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error?.message
          );
        }
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
        this.dataTableProduct = [];
      } else {
        if (resp.error) {
          this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.dataTableProduct = resp.result;
        }
      }
    });
  }

  controller(evt: IconEventClickModel) {
    this._customRouter.navigate(
      [`dashboard/products/modify/${evt.value.pkIIdProduct}`]
    );
  }

  getProductParentNameById(id: any) {
    var temp = this.productsParent.find(x => x.pkIIdProductParent == id)
    return temp != undefined ? temp.vProductName : id;

  }

  getProductParent() {
    this.productParentSubs = this._parameterService.getProductGlobal().pipe(
      catchError((error) => {
        this._messageService.messageWaring(this._translateService.instant('Warning'), error.error.message)
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
        this.productsParent = [];
      } else {
        if (resp.error) {
          this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.productsParent = resp.result;
        }
      }
    });
  }

}




