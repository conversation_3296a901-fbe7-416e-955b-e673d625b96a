import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatIconModule } from '@angular/material/icon';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { TaskTrayConfigListModel } from 'src/app/shared/models/task-tray-config';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { Subscription, catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import {
  DynamicFieldsModel,
  FilterTaskTrayModel,
  TaskReassignmentTableDataSubscriptionModel,
} from 'src/app/shared/models/task';
import { TaskTayConfigService } from 'src/app/shared/services/task-tray-config/task-tay-config.service';
import { MatDialog } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { TaskFilterComponent } from 'src/app/shared/components/task-filter/task-filter.component';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UsersAccessToTaskTrayModel } from 'src/app/shared/models/reassign-tasks';
import { SettingService } from 'src/app/shared/services/setting/setting.service';


@Component({
  selector: 'app-all-task-reassignment',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    TranslateModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    Modal2Component,
    TableComponent,
    TaskFilterComponent,
  ],
  templateUrl: './all-task-reassignment.component.html',
  styleUrls: ['./all-task-reassignment.component.scss'],
})
export class AllTaskReassignmentComponent implements OnInit {
  //Referencia a modal de filtros
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;

  //Referencia a modal Reasignar tareas
  @ViewChild('reassignTasksModal') reassignTasksModal?: TemplateRef<any>;

  //variable formulario.
  formReassignTasks: FormGroup = new FormGroup({});
  formFilter: FormGroup = new FormGroup({});

  //variables que se usan para idBusinessCountry.
  dataSetting: any = {};
  idBusinessByCountry: number = 0;

  //Variables que llenan las listas.
  processList: any[] = [];
  productsList: any[] = [];
  filterList: TaskTrayConfigListModel[] = [];
  stateList: any[] = [];
  usersListreassignTasks: UsersAccessToTaskTrayModel[] = [];
  userListByTaskId: UsersAccessToTaskTrayModel[] = [];
  userListByTaskIdTable: number[] = [];

  //Variables para el apartado de filtros.
  dataTableTaskTray: any[] = [];
  disabledFilter: boolean = true;
  dynamicFields: DynamicFieldsModel[] = [];

  //Estructura y datos de la tabla.
  estructTableTaskTray: BodyTableModel[] = [
    // {
    //   columnLabel: this._translateService.instant('TaskFilter.Table.Manage'),
    //   columnValue: 'manage',
    //   columnIcon: 'search',
    // },
  ];

  //Variables el paginado de la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;
  paginatorData: any = {};
  reassignedTasks: boolean = false;

  //Variables necesarias para la suscripción a la data.
  private _taskFilterDataSubscription?: Subscription;
  taskReassignmentTableDataSubscription: TaskReassignmentTableDataSubscriptionModel;

  //Variables para modal reasignar tareas.
  userNames: string = '';

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _parametersService: ParametersService,
    private _roleService: RoleService,
    private _translateService: TranslateService,
    private _moduleService: ModuleService,
    private _taskTayConfigService: TaskTayConfigService,
    public utilsService: UtilsService,
    public matDialog: MatDialog,
    private _router: Router,
    private _transactionService: TransactionService,
    private _settingService: SettingService,

  ) {
    this.taskReassignmentTableDataSubscription = {
      amountRows: 0,
      dataTable: [],
      estructTableTaskTray: [],
      formValue: {},
    };
  }

  ngOnInit(): void {
    this.getIdUserSession();
    this.getSubscriptionData();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table reasiganar tareas
      // this.estructTableTaskTray[1].columnLabel = this._translateService.instant(
      //   'TaskFilter.Table.Manage'
      // );
    });
  }

  getSubscriptionData() {
    this._taskFilterDataSubscription =
      this._taskTayConfigService.currentTaskReassignmentTableDataSubscription.subscribe(
        {
          next: (response) => {
            if (!(Object.keys(response).length === 0)) {
              this.taskReassignmentTableDataSubscription = response;
              this.estructTableTaskTray = response.estructTableTaskTray;
              this.estructTableTaskTray = response.estructTableTaskTray.filter(
                (item) => item.columnLabel !== "Gestionar"
              );
              this.dataTableTaskTray = response.dataTable;
              this.amountRows = response.amountRows;
            }
          },
        }
      );
  }

  //Obtine el los datos del usuario en sesión.
  async getIdUserSession() {
    this.dataSetting = await this._settingService.getDataSettingInit();
    const pkIIdBusinessByCountry =
      this.dataSetting == undefined ? 0 : this.dataSetting.idBusinessByCountry;
    this.idBusinessByCountry = pkIIdBusinessByCountry;
  }

  initreassignTasksForm() {
    this.formReassignTasks = this._fb.group({
      pkIIdUser: [0],
    });
  }

  //Obtiene lista de usuarios disponibles para reasignar las tareas selecionadas.
  getTaskTrayUserList() {
    this._roleService
      .getTaskTrayUserList(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.usersListreassignTasks = resp.result;
        }
      });
  }

  //Obtiene lista de usuarios responsables en la tarea por ID.
  getUserListByTaskId(taskId: number[]) {
    this._transactionService
      .getUserListByTaskId(taskId)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.userListByTaskId = resp.result;
          if (this.userListByTaskId.length === 1) {
            this.userNames = this.userListByTaskId[0].vPersonName;
          } else {
            this.userNames = this.userListByTaskId.map(user => user.vPersonName).join(', ');
          }
        }
      });
  }

  //Modal de confirmación antes de reasignar una tarea.
  confirmationReasignTask() {
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('ReassignTasks.ConfirmReassignment'),
        '',
        'warning',
        this._translateService.instant('Confirm'),
        this._translateService.instant('Cancel')
      )
      .then((result) => {
        if (result) {
          this.reasignTask(
            this.formReassignTasks.get('pkIIdUser')?.value,
            this.userListByTaskIdTable
          );
          this.matDialog.closeAll();
        }
      });
  }

  //Reasigna las tareas seleccionadas al nuevo usuario seleccionado.
  reasignTask(idUser: number, taskId: number[]) {
    this._transactionService
      .reasignTask(idUser, taskId)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess(
            this._translateService.instant('ReassignTasks.Reassigned'),
            ''
          );
          this.userListByTaskIdTable = [];
          this.reassignedTasks = !this.reassignedTasks;

        }
      });
  }

  //accion al dar en boton gestionar
  controller(event: IconEventClickModel) {
    // if (event.column === 'manage') {
    //   this._customRouter.navigate([
    //     `/dashboard/task-tray/new/` +
    //     this.taskReassignmentTableDataSubscription.formValue.fkIIdProduct
    //       .pkIIdProductModule +
    //     '/' +
    //     this.taskReassignmentTableDataSubscription.formValue.fkIIdProcess
    //       .vNameProcess +
    //     '/' +
    //     this.taskReassignmentTableDataSubscription.formValue.fkIIdProduct
    //       .vName +
    //     '/' +
    //     event.value.Id_de_tarea+
    //     '/' +
    //     event.value.pk_i_IdTaskState +
    //     '/' +
    //     event.value.fk_i_IdStateModule
    //   ]);
    // }
    if (event.value === 'ALL') {
      this.selectAll();
    }
    if (event.value === 'NONE') {
      this.userListByTaskIdTable = [];
    }
    if (
      event.column === '1' &&
      event.value !== 'NONE' &&
      event.value !== 'ALL'
    ) {
      this.getUsersByTasks(event.value.fk_i_IdTask);
    }
  }

  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.reassignedTasks = false;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 1;
    }
    this.paginatorData = {
      pageIndex: event.pageIndex + 1,
      pageSize: event.pageSize,
    };
  }

  //Función que abre el modal pro referencia.
  openReassignTasksDialog() {
    this.matDialog.open(this.reassignTasksModal!, {
      disableClose: true,
      width: '50vw',
      maxHeight: '70vh',
    });
    this.initreassignTasksForm();
  }

  //Función que ejecuta acciones personalizadas, cada vez que se cierre el modal de filtro.
  closeModalReassignTasks() { }

  //Acciones que se ejecutan cuándo el usuario preciona el botón Reasigar tareas.
  reassignTasks() {
    this.getUserListByTaskId(this.userListByTaskIdTable);
    this.getTaskTrayUserList();
    this.openReassignTasksDialog();
  }

  //Agrega un valor forma individual seleccionadas por el usuario en la tabla.
  getUsersByTasks(userId: string) {
    const numero = Number(userId);
    if (!isNaN(numero)) {
      const index = this.userListByTaskIdTable.indexOf(numero);
      if (index !== -1) {
        this.userListByTaskIdTable.splice(index, 1);
      } else {
        this.userListByTaskIdTable.push(numero);
      }
    }
  }

  //Agrega todos los valores seleccionados en la tabla.
  selectAll() {
    // Recorrer el array dataTableTaskTray
    for (let value of this.dataTableTaskTray) {
      // Si el valor no existe, agregarlo
      if (
        this.userListByTaskIdTable.indexOf(Number(value.fk_i_IdTask)) === -1
      ) {
        this.userListByTaskIdTable.push(Number(value.fk_i_IdTask));
      }
    }
    //Ordenamos el array
    this.userListByTaskIdTable.sort((a, b) => a - b);
  }

  ngOnDestroy(): void {
    this.taskReassignmentTableDataSubscription.dataTable = [];
    this.taskReassignmentTableDataSubscription.estructTableTaskTray = [];
    this._taskFilterDataSubscription?.unsubscribe();
  }
}
