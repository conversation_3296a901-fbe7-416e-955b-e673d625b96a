<h2 mat-dialog-title>
  <button mat-icon-button class="close-button" (click)="close()">
    <mat-icon>close</mat-icon>
  </button>
</h2>
<mat-dialog-content>
  <h4><strong>{{ 'Filter' | translate }}</strong></h4>
  <hr style="margin-top: 5px;">


  <form *ngIf="isProduct" [formGroup]="filterForm">
    <mat-form-field appearance="fill" class="col-12">
      <mat-label>{{ 'Client.Filter.Product' | translate }}</mat-label>
      <mat-select formControlName="product">
        <mat-option *ngFor="let product of data.products" [value]="product">{{ product.name }}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="fill" class="col-12">
      <mat-label>{{ 'Client.Filter.Insurance' | translate }}</mat-label>
      <mat-select formControlName="insurer">
        <mat-option *ngFor="let insurer of data.insurers" [value]="insurer">{{ insurer.name }}</mat-option>
      </mat-select>
    </mat-form-field>


    <div class="col-12">

      <p>{{ 'Client.Filter.State' | translate }}</p>
      <mat-checkbox *ngIf="activaQuote" formControlName="active">{{ 'Client.Filter.Active' | translate }}</mat-checkbox>
      <br *ngIf="activaQuote">
      <mat-checkbox *ngIf="inactivaQuote" formControlName="inactive">{{ 'Client.Filter.Inactive' | translate
        }}</mat-checkbox>
      <br *ngIf="inactivaQuote">
      <mat-checkbox *ngIf="esEmitido" formControlName="emitida">{{ 'Client.Filter.Issued' | translate }}</mat-checkbox>
      <br *ngIf="esEmitido">
      <mat-checkbox *ngIf="esSinEmitir" formControlName="sinEmitir">{{ 'Client.Filter.Unissued' | translate
        }}</mat-checkbox>
    </div>
  </form>

  <form *ngIf="isProceure" [formGroup]="filterForm">

    <div class="col-12">

      <p>{{ 'Client.Filter.State' | translate }}</p>

      <mat-checkbox *ngIf="activaQuote" formControlName="active">{{ 'Client.Filter.Active' | translate }}</mat-checkbox>
      <br *ngIf="activaQuote">

      <mat-checkbox *ngIf="inactivaQuote" formControlName="inactive">{{ 'Client.Filter.Inactive' | translate
        }}</mat-checkbox>
      <br *ngIf="inactivaQuote">

      <mat-checkbox *ngIf="esEmitido" formControlName="emitida">{{ 'Client.Filter.Issued' | translate }}</mat-checkbox>
      <br *ngIf="esEmitido">

      <mat-checkbox *ngIf="esSinEmitir" formControlName="sinEmitir">{{ 'Client.Filter.Unissued' | translate
        }}</mat-checkbox>
      <br *ngIf="esSinEmitir">

      <mat-checkbox *ngIf="abierto" formControlName="abierto">{{ 'Client.Filter.Open' | translate
        }}</mat-checkbox>
      <br *ngIf="abierto">

      <mat-checkbox *ngIf="cerrado" formControlName="cerrado">{{ 'Client.Filter.Closed' | translate
        }}</mat-checkbox>
      <br *ngIf="cerrado">

      <mat-checkbox *ngIf="enCurso" formControlName="enCurso">{{ 'Client.Filter.InProgress' | translate
        }}</mat-checkbox>
      <br *ngIf="enCurso">

    </div>
    <br>
    <mat-form-field appearance="fill" class="col-12">
    <mat-label>{{ 'Client.Filter.State' | translate }}</mat-label>
      <mat-select formControlName="procedure">
        <mat-option *ngFor="let procedure of data.procedure" [value]="procedure">{{ procedure.name }}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="fill" class="col-12">
      <mat-label>{{ 'Client.Filter.Product' | translate }}</mat-label>
      <mat-select formControlName="product">
        <mat-option *ngFor="let product of data.products" [value]="product">{{ product.name }}</mat-option>
      </mat-select>
    </mat-form-field>

  </form>

  <form *ngIf="isQuote" [formGroup]="filterForm">
    <mat-form-field appearance="fill" class="col-12">
      <mat-label>{{ 'Client.Filter.Product' | translate }}</mat-label>
      <mat-select formControlName="product">
        <mat-option *ngFor="let product of data.products" [value]="product">{{ product.name }}</mat-option>
      </mat-select>
    </mat-form-field>


    <mat-form-field appearance="fill" class="col-12">
      <mat-label> N° {{ 'Client.Quote.QuoteNumber' | translate }}</mat-label>
      <mat-select formControlName="quoteNumber">
        <mat-option *ngFor="let quote of data.quotes" [value]="quote">{{ quote }}</mat-option>
      </mat-select>
    </mat-form-field>

    <div class="col-12">

      <p>{{ 'Client.Filter.State' | translate }}</p>
      <mat-checkbox *ngIf="activaQuote" formControlName="active">{{ 'Client.Filter.Active'| translate }}</mat-checkbox>
      <br *ngIf="activaQuote">
      <mat-checkbox *ngIf="inactivaQuote" formControlName="inactive">{{ 'Client.Filter.Inactive' | translate }}</mat-checkbox>
      <br *ngIf="inactivaQuote">
      <mat-checkbox *ngIf="esEmitido" formControlName="emitida">{{ 'Client.Filter.Issued' | translate }}</mat-checkbox>
      <br *ngIf="esEmitido">
      <mat-checkbox *ngIf="esSinEmitir" formControlName="sinEmitir">{{ 'Client.Filter.Unissued' | translate
        }}</mat-checkbox>
    </div>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="center" style="padding-bottom: 20px;">
  <button mat-raised-button (click)="close()">{{ 'Client.Filter.DeleteFilers' | translate }}</button>
  <button mat-raised-button color="primary" (click)="applyFilters()">{{ 'Client.Filter.Apply' | translate }}</button>
</mat-dialog-actions>