import { Routes } from '@angular/router';
import { PlansComponent } from './plans.component';

export default [
  {
    path: '',
    component: PlansComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-plans/all-plans.component').then(
            (c) => c.AllPlansComponent
          ),
      },
      {
        path: 'new/:pkIIdProduct',
        loadComponent: () =>
          import('./edit-new-plans/edit-new-plans.component').then(
            (c) => c.EditNewPlansComponent
          ),
      },
      {
        path: 'edit/:pkIIdProduct/:pkIIdPlan',
        loadComponent: () =>
          import('./edit-new-plans/edit-new-plans.component').then(
            (c) => c.EditNewPlansComponent
          ),
      },
      {
        path:'plan-Premium',
        loadComponent:()=> import('./plan-premium/plan-premium.component').then(
          (c)=>c.PlanPremiumComponent
        ),
      }
    ],
  },
] as Routes;
