<div class="row mt-5">
  <h5 class="fw-bold mb-2">
    {{ 'SectionClientPortal.LinksTitle' | translate }}
  </h5>

  <app-table
    [displayedColumns]="estructTableLinks"
    [data]="dataTableLinks"
    (iconClick)="controller($event)"
  ></app-table>

  <div class="row">
    <div class="col">
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="openModal('link')"
      >
        {{ "SectionClientPortal.AddLink" | translate }}
      </button>
    </div>
  </div>
</div>



<div class="row mt-5">
  <h5 class="fw-bold mb-2">
    {{ 'SectionClientPortal.SocialMediaTitle' | translate }}
  </h5>

  <app-table
    [displayedColumns]="estructTableSocialMedia"
    [data]="dataTableSocialMedia"
    (iconClick)="controller($event)"
  ></app-table>

  <div class="row">
    <div class="col">
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="openModal('socialMedia')"
      >
        {{ "SectionClientPortal.AddSocialMedia" | translate }}
      </button>
    </div>
  </div>
</div>


<div class="row mt-5">
  <h5 class="fw-bold mb-2">
    {{ 'SectionClientPortal.CopyrightTitle' | translate }}
  </h5>
  <form [formGroup]="formCopyrigth">
    <mat-form-field appearance="outline" class="w-100 mb-2">
      <mat-label>
        {{'SectionClientPortal.Copyright' | translate}}
      </mat-label>
      <input matInput formControlName="vText" PreventionSqlInjector/>
      <mat-error *ngIf="utilsSvc.isControlHasError(formLink, 'vText', 'required')" >
        {{'ThisFieldIsRequired' | translate}}
      </mat-error>
    </mat-form-field>
  </form>
</div>

<div class="row">
  <div class="col">
    <button
      type="button"
      mat-raised-button
      color="primary"
      (click)="save()"
    >
      {{ "Product.Save" | translate }}
    </button>
  </div>
</div>

<ng-template #createEditLink>
  <app-modal2 [titleModal]="(indexLinkToModify ? 'SectionClientPortal.UpdateLinkTitle' : 'SectionClientPortal.CreateLinkTitle') | translate">
    <ng-container body>
      <form [formGroup]="formLink">
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{'SectionClientPortal.Name' | translate}}
          </mat-label>
          <input matInput formControlName="vName" PreventionSqlInjector/>
          <mat-error *ngIf="utilsSvc.isControlHasError(formLink, 'vName', 'required')" >
            {{'ThisFieldIsRequired' | translate}}
          </mat-error>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{'SectionClientPortal.Link' | translate}}
          </mat-label>
          <input matInput formControlName="vlink"/>
          <mat-error *ngIf="utilsSvc.isControlHasError(formLink, 'vlink', 'required')" >
            {{'ThisFieldIsRequired' | translate}}
          </mat-error>
        </mat-form-field>

      </form>
    </ng-container>
    <ng-container customButtonRight>
      <div class="modal-footer">
        <button mat-raised-button color="primary" type="button" class="" (click)="saveLink()">
          {{ "Save" | translate }}
        </button>
      </div>
    </ng-container>
  </app-modal2>
</ng-template>


<ng-template #createEditSocialMedia>
  <app-modal2 [titleModal]="(indexSocialMediaToModify ? 'SectionClientPortal.UpdateLinkTitle' : 'SectionClientPortal.CreateLinkTitle') | translate">
    <ng-container body>
      <form [formGroup]="formSocialMedia">
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{'SectionClientPortal.SocialMediaName' | translate}}
          </mat-label>
          <input matInput formControlName="vName" PreventionSqlInjector/>
          <mat-error *ngIf="utilsSvc.isControlHasError(formLink, 'vName', 'required')" >
            {{'ThisFieldIsRequired' | translate}}
          </mat-error>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-100 mb-2">
          <mat-label>
            {{'SectionClientPortal.SocialMediaLink' | translate}}
          </mat-label>
          <input matInput formControlName="vlink"/>
          <mat-error *ngIf="utilsSvc.isControlHasError(formLink, 'vlink', 'required')" >
            {{'ThisFieldIsRequired' | translate}}
          </mat-error>
        </mat-form-field>

      </form>
    </ng-container>
    <ng-container customButtonRight>
      <div class="modal-footer">
        <button mat-raised-button color="primary" type="button" class="" (click)="saveSocialMedia()">
          {{ "Save" | translate }}
        </button>
      </div>
    </ng-container>
  </app-modal2>
</ng-template>