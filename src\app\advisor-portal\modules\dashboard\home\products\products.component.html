<ng-container *ngIf="productList.length > 0">
  <div class="cont-title mb-3">
    <h4 *ngIf="productsObject.vName; else notName">
      {{ productsObject.vName }}
    </h4>
    <ng-template #notName>
      <h4>{{ "HomeAdvisors.Poducts.Title" | translate }}</h4>
    </ng-template>
  </div>
  <div class="product-type">
    <a 
      (click)="goToQuotation(product.pkIIdProduct)"
      class="product click"
      *ngFor="let product of productList"
    >
      <div
        class="box"
        [ngClass]="{
          'box-min': productList.length <= 3,
          'box-max': productList.length > 3
        }"
      >
        <img
          [src]="product.vFilePath"
          alt="Logo"
          [ngClass]="{
            'logo-min': productList.length <= 3,
            'logo-max': productList.length > 3
          }"
        />
      </div>
      <h5
        class="h4"
        [ngClass]="{
          'product-name': productList.length > 3,
          'product-name-size': productList.length > 3
        }"
      >
        {{ product.vProductName }}
      </h5>
    </a>
  </div>
</ng-container>
