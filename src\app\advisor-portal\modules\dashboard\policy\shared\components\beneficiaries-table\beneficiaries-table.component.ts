import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { BeneficiaryModel } from '../../models';

@Component({
  selector: 'app-beneficiaries-table',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    TranslateModule,
  ],
  templateUrl: './beneficiaries-table.component.html',
  styleUrls: ['./beneficiaries-table.component.scss'],
})
export class BeneficiariesTableComponent implements OnInit {
  @Input() estructTable: BodyTableModel[] = [];
  @Input() beneficiariesData: BeneficiaryModel[] = [];
  @Input() showDetailsColumn: boolean = false;
  @Output() actionTable = new EventEmitter<any>();

  constructor() {}
  ngOnInit(): void {

  }

  //Controlador de las acciones configuradas en la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'viewDetails':
        this.actionTable.emit(event);
        break;
      case 'modify':
        this.actionTable.emit(event);
        break;
      case 'delete':
        this.actionTable.emit(event);
        break;
      default:
        break;
    }
  }
}
