<div class="title m-0">
    <h2 class="h3 m-0">
      <img src="assets/img/layouts/config_ico.svg" alt="" />{{ "IncentivesForm.ConfigTitle" | translate }}
    </h2>
  </div>
  <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>
  <app-choose-country-and-company></app-choose-country-and-company>


  <div class="ng-container" >

    <form [formGroup]="form">
        <mat-tab-group >
            <mat-tab label="{{'IncentivesForm.TabTitleMonetary' | translate}}">

                <div class="col-md-6 col-sm-12" >
                    <mat-slide-toggle class="mb-3" style="margin-top: 10px;" formControlName="active" >{{ 'IncentivesForm.TabMonetaryActiveText' | translate }}</mat-slide-toggle>

                </div>
                <div class="col-md-6 col-sm-12">
                    <input class="form-check-input"   type="checkbox"   formControlName="showSales"  />
                    <mat-label>  {{ "IncentivesForm.TabMonetaryShowSales" | translate }} </mat-label>

                </div>

                <div class="col-md-6 col-sm-12">
                    <input class="form-check-input"   type="checkbox" formControlName="showCancellations"  />
                    <mat-label>  {{ "IncentivesForm.TabMonetaryShowCancellations" | translate }} </mat-label>

                </div>

                <div *ngIf="hasError()" class="text-danger">¡Debe seleccionar al menos una opción!</div>

                <div class="col-md-6 col-sm-12">
                     <h4 class="h4 mt-3"> {{'IncentivesForm.TabMonetaryLoadTemplateTitle' | translate }}</h4>
                     <mat-label>  {{ "IncentivesForm.TabMonetaryLoadTemplateText" | translate }} </mat-label>

                </div>

                <div class="cont-subtitle-form mb-3">
                    <h4  class="h4 mt-3"> {{ "IncentivesForm.TabMonetaryTemplateLabel" | translate }}</h4>
                </div>

                <div class="file-download">
                    <div class="file-info">
                      <span class="file-name">{{ "IncentivesForm.TabMonetaryFileName" | translate }}</span>
                      <span class="file-size">200 MB</span>
                    </div>
                    <button mat-icon-button class="download-button"  (click)="downloadFile()" aria-label="Descargar archivo">
                      <mat-icon>download</mat-icon>
                    </button>
                </div>

                 <div class="col-md-6 col-sm-12">
                    <div class="select-container">
                        <mat-form-field appearance="outline" class="custom-select">
                          <mat-label>{{ "IncentivesForm.TabMonetarySelectYearLabel" | translate }}</mat-label>
                          <mat-select  (selectionChange)="getSelectedYear()" formControlName="year">
                            <mat-option *ngFor="let year of years" [value]="year">{{ year }}</mat-option>
                          </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="custom-select">
                          <mat-label>{{ "IncentivesForm.TabMonetarySelectMonthLabel" | translate }}</mat-label>
                          <mat-select  formControlName="month" (selectionChange)="validateUploadedFileExists()">
                            <mat-option *ngFor="let month of disposableMonths" [value]="month.value">
                              {{ month.name }}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                 </div>

                 <div class="col-md-6 col-sm-12">
                    <div class="contenedor">
                        <label class="labelModal">Cargar Archivo</label>
                        <div class="contenedor-file">
                          <div appearance="outline" class="input-file">
                            <input type="file" (onChangeValidated)="selectFile($event)" accept=".xlsx, .xls" hidden #fileInput ValidationInputFile [allowedExtensions]="['xlsx','xls']"/>
                            <label class="dta">{{ fileName || '' }}</label>
                            <button mat-button matSuffix style="cursor: none; width: 1px; height: 1px; " >  </button>
                          </div>

                          <div class="iconos-dentro">
                              <button mat-icon-button color="primary" (click)="fileInput.click()"><mat-icon matTooltipPosition="right" matTooltip="Cargar archivo">upload</mat-icon></button>
                              <button mat-icon-button color="primary" ><mat-icon  matTooltipPosition="right" matTooltip="Descargar archivo" (click)="downloadExistsFile(true)">download</mat-icon></button>
                              <button mat-icon-button (click)="deleteIncentiveMonetary('Monetary')" >
                                <mat-icon style="color:black">close</mat-icon>
                              </button>
                          </div>
                        </div>

                      </div>

                  </div>

                  <div id="validando">Validando archivo...</div>

                 <div class="col-md-6 col-sm-12">
                    <app-generic-buttons [label]="'IncentivesForm.TabMonetarySaveIncentives' | translate"   [type]="'button'"   [icon]="'save'"   [classList]="'width-button'" [color]="'primary'"
                    (buttonClick)="saveMonetaryIncentives()"></app-generic-buttons>
                 </div>

                 <div *ngIf="errores.length > 0" class="error-container2">
                  <h3>Errores encontrados:</h3>

                  <div class="error-list" *ngIf="errores.length > 0">
                    <ul>
                      <li *ngFor="let error of errores">{{ error }}</li>
                    </ul>
                  </div>
                </div>

            </mat-tab>

<!--NO Monetarios-->
            <mat-tab label="{{'IncentivesForm.TabTitleNotMonetary' | translate}}">

                <div class="col-md-6 col-sm-12" >
                    <mat-slide-toggle class="mb-3" style="margin-top: 10px;" >{{ 'IncentivesForm.TabNoMonetaryActiveText' | translate }}</mat-slide-toggle>
                </div>

                <div class="cont-subtitle-form mb-3">
                  <h4  class="h4 mt-3"> Incentivos no monetarios</h4>
                </div>

                <div class="row mt-2">
                    <app-table [displayedColumns]="structureIncentivesTable"   [data]="NotMonetaryIncentivesTable "   (iconClick)="controller($event)">
                    </app-table>
                </div>

                <div class="col-md-6 col-sm-12" >
                    <button type="button" class="w-auto" mat-raised-button color="primary" (click)="addNoMonetaryIncentive()">
                        {{ "IncentivesForm.AddNotMonetaryButton" | translate }}
                        <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
                    </button>
                </div>
            </mat-tab>
        </mat-tab-group>

    </form>

<!-- modal     -->
  <ng-template #NotMonetaryIncentiveModal>
    <app-modal2 [titleModal]="'IncentivesForm.AddNotMonetaryButton' | translate " (closeModal)="closeModal()">
      <ng-container body>
        <form [formGroup]="notMonetaryIncentiveModalForm">

          <div class="col-md-12 col-sm-12">
                <mat-slide-toggle class="mb-3" formControlName="active" style="margin-top: 10px;" >{{ 'IncentivesForm.ActiveIncentiveText' | translate }}</mat-slide-toggle>
          </div>

          <div class="col-md-12 col-sm-12">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{"IncentivesForm.NotMonetaryTypeColumn" | translate}}</mat-label>
                <input matInput formControlName="type"  />
                <mat-error *ngIf="_utilsService.isControlHasError(notMonetaryIncentiveModalForm, 'type', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>

          </div>

          <div class="col-md-12 col-sm-12">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{"IncentivesForm.NotMonetaryCampaignNameColumn" | translate}}</mat-label>
                <input matInput formControlName="campaignName"  />
                <mat-error *ngIf="_utilsService.isControlHasError(notMonetaryIncentiveModalForm, 'campaignName', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>

          </div>

          <div class="col-md-12 col-sm-12">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{"IncentivesForm.NotMonetaryEffectiveDateColumn" | translate}}</mat-label>

                <input  matInput  [matDatepicker]="dInitialDate" formControlName="startDate" />
                 <mat-datepicker-toggle  matIconSuffix [for]="dInitialDate"></mat-datepicker-toggle>
                <mat-datepicker #dInitialDate></mat-datepicker>

                <mat-error *ngIf="_utilsService.isControlHasError(notMonetaryIncentiveModalForm, 'startDate', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
          </div>

          <div class="col-md-12 col-sm-12">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{"IncentivesForm.NotMonetaryEffectiveEndDateColumn" | translate}}</mat-label>

                <input  matInput  [matDatepicker]="dFinalDate" formControlName="endDate" />

                 <mat-datepicker-toggle  matIconSuffix [for]="dFinalDate"></mat-datepicker-toggle>
                <mat-datepicker #dFinalDate></mat-datepicker>

                <mat-error *ngIf="_utilsService.isControlHasError(notMonetaryIncentiveModalForm, 'endDate', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>



            </mat-form-field>
            <div *ngIf="isDateRangeInvalid()" class="text-danger">
              La fecha de inicio no puede ser mayor que la fecha de finalización!
            </div>


          </div>

          <div class="col-md-12 col-sm-12">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{"IncentivesForm.DescriptionLabel" | translate}}</mat-label>
                <input matInput formControlName="description"  />
                <mat-error *ngIf="_utilsService.isControlHasError(notMonetaryIncentiveModalForm, 'description', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
          </div>

          <div class="col-md-6 col-sm-12">
            <div class="contenedor">
                <label class="labelModal">Cargar imagen</label>
                <div appearance="outline" class="input-file">
                  <input type="file" (onChangeValidated)="selectImage($event)" accept=".png,.jpg,.jpeg" hidden #imageInput ValidationInputFile [allowedExtensions]="['.png','.jpg', '.jpeg']"/>
                  <mat-label>{{ imageName || '' }}</mat-label>
                  <button mat-button matSuffix >  </button>
                </div>

                <div class="iconos-dentro">
                    <button mat-icon-button color="primary" (click)="imageInput.click()" ><mat-icon matTooltipPosition="right" matTooltip="Cargar archivo">upload</mat-icon></button>
                </div>
              </div>
          </div>

          <div class="col-md-6 col-sm-12">
            <div class="contenedor">
                <label class="labelModal">Cargar Condiciones</label>
                  <div appearance="outline" class="input-file">
                    <input type="file" (onChangeValidated)="selectPdf($event)" accept=".pdf" hidden #pdfInput ValidationInputFile [allowedExtensions]="['.pdf']"/>
                    <mat-label>{{ pdfName || '' }}</mat-label>
                    <button mat-button matSuffix></button>
                  </div>

                  <div class="iconos-dentro">
                    <button mat-icon-button color="primary" (click)="pdfInput.click()"><mat-icon matTooltipPosition="right" matTooltip="Cargar archivo">upload</mat-icon></button>
                </div>
              </div>

          </div>
        </form>
      </ng-container>

      <ng-container customButtonRight>
         <button type="button" *ngIf="idContent<= 0" mat-raised-button color="primary"  (click)="saveIncentiveNotMonetary()">{{"IncentivesForm.SaveIncentiveButtonLabel" | translate}}
          <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
        </button>

        <button *ngIf="idContent > 0" type="button" mat-raised-button  color="primary"(click)="editIncentiveNotMonetary()">{{ "Modify" | translate }}
          <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        </button>

      </ng-container>

    </app-modal2>

  </ng-template>

  <ng-template #ShowIncentiveNotMonetaryModal>
    <app-modal2 titleModal="Ver Incentivo" (closeModal)="closeModal()" (showCancelButton)="false" (showCancelButtonBelow)="false">
      <ng-container body>
        <div class="popup-container">
          <mat-card class="incentive-card" >
              <img mat-card-image [src]="srcImage" alt="Incentivo" class="scaled-image" />
              <div class="overlay-title">{{ popCampaingName }}</div>
            <mat-card-content>
              <p class="valid-date">Válido hasta el: {{ popValidUntil | date: 'dd-MM-yyyy' }}</p>
              <p class="description">{{ popDescription }}</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary"  mat-raised-button class=""(click)="openPDF(popPdfPath)">Ver condiciones
                <mat-icon>launch</mat-icon>
              </button>
            </mat-card-actions>

          </mat-card>
        </div>
      </ng-container>

    </app-modal2>
  </ng-template>

  </div>

