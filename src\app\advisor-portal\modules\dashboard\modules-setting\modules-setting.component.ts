import { Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-modules-setting',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent, MatIconModule, MatTooltipModule],
  template: `
    <div class="title m-0">
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
        {{ 'ModulesSetting.Modules.MainTitle' | translate }}
        <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.ModulesConfiguration' | translate }}">help_outline</mat-icon>
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class ModulesSettingComponent implements OnInit, OnDestroy {
  constructor(private _translateService: TranslateService) {}

  configuration: string = this._translateService.instant('Configuración');
  modules: string = this._translateService.instant('Módulos');

  sections: { label: string; link: string }[] = [
    { label: this.configuration, link: '/dashboard/configuration' },
    { label: this.modules, link: '/modules-setting' },
  ];

  ngOnDestroy(): void {}
  ngOnInit(): void {}
}
