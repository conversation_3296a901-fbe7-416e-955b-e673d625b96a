import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateAdapter, MAT_DATE_LOCALE, MatNativeDateModule } from '@angular/material/core';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ActivatedRoute } from '@angular/router';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { catchError, debounceTime, of } from 'rxjs';
import {
  ListPolicyFileModel,
  PolicyCoverage,
  PolicyDetailModel,
  RequestGeneralInfoModel,
} from 'src/app/shared/models/policy';
import { MatTabChangeEvent, MatTabGroup, MatTabsModule } from '@angular/material/tabs';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ActiveRiskTableComponent } from '../../shared/components/active-risk-table/active-risk-table.component';
import { BodyTableModel } from 'src/app/shared/models/table';
import { CoveragesComponent } from '../../../policy-configuration/new-edit-policy/coverages/coverages.component';
import { InsuranceService } from 'src/app/shared/services/insurance/insurance.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { CustomDateAdapter } from '../../shared/utils/custom-date-adapter';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';
import { PolicyTableService } from '../../shared/service/policy-table.service';

@Component({
  selector: 'app-page-renewal-collective-individual-policy',
  standalone: true,
  imports: [
    CommonModule,
    MatSlideToggleModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTabsModule,
    ActiveRiskTableComponent,
    CoveragesComponent,
    ValidationInputFileDirective
  ],
  templateUrl: './page-renewal-collective-individual-policy.component.html',
  styleUrls: ['./page-renewal-collective-individual-policy.component.scss'],
  providers: [
      { provide: MAT_DATE_LOCALE, useValue: 'es' },
      { provide: DateAdapter, useClass: CustomDateAdapter },
    ]
})
export class PageRenewalCollectiveIndividualPolicyComponent implements OnInit {
  @ViewChild('tabGroup') tabGroup!: MatTabGroup; // Referencia al mat-tab-group

  //Variables relacionadas con el formulario.
  form: FormGroup = new FormGroup({});
  idWtw: number = 0;
  idPolicyType: number = 0;
  idUser: number = 0;
  listFile: File[] = [];
  selectedFileName: string = '';
  fileTypes: string[] = ['XLS', 'CSV', 'PDF', 'PNG', 'JPG'];
  maxFileSizeMB: number = 20;
  listIdFiles: ListPolicyFileModel[] = [];
  showBtnDelete: boolean = false;
  showBtnDownload: boolean = false;
  coverages: PolicyCoverage[] = [];
  renewPolicy = new FormData();
  editedFiles: boolean = false;
  endValidity: Date = new Date;
  isLastTab: boolean = false;
  //Variables relacionadas con la tabla de riesgos activos.
  titleRiskTable: string = this._translateService.instant(
    'Policy.ActiveRisksTable'
  );
  estructTableActiveRisk: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Policy.NameSecured'),
      columnValue: 'insuredFullName',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.DocumentType'),
      columnValue: 'insuredDocumentType',
    },
    {
      columnLabel: this._translateService.instant('MyProfile.DocumentNumber'),
      columnValue: 'insuredDocumentNumber',
    },
  ];

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _activatedRoute: ActivatedRoute,
    public _utilService: UtilsService,
    private _transactionService: TransactionService,
    private _fileService: FileService,
    private _translateService: TranslateService,
    private _location: Location,
    private _insuranceService: InsuranceService,
    private _productService: ProductService,
    private _userService: UserService
  ) {}

  async ngOnInit() {
    this.initForm();
    this.getDataUrl();
    this.idUser = await this._userService.getUserIdSesion();
  }

  //Obtiene los valores de las variables enviadas por medio de la URL.
  getDataUrl() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idPolicy) {
        this.idWtw = Number(params.idPolicy);
        this.getPolicyDetailById(this.idWtw);
      }

      if (params.idPolicyType) {
        this.idPolicyType = Number(params.idPolicyType);
        if (this.idPolicyType === 3) {
          const newColumn = {
            columnLabel: this._translateService.instant('PolicyConfiguration.GeneralInformation.PolicyNumber'),
            columnValue: 'policyNumber',
          };
          this.estructTableActiveRisk.unshift(newColumn);
        }
      }


    });
  }

  //Inicializa el formulario.
  initForm() {
    this.form = this._fb.group({
      PolicyName: [null, [Validators.required]],
      ProductName: [{ value: null, disabled: true }],
      InsuranceName: [{ value: null, disabled: true }],
      Extension: [false, [Validators.required]],
      IdPolicy: [{ value: null, disabled: true }],
      PolicyNumber: [null],
      StartValidity: [null, [Validators.required]],
      EndValidity: [null, [Validators.required]],
      Files: [null],
      EditedFiles: [false],
    });
    this.form.valueChanges.pipe(debounceTime(300)).subscribe({
      next: (data: RequestGeneralInfoModel) => {
        const form: RequestGeneralInfoModel = this.form.getRawValue();
        form.Files = this.listFile;
        form.formValid = this.form.valid;
        if (data.Files) {
          this.showBtnDelete = true;
        } else {
          this.showBtnDelete = false;
        }
      },
    });
    this.form.get('StartValidity')?.valueChanges.subscribe((startDate) => {
      if (startDate) {
        const start = new Date(startDate);
        const endControl = this.form.get('EndValidity');
        const end = endControl?.value ? new Date(endControl.value) : null;

        if (end && start > end) {
          endControl?.setValue(null);
        }
        endControl?.updateValueAndValidity();
      }
    });
  }

  //Obtene los cambios de una pestaña a otra en el componente tab.
  onTabChange(event: MatTabChangeEvent) {
    const totalTabs = this.tabGroup._tabs.length;
    this.isLastTab = event.index === totalTabs - 1;
  }

  // Cambiar al siguiente tab
  goToNextTab(): void {
    if (this.tabGroup) {
      const currentIndex = this.tabGroup.selectedIndex || 0;
      this.tabGroup.selectedIndex = currentIndex + 1;
    }
  }

  onFileChange(event: Event) {
    const guid = crypto.randomUUID();
    const inputElement = event.target as HTMLInputElement;
    const files = inputElement.files;
    let totalSize: number = 0;

    // Array de extensiones permitidas
    const allowedExtensions: string[] = ['xls', 'csv', 'pdf', 'png', 'jpg'];

    // Limpiamos la lista de archivos y el nombre de los archivos seleccionados si ya hay elementos guardados.
    if (this.listFile) {
      this.selectedFileName = '';
    }

    let fileName: Array<string> = [];

    if (
      this.selectedFileName === '' ||
      this.selectedFileName.split(' ').length == 0
    ) {
      if (files && files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const fileExtension = file.name.split('.').pop()?.toLowerCase(); // Obtenemos la extensión del archivo

          // Validamos si la extensión está permitida
          if (fileExtension && allowedExtensions.includes(fileExtension)) {
            this.selectedFileName += `${guid}-` + file.name + ' ';
            fileName.push(`${guid}-` + file.name);
            totalSize += file.size;
          } else {
            this._messageService.messageInfo(
              `El archivo ${file.name} tiene una extensión no permitida.`,
              `Solo se permiten: ${allowedExtensions.join(', ').toUpperCase()}`
            );

            // Limpiar la lista de archivos y el nombre seleccionado
            this.selectedFileName = '';
            this.listFile = [];
            this.listIdFiles = [];
            this.editedFiles = false;

            return; // Salir de la función si hay un archivo con extensión no válida
          }
        }

        totalSize = totalSize / (1024 * 1024);
        if (totalSize < 20) {
          const copiedFiles: File[] = Array.from(files).map(
            (file) =>
              new File([file], `${guid}-` + file.name, { type: file.type })
          );
          this.listFile = copiedFiles;
          this.assignDocumentNames();
          this.editedFiles = true;
        }
      }
    }
  }

  assignDocumentNames() {
    let listFileName: string = '';
    this.listFile.forEach((element) => {
      listFileName = listFileName + element.name;
    });
    this.form.get('Files')?.setValue(listFileName);
  }

  //Permite descargar desde la api de file una lista de archivos.
  getFileByIdList(idFiles: Array<number>) {
    this._fileService
      .GetFileByIdList(idFiles)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: any) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            // Intenta obtener el nombre del archivo desde la cabecera Content-Disposition
            const fileName = resp.headers.get('X-File-Name') || 'file.zip';
            // Especifica el tipo MIME del archivo según el tipo que se espera
            const mimeType = 'application/octet-stream';
            const blob = new Blob([resp.body], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = fileName;
            link.href = url;
            link.click();
            window.URL.revokeObjectURL(url);
          }
        }
      });
  }

  //Evento que se dispara una vez se preciona en el iciono de descargar documento.
  downloadDocument() {
    if (this.listIdFiles.length > 0) {
      this.getFileByIdList(this.listIdFiles.map((value) => value.idUploadFile));
    }
  }

  //Elimina el archivo cargado.
  deleteDocument() {
    this.form.get('Files')?.setValue(null);
    this.listFile = [];
    this.listIdFiles = [];
    this.selectedFileName = '';
    this.showBtnDownload = false;
    this.editedFiles = true;
  }
  //Función que se ejecuta al darle click al botón cancelar.
  cancel() {
    this._location.back();
  }

  //Función que se ejecuta al darle click al botón guardar cambios.
  saveChange() {
    let startValidity = this.form.get('StartValidity')?.value;
    startValidity.setHours(0, 0, 0, 0);
    if (startValidity <= this.endValidity){
      this._messageService.messageWaring(this._translateService.instant('Policy.InvalidRenovationValidity'), '');
      return;
    }

    if (!this.form.valid){
      this.form.markAllAsTouched();
      this._messageService.messageWaring(this._translateService.instant('InvalidForm') + ": " + this._translateService.instant('Policy.PolicyData'), '');
      return;
    }

    const payload: FormData = new FormData();
    payload.append('IdPolicy', this.idWtw?.toString() || '');
    payload.append('IdPolicyType', this.idPolicyType?.toString() || '');
    payload.append('IdUser', this.idUser?.toString() || '');
    payload.append('DStartValidity', this.form.get('StartValidity')?.value ? this._utilService.formatDate(this.form.get('StartValidity')?.value).toString() : '');
    payload.append('DEndValidity', this.form.get('EndValidity')?.value ? this._utilService.formatDate(this.form.get('EndValidity')?.value).toString() : '');
    payload.append('BIsExtension', this.form.get('Extension')?.value?.toString() || '');
    payload.append('VPolicyName', this.form.get('PolicyName')?.value?.toString() || '');
    payload.append('VPolicyNumber', this.form.get('PolicyNumber')?.value?.toString() || '');
    payload.append('EditedFiles', this.editedFiles?.toString() || '');
    payload.append('Coverages', this.coverages.length > 0 ? JSON.stringify(this.coverages) : '');

    let files: File[] = this.listFile;
    if (files) {
      if (files.length > 0) {
        if (this.idWtw > 0 && this.editedFiles) {
          const EditedFiles: boolean = true;
          const addFiles: File[] = files;
          if (addFiles && addFiles.length > 0) {
            addFiles.forEach((file) => {
              const fileObject = file;
              payload.append(`Files`, fileObject); // Agregar cada archivo al FormData
            });
            payload.append('EditedFiles', EditedFiles.toString());
          }
        } else {
          const EditedFiles: boolean = false;
          const addFiles: File[] = files;
          if (addFiles && addFiles.length > 0) {
            addFiles.forEach((file) => {
              const fileObject = file;
              payload.append(`Files`, fileObject); // Agregar cada archivo al FormData
            });
            payload.append('EditedFiles', EditedFiles.toString());
          }
        }
      }
    }
    this._transactionService
      .renewPolicy(payload)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._location.back();
          if (this.form.get('Extension')?.value == true){
            this._messageService.messageConfirmatio(
              this._translateService.instant('Policy.ExtensionSaved'), '', 'success',
              this._translateService.instant('Continue'));
          }
          else {
            this._messageService.messageConfirmatio(
              this._translateService.instant('Policy.RenovationSaved'), '', 'success',
              this._translateService.instant('Continue'));
          }
        }
      });
  }

  //Obtiene la lista de tipo de identificación registrados en el sistema.
  getPolicyDetailById(idPolicy: number) {
    this._transactionService
      .getPolicyDetailById(idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.validationsForm(resp.result.idPolicyType);
          this.assignDataForm(resp.result);
        }
      });
  }

  validationsForm(policyType: number) {
    if (policyType == 2){
      this.form.get('PolicyNumber')?.setValidators(Validators.required);
      this.form.get('PolicyNumber')?.updateValueAndValidity();
    }
  }

  assignDataForm(data: PolicyDetailModel) {
    let startDate = new Date(data.startValidity);
    startDate.setDate(startDate.getDate() + 1);
    let endDate = new Date(data.endValidity);
    endDate.setDate(endDate.getDate() + 1);

    this.form.get('IdPolicy')?.setValue(data.idPolicy);
    this.form.get('PolicyName')?.setValue(data.policyName);
    this.form.get('IdBusinessCountry')?.setValue(data.idBusinessCountry);
    this.form.get('PolicyNumber')?.setValue(data.policyNumber);
    this.form.get('StartValidity')?.setValue(startDate);
    this.form.get('EndValidity')?.setValue(endDate);
    this.form.get('EditedFiles')?.setValue(false);
    this.listIdFiles = data.filesCreated;
    const [year, month, day] = data.endValidity.split('-').map(Number);
    this.endValidity = new Date(year, month - 1, day);
    this.endValidity.setHours(0, 0, 0, 0);
    if (data.filesCreated) {
      let listFileName: string = '';
      if (data.filesCreated.length > 0) {
        data.filesCreated.forEach((element: any) => {
          listFileName = listFileName + element.fileName;
        });
        this.form.get('Files')?.setValue(listFileName);
        this.showBtnDownload = true;
      }
    }
    this.getInsuranceById(data.idInsurance);
    this.GetProductById(data.idProduct);
  }

  getCoverages(coverages: any[]){
    this.coverages = coverages;
  }

  getInsuranceById(idInsurance: number){
    this._insuranceService
      .getInsuranceCompanyById(idInsurance)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.form.get('InsuranceName')?.setValue(resp.result.vName);
        }
      });
  }

  GetProductById(idProduct: number){
    this._productService
      .GetProductById(idProduct)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.form.get('ProductName')?.setValue(resp.result.vProductName);
        }
      });
  }
}
