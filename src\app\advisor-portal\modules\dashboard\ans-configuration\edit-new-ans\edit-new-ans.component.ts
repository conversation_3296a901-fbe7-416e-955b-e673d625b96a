import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabsModule } from '@angular/material/tabs';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { Subscription, catchError, debounceTime, of } from 'rxjs';

import { MeasurementModel, TypeDayEnum } from 'src/app/shared/models/ans';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { GlobalSelectModel } from 'src/app/shared/models/shared';
import { AnsService } from 'src/app/shared/services/ans/ans.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { WorkFlowService } from 'src/app/shared/services/work-flow/work-flow.service';
import { GeneralConditionsComponent } from '../general-conditions/general-conditions.component';

@Component({
  selector: 'app-edit-new-ans',
  standalone: true,
  imports: [
    CommonModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule,
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatInputModule,
    MatTabsModule,
    GeneralConditionsComponent,
  ],
  templateUrl: './edit-new-ans.component.html',
  styleUrls: ['./edit-new-ans.component.scss'],
})
export class EditNewAnsComponent implements OnInit, OnDestroy {
  //Variables para manejo de información entre componentes.
  private _formAns?: Subscription;
  @Output() formValid = new EventEmitter<any>();
  @Output() modalAction = new EventEmitter<any>();
  @Output() closeModal = new EventEmitter<any>();
  @Input() pkIIdGeneralAns: number = 0;
  validFormGeneralConditions: boolean = false;
  ansToEdit: any = {};

  //Variables para los formularios
  formAddAns: FormGroup = this._fb.group({});
  firstTime: boolean = true;

  //Variables para los select
  processes: any[] = [];
  products: any[] = [];
  stage: any[] = [];
  state: any[] = [];
  measurementTakenFrom: GlobalSelectModel[] = [];
  measurementTakenUpTo: GlobalSelectModel[] = [];
  measurement: MeasurementModel[] = [];

  //variables relacionadas con empresa pais.
  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessCountry: number = 0;

  constructor(
    private _fb: FormBuilder,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _settingService: SettingService,
    private _parametersService: ParametersService,
    private _roleService: RoleService,
    public utilsService: UtilsService,
    private _moduleService: ModuleService,
    private _ansService: AnsService,
    private _workFlowService: WorkFlowService
  ) {
    this.measurementTakenFrom = [
      {
        value: 1,
        name: this._translateService.instant(
          'AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenFromFirtsItem'
        ),
      },
      {
        value: 2,
        name: this._translateService.instant(
          'AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenFromSecondItem'
        ),
      },
    ];
    this.measurementTakenUpTo = [
      {
        value: 1,
        name: this._translateService.instant(
          'AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenUpToFirtsItem'
        ),
      },
      {
        value: 2,
        name: this._translateService.instant(
          'AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenUpToSecondItem'
        ),
      },
    ];
  }

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this.getParameters();
    this.initFormAddAns();
    this.getDataFormAns();
    this.validateAction(this.pkIIdGeneralAns);
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data de select de la epstaña medición.
      this.measurementTakenFrom[0].name = this._translateService.instant(
        'AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenFromFirtsItem'
      );
      this.measurementTakenFrom[1].name = this._translateService.instant(
        'AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenFromSecondItem'
      );
      this.measurementTakenUpTo[1].name = this._translateService.instant(
        'AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenUpToFirtsItem'
      );
      this.measurementTakenUpTo[1].name = this._translateService.instant(
        'AnsConfiguration.ModalAns.MeasurementTab.MeasurementTakenUpToSecondItem'
      );
    });
  }

  //Obtiene la información del formulario de ANS.
  getDataFormAns() {
    this._formAns = this._ansService.currentAnsForm.subscribe((response) => {
      if (!(Object.keys(response).length === 0)) {
        if (response.saveAns) {
          this.saveAns();
        } else if (response.editAns) {
          this.editAns();
          if (response.editAns.fkIdProduct) {
            this.editAns();
          }
        }
      }
    });
  }

  //Obtiene la data del businnesByCountry incial, seleccionada por el usuario.
  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;
            this.getAllProcess();
          } else {
          }
        }
      );
  }

  //Función que inicializa el formulario para añadir ANS.
  initFormAddAns() {
    this.formAddAns = this._fb.group({
      pkIIdGeneralAns: [0],
      fkIIdCountryHoliday: [null],
      fkIIdState: [null, [Validators.required]],
      bActive: [true],
      dDateInitial: [null, Validators.required],
      dDateEnd: [null, Validators.required],
      iMeasuringFrom: [0],
      iMeasuringUntil: [0],
      iCantInstructor: [null, Validators.required],
      vUnitMeasurementInstructor: [''],
      iCantGoal: [0],
      vUnitMeasurementGoal: [''],
      iCantAlert: [0],
      vUnitMeasurementAlert: [0],
      fkIIdBusinessByCountry: [this.idBusinessCountry],
      fkIIdGeneralAns: [0],
      bIncludeRestDay: [true],
      bIncludeHolidays: [true],
      bSpecificCondition: [true],
      iTypeDays: [0],
      iTimeZone: [0],
      vJson: [''],
      fkIdProcess: [null, [Validators.required]],
      fkIdProduct: [null],
      fkIdStage: [null, [Validators.required]],
    });

    this.formAddAns.get('fkIdProcess')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          if (this.firstTime) {
            this.firstTime = !this.firstTime;
          }
          if (this.pkIIdGeneralAns == 0) {
            this.formAddAns.get('fkIdProduct')?.setValue(null);
            this.formAddAns.get('fkIdStage')?.setValue(null);
            this.formAddAns.get('fkIIdState')?.setValue(null);
            this.products = [];
            this.stage = [];
            this.state = [];
          }
          if (this.pkIIdGeneralAns > 0 && !this.firstTime) {
            this.formAddAns.get('fkIdProduct')?.setValue(null);
            this.formAddAns.get('fkIdStage')?.setValue(null);
            this.formAddAns.get('fkIIdState')?.setValue(null);
            this.products = [];
            this.stage = [];
            this.state = [];
          }

          this.getProductsList();
        }
      },
    });

    this.formAddAns.get('fkIdProduct')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          if (this.pkIIdGeneralAns == 0) {
            this.formAddAns.get('fkIdStage')?.setValue(null);
            this.formAddAns.get('fkIIdState')?.setValue(null);
            this.stage = [];
            this.state = [];
          }
          if (this.pkIIdGeneralAns > 0 && !this.firstTime) {
            this.formAddAns.get('fkIdStage')?.setValue(null);
            this.formAddAns.get('fkIIdState')?.setValue(null);
            this.stage = [];
            this.state = [];
          }
          this.getStageByIdProductModule();
        }
      },
    });

    this.formAddAns.get('fkIdStage')?.valueChanges.subscribe({
      next: (data) => {
        if (data) {
          if (this.pkIIdGeneralAns == 0) {
            this.formAddAns.get('fkIIdState')?.setValue(null);
            this.state = [];
          }
          if (this.pkIIdGeneralAns > 0 && !this.firstTime) {
            this.formAddAns.get('fkIIdState')?.setValue(null);
            this.state = [];
          }
          this.getStageByStateList();
        }
      },
    });

    this.formAddAns?.valueChanges.pipe(debounceTime(600)).subscribe({
      next: (data) => {
        if (data) {
          if (this.formAddAns.valid && this.validFormGeneralConditions) {
            this.formValid.emit(true);
          } else if (this.formAddAns.valid && !data.bSpecificCondition) {
            this.formValid.emit(true);
          } else {
            this.formValid.emit(false);
          }
        }
      },
    });
  }

  //Función que valida la Acción que se está ejecutando en el llamado del componete
  validateAction(pkIIdGeneralAns: number) {
    if (pkIIdGeneralAns > 0) {
      this.modalAction.emit('edit');
      this.getGeneralANSByIdGeneralANS(pkIIdGeneralAns);
    } else {
      this.modalAction.emit('create');
    }
  }

  //Obtiene el valor del control bSpecificCondition.
  get bSpecificCondition(): boolean {
    return this.formAddAns.get('bSpecificCondition')?.value;
  }

  //Obtiene todos los procesos.
  getAllProcess() {
    this._parametersService
      .getAllProcess()
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.processes = resp.result;
        }
      });
  }

  //obtiene los productos registrados por proceso y idBusinessCountry
  getProductsList() {
    this._roleService
      .getProductsByIdProcess(
        this.formAddAns.get('fkIdProcess')?.value,
        this.idBusinessCountry
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('WarningMessage'),
              this._translateService.instant('TaskTray.Messages.NoProducts')
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.products = [];
          this.formAddAns.get('fkIdProduct')?.setValue(null);
        } else {
          if (resp.result.length === 1) {
            this.formAddAns.get('fkIdProduct')?.disable();
            this.formAddAns
              .get('fkIdProduct')
              ?.setValue(resp.result[0].pkIIdProductModule);
          } else {
            this.formAddAns.get('fkIdProduct')?.enable();
            this.products = resp.result;
          }
        }
      });
  }

  //obtiene la lista de etapas asignados a un producto.
  getStageByIdProductModule() {
    if (this.formAddAns.get('fkIdProduct')?.value > 0) {
      this._moduleService
        .getStageByIdProductModule(this.formAddAns.get('fkIdProduct')?.value)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('Warning'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            this.formAddAns.get('fkIIdState')?.setValue(null);
            this.state = [];
          } else {
            this.stage = resp.result;
          }
        });
    }
  }

  //Obtiene la lista de estados asociados a una etapa.
  getStageByStateList() {
    this._moduleService
      .getStageByStateById(this.formAddAns.get('fkIdStage')?.value)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.state = [];
        } else {
          this.state = resp.result;
          this.firstTime = !this.firstTime;
        }
      });
  }

  //Función que obtiene las unidades de medidas.
  getParameters() {
    this._parametersService
      .getParameters('UnitMeasuring')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.measurement = resp;
        } else {
        }
      });
  }

  //Obtiene un ANS por filtrado por idGeneralANS.
  getGeneralANSByIdGeneralANS(idGeneralANS: number) {
    this._workFlowService
      .getGeneralANSByIdGeneralANS(idGeneralANS)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.formAddAns.setValue(resp.result);
          this.ansToEdit = resp.result;
        }
      });
  }

  //Función que gaurda la ANS.
  saveAns() {
    if (this.formAddAns.valid) {
      this.formAddAns
        .get('iTypeDays')
        ?.setValue(
          this.parseTypeDaysValue(this.formAddAns.get('iTypeDays')?.value)
        );
      this._workFlowService
        .createGeneralANS(this.formAddAns.value)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant(
                'ModulesSetting.SuccessfulMessageCreated'
              ),
              ''
            );
            this.pkIIdGeneralAns = 0;
            //Accción de cerrar modal.
            this.closeModal.emit(true);
          }
        });
    }
  }

  //Función que edita la Ans.
  editAns() {
    if (this.formAddAns.valid) {
      this.formAddAns
        .get('iTypeDays')
        ?.setValue(
          this.parseTypeDaysValue(this.formAddAns.get('iTypeDays')?.value)
        );
      this._workFlowService
        .updateGeneralANS(this.formAddAns.value)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this._messageService.messageSuccess(
              this._translateService.instant(
                'ModulesSetting.SuccessfulMessageUpdated'
              ),
              ''
            );
            this.pkIIdGeneralAns = 0;
            //Accción de cerrar modal.
            this.closeModal.emit(true);
          }
        });
    }
  }

  //Obtiene la información del formulario de condiciones generales.
  getGeneralConditionsFormValue(event: any) {
    this.validFormGeneralConditions = event.formValid;
    if (this.formAddAns.valid && event.formValid) {
      this.formValid.emit(true);
    } else if (
      this.formAddAns.valid &&
      this.formAddAns.get('bSpecificCondition')
    ) {
      this.formValid.emit(true);
    } else {
      this.formValid.emit(false);
    }
    this.formAddAns
      .get('bIncludeHolidays')
      ?.setValue(event.formValue.bIncludeHolidays);
    this.formAddAns
      .get('bIncludeRestDay')
      ?.setValue(event.formValue.bIncludeRestDay);

    this.formAddAns.get('iTypeDays')?.setValue(event.formValue.iTypeDays);

    this.formAddAns.get('iTimeZone')?.setValue(event.formValue.iTimeZone);

    this.formAddAns
      .get('fkIIdCountryHoliday')
      ?.setValue(event.formValue.fkIIdCountryHoliday);

    this.formAddAns
      .get('vJson')
      ?.setValue(JSON.stringify(event.formValue.daysOfWeek));
  }

  //Cambia el valor de tipo de días a un numero.
  parseTypeDaysValue(value: string): number {
    switch (value) {
      case 'Habiles':
        return TypeDayEnum.Habiles;
      case 'Calendario':
        return TypeDayEnum.Calendario;

      default:
        return TypeDayEnum.Default;
    }
  }

  //Acciones que se ejecutan cuándo se destuye el componete actual.
  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
    this._formAns?.unsubscribe();
  }
}
