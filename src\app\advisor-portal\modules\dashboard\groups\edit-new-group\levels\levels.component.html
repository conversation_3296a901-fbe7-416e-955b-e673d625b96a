<div class="title-customer">
  <h3 class="h3">
    {{'Group.GroupLevels' | translate}}
    <mat-icon class="click" matTooltipPosition="right" matTooltip="{{'Tooltips.LevelTitle' | translate}}">help_outline</mat-icon>
  </h3>
  <span> 
    {{'Group.AllChangesWillBeSavedAutomatically' | translate}}
  </span>  
</div>
<form class="mt-3" [formGroup]="form">
  <div formArrayName="lavels">
    <div class="row" *ngFor="let level of lavels.controls; let i = index">
      <div class="col-12 col-md-12" [formGroupName]="i">
        <div class="example-form mt-3">
          <mat-form-field class="example-full-width">
            <mat-label>
              {{ 'Group.Level' | translate }} 
              {{ i + 1 }}
            </mat-label>
            <input
              PreventionSqlInjector
              type="text"
              matInput
              placeholder="{{ 'Group.Level' | translate }} {{ i + 1 }}"
              formControlName="vLevelName"
            />
            <mat-icon
              *ngIf="i !== 0"
              class="click"
              (click)="removeGroup(i, level)"
              matSuffix
              >delete_forever</mat-icon
            >
            <mat-error
              *ngIf="utilsSvc.isControlHasError(form, 'vLevelName', 'required')"
            >
            {{ 'ThisFieldIsRequired' | translate }}
            
            </mat-error>
          </mat-form-field>
          <span *ngIf="i == 0">
            {{ 'Group.LevelHighestAccess' | translate }}
          </span>
        </div>
      </div>
    </div>
  </div>
</form>
<div *ngIf="!gruopState">
  <p>* 
    {{ 'Group.GroupsDisabledAddLevels' | translate }}
  </p>
</div>

<div class="row mt-2">
  <div class="col-md-12">
    <button
      [disabled]="!gruopState"
      (click)="addGroup()"
      type="button"
      mat-raised-button
      color="primary"
    >
    {{ 'Group.AddLevel' | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
    </button>
  </div>
</div>
