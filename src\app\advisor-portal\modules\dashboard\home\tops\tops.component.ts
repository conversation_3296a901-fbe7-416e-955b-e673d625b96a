import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TopModel } from 'src/app/shared/models/business';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { SectionIndexModel } from 'src/app/shared/models/configuration-index';

@Component({
  selector: 'app-tops-home',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './tops.component.html',
  styleUrls: ['./tops.component.scss'],
})
export class TopsHomeComponent implements OnInit, OnDestroy {
  @Input() topsObject: SectionIndexModel = SectionIndexModel.fromObj({});
  tops: TopModel[] = [];

  constructor(
    private _businessService: BusinessService,
    private _msgSvc: MessageService,
    private _translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.getTops();
  }
  ngOnDestroy(): void {}

  getTops() {
    if (this.topsObject.pkIIdSectionIndexBusiness !== 0) {
      this._businessService
        .getTopsBySection(this.topsObject.pkIIdSectionIndexBusiness)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._msgSvc.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
            } else {
              this.tops = resp.result;
              this.tops.forEach((element: TopModel) => {
                if (element.vJsonElements) {
                  element.vJsonElements = JSON.parse(element.vJsonElements);
                }
              });
            }
          }
        });
    }
  }
}
