import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit, Input } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UserService } from 'src/app/shared/services/user/user.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit, OnD<PERSON>roy {
  @Input() isNavbarExpanded: boolean = false;
  private _settingCompanySubscription?: Subscription;
  imageSrc: any;
  country?: string = '';
  userIdSession: number = 0;
  email: string = '';
  profile: string = '';
  constructor(
    public _translateService: TranslateService,
    private _fileService: FileService,
    private _settingService: SettingService,
    private _businessService: BusinessService,
    private _sanitizer: DomSanitizer,
    private _userService: UserService,
    private _customRouter: CustomRouterService,

  ) {}

  ngOnInit(): void {
    this.getDataSettingInit();
    
  }

  switchLang = (lang: string) => {
    this._translateService.use(lang);
  };

  getBusinessByCountry(idBusiness: number) {
    this._businessService.getBusinessCountryByBusinessId(idBusiness).subscribe({
      next: (response) => {
        this.getUserId(response.result[0].pkIIdBusinessByCountry);
        this.getImageBusiness(response.result[0].pkIIdBusinessByCountry);
      },
    });
  }

  getImageBusiness(pkIIdBusinessByCountry: number) {
    let extension: string = '';
    let base64: string = '';
    if (true) {
      this._fileService
        .getUploadFileByBusinessCountryId(pkIIdBusinessByCountry)
        .subscribe({
          next: (response) => {
            extension = response.result.vFileName.split('.');
            extension =
              extension[response.result.vFileName.split('.').length - 1];
            base64 = `data:image/${extension};base64,${response.result.imageBase64}`;
            this.imageSrc =
              this._sanitizer.bypassSecurityTrustResourceUrl(base64);
          },
          error: (err) => {
            this.imageSrc = `assets/img/image.svg`;
          },
        });
    } else {
      console.log('no hay valor');
    }
  }

  changeCompany() {
    this._customRouter.navigate(['/onboard/choose-company']);
  }

  logout() {
    let token = this._userService.getTokenLocalStorage();
    this._userService.logout(token).subscribe({
      next: (response) => {
        if (!response.error) {
          this._userService.removeTokenLocalStorage();
          this._settingService.removeDataSettingInit();
          this._customRouter.deleteNavigationData(); 
          localStorage.setItem('autoLoginMSAL', 'false');
          this._customRouter.navigate(['/auth/login']);
        }
      },
    });
  }

  async getUserId(idbusinessCountry: number) {
    let userIdSession = await this._settingService.getDataSettingInit();
    if (userIdSession) {
      this.userIdSession = Number(userIdSession.idUser);
    }
    if (this.userIdSession) {
      this.getUserPersonalList(idbusinessCountry);
    }
  }

  getUserPersonalList(idbusinessCountry: number) {
    this._userService.getUserPersonalList(this.userIdSession, idbusinessCountry).subscribe({
      next: (response) => {
        if(!response.error){
          this.email = response.result.vEmailUser;
          this.profile = response.result.vPositionName;
        }
      },
    });
  }

  async getDataSettingInit() {
    let data: any = await this._settingService.getDataSettingInit();
    this.country = data.countryName;
    this.getBusinessByCountry(data.idBusiness);
  }

  ngOnDestroy(): void {
    this._settingCompanySubscription?.unsubscribe();
  }

  goToProfile(){
    this._customRouter.navigate(['/profiles']);
  }
}
