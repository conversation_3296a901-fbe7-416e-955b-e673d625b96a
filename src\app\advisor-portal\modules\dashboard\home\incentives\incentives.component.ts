import { Component, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { catchError, of, Subscription } from 'rxjs';

import { NotMonetaryIncentivesTableModel } from 'src/app/shared/models/incentives/not-monetary-incentives-table.model';
import { CarouselIncentivesModel } from 'src/app/shared/models/incentives/carousel-incentives.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { FileService } from 'src/app/shared/services/file/file.service';
import { IncentivesService } from 'src/app/shared/services/incentives/incentives.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { NgFor, NgIf } from '@angular/common';
import { RouterLink, RouterModule } from '@angular/router';
import { DatePipe,CommonModule  } from '@angular/common';
import { SettingService } from 'src/app/shared/services/setting/setting.service';

@Component({
  selector: 'app-incentives-home',
  standalone: true,
  imports: [TranslateModule,MatCardModule, MatButtonModule, MatIconModule,NgFor,NgIf,RouterModule,RouterLink,DatePipe,MatButtonModule,CommonModule],
  templateUrl: './incentives.component.html',
  styleUrl: './incentives.component.scss'
})
export class IncentivesHomeComponent implements OnInit{

  private _settingCountryAndCompanySubscription?: Subscription;
  incentivesCarouselImages: any[] = [];
  currentSlide = 0;    
  currentSlides: number[] = [];
  autoSlideInterval: any;
  carouselIncentives: CarouselIncentivesModel[]=[];
  srcImage!:string;
  currentIndex: number = 0; 
  idBusinessCountry: number = 0;
  fileUrl!: SafeResourceUrl; 
  constructor(     
    private _messageService: MessageService,     
    private _fileService: FileService,
    private _sanitizer: DomSanitizer,
    public _incentivesService: IncentivesService,
    private _translateService: TranslateService,
    private _settingService: SettingService,
  ) {}

  ngOnInit(): void {
    this.getBusinessCountry();
    this.getAllIncentivesNonMonetarys()    
  } 

  getBusinessCountry() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idBusinessCountry = response.enterprise.pkIIdBusinessByCountry;                    
          }
        }
      );
  }

  getAllIncentivesNonMonetarys(){
    this._incentivesService.getAllIncentivesNonMonetarys(this.idBusinessCountry).pipe(
      catchError((error)=>{
        this._messageService.messageWaring('', error.error.message);      
        return of([]);
      })
    ).subscribe((resp:ResponseGlobalModel | never[])=>{
      if (Array.isArray(resp)) {
        this._messageService.modalMessage('','Hubo un error al tratar de obtener todos los incentivos.',          "error",
          this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
      }else{                                      
        this.carouselIncentives= resp.result.filter((img:any)=>img.imagePath);       
        this.getImagesIncentives();
        // this.getImage(this.carouselIncentives);
        
      }                
    });
  }

getImagesIncentives(){
  this.carouselIncentives.forEach((element:CarouselIncentivesModel,index:number )=>{

    this._fileService.getUploadFileById(element.fkIIdUploadImage).pipe(
      catchError((error)=>{
        this._messageService.messageWaring('', error.error.message);      
        return of([]);
      })
    ).subscribe((resp:ResponseGlobalModel | never[])=>{
      if (Array.isArray(resp)) {
        this._messageService.modalMessage('','Hubo un error al tratar de obtener todos los incentivos.',          "error",
          this._translateService.instant('IncentivesForm.TabMonetaryValidationModalContinueButtonText'), );  
      }else{               
             element.imageBase64=resp.result.imageBase64;              
             this.getImage(this.carouselIncentives);         
      }                
    });;
  })
  
}


  getImage(array: CarouselIncentivesModel[]) {
    let src = '';
    array.forEach((element: CarouselIncentivesModel, index: number) => {     
      const fileName = element.imagePath.split('\\').pop() || '';      
      const extension = fileName.split('.').pop() || 'png';      
      src = `data:image/${extension};base64,${element.imageBase64}`;
      this.srcImage=src;     
      this.carouselIncentives[index].src = this.srcImage;

     });     
  }
   
  prev() {
    this.currentIndex =
    (this.currentIndex - 1 + this.carouselIncentives.length) %
    this.carouselIncentives.length;
  }

  next() {
    this.currentIndex =(this.currentIndex + 1) % this.carouselIncentives.length;
  }
  openPDF(pdfUrl: string|undefined) {
    debugger
    if(pdfUrl != undefined){
      const date = new Date().toISOString().split('T')[0];
      this.downloadFileByFilePath(pdfUrl, date );
    }
  } 
  downloadFileByFilePath(filePath: string, title: string) {
    this._fileService.downloadFileByFilePath(filePath).subscribe({
      next: (blob) => {
        const link = document.createElement('a');
        const objectUrl = URL.createObjectURL(blob);
        this.fileUrl = this._sanitizer.bypassSecurityTrustResourceUrl(objectUrl);       
         link.href = objectUrl;
         link.download = title;
         link.click();
         window.URL.revokeObjectURL(objectUrl);
      },
      error: (err) => {
        this._messageService.messageWaring('','Error al descargar el archivo')
      }
    });
  }
}
