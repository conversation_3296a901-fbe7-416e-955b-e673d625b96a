import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { ForgotPasswordModel } from 'src/app/shared/models/user';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';

@Component({
  selector: 'app-recover-password',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule
  ],
  templateUrl: './recover-password.component.html',
  styleUrls: ['../auth.scss'],
})
export class RecoverPasswordComponent implements OnInit {
  form: FormGroup = new FormGroup({});
  businessCountryName: string | null = null;
  constructor(
    private messageService: MessageService,
    private fb: FormBuilder,
    public utilsService: UtilsService,
    private userService: UserService,
    private _translateService: TranslateService,
    private router: Router,
    private _customRouter: CustomRouterService,
    private _ar: ActivatedRoute
  ) {
    this.initForm();
  }
  ngOnInit(): void {
    const parts = this.router.url.split('/');
    this._customRouter.setNavigationData(parts[1], parts[2]);
  }

  initForm() {
    this.form = this.fb.group({
      email: [
        '',
        [
          Validators.email,
          Validators.required,
          Validators.pattern(
            /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],
      documentNumber: ['', [Validators.required]],
    });
  }

  get valid(): boolean {
    return this.form.valid;
  }

  recoverPassword() {
    let payload: ForgotPasswordModel = {
      ...this.form.value,
      BusinessCountry: this.router.url.split('/')[2]
    }
    this.userService.forgotPassword(payload).subscribe({
      next: (response) => {
        if (!response.error) {
          this.messageService.messageSuccess(
            this._translateService.instant('Auth.ARecoveryLinkHasBeenSent'),
            this._translateService.instant('Auth.PleaseGoThereToRecoverPassword')
          );
          this._customRouter.navigate(['/auth/login']);
        } else {
          this.messageService.messageInfo(this._translateService.instant('ThereWasAError'), response.message);
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  goToLogIn(){
    this._customRouter.navigate(['/auth/login']);
  }
}
