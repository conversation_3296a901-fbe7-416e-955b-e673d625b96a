import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Element<PERSON><PERSON>, On<PERSON><PERSON><PERSON>, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms";
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { catchError, of, Subscription } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { ValidationInputFileDirective } from 'src/app/shared/directives/shared/validation-input-file.directive';
import { LinkPrivacyPoliciesModel, PrivacyPoliciesModel } from 'src/app/shared/models/privacy-policies';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BodyTableModel, IconEventClickModel } from 'src/app/shared/models/table';
import { BusinessService } from 'src/app/shared/services/business/business.service';
import { CopyrigthService } from "src/app/shared/services/copyrigth/copyrigth.service";
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { UtilsService } from "src/app/shared/services/utils/utils.service";


@Component({
  selector: 'app-config-footer',
  standalone: true,
  imports: [
    MatCardModule,
    CommonModule,
    MatButtonModule,
    MatInputModule,
    TranslateModule,
    ReactiveFormsModule,
    TableComponent,
    Modal2Component,
    PreventionSqlInjectorDirective,
    MatSlideToggleModule,
    MatSelectModule,
    FormsModule,
    ValidationInputFileDirective
  ],
  templateUrl: './config-footer.component.html',
  styleUrls: ['./config-footer.component.scss']
})
export class ConfigFooterComponent implements OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;

  logoUrl: string | ArrayBuffer | null = null;
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;
  maxFileSize = 5 * 1024 * 1024;
  idBusinessByCountry: number = 0;
  pkIIdCopyright: number = 0;
  pkIIdLink: number = 0;

  vExternalPDF: string = "";
  vFileName: string = "";
  FkIIdUploadedFile: number = 0;
  imageSrc: string = '';
  dataTableCategory: any[] = [];
  dataTableLinks: any[] = [];
  dataTableContacto: any[] = [];
  dataTablePrivacyPolices: LinkPrivacyPoliciesModel[] = [];

  pkCategoryToModify: number = 0;
  pkLinkToModify: number = 0;
  pkContactToModify: number = 0;
  pkPrivacyPolicies = 0;
  pkLinkPrivacyPolicies = 0;

  _currentModal: MatDialogRef<any> | null = null;
  @ViewChild('createEditCategory') createEditCategory?: TemplateRef<any>;
  @ViewChild('createEditLink') createEditLink?: TemplateRef<any>;
  @ViewChild('createEditContact') createEditContact?: TemplateRef<any>;
  @ViewChild('createEditPrivacyPolicies') createEditPrivacyPolicies?: TemplateRef<any>;
  

  titlePrivacyPolices = '';
  subtitlePrivacyPolices = '';

  allowedExtensions: string[] = ['png','svg'];
  maxFileSizeMB: number = 5;
  formSubs?: Subscription;

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
  }
  constructor(
    private _settingService: SettingService,
    private _router: Router,
    private _translateService: TranslateService,
    private _fb: FormBuilder,
    private _copyrigthService: CopyrigthService,
    public utilsSvc: UtilsService,
    private _fileService: FileService,
    private _messageService: MessageService,
    private _cdr: ChangeDetectorRef,
    private _modalDialog: MatDialog,
    private _businessService: BusinessService

  ) {
  }
  formCopyrigth: FormGroup = this._fb.group({
    vText: [null],
    vDisclaimer: [null],
    FkIdBusinessByCountry: [null],
  })
  formCategory: FormGroup = this._fb.group({
    VName: ['', Validators.required],
  })
  formLink: FormGroup = this._fb.group({
    FkIIdCategory:[null, Validators.required],
    VName: ['', Validators.required],
    vLink: ['', Validators.required],

  })
  formContact: FormGroup = this._fb.group({
    VName: ['', Validators.required],
    VContact: ['', Validators.required],

  })
 
  formLinkPrivacyPolicies: FormGroup = this._fb.group({
    idLinkPrivacyPoliciesFooter: [0],
    idPrivacyPoliciesFooter: [0],
    name: ['', Validators.required],
    link: ['', Validators.required],
    bActive:[ true ]

  })

  Listproducts: any[] = [
    { id: 1, name: 'Pestaña 1' },
    { id: 2, name: 'Pestaña 2' },
    { id: 3, name: 'Pestaña 3' },

  ]
  
  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            setTimeout(() => {
              if (!(Object.keys(response).length === 0)) {
                this.idBusinessByCountry = response.enterprise.pkIIdBusinessByCountry;
                this._loadCopyrigth();
                this._loadCategory();
                this.getAllPrivacyPoliciesFooter(this.idBusinessByCountry);
              }
            }, 500);          
          }
        }
      );
  }
  private _loadCopyrigth() {
    this._copyrigthService.getCopyrigths(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.pkIIdCopyright = resp.result[0].pkIIdCopyright;
          this.FkIIdUploadedFile = resp.result[0].fkIIdUploadedFile;

          this.formCopyrigth.patchValue({
            vText: resp.result[0].vName,
            vDisclaimer: resp.result[0].vDisclaimer,
            FkIdBusinessByCountry: resp.result[0].FkIdBusinessByCountry,

          })
          if (this.FkIIdUploadedFile > 0) {
            this.getIMG(this.FkIIdUploadedFile)
          }


        }
      })
    })
  }

  selectLogo() {
    this.fileInput.nativeElement.click();
  }
  onFileSelected(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];

    if (file) {
      const validImageTypes = ['image/png', 'image/svg+xml'];
      // Validar el tipo de archivo
      if (!validImageTypes.includes(file.type)) {
        alert('Por favor, seleccione un archivo válido (PNG o SVG)');
        return;
      }
      // Validar el tamaño del archivo (5MB máximo)
      if (file.size > this.maxFileSize) {
        alert('El archivo supera el tamaño máximo permitido de 5MB');
        return;
      }
      const reader = new FileReader();
      reader.onload = () => {
        this.logoUrl = reader.result;
        this.vExternalPDF = reader.result as string;
        this.vFileName = file.name;
        this.imageSrc = reader.result as string;
      };
      
      reader.readAsDataURL(file);
    }
  }
  save() {
    this._copyrigthService.modifyCopyrigth({
      pkIIdCopyright: this.pkIIdCopyright,
      fkIdBusinessByCountry: this.idBusinessByCountry,
      vName: this.formCopyrigth.get('vText')?.value,
      vDisclaimer: this.formCopyrigth.get('vDisclaimer')?.value,
      bActive: true,
      fkIIdUploadedFile: this.FkIIdUploadedFile,
      vFileName: this.vFileName,
      vExternalPDF: this.vExternalPDF
    }).subscribe({
      next: (response) => {
        console.log('Respuesta del servicio:', response);
      },
      error: (err) => {
        console.error('Error en el servicio:', err);
        alert(`Ocurrió un error: ${err.message || 'Error desconocido'}`);
      }
    });
  }

  async getIMG(idUploadFile: number) {
    let extension: string = '';
    
    await this._fileService
      .getUploadFileById(idUploadFile, true)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant('')
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.result.vFileName && resp.result.imageBase64) {
            extension = resp.result.vFileName.split('.');
            extension =
              extension[resp.result.vFileName.split('.').length - 1];
            this.imageSrc = resp.result.vFilePath;
            this.logoUrl = this.imageSrc;
            this.imageSrc = `data:image/${extension};base64,${resp.result.imageBase64}`;

            // this.formActiveNews
            //   .get('fileName')
            //   ?.setValue(resp.result.vFileName);
            // this.formActiveNews
            //   .get('imageBase64')
            //   ?.setValue(resp.result.imageBase64);
          }

          if (resp.result != null) {
            if (resp.result.vFilePath && resp.result.imageBase64) {
              // this.pdfSrc =
              //   'data:application/pdf;base64,' + resp.result.imageBase64;
            }
          }
        }
      });
  }

  estructTableCategory: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Nombre'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ];
  estructTableLinks: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Nombre'),
      columnValue: 'name',
    },
    {
      columnLabel: this._translateService.instant('Link'),
      columnValue: 'link',
    },
    {
      columnLabel: this._translateService.instant('Categoria'),
      columnValue: 'category',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyLink',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteLink',
      columnIcon: 'delete',
    },
  ];
  estructTableContacto: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Texto'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Contacto'),
      columnValue: 'vContact',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyContact',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deleteContact',
      columnIcon: 'delete',
    },
  ];

  estructTablePrivacyPolicies: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Nombre'),
      columnValue: 'name',
    },
    {
      columnLabel: this._translateService.instant('Link'),
      columnValue: 'link',
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modifyPrivacyPolicies',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant('Delete'),
      columnValue: 'deletePrivacyPolicies',
      columnIcon: 'delete',
    },
  ];

  private _loadCategory() {
    this._copyrigthService.getCategory(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.dataTableCategory = [...resp.result];
          const selectedCategories: any[] = this.dataTableCategory.map(item => item.pkIIdCategory);

          this._cdr.detectChanges()
          this._copyrigthService.getLink(selectedCategories).subscribe({
            next: (resp => {
              if (!resp.error) {
                this.dataTableLinks = [...resp.result];
                this._cdr.detectChanges()
              }
            })
          })
          

        }
      })
    })

    this._copyrigthService.getContacto(this.idBusinessByCountry).subscribe({
      next: (resp => {
        if (!resp.error) {
          this.dataTableContacto = [...resp.result];
          this._cdr.detectChanges()
        }
      })
    })
  }
  controller(evt: IconEventClickModel) {
    switch (evt.column) {
      case "delete":
        this._copyrigthService.deleteCategory(evt.value.pkIIdCategory).subscribe({
          next: (response) => {
            if (!response.error) {
              this._loadCategory()
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
        break;
      case "deleteLink":
        this._copyrigthService.deleteLink(evt.value.pkIIdFooterLink).subscribe({
          next: (response) => {
            if (!response.error) {
              this._loadCategory()
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
        break;
      case "deleteContact":
        this._copyrigthService.deleteContact(evt.value.pkIIdContact).subscribe({
          next: (response) => {
            if (!response.error) {
              this._loadCategory()
              this._messageService.messageSuccess(
                this._translateService.instant('DataSavedSuccessfully'),
                this._translateService.instant('SectionClientPortal.CoveragesDeletedSucessfully')
              );
            }
          },
          error: (error) => {
            this._messageService.messageInfo(
              this._translateService.instant('ThereWasAError'),
              error
            );
          },
        })
        break;
      case "modify":
        this.pkCategoryToModify = evt.value.pkIIdCategory
        this.formCategory.patchValue({
          VName: evt.value.vName,
        })
        this.openModalCategory()
        break;
      case "modifyLink":
        this.pkLinkToModify = evt.value.pkIIdFooterLink
        this.formLink.patchValue({
          FkIIdCategory: evt.value.fkIIdCategory,
          vLink: evt.value.link,
          VName: evt.value.name,
        })
        this.openModalLink()
        break;
      case "modifyContact":
        this.pkContactToModify = evt.value.pkIIdContact
        this.formContact.patchValue({
          VName: evt.value.vName,
          VContact: evt.value.vContact,
        })
        this.openModalContact()
        break;
      case "modifyPrivacyPolicies":
        this.pkLinkPrivacyPolicies = evt.value.idLinkPrivacyPoliciesFooter;
        this.getLinkPrivacyPoliciesFooterById(evt.value.idLinkPrivacyPoliciesFooter);
        this.openModalPrivacyPolicies();
        break;
      case "deletePrivacyPolicies":
        this._messageService
        .messageConfirmationAndNegation(
          this._translateService.instant('Delete') + '?',
          '',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            this.deleteLinkPrivacyPoliciesFooter(evt.value.idLinkPrivacyPoliciesFooter);
          }
        });
        break;
    
      default:
        break;
    }
  }

  openModalCategory(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this._currentModal = this._modalDialog.open(this.createEditCategory!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formCategory.reset();
      })
    })
  }
  openModalLink(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this._currentModal = this._modalDialog.open(this.createEditLink!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.pkLinkToModify = 0;
        this.formLink.reset();
      })
    })
  }
  openModalContact(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this._currentModal = this._modalDialog.open(this.createEditContact!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formContact.reset();
      })
    })
  }

  openModalPrivacyPolicies(){
    var sizeConfiguration = {
      disableClose: false,
      width: '50vw',
      maxHeight: '90vh',
    };
    this._currentModal = this._modalDialog.open(this.createEditPrivacyPolicies!, sizeConfiguration);
    this.formSubs = this._currentModal.beforeClosed().subscribe({
      next: ( _ => {
        this.formLinkPrivacyPolicies.reset();
        this.pkLinkPrivacyPolicies = 0;
      })
    })
  }

  async saveCategory(){
    if (this.formCategory.invalid)
      return this.formCategory.markAllAsTouched()
    let data = {
      ...this.formCategory.value,
      FkIIdBusinessByCountry: this.idBusinessByCountry,
      PkIIdCategory: this.pkCategoryToModify,
      BActive: true
    }
    if (this.pkCategoryToModify === 0)
    {
      this._copyrigthService.CreateModifyCategory(data).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadCategory()
            this._messageService.messageSuccessSimple(
              this._translateService.instant('DataSavedSuccessfully'),
            );
          }
        },
        error: (error) => {

          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );      
        }
      })
    }
    else{
      data.PkIIdCategory = this.pkCategoryToModify
      this._copyrigthService.CreateModifyCategory(
        data
      ).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadCategory()
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.CoverageModifiedSucessfully')
            );
          }
        },
        error: (error) => {
          console.log("error",error)

          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    }
    this.closeModal()
  }
  async saveLink(){
    if (this.formLink.invalid)
      return this.formLink.markAllAsTouched()
    let data = {
      ...this.formLink.value,
      IdBusinessCountry: this.idBusinessByCountry,
      BActive: true,
      PkIIdFooterLink : this.pkLinkToModify
    }
    if (this.pkLinkToModify === 0){
      this._copyrigthService.CreateModifyLink(data).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadCategory()
            this._messageService.messageSuccessSimple(
              this._translateService.instant('DataSavedSuccessfully'),
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );      
        }
      })
    }
    else
    {
      data.idLink= this.pkLinkToModify
      this._copyrigthService.CreateModifyLink(
        data
      ).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadCategory()
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.CoverageModifiedSucessfully')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    }
    this.closeModal()
  }
  async saveContact(){
    if (this.formContact.invalid)
      return this.formContact.markAllAsTouched()
    let data = {
      ...this.formContact.value,
      FkIIdBusinessByCountry: this.idBusinessByCountry,
      BActive: true,
      PkIIdContact: this.pkContactToModify
    }
    if (this.pkContactToModify === 0){
      this._copyrigthService.CreateModifyContacto(data).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadCategory();
            this._messageService.messageSuccessSimple(
              this._translateService.instant('DataSavedSuccessfully'),
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );      
        }
      })
    }
    else{
      data.idContact = this.pkContactToModify
      this._copyrigthService.CreateModifyContacto(
        data
      ).subscribe({
        next: (response) => {
          if (!response.error) {
            this._loadCategory();
            this._messageService.messageSuccess(
              this._translateService.instant('DataSavedSuccessfully'),
              this._translateService.instant('SectionClientPortal.CoverageModifiedSucessfully')
            );
          }
        },
        error: (error) => {
          this._messageService.messageInfo(
            this._translateService.instant('ThereWasAError'),
            error
          );
        }
      })
    }
    this.closeModal()
  }

  //Valida si se guarda o edita un link de aviso de seguridad.
  saveUpdateLinkPrivacyPolicies(){
    if(this.formLinkPrivacyPolicies.valid){
      const payload: LinkPrivacyPoliciesModel = {
        idPrivacyPoliciesFooter: this.pkPrivacyPolicies,
        idLinkPrivacyPoliciesFooter : this.pkLinkPrivacyPolicies,
        link: this.formLinkPrivacyPolicies.get('link')?.value,
        name: this.formLinkPrivacyPolicies.get('name')?.value,
        bActive: true,
      }
      this.pkLinkPrivacyPolicies === 0 ? this.createLinkPrivacyPoliciesFooter(payload) : this.updateLinkPrivacyPoliciesFooter(payload);
    }

  }

  closeModal() {
    if (this._currentModal) {
      this._currentModal.close();
      this._currentModal = null;
      this.formSubs?.unsubscribe();
    }
  }

  
  //Obtiene los avisos de privacidad configurados por empresa país.
  getAllPrivacyPoliciesFooter(idBusinessByCountry : number) {
    this._businessService
      .getAllPrivacyPoliciesFooter(idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          
        } else {
          if(resp.result.length > 0){
            this.pkPrivacyPolicies = resp.result[0].idPrivacyPoliciesFooter;
            this.titlePrivacyPolices = resp.result[0].title;
            this.subtitlePrivacyPolices = resp.result[0].subtitle;
            this.dataTablePrivacyPolices = resp.result[0].links;
          } else {
            this.pkPrivacyPolicies = 0;
            this.titlePrivacyPolices = '';
            this.subtitlePrivacyPolices = '';
            this.dataTablePrivacyPolices = [];
          }
        }
      });
  }

  //Obtiene un link de aviso de privacidad por medio de su idLinkPrivacyPoliciesFooter.
  getLinkPrivacyPoliciesFooterById(idLinkPrivacyPoliciesFooter: number) {
    this._businessService
      .getLinkPrivacyPoliciesFooterById(idLinkPrivacyPoliciesFooter)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.formLinkPrivacyPolicies.patchValue(resp.result);
        }
      });
  }

  //Crea un avisos de privacidad.
  createUpdatePrivacyPoliciesFooter() {
    if(this.titlePrivacyPolices){
      const payload: PrivacyPoliciesModel = {
        bActive: true,
        idBusinessByCountry: this.idBusinessByCountry,
        idPrivacyPoliciesFooter: this.pkPrivacyPolicies,
        title: this.titlePrivacyPolices,
        subtitle: this.subtitlePrivacyPolices,
        links: []
      }
      if(this.pkPrivacyPolicies === 0){
        this._businessService
        .createPrivacyPoliciesFooter(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            resp.result ? this.pkPrivacyPolicies = resp.result : this.pkPrivacyPolicies = 0;
            this._messageService.messageSuccess(
              this._translateService.instant('Saved'),
              ''
            );
          }
        });
      } else if(this.pkPrivacyPolicies > 0){
        this.updatePrivacyPoliciesFooter(payload);
      }
    }

  }

  //Crea un link de aviso de privacidad.
  createLinkPrivacyPoliciesFooter(payload: LinkPrivacyPoliciesModel) {
      this._businessService
        .createLinkPrivacyPoliciesFooter(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this.getAllPrivacyPoliciesFooter(this.idBusinessByCountry);
            this._modalDialog.closeAll();
            this._messageService.messageSuccess(
              this._translateService.instant('Saved'),
              ''
            );
          }
        });
  }

  //Actualiza un avisos de privacidad.
  updatePrivacyPoliciesFooter(model: PrivacyPoliciesModel) {
    this._businessService
      .updatePrivacyPoliciesFooter(model)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._messageService.messageSuccess(
            this._translateService.instant('Saved'),
            ''
          );
        }
      });
  }

  //Actualiza un link de aviso de privacidad.
  updateLinkPrivacyPoliciesFooter(payload: LinkPrivacyPoliciesModel) {
      this._businessService
        .updateLinkPrivacyPoliciesFooter(payload)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this.getAllPrivacyPoliciesFooter(this.idBusinessByCountry);
            this._modalDialog.closeAll();
            this._messageService.messageSuccess(
              this._translateService.instant('Saved'),
              ''
            );
          }
        });
  }

  //Elimina un link de aviso de privacidad.
  deleteLinkPrivacyPoliciesFooter(idLinkPrivacyPoliciesFooter: number) {
      this._businessService
        .deleteLinkPrivacyPoliciesFooter(idLinkPrivacyPoliciesFooter)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            this.getAllPrivacyPoliciesFooter(this.idBusinessByCountry);
            this._messageService.messageConfirmatio(
              this._translateService.instant('Deleted'),
              '',
              'success',
              this._translateService.instant('Continue')
            );
          }
    });
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }


}
