import { CommonModule } from '@angular/common';
import {
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabsModule } from '@angular/material/tabs';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { DpDatePickerModule } from 'ng2-date-picker';
import { catchError, debounceTime, of, Subscription } from 'rxjs';
import { ActionsToCreateComponent } from 'src/app/shared/components/actions-to-create/actions-to-create.component';
import { CalculationFormulasComponent } from 'src/app/shared/components/calculation-formulas/calculation-formulas.component';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { GenericImagePickerComponent } from 'src/app/shared/components/generic-image-picker/generic-image-picker.component';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { CatalogModel } from 'src/app/shared/models/catalog-setting';
import { SectionListTableModel } from 'src/app/shared/models/configuration-form';
import {
  CatalogCreate,
  FieldModelCreate,
  SectionModelCreate,
  TypeField,
} from 'src/app/shared/models/form/form.model';
import { RequestUpdateTabModel, TabModel } from 'src/app/shared/models/policy';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { FieldService } from 'src/app/shared/services/field/field.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { PolicyService } from 'src/app/shared/services/policy/policy.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { PreviewFormComponent } from '../../../products/edit-new-product/preview-form/preview-form.component';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-risk-data',
  standalone: true,
  imports: [
    CommonModule,
    MatSlideToggleModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatFormFieldModule,
    Modal2Component,
    TableComponent,
    CommonModule,
    MatRadioModule,
    MatTabsModule,
    GenericImagePickerComponent,
    ActionsToCreateComponent,
    ModalComponent,
    MatCheckboxModule,
    PreviewFormComponent,
    ChooseCountryAndCompanyComponent,
    PreventionSqlInjectorDirective,
    CalculationFormulasComponent,
    MatDatepickerModule,
    MatCardModule,
    MatNativeDateModule,
    DpDatePickerModule,
  ],
  templateUrl: './risk-data.component.html',
  styleUrls: ['./risk-data.component.scss'],
})
export class RiskDataComponent implements OnInit, OnDestroy {
  //variable formulario.
  formRiskData: FormGroup = new FormGroup({});

  formSection: FormGroup = this._fb.group({
    pkIIdSection: [0],
    b_Active: [true],
    v_Name: [null, [Validators.required]],
    fk_i_idTab: [null, Validators.required],
    i_Order: [null, Validators.required],
  });

  formField: FormGroup = this._fb.group({
    pkIIdField: 0,
    bActive: [true],
    vNameField: [null, [Validators.required]],
    fkIIdFieldType: [null, Validators.required],
    vNameFieldDb: [null, Validators.required],
    vDescription: [null, Validators.required],
    idTab: [null, Validators.required],
    idSection: [null, Validators.required],
    bRequired: [false],
    bIsEncrypted: [false],
    IsPolicy: [false],
    bIsPolicy: [false],
    bIsInPdf: [false],
    bAllowMultipleUploads: [false],
    bShowYear: [true],
    bShowMonth: [true],
    bIsSearch: [false],
    bIsPerson: [false],
    bShowDay: [true],
    bShowHour: [false],
    bShowCoin: [false],
    bIsGrouper: [false],
    bIsReadonly: [false],
    bIsUsingJSON: [false],
    iMinLength: [null],
    iMaxLength: [null],
    vHelpText: [null],
    vFormat: [null],
    options: [null],
    optionValues: this._fb.array([]),
    bHelpText: [false],
    bIsDependent: [false],
    iOrder: [null, [Validators.required]],
    fkIIdCatalog: [0],
    fkIIdFieldCatalog: [],
    selectedFileTypes: [],
    vEquivalentField: [null],
    fkIGrouperField: [null],
    iSearchType: [null],
    iOptionDependent: [0],
    fkIIdParent: [0],
    fkIIdChildrenDependent: [[]],
    fkIdFieldExistent: [],
    bUsingExistent: [],
    rowsRequest: this._fb.array([]),
    rowsResponse: this._fb.array([]),
    iTypeRequest: [],
    vEndpoint: [null],
    bIsHasDefaultValue: [false],
    bisKeyField: [false],
    vSetDefaultValue: [null],
    bIsEmail: [false],
    bIsMaxDateRequerid: [false],
    bIsMinDateRequerid: [false],
    bIsValueMax: [false],
    bIsValueMin: [false],
    dMaxDateRequerid: [null],
    dMinDateRequerid: [null],
    vValueMax: [null],
    vValueMin: [null],
    vTypeDateMin: [null],
    vTypeDateMax: [null],
    idForm: [null],
    bIsStandard: [null],
    bIsMultiple: [false]
  });

  idBusinessByCountry: number = 0;
  idCountry: number = 0;
  idForm: number = 0;
  idPolicy: number = 0;
  idPolicyType: number = 0;
  idTab: number = 0;
  tabList: TabModel[] = [];
  nameAnotherTab: string = '';
  orderList: number[] = [];
  orderListSection: number[] = [];
  orderListFields: number[] = [];
  catalogs: any[] = [];
  allFields: any[] = [];
  fieldTypes: any[] = [];
  selectedTabSections: any[] = [];
  fieldGroupers: any[] = [];
  nameColumnsCustomerTable: any[] = [];
  catalogTable: CatalogModel[] = [];
  catalogFields: any[] = [];
  showBeneficiary: boolean = false;
  showTabName: boolean = false;
  idFieldModule: number = 0;
  isEditingField: boolean = false;
  isEditingSection: boolean = false;
  editGeneral: boolean = false;
  standarField: boolean = false;
  standarSection: boolean = false;
  maxTextAreaLength: number = 2000;
  maxHelpTextLength: number = 100;
  maxTextOnlyLength: number = 50;
  typeDateMinFilter: any[] = [];
  typeDateMaxFilter: any[] = [];
  typeDate: any[] = [];
  maximumNumberBeneficiaries: number[] = Array.from(
    { length: 10 },
    (_, i) => i + 1
  );
  config: any = {};
  placeholderText: string = '';
  modeDatePicker: any = 'day';
  clockTypeIcon: boolean = false;
  typeFormatDate: any = [
    { format: 'YYYY-MM-DD' },
    { format: 'DD-MM-YYYY' },
    { format: 'MM-DD-YYYY' },
  ];

  typeFormatCoin: any = [
    { format: 'Pesos colombianos' },
    { format: 'Pesos mexicanos' },
    { format: 'Peso argentino' },
    { format: 'Dolar' },
    { format: 'Pesos chilenos' },
    { format: 'Colón costarricense' },
    { format: 'Córdoba de nicaragua' },
    { format: 'Real de Brasil' },
    { format: 'Guarani de paraguay ' },
    { format: 'Lempira hondureño' },
    { format: 'Quetzal de guatemala' },
  ];
  //Modals
  @ViewChild('editNewTabModal') editNewTabModal?: TemplateRef<any>;
  @ViewChild('editNewSectionModal') editSectionTabModal?: TemplateRef<any>;
  @ViewChild('editNewFieldModal') editNewFieldModal?: TemplateRef<any>;
  @ViewChild('previewForm') previewForm?: TemplateRef<any>;
  @ViewChild('typeUpload') typeUpload?: TemplateRef<any>;
  @ViewChild('copyParameters') copyParameters?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;

  //Subscription
  private _settingCountryAndCompanySubscription?: Subscription;
  private _policyDataSubscription?: Subscription;
  productSubs?: Subscription;
  formSubs?: Subscription;
  fieldSubs?: Subscription;
  productList?: Subscription;
  transactionSubs?: Subscription;

  //Variables Relacionadas con las tablas.
  dataTableSection: any[] = [];
  estructTableSections: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.SectionName'),
      columnValue: 'vName',
    },
    {
      columnLabel: this._translateService.instant('Product.Order'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Modify'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];
  estructTableFields: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('Product.FieldName'),
      columnValue: 'vNameField',
    },
    {
      columnLabel: this._translateService.instant('Product.Order'),
      columnValue: 'iOrder',
    },
    {
      columnLabel: this._translateService.instant('Product.Type'),
      columnValue: 'bIsStandard',
      functionValue: (item: any) => this.getFieldTypeTable(item.bIsStandard),
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'bActive',
      functionValue: (item: any) => this.utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
  ];

  nameColumnsPolicyTable: any[] = [];
  dataTableFields: any[] = [];
  constructor(
    private _translateService: TranslateService,
    private _msgSvc: MessageService,
    private _fb: FormBuilder,
    private _transactionService: TransactionService,
    private _policyService: PolicyService,
    private _fieldSvc: FieldService,
    private _catalogService: CatalogSettingService,
    public modalDialog: MatDialog,
    public utilsSvc: UtilsService,
    private _parametersService: ParametersService,
    private _activatedRoute: ActivatedRoute,
  ) {
    this.getBusinessByCountry();
  }

  ngOnInit(): void {
    this.initFormRiskData();
    this.getPolicyDataSubscription();
    this.getParameters();
    this.getFieldType();

    this.formField.get('vNameField')?.valueChanges.subscribe((value) => {
      const cleanedValue = this.cleanName(value);
      this.formField.get('vNameFieldDb')?.setValue(cleanedValue);
    });
    this.formField.get('fkIIdCatalog')?.valueChanges.subscribe((value) => {
      if (value != null) {
        this.getCatalogFieldByCatalogId();
      }
    });
    this.formField.get('bIsPolicy')?.valueChanges.subscribe((value) => {
      if (value != null) {
        this.getFieldsGrouperByForm(this.idForm, false);
      }
    });
    this.formField.get('bIsPerson')?.valueChanges.subscribe((value) => {
      if (value != null) {
        this.getCustomerColumnNames();
        this.getFieldsGrouperByForm(this.idForm, true);
      }
      this.togglePersonValidators(value);
    });
    this.formField.get('bUsingExistent')?.valueChanges.subscribe((value) => {
      if (value) {
        this.getAllFieldsPolicy();
      }
    });
    this.formField.get('fkIdFieldExistent')?.valueChanges.subscribe((value) => {
      if (value != null) {
        this.getFieldById(value, true);
      }
    });

    this.formField.get('bIsDependent')!.valueChanges.subscribe((value) => {
      if (value === false) {
        this.formField.get('fkIIdParent')?.setValue(0);
        this.formField.get('fkIIdParent')?.clearValidators();
        this.formField.get('fkIIdParent')?.updateValueAndValidity();

        this.formField.get('iOptionDependent')?.setValue(null);
        this.formField.get('iOptionDependent')?.updateValueAndValidity();
      }
    });

    this.formField.get('fkIIdParent')?.valueChanges.subscribe((value) => {
      this.catalogs = [];
      if (value != null) {
        if (this.formField.get('bIsDependent')?.value === false) {
          this.formField.get('iOptionDependent')?.setValue(null);
          this.formField.get('iOptionDependent')?.clearValidators();
          this.formField.get('iOptionDependent')?.updateValueAndValidity();
        }

        if (this.formField.get('bIsDependent')?.value)
          this.getFieldById(value, false);
      }
    });
    this.formField.get('bIsSearch')!.valueChanges.subscribe((value) => {
      this.toggleSearchValidators(value);
    });
    this.formField.get('iSearchType')!.valueChanges.subscribe((value) => {
      if (this.formField.get('bIsSearch')!.value) {
        this.updateValidatorsBasedOnSearchType(value);
      }
    });
    this.formField.get('bIsGrouper')?.valueChanges.subscribe((value) => {
      if (value == true || value === null) {
        this.formField.get('fkIGrouperField')?.clearValidators();
        this.formField.get('fkIGrouperField')?.updateValueAndValidity();
      } else if (value == false) {
        this.formField
          .get('fkIGrouperField')
          ?.setValidators([Validators.required]);
        this.formField.get('fkIGrouperField')?.updateValueAndValidity();
      }
    });

    this.formField.get('fkIIdFieldType')?.valueChanges.subscribe((value) => {
      if (value != null) {
        if (value !== this.typeField.Alphanumeric)
          this.formField.get('bIsEmail')?.setValue(null);

        if (
          value !== this.typeField.Alphanumeric &&
          value !== this.typeField.Numeric &&
          value !== this.typeField.Text &&
          value !== this.typeField.Date &&
          value !== this.typeField.DropDownList
        ) {
          this.formField.get('bIsSearch')?.setValue(false);
        }
        if (value === this.typeField.DropDownList) {
          this.formField
            .get('fkIIdCatalog')
            ?.setValidators([Validators.required]);
          this.formField
            .get('fkIIdFieldCatalog')
            ?.setValidators([Validators.required]);
          this.formField.get('fkIIdCatalog')?.updateValueAndValidity();
          this.formField.get('fkIIdFieldCatalog')?.updateValueAndValidity();
        } else {
          this.formField.get('fkIIdCatalog')?.clearValidators();
          this.formField.get('fkIIdFieldCatalog')?.clearValidators();
          this.formField.get('fkIIdCatalog')?.updateValueAndValidity();
          this.formField.get('fkIIdFieldCatalog')?.updateValueAndValidity();
        }
      }
    });

    this.formField.get('bIsEmail')?.valueChanges.subscribe((value) => {
      if (value != null) {
        if (value) {
          this.formField.get('bIsValueMax')?.disable();
          this.formField.get('bIsValueMax')?.setValue(false);

          this.formField.get('bIsValueMin')?.disable();
          this.formField.get('bIsValueMin')?.setValue(false);
        } else {
          this.formField.get('bIsValueMax')?.enable();
          this.formField.get('vValueMax')?.enable();

          this.formField.get('bIsValueMin')?.enable();
          this.formField.get('vValueMin')?.enable();
        }
      }
    });

    this.formField.get('bIsValueMax')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formField.get('vValueMax')?.enable();
        this.formField.get('vValueMax')?.setValidators(Validators.required);
        this.formField.get('vValueMax')?.updateValueAndValidity();
      } else {
        this.formField.get('vValueMax')?.disable();
        this.formField.get('vValueMax')?.setValue('');
        this.formField.get('vValueMax')?.setValidators(null);
        this.formField.get('vValueMax')?.updateValueAndValidity();
      }
    });

    this.formField.get('bIsValueMin')?.valueChanges.subscribe((value) => {
      if (value) {
        this.formField.get('vValueMin')?.enable();
        this.formField.get('vValueMin')?.setValidators(Validators.required);
        this.formField.get('vValueMin')?.updateValueAndValidity();
      } else {
        this.formField.get('vValueMin')?.disable();
        this.formField.get('vValueMin')?.setValue('');
        this.formField.get('vValueMin')?.setValidators(null);
        this.formField.get('vValueMin')?.updateValueAndValidity();
      }
    });

    this.formField
      .get('bIsMinDateRequerid')
      ?.valueChanges.subscribe((value) => {
        if (value) {
          this.formField.get('dMinDateRequerid')?.enable();
          this.formField
            .get('dMinDateRequerid')
            ?.setValidators(Validators.required);
          this.formField.get('dMinDateRequerid')?.updateValueAndValidity();

          this.formField.get('vTypeDateMin')?.enable();
        } else {
          this.formField.get('vTypeDateMin')?.disable();
          this.formField.get('vTypeDateMin')?.setValue(0);
          this.typeDateMaxFilter = this.typeDate;
          this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
          this.validationTypeDate(
            this.formField.get('bShowYear')?.value,
            this.formField.get('bShowMonth')?.value,
            this.formField.get('bShowDay')?.value,
            this.formField.get('bShowHour')?.value
          );
        }
      });

    this.formField
      .get('bIsMaxDateRequerid')
      ?.valueChanges.subscribe((value) => {
        if (value) {
          this.formField.get('dMaxDateRequerid')?.enable();
          this.formField
            .get('dMaxDateRequerid')
            ?.setValidators(Validators.required);
          this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();

          this.formField.get('vTypeDateMax')?.enable();
        } else {
          this.formField.get('vTypeDateMax')?.disable();
          this.formField.get('vTypeDateMax')?.setValue(0);
          this.formField.get('dMaxDateRequerid')?.disable();
          this.formField.get('dMaxDateRequerid')?.setValue('');
          this.formField.get('dMaxDateRequerid')?.setValidators(null);
          this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();
        }
      });

    this.formField.get('bShowYear')?.valueChanges.subscribe((value) => {
      this.cleanFieldDate();
      if (value) {
        this.validationTypeDate(
          true,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      } else {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      }
    });

    this.formField.get('bIsGrouper')?.valueChanges.subscribe((value) => {
      // Forzar la reevaluación de la validez del campo
      if (value === true) {
        this.formField.get('fkIGrouperField')?.clearValidators();
        this.formField.get('fkIGrouperField')?.disable;
        this.formField.get('fkIGrouperField')?.updateValueAndValidity();
      } else if (value === false) {
        this.formField
          .get('fkIGrouperField')
          ?.setValidators([Validators.required]);
        this.formField.get('fkIGrouperField')?.updateValueAndValidity();
      }
    });

    this.formField.get('bShowMonth')?.valueChanges.subscribe((value) => {
      this.cleanFieldDate();
      if (value) {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          true,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      } else {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      }
    });

    this.formField.get('bShowDay')?.valueChanges.subscribe((value) => {
      this.cleanFieldDate();
      if (value) {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          true,
          this.formField.get('bShowHour')?.value
        );
      } else {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      }
    });

    this.formField.get('bShowHour')?.valueChanges.subscribe((value) => {
      this.cleanFieldDate();
      if (value) {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          true
        );
      } else {
        this.validationTypeDate(
          this.formField.get('bShowYear')?.value,
          this.formField.get('bShowMonth')?.value,
          this.formField.get('bShowDay')?.value,
          this.formField.get('bShowHour')?.value
        );
      }
    });

    this.formField.get('dMaxDateRequerid')?.valueChanges.subscribe((value) => {
      if (value != null) this.isDateInvalid(false);
    });

    this.formField.get('dMinDateRequerid')?.valueChanges.subscribe((value) => {
      if (value != null) this.isDateInvalid(true);
    });

    this.formField.get('vTypeDateMin')?.valueChanges.subscribe((value) => {
      const selectedValue = this.formField.get('vTypeDateMin')?.value;
      this.typeDateMaxFilter = this.typeDate.filter(
        (item) => item.Name === selectedValue
      );
      this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
    });

    this.formField.get('vTypeDateMax')?.valueChanges.subscribe((value) => {
      this.filterFieldTypeDateMax();
    });

    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTableSections[0].columnLabel = this._translateService.instant(
        'Product.SectionName'
      );
      this.estructTableSections[1].columnLabel =
        this._translateService.instant('Product.Order');
      this.estructTableSections[2].columnLabel =
        this._translateService.instant('Status');
      this.estructTableSections[3].columnLabel =
        this._translateService.instant('Modify');

      this.estructTableFields[0].columnLabel =
        this._translateService.instant('Product.FieldName');
      this.estructTableFields[1].columnLabel =
        this._translateService.instant('Product.Order');
      this.estructTableFields[2].columnLabel =
        this._translateService.instant('Product.Type');
      this.estructTableFields[3].columnLabel =
        this._translateService.instant('Status');
      this.estructTableFields[4].columnLabel =
        this._translateService.instant('Modify');
    });
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
      }
      if (params.idCountry) {
        this.idCountry = Number(params.idCountry);
      }
    });
  }

  getPolicyDataSubscription() {
    this._policyDataSubscription =
      this._policyService.currentpolicyData.subscribe({
        next: (response) => {
          if (!(Object.keys(response).length === 0)) {
            this.idPolicy = response.idPolicy;
            this.idPolicyType = response.idPolicyType;
            this.idForm = response.idForm;
            if (this.idForm > 0) {
              this.getTabsListByPolicyFormId(this.idForm, false);
            }
          }
        },
      });
  }

  initFormRiskData() {
    this.formRiskData = this._fb.group({
      tab: [null, [Validators.required]],
      maximumNumberBenefici: [null],
      nameTab: [null],
    });
    this.formRiskData.get('tab')?.valueChanges.subscribe({
      next: (tab: TabModel) => {
        this.idTab = tab.pkIIdTab;
        this.showBeneficiary = false;
        this.showTabName = false;
        this.formRiskData.get('maximumNumberBenefici')?.setValue(null);
        this.formRiskData.get('nameTab')?.setValue(null);
        this.getSectionByTab(tab.pkIIdTab);
        this.getFieldsByTabId(tab.pkIIdTab);
        if (tab) {
          switch (tab.vName) {
            case 'Asegurado':
              break;
            case 'Beneficiario':
              this.getBeneficiariesNumberByIdPolicy(this.idPolicy);
              this.showBeneficiary = true;
              this.formRiskData.get('maximumNumberBenefici')?.clearValidators();
              this.formRiskData.get('nameTab')?.clearValidators();
              this.formRiskData
                .get('maximumNumberBenefici')
                ?.setValidators(Validators.required);
              this.formRiskData
                .get('maximumNumberBenefici')
                ?.updateValueAndValidity();
              this.formRiskData.get('nameTab')?.updateValueAndValidity();
              break;
            case 'Tomador':
              break;
            case 'Otros':
              this.showTabName = true;
              this.formRiskData.get('maximumNumberBenefici')?.clearValidators();
              this.formRiskData.get('nameTab')?.clearValidators();
              this.formRiskData
                .get('nameTab')
                ?.setValidators(Validators.required);
              this.formRiskData.get('nameTab')?.updateValueAndValidity();
              this.formRiskData
                .get('maximumNumberBenefici')
                ?.updateValueAndValidity();
              this.formRiskData.get('nameTab')?.setValue(this.nameAnotherTab);
              break;

            default:
              this.showTabName = true;
              this.formRiskData.get('maximumNumberBenefici')?.clearValidators();
              this.formRiskData.get('nameTab')?.clearValidators();
              this.formRiskData
                .get('nameTab')
                ?.setValidators(Validators.required);
              this.formRiskData.get('nameTab')?.updateValueAndValidity();
              this.formRiskData
                .get('maximumNumberBenefici')
                ?.updateValueAndValidity();
              this.formRiskData.get('nameTab')?.setValue(this.nameAnotherTab);
              break;
          }
        }
      },
    });

    this.formField
      .get('iMaxLength')
      ?.valueChanges.pipe(debounceTime(800))
      .subscribe((value) => {
        if (value != null) {
          if (value && this.formField.get('iMinLength')?.value) {
            if (value <= this.formField.get('iMinLength')?.value) {
              this.formField.get('iMaxLength')?.setValue(null);
              this._msgSvc.messageInfo(
                'Warning',
                'El valor máximo, no puede ser igual o inferior al valor mínimo'
              );
            }
          }
        }
      });
    this.formField
      .get('iMinLength')
      ?.valueChanges.pipe(debounceTime(800))
      .subscribe((value) => {
        if (value != null) {
          if (value && this.formField.get('iMaxLength')?.value) {
            if (value >= this.formField.get('iMaxLength')?.value) {
              this.formField.get('iMinLength')?.setValue(null);
              this._msgSvc.messageInfo(
                'Warning',
                'El valor mínimo, no puede ser igual o mayor al valor máximo'
              );
            }
          }
        }
      });
  }

  private get formFieldValue(): FieldModelCreate {
    return this.formField.getRawValue();
  }

  /// Function that performs validation between dates
  isDateInvalid(isDateMin: boolean) {
    let horaMin: number = 0;
    let horaMax: number = 0;
    let isHour: boolean = false;
    const vTypeDateMinValue = this.formField.get('vTypeDateMin')?.value;
    const minDate = this.formField.get('dMinDateRequerid')?.value;
    const maxDate = this.formField.get('dMaxDateRequerid')?.value;

    /// If the type is time, convert to seconds
    if (
      vTypeDateMinValue != null &&
      vTypeDateMinValue != '' &&
      vTypeDateMinValue != 0
    )
      if (vTypeDateMinValue.includes('hora')) {
        horaMin = this.timeToSeconds(minDate);
        horaMax = this.timeToSeconds(maxDate);
        isHour = true;
      }

    /// If the variable isMindate is true, it is valid for the minimum date field, if not, it is valid for the maximum date.
    if (isDateMin) {
      /// If the time variable is true, it validates by hour, if not, it validates by date, month, day, etc...
      if (isHour) {
        if (horaMin > horaMax) {
          this.formField
            .get('dMinDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMinDateRequerid')?.setErrors(null);
        }
      } else {
        if (Date.parse(minDate) > Date.parse(maxDate)) {
          this.formField
            .get('dMinDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMinDateRequerid')?.setErrors(null);
        }
      }
    } else {
      /// If the time variable is true, it validates by hour, if not, it validates by date, month, day, etc...
      if (isHour) {
        if (horaMax < horaMin) {
          this.formField
            .get('dMaxDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMaxDateRequerid')?.setErrors(null);
        }
      } else {
        if (Date.parse(maxDate) < Date.parse(minDate)) {
          this.formField
            .get('dMaxDateRequerid')
            ?.setErrors({ dateRangeInvalid: true });
        } else {
          this.formField.get('dMaxDateRequerid')?.setErrors(null);
        }
      }
    }
  }

  cleanName(name: string): string {
    // Limpiar el nombre: eliminar tildes, espacios, etc.
    let cleanedName = name?.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    cleanedName = cleanedName?.replace(/\s+/g, '');

    // Agregar 'DB' al final del nombre
    cleanedName += 'DB';

    return cleanedName;
  }

  timeToSeconds(time: string): number {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  }

  cleanFieldDate() {
    this.formField.get('vTypeDateMin')?.setValue(null);
    this.formField.get('dMaxDateRequerid')?.setValue('');
    this.formField.get('dMaxDateRequerid')?.disable();
    this.formField.get('dMaxDateRequerid')?.setValidators(null);
    this.formField.get('dMaxDateRequerid')?.updateValueAndValidity();

    this.formField.get('vTypeDateMax')?.setValue(null);
    this.formField.get('dMinDateRequerid')?.setValue('');
    this.formField.get('dMinDateRequerid')?.disable();
    this.formField.get('dMinDateRequerid')?.setValidators(null);
    this.formField.get('dMinDateRequerid')?.updateValueAndValidity();
  }

  /// Function where we validate the selected check and thus filter the array for the field
  validationTypeDate(
    bShowYear: boolean,
    bShowMonth: boolean,
    bShowDay: boolean,
    bShowHour: boolean
  ) {
    let filters: string[] = [];
    this.typeDateMinFilter = [];
    if (bShowYear) filters.push('año');
    if (bShowMonth) filters.push('mes');
    if (bShowDay) filters.push('día');
    if (bShowHour) filters.push('horas');

    /// A filter is made depending on the check or the marked checks.
    this.typeDateMinFilter = this.typeDate.filter((item) => {
      return filters.some((prop) => item.Name.toLowerCase().includes(prop));
    });

    /// It is validated if the filter array has data if it does not leave the list as it was initially
    if (filters.length == 0) {
      this.typeDateMinFilter = this.typeDate;
    }

    /// We refresh the field array
    this.typeDateMinFilter = this.typeDateMinFilter.slice();
    this.typeDateMaxFilter = this.typeDateMinFilter;
    this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
  }

  get validFormRiskData(): boolean {
    return this.formRiskData.valid;
  }

  get orderItems() {
    return this.formField.controls['optionValues'] as FormArray;
  }

  get rowsRequest() {
    return (this.formField.get('rowsRequest') as FormArray).controls;
  }
  get rowsResponse() {
    return (this.formField.get('rowsResponse') as FormArray).controls;
  }

  generateSchemaRow(flag: boolean): FormGroup {
    return this._fb.group({
      fkIIdField: 0,
      vEquivalentField: '',
      bIsForFieldEndpoint: true,
      bIsForResponse: flag,
    });
  }

  public get typeField(): typeof TypeField {
    return TypeField;
  }

  resetFieldSetDefaultValue() {
    this.formField.get('vSetDefaultValue')?.setValue(null);
  }

  private get formValue(): TabModel {
    return this.formRiskData.getRawValue();
  }

  getColumnNamesPolicy() {
    this.transactionSubs = this._transactionService
      .getColumnNamesPolicy()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.nameColumnsPolicyTable = resp.result;
          }
        }
      });
  }

  EditField(isEditing: boolean, evt: IconEventClickModel) {
    this.open('editNewFieldModal');
    this.getColumnNamesPolicy();
    if (isEditing) {
      this.getFieldById(evt?.value.pkIIdField, true);
      this.idFieldModule = evt?.value.pkIIdField;
    }
    this.isEditingField = true;
    this.setOrder(isEditing, 'field');

  }

  EditSection(evt: IconEventClickModel) {
    this.isEditingSection = true;
    this.open('editNewSectionModal');
    this.formSection.patchValue({
      pkIIdSection: evt.value.pkIIdSection,
      b_Active: evt.value.bActive,
      v_Name: evt.value.vName,
      fk_i_idTab: evt.value.fkIIdTab,
      i_Order: evt.value.iOrder,
    });
    if (evt.value.vName.includes('Datos básicos')) {
      this.standarSection = true;
    } else {
      this.standarSection = false;
    }
  }

  open(component: string) {
    var sizeConfiguration = {
      disableClose: false,
      width: '70vw',
      maxHeight: '90vh',
    };
    var modal: TemplateRef<any>;
    switch (component) {
      case 'editNewSectionModal':
        this.setOrder(this.isEditingSection, 'section')
        this.formSection.reset();
        this.formSection.patchValue({ b_Active: true });
        this.formSection.get('fk_i_idTab')?.setValue(this.idTab);
        this.formSection.get('fk_i_idTab')?.disable();
        modal = this.editSectionTabModal!;
        break;
      case 'editNewFieldModal':
        this.formField.get('vNameField')?.enable();
        this.formField.get('fkIIdFieldType')?.enable();
        this.formField.get('bIsPerson')?.enable();
        this.setOrder(false, 'field')
        this.formField.reset();
        this.clearFormArray('rowsRequest');
        this.clearFormArray('rowsResponse');
        this.isEditingField = false;
        this.formField.patchValue({
          bActive: true,
          selectedFileTypes: [],
          optionValues: [],
        });
        //It's neccesary force clear to optionValues
        var renew = this.formField.get('optionValues') as FormArray;
        renew.clear();
        modal = this.editNewFieldModal!;
        this.formField.get('idTab')?.setValue(this.idTab);
        this.formField.get('idTab')?.disable();
        break;
      case 'previewForm':
        modal = this.previewForm!;
        sizeConfiguration.width = '90vw';
        break;
      case 'typeUpload':
        modal = this.typeUpload!;
        sizeConfiguration.width = '50vw';

        break;
      case 'copyParameters':
        modal = this.copyParameters!;
        break;
      default:
        return;
    }

    // Abre el modal y guarda su referencia en la propiedad currentModal
    this.currentModal = this.modalDialog.open(modal, sizeConfiguration);
  }

  closeModal() {
    if (this.currentModal) {
      this.currentModal.close();
      this.currentModal = null; // Limpia la referencia después de cerrar el modal
      this.formField.get('vNameField')?.enable();
      this.formField.get('fkIIdFieldType')?.enable();
      this.formField.get('bIsPerson')?.enable();
    }
    this.isEditingField = false;
    this.isEditingSection = false;
  }

  //Función que obtiene las unidades de medidas.
  getParameters() {
    this._parametersService
      .getParameters('Type_Date')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.typeDate = resp;
          this.typeDateMinFilter = resp;
          this.typeDateMaxFilter = resp;
        } else {
        }
      });
  }

  getFieldsGrouperByForm(idForm: number, isPerson: boolean) {
    this.fieldSubs = this._fieldSvc
      .getFieldsGrouperByFormId(idForm, isPerson)
      .pipe(
        catchError((excepception) => {
          if (excepception.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              excepception.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.fieldGroupers = resp.result;
          }
        }
      });
  }

  getCustomerColumnNames() {
    this.transactionSubs = this._transactionService
      .getColumnNamesCustomer()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.nameColumnsCustomerTable = resp.result;
            const selectedTab: TabModel = this.formRiskData.get('tab')?.value;
            if (selectedTab.vName === 'Beneficiario') {
              this.nameColumnsCustomerTable.push('d_percentage');
            }
          }
        }
      });
  }

  //Obtiene la lista de pestañas asociadas al formulario.
  getTabsListByPolicyFormId(idForm: number, updateOtherTab: boolean) {
    this._fieldSvc
      .getTabsListByPolicyFormId(idForm)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.tabList = resp.result;
          if (this.tabList[this.tabList.length - 1].vName) {
            if (this.tabList[this.tabList.length - 1].vName !== 'Otros') {
              this.nameAnotherTab = this.tabList[this.tabList.length - 1].vName;
              this.tabList[this.tabList.length - 1].vName = 'Otros';
            }
          }
          if (updateOtherTab) {
            this.formRiskData
              .get('tab')
              ?.setValue(this.tabList[this.tabList.length - 1]);
          }
        }
      });
  }

  getFieldById(idField: Number, bIsForPatching: boolean) {
    this.fieldSubs = this._fieldSvc
      .getFieldById(idField)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            var temp = resp.result;
            this.standarField = resp.result.bIsStandard || false;
            this.standardTypeFieldValidations(resp.result);
            if (bIsForPatching) {
              if (temp.fkIIdChildrenDependent != null) {
                temp.fkIIdChildrenDependent = JSON.parse(
                  temp.fkIIdChildrenDependent
                );
              }
              if (temp.fkIIdFieldType === this.typeField.Radio) {
                this.getCatalogById(temp, true);
              }

              if (temp.optionValues) {
                const optionValuesFormArray = this.formField.get(
                  'optionValues'
                ) as FormArray;
                optionValuesFormArray.clear();
                temp.optionValues = JSON.parse(temp.optionValues);
                temp.options = temp.optionValues.length;
                this.formField.patchValue(temp);

                temp.optionValues.forEach((value: any) => {
                  optionValuesFormArray.push(
                    new FormControl(value, Validators.required)
                  );
                  setTimeout(() => {
                    const inputField = document.getElementById(
                      `optionInput-${value.Id}`
                    ) as HTMLInputElement;
                    // Asignar el valor actualizado al campo de entrada
                    if (inputField) {
                      inputField.value = value.Value;
                    }
                  });
                });
                this.formField.setControl(
                  'optionValues',
                  optionValuesFormArray
                );
              }
              if (temp.rowsRequest != null || temp.rowsResponse != null) {
                const rowsRequest = this.formField.get(
                  'rowsRequest'
                ) as FormArray;
                const rowsResponse = this.formField.get(
                  'rowsResponse'
                ) as FormArray;
                rowsRequest.clear();
                rowsResponse.clear();
                temp.rowsRequest.forEach((value: any) => {
                  rowsRequest.push(this._fb.group(value));
                });
                temp.rowsResponse.forEach((value: any) => {
                  rowsResponse.push(this._fb.group(value));
                });

                this.formField.patchValue(temp);
                this.formField.setControl('rowsRequest', rowsRequest);
                this.formField.setControl('rowsResponse', rowsResponse);
              } else {
                this.formField.patchValue(temp);
                if (
                  this.formField.get('fkIIdFieldType')?.value ==
                  this.typeField.UploadDocuments
                ) {
                  this.formField
                    .get('selectedFileTypes')
                    ?.setValue(
                      this.formField
                        .get('vFormat')
                        ?.value.replace(' ', '')
                        .split(',')
                    );
                }
              }
              this.formField.get('idTab')?.setValue(this.idTab);
              this.getSectionByTab(this.idTab);
            } else {
              //When it's not neccesary patch the info just for consulting.
              if (temp.fkIIdFieldType == this.typeField.Radio) {
                this.getCatalogById(temp, false);
              }
              if (temp.fkIIdFieldType == this.typeField.DropDownList) {
                this.getCatalogByIdForDropdown(temp);
              }
            }
          }
        }
      });
  }

  getAllFieldsPolicy() {
    this._fieldSvc
      .getAllFieldsPolicy(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
          } else {
            this.allFields = response.result;
          }
        }
      });
  }

  getListCatalog(idBusinessCountry: number, idCountry: number) {
    this._catalogService
      .getAllCatalogByCountryAndBusiness(idBusinessCountry, idCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.catalogTable = response.result;
          }
        }
      });
  }
  getCatalogFieldByCatalogId() {
    this._catalogService
      .getCatalogBasicInfoFieldByCatalogId(
        this.formField.get('fkIIdCatalog')?.value
      )
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((response: ResponseGlobalModel | never[]) => {
        if (Array.isArray(response)) {
        } else {
          if (response.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.catalogFields = response.result;
          }
        }
      });
  }

  getCatalogById(field: any, bIsForPatching: boolean) {
    //Catalogs for radio button.
    this.fieldSubs = this._fieldSvc
      .getCatalogById(field.fkIIdCatalog)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            if (bIsForPatching) {
              const optionValuesFormArray = this.formField.get(
                'optionValues'
              ) as FormArray;
              optionValuesFormArray.clear();
              field.optionValues = JSON.parse(resp.result.vJson);
              field.options = field.optionValues.length;
              this.formField.patchValue(field);

              field.optionValues.forEach((value: any) => {
                optionValuesFormArray.push(
                  new FormControl(value, Validators.required)
                );
                setTimeout(() => {
                  const inputField = document.getElementById(
                    `optionInput-${value.Id}`
                  ) as HTMLInputElement;
                  // Asignar el valor actualizado al campo de entrada
                  if (inputField) {
                    inputField.value = value.Value;
                  }
                });
              });
              this.formField.setControl('optionValues', optionValuesFormArray);
            } else {
              this.catalogs = JSON.parse(resp.result.vJson.toLowerCase());
            }
          }
        }
      });
  }

  getCatalogByIdForDropdown(field: any) {
    //Catalogs for dropdown.
    this.fieldSubs = this._catalogService
      .getCatalogFieldById(field.fkIIdFieldCatalog)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.catalogs = resp.result.vJson;
          }
        }
      });
  }

  getFieldType() {
    this.fieldSubs = this._fieldSvc
      .getFieldType()
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.fieldTypes = resp.result;
          }
        }
      });
  }

  clearFormArray(key: string) {
    const formArray = this.formField.get(key) as FormArray;
    formArray.clear();
  }

  saveSection() {
    //Create a tab by form
    if (this.formSection.valid) {
      var caller;
      var section: SectionModelCreate = this.formSection.getRawValue();
      if (this.isEditingSection) {
        caller = this._fieldSvc.updateSection(section);
      } else {
        caller = this._fieldSvc.createSection(section);
      }

      this.fieldSubs = caller
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.getSectionByTab(this.idTab);
              this._msgSvc.messageSuccess(
                '',
                this._translateService.instant('Saved')
              );
              this.closeModal();
            }
          }
        });
    }
  }

  async saveField() {
    var idCatalog: number = 0;
    var caller;
    Object.keys(this.formField.controls).forEach((key) => {
      const control = this.formField.get(key);
      if (control!.invalid) {
      }
    });
    //Create a field by form
    if (this.formField.valid) {

      debugger;
      if (this.formField.value.iOptionDependent === 0) {
        this.formField.patchValue({
          iOptionDependent: null
        });
      }
      var field: FieldModelCreate = this.formFieldValue;
      const selectedTab: TabModel = this.formRiskData.get('tab')?.value;
      field.idForm = this.idForm;


      if (
        !field.bIsStandard &&
        !field.vNameField
          .toLocaleLowerCase()
          .includes(selectedTab.vName.toLocaleLowerCase())
      ) {
        if(this.formRiskData.get('tab')?.value.vName === 'Otros'){
          if(!(field.vNameField.toLocaleLowerCase().includes(this.formRiskData.get('nameTab')?.value.toLocaleLowerCase()))){
            field.vNameField = `${
              field.vNameField
            } ${this.formRiskData.get('nameTab')?.value.toLocaleLowerCase()}`;
          }
        } else {
          field.vNameField = `${
            field.vNameField
          } ${selectedTab.vName.toLocaleLowerCase()}`;
          field.vNameFieldDb = `${ field.vNameFieldDb.slice(0, -2) }${selectedTab.vName.toLocaleLowerCase()}DB`;
        }
      }
      if (this.isEditingField) {
        caller = this._fieldSvc.updateField(field);
      } else {
        caller = this._fieldSvc.createField(field);
        if (this.formField.get('fkIIdFieldType')?.value === TypeField.Radio) {
          idCatalog = await this.saveCatalog();
          if (idCatalog === 0) {
            return;
          }
          field.fkIIdCatalog = idCatalog;
        }
      }
      this.fieldSubs = caller
        .pipe(
          catchError((error) => {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._msgSvc.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.validationTabRulesField(resp.result);
            }
          }
        });
    } else {
      this.formField.markAllAsTouched();
    }
  }

  async saveCatalog(): Promise<number> {
    try {
      const catalog: CatalogCreate = {
        vDescription: this.formField.get('vDescription')?.value,
        vJson: JSON.stringify(this.formField.get('optionValues')?.value),
      };

      const resp: ResponseGlobalModel | undefined = await this._fieldSvc
        .createCatalog(catalog)
        .toPromise();

      if (!resp || resp.error) {
        this._msgSvc.messageError(
          this._translateService.instant('ThereWasAError') +
            ': ' +
            (resp?.message || this._translateService.instant('ThereWasAError'))
        );
        return 0;
      }

      return resp.result;
    } catch (error) {
      console.error('Ocurrió un error:', error);
      return 0;
    }
  }

  validationTabRulesField(idField: number) {
    this.formField.get('pkIIdField')?.setValue(idField);
    if (!this.isFieldDisabledTabRuleField()) {
      this.getFieldsByTabId(this.idTab);
      this._msgSvc.messageSuccess('', this._translateService.instant('Saved'));
      return;
    }

    if (this.isEditingField) {
      this.getFieldsByTabId(this.idTab);
      this._msgSvc.messageSuccess('', this._translateService.instant('Saved'));
    } else {
      this._msgSvc
        .messageConfirmationAndNegationReverseButton(
          this._translateService.instant('¿Deseas agregar reglas del campo?'),
          '',
          'warning',
          this._translateService.instant('Cancel'),
          this._translateService.instant('Confirm')
        )
        .then((result) => {
          if (!result) {
            this.getFieldsByTabId(this.idTab);
            this._msgSvc.messageSuccess(
              '',
              this._translateService.instant('Saved')
            );
          } else {
            this.isFieldDisabledTabRuleField();
            this.idFieldModule = idField;
            this.isEditingField = true;
          }
        });
    }
  }

  isFieldDisabledTabRuleField(): boolean {
    const fkIIdFieldTypeValue = this.formField.get('fkIIdFieldType')?.value;
    const pkIIdFieldValue = this.formField.get('pkIIdField')?.value;
    return (
      (fkIIdFieldTypeValue === this.typeField.Alphanumeric ||
        fkIIdFieldTypeValue === this.typeField.Numeric ||
        fkIIdFieldTypeValue === this.typeField.Text) &&
      pkIIdFieldValue > 0
    );
  }

  deleteSection() {
    if (this.formSection.valid) {
      this._msgSvc
        .messageConfirmationAndNegation(
          'Está a punto  de eliminar esta sección',
          'Esto puede afectar el funcionamiento del sistema',
          'info',
          'Confirmar',
          'Cancelar'
        )
        .then((response) => {
          if (response) {
            this.fieldSubs = this._fieldSvc
              .deleteSection(this.formSection.get('pkIIdSection')?.value)
              .pipe(
                catchError((error) => {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('Warning'),
                    error.error.message
                  );
                  return of([]);
                })
              )
              .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                } else {
                  if (resp.error) {
                    this._msgSvc.messageError(
                      this._translateService.instant('ThereWasAError') +
                        resp.message
                    );
                  } else {
                    this._msgSvc.messageSuccess(
                      'Confirmado',
                      'Sección eliminada exitosamente'
                    );
                    this.getSectionByTab(this.idTab);
                    this.closeModal();
                  }
                }
              });
          }
        });
    }
  }

  deleteField() {
    if (this.formField.valid) {
      this._msgSvc
        .messageConfirmationAndNegation(
          'Está a punto  de eliminar este campo',
          'Esto puede afectar el funcionamiento del sistema',
          'info',
          'Confirmar',
          'Cancelar'
        )
        .then((response) => {
          if (response) {
            this.fieldSubs = this._fieldSvc
              .deleteField(this.formField.get('pkIIdField')?.value)
              .pipe(
                catchError((error) => {
                  this._msgSvc.messageWaring(
                    this._translateService.instant('Warning'),
                    error.error.message
                  );
                  return of([]);
                })
              )
              .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                } else {
                  if (resp.error) {
                    this._msgSvc.messageError(
                      this._translateService.instant('ThereWasAError') +
                        resp.message
                    );
                  } else {
                    this._msgSvc.messageSuccess(
                      'Confirmado',
                      'Campo eliminado exitosamente'
                    );
                    this.getFieldsByTabId(this.idTab);
                  }
                }
              });
          }
        });
    }
  }

  getFieldsByTabId(idTab: Number) {
    this.getListCatalog(this.idBusinessByCountry, this.idCountry);
    this.fieldSubs = this._fieldSvc
      .getFieldsByTabId(idTab)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this.orderListFields = this.utilsSvc.generarArrayOrderList(1);
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataTableFields = resp.result;
            this.modalDialog.closeAll();
          }
        }
      });
  }

  getSectionByTab(idTab: number) {
    this.fieldSubs = this._fieldSvc
      .getSectionsByTabId(idTab)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.dataTableSection = resp.result;
            this.selectedTabSections = resp.result;
            if (resp.result.length > 0) {
              this.orderListSection = this.utilsSvc.generarArrayOrderList(
                resp.result.length + 1
              );
            } else {
              this.orderListSection = this.utilsSvc.generarArrayOrderList(1);
            }
          }
        }
      });
  }

  getBeneficiariesNumberByIdPolicy(idPolicy: number) {
    this._transactionService
      .getBeneficiariesNumberByIdPolicy(idPolicy)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._msgSvc.messageWaring(
              this._translateService.instant('Warning'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._msgSvc.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {
            this.formRiskData
              .get('maximumNumberBenefici')
              ?.setValue(resp.result);
          }
        }
      });
  }

  updateSectionInfo() {
    //Update sections by tabs;
    this.selectedTabSections = this.dataTableSection.filter(
      (x) => x.fkIIdTab == this.formField.get('idTab')?.value
    );
  }

  updateOptions() {
    const options = this.formField.get('options')?.value;
    const optionValues = this.formField.get('optionValues') as FormArray;

    // Obtener la cantidad actual de opciones
    const currentOptions = optionValues.length;

    if (options > currentOptions) {
      // Agregar nuevas opciones
      for (let i = currentOptions; i < options; i++) {
        optionValues.push(new FormControl(null, Validators.required));
      }
    } else if (options < currentOptions) {
      // Eliminar opciones excedentes
      for (let i = currentOptions - 1; i >= options; i--) {
        optionValues.removeAt(i);
      }
    }
  }

  updateOptionValue(index: number, value: any) {
    const option = this.formField
      .get('optionValues')
      ?.get(index.toString()) as FormGroup;
    option.patchValue({
      Id: index,
      Value: value.value,
    });

    // Obtener el campo de entrada correspondiente por su índice
    const inputField = document.getElementById(
      `optionInput-${index}`
    ) as HTMLInputElement;

    // Asignar el valor actualizado al campo de entrada
    if (inputField) {
      inputField.value = option.value.Value;
    }
  }

  get maxLengthText() {
    if (
      this.formField.get('fkIIdFieldType')?.value === this.typeField.TextArea
    ) {
      return this.maxTextAreaLength;
    } else if (
      this.formField.get('fkIIdFieldType')?.value === this.typeField.Text
    ) {
      return this.maxTextOnlyLength;
    } else {
      return null;
    }
  }
  onChangeTypeMinDate(event: any) {
    if (!this.isEditingField) {
      this.formField.get('dMinDateRequerid')?.disable();
    }

    this.formField.get('dMinDateRequerid')?.setValue('');
    this.formField.get('dMinDateRequerid')?.disable();
    this.formField.get('dMaxDateRequerid')?.setValue('');
    this.formField.get('dMaxDateRequerid')?.disable();

    const selectedValue = this.formField.get('vTypeDateMin')?.value;
    this.typeDateMaxFilter = this.typeDate.filter(
      (item) => item.Name === selectedValue
    );
    this.typeDateMaxFilter = this.typeDateMaxFilter.slice();

    if (
      event != null &&
      event != '0' &&
      this.formField.get('bIsMinDateRequerid')
    ) {
      this.formField.get('dMinDateRequerid')?.enable();
      this.formateDate(event);
    }
  }

  onChangeTypeMaxDate(event: any) {
    if (!this.isEditingField) {
      this.formField.get('dMaxDateRequerid')?.setValue('');
      this.formField.get('dMaxDateRequerid')?.disable();
    }
    this.filterFieldTypeDateMax();

    if (
      event != null &&
      event != 0 &&
      this.formField.get('bIsMaxDateRequerid')
    ) {
      this.formField.get('dMaxDateRequerid')?.enable();
      this.formateDate(event);
    }
  }

  /// Function formate date
  formateDate(typeDate: string) {
    const vTypeDateMinValue = typeDate;

    this.config = {
      format: '',
      showMultipleYearsNavigation: false,
      multipleYearsNavigateBy: 1,
      showNearMonthDays: true,
    };

    if (!typeDate || typeDate === '') {
      console.log('No se realiza la validación porque falta vTypeDateMinValue');
      return;
    }

    if (vTypeDateMinValue.includes('año')) {
      this.modeDatePicker = 'day';
      this.config = {
        format: 'YYYY',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false,
      };
      this.placeholderText = 'YYYY';
    }

    if (vTypeDateMinValue.includes('mes')) {
      this.modeDatePicker = 'month';
      this.config = {
        format: 'MM',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += '-MM';
    }

    if (vTypeDateMinValue.includes('día')) {
      this.modeDatePicker = 'day';
      this.config = {
        format: 'DD',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += '-DD';
    }

    if (vTypeDateMinValue.includes('hora')) {
      this.modeDatePicker = 'time';
      this.clockTypeIcon = true;
      this.config = {
        format: 'hh:mm:ss',
        enableMonthSelector: false, //Desahabilita el selecctor de meses.
        secondsInterval: 1,
        showSeconds: true,
      };
      this.placeholderText += 'hh:mm:ss';
    }

    if (vTypeDateMinValue.toLowerCase().includes('fecha')) {
      this.modeDatePicker = 'day';
      this.clockTypeIcon = false;
      this.config = {
        format: 'YYYY-MM-DD',
        showMultipleYearsNavigation: true,
        multipleYearsNavigateBy: 10,
        showNearMonthDays: false, //Si se van a mostrar u ocultar los días del mes siguiente y del mes anterior.
      };
      this.placeholderText += 'YYYY-MM-DD';
    }
  }

  filterFieldTypeDateMax() {
    if (
      this.formField.get('vTypeDateMin')?.value != null &&
      this.formField.get('vTypeDateMin')?.value != 0
    ) {
      const selectedValue = this.formField.get('vTypeDateMin')?.value;
      this.typeDateMaxFilter = this.typeDate.filter(
        (item) => item.Name === selectedValue
      );
      this.typeDateMaxFilter = this.typeDateMaxFilter.slice();
    }
  }

  deleteRowForTable(key: string, index: number) {
    const rowsArray = this.formField.get(key) as FormArray;
    if (rowsArray) {
      rowsArray.removeAt(index);
    }
  }

  addRow(key: string) {
    const rowsArray = this.formField.get(key) as FormArray;
    rowsArray.push(
      this.generateSchemaRow(key === 'rowsRequest' ? false : true)
    );
  }

  private toggleSearchValidators(isSearch: boolean) {
    const iSearchTypeControl = this.formField.get('iSearchType')!;

    if (isSearch) {
      iSearchTypeControl.setValidators([Validators.required]);
    } else {
      iSearchTypeControl.clearValidators();
      this.clearAllConditionalValidators();
    }

    iSearchTypeControl.updateValueAndValidity();
  }

  private togglePersonValidators(IsPerson: boolean) {
    const fkIGrouperField = this.formField.get('fkIGrouperField')!;
    const vEquivalentField = this.formField.get('vEquivalentField')!;

    if (IsPerson) {
      fkIGrouperField.setValidators([Validators.required]);
      vEquivalentField.setValidators([Validators.required]);
    } else {
      fkIGrouperField.clearValidators();
      vEquivalentField.clearValidators();
    }

    fkIGrouperField.updateValueAndValidity();
    vEquivalentField.updateValueAndValidity();
  }

  private updateValidatorsBasedOnSearchType(searchType: number) {
    const fkIIdChildrenDependentControl = this.formField.get(
      'fkIIdChildrenDependent'
    )!;
    const vEndpointControl = this.formField.get('vEndpoint')!;
    const iTypeRequestControl = this.formField.get('iTypeRequest')!;

    this.clearAllConditionalValidators();

    if (searchType === 2) {
      fkIIdChildrenDependentControl.setValidators([Validators.required]);
    } else if (searchType === 3) {
      vEndpointControl.setValidators([Validators.required]);
      iTypeRequestControl.setValidators([Validators.required]);
    }

    fkIIdChildrenDependentControl.updateValueAndValidity();
    vEndpointControl.updateValueAndValidity();
    iTypeRequestControl.updateValueAndValidity();
  }

  private clearAllConditionalValidators() {
    const fkIIdChildrenDependentControl = this.formField.get(
      'fkIIdChildrenDependent'
    )!;
    const vEndpointControl = this.formField.get('vEndpoint')!;
    const iTypeRequestControl = this.formField.get('iTypeRequest')!;

    fkIIdChildrenDependentControl.clearValidators();
    vEndpointControl.clearValidators();
    iTypeRequestControl.clearValidators();

    fkIIdChildrenDependentControl.setValue(null);
    vEndpointControl.setValue(null);
    iTypeRequestControl.setValue(null);

    fkIIdChildrenDependentControl.updateValueAndValidity();
    vEndpointControl.updateValueAndValidity();
    iTypeRequestControl.updateValueAndValidity();
  }

  updateTabs(tab: RequestUpdateTabModel) {
    this._fieldSvc
      .updateTabs(tab)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._msgSvc.messageSuccess(
            this._translateService.instant('Saved'),
            ''
          );
          this.getTabsListByPolicyFormId(this.idForm, true);
        }
      });
  }

  updateBeneficiariesNumberByIdPolicy(
    idPolicy: number,
    maximumNumberBenefici: number
  ) {
    this._transactionService
      .updateBeneficiariesNumberByIdPolicy(idPolicy, maximumNumberBenefici)
      .pipe(
        catchError((error) => {
          this._msgSvc.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this._msgSvc.messageSuccess(
            this._translateService.instant('Saved'),
            ''
          );
        }
      });
  }

  saveTabName() {
    const currentTab: TabModel = this.formRiskData.get('tab')?.value;
    const payload: RequestUpdateTabModel = {
      b_Active: currentTab.bActive,
      fk_i_IdForm: this.idForm,
      i_AmountColumns: 0,
      i_Order: currentTab.iOrder,
      i_SizeBetweenColumns: 0,
      i_SizeBetweenRows: 0,
      pkIIdTab: currentTab.pkIIdTab,
      v_Name: this.formRiskData.get('nameTab')?.value,
      v_NameDb: this.utilsSvc.generateNameDb(
        this.formRiskData.get('nameTab')?.value
      ),
    };
    this.updateTabs(payload);
  }
  saveMaximumNumberBenefici() {
    this.updateBeneficiariesNumberByIdPolicy(
      this.idPolicy,
      this.formRiskData.get('maximumNumberBenefici')?.value
    );
  }

  getFieldTypeTable(item: boolean) {
    return item ? 'Estándar' : 'Adicional';
  }

  standardTypeFieldValidations(currentField: FieldModelCreate) {
    if (currentField.bIsStandard && this.isEditingField) {
      this.formField.get('vNameField')?.disable();
      this.formField.get('fkIIdFieldType')?.disable();
      this.formField.get('bIsPerson')?.disable();
    }
  }

  setOrder(edit: boolean, table: string) {
    switch (table) {
      case 'section':
        if (edit) {
          this.orderListSection = this.utilsSvc.generarArrayOrderList(
            this.dataTableSection.length
          );
        } else {
          this.orderListSection = this.utilsSvc.generarArrayOrderList(
            this.dataTableSection.length + 1
          );
        }
        break;
      case 'field':
        if (edit) {
          this.orderListFields = this.utilsSvc.generarArrayOrderList(
            this.dataTableFields.length
          );
        } else {
          this.orderListFields = this.utilsSvc.generarArrayOrderList(
            this.dataTableFields.length + 1
          );
        }
        break;

      default:
        break;
    }
  }

  ngOnDestroy(): void {
    this.productSubs?.unsubscribe();
    this.formSubs?.unsubscribe();
    this.fieldSubs?.unsubscribe();
    this._settingCountryAndCompanySubscription?.unsubscribe();
    this._policyDataSubscription?.unsubscribe();
    this.productList?.unsubscribe();
    this.transactionSubs?.unsubscribe();
  }
}
