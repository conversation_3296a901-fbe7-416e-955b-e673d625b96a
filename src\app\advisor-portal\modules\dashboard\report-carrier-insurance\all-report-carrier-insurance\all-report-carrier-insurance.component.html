<form [formGroup]="formReportLog">
    <div class="row mb-2">
        <div class="col-md-3 mb-2">
            <mat-form-field class="example-full-width w-100" appearance="fill">
                <mat-label>{{"ReportLogCarrier.StartDateLabel" | translate}}</mat-label>
                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                <mat-datepicker-toggle matIconSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
                <mat-error *ngIf="_utilsService.isControlHasError(formReportLog, 'startDate', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-md-3 mb-2">
            <mat-form-field class="example-full-width w-100" appearance="fill">
                <mat-label>{{"ReportLogCarrier.FinalDateLabel" | translate}}</mat-label>
                <input matInput [matDatepicker]="endPicker" [matDatepickerFilter]="endDateFilter"
                    formControlName="endDate">
                <mat-datepicker-toggle matIconSuffix [for]="endPicker"></mat-datepicker-toggle>
                <mat-datepicker #endPicker></mat-datepicker>
                <mat-error *ngIf="_utilsService.isControlHasError(formReportLog, 'endDate', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-md-3 mb-2">
            <mat-form-field class="w-100" appearance="fill">
                <mat-label>
                    {{"ReportLogCarrier.InsuranceLabel" | translate}}
                </mat-label>
                <mat-select formControlName="insuranceName">
                    <mat-option *ngFor="let insurer of insurers" [value]="insurer.vName">
                        {{ insurer.vName }}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="_utilsService.isControlHasError(formReportLog, 'insuranceName', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-md-3 mb-2">
            <mat-form-field appearance="outline" class="w-100 mb-2">
                <mat-label>
                    {{"ReportLogCarrier.QuoteNumberLabel" | translate}}
                </mat-label>
                <input matInput formControlName="idQuote" PreventionSqlInjector />
                <mat-error *ngIf="_utilsService.isControlHasError(formReportLog, 'idQuote', 'required')">
                    {{ "ThisFieldIsRequired" | translate }}
                </mat-error>
            </mat-form-field>
        </div>
    </div>
    <div class="cont-btn mx-3">
        <div class="row">
            <button type="button" class="w-auto mr-1 mb-1" mat-raised-button color="primary" (click)="search()"
                [disabled]="!validForm">
                {{"ReportLogCarrier.SearchButton" | translate}}
                <mat-icon iconPositionEnd fontIcon="search"></mat-icon>
            </button>
            <button type="button" class="w-auto mr-1 mb-1" mat-raised-button color="primary" (click)="cleanFilters()">
                {{"ReportLogCarrier.CleanFilterButton" | translate}}
                <mat-icon iconPositionEnd class="material-symbols-outlined">cleaning_services</mat-icon>
            </button>
        </div>
        <div class="row">
            <button type="button" class="w-auto mr-1" mat-raised-button color="primary"
                [disabled]="this.dataReportLogTable.length <= 0" (click)="downloadReport()">
                {{"ReportLogCarrier.DownloadReportButton" | translate}}
                <mat-icon iconPositionEnd class="material-symbols-outlined">download</mat-icon>
            </button>
        </div>
    </div>
</form>

<div class="cont-table mt-2" *ngIf="estructReportLogTable.length > 0">
    <app-table [displayedColumns]="estructReportLogTable" [data]="dataReportLogTable"
        (iconClick)="controller($event)"></app-table>
</div>