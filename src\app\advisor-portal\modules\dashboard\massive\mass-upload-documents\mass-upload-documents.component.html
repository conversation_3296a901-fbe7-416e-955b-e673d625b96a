<!-- Configurar plantillas de carga -->
<button class=" mr-2 mt-1" type="button" color="primary" mat-raised-button (click)="configureUploadTemplate()">{{
    "UploadDocuments.SettingUpLoadTemplates" |
    translate }}</button>

<div class="row mt-3">
    <!-- Seleccione el tipo de carga de documento -->
    <div class="col-md-12 mb-2">
        <mat-form-field appearance="outline" class="select-look m-auto w-100">
            <mat-label>
                {{ "UploadDocuments.SelectTypeDocumentUpload" | translate }}
            </mat-label>
            <mat-select (selectionChange)="onTypeLoadDocument($event)">
                <mat-option *ngFor="let load of typeOfLoad" [value]="load">
                    {{ load.vName }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>

    <!-- <PERSON>leccione la plantilla de carga -->
    <div class="col-md-12 mb-2">
        <mat-form-field appearance="outline" class="select-look m-auto w-100">
            <mat-label>
                {{ "UploadDocuments.SelectUploadTemplate" | translate }}
            </mat-label>
            <mat-select (selectionChange)="onUploadTemplate($event)">
                <mat-option *ngFor="let item of loadingTemplate" [value]="item">
                    {{ item.templateName }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
</div>

<!-- Subir plantilla -->
<div class="row">
    <ng-container *ngIf="showFileUpload">
        <div class="cont-fieles mt-3">
            <span class="title-local">Plantilla</span>
            <div class="cont-template">
                <div class="cont-info-file">
                    <p class="m-0 p-0">{{templateName}}</p>
                    <span class="description">25 MB</span>
                </div>
                <div class="cont-download-icon">
                    <span (click)="downloadTemplate()" class="material-symbols-outlined click">
                        download
                    </span>
                </div>
            </div>
            <div class="cont-template" *ngIf="showTemplateUpload">
                <div class="cont-info-file">
                    <p class="m-0 p-0">{{fileNameTemplate}}</p>
                    <span class="description">{{templateSize}}</span>
                </div>
                <div class="cont-download-icon">
                    <span class="material-symbols-outlined click" (click)="deleteTemplate()">
                        delete
                    </span>
                </div>
            </div>
            <div class="cont-upload" *ngIf="!showTemplateUpload">
                <app-drag-drop-upload [fileAccept]="'.XLSX, XLS'" [isOneFile]="true"
                    [message]="'UploadDocuments.UploadFiles'" (saveFile)="getFiles($event)"></app-drag-drop-upload>
            </div>
        </div>
    </ng-container>
</div>

<!-- Documentos a cargar -->
<div class="row">
    <ng-container *ngIf="showFileUpload">
        <div class="cont-fieles mt-3">
            <div class="cont-template" *ngIf="files.length > 0">
                <div class="cont-info-file">
                    <p class="m-0 p-0">{{filesName}}</p>
                    <span class="description">{{filesSize}}</span>
                </div>
                <div class="cont-download-icon">
                    <span class="material-symbols-outlined click" (click)="deleteFile()">
                        delete
                    </span>
                </div>
            </div>
            <div class="cont-upload" *ngIf="files.length === 0">
                <app-drag-drop-upload [message]="'UploadDocuments.DocumentsToUpload'" [fileAccept]="this.fileAccept"
                    (saveFile)="getFilesToLoad($event)"></app-drag-drop-upload>
            </div>
        </div>
    </ng-container>
</div>

<div class="row mb-3 mt-1">
    <div class="col-md-12">
        <span class="cont-msg-info">
            {{"UploadDocuments.DescriptionBlue" | translate}}
        </span>
    </div>

</div>



<!-- datatable cargas -->
<div class="row mt-2">
    <app-table [displayedColumns]="estructTableLoad" [data]="dataTableLoad"
        (iconClick)="controller($event)"></app-table>
</div>

<!-- Conteo de procesos en tabla Cargas en proceso -->
<div class="cont-footer-table mt-2">
    <h6 class="title-local mb-1">{{ 'UploadDocuments.FilesToUpload' | translate }} :
        {{dataTableLoad.length}}
    </h6>
    <button (click)="deleteAll()" class="btn-custom w-auto" type="button" mat-raised-button>
        <strong>{{ "UploadDocuments.DeleteAll" | translate }}</strong>
    </button>
</div>

<div class="d-flex justify-content-center mt-3">
    <a class="label-button mt-1 mx-3" mat-button
        (click)="goBackMassive()"><span>{{"BulkUpload.Massives.MassCreationPolicy.GoOutToMass" | translate}}</span>
        <mat-icon fontIcon="arrow_back"></mat-icon>
    </a>
    <button class=" mr-2 mt-1" type="button" color="primary" mat-raised-button (click)="startUploadingDocuments()">{{
        "UploadDocuments.StartUploadingDocuments" |
        translate }}</button>
</div>
