import { Routes } from '@angular/router';
import { InsurersComponent } from './insurers.component';

export default [
  {
    path: '',
    component: InsurersComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./all-insurers/all-insurers.component').then(
            (c) => c.AllInsurersComponent
          ),
      },
      {
        path: 'new',
        loadComponent: () =>
          import('./edit-create-insurers/edit-create-insurers.component').then(
            (c) => c.EditCreateInsurersComponent
          ),
      },
      {
        path: 'modify/:id',
        loadComponent: () =>
          import('./edit-create-insurers/edit-create-insurers.component').then(
            (c) => c.EditCreateInsurersComponent
          ),
      },
    ],
  },
] as Routes;
