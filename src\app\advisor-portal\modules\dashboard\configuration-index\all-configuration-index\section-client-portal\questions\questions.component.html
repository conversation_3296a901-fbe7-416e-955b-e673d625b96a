<div class="row mt-5">
    <h5 class="fw-bold mb-2">
      {{ 'SectionClientPortal.FrequentQuestionsTitle' | translate }}
    </h5>
    <mat-label>
      {{ 'SectionClientPortal.FrequentQuestionsText' | translate }}
    </mat-label>

    <app-table
      [displayedColumns]="estructTableQuestions"
      [data]="dataTableQuestions"
      (iconClick)="controller($event)"
    ></app-table>

    <div class="row" *ngIf="dataTableQuestions.length < maxItems">
      <div class="col">
        <button
          type="button"
          mat-raised-button
          color="primary"
          (click)="openModal()"
        >
          {{ "SectionClientPortal.AddQuestion" | translate }}
        </button>
      </div>
    </div>
  </div>


  <ng-template #createEditQuestion>
    <app-modal2 [titleModal]="(formQuestion.get('id')?.value ? 'SectionClientPortal.UpdateQuestionTitle' : 'SectionClientPortal.CreateQuestionTitle') | translate">
      <ng-container body>
        <form [formGroup]="formQuestion">
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'SectionClientPortal.Question' | translate}}
            </mat-label>
            <input matInput formControlName="vQuestion" PreventionSqlInjector/>
            <mat-error *ngIf="utilsSvc.isControlHasError(formQuestion, 'vQuestion', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label>
              {{'SectionClientPortal.Answer' | translate}}
            </mat-label>
            <textarea matInput formControlName="vAnswer" PreventionSqlInjector cdkTextareaAutosize cdkAutosizeMinRows="3" cdkAutosizeMaxRows="10"></textarea>
            <mat-error *ngIf="utilsSvc.isControlHasError(formQuestion, 'vAnswer', 'required')" >
              {{'ThisFieldIsRequired' | translate}}
            </mat-error>
          </mat-form-field>
  
        </form>
      </ng-container>
      <ng-container customButtonRight>
        <div class="modal-footer">
          <button mat-raised-button color="primary" type="button" class="" (click)="saveQuestion()">
            {{ "Save" | translate }}
          </button>
        </div>
      </ng-container>
    </app-modal2>
  </ng-template>