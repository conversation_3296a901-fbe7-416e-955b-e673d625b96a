<app-choose-country-and-company (valueForm)="validForm($event)">
</app-choose-country-and-company>
<div class="mb-2 d-flex align-items-center">
  <h3>{{ "FormConfiguration.FormSelection" | translate }}</h3>
  <mat-icon class="click mb-1" matTooltipPosition="right" matTooltip="{{ 'Tooltips.FormSelect' | translate }}">help_outline</mat-icon>
</div>



<app-choose-filter-form-module
  (saveDataFilter)="saveDataFilter($event)"
></app-choose-filter-form-module>

<app-all-forms
  *ngIf="isForm == true"
  [idStage]="idStage"
  [dataFilterFormModuleSetting]="dataFilterFormModuleSetting"
></app-all-forms>
