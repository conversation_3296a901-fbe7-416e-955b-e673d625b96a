<div *ngIf="idForm != 0" class="row mb-2">
  <div class="col-12 col-md-12">
      <div class="d-flex justify-content-start align-items-center mb-2">
          <h3 style="margin: 0">
              {{ "Product.Fields" | translate }}
          </h3>
          <mat-icon class="click ml-1" matTooltipPosition="right" matTooltip="{{ 'Tooltips.FormField' | translate }}">help_outline</mat-icon>
      </div>
      <app-table *ngIf="!LoginClient" [displayedColumns]="estructTableFields" [data]="dataTableFields"
          (iconClick)="EditField(true,$event)"></app-table>

      <app-table *ngIf="LoginClient" [displayedColumns]="estructTableFieldsLogin" [data]="dataTableFields"
          (iconClick)="EditField(true,$event)"></app-table>

      <button class="mx-3 mb-2" type="button" mat-raised-button color="primary" (click)="EditField(false)">
          {{ "Add" | translate }}
          <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
      </button>
  </div>
</div>



<ng-template #editNewFieldModal>
  <app-modal2 [showCancelButtonBelow]="false"  [titleModal]="
      isEditingField
      ? ('Product.UpdateField' | translate)
      : ('Product.NewField' | translate)
  ">
      <ng-container body>
          <mat-tab-group class="custom-tab-group">
              <mat-tab
              [label]="'Product.Characteristics' | translate"
              >
                  <div class="row mb-2">
                      <div class="border col-md-12 col-sm-12">
                          <form [formGroup]="formField">
                              <div class="row">
                                  <div class="d-flex  align-items-center">
                                      <div class="">
                                          <mat-checkbox formControlName="bUsingExistent" class="my-4">
                                              {{ "Product.ConfigureExistingField" | translate }}
                                          </mat-checkbox>
                                      </div>
                                      <div class="">
                                          <!-- Configurar con campo OCR -->
                                          <mat-checkbox formControlName="bConfigureWithOCRField" class="my-4">
                                              {{ "OCR.ConfigureWithOCRField" | translate }}
                                          </mat-checkbox>
                                      </div>
                                  </div>

                                  @if(formField.get('bConfigureWithOCRField')?.value){
                                      <!-- Lectura OCR -->
                                      <mat-form-field  appearance="outline"
                                          class="select-look w-100">
                                          <mat-label>
                                              {{ "OCR.ReadingOCR" | translate }}
                                          </mat-label>
                                          <mat-select formControlName="readingOCR" id="readingOCR">
                                              @for (item of readingsData; track $index) {
                                                  <mat-option [value]="item.id">
                                                      {{ item.name }}
                                                  </mat-option>
                                              }
                                          </mat-select>
                                      </mat-form-field>

                                      <!-- Campo base -->
                                      <mat-form-field  appearance="outline"
                                          class="select-look w-100">
                                          <mat-label>
                                              {{ "OCR.FiledBase" | translate }}
                                          </mat-label>
                                          <mat-select formControlName="baseField" id="baseField">
                                              @for (item of homologationData; track $index) {
                                                  <mat-option [value]="item.id">
                                                      {{ item.baseName }}
                                                  </mat-option>
                                              }
                                          </mat-select>
                                      </mat-form-field>

                                  }

                                  <mat-form-field *ngIf="formField.get('bUsingExistent')?.value" appearance="outline"
                                      class="select-look w-100">
                                      <mat-label>
                                          {{ "Product.FieldType" | translate }}
                                      </mat-label>
                                      <mat-select formControlName="fkIdFieldExistent" id="idField">
                                          <mat-option *ngFor="let item of allFields" [value]="item.id">
                                              {{ item.name }}
                                          </mat-option>
                                      </mat-select>
                                  </mat-form-field>

                                  <mat-checkbox formControlName="bIsInheritedField" class="my-4">
                                      {{ "Product.InheritedField" | translate }}
                                  </mat-checkbox>

                                  <mat-form-field *ngIf="formField.get('bIsInheritedField')?.value" appearance="outline"
                                      class="select-look w-100">
                                      <mat-label>
                                          {{ "Etapa" | translate }}
                                      </mat-label>
                                      <mat-select formControlName="fkIdstageInherited" >
                                          <mat-option *ngFor="let item of allStages" [value]="item.pkIIdStage">
                                              {{ item.vNameStage }}
                                          </mat-option>
                                      </mat-select>
                                  </mat-form-field>

                                  <mat-form-field *ngIf="formField.get('bIsInheritedField')?.value" appearance="outline"
                                      class="select-look w-100">
                                      <mat-label>
                                          {{ "Estado" | translate }}
                                      </mat-label>
                                      <mat-select formControlName="fkIdStateInherited">
                                          <mat-option *ngFor="let item of allStates" [value]="item.id">
                                              {{ item.name }}
                                          </mat-option>
                                      </mat-select>
                                  </mat-form-field>


                                  <mat-form-field *ngIf="formField.get('bIsInheritedField')?.value" appearance="outline"
                                      class="select-look w-100">
                                      <mat-label>
                                          {{ "Product.OriginalField" | translate }}
                                      </mat-label>
                                      <mat-select formControlName="fkIdFieldInherited">
                                          <mat-option *ngFor="let item of allOriginalField; let i = index" [value]="item.pkIIdFieldModule">
                                              {{ item.vNameField }}
                                          </mat-option>
                                      </mat-select>
                                  </mat-form-field>

                              </div>
                              <mat-slide-toggle class="my-4" formControlName="bActive">
                                  {{ "Table.ActiveField" | translate }}
                              </mat-slide-toggle>
                              <mat-form-field appearance="outline" class="select-look w-100">
                                  <mat-label>
                                      {{ "Product.FieldType" | translate }}
                                  </mat-label>
                                  <mat-select formControlName="fkIIdFieldType" id="fkIIdFieldType"  (ngModelChange)="resetFieldSetDefaultValue()" required>
                                      <mat-option *ngFor="let item of fieldTypes" [value]="item.pkIIdFieldType">
                                          {{ item.vName }}
                                      </mat-option>
                                  </mat-select>
                              </mat-form-field>
                              <mat-form-field appearance="outline" class="w-100 mb-2">
                                  <mat-label>
                                      {{formField.get('fkIIdFieldType')?.value === typeField.DescriptionText?
                                      ("FormConfiguration.Fields.ContentText" | translate):("Product.FieldName" | translate)
                                      }} (visible)
                                  </mat-label>
                                  <input matInput formControlName="vNameField" />
                                  <mat-error *ngIf="utilsSvc.isControlHasError(formField, 'vNameField', 'required')">
                                      {{ "ThisFieldIsRequired" | translate }}
                                  </mat-error>
                              </mat-form-field>

                                      <mat-form-field class="w-100 mb-2" appearance="outline">
                                          <mat-label>
                                              {{ "FormConfiguration.ProgressBar.StepOrderLabel" | translate }}
                                          </mat-label>
                                          <mat-select formControlName="iOrder">
                                              <mat-option *ngFor="let order of orderList" [value]="order">
                                                  {{ order }}
                                              </mat-option>
                                          </mat-select>
                                          <mat-error *ngIf="utilsSvc.isControlHasError(formField, 'iOrder', 'required')">
                                              {{ "ThisFieldIsRequired" | translate }}
                                          </mat-error>
                                      </mat-form-field>
                                      <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value != typeField.DescriptionText"
                                          appearance="outline" class="w-100 mb-2">
                                          <mat-label>
                                              {{ "Product.NameInDataBase" | translate }}
                                          </mat-label>
                                          <input matInput formControlName="vNameFieldDb" [readonly]="true" />
                                      </mat-form-field>
                                      <mat-form-field appearance="outline" class="w-100 mb-2">
                                          <mat-label>
                                              {{ "Product.Description" | translate }}
                                          </mat-label>
                                          <input matInput formControlName="vDescription" />
                                          <mat-error *ngIf="
                                  utilsSvc.isControlHasError(formField, 'vDescription', 'required')
                                  ">
                                              {{ "ThisFieldIsRequired" | translate }}
                                          </mat-error>
                                      </mat-form-field>
                                      <mat-form-field appearance="outline" class="select-look w-100">
                                          <mat-label>
                                              {{ "FormConfiguration.ProgressBar.Title" | translate }}
                                          </mat-label>
                                          <mat-select formControlName="fkIIdProgressBar">
                                              <mat-option *ngFor="let item of progressBarList" [value]="item.pkIIdProgressBar">
                                                  {{ item.vName }}
                                              </mat-option>
                                          </mat-select>
                                      </mat-form-field>
                                      <mat-form-field appearance="outline" class="select-look w-100">
                                          <mat-label>
                                              {{ "Product.Tab" | translate }}
                                          </mat-label>
                                          <mat-select formControlName="fkIIdTabModule">
                                              <mat-option *ngFor="let item of tabList" [value]="item.pkIIdTabModule">
                                                  {{ item.vName }}
                                              </mat-option>
                                          </mat-select>
                                      </mat-form-field>
                                      <mat-form-field appearance="outline" class="select-look w-100">
                                          <mat-label>
                                              {{ "Product.Section" | translate }}
                                          </mat-label>
                                          <mat-select formControlName="fkIIdSectionModule" id="idSection">
                                              <mat-option *ngFor="let item of dataTableSections" [value]="item.pkIIdSectionModule">
                                                  {{ item.vName }}
                                              </mat-option>
                                          </mat-select>
                                      </mat-form-field>

                                      <ng-container *ngIf="
                                              formField.get('fkIIdFieldType')?.value ===
                                              typeField.DropDownList">

                                          <mat-form-field appearance="outline" class="select-look w-100">
                                              <mat-label>
                                                  {{ "FormConfiguration.Fields.SelectCatalog" | translate }}
                                              </mat-label>
                                              <mat-select formControlName="fkIIdCatalog" id="fkIIdCatalog">
                                                  <mat-option *ngFor="let item of catalogTable" [value]="item.pkIIdCatalog">
                                                      {{ item.vName }}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>

                          <mat-form-field appearance="outline" class="select-look w-100">
                              <mat-label>
                                  {{ "FormConfiguration.Fields.SelectCatalogField" | translate }}
                              </mat-label>
                              <mat-select formControlName="fkIIdFieldCatalog" id="fkIIdFieldCatalog">
                                  <mat-option *ngFor="let item of catalogFields" [value]="item.pkIIdCatalogField">
                                      {{ item.vName }}
                                  </mat-option>
                              </mat-select>
                          </mat-form-field>

                                      </ng-container>

                                      <ng-container *ngIf="formField.get('fkIIdFieldType')?.value != typeField.DescriptionText">
                                          <h6 class="subtitle">{{ "FormConfiguration.Fields.FieldFeatures" | translate}}</h6>

                                          <mat-checkbox formControlName="bisKeyField" class="my-4" *ngIf="!LoginClient">
                                              {{ "Product.FieldKey" | translate }}
                                            </mat-checkbox>
                                          <mat-checkbox formControlName="bIsSearch" class="my-4">
                                              {{ "FormConfiguration.Fields.SearchField" | translate }}
                                          </mat-checkbox>
                                          <mat-checkbox formControlName="bIsTable" class="my-4">
                                              {{ "FormConfiguration.Fields.TableField" | translate }}
                                          </mat-checkbox>
                                          <mat-checkbox formControlName="bIsPerson" class="my-4">
                                              {{ "FormConfiguration.Fields.PersonField" | translate }}
                                          </mat-checkbox>
                                          <mat-checkbox formControlName="bIsPolicy" class="my-4">
                                              {{ "FormConfiguration.Fields.PolicyField" | translate }}
                                          </mat-checkbox>
                                          <mat-checkbox formControlName="bHelpText" class="my-4">
                                              {{ "Product.HelpText" | translate }}
                                          </mat-checkbox>
                                          <mat-checkbox formControlName="bIsReadonly" class="my-4">
                                              {{ "Product.NoEditing" | translate }}
                                          </mat-checkbox>
                                          <mat-checkbox formControlName="bIsDependent" class="my-4">
                                              {{ "Product.DependentField" | translate }}
                                          </mat-checkbox>
                                          <mat-checkbox formControlName="bRequired" class="my-4">
                                              {{ "Product.Required" | translate }}
                                          </mat-checkbox>
                                          <mat-checkbox formControlName="bVisibleToConsultation" class="my-4" *ngIf="formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments">
                                              {{ "Product.VisibleToConsultating" | translate }}
                                          </mat-checkbox>

                                          <mat-checkbox formControlName="bIsHasDefaultValue" class="my-4" *ngIf=" [ typeField.Numeric, typeField.Text, typeField.TextArea, typeField.Date, typeField.Decimal ].includes(formField.get('fkIIdFieldType')?.value) ">
                                          {{"FormConfiguration.Fields.IsHasDefaultValue" | translate }}
                                        </mat-checkbox>
                                        <mat-checkbox formControlName="bisCheckField" class="my-4" *ngIf="LoginClient">
                                          {{ "Product.FieldCheck" | translate }}
                                        </mat-checkbox>
                                        <!-- Campo llave OCR -->
                                        <mat-checkbox formControlName="bisKeyFieldOCR" class="my-4">
                                          {{ "OCR.OCRKeyField" | translate }}
                                        </mat-checkbox>
                                        <ng-container >
                                          <!-- Campo llave para crear póliza -->
                                          <mat-checkbox formControlName="keyFieldCreatePolicy"  class="my-4">
                                              {{ "OCR.KeyFieldToCreatePolicy" | translate }}
                                          </mat-checkbox>

                                          <mat-checkbox formControlName="bIsInvisible" class="my-4">
                                              {{ "FormConfiguration.Fields.InvisibleField" | translate }}
                                          </mat-checkbox>

                                          <mat-checkbox formControlName="bIsGetdate" class="my-4" *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date">
                                            {{"FormConfiguration.Fields.GetDateActual" | translate }}
                                          </mat-checkbox>

                                          <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.DropDownList" formControlName="bIsMultiple" class="my-4">
                                            {{ "Selección multiple" | translate }}
                                          </mat-checkbox>

                                          <div class="form-group" *ngIf="formField.get('bIsInvisible')?.value">
                                              <mat-radio-group formControlName="iElementHideField">
                                                <mat-radio-button [value]="1">{{ "FormConfiguration.Fields.IsPortalClient" | translate }}</mat-radio-button>
                                                <mat-radio-button [value]="2">{{ "FormConfiguration.Fields.IsPortalAdvisor" | translate }}</mat-radio-button>
                                                <mat-radio-button [value]="3">{{ "FormConfiguration.Fields.IsHideBoth" | translate }}</mat-radio-button>
                                              </mat-radio-group>
                                          </div>
                                    </ng-container>

                                        @if(formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments ){
                                        <ng-container >
                                              <!-- Enviar documento para lectura OCR -->
                                              <mat-checkbox formControlName="sendDocumentForOCRReading" class="my-4">
                                                  {{ "OCR.SendDocumentForOCRReading" | translate }}
                                              </mat-checkbox>
                                        </ng-container>
                                      }
                                        <mat-checkbox formControlName="bIsExternalLink" class="my-4">
                                          {{ "FormConfiguration.Fields.IsExternalLinktValue" | translate }}
                                        </mat-checkbox>

                                      </ng-container>

                                      @if(formField.get('sendDocumentForOCRReading')?.value){
                                          <ng-container >
                                              <!-- Enviar documento a OCR -->
                                              <h6 class="mb-3 title-local">{{ "OCR.SendDocumentToOCR" | translate}}</h6>

                                              <!-- Campo llave del OCR -->
                                              <mat-form-field appearance="outline"
                                              class="select-look w-100">
                                              <mat-label>
                                                  {{ "OCR.OCROfKeyField" | translate }}
                                              </mat-label>
                                              <mat-select [(ngModel)]="keyFieldOCR" [disabled]="true" [ngModelOptions]="{standalone: true}">
                                                  <mat-option *ngFor="let item of fielkeyOCRList" [value]="item.id">
                                                      {{ item.name }}
                                                  </mat-option>
                                              </mat-select>
                                              </mat-form-field>

                                              <!-- Lectura OCR a la que se envia el documento -->
                                              <mat-form-field  appearance="outline"
                                                  class="select-look w-100">
                                                  <mat-label>
                                                      {{ "OCR.OCRReadingSentDoc" | translate }}
                                                  </mat-label>
                                                  <mat-select formControlName="fkOCRReadingSentDoc" id="fkOCRReadingSentDoc">
                                                      @for (item of readingsData; track $index) {
                                                          <mat-option [value]="item.id">
                                                              {{ item.name }}
                                                          </mat-option>
                                                      }
                                                  </mat-select>
                                              </mat-form-field>
                                          </ng-container>
                                      }

                                      <!--Visible for searching field-->
                                      <div class="container group-flow" *ngIf="formField.get('bIsSearch')?.value">
                                          <h6 class="mb-2">{{ "FormConfiguration.Fields.SearchField" | translate}}</h6>
                                          <fieldset>
                                              <mat-radio-group formControlName="iSearchType">
                                                  <mat-radio-button
                                                      *ngIf="formField.get('fkIIdFieldType')?.value !== typeField.DropDownList"
                                                      [value]="1">{{ "FormConfiguration.Fields.HeIsAPersontype" |
                                                      translate}}</mat-radio-button>
                                                  <mat-radio-button
                                                      *ngIf="formField.get('fkIIdFieldType')?.value !== typeField.DropDownList"
                                                      [value]="2">{{ "FormConfiguration.Fields.ItIsQuoteType" |
                                                      translate}}</mat-radio-button>
                                                  <mat-radio-button [value]="3">{{ "FormConfiguration.Fields.ItIsEndpoint" |
                                                      translate}}</mat-radio-button>

                                              </mat-radio-group>
                                          </fieldset>

                                          <mat-form-field *ngIf="formField.get('iSearchType')?.value==2" appearance="outline"
                                              class="select-look w-100">
                                              <mat-label>
                                                  {{ "Product.DependentField" | translate }}
                                              </mat-label>
                                              <mat-select multiple formControlName="fkIIdChildrenDependent" id="Children" required>
                                                  <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdFieldModule">
                                                      {{ item.vNameField }}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>

                                          <ng-container *ngIf="formField.get('iSearchType')?.value == 3">
                                              <div class="row">
                                              <div class="col-md-12">
                                                  <mat-form-field appearance="outline" class="w-100">
                                                  <mat-label>URL Endpoint</mat-label>
                                                  <input formControlName="vEndpoint" matInput placeholder="https://DOMAIN/CONTROLLER/METHOD" value="">
                                                  </mat-form-field>
                                              </div>
                                              <div class="col-md-12">
                                                  <mat-form-field class="w-100">
                                                  <mat-label>Tipo</mat-label>
                                                  <mat-select formControlName="iTypeRequest" id="typeRequest" required>
                                                      <mat-option [value]="1">GET</mat-option>
                                                      <mat-option [value]="2">POST</mat-option>
                                                  </mat-select>
                                                  </mat-form-field>
                                              </div>
                                              </div>

                                              <div class="row">
                                              <div class="col-md-12">
                                                  <h3>Mapeo elementos a enviar</h3>
                                                  <table class="table">
                                                  <thead>
                                                      <tr>
                                                      <th>Campo</th>
                                                      <th>Texto en API</th>
                                                      <th>{{ "Product.Convertion" | translate }}</th>
                                                      </tr>
                                                  </thead>
                                                  <tbody formArrayName="rowsRequest">
                                                      <tr *ngFor="let row of rowsRequest; let i = index" [formGroupName]="i">
                                                          <td>
                                                              <mat-form-field class="w-100">
                                                              <mat-label>Campo {{i}}</mat-label>
                                                              <mat-select formControlName="fkIIdField" id="Children" required>
                                                                  <mat-option [value]="0">Campo Actual</mat-option>
                                                                  <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdFieldModule">
                                                                  {{ item.vNameField }}
                                                                  </mat-option>
                                                              </mat-select>
                                                              </mat-form-field>
                                                          </td>
                                                          <td>
                                                              <mat-form-field class="w-100">
                                                              <mat-label>Texto en API</mat-label>
                                                              <input matInput formControlName="vEquivalentField" />
                                                              </mat-form-field>
                                                          </td>
                                                          <td>
                                                              <mat-form-field class="w-100">
                                                                <mat-label>{{ "Product.Convertion" | translate }}</mat-label>
                                                                <mat-select formControlName="iTypeConvertion" id="iTypeConvertion">
                                                                  <mat-option [value]="1">{{ "Product.ByDefect" | translate }}</mat-option>
                                                                  <mat-option [value]="2">{{ "Product.String" | translate }}</mat-option>
                                                                </mat-select>
                                                              </mat-form-field>
                                                            </td>
                                                          <td>
                                                              <button mat-icon-button (click)="deleteRowForTable('rowsRequest', i)" type="button">
                                                              <mat-icon>delete</mat-icon>
                                                              </button>
                                                          </td>
                                                      </tr>
                                                  </tbody>
                                                  </table>
                                                  <button type="button" mat-raised-button color="primary"
                                                  (click)="addRow('rowsRequest')">Añadir</button>
                                                  <mat-checkbox formControlName="bIsUsingJson">
                                                  Añadir JSON
                                                  </mat-checkbox>
                                              </div>
                                              </div>

                                              <div *ngIf="formField.get('bIsUsingJson')?.value" class="row my-2">
                                              <div class="col-md-12">
                                                  <mat-form-field class="w-100">
                                                  <mat-label>Estructura del JSON</mat-label>
                                                  <textarea formControlName="vSchemaJson" matInput placeholder="{KEY:VALUE}"></textarea>
                                                  </mat-form-field>
                                              </div>
                                              </div>

                                              <div class="row my-5" *ngIf="formField.get('fkIIdFieldType')?.value === typeField.DropDownList">
                                                  <div class="col-md-12">
                                                    <h3>{{ "Product.MappingFieldsReceive" | translate }}</h3>
                                                    <div class="col-md-12">
                                                      <mat-form-field class="w-100">
                                                        <mat-label>{{ "Product.TypeResponseList" | translate }}</mat-label>
                                                        <mat-select formControlName="iTypeResponseList" id="typeResponseList" required>
                                                          <mat-option [value]="1">{{ "Product.Object" | translate }}</mat-option>
                                                          <mat-option [value]="2">{{ "Product.List" | translate }}</mat-option>
                                                        </mat-select>
                                                      </mat-form-field>
                                                      <mat-form-field class="w-100">
                                                        <mat-label>{{ "Product.NameResponseList" | translate }}</mat-label>
                                                        <input matInput formControlName="vNameResponseList" />
                                                      </mat-form-field>
                                                    </div>
                                                  </div>
                                              </div>

                                              <div class="row my-2" *ngIf="formField.get('fkIIdFieldType')?.value !== typeField.DropDownList">
                                              <div class="col-md-12">
                                                  <h3>Mapeo elementos a recibir</h3>
                                                  <table class="table">
                                                  <thead>
                                                      <tr>
                                                      <th>Campo</th>
                                                      <th>Llave JSON a recibir</th>
                                                      </tr>
                                                  </thead>
                                                  <tbody formArrayName="rowsResponse">
                                                      <tr *ngFor="let row of rowsResponse; let i = index" [formGroupName]="i">
                                                          <td>
                                                              <mat-form-field class="w-100">
                                                              <mat-label>Campo:</mat-label>
                                                              <mat-select formControlName="fkIIdField" id="Children" required>
                                                                  <mat-option [value]="0">Campo Actual</mat-option>
                                                                  <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdFieldModule">
                                                                  {{ item.vNameField }}
                                                                  </mat-option>
                                                              </mat-select>
                                                              </mat-form-field>
                                                          </td>
                                                          <td>
                                                              <mat-form-field class="w-100">
                                                              <mat-label>Llave JSON a recibir</mat-label>
                                                              <input matInput formControlName="vEquivalentField" />
                                                              </mat-form-field>
                                                          </td>
                                                          <td>
                                                              <button mat-icon-button (click)="deleteRowForTable('rowsResponse', i)" type="button">
                                                              <mat-icon>delete</mat-icon>
                                                              </button>
                                                          </td>
                                                      </tr>
                                                  </tbody>
                                                  </table>
                                                  <button type="button" mat-raised-button color="primary"
                                                  (click)="addRow('rowsResponse')">Añadir</button>
                                              </div>
                                              </div>
                                          </ng-container>

                                          </div>

                                      <!--Default value field-->
                                      <mat-form-field
                                       *ngIf="formField.get('bIsHasDefaultValue')?.value && formField.get('fkIIdFieldType')?.value" appearance="outline" class="select-look w-100" >
                                      <mat-label>{{
                                        "FormConfiguration.Fields.SetDefaultValue" | translate
                                      }}</mat-label>

                                      <ng-container [ngSwitch]="formField.get('fkIIdFieldType')?.value">
                                        <input
                                          *ngSwitchCase="typeField.Numeric"
                                          matInput
                                          type="number"
                                          formControlName="vSetDefaultValue"
                                        />
                                        <input
                                          *ngSwitchCase="typeField.Text"
                                          matInput
                                          type="text"
                                          formControlName="vSetDefaultValue"
                                        />
                                        <input
                                          *ngSwitchCase="15"
                                          matInput
                                          type="text"
                                          formControlName="vSetDefaultValue"
                                        />
                                        <div *ngSwitchCase="typeField.Date">
                                          <input
                                            matInput
                                            [matDatepicker]="initialValidityAns"
                                            formControlName="vSetDefaultValue"
                                          />
                                        </div>
                                        <input
                                          *ngSwitchCase="typeField.TextArea"
                                          matInput
                                          type="text"
                                          formControlName="vSetDefaultValue"
                                        />
                                        <input
                                          *ngSwitchCase="typeField.Decimal"
                                          matInput
                                          type="text"
                                          step="0.001"
                                          formControlName="vSetDefaultValue"
                                          (input)="validateDecimals($event)"
                                          (blur)="formatToThreeDecimals($event)"
                                        />
                                      </ng-container>

                                      <mat-datepicker-toggle
                                        *ngIf="
                                          formField.get('fkIIdFieldType')?.value === typeField.Date
                                        "
                                        matSuffix
                                        [for]="initialValidityAns"
                                      ></mat-datepicker-toggle>
                                      <mat-datepicker #initialValidityAns></mat-datepicker>
                                    </mat-form-field>

                                      <!--Visible for person field-->
                                      <div class="container group-flow" *ngIf="formField.get('bIsPerson')?.value">
                                          <h6 class="mt-3 mb-0">{{ "FormConfiguration.Fields.PersonTypeField" | translate }}</h6>
                                          <mat-checkbox formControlName="bIsGrouper" class="my-4">
                                              {{ "FormConfiguration.Fields.ItIsGrouperField" | translate }}
                                          </mat-checkbox>

                                          <mat-form-field *ngIf="!isGrouper" appearance="outline" class="select-look w-100">
                                              <mat-label>
                                                  {{ "FormConfiguration.Fields.GrouperField" | translate }}
                                              </mat-label>
                                              <mat-select formControlName="fkIGrouperField" id="fkIGrouperField">
                                                  <mat-option *ngFor="let item of fieldGroupers" [value]="item.id">
                                                      {{ item.name }}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>

                          <mat-form-field appearance="outline" class="select-look w-100">
                              <mat-label>
                                  {{ "FormConfiguration.Fields.EquivalenceItemTablePerson" | translate }}
                              </mat-label>
                              <mat-select formControlName="vEquivalentField" id="vEquivalentField">
                                  <mat-option *ngFor="let item of nameColumnsCustomerTable" [value]="item">
                                      {{ item }}
                                  </mat-option>
                              </mat-select>
                          </mat-form-field>
                                      </div>

                                      <div class="group-flow container" *ngIf="formField.get('bIsPolicy')?.value">
                                          <h6 class="subtitle">{{ "FormConfiguration.Fields.PolicyTypeField" | translate }}</h6>
                                          <mat-checkbox formControlName="bIsGrouper" class="my-4">
                                              {{ "FormConfiguration.Fields.ItIsGrouperField" | translate }}
                                          </mat-checkbox>

                                          <mat-form-field *ngIf="!formField.get('bIsGrouper')?.value" appearance="outline"
                                              class="select-look w-100">
                                              <mat-label>
                                                  {{ "FormConfiguration.Fields.GrouperField" | translate }}
                                              </mat-label>
                                              <mat-select formControlName="fkIGrouperField" id="idParent" required>
                                                  <mat-option *ngFor="let item of fieldGroupers" [value]="item.id">
                                                      {{ item.name }}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>

                                          <mat-form-field appearance="outline" class="select-look w-100">
                                              <mat-label>
                                                  {{ "FormConfiguration.Fields.EquivalenceItemPolicyTable" | translate }}
                                              </mat-label>
                                              <mat-select formControlName="vEquivalentField" id="idParent" required>
                                                  <mat-option *ngFor="let item of nameColumnsPolicyTable" [value]="item">
                                                      {{ item }}
                                                  </mat-option>
                                              </mat-select>
                                          </mat-form-field>
                                      </div>

                              <!--Visible for dependent field-->
                              <ng-container *ngIf="formField.get('bIsDependent')?.value">
                              <mat-form-field  appearance="outline"
                                  class="select-look w-100">
                                  <mat-label>
                                      {{ "Product.DependentField" | translate }}
                                  </mat-label>
                                  <mat-select formControlName="fkIIdParent" id="idParent" required>
                                      <mat-option *ngFor="let item of dataTableFields" [value]="item.pkIIdFieldModule">
                                          {{ item.vNameField }}
                                      </mat-option>
                                  </mat-select>
                              </mat-form-field>
                              <mat-form-field *ngIf="catalogs.length>0"  appearance="outline"
                                  class="select-look w-100">
                                  <mat-label>
                                      Opción a la que es dependiente
                                  </mat-label>
                                  <mat-select formControlName="iOptionDependent" multiple id="iOptionDependent">
                                      <mat-option [value]="null">
                                          Opción a la que es dependiente
                                      </mat-option>
                                      <mat-option *ngFor="let item of catalogs" [value]="item.id">
                                          {{ item.name }}
                                      </mat-option>
                                  </mat-select>
                              </mat-form-field>
                              </ng-container>

                              <ng-container *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                              formField.get('fkIIdFieldType')?.value === typeField.Numeric ||  formField.get('fkIIdFieldType')?.value === typeField.Money ||
                              formField.get('fkIIdFieldType')?.value === typeField.Text || formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments ||
                              formField.get('fkIIdFieldType')?.value === typeField.TextArea || formField.get('fkIIdFieldType')?.value === typeField.Decimal">
                                  <mat-card [ngClass]="{'group-flow': formField.get('fkIIdFieldType')?.value === typeField.TextArea}">
                                      <mat-card-title><strong>{{ "Product.FieldRules" | translate }}</strong> </mat-card-title>
                                      <mat-card-content>
                                          <div class="row my-2">
                                              <div class="col-6 col-md-6">
                                                  <!--Visible for numeric, text field. Set size min that a input has to have-->
                                                  <mat-form-field *ngIf="
                                                              formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                              formField.get('fkIIdFieldType')?.value === typeField.Money ||
                                                              formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                              formField.get('fkIIdFieldType')?.value === typeField.Text ||
                                                              formField.get('fkIIdFieldType')?.value === typeField.TextArea ||
                                                              formField.get('fkIIdFieldType')?.value === typeField.Decimal
                                                          " appearance="outline" class="w-100 mb-2">
                                                      <mat-label>
                                                          {{ "Product.MinimumSize" | translate }}
                                                      </mat-label>
                                                      <input matInput type="number" formControlName="iMinLength" />
                                                  </mat-form-field>
                                              </div>
                                              <div class="col-6 col-md-6">
                                                  <!--Visible for numeric, text field, UploadDocument.
                                                                                                                      Set size max that a input has to have and set max size for a file -->
                                                  <mat-form-field
                                                      *ngIf=" formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.Money ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.Text ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.UploadDocuments ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.TextArea  ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                                                      appearance="outline" class="w-100 mb-2">
                                                      <mat-label>
                                                          {{ "Product.MaximumSize" | translate }}
                                                      </mat-label>
                                                      <input matInput type="number" formControlName="iMaxLength" [max]="maxLengthText"/>
                                                      <mat-hint align="start" *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Text">
                                                          {{ "Product.Maximun" | translate }} {{ maxTextOnlyLength }} {{ "Product.MaxLengthText" | translate }}
                                                      </mat-hint>
                                                      <mat-error *ngIf="formField.get('iMaxLength')?.value < formField.get('iMinLength')?.value">
                                                          {{
                                                          "Product.MaximumSizeMustBeGreaterThanMinimumSize" | translate
                                                          }}
                                                      </mat-error>
                                                      <mat-error *ngIf="formField.get('iMaxLength')?.hasError('max')">
                                                          {{ "Product.MaxCharacters" | translate }} {{maxLengthText}}
                                                      </mat-error>
                                                  </mat-form-field>
                                              </div>
                                          </div>
                                          <div class="row my-2">
                                              <div class="col-12 col-md-12">
                                                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric"
                                                  formControlName="bIsEmail" class="my-4">
                                                  {{ "Product.CheckIsEmail" | translate }}
                                              </mat-checkbox>
                                              </div>
                                          </div>
                                          <div class="row my-2">
                                              <div class="col-6 col-md-6">
                                                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.Money ||  formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                                                      formControlName="bIsValueMax" class="my-4">
                                                      {{ "Product.MaximumValue" | translate }}
                                                  </mat-checkbox>
                                              </div>
                                              <div class="col-6 col-md-6">
                                                  <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Money ||  formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                                                      appearance="outline" class="w-100 mb-2">
                                                      <mat-label>
                                                          {{ "Product.EnterMaximumValue" | translate }}
                                                      </mat-label>
                                                      <input matInput type="number" formControlName="vValueMax" />
                                                      <mat-error *ngIf="formField.get('vValueMax')?.value < formField.get('vValueMin')?.value">
                                                          {{
                                                          "Product.MaximumSizeMustBeGreaterThanMinimumSize" | translate
                                                          }}
                                                      </mat-error>
                                                      <mat-error *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                                          <span *ngIf="utilsSvc.isControlHasError(formField, 'vValueMax', 'required')">
                                                              {{ 'ThisFieldIsRequired' | translate }}
                                                          </span>
                                                        </mat-error>
                                                  </mat-form-field>
                                              </div>
                                          </div>
                                          <div class="row my-2">
                                              <div class="col-6 col-md-6">
                                                  <mat-checkbox
                                                      *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                      formField.get('fkIIdFieldType')?.value === typeField.Money ||  formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                                                      formControlName="bIsValueMin" class="my-4">
                                                      {{ "Product.MinimumValue" | translate }}
                                                  </mat-checkbox>
                                              </div>
                                              <div class="col-6 col-md-6">
                                                  <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Alphanumeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Numeric ||
                                                  formField.get('fkIIdFieldType')?.value === typeField.Money ||  formField.get('fkIIdFieldType')?.value === typeField.Decimal"
                                                      appearance="outline" class="w-100 mb-2">
                                                      <mat-label>
                                                          {{ "Product.EnterMinimumValue" | translate }}
                                                      </mat-label>
                                                      <input matInput type="number" formControlName="vValueMin" />
                                                      <mat-error *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                                          <span *ngIf="utilsSvc.isControlHasError(formField, 'vValueMin', 'required')">
                                                              {{ 'ThisFieldIsRequired' | translate }}
                                                          </span>
                                                      </mat-error>
                                                  </mat-form-field>
                                              </div>
                                          </div>
                                      </mat-card-content>
                                  </mat-card>
                              </ng-container>

                                      <!--Extension availables for upload a file -->

                                      <mat-form-field class="w-100" *ngIf="
                                      formField.get('fkIIdFieldType')?.value ===
                                      typeField.UploadDocuments">
                                          <mat-label>{{ "Product.TypeFile" | translate }}</mat-label>
                                          <div class="input-container">
                                              <input matInput formControlName="selectedFileTypes" [readonly]="true"
                                                  (click)="open('typeUpload')" placeholder="Seleccione los tipos de archivo" />
                                              <button mat-icon-button (click)="open('typeUpload')">
                                                  <mat-icon iconPositionEnd fontIcon="edit"></mat-icon>
                                              </button>
                                          </div>
                                      </mat-form-field>

                                      <mat-checkbox *ngIf="
                                  formField.get('fkIIdFieldType')?.value ===
                                  typeField.UploadDocuments
                              " formControlName="bAllowMultipleUploads" class="my-4">
                                          {{ "Product.MultipleFile" | translate }}
                                      </mat-checkbox>

                                      <!-- Configuration for field type radio -->
                                      <ng-container *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Radio">
                                          <mat-form-field appearance="outline" class="w-100 mb-2">
                                              <mat-label>
                                                  {{ "Product.OptionNumber" | translate }}
                                              </mat-label>
                                              <input (focusout)="updateOptions()" matInput type="number" formControlName="options" />
                                          </mat-form-field>
                                          <div formArrayName="optionValues">
                                              <div *ngFor="let option of orderItems.controls; let i = index">
                                                  <mat-form-field appearance="outline" class="w-100 mb-2">
                                                      <mat-label>
                                                          {{ "Product.Option" | translate }} {{ i + 1 }}
                                                      </mat-label>
                                                      <input id="optionInput-{{ i }}" matInput type="text" [formControlName]="i"
                                                          (focusout)="updateOptionValue(i, $event.target)" />
                                                  </mat-form-field>
                                              </div>
                                          </div>
                                      </ng-container>


                                      <mat-form-field *ngIf="formField.get('bHelpText')?.value" appearance="outline"
                                          class="w-100 mb-2">
                                          <mat-label>
                                              {{ "Product.HelpText" | translate }}
                                          </mat-label>
                                          <input matInput type="text" formControlName="vHelpText" [maxlength]="maxHelpTextLength" PreventionSqlInjector/>
                                      </mat-form-field>

                                      <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                          appearance="outline" class="select-look w-100">
                                          <mat-label>
                                              {{ "Product.FormatDate" | translate }}
                                          </mat-label>
                                          <mat-select formControlName="vFormat" [required]="
                                  formField.get('fkIIdFieldType')?.value === typeField.Date
                                  ">
                                              <mat-option *ngFor="let item of typeFormatDate" [value]="item.format">
                                                  {{ item.format }}
                                              </mat-option>
                                          </mat-select>
                                      </mat-form-field>
                                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                          formControlName="bShowYear" class="my-4">
                                          {{ "Product.ShowYear" | translate }}
                                      </mat-checkbox>
                                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                          formControlName="bShowMonth" class="my-4">
                                          {{ "Product.ShowMonth" | translate }}
                                      </mat-checkbox>
                                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                          formControlName="bShowDay" class="my-4">
                                          {{ "Product.ShowDay" | translate }}
                                      </mat-checkbox>
                                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                          formControlName="bShowHour" class="my-4">
                                          {{ "Product.ShowHour" | translate }}
                                      </mat-checkbox>

                              <ng-container *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date">
                                  <mat-card>
                                      <mat-card-title><strong>{{ "Product.FieldRules" | translate }}</strong> </mat-card-title>
                                      <mat-card-content>
                                          <div class="row my-3">
                                              <div class="col-4 col-md-4">
                                                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                                      formControlName="bIsMinDateRequerid" class="my-4">
                                                      {{ "Product.MinimumDateRequired" | translate }}
                                                  </mat-checkbox>
                                              </div>
                                              <div class="col-4 col-md-4">
                                                  <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" appearance="outline"
                                                      class="select-look w-100">
                                                      <mat-label>
                                                          {{ "Product.Type" | translate }}
                                                      </mat-label>
                                                      <mat-select formControlName="vTypeDateMin" (ngModelChange)="onChangeTypeMinDate($event)" [required]="formField.get('fkIIdFieldType')?.value === typeField.Date">
                                                          <mat-option *ngFor="let item of typeDateMinFilter" [value]="item.Name">
                                                              {{ item.Name }}
                                                          </mat-option>
                                                      </mat-select>
                                                  </mat-form-field>
                                              </div>
                                              <div class="col-4 col-md-4">
                                                  <!-- Campo de fecha -->
                                                  <mat-form-field appearance="outline" class="w-100 mb-2">
                                                      <mat-label> {{ "Product.EnterMinimumDate" | translate }} </mat-label>
                                                      <input matInput [mode]="modeDatePicker" [dpDayPicker]="config" theme="dp-material" formControlName="dMinDateRequerid"
                                                      [placeholder]="placeholderText">
                                                      <mat-icon class="click"  *ngIf="!clockTypeIcon"  mat-icon-button  matSuffix>today</mat-icon>
                                                      <mat-icon class="click"  *ngIf="clockTypeIcon"  mat-icon-button  matSuffix>query_builder</mat-icon>
                                                      <mat-error *ngIf="formField.invalid && (formField.dirty || formField.touched)">
                                                          <span *ngIf="utilsSvc.isControlHasError(formField, 'dMinDateRequerid', 'required')">
                                                              {{ 'ThisFieldIsRequired' | translate }}
                                                          </span>
                                                      </mat-error>
                                                      <mat-error *ngIf="formField.get('dMinDateRequerid')?.hasError('dateRangeInvalid')">
                                                          {{ "Product.MinimumDateWarning" | translate }}
                                                      </mat-error>
                                                  </mat-form-field>
                                              </div>
                                          </div>
                                          <div class="row my-3">
                                              <div class="col-4 col-md-4">
                                                  <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date"
                                                      formControlName="bIsMaxDateRequerid" class="my-4">
                                                      {{ "Product.MaximumDateRequired" | translate }}
                                                  </mat-checkbox>
                                              </div>
                                              <div class="col-4 col-md-4">
                                                  <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Date" appearance="outline"
                                                      class="select-look w-100">
                                                      <mat-label>
                                                          {{ "Product.Type" | translate }}
                                                      </mat-label>
                                                      <mat-select formControlName="vTypeDateMax" (ngModelChange)="onChangeTypeMaxDate($event)" [required]="formField.get('fkIIdFieldType')?.value === typeField.Date">
                                                          <mat-option *ngFor="let item of typeDateMaxFilter" [value]="item.Name">
                                                              {{ item.Name }}
                                                          </mat-option>
                                                      </mat-select>
                                                  </mat-form-field>
                                              </div>
                                              <div class="col-4 col-md-4">
                                                  <!-- Campo de fecha -->
                                                  <mat-form-field appearance="outline" class="w-100 mb-2">
                                                      <mat-label> {{ "Product.EnterMaxiumDate" | translate }}</mat-label>
                                                      <input matInput [mode]="modeDatePicker" [dpDayPicker]="config" theme="dp-material" formControlName="dMaxDateRequerid"
                                                      [placeholder]="placeholderText">
                                                      <mat-icon class="click"  *ngIf="!clockTypeIcon"  mat-icon-button  matSuffix>today</mat-icon>
                                                      <mat-icon class="click"  *ngIf="clockTypeIcon"  mat-icon-button  matSuffix>query_builder</mat-icon>
                                                      <mat-error>
                                                          <span *ngIf="utilsSvc.isControlHasError(formField, 'dMaxDateRequerid', 'required')">
                                                              {{ 'ThisFieldIsRequired' | translate }}
                                                          </span>
                                                      </mat-error>
                                                      <mat-error *ngIf="formField.get('dMaxDateRequerid')?.hasError('dateRangeInvalid')">
                                                          {{ "Product.MaximumDateWarning" | translate }}
                                                      </mat-error>
                                                  </mat-form-field>
                                              </div>
                                          </div>
                                      </mat-card-content>
                                  </mat-card>
                              </ng-container>

                              <ng-container *ngIf="formField.get('bIsExternalLink')?.value">
                                  <p>{{ "FormConfiguration.Fields.linkExterno" | translate }}</p>
                                  <mat-label>
                                    {{ "FormConfiguration.Fields.SetLinkValue" | translate }}
                                  </mat-label>
                                  <p>
                                    <mat-radio-group formControlName="iLink">
                                      <mat-radio-button [value]="1">{{ "FormConfiguration.Fields.IsDocument" | translate }}</mat-radio-button>
                                      <mat-radio-button [value]="2">{{ "FormConfiguration.Fields.IsLink" | translate }}</mat-radio-button>
                                    </mat-radio-group>
                                  </p>
                                    <ng-container *ngIf="formField.get('iLink')?.value === 1">
                                      <div class="upload-container" matTooltip="Solo se permiten archivos PDF">
                                        <div class="upload-area" [ngClass]="{ 'dragover': isDragOver }" (drop)="onFileDropped($event)" (dragover)="onDragOver($event)"
                                        (dragleave)="isDragOver = false">
                                          <input type="file" (onChangeValidated)="onFileSelected($event)" id="fileUpload"
                                            accept="application/pdf" hidden  ValidationInputFile />
                                          <label for="fileUpload"  class="upload-label" [ngClass]="{ 'uploaded': showBtnDelete }">
                                            <mat-icon  class="mb-5"
                                              [style.color]="showBtnDelete ? 'green' : 'inherit'"
                                              style="overflow: visible; margin-right: 23px"
                                              >{{ showBtnDelete || fileName ? 'check_circle' : 'cloud_upload' }}</mat-icon
                                            >
                                            <p *ngIf="showBtnDelete || fileName">{{ fileName }}</p>

                                            <p *ngIf="!showBtnDelete && !fileName">
                                              Haga clic para cargar o arrastre y suelte aquí los archivos
                                            </p>
                                            <span>PDF</span>
                                          </label>
                                        </div>
                                        <p class="upload-info">
                                          Tamaño máximo: 20 MB. Sólo permite un archivo.
                                        </p>
                                      </div>
                                    </ng-container>

                                    <ng-container *ngIf="formField.get('iLink')?.value === 2">
                                      <mat-label>Link</mat-label>
                                      <mat-form-field class="w-100">
                                        <input matInput type="text" formControlName="vExternalLink"  placeholder="https://example.com"/>
                                      </mat-form-field>
                                    </ng-container>

                                </ng-container>

                                      <mat-form-field *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Money"
                                          appearance="outline" class="select-look w-100">
                                          <mat-label>
                                              {{ "Product.SelectCoin" | translate }}
                                          </mat-label>
                                          <mat-select formControlName="vFormat" [required]="
                                  formField.get('fkIIdFieldType')?.value === typeField.Money
                                  ">
                                              <mat-option *ngFor="let item of typeFormatCoin" [value]="item.format">
                                                  {{ item.format }}
                                              </mat-option>
                                          </mat-select>
                                      </mat-form-field>
                                      <mat-checkbox *ngIf="formField.get('fkIIdFieldType')?.value === typeField.Money"
                                          formControlName="bShowCoin" class="my-4">
                                          {{ "Product.ShowCoin" | translate }}
                                      </mat-checkbox>

                                      <ng-container *ngIf="formField.get('bisCheckField')?.value && formField.get('bisCheckField')?.value">
                                          <br/>
                                          <mat-label> {{ "Product.checkAprobacion" | translate }}</mat-label>
                                          <p>{{ "Product.textcheckaprobacion" | translate }}</p>
                                          <mat-form-field class="select-look w-100">
                                              <mat-label>{{ "Select options" | translate }}</mat-label>
                                              <mat-select formControlName="OptionsChecks" [multiple]="true">
                                                <mat-option *ngFor="let option of optionsChecks" [value]="option.pkIIdCheck">
                                                  {{ option.vNameCheck }}
                                                </mat-option>
                                              </mat-select>
                                          </mat-form-field>
                                      </ng-container>
                          </form>
                      </div>
                  </div>
              <!-- botones -->
              <ng-container customButtonRight>
                  <div class="button-container">
                      <a class="w-auto mr-3" mat-button (click)="closeModal()" style="margin: 10px;"><span
                              class="label-button">{{'Cancel'|
                              translate}}</span></a>

                      <button *ngIf="isEditingField" class="w-auto mr-3" type="button" mat-raised-button (click)="deleteField()">
                          {{ ["Product.DeleteCharacteristics" | translate] }}
                          <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
                      </button>
                      <button class="w-auto" type="button" mat-raised-button color="primary" (click)="saveField()">
                          {{
                          isEditingField
                          ? ["Product.UpdateCharacteristics" | translate]
                          : ["Product.SaveCharacteristics" | translate]
                          }}
                          <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
                      </button>
                  </div>
              </ng-container>
          </mat-tab>
          <mat-tab [disabled]="!isFieldDisabledTabRuleField()" [label]="'Product.FieldRules' | translate">
              <app-calculation-formulas [idField]="idFieldModule" [idForm]="idForm" [isModule]="0"
                  (getFieldsByForm)="getFieldsByForm(idForm)"></app-calculation-formulas>
          </mat-tab>
      </mat-tab-group>
      </ng-container>
  </app-modal2>
</ng-template>



<ng-template #typeUpload>
  <app-modal2 (closeModal)="closeModal()" [titleModal]="'Product.TypeFile' | translate">
      <ng-container body>
          <div class="row">
              <div class="col-4 form-check" *ngFor="let fileType of fileTypes">
                  <input class="form-check-input" type="checkbox" [checked]="isSelected(fileType.Name)"
                      (change)="toggleFileType(fileType.Name)" />
                  <label class="form-check-label">{{ fileType.Name }}</label>
              </div>
          </div>
      </ng-container>
      <ng-container customButtonRight>
          <button mat-raised-button color="primary" type="button" class="" (click)="saveInfo()">
              {{ "Save" | translate }}
          </button>
      </ng-container>
  </app-modal2>
</ng-template>
