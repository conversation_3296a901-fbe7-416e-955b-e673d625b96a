import { Routes } from "@angular/router";
import { RolesComponent } from "./roles.component";

export default [
 {
  path:'',
  component: RolesComponent,
  children: [
    {path: '', loadComponent:()=> import('./all-roles/all-roles.component').then(c=>c.AllRolesComponent)},
    {path: 'new', loadComponent:()=> import('./edit-new-role/edit-new-role.component').then(c=>c.EditNewRoleComponent)},
    {path: 'modify/:id', loadComponent:()=> import('./edit-new-role/edit-new-role.component').then(c=>c.EditNewRoleComponent)}    
  ]
 }
] as Routes;