.listdrop {
  width: 100%;
  max-width: 100%;
  border: solid 1px #ccc;
  min-height: 20%;
  display: block;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
}

.style-box {
  padding: 1em 1em;
  border-bottom: solid 1px #ccc;
  color: rgba(0, 0, 0, 0.87);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: move;
  background: white;
  font-size: 1em;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4em;
  box-shadow: 0 5em 5em -3em rgba(0, 0, 0, 0.2), 0 8em 10em 1em rgba(0, 0, 0, 0.14), 0 3em 14em 2em rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box:last-child {
  border: none;
}

.listdrop.cdk-drop-list-dragging .style-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
