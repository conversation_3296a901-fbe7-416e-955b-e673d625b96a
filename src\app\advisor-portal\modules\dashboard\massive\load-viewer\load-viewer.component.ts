import { CommonModule } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { PageEvent } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  BulkDataModel,
  BulkLoadTypeModel,
  RequestFilterBulksModel,
} from 'src/app/shared/models/massive';
import { OptionModel } from 'src/app/shared/models/options/optionmodel';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { GlobalSelectModel } from 'src/app/shared/models/shared';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-load-viewer',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    Modal2Component,
  ],
  templateUrl: './load-viewer.component.html',
  styleUrls: ['./load-viewer.component.scss'],
})
export class LoadViewerComponent implements OnInit {
  //Variables relacionadas con los modales.
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;

  //variables del formulario.
  formFilter: FormGroup = new FormGroup({});
  idBusinessByCountry: number = 0;
  idUser: number = 0;
  isInProccess: boolean = true;
  keywordInProgress: string = '';
  keywordFinished: string = '';
  bulkLoadType: BulkLoadTypeModel[] = [];
  userList: OptionModel[] = [];
  typeBulkUploadProcess: GlobalSelectModel[] = [];

  //Request para filtrar la tabla de cargas en proceso.
  requestFilterInProgress: RequestFilterBulksModel = {
    keyword: '',
    idBusinessCountry: 0,
    page: 0,
    pageSize: 5,
    order: 'desc',
    typeManagement: 0,
    userId: 0,
    typeState: [1, 2], // 1 y 2 son los tipos de estados 'Sin inicar' y 'En proceso'.
    isInProccess: true,
  };

  //Request para filtrar la tabla de cargas finalizadas.
  requestFilterFinished: RequestFilterBulksModel = {
    keyword: '',
    idBusinessCountry: 0,
    page: 0,
    pageSize: 5,
    order: 'desc',
    typeManagement: 0,
    userId: 0,
    typeState: [3, 4, 5], // 3, 4, y 5 son los tipos de estados 'Carga parcial', 'Carga finalizada' y 'Carga fallida'.
    isInProccess: false,
  };

  //Variables relacioandas con la tabla cargas en proceso.
  amountRowsInProcess: number = 0;
  pageIndexInProcess: number = 0;
  pageSizeInProcess: number = 5;
  currentPositionInProcess: number = 0;
  ongoingProcesses: number = 0;
  showTableInProcess: boolean = true;
  dataTableLoadViewerInProcess: BulkDataModel[] = [];
  estructTableLoadViewerPolicyInProcess: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.LoadID'
      ),
      columnValue: 'idBulk',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.ManagementOfType'
      ),
      columnValue: 'typeOfManagement',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.ResponsibleUser'
      ),
      columnValue: 'user',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.LoadDate'
      ),
      columnValue: 'dateBulk',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'state',
      functionValue: (item: any) => this.colorChangeByState(item),
    },
  ];

  //Variables relacioandas con la tabla cargas finalizadas.
  amountRowsFinished: number = 0;
  pageIndexFinished: number = 0;
  pageSizeFinished: number = 5;
  currentPositionFinished: number = 0;
  showTableFinished: boolean = true;
  dataTableLoadViewerFinished: BulkDataModel[] = [];
  estructTableLoadViewerPolicyFinished: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.LoadID'
      ),
      columnValue: 'idBulk',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.ManagementOfType'
      ),
      columnValue: 'typeOfManagement',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.ResponsibleUser'
      ),
      columnValue: 'user',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.LoadDate'
      ),
      columnValue: 'dateBulk',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'state',
      functionValue: (item: any) => this.colorChangeByState(item),
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.Table.ProcessingTime'
      ),
      columnValue: 'time',
    },
    {
      columnLabel: this._translateService.instant(
        'BulkUpload.LoadViewer.LoadsCompleted.ResultsLog'
      ),
      columnValue: 'logResult',
      columnIcon: 'download',
    },
  ];

  constructor(
    private _messageService: MessageService,
    private _fb: FormBuilder,
    private _utilsSvc: UtilsService,
    public matDialog: MatDialog,
    private _translateService: TranslateService,
    private _activatedRoute: ActivatedRoute,
    private _customeRouter: CustomRouterService,
    private _parametersService: ParametersService,
    private _fileService: FileService,
    private _userService: UserService
  ) {
    this.getBusinessByCountry();
  }

  async ngOnInit() {
    this.idUser = await this._userService.getUserIdSesion();
    this.requestFilterInProgress.userId = this.idUser;
    this.requestFilterFinished.userId = this.idUser;
    this.filterGetBulks(this.requestFilterInProgress, true);
    this.filterGetBulks(this.requestFilterFinished, false);
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //Traducción de la tabla Cargas en proceso.
      this.estructTableLoadViewerPolicyInProcess[0].columnLabel =
        this._translateService.instant('BulkUpload.LoadViewer.Table.LoadID');
      this.estructTableLoadViewerPolicyInProcess[1].columnLabel =
        this._translateService.instant(
          'BulkUpload.LoadViewer.Table.ManagementOfType'
        );
      this.estructTableLoadViewerPolicyInProcess[2].columnLabel =
        this._translateService.instant(
          'BulkUpload.LoadViewer.Table.ResponsibleUser'
        );
      this.estructTableLoadViewerPolicyInProcess[3].columnLabel =
        this._translateService.instant('BulkUpload.LoadViewer.Table.LoadDate');
      this.estructTableLoadViewerPolicyInProcess[4].columnLabel =
        this._translateService.instant('Status');

      //Traducción de la tabla Cargas finalizadas.
      this.estructTableLoadViewerPolicyFinished[0].columnLabel =
        this._translateService.instant('BulkUpload.LoadViewer.Table.LoadID');
      this.estructTableLoadViewerPolicyFinished[1].columnLabel =
        this._translateService.instant(
          'BulkUpload.LoadViewer.Table.ManagementOfType'
        );
      this.estructTableLoadViewerPolicyFinished[2].columnLabel =
        this._translateService.instant(
          'BulkUpload.LoadViewer.Table.ResponsibleUser'
        );
      this.estructTableLoadViewerPolicyFinished[3].columnLabel =
        this._translateService.instant('BulkUpload.LoadViewer.Table.LoadDate');
      this.estructTableLoadViewerPolicyFinished[4].columnLabel =
        this._translateService.instant('Status');
      this.estructTableLoadViewerPolicyFinished[5].columnLabel =
        this._translateService.instant(
          'BulkUpload.LoadViewer.Table.ProcessingTime'
        );
      this.estructTableLoadViewerPolicyFinished[6].columnLabel =
        this._translateService.instant(
          'BulkUpload.LoadViewer.LoadsCompleted.ResultsLog'
        );
    });
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
        if (this.idBusinessByCountry > 0) {
          this.requestFilterInProgress.idBusinessCountry =
            this.idBusinessByCountry;
          this.requestFilterFinished.idBusinessCountry =
            this.idBusinessByCountry;
        }
      }
    });
  }

  //Delcaración del formulario formFilter.
  initFilterForm() {
    this.formFilter = this._fb.group({
      typeManagement: null,
      typeState: [],
      userId: null,
    });
  }

  //Obtiene todos los usuarios disponibles para filtrar las cargas por idBussinesByCountry.
  getUserByBulk() {
    this._fileService
      .getUserByBulk(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.userList = resp.result;
        }
      });
  }

  //Obtiene todos los tipos de gestiones registrados en el sistema.
  getManagement(idsState: number[]) {
    this._fileService
      .getManagement(idsState)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.bulkLoadType = resp.result;
        }
      });
  }

  //Obtiene todos los tipos de gestiones registrados en el sistema.
  getAllTypeBulkUploadProcess(isInProccess: boolean) {
    this._parametersService
      .getParameters('Type_of_Bulk_Upload_Process')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          if (isInProccess) {
            this.typeBulkUploadProcess = resp.filter(
              (item: GlobalSelectModel) => item.value === 1 || item.value === 2
            );
          } else {
            this.typeBulkUploadProcess = resp.filter(
              (item: GlobalSelectModel) =>
                item.value === 3 || item.value === 4 || item.value === 5
            );
          }
        } else {
        }
      });
  }

  //Función que se ejecuta al dar enter o click en el icono de busqueda, del input buscar.
  search(keyword: string, isInProccess: boolean) {
    if (isInProccess) {
      this.requestFilterInProgress.keyword = keyword;
      this.requestFilterInProgress.page = 0;
      this.filterGetBulks(this.requestFilterInProgress, true);
    } else {
      this.requestFilterFinished.keyword = keyword;
      this.requestFilterFinished.page = 0;
      this.filterGetBulks(this.requestFilterFinished, false);
    }
  }

  //Controlador de las acciones configuradas en la tabla cargas finalizadas.
  controllerLoadViewerFinished(event: IconEventClickModel) {
    switch (event.column) {
      case 'logResult':
        this.getFileByIdList([event.value.idUpload]);
        break;
      default:
        break;
    }
  }

  //Metodo que descarga un archivo regiustrado en el sistema por medio del idFile o una lista de idFile.
  getFileByIdList(idFiles: Array<number>) {
    this._fileService
      .GetFileByIdList(idFiles)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          return of([]);
        })
      )
      .subscribe((resp: any) => {
        if (Array.isArray(resp)) {
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('Warning') + resp.message
            );
          } else {
            // Intenta obtener el nombre del archivo desde la cabecera Content-Disposition
            const fileName = resp.headers.get('X-File-Name') || 'file.zip';
            // Especifica el tipo MIME del archivo según el tipo que se espera
            const mimeType = 'application/octet-stream';
            const blob = new Blob([resp.body], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = fileName;
            link.href = url;
            link.click();
            window.URL.revokeObjectURL(url);
          }
        }
      });
  }

  //Función que abre el modal para filtrar de filtros.
  openFilterInProcess(isInProccess: boolean) {
    this.isInProccess = isInProccess;
    this.initFilterForm();
    this.isInProccess
      ? this.getManagement([1, 2])
      : this.getManagement([3, 4, 5]);
    this.getUserByBulk();
    this.isInProccess
      ? this.getAllTypeBulkUploadProcess(true)
      : this.getAllTypeBulkUploadProcess(false);
    const dialogRef = this.matDialog.open(this.filtersModal!, {
      width: '30vw',
      maxHeight: '90vh',
    });
    if (isInProccess) {
      this.formFilter
        .get('typeManagement')
        ?.setValue(this.requestFilterInProgress.typeManagement);
      this.formFilter
        .get('userId')
        ?.setValue(this.requestFilterInProgress.userId);
      this.formFilter
        .get('typeState')
        ?.setValue(this.requestFilterInProgress.typeState);
    } else {
      this.formFilter
        .get('typeManagement')
        ?.setValue(this.requestFilterFinished.typeManagement);
      this.formFilter
        .get('userId')
        ?.setValue(this.requestFilterFinished.userId);
      this.formFilter
        .get('typeState')
        ?.setValue(this.requestFilterFinished.typeState);
    }
  }

  //Detecta los cambios en la paginación de la tabla cargas en proceso.
  onPageChangeInProcess(event: PageEvent) {
    this.pageIndexInProcess = event.pageIndex;
    this.pageSizeInProcess = event.pageSize;
    this.currentPositionInProcess =
      this.pageIndexInProcess * this.pageSizeInProcess;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndexInProcess < 0) {
      this.pageIndexInProcess = 0;
    }
    this.requestFilterInProgress.page = this.currentPositionInProcess;
    this.requestFilterInProgress.pageSize = this.pageSizeInProcess;

    let payload: RequestFilterBulksModel = this.requestFilterInProgress;
    this.filterGetBulks(payload, true);
  }

  //Detecta los cambios en la paginación de la tabla cargas en finalizadas.
  onPageChangeFinished(event: PageEvent) {
    this.pageIndexFinished = event.pageIndex;
    this.pageSizeFinished = event.pageSize;
    this.currentPositionFinished =
      this.pageIndexFinished * this.pageSizeFinished;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndexFinished < 0) {
      this.pageIndexFinished = 0;
    }
    this.requestFilterFinished.page = this.currentPositionFinished;
    this.requestFilterFinished.pageSize = this.pageSizeFinished;

    let payload: RequestFilterBulksModel = this.requestFilterFinished;
    this.filterGetBulks(payload, false);
  }

  //Función que aplica los filtros seleccionados en el modal de filtros generales.
  applyFilters() {
    if (this.isInProccess) {
      this.pageIndexInProcess = 0;
      this.requestFilterInProgress.page = 0;
      this.requestFilterInProgress.pageSize = this.pageSizeInProcess;
      this.requestFilterInProgress.keyword = this.keywordInProgress;
      this.requestFilterInProgress.typeManagement =
        this.formFilter.get('typeManagement')?.value;
      this.requestFilterInProgress.userId =
        this.formFilter.get('userId')?.value;
      this.requestFilterInProgress.typeState =
        this.formFilter.get('typeState')?.value;
      if (this.requestFilterInProgress.typeState.length == 0) {
        this.requestFilterInProgress.typeState = [1, 2];
      }
      this.filterGetBulks(this.requestFilterInProgress, true);
    } else {
      this.pageIndexFinished = 0;
      this.requestFilterFinished.page = 0;
      this.requestFilterFinished.pageSize = this.pageSizeFinished;
      this.requestFilterFinished.keyword = this.keywordFinished;
      this.requestFilterFinished.typeManagement =
        this.formFilter.get('typeManagement')?.value;
      this.requestFilterFinished.userId = this.formFilter.get('userId')?.value;
      this.requestFilterFinished.typeState =
        this.formFilter.get('typeState')?.value;
      if (this.requestFilterFinished.typeState.length == 0) {
        this.requestFilterFinished.typeState = [3, 4, 5];
      }
      this.filterGetBulks(this.requestFilterFinished, false);
    }
  }

  //Función que borra los filtros aplicados a el formulario formFilter.
  cleanFilterForm() {
    if (this.isInProccess) {
      this.formFilter.reset();
      this.bulkLoadType = [];
      this.userList = [];
      this.typeBulkUploadProcess = [];
      this.requestFilterInProgress = {
        keyword: '',
        idBusinessCountry: this.idBusinessByCountry,
        page: 0,
        pageSize: this.pageSizeInProcess,
        order: 'desc',
        typeManagement: 0,
        userId: 0,
        typeState: [1, 2], // 1 y 2 son los tipos de estados 'Sin inicar' y 'En proceso'.
        isInProccess: true,
      };
      this.filterGetBulks(this.requestFilterInProgress, true);
    } else {
      this.formFilter.reset();
      this.bulkLoadType = [];
      this.userList = [];
      this.typeBulkUploadProcess = [];
      this.requestFilterFinished = {
        keyword: '',
        idBusinessCountry: this.idBusinessByCountry,
        page: 0,
        pageSize: this.pageSizeFinished,
        order: 'desc',
        typeManagement: 0,
        userId: this.idUser,
        typeState: [3, 4, 5], // 3, 4, y 5 son los tipos de estados 'Carga parcial', 'Carga finalizada' y 'Carga fallida'.
        isInProccess: false,
      };
      this.filterGetBulks(this.requestFilterFinished, false);
    }
  }

  //Aplica el filtro seleccionado en el ódulo de cargas, según la tabla seleccionada.
  filterGetBulks(request: RequestFilterBulksModel, isInProccess: boolean) {
    this._fileService
      .getBulks(request)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          if (isInProccess) {
            this.dataTableLoadViewerInProcess = resp.result.item1;
            this.amountRowsInProcess = resp.result.item2;
            this.matDialog.closeAll();
            this.ongoingProcesses = this.getOngoingProcesses(
              this.dataTableLoadViewerInProcess
            );
          } else {
            this.dataTableLoadViewerFinished = resp.result.item1;
            this.amountRowsFinished = resp.result.item2;
            this.matDialog.closeAll();
          }
        }
      });
  }

  //Función que aplica un estilo y color a los estados de las tablas, basado en el tipo de estado que tenga cada item.
  colorChangeByState(item: BulkDataModel) {
    switch (item.state) {
      case 1:
        item.Color = 'Amarillo';
        item.Estado = this._translateService.instant(
          'BulkUpload.LoadViewer.Table.NotStarted'
        );
        return this._utilsSvc.changeStatusTaskValue(item);
      case 2:
        item.Color = 'Naranja';
        item.Estado = this._translateService.instant(
          'BulkUpload.LoadViewer.Table.InProgress'
        );
        return this._utilsSvc.changeStatusTaskValue(item);
      case 3:
        item.Color = 'Verde';
        item.Estado = this._translateService.instant(
          'BulkUpload.LoadViewer.Table.ProcessedLoad'
        );
        return this._utilsSvc.changeStatusTaskValue(item);
      case 4:
        item.Color = 'Naranja';
        item.Estado = this._translateService.instant(
          'BulkUpload.LoadViewer.Table.PartialLoad'
        );
        return this._utilsSvc.changeStatusTaskValue(item);
      case 5:
        item.Color = 'Rojo';
        item.Estado = this._translateService.instant(
          'BulkUpload.LoadViewer.Table.LoadFailed'
        );
        return this._utilsSvc.changeStatusTaskValue(item);
      default:
        return this._utilsSvc.changeStatusTaskValue('');
    }
  }

  //Calcula la cantidad de registros que se encuentran con estado 'En proceso', dentro de la tabla 'Cargas en proceso'.
  getOngoingProcesses(data: BulkDataModel[]): number {
    return data.filter((item) => item.state === 2).length;
  }

  //Regresa al menú principal de masivos.
  goBackMassive() {
    this._customeRouter.navigate([`dashboard/massive`]);
  }
}
