import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import {
  MatDatepickerInputEvent,
  MatDatepickerModule,
} from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { IReport } from 'src/app/shared/models/reports/new-report-report-generate.model';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ReportService } from 'src/app/shared/services/reports/report.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { FileService } from 'src/app/shared/services/file/file.service';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { ExcelService } from 'src/app/shared/services/excel/excel.service';
import { GroupModel } from 'src/app/shared/models/groups/group.model';
import { RolesListModel } from 'src/app/shared/models/role/roles-list.model';
import { UserListModel, UserModel } from 'src/app/shared/models/user';
import { GroupsService } from 'src/app/shared/services/groups/groups.service';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { FieldComponent } from "../../../../../shared/components/field-generator/field.component";
import { FieldService } from 'src/app/shared/services/field/field.service';
import { FieldTypeModel } from 'src/app/shared/models/field/field-type.model';
import { FieldModel } from 'src/app/shared/models/field/field.model';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { CatalogSettingService } from 'src/app/shared/services/catalog-setting/catalog-setting.service';
import { BodyTableModel } from 'src/app/shared/models/table';
import { REPORTS_FILTERS_SELECT_POLICY } from 'src/app/shared/services/constants';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';

@Component({
  selector: 'app-reports-generated',
  standalone: true,
  templateUrl: './reports-generated.component.html',
  styleUrls: ['./reports-generated.component.scss'],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    TranslateModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatGridListModule,
    FieldComponent,
    TableComponent
],
})
export class ReportsGeneratedComponent implements OnInit {
  listReportBd: IReport[] = [];
  report: any[] = [];
  //Table related variables.
  dataReportTable: any[] = [];
  estructReportTable: BodyTableModel[] = [];

  idBusinessByCountry: number = 0;

  disabledBtnCrud: boolean = true;
  colorBtnGenerate: string = '';
  colorEdit: string = '';

  disabledBtnGenerate: boolean = true;
  catalogsByFieldName: any = {}
  form: FormGroup;
  formAdditionalFilters: FormGroup;
  formFilters: FormGroup = new FormGroup({})

  initialDate: Date | null | undefined;
  finalDate: Date | null | undefined;
  fieldFilters: any[] = []
  moduleFilters: any[] = []
  listAllFilters: any[] = []
  responseFilters: any = {}

  // new
  CheckFilterRoleUserReport: boolean = true;
  groupTable: GroupModel[] = [];
  roleList: RolesListModel[] = [];
  tempUsersList: UserListModel[] = [];
  filterTypeList: FieldTypeModel[] = [];
  filterTypeDateList: FieldTypeModel[] = [];
  listFieldFilter: any[] = [];
  listFieldFilterDate: any[] = [];
  
  
  constructor(
    public utilsService: UtilsService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    public _reportSvc: ReportService,
    private _customRouter: CustomRouterService,
    private _settingService: SettingService,
    public _fileSvc: FileService,
    private excelService: ExcelService,
    private _groupsService: GroupsService,
    private _roleService: RoleService,
    private _userService: UserService,
    private _fieldServices: FieldService,
    private _moduleService: ModuleService,
    private _catalogService: CatalogSettingService,
    private _transactionService: TransactionService
  ) {
    this.form = new FormGroup({
      fkIIdReport: new FormControl({ value: '', disabled: false }),
    });
    this.formAdditionalFilters = new FormGroup({
      listGroupBussines: new FormControl({ value: '', disabled: false }),
      listRoleBusiness: new FormControl({ value: '', disabled: false }),
      listUserBussiens: new FormControl({ value: '', disabled: false }),
    })

  }

  ngOnInit(): void {
    this.getBusinessByCountry();

    this.form.get('fkIIdReport')?.valueChanges.subscribe({
      next: (report => {
        if (report != 0)
          {
            this._setFilterByReport(report);
            this.report = [];
            this.estructReportTable = [];
            this.dataReportTable = [];
          }
      })
    })

    this.loadInitialData();
    this.getFieldType()
  }

  async _getFieldData(idField: number, typeField: 'field' | 'module'): Promise<any> {
    return new Promise((resolve) => {
      let service = typeField == 'field' ? this._fieldServices.getFieldById(idField) : this._moduleService.getFieldById(idField)
      service.subscribe(
        (response) => {
          if (response.result) {
            resolve(response.result);
          }
          resolve('');
        },
        (error) => {
          resolve('');
        }
      );
    });
    
  }
  private _setFilterByReport(idReport: string){
    this._reportSvc.getFiltersReport(idReport).subscribe({
      next: (async response => {
        let filters = []
        this.responseFilters = response.result;
        let filterModules = response.result.dynamicFilters
          .filter((m: { bIsStandard: boolean; bIsToField: boolean; }) => (m.bIsToField == false || null) && !m.bIsStandard)
        for (const f of filterModules) {
          filters.push(await this._getFieldData(Number(f.fkIIdField), 'module'));
        }
          
        let filterFields = response.result.dynamicFilters
          .filter((m: { bIsStandard: boolean; bIsToField: boolean; }) => m.bIsToField == true && !m.bIsStandard)
        for (const f of filterFields) {
          filters.push(await this._getFieldData(Number(f.fkIIdField), 'field'));
        }

        let namesSelectFields: string[] = []
        REPORTS_FILTERS_SELECT_POLICY.forEach(element => namesSelectFields.push(element.vNameFieldDb))
        let filterStandard = response.result.dynamicFilters
          .filter((m: { bIsStandard: boolean;}) => m.bIsStandard)
        for (const f of filterStandard) {
          filters.push({
            bIsLoaded: true,
            pkIIdFieldModule: 0,
            pkIIdField: 0,
            fkIIdFieldType: (namesSelectFields.includes(f.vFieldNameStandard)) ? 4 : 7,
            vNameField: f.vFieldNameStandard,
            vNameFieldDb: f.vFieldNameStandard,
            fkIIdFormModule: 0,
            fkIIdProgressBar: 0,
            fkIIdTabModule: 0,
            fkIIdSectionModule: 0,
            fkIIdCatalog: null,
            bRequired: null,
            iMinLength: null,
            iMaxLength: null,
            iOrder: 5,
            bActive: true,
            bAllowMultipleUploads: false,
            bShowDay: null,
            bShowMonth: null,
            bShowHour: null,
            bShowYear: null,
            bShowCoin: null,
            bIsReadonly: null,
            bIsDependent: null,
            bIsPolicy: null,
            bIsPerson: null,
            bIsTable: null,
            bIsSearch: null,
            bIsGrouper: null,
            vEquivalentField: null,
            fkIGrouperField: null,
            vDescription: "Fecha",
            vHelpText: null,
            vFormat: "DD-MM-YYYY",
            iOptionDependent: null,
            iSearchType: null,
            fkIIdParent: null,
            fkIIdChildrenDependent: null,
            fkIIdFieldCatalog: null,
            vSchemaJson: null,
            vEndpoint: null,
            iTypeRequest: null,
            bIsUsingJson: null,
            bIsEmail: null,
            bIsMaxDateRequerid: null,
            bIsMinDateRequerid: null,
            bIsValueMax: null,
            bIsValueMin: null,
            dMaxDateRequerid: null,
            dMinDateRequerid: null,
            vValueMax: null,
            vValueMin: null,
            vTypeDateMin: null,
            vTypeDateMax: null,
            bIsHasDefaultValue: null,
            vSetDefaultValue: null,
            rowsResponse: [],
            rowsRequest: [],
            vNameResponseList: null,
            iTypeResponseList: null,
            bIsInheritedField: null,
            fkIdstageInherited: null,
            fkIdStateInherited: null,
            fkIdFieldInherited: null
          });
        }
        await this.getFieldFilter(filters);
        this._getCatalogs(filters)        
      })
    })
  }

  private async _getCatalogs(filters: any[]){
    let fieldsToConsult = filters.filter(f => f.fkIIdFieldCatalog != null || f?.vDefaultCatalog)
    fieldsToConsult.forEach(field => {
      if (field?.vDefaultCatalog)
        this.catalogsByFieldName[field.vNameFieldDb] = field?.vDefaultCatalog
      else{
        this._catalogService.getCatalogFieldById(field.fkIIdFieldCatalog).subscribe({
          next: (response => {
            this.catalogsByFieldName[field.vNameFieldDb] = response.result.vJson
          })
        })
      }      
    });
  }

  /**
   * Loads the initial data for the component, fetching all reports.
   */
  loadInitialData() {
    setTimeout(() => {
      this._reportSvc.getAllReports(this.idBusinessByCountry).subscribe((r) => {
        this.form.get('fkIIdReport')?.setValue(0);
        this.listReportBd = [];
        this.listReportBd = r.result;
      });
    }, 500);
  }

  /**
   * Handles the change event when a report is selected.
   * @param event - The event object containing the selected value.
   */
  onReportChange(event: string) {
    this.disabledBtnCrud = false;
    this.colorEdit = 'primary';

    if (
      this.initialDate &&
      this.finalDate &&
      this.form.get('fkIIdReport')?.value
    ) {
      this.colorBtnGenerate = 'primary';
      this.disabledBtnGenerate = false;
    } else {
      this.colorBtnGenerate = '';
      this.disabledBtnGenerate = true;
    }
  }

  /**
   * Handles the change event when the initial date is selected.
   * @param event - The datepicker event containing the selected date.
   */
  onInitialDateChange(event: MatDatepickerInputEvent<Date>) {
    this.initialDate = event.value;

    if (
      this.initialDate &&
      this.finalDate &&
      this.form.get('fkIIdReport')?.value
    ) {
      this.disabledBtnGenerate = false;
      this.colorBtnGenerate = 'primary';
    } else {
      this.colorBtnGenerate = '';
      this.disabledBtnGenerate = true;
    }
  }

  /**
   * Handles the change event when the final date is selected.
   * @param event - The datepicker event containing the selected date.
   */
  onFinalDateChange(event: MatDatepickerInputEvent<Date>) {
    this.finalDate = event.value;

    if (
      this.initialDate &&
      this.finalDate &&
      this.form.get('fkIIdReport')?.value
    ) {
      this.colorBtnGenerate = 'primary';
      this.disabledBtnGenerate = false;
    } else {
      this.colorBtnGenerate = '';
      this.disabledBtnGenerate = true;
    }
  }

  /**
   * Handles the click event to delete a report.
   */
  onClickDeleteReport() {
    const nameReportSelected = this.listReportBd.filter(
      (r) => r.pkGIdReport == this.form.get('fkIIdReport')?.value
    )[0].vReportName;
    this._messageService
      .messageConfirmationAndNegation(
        this._translateService.instant('Reports.ReportsGenerated.DeleteReport'),
        `${this._translateService.instant('Reports.Utils.AcceptingWillDelete')} <strong> 
          ${nameReportSelected} </strong>`,
        'info',
        this._translateService.instant('Accept'),
        this._translateService.instant('Cancel')
      )
      .then((re) => {
        if (re) {
          this.reportDeleted();
        }
      });
  }

  /**
   * Deletes the selected report by making an API call.
   */
  reportDeleted() {
    this._reportSvc.deleteReportById(this.form.get('fkIIdReport')?.value).subscribe(response => {
      this._messageService.messageSuccess(
        this._translateService.instant('Reports.Utils.OperationSuccessfull'),
        this._translateService.instant('Reports.Utils.ReportDetailsSuccessfullyDeleted')
      );
      this.loadInitialData();
    });
  }

  /**
   * Navigates to the edit report page for the selected report.
   */
  onClickEditReport() {
    this._customRouter.navigateByUrl(
      'dashboard/reports/new-report/1/' + this.form.get('fkIIdReport')?.value
    );
  }

  private _formatValues(){
    let filter: any = {}
    for (const key in this.formFilters.value)
    {
      if (typeof this.formFilters.value[key] === 'number')
      {
        const nameDB = key.split('_').slice(1).join('_')
        const idValueCatalog = this.formFilters.value[key]
        const catalogField = this.catalogsByFieldName[nameDB]
        if (catalogField)
        {
          const value = catalogField.filter((c: { id: number; }) => c.id == idValueCatalog)
          filter[key] = value[0].name
        }
        else
          filter[key] = idValueCatalog
        
      }
      else if (this.formFilters.value[key] != null)
        filter[key] = this.formFilters.value[key]
    }
    for (const key in this.formAdditionalFilters.value){
      const data = this.formAdditionalFilters.value[key]
      if (data.length > 0)
        filter[key] = data.join(',')
    }
    return filter
  }

  private _dowloadReport(type: string){
    if (type == 'XLSX') {
      var reportName = this.listReportBd.find(x => x.pkGIdReport == this.form.get('fkIIdReport')?.value);
      this.excelService.exportAsExcelFile(this.report, reportName? reportName.vReportName: 'report');
    }
    else {
      this.generateCSV(this.report)
    }
  }
  private _formatReportToTable(){
    if (this.report.length == 0)
      {
        this.dataReportTable = []
        return
      }
    let names = Object.keys(this.report[0])
    this.estructReportTable = []
    names.forEach( column => {
      this.estructReportTable.push({
        columnLabel: column,
        columnValue: column
      })
    }) 
    this.dataReportTable = this.report

  }

  generateReport(type: string) {
    this.form.markAllAsTouched();

    if (this.formFilters.invalid)
      this.formFilters.markAllAsTouched()

    this._reportSvc.getReportByFilter(this.form.get('fkIIdReport')?.value, this._formatValues()).pipe(
      catchError((error) => {
        this._messageService.messageWaring(this._translateService.instant('Warning'), error.error.message)
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        this.report = [];
      } else {
        if (resp.error) {
          this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.report = resp.result;
          if (type != '')
            this._dowloadReport(type)
          else
            this._formatReportToTable()
        }
      }
    });
  }
  


  generateCSV(dynamic: any) {
    this._fileSvc
      .generateCSVDynamic(dynamic)
      .pipe(
        catchError((error) => {
          if (!error.ok) {
            this._messageService.messageWaring(this._translateService.instant('Warning'), error.error.message)
          }
          return of(null);
        })
      )
      .subscribe((resp) => {
        if (resp) {
          const blob = new Blob([resp], {
            type: 'text/csv;charset=utf-8;'
          });
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          var reportName = this.listReportBd.find(x => x.pkGIdReport == this.form.get('fkIIdReport')?.value);

          link.download =reportName?.vReportName + ".csv" || 'report.csv';
          link.click();
          window.URL.revokeObjectURL(downloadUrl);
        }
      });
  }

  //get BusinessByCountry
  async getBusinessByCountry() {
    let dataSetting = await this._settingService.getDataSettingInit();
    this.idBusinessByCountry =
      dataSetting == undefined ? 0 : dataSetting.idBusinessByCountry;

      this.getRoleList(false);
      this.getListGroupsByIdBusiness(this.idBusinessByCountry);
      this.getUsersList(this.idBusinessByCountry, 0);
  }










  // new 

  getListGroupsByIdBusiness(idBusinessByCountry: number) {
    this._groupsService
      .getListGroupsByIdBusiness(idBusinessByCountry)
      .subscribe({
        next: (response) => {
          if (response.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') +
                response.message
            );
          } else {
            this.groupTable = response.result;
          }
        },
      });
  }

  getRoleList(bDependiente: boolean) {
    if (bDependiente) {
      let dataFilterGroup = {
        listIdGroup:
          this.form.get('listGroupBussines')?.value || [],
      };
      this._groupsService
        .getRoleByIdGroup(dataFilterGroup)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.roleList = resp.result;
            }
          }
        });
    } else {
      this._roleService
        .getRole(this.idBusinessByCountry)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.roleList = resp.result;
            }
          }
        });
    }
  }

  //metodos get, post, put.. que consumen la api
  getUsersList(id: number, type: number) {
    if (type == 0) {
      this._userService
        .getUserList(id)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.tempUsersList = resp.result.filter((value: { pkIIdUser: any; }, index: any, self: any[]) =>
                index === self.findIndex((t) => (
                    t.pkIIdUser === value.pkIIdUser
                ))
            );
            }
          }
        });
    } else if (type == 1) {
      let listIdRole =
        this.form.get('listRoleBusiness')?.value.join('') || '';
      this._userService
        .getUserByIRoles(this.idBusinessByCountry, listIdRole)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.tempUsersList = resp.result;
            }
          }
        });
    } else if (type == 2) {
      let listIdGroup =
        this.form.get('listGroupBussines')?.value.join('') || '';

      this._userService
        .getUserByIdGroup(listIdGroup)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.tempUsersList = resp.result;
            }
          }
        });
    } else if (type == 3) {
      let listIdGroup =
        this.form.get('listGroupBussines')?.value.join(',') || '';
      let listIdRole =
        this.form.get('listRoleBusiness')?.value.join(',') || '';
      this._userService
        .getUserByIRolesAndGroups(
          this.idBusinessByCountry,
          listIdGroup.toString(),
          listIdRole.toString()
        )
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.tempUsersList = resp.result;
            }
          }
        });
    }
  }

  getFieldType() { //esta función podría irse
    this._fieldServices.getFieldType().pipe(
      catchError((error) => {
        this._messageService.messageWaring(this._translateService.instant('Warning'), error.error.message)
        return of([]);
      })
    ).subscribe((resp: ResponseGlobalModel | never[]) => {
      if (Array.isArray(resp)) {
        console.log("El tipo de datos devueltos es un array vacío.");
      } else {
        if (resp.error) {
          this._messageService.messageError(this._translateService.instant('ThereWasAError') + resp.message)
        }
        else {
          this.filterTypeList = resp.result.filter((item:FieldTypeModel) => item.vCode === 'DROPDOWN' || item.vCode === 'RADIO');
          this.filterTypeDateList = resp.result.filter((item:FieldTypeModel) => item.vCode === 'DATE');
        }
      }
    });
  }

  private async _getIdWtwPolicies() {
    return new Promise((resolve, reject) => {
      var reportData = this.listReportBd.find(x => x.pkGIdReport == this.form.get('fkIIdReport')?.value);
      this._transactionService
        .filterGeneralPolicyData({
          idProduct: Number(reportData?.fkIIdProduct),
          idInsurance: Number(reportData?.fkIIdInsuranceCompany),
          idBusinessCountry: this.idBusinessByCountry,
          order: 'asc',
          keyword: null,
          active: null,
          idPolicyType: null,
          page: 0,
          pageSize: 1000
        })
        .subscribe((resp: ResponseGlobalModel | null) => {
          if (resp && Array.isArray(resp.result)) {
            resolve(resp.result.map(obj => Number(obj['idPolicy'])));
          } else {
            resolve([]);
          }
        });
    });
  }


  getFieldFilter(filters: any[]) {
    this.listFieldFilter = filters.filter((item:FieldModel) => 
      this.filterTypeList.some(filterItem => filterItem.pkIIdFieldType === item.fkIIdFieldType)
    );
    this.listFieldFilter.forEach(async element => {
      element.bIsLoaded = false;
      if (element.vNameFieldDb.includes("Estado (riesgo)") || element.vNameFieldDb.includes("Estado (póliza)"))
      {
        element['vDefaultCatalog'] = [
          {id: 1, name: "Activo", isDependant: null, description: null},
          {id: 0, name: "Inactivo", isDependant: null, description: null},
        ]
      }
      else if (element.vNameFieldDb.includes("ID WTW"))
      {
        let data = await this._getIdWtwPolicies();
        element['vDefaultCatalog'] = [];
        (data as number[]).forEach((id: any, index: number) => {
          element['vDefaultCatalog'].push({id: index, name: id.toString(), isDependant: null, description: null})
        })
        this.catalogsByFieldName[element.vNameFieldDb] = element['vDefaultCatalog']
      }
      else if (element.vNameFieldDb.includes("Tipo póliza"))
      {
        element['vDefaultCatalog'] = [
          {id: 1, name: "Individual", isDependant: null, description: null},
          {id: 2, name: "Colectiva", isDependant: null, description: null},
          {id: 3, name: "Individual - colectiva", isDependant: null, description: null},
        ]
      }
    element.bIsLoaded = true;
    })
    this.listFieldFilterDate = filters.filter((item: FieldModel) =>
      this.filterTypeDateList.some(filterItem => filterItem.pkIIdFieldType === item.fkIIdFieldType)
    ).map(item => {
      // Crea un campo para la "Fecha Inicial"
      const fieldInicial = { 
        ...item, 
        vNameField: `${item.vNameField} Inicial`, // Cambia el nombre para "Fecha Inicial"
        vNameFieldDb: `${item.vNameFieldDb}_inicial` // Asegura que el campo en la base de datos tenga un sufijo "_inicial"
      };

      // Crea un campo para la "Fecha Final"
      const fieldFinal = { 
        ...item, 
        vNameField: `${item.vNameField} Final`, // Cambia el nombre para "Fecha Final"
        vNameFieldDb: `${item.vNameFieldDb}_final` // Asegura que el campo en la base de datos tenga un sufijo "_final"
      };

      // Devuelve ambos campos en un array
      return [fieldInicial, fieldFinal];
    }).flat();  // `flat()` para aplanar el array de arrays en un solo array de objetos

    this.listAllFilters = [
      ...this.listFieldFilterDate,
      ...this.listFieldFilter
    ]
    const formGroupFields = this.getFormControlsFields();
    this.formFilters = new FormGroup(formGroupFields);
  }

  private getFormControlsFields() {
    const formGroupFields: any = {};

    this.listFieldFilterDate.forEach(field => {
      formGroupFields[(field.pkIIdFieldModule || field.pkIIdField) + '_' + field.vNameFieldDb] = new FormControl(null);
    });
  
    this.listFieldFilter.forEach(field => {
      formGroupFields[(field.pkIIdFieldModule || field.pkIIdField) + '_' + field.vNameFieldDb] = new FormControl(null);
    });
   
    return formGroupFields;
  }


}
