<div class="cont-test">
  <app-choose-country-and-company
    (valueForm)="validForm($event)"
  ></app-choose-country-and-company>
</div>

<div class="cont-subtitle mt-3">
  <h3>{{ "ModulesSetting.Modules.MainSubtitle" | translate }}</h3>
  <p>{{ "ModulesSetting.Modules.MainDescription" | translate }}</p>
</div>

<div class="cont-table">
  <app-table
    [displayedColumns]="estructModulesSettingTable"
    [data]="modulesDataTable"
    (iconClick)="controller($event)"
  ></app-table>
</div>

<div class="cont-btn-create">
  <button
    class="mt-2 w-20"
    type="button"
    (click)="gotToCreate()"
    mat-raised-button
    color="primary"
  >
    {{ "ModulesSetting.Modules.CreateModule" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
</div>
