<ng-container *ngIf="data">
  <div [formGroup]="dynamicFormGroup" class="cont-visualizer mt-3 container-hidden-x"  *ngFor="let item of data">
    <!-- <PERSON>aso cu<PERSON>do hay pasos -->
    <app-wizard *ngIf="
          item.progressBarModules.length >= 1 &&
          item.progressBarModules[0].pkIIdProgressBar &&
          item
        " [steps]="steps" [currentStep]="currentStep" (currentStepChange)="currentStepChange($event)"
      (next)="next($event)" (back)="back($event)" [stepToCompare]="steps[currentStep]">
      <ng-container stepBody [ngSwitch]="currentStep">
        <div>
          <ng-container *ngFor="
                let progressBar of item.progressBarModules;
                let iProgressBar = index
              ">
            <div *ngSwitchCase="iProgressBar">
              <mat-tab-group class="mt-5 mb-5">
                <ng-container *ngFor="let tab of progressBar.tabModules" >
                  <mat-tab *ngIf="tab.pkIIdTabModule" label="{{ tab.vName }}">
                    <ng-container *ngFor="let section of tab.sectionModules">
                      <div class="row" *ngIf="section.pkIIdSectionModule; else notSection" class="container-hidden-x">
                        <div class="row mt-3 mb-3">
                          <div class="col-md-12 col-sm-12">
                            <h3 class="section-title">{{ section.vName }}</h3>
                          </div>
                          <ng-container *ngFor="let field of getActiveFields(section.fieldModules)">
                            <div 
                            [hidden]="(field.bIsInvisible && 
                            ((isClientPortal==true && (field.iElementHideField == 1 || field.iElementHideField == 3 )) || 
                                      (isClientPortal==false && (field.iElementHideField == 2 || field.iElementHideField == 3 ))))"
                            [ngClass]="getColumnClass(item.iAmountColumns, field.bExternalHidden)" *ngIf="field">
                              <app-field-generator [isQuote]="false" [field]="field"[idTaskState]="idTaskEditing" [isClientPortal]="isClientPortal"
                                *ngIf="field.pkIIdFieldModule" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                              <h1 *ngIf="!field.pkIIdFieldModule">{{ "ModulesDynamic.NotFieldConfigured" | translate }}</h1>
                            </div>
                          </ng-container>
                        </div>
                      </div>
                      <ng-template #notSection>
                        <div class="row mt-3 mb-3">
                        <ng-container *ngFor="let field of getActiveFields(section.fieldModules)">
                            <div [ngClass]="getColumnClassShort(item.iAmountColumns, field.bExternalHidden)"  *ngIf="field" [hidden]="(field.bIsInvisible && 
                            ((isClientPortal==true && (field.iElementHideField == 1 || field.iElementHideField == 3 )) || 
                                      (isClientPortal==false && (field.iElementHideField == 2 || field.iElementHideField == 3 ))))">
                              <app-field-generator [isClientPortal]="isClientPortal" [isQuote]="false" [field]="field"[idTaskState]="idTaskEditing"
                                *ngIf="field.pkIIdFieldModule" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)"></app-field-generator>
                              <h1 *ngIf="!field.pkIIdFieldModule">{{ "ModulesDynamic.NotFieldConfigured" | translate }}</h1>
                            </div>
                        </ng-container>
                      </div>
                      </ng-template>
                      <!-- Cuando hay campos tipo tabla en secciones -->
                      <ng-container *ngIf=" section.tableData!==undefined">
                        <ng-container *ngFor="let table of section.tableData">
                          <div *ngIf=" table.columnTable[0].pkIIdColumnTableModule!=null"
                            class="mat-elevation-z8 row px-1">
                            <div class="col-12"> <!-- Columna de ancho completo -->
                              <h2 class="my-4 table-title">{{ table.vName }}</h2>

                              <table class="table">
                                <!-- Encabezados de columnas -->
                                <thead>
                                  <tr>
                                    <th *ngFor="let header of table.columnTable">
                                      {{ header.bName }}
                                    </th>
                                    <!-- Agregar encabezados adicionales si las llaves están activas -->
                                    <th *ngIf="table.bDelete">{{ "Delete" | translate }}</th>
                                    <th *ngIf="table.bUpload">{{ "Upload" | translate }}</th>
                                    <th *ngIf="table.bDownload">{{ "Download" | translate }}</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let row of numberRows(table)?.controls; let iRow = index">
                                    <td *ngFor="let column of table.columnTable">
                                      <!-- Puedes agregar el valor de cada control aquí -->
                                      <app-field-generator [isClientPortal]="isClientPortal" [formArrayName]="'table_' + table.vName" [rowIndex]="iRow"[idTaskState]="idTaskEditing"
                                        [isQuote]="false" [field]="column.fieldModules" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                                    </td>
                                    <!-- Agregar columnas adicionales si las llaves están activas -->
                                    <td *ngIf="table.bDelete">
                                      <button mat-icon-button (click)="deleteRowForTable(table, iRow)" type="button">
                                        <mat-icon>delete</mat-icon>
                                      </button>
                                    </td>
                                    <td *ngIf="table.bUpload">
                                      <input #fileInput (change)="onFileUploadForTable($event, row)" type="file"
                                        style="display: none;" ValidationInputFile />
                                      <button (click)="fileInput.click()" mat-icon-button type="button">
                                        <mat-icon matTooltip="file" mat-icon-button>upload</mat-icon>
                                      </button>
                                    </td>
                                    <td *ngIf="table.bDownload">
                                      <button mat-icon-button (click)="downloadFileForTable(row)" type="button">
                                        <mat-icon>download</mat-icon>
                                      </button>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>

                            <div *ngIf="!isViewing" class="col-12 mt-3">
                              <!-- Columna de ancho completo, contenido centrado y margen superior -->
                              <button (click)="addRow(table)" class="mb-2" type="button" color="primary"
                                mat-raised-button>
                                {{ "Add" | translate }}
                              </button>
                            </div>

                          </div>
                        </ng-container>

                      </ng-container>
                    </ng-container>
                  </mat-tab>
                </ng-container>
              </mat-tab-group>

              <ng-container *ngIf="progressBar.tabModules[0].pkIIdTabModule === null">
                <ng-container *ngFor="
                      let section of progressBar.tabModules[0].sectionModules
                    ">
                  <div class="row mt-3 mb-3">
                    <div class="col-md-12 col-sm-12">
                      <h3 class="section-title">{{ section.vName }}</h3>
                    </div>
                    <ng-container *ngFor="let field of getActiveFields(section.fieldModules)">
                      <div [ngClass]="getColumnClass(item.iAmountColumns, field.bExternalHidden)"
                        *ngIf="field" [hidden]="(field.bIsInvisible && 
                            ((isClientPortal==true && (field.iElementHideField == 1 || field.iElementHideField == 3 )) || 
                                      (isClientPortal==false && (field.iElementHideField == 2 || field.iElementHideField == 3 ))))">
                        <app-field-generator [isClientPortal]="isClientPortal" [isQuote]="false" [field]="field"[idTaskState]="idTaskEditing"
                          *ngIf="field.pkIIdFieldModule" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                      </div>
                    </ng-container>
                  </div>

                  <!-- Table of fields  -->
                  <ng-container *ngIf=" section.tableData!==undefined">

                    <app-table [IsStatic]="true" [displayedColumns]="(estructMyQuotationTable | async) || []"
                      [data]="section.tableData"></app-table>
                  </ng-container>
                </ng-container>
              </ng-container>
            </div>
          </ng-container>
        </div>

        <!-- Campo de observaciones no es dinamico -->
        <!-- <div class="container-hidden-x ">
          <div class="mt-1">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>{{ 'Observations' | translate }}</mat-label>
              <textarea
              matInput
              id="observationInput"
              [(ngModel)]="observationInput"
              formControlName="observationInput"
              maxlength="1000"
              rows="4"
              (input)="validateCharacter($event)"
            ></textarea>

              <mat-error *ngIf="dynamicFormGroup.get('observationInput')?.hasError('required')">
                {{ 'ThisFieldIsRequired' | translate }}
              </mat-error>
              <mat-error *ngIf="dynamicFormGroup.get('observationInput')?.hasError('maxlength')">
                {{ 'Maximum length exceeded' | translate }}
              </mat-error>
            </mat-form-field>

          </div>
        </div> -->

        <div *ngSwitchDefault>{{ "ModulesDynamic.Default" | translate }}</div>
      </ng-container>

      <ng-container customButtonInit>
        <button class="mx-2" *ngIf="!isInternal" mat-raised-button (click)="goToBack()">
          <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
          {{ "Cancel" | translate }}
        </button>
      </ng-container>
      <ng-container customButtonFinal>
        <button *ngIf="!item.documentTable" type="button" color="primary" mat-raised-button (click)="saveTask()"
          [disabled]="isViewing">
          {{ "Save" | translate }}
        </button>
      </ng-container>
    </app-wizard>

    <!-- Caso cuándo no hay pasos -->
    <ng-container *ngIf="item.progressBarModules[0].pkIIdProgressBar === null">
      <div>
        <div *ngFor="let progressBar of item.progressBarModules">
          <mat-tab-group class="mt-5 mb-5">
            <ng-container *ngFor="let tab of progressBar.tabModules; let currentTab = index">
              <mat-tab *ngIf="tab.pkIIdTabModule" label="{{ tab.vName }}">
                <ng-container *ngFor="let section of tab.sectionModules">
                  <div class="row" *ngIf="section.pkIIdSectionModule; else notSection">
                    <div class="row mt-3 mb-3">
                      <div class="col-md-12 col-sm-12">
                        <h3 class="section-title">{{ section.vName }}</h3>
                      </div>
                      <ng-container *ngFor="let field of getActiveFields(section.fieldModules)">
                        <div [ngClass]="getColumnClass(item.iAmountColumns, field.bExternalHidden)" *ngIf="field" [hidden]="(field.bIsInvisible && 
                            ((isClientPortal==true && (field.iElementHideField == 1 || field.iElementHideField == 3 )) || 
                                      (isClientPortal==false && (field.iElementHideField == 2 || field.iElementHideField == 3 ))))">
                          <app-field-generator [isClientPortal]="isClientPortal" [isQuote]="false" [field]="field"[idTaskState]="idTaskEditing"
                            *ngIf="field.pkIIdFieldModule" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                        </div>
                      </ng-container>
                    </div>
                  </div>
                  <ng-template #notSection>
                    <div class="row mt-3 mb-3" >
                      <ng-container *ngFor="let field of getActiveFields(section.fieldModules)">
                        <div [ngClass]="getColumnClassShort(item.iAmountColumns, field.bExternalHidden)" *ngIf="field" [hidden]="(field.bIsInvisible && 
                            ((isClientPortal==true && (field.iElementHideField == 1 || field.iElementHideField == 3 )) || 
                                      (isClientPortal==false && (field.iElementHideField == 2 || field.iElementHideField == 3 ))))">
                          <app-field-generator [isClientPortal]="isClientPortal" [isQuote]="false" [field]="field" *ngIf="field.pkIIdFieldModule" [isEditing]="isViewing"[idTaskState]="idTaskEditing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)"
                          [formulasCalculated]="formulasCalculated"></app-field-generator>
                        </div>
                      </ng-container>
                    </div>
                  </ng-template>

                  <!-- Campo de observaciones no es dinamico -->
                  <!-- <div class="container-hidden-x ">
                    <div class="mt-1">
                      <mat-form-field appearance="outline" class="w-100">
                        <mat-label>{{ 'Observations' | translate }}</mat-label>
                        <textarea
                        matInput
                        id="observationInput"
                        [(ngModel)]="observationInput"
                        formControlName="observationInput"
                        maxlength="1000"
                        rows="4"
                        (input)="validateCharacter($event)"
                      ></textarea>

                        <mat-error *ngIf="dynamicFormGroup.get('observationInput')?.hasError('required')">
                          {{ 'ThisFieldIsRequired' | translate }}
                        </mat-error>
                        <mat-error *ngIf="dynamicFormGroup.get('observationInput')?.hasError('maxlength')">
                          {{ 'Maximum length exceeded' | translate }}
                        </mat-error>
                      </mat-form-field>

                    </div>
                  </div> -->

                  <!-- Cuando hay campos tipo tabla en secciones -->
                <ng-container *ngIf=" section.tableData!==undefined">
                  <ng-container *ngFor="let table of section.tableData">
                    <div *ngIf=" table.columnTable[0].pkIIdColumnTableModule!=null"
                    class="mat-elevation-table">
                      <div class="col-12"> <!-- Columna de ancho completo -->
                        <h2 class="my-4 table-title">{{ table.vName }}</h2>

                        <table class="table">
                          <!-- Encabezados de columnas -->
                          <thead>
                            <tr>
                              <th *ngFor="let header of table.columnTable">
                                {{ header.bName }}
                              </th>
                              <!-- Agregar encabezados adicionales si las llaves están activas -->
                              <th *ngIf="table.bDelete">{{ "Delete" | translate }}</th>
                              <th *ngIf="table.bUpload">{{ "Upload" | translate }}</th>
                              <th *ngIf="table.bDownload">{{ "Download" | translate }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let row of numberRows(table)?.controls; let iRow = index">
                              <td *ngFor="let column of table.columnTable">
                                <!-- Puedes agregar el valor de cada control aquí -->
                                <app-field-generator [isClientPortal]="isClientPortal" [formArrayName]="'table_' + table.vName" [rowIndex]="iRow"[idTaskState]="idTaskEditing"
                                  [isQuote]="false" [field]="column.fieldModules" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                              </td>
                              <!-- Agregar columnas adicionales si las llaves están activas -->
                              <td *ngIf="table.bDelete">
                                <button mat-icon-button (click)="deleteRowForTable(table, iRow)" type="button">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              </td>
                              <td *ngIf="table.bUpload">
                                <input #fileInput (change)="onFileUploadForTable($event, row)" type="file"
                                  style="display: none;" ValidationInputFile />
                                <button (click)="fileInput.click()" mat-icon-button type="button">
                                  <mat-icon matTooltip="file" mat-icon-button>upload</mat-icon>
                                </button>
                              </td>
                              <td *ngIf="table.bDownload">
                                <button mat-icon-button (click)="downloadFileForTable(row)" type="button">
                                  <mat-icon>download</mat-icon>
                                </button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div *ngIf="!isViewing" class="col-12 mt-3">
                        <!-- Columna de ancho completo, contenido centrado y margen superior -->
                        <button (click)="addRow(table)" class="mb-2 add-row-button" type="button" color="primary"
                          mat-raised-button>
                          {{ "Add" | translate }}
                        </button>
                      </div>

                    </div>
                  </ng-container>
                </ng-container>

                <!-- Cuando hay campos tipo tabla en pestañas -->
                <ng-container *ngIf=" tab.tableData!==undefined">
                  <ng-container *ngFor="let table of tab.tableData">
                    <div *ngIf=" table.columnTable[0].pkIIdColumnTableModule!=null"
                    class="mat-elevation-table">
                      <div class="col-12"> <!-- Columna de ancho completo -->
                        <h2 class="my-4 table-title">{{ table.vName }}</h2>

                        <table class="table">
                          <!-- Encabezados de columnas -->
                          <thead>
                            <tr>
                              <th *ngFor="let header of table.columnTable">
                                {{ header.bName }}
                              </th>
                              <!-- Agregar encabezados adicionales si las llaves están activas -->
                              <th *ngIf="table.bDelete">{{ "Delete" | translate }}</th>
                              <th *ngIf="table.bUpload">{{ "Upload" | translate }}</th>
                              <th *ngIf="table.bDownload">{{ "Download" | translate }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let row of numberRows(table)?.controls; let iRow = index">
                              <td *ngFor="let column of table.columnTable">
                                <!-- Puedes agregar el valor de cada control aquí -->
                                <app-field-generator [isClientPortal]="isClientPortal" [formArrayName]="'table_' + table.vName" [rowIndex]="iRow"[idTaskState]="idTaskEditing"
                                  [isQuote]="false" [field]="column.fieldModules" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                              </td>
                              <!-- Agregar columnas adicionales si las llaves están activas -->
                              <td *ngIf="table.bDelete">
                                <button mat-icon-button (click)="deleteRowForTable(table, iRow)" type="button">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              </td>
                              <td *ngIf="table.bUpload">
                                <input #fileInput (change)="onFileUploadForTable($event, row)" type="file"
                                  style="display: none;" ValidationInputFile />
                                <button (click)="fileInput.click()" mat-icon-button type="button">
                                  <mat-icon matTooltip="file" mat-icon-button>upload</mat-icon>
                                </button>
                              </td>
                              <td *ngIf="table.bDownload">
                                <button mat-icon-button (click)="downloadFileForTable(row)" type="button">
                                  <mat-icon>download</mat-icon>
                                </button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div *ngIf="!isViewing" class="col-12 mt-3">
                        <!-- Columna de ancho completo, contenido centrado y margen superior -->
                        <button (click)="addRow(table)" class="mb-2 add-row-button" type="button" color="primary"
                          mat-raised-button>
                          {{ "Add" | translate }}
                        </button>
                      </div>

                    </div>
                  </ng-container>
                </ng-container>
                </ng-container>
                <ng-container *ngIf="!item.documentTable">
                  <button class="mx-2" *ngIf="!isInternal" mat-raised-button (click)="goToBack()">
                    <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
                    {{ "Cancel" | translate }}
                  </button>
                  <button class="mx-2" *ngIf="currentTab === progressBar.tabModules.length - 2" type="button" color="primary" mat-raised-button (click)="saveTask()"
                    [disabled]="isViewing">
                    {{ "Save" | translate }}
                  </button>
                </ng-container>

              </mat-tab>
            </ng-container>
          </mat-tab-group>

          <ng-container *ngIf="progressBar.tabModules[0].pkIIdTabModule === null">
            <ng-container *ngIf="
                  progressBar.tabModules[0].sectionModules[0].pkIIdSectionModule
                ">
              <ng-container *ngFor="let section of progressBar.tabModules[0].sectionModules">
                <div class="row mt-3 mb-3">
                  <div class="col-md-12 col-sm-12">
                    <h3 class="section-title">{{ section.vName }}</h3>
                  </div>
                  <ng-container *ngFor="let field of getActiveFields(section.fieldModules)">
                    <div [ngClass]="getColumnClass(item.iAmountColumns, field.bExternalHidden)" *ngIf="field" [hidden]="(field.bIsInvisible && 
                            ((isClientPortal==true && (field.iElementHideField == 1 || field.iElementHideField == 3 )) || 
                                      (isClientPortal==false && (field.iElementHideField == 2 || field.iElementHideField == 3 ))))">
                      <app-field-generator [isClientPortal]="isClientPortal" [isQuote]="false" [field]="field"[idTaskState]="idTaskEditing"
                        *ngIf="field.pkIIdFieldModule" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                    </div>
                  </ng-container>
                  <!-- Cuando hay campos tipo tabla en secciones -->
                  <ng-container *ngIf=" section.tableData!== undefined">
                    <ng-container *ngFor="let table of section.tableData">
                      <div *ngIf=" table.columnTable[0].pkIIdColumnTableModule!=null"
                        class="mat-elevation-z8 row px-1 m-auto">
                        <div class="col-12 horizontal-scroll"> <!-- Columna de ancho completo -->
                          <h2 class="my-4 table-title">{{ table.vName }}</h2>

                          <table class="table">
                            <!-- Encabezados de columnas -->
                            <thead>
                              <tr>
                                <th *ngFor="let header of table.columnTable">
                                  {{ header.bName }}
                                </th>
                                <!-- Agregar encabezados adicionales si las llaves están activas -->
                                <th *ngIf="table.bDelete">{{ "Delete" | translate }}</th>
                                <th *ngIf="table.bUpload">{{ "Upload" | translate }}</th>
                                <th *ngIf="table.bDownload">{{ "Download" | translate }}</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr *ngFor="let row of numberRows(table)?.controls; let iRow = index">
                                <td *ngFor="let column of table.columnTable">
                                  <!-- Puedes agregar el valor de cada control aquí -->
                                  <app-field-generator [isClientPortal]="isClientPortal" [formArrayName]="'table_' + table.vName" [rowIndex]="iRow"[idTaskState]="idTaskEditing"
                                    [isQuote]="false" [field]="column.fieldModules" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                                </td>
                                <!-- Agregar columnas adicionales si las llaves están activas -->
                                <td *ngIf="table.bDelete">
                                  <button mat-icon-button (click)="deleteRowForTable(table, iRow)" type="button">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                </td>
                                <td *ngIf="table.bUpload">
                                  <input #fileInput (change)="onFileUploadForTable($event, row)" type="file"
                                    style="display: none;" ValidationInputFile />
                                  <button (click)="fileInput.click()" mat-icon-button type="button">
                                    <mat-icon matTooltip="file" mat-icon-button>upload</mat-icon>
                                  </button>
                                </td>
                                <td *ngIf="table.bDownload">
                                  <button mat-icon-button (click)="downloadFileForTable(row)" type="button">
                                    <mat-icon>download</mat-icon>
                                  </button>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>

                        <div *ngIf="!isViewing" class="col-12 mt-3" >
                          <!-- Columna de ancho completo, contenido centrado y margen superior -->
                          <button class="mb-2" (click)="addRow(table)" type="button" color="primary" mat-raised-button>
                            {{ "Add" | translate }}
                          </button>
                        </div>

                      </div>
                    </ng-container>

                  </ng-container>


                </div>
              </ng-container>

            </ng-container>

            <ng-container *ngIf="
                  progressBar.tabModules[0].sectionModules[0]
                    .pkIIdSectionModule === null
                ">
              <ng-container *ngFor="let section of progressBar.tabModules[0].sectionModules">
                <div class="row mt-3 mb-3">
                  <ng-container *ngFor="let field of getActiveFields(section.fieldModules)">
                    <div [ngClass]="getColumnClass(item.iAmountColumns, field.bExternalHidden)" *ngIf="field" [hidden]="(field.bIsInvisible && 
                            ((isClientPortal==true && (field.iElementHideField == 1 || field.iElementHideField == 3 )) || 
                                      (isClientPortal==false && (field.iElementHideField == 2 || field.iElementHideField == 3 ))))">
                      <app-field-generator [isClientPortal]="isClientPortal" [isQuote]="false" [field]="field"[idTaskState]="idTaskEditing"
                        *ngIf="field.pkIIdFieldModule" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                    </div>
                  </ng-container>
                  <!-- Cuando hay campos tipo tabla en secciones -->
                  <ng-container *ngIf=" section.tableData!== undefined">
                    <ng-container *ngFor="let table of section.tableData">
                      <div *ngIf=" table.columnTable[0].pkIIdColumnTableModule!=null" class="mat-elevation-z8 row px-1">
                        <div class="col-12"> <!-- Columna de ancho completo -->
                          <h2 class="my-4 table-title">{{ table.vName }}</h2>

                          <table class="table">
                            <!-- Encabezados de columnas -->
                            <thead>
                              <tr>
                                <th *ngFor="let header of table.columnTable">
                                  {{ header.bName }}
                                </th>
                                <!-- Agregar encabezados adicionales si las llaves están activas -->
                                <th *ngIf="table.bDelete">{{ "Delete" | translate }}</th>
                                <th *ngIf="table.bUpload">{{ "Upload" | translate }}</th>
                                <th *ngIf="table.bDownload">{{ "Download" | translate }}</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr *ngFor="let row of numberRows(table)?.controls; let iRow = index">
                                <td *ngFor="let column of table.columnTable">
                                  <!-- Puedes agregar el valor de cada control aquí -->
                                  <app-field-generator [isClientPortal]="isClientPortal" [formArrayName]="'table_' + table.vName" [rowIndex]="iRow"[idTaskState]="idTaskEditing"
                                    [isQuote]="false" [field]="column.fieldModules" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                                </td>
                                <!-- Agregar columnas adicionales si las llaves están activas -->
                                <td *ngIf="table.bDelete">
                                  <button mat-icon-button (click)="deleteRowForTable(table, iRow)" type="button">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                </td>
                                <td *ngIf="table.bUpload">
                                  <input #fileInput (change)="onFileUploadForTable($event, row)" type="file"
                                    style="display: none;" ValidationInputFile />
                                  <button (click)="fileInput.click()" mat-icon-button type="button">
                                    <mat-icon matTooltip="file" mat-icon-button>upload</mat-icon>
                                  </button>
                                </td>
                                <td *ngIf="table.bDownload">
                                  <button mat-icon-button (click)="downloadFileForTable(row)" type="button">
                                    <mat-icon>download</mat-icon>
                                  </button>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>

                        <div *ngIf="!isViewing" class="col-12 mt-3">
                          <!-- Columna de ancho completo, contenido centrado y margen superior -->
                          <button (click)="addRow(table)" class="mb-2" type="button" color="primary" mat-raised-button>
                            {{ "Add" | translate }}
                          </button>
                        </div>

                      </div>
                    </ng-container>

                  </ng-container>

                  <!-- Cuando hay campos tipo tabla y no hay pasos, pestañas ni secciones -->
                  <ng-container *ngIf="item.tableData !== undefined">
                    <ng-container *ngFor="let table of item.tableData">
                      <div *ngIf=" table.columnTable[0].pkIIdColumnTableModule!=null"
                        class="mat-elevation-z8 row px-1 m-auto">
                        <div class="col-12"> <!-- Columna de ancho completo -->
                          <h2 class="my-4 table-title">{{ table.vName }}</h2>

                          <table class="table">
                            <!-- Encabezados de columnas -->
                            <thead>
                              <tr>
                                <th *ngFor="let header of table.columnTable">
                                  {{ header.bName }}
                                </th>
                                <!-- Agregar encabezados adicionales si las llaves están activas -->
                                <th *ngIf="table.bDelete">{{ "Delete" | translate }}</th>
                                <th *ngIf="table.bUpload">{{ "Upload" | translate }}</th>
                                <th *ngIf="table.bDownload">{{ "Download" | translate }}</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr *ngFor="let row of numberRows(table)?.controls; let iRow = index">
                                <td *ngFor="let column of table.columnTable">
                                  <!-- Puedes agregar el valor de cada control aquí -->
                                  <app-field-generator [isClientPortal]="isClientPortal" [formArrayName]="'table_' + table.vName" [rowIndex]="iRow"[idTaskState]="idTaskEditing"
                                    [isQuote]="false" [field]="column.fieldModules" [isEditing]="isViewing" (downloadDocumentEvent)="getdownloadDocumentEvent($event)" [formulasCalculated]="formulasCalculated"></app-field-generator>
                                </td>
                                 <!-- Agregar columnas adicionales si las llaves están activas -->
                                 <td *ngIf="table.bDelete">
                                  <button mat-icon-button (click)="deleteRowForTable(table, iRow)" type="button">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                </td>
                                <td *ngIf="table.bUpload">
                                  <input #fileInput (change)="onFileUploadForTable($event, row)" type="file"
                                    style="display: none;" ValidationInputFile />
                                  <button (click)="fileInput.click()" mat-icon-button type="button">
                                    <mat-icon matTooltip="file" mat-icon-button>upload</mat-icon>
                                  </button>
                                </td>
                                <td *ngIf="table.bDownload">
                                  <button mat-icon-button (click)="downloadFileForTable(row)" type="button">
                                    <mat-icon>download</mat-icon>
                                  </button>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>

                        <div *ngIf="!isViewing" class="col-12 mt-3">
                          <!-- Columna de ancho completo, contenido centrado y margen superior -->
                          <button (click)="addRow(table)" class="mb-2" type="button" color="primary" mat-raised-button>
                            {{ "Add" | translate }}
                          </button>
                        </div>

                      </div>
                    </ng-container>

                  </ng-container>


                  <ng-container *ngIf="
                  progressBar.tabModules[0].sectionModules[0]
                    .fieldModules[0].pkIIdFieldModule === null
                ">No hay formulario configurado</ng-container>
                </div>
              </ng-container>
            </ng-container>
            <!-- Campo de observaciones no es dinamico -->
            <!-- <div class="container-hidden-x" *ngIf="!item.documentTable">
              <div class="mt-1">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>{{ 'Observations' | translate }}</mat-label>
                  <textarea
                  matInput
                  id="observationInput"
                  [(ngModel)]="observationInput"
                  formControlName="observationInput"
                  maxlength="1000"
                  rows="4"
                  (input)="validateCharacter($event)"
                ></textarea>

                  <mat-error *ngIf="dynamicFormGroup.get('observationInput')?.hasError('required')">
                    {{ 'ThisFieldIsRequired' | translate }}
                  </mat-error>
                  <mat-error *ngIf="dynamicFormGroup.get('observationInput')?.hasError('maxlength')">
                    {{ 'Maximum length exceeded' | translate }}
                  </mat-error>
                </mat-form-field>

              </div>
            </div> -->
            <div class="row">
              <div style="text-align-last: center;" class="col-12">
                <button class="mx-2" *ngIf="!isInternal && !item.documentTable" mat-raised-button (click)="goToBack()">
                  <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
                  {{ "Cancel" | translate }}
                </button>
                <button type="button" color="primary" mat-raised-button (click)="saveTask()" *ngIf="!item.documentTable"
                  [disabled]="isViewing">
                  {{ "Save" | translate }}
                </button>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="item.documentTable!==undefined">
      <div class="mx-1 mt-3">
        <app-table (iconClick)="controllerDocuments($event)" [IsStatic]="true"
        [displayedColumns]="(estructMyQuotationTable | async) || []" [data]="item.documentTable"></app-table>
      </div>
    </ng-container>
    <!-- Campo de observaciones no es dinamico -->
    <!-- <div class="container-hidden-x" *ngIf="item.documentTable!==undefined">
      <div class="mt-1">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'Observations' | translate }}</mat-label>
          <textarea
          matInput
          id="observationInput"
          [(ngModel)]="observationInput"
          formControlName="observationInput"
          maxlength="1000"
          rows="4"
          (input)="validateCharacter($event)"
        ></textarea>

          <mat-error *ngIf="dynamicFormGroup.get('observationInput')?.hasError('required')">
            {{ 'ThisFieldIsRequired' | translate }}
          </mat-error>
          <mat-error *ngIf="dynamicFormGroup.get('observationInput')?.hasError('maxlength')">
            {{ 'Maximum length exceeded' | translate }}
          </mat-error>
        </mat-form-field>

      </div>
    </div> -->
    <div class="row">
      <div style="text-align-last: center;" class="col-md-12 mb-2">
        <button class="mx-2" *ngIf="!isInternal && item.documentTable!==undefined && !isClientProcedure" mat-raised-button (click)="goToBack()">
          <mat-icon fontIcon="arrow_back" iconPositionStart></mat-icon>
          {{ "Cancel" | translate }}
        </button>
        <button type="button" color="primary" mat-raised-button (click)="saveTask()"
          *ngIf="item.documentTable!==undefined" [disabled]="isViewing">
          {{ "Save" | translate }}
        </button>
      </div>
    </div>
  </div>


</ng-container>


