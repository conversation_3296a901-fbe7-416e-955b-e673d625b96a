<!-- Dettale de la póliza -->
<div class="mt-3">
    <app-details-policy [detailsPolicyData]="detailsPolicyData" [idWtw]="idWtw" [policyNumber]="policyNumber"
        [policyStatus]="policyStatus" [idPolicyType]="idPolicyType"></app-details-policy>
</div>

<!-- Renovación -->
<div class="mt-3 mb-5">
    <button class="btn-custom" type="button" mat-raised-button (click)="goToRenewal()">
        <strong>{{ "Policy.Renew" | translate }}</strong>
    </button>
</div>

<!-- Tabla de riesgos -->
<div class="mt-5">
    <app-risk-table [estructTable]="estructTableRisk" [idPolicy]="idWtw" [idtypePolicy]="idPolicyType"></app-risk-table>
</div>

<!-- Tabla Historico de movimientos de póliza-->
<div class="mt-5">
    <app-history-table [estructTable]="estructTablePolicyMovementHistory" [historyData]="policyMovementHistoryData"
        [title]="titlePolicyMovementHistory"
        (actionTable)="getActionTableModificationHistory($event)" ></app-history-table>
</div>

<!-- Botón Regresar -->
<div class="d-flex justify-content-center mt-3">
    <button mat-raised-button type="button" (click)="goBack()">
        <mat-icon fontIcon="arrow_back"></mat-icon>
        {{ "Back" | translate }}
    </button>
</div>

<!-- modal ver historico de movimiento de pólizas -->
<ng-template #viewHistoryMovementModal>
    <app-modal2 [titleModal]="typeMovement | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <app-page-see-detail-movement-history-colective-individual-policy [idHistory]="idHistoryPolicy"
                [idPolicyType]="idPolicyType"
                [typeMovement]="typeMovement"></app-page-see-detail-movement-history-colective-individual-policy>
        </ng-container>

        <!-- Botón Cerrar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary"
                (click)="closeModalviewHistoryMovement()">
                {{ "Close" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>
