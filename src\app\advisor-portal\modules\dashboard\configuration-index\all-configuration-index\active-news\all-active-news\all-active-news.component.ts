import { CommonModule } from '@angular/common';
import { Component, Input, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { LangChangeEvent, TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription, catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ActiveNewsModel } from 'src/app/shared/models/configuration-index/active-news.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { BodyTableModel,IconEventClickModel} from 'src/app/shared/models/table';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { NewsService } from 'src/app/shared/services/news/news.service';
import { EditActiveNewsComponent } from '../edit-active-news/edit-active-news.component';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { HeadsNews } from 'src/app/shared/models/configuration-index/HeadsNews';
@Component({
  selector: 'app-all-active-news',
  standalone: true,
  imports: [CommonModule, TableComponent, TranslateModule, MatIconModule, MatButtonModule,
    Modal2Component, EditActiveNewsComponent,ReactiveFormsModule,MatInputModule,
    PreventionSqlInjectorDirective],
  templateUrl: './all-active-news.component.html',
  styleUrls: ['./all-active-news.component.scss']
})
export class AllActiveNewsComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  @Input() idBusinessByCountry: number = 0;
  @Input() objetActNews: any = {};
  @Input() userType: number = 0;

  @ViewChild('editActiveNewsModal') editActiveNewsModal?: TemplateRef<any>;
  titelModal: string = this._translateService.instant('News.AddNews');
  newsId: number = 0;
  activeNewsDataTable: ActiveNewsModel[] = [];
  estructActiveNewsTable: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant('News.Title'),
      columnValue: 'vTitle',
    },
    {
      columnLabel: this._translateService.instant('News.SubTitle'),
      columnValue: 'vSubTitle',
    },
    {
      columnLabel: this._translateService.instant('News.DateExpiry'),
      columnValue: 'dEndDate',
    },
    {
      columnLabel: this._translateService.instant('Action'),
      columnValue: 'modify',
      columnIcon: 'edit',
    }
  ];
  
  formConfigurations: FormGroup = this._fb.group({
    vTitleLanding: [null, Validators.required],
    vSubTitleLanding: [null, Validators.required],

  })

  constructor(
    private _fb: FormBuilder,
    public utilsSvc: UtilsService,
    public _translateService: TranslateService,
    private _msgSvc: MessageService,
    public modalDialog: MatDialog,
    private _newsService: NewsService,
  ) { }

  ngOnInit(): void {
    this.getNewsByIdBusinessCountry(this.idBusinessByCountry);
    this.getHeadsNewsByIdBusinessCountry(this.idBusinessByCountry);
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //traduccion data table active news
      this.estructActiveNewsTable[0].columnLabel =  this._translateService.instant('News.Title')
      this.estructActiveNewsTable[1].columnLabel =  this._translateService.instant('News.SubTitle')
      this.estructActiveNewsTable[2].columnLabel =  this._translateService.instant('News.DateExpiry');
      this.estructActiveNewsTable[3].columnLabel = this._translateService.instant('Action');
    });
  }

  getNewsByIdBusinessCountry(idBusinessByCountry: number) {
    this._newsService.getNewsByIdBusinessCountry(idBusinessByCountry, this.userType)
      .pipe(
        catchError((error) => {
          if(error.error.error){
            this._msgSvc.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          console.log("El tipo de datos devueltos es un array vacío.");
          this.activeNewsDataTable = [];
        } else {
            this.activeNewsDataTable = resp.result;
        }
      });
  }

  
  getHeadsNewsByIdBusinessCountry(idBusinessByCountry: number) {
    this._newsService.getHeadNewsById(idBusinessByCountry)
      .pipe()
      .subscribe((resp: ResponseGlobalModel | never[]) => {

        if (Array.isArray(resp)) {
        } else {
          this.formConfigurations.get("vTitleLanding")?.setValue(resp.result.vTitleNew);
          this.formConfigurations.get("vSubTitleLanding")?.setValue(resp.result.vSubTitleNew);
        }
      });
  }

 insertToUpdateHeadNews(){

  const gg : number = 1

  if((this.formConfigurations.value.vTitleLanding == null || "" )&&( this.formConfigurations.value.vSubTitleLanding == null || "")){
    return null
  }

  const headNews: HeadsNews = new HeadsNews(
    this.formConfigurations.value.vTitleLanding,
    this.formConfigurations.value.vSubTitleLanding,
    this.idBusinessByCountry
  );
  this._newsService.createToUpdateHeadNews(headNews)
  .pipe(
    catchError((error) => {
      if(error.error.error){
        this._msgSvc.messageWaring(
          this._translateService.instant('ThereWasAError'),
          error.error.message
        );
      }
      return of([]);
    })
  )
  .subscribe((resp: ResponseGlobalModel | never[]) => {
  });

  return;
}

  gotToCreate() {
    this.insertToUpdateHeadNews();
    this.newsId = 0;
    this.titelModal = this._translateService.instant('News.AddNews');
    this.openModal();
  }

  openModal() {
    this.modalDialog.open(this.editActiveNewsModal!, {
      width: '80vw',
      height: '80vh',
    });
  }

  controller(evt: IconEventClickModel) {
    switch (evt.column) {
      case 'modify':
        this.newsId = evt.value.pkIIdNews;
        this.titelModal = this._translateService.instant('News.ModifyNews');
        this.openModal();
        break;
      default:
        break;
    }
  }

  eventCloseModal(event: any) {
    this.newsId = 0;
  }

  ngOnDestroy(): void {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
