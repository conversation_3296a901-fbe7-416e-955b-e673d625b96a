import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { RouterModule } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-catalog-setting',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, BreadcrumbComponent, MatTooltipModule, MatIconModule],
  template: `
    <div>
      <h2 class="h3">
        <img src="assets/img/layouts/config_ico.svg" alt="" />
         {{"ConfigurarCamposCatalogo" | translate}}
         <mat-icon class="click" matTooltipPosition="right" matTooltip="{{ 'Tooltips.CatalogSettingTitle' | translate }}">help_outline</mat-icon>
      </h2>
    </div>
    <app-breadcrumb [breadcrumbSections]="sections"></app-breadcrumb>

    <router-outlet></router-outlet>
  `,
  styleUrls: [],
})
export class CatalogSettingComponent implements OnInit {
  constructor(private _translateService: TranslateService) {}

  inicio: string = this._translateService.instant('Configuración');
  catalog: string = this._translateService.instant('CatalogSetting.CatalogFields');

  sections: { label: string; link: string }[] = [
    { label: this.inicio, link: '/dashboard' },
    { label: this.catalog, link: '/dashboard/catalog-setting' },
  ];

  ngOnInit(): void {
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.inicio = this._translateService.instant('Configuración');
      this.catalog = this._translateService.instant('CatalogSetting.CatalogFields');
      this.sections[0].label = this.inicio;
      this.sections[1].label = this.catalog;
    });
  }
}
