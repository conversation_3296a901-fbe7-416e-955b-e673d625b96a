import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, TemplateRef, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { PageEvent } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { catchError, of } from 'rxjs';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  FilterPolicyModel,
  InsurancePolicyModel,
  PolicyModel,
  PolicyTypeModel,
  ProductPolicyModel,
} from 'src/app/shared/models/policy';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { GlobalSelectModel } from 'src/app/shared/models/shared';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { ParametersService } from 'src/app/shared/services/parameters/parameters.service';
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';

@Component({
  selector: 'app-mass-creation-policy',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    TableComponent,
    Modal2Component,
  ],
  templateUrl: './mass-creation-policy.component.html',
  styleUrls: ['./mass-creation-policy.component.scss'],
})
export class MassCreationPolicyComponent implements OnInit {
  //Variables relacionadas con los modales.
  @ViewChild('filtersModal') filtersModal?: TemplateRef<any>;

  //Variables para filtrar la data de la tabla.
  keyword: string = '';
  //Tipos de póliza
  policyTypes: PolicyTypeModel[] = [];

  //Variables relacioandas con la tabla.
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 5;
  currentPosition: number = 0;
  showTable: boolean = false;
  dataTablePolicy: PolicyModel[] = [];
  estructTablePolicy: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      ),
      columnValue: 'idPolicy',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyNumber'
      ),
      columnValue: 'policyNumber',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      ),
      columnValue: 'insuranceName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      ),
      columnValue: 'productName',
    },
    {
      columnLabel: this._translateService.instant(
        'validityTypeName'
      ),
      columnValue: 'validityName',
    },
    {
      columnLabel: this._translateService.instant(
        'PolicyConfiguration.Table.Type'
      ),
      columnValue: 'policyTypeName',
    },
    {
      columnLabel: this._translateService.instant('Status'),
      columnValue: 'active',
      functionValue: (item: any) => this._utilsSvc.changeStatusValue(item),
    },
    {
      columnLabel: this._translateService.instant('Gestión masiva'),
      columnValue: 'massManagement',
      columnIcon: 'upload',
    },
  ];

  //variable formulario.
  formFilter: FormGroup = new FormGroup({});
  listSelectedStates: number[] = [];
  listSelectedPolicyType: number[] = [];
  productList: ProductPolicyModel[] = [];
  insuranceList: InsurancePolicyModel[] = [];
  idBusinessByCountry: number = 0;
  policyType: any = null;
  requestFilterPolicy: FilterPolicyModel = {
    active: null,
    idBusinessCountry: 0,
    idInsurance: null,
    idPolicyType: null,
    idProduct: null,
    keyword: null,
    order: 'desc',
    page: 0,
    pageSize: 5,
  };
  typeOfValidity: GlobalSelectModel[] = [];
  private localStorageService = inject(LocalStorageService);
  constructor(
    private _messageService: MessageService,
    private _transactionService: TransactionService,
    private _fb: FormBuilder,
    private _utilsSvc: UtilsService,
    public matDialog: MatDialog,
    private _translateService: TranslateService,
    private _activatedRoute: ActivatedRoute,
    private _customeRouter: CustomRouterService,
    private _parametersService: ParametersService,
  ) { }

  ngOnInit(): void {
    this.getBusinessByCountry();
    this.getAllPolicyType();
    this.getAllValidyOfType();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.estructTablePolicy[0].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.IdWTW'
      );
      this.estructTablePolicy[1].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.PolicyNumber'
      );
      this.estructTablePolicy[2].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Insurance'
      );
      this.estructTablePolicy[3].columnLabel = this._translateService.instant(
        'PolicyConfiguration.GeneralInformation.Product'
      );
      this.estructTablePolicy[4].columnLabel = this._translateService.instant(
        'PolicyConfiguration.Table.Type'
      );
      this.estructTablePolicy[5].columnLabel =
        this._translateService.instant('Status');
      this.estructTablePolicy[6].columnLabel =
        this._translateService.instant('Modify');
    });
  }

  //Obtiene el idBusinessByCountry por medio de la URL.
  getBusinessByCountry() {
    this._activatedRoute.params.subscribe((params: any) => {
      if (params.idBusinessByCountry) {
        this.idBusinessByCountry = Number(params.idBusinessByCountry);
        if (this.idBusinessByCountry > 0) {
          this.requestFilterPolicy.idBusinessCountry = this.idBusinessByCountry;
        }
      }
    });
  }

  //Delcaración del formulario formFilter.
  initFilterForm() {
    this.formFilter = this._fb.group({
      idProduct: null,
      idInsurance: null,
      active: null,
      idPolicyType: [],
      idBusinessCountry: this.idBusinessByCountry,
      page: this.pageIndex,
      pageSize: 5,
      order: 'desc',
    });
  }

  // Función que se ejecuta al detectar un cambio en el select
  onPolicyTypeChange(event: any): void {
    let selectedValue: number[] = [];
    selectedValue = [event.value];
    event.value > 0 ? (this.showTable = true) : (this.showTable = false);
    this.requestFilterPolicy.idPolicyType = selectedValue;
    this.filterGeneralPolicyData(this.requestFilterPolicy);
  }

  //Obtiene los tramites filtrados por empresa pais y otros filtros opcionales.
  getAllPolicyType() {
    this._transactionService
      .getAllPolicyType()
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.policyTypes = resp.result;
        }
      });
  }

  //Obtiene todos los productos que esten asociados a pólizas por idBusinessCountry.
  getProductsWithAssociatedPolicies() {
    this._transactionService
      .getProductsWithAssociatedPolicies(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.productList = resp.result;
        }
      });
  }

  //Obtiene todas las aseguradoras que esten asociadas a pólizas por idBusinessCountry.
  getInsurancesWithAssociatedPolicies() {
    this._transactionService
      .getInsurancesWithAssociatedPolicies(this.idBusinessByCountry)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.insuranceList = resp.result;
        }
      });
  }

  //Obtiene las pólizas registradas en el sistema por medio de un filtro.
  filterGeneralPolicyData(model: FilterPolicyModel) {
    this._transactionService
      .filterGeneralPolicyData(model)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring('', error.error.message);
          this.dataTablePolicy = [];
          this.matDialog.closeAll();
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.dataTablePolicy = [];
          this.matDialog.closeAll();
        } else {
          this.dataTablePolicy = resp.result;
          this.formatData();
          this.amountRows = resp.rowCount;
          this.matDialog.closeAll();
        }
      });
  }

  //Función que se ejecuta al dar enter o click en el icono de busqueda, del input buscar.
  search(keyword: string) {
    this.requestFilterPolicy.keyword = keyword;
    this.requestFilterPolicy.page = 0;
    this.filterGeneralPolicyData(this.requestFilterPolicy);
  }

  //Controlador de las acciones configuradas en la tabla.
  controller(event: IconEventClickModel) {
    switch (event.column) {
      case 'massManagement':
        this.localStorageService.setItem('selectedPolicy', JSON.stringify(event.value));
        this._customeRouter.navigate([
          `dashboard/massive/mass-management-policy/${this.idBusinessByCountry}`,
        ]);
        break;
      default:
        break;
    }
  }

  //Detecta los cambios en la paginación de la tabla.
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.currentPosition = this.pageIndex * this.pageSize;

    // Asegúrate de que pageIndex no sea negativo
    if (this.pageIndex < 0) {
      this.pageIndex = 0;
    }
    this.requestFilterPolicy.page = this.currentPosition;
    this.requestFilterPolicy.pageSize = this.pageSize;

    let payload: FilterPolicyModel = this.requestFilterPolicy;
    this.filterGeneralPolicyData(payload);
  }

  //Función que abre el modal para filtrar de filtros.
  openFilterDialog() {
    this.getAllPolicyType();
    this.getProductsWithAssociatedPolicies();
    this.getInsurancesWithAssociatedPolicies();
    const dialogRef = this.matDialog.open(this.filtersModal!, {
      width: '30vw',
      maxHeight: '90vh',
    });
    this.initFilterForm();
    this.formFilter
      .get('idProduct')
      ?.setValue(this.requestFilterPolicy.idProduct);
    this.formFilter
      .get('idInsurance')
      ?.setValue(this.requestFilterPolicy.idInsurance);
    this.formFilter
      .get('idPolicyType')
      ?.setValue(this.requestFilterPolicy.idPolicyType);
  }

  //Función que aplica los filtros seleccionados en el modal de filtros generales.
  applyFilters() {
    this.pageIndex = 0;
    let payload: FilterPolicyModel = this.formFilter.value;
    payload.idBusinessCountry = this.idBusinessByCountry;
    payload.page = 0;
    this.requestFilterPolicy = payload;
    this.filterGeneralPolicyData(payload);
  }

  //Función que borra los filtros aplicados a el formulario formFilter.
  cleanFilterForm() {
    this.formFilter.reset();
    this.listSelectedPolicyType = [];
    this.listSelectedStates = [];
    this.requestFilterPolicy = {
      active: null,
      idBusinessCountry: this.idBusinessByCountry,
      idInsurance: null,
      idPolicyType: null,
      idProduct: null,
      keyword: null,
      order: 'desc',
      page: 0,
      pageSize: 5,
    };
    this.filterGeneralPolicyData(this.requestFilterPolicy);
  }

  //Regresa al menú principal de masivos.
  goBackMassive() {
    this._customeRouter.navigate([`dashboard/massive`]);
  }

  //Obtiene todos los tipos de vigencia registrados en el sistema.
  getAllValidyOfType() {
    this._parametersService
      .getParameters('Type_Of_Validity')
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
          this.typeOfValidity = resp;
        } else {
        }
      });
  }

  formatData() {
    const updatedPolicies = this.dataTablePolicy.map(policy => {
      const validity = this.typeOfValidity.find(type => type.value === policy.fkIIdValidityType);

      return {
        ...policy,
        validityName: validity && policy.idPolicyType !== 1 ? validity.name : "No aplica"
      };
    });

    this.dataTablePolicy = updatedPolicies;
  }
}
