<div class="row">
    <div class="col-md-12">
        <app-choose-country-and-company (valueForm)="validForm($event)"></app-choose-country-and-company>
    </div>
</div>

<!-- <PERSON><PERSON><PERSON><PERSON> configuradas-->
<div class="row mb-3">
    <div class="col-md-12 col-sm-12">
        <h4>{{ "PolicyConfiguration.PoliciesConfigured" | translate }}</h4>
    </div>
</div>

<div class="row">
    <div class="col-md-11">
        <!-- input de busqueda en la tabla -->
        <mat-form-field class="w-100">
            <mat-label>
                {{ "Search" | translate }}
            </mat-label>
            <input (keyup.enter)="search(keyword)" [(ngModel)]="keyword" matInput type="text" class="form-control"
                placeholder="{{ 'Search' | translate }}" />
            <mat-icon class="hand click" (click)="search(keyword)" matSuffix>search</mat-icon>
        </mat-form-field>
    </div>
    <div class="col-md-1">
        <!-- boton que abre el modal de filtrar -->
        <button class=" mr-2 mt-1" type="button" color="primary" (click)="openFilterDialog()" mat-raised-button>
            {{ "Filter" | translate }}
            <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
        </button>
    </div>
</div>

<!-- datatable Pólizas -->
<div class="row mt-2">
    <app-table *ngIf="dataTablePolicy.length > 0" [displayedColumns]="estructTablePolicy" [data]="dataTablePolicy"
        [IsStatic]="false" [pageIndex]="pageIndex" [pageSize]="pageSize" [amountRows]="amountRows"
        (pageChanged)="onPageChange($event)" (iconClick)="controller($event)"></app-table>
</div>

<!-- Configuración póliza -->
<button class="mt-1" type="button" color="primary" (click)="policyConfiguration()" mat-raised-button>
    {{ "PolicyConfiguration.AddPolicy" | translate }}
</button>

<!-- modal filtros -->
<ng-template #filtersModal>
    <app-modal2 [titleModal]="'Filter' | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <form [formGroup]="formFilter">
                <!-- Producto -->
                <div class="col-md-12 mb-2">
                    <mat-form-field appearance="outline" class="select-look w-50 m-auto w-100">
                        <mat-label>
                            {{ "FormsConfigurationHistory.Product" | translate }}
                        </mat-label>
                        <mat-select formControlName="idProduct">
                            <mat-option *ngFor="let product of productList" [value]="product.pkIIdProduct">
                                {{ product.vProductName }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- Aseguradora -->
                <div class="col-md-12 mb-2">
                    <mat-form-field class="w-100" appearance="fill">
                        <mat-label>
                            {{"ReportLogCarrier.InsuranceLabel" | translate}}
                        </mat-label>
                        <mat-select formControlName="idInsurance">
                            <mat-option *ngFor="let insurance of insuranceList"
                                [value]="insurance.pkIIdInsuranceCompanies">
                                {{ insurance.vName }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- estado -->
                <div class="row mt-3">
                    <h4 class="mb-2">{{ "Status" | translate }}</h4>
                    <ul *ngFor="let estado of statusList">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" value="{{ estado.id }}"
                                id="CheckAction{{ estado.id }}" (change)="checkedChangeStatus(estado.id)"
                                [checked]="checkedStatus(estado.id)" />
                            <label class="form-check-label" for="CheckAction{{ estado.name }}">
                                {{ estado.name | translate }}
                            </label>
                        </div>
                    </ul>
                </div>

                <!-- Tipo -->
                <div class="row mt-3">
                    <h4 class="mb-2">{{ "Product.Type" | translate }}</h4>
                    <ul *ngFor="let type of policyTypes">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" value="{{ type.pkIIdPolicyType }}"
                                id="CheckAction{{ type.pkIIdPolicyType }}" [checked]="checkedType(type.pkIIdPolicyType)"
                                (change)="checkedChangeType(type.pkIIdPolicyType)" />
                            <label class="form-check-label" for="CheckAction{{ type.vPolicyTypeName }}">
                                {{ type.vPolicyTypeName | translate }}
                            </label>
                        </div>
                    </ul>
                </div>
            </form>
        </ng-container>

        <!-- Botón Borrar filtros -->
        <ng-container customButtonCenter>
            <button (click)="cleanFilterForm()" class="btn-custom w-100" type="button" mat-raised-button>
                <strong>{{ "PolicyConfiguration.Filter.DeleteFilters" | translate }}</strong>
            </button>
        </ng-container>
        <!-- Botón Aplicar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary" (click)="applyFilters()">
                {{ "Apply" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>