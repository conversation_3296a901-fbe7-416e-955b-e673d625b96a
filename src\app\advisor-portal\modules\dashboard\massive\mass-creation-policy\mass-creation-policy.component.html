<!-- Subtitu<PERSON> Tipo de póliza -->
<div class="row">
    <h5 class="title-local mb-1">{{ 'PolicyConfiguration.PolicyTypes' | translate }}</h5>
    <p class="description">{{ 'BulkUpload.Massives.MassCreationPolicy.DescriptionOne' | translate }}</p>
</div>

<!-- Tipo de póliza -->
<div class="row">
    <div class="col-md-12">
        <mat-form-field class="w-100" appearance="fill">
            <mat-label>{{ 'PolicyConfiguration.PolicyTypes' | translate }}</mat-label>
            <mat-select (selectionChange)="onPolicyTypeChange($event)">
                <mat-option *ngFor="let item of policyTypes" [value]="item.pkIIdPolicyType">
                    {{ item.vPolicyTypeName }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
</div>

<!-- Subtitulo Pólizas configuradas-->
<div class="row mt-2">
    <h5 class="title-local mb-1">{{ 'BulkUpload.Massives.MassCreationPolicy.Subtitle' | translate }}</h5>
    <p class="description">{{ 'BulkUpload.Massives.MassCreationPolicy.DescriptionTwo' | translate }}</p>
</div>

<ng-container *ngIf="showTable">
    <div class="row">
        <div class="col-md-11">
            <!-- input de busqueda en la tabla -->
            <mat-form-field class="w-100">
                <mat-label>
                    {{ "Search" | translate }}
                </mat-label>
                <input (keyup.enter)="search(keyword)" [(ngModel)]="keyword" matInput type="text" class="form-control"
                    placeholder="{{ 'Search' | translate }}" />
                <mat-icon class="hand click" (click)="search(keyword)" matSuffix>search</mat-icon>
            </mat-form-field>
        </div>
        <div class="col-md-1">
            <!-- boton que abre el modal de filtrar -->
            <button class=" mr-2 mt-1" type="button" color="primary" (click)="openFilterDialog()" mat-raised-button>
                {{ "Filter" | translate }}
                <mat-icon iconPositionEnd fontIcon="filter_list"></mat-icon>
            </button>
        </div>
    </div>

    <!-- datatable Pólizas -->
    <div class="row mt-2">
        <app-table *ngIf="dataTablePolicy.length > 0" [displayedColumns]="estructTablePolicy" [data]="dataTablePolicy"
            [IsStatic]="false" [pageIndex]="pageIndex" [pageSize]="pageSize" [amountRows]="amountRows"
            (pageChanged)="onPageChange($event)" (iconClick)="controller($event)"></app-table>
    </div>
</ng-container>

<div class="d-flex justify-content-center mt-3">
    <a  class="label-button" mat-button (click)="goBackMassive()"><span>{{"BulkUpload.Massives.MassCreationPolicy.GoOutToMass" | translate}}</span>
        <mat-icon fontIcon="arrow_back"></mat-icon>
    </a>
</div>

<!-- modal filtros -->
<ng-template #filtersModal>
    <app-modal2 [titleModal]="'Filter' | translate" [showCancelButtonBelow]="false">
        <ng-container body>
            <form [formGroup]="formFilter">
                <!-- Producto -->
                <div class="col-md-12 mb-2">
                    <mat-form-field appearance="outline" class="select-look w-50 m-auto w-100">
                        <mat-label>
                            {{ "FormsConfigurationHistory.Product" | translate }}
                        </mat-label>
                        <mat-select formControlName="idProduct">
                            <mat-option *ngFor="let product of productList" [value]="product.pkIIdProduct">
                                {{ product.vProductName }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- Aseguradora -->
                <div class="col-md-12 mb-2">
                    <mat-form-field class="w-100" appearance="fill">
                        <mat-label>
                            {{"ReportLogCarrier.InsuranceLabel" | translate}}
                        </mat-label>
                        <mat-select formControlName="idInsurance">
                            <mat-option *ngFor="let insurance of insuranceList"
                                [value]="insurance.pkIIdInsuranceCompanies">
                                {{ insurance.vName }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </form>
        </ng-container>

        <!-- Botón Borrar filtros -->
        <ng-container customButtonCenter>
            <button (click)="cleanFilterForm()" class="btn-custom w-100" type="button" mat-raised-button>
                <strong>{{ "PolicyConfiguration.Filter.DeleteFilters" | translate }}</strong>
            </button>
        </ng-container>
        <!-- Botón Aplicar -->
        <ng-container customButtonRight>
            <button class="w-auto" type="button" mat-raised-button color="primary" (click)="applyFilters()">
                {{ "Apply" | translate }}
            </button>
        </ng-container>
    </app-modal2>
</ng-template>
