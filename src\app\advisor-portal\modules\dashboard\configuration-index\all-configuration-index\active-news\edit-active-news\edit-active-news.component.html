<form [formGroup]="formActiveNews">
  <div class="row">
    <div class="col-12 col-md-6">
      <mat-form-field class="w-100 mb-2" appearance="fill">
        <mat-label>
          {{ "Product.Type" | translate }}
        </mat-label>
        <mat-select formControlName="fkIIdSizeNew" (selectionChange)="SizeChange($event)">
          <mat-option *ngFor="let item of listSize" [value]="item.pkIIdSizeNew">
            {{ item.vSize }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="utilsSvc.isControlHasError(formActiveNews, 'fkIIdSizeNew', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field class="w-100" appearance="fill">
        <mat-label>
          {{ "Category" | translate }}
        </mat-label>
        <input matInput formControlName="vCategory" (change)="category_Change()" PreventionSqlInjector />
        <mat-error *ngIf="utilsSvc.isControlHasError(formActiveNews, 'vCategory', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field class="w-100" appearance="fill">
        <mat-label>
          {{ "News.Title" | translate }}
        </mat-label>
        <input matInput formControlName="vTitle" (change)="title_Change()" />
        <mat-error *ngIf="utilsSvc.isControlHasError(formActiveNews, 'vTitle', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
      
      <mat-form-field class="w-100 mb-2" appearance="fill">
        <mat-label>
          {{ "News.Text" | translate }}
        </mat-label>
        <input matInput formControlName="vDescription" (change)="description_Change()"/>
        <mat-error *ngIf="utilsSvc.isControlHasError(formActiveNews, 'vDescription', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
        <mat-error *ngIf="utilsSvc.isControlHasError(formActiveNews, 'vDescription', 'maxlength')">
          {{ "MaximumCharacters" | translate }}
        </mat-error>
      </mat-form-field>

                        <mat-slide-toggle formControlName="bActiveButton" class="mb-2">
                            {{ 'configurationClientPortal.button' | translate }}
                        </mat-slide-toggle>

                         <p></p>
                        <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                        <mat-form-field appearance="outline" class="w-100 mb-2">
                            <mat-label>{{ 'configurationClientPortal.textbuttom' | translate }}</mat-label>
                            <input matInput formControlName="vTextButton" />                          
                        </mat-form-field>

      <mat-form-field class="w-100 mb-2" appearance="fill">
        <mat-label>
          {{ "News.Link" | translate }}
        </mat-label>
        <input matInput formControlName="vLink" (change)="link_Change()" />
        <mat-error *ngIf="utilsSvc.isControlHasError(formActiveNews, 'vLink', 'required')">
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field class="w-100">
        <mat-label>
          {{ "News.DateExpiry" | translate }}
        </mat-label>
        <input matInput [matDatepicker]="dEndDate" formControlName="dEndDate" />
        <!-- <mat-hint>MM/DD/YYYY</mat-hint> -->
        <mat-datepicker-toggle matIconSuffix [for]="dEndDate"></mat-datepicker-toggle>
        <mat-datepicker #dEndDate></mat-datepicker>
      </mat-form-field>

      <app-upload-image-varies-ext [showUploadedImage]="false" [showBtnDownload]="false"
                                   (changeFile)="changeFile($event)" (deleteFile)="deleteFile($event)"></app-upload-image-varies-ext>
    </div>

    <div class="col-12 col-md-6">
      <mat-card class="sm-card" *ngIf="size == 1">
        <mat-card-content>
          <div class="content">
            <img mat-card-image [src]="imageSrc">
            <div class="text-content">
              <div class="content-label">{{category}}</div>
              <div class="content-title">{{title}}</div>
            </div>
            <mat-icon class="arrow-icon">arrow_forward</mat-icon>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card *ngIf="size == 2">
        <img mat-card-image [src]="imageSrc">
        <mat-card-header>
          <mat-card-title>
            <h3>{{category}}</h3>
          </mat-card-title>
          <mat-card-subtitle>
            <strong>{{title}}</strong>
          </mat-card-subtitle>
        </mat-card-header>
        <br>
        <mat-card-content>
          <p>{{description}}</p>
          <p *ngIf="link != '' && link != null"><a [href]="link">Seguir leyendo ></a></p>
        </mat-card-content>
      </mat-card>
    </div>

  </div>
  <div class="d-flex justify-content-center">
    <div class="mt-3">
      <button class="mx-2" (click)="cancel()" type="button" mat-raised-button>
        {{ "Cancel" | translate }}
      </button>
      <button class="mx-2" (click)="deleteteNews()" type="button" mat-raised-button color="warn">
        <mat-icon iconPositionEnd fontIcon="delete"></mat-icon>
        {{ "Delete" | translate }}
      </button>

      <button (click)="completeNews()" type="button" mat-raised-button color="primary" [disabled]="!formValid">
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "SaveChanges" | translate }}
      </button>
    </div>
  </div>
</form>
