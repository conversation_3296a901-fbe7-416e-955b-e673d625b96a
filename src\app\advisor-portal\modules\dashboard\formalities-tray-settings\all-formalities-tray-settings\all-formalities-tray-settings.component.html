<app-choose-country-and-company></app-choose-country-and-company>
<div class="row mb-2 mt-2">
    <h4 class="col-md-12">{{ "ProceduresViewerSettings.ViewerItems" | translate }}</h4>
</div>

<div>
    <app-table
        [displayedColumns]="estructProceduresViewerSettingsTable"
        [data]="proceduresViewerSettingsDataTable"
        (iconClick)="controller($event)"
    ></app-table>
</div>

<!-- modal editar item -->
<ng-template #editElementModal>
  <app-modal2 [titleModal]="'ProceduresViewerSettings.ItemOfProceduresViewer' | translate">
    <ng-container body>
      <form [formGroup]="form">
        <div class="row">  
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{
                  "ProceduresViewerSettings.OrderInTable" | translate
                }}
              </mat-label>
              <mat-select formControlName="iOrder">
                <mat-option *ngFor="let order of orderList" [value]="order">
                  {{ order }}
                </mat-option>
              </mat-select>
            </mat-form-field>
        </div>
        <div class="cont-slide">
          <div class="mx-3">
            <mat-slide-toggle class="mb-3" formControlName="bActive">
              {{
                "TaskTraySettings.FormCreateEditElement.LabelActive" | translate
              }}
            </mat-slide-toggle>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="complete()"
      >
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "Save" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>