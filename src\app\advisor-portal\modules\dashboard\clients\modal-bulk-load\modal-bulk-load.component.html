<h2 mat-dialog-title>
    <div class="row">
        <div class="col-md-10" style="margin-top: -25px;">
            <strong>
                <h4 style="font-weight: 700;">{{ 'Client.BulkLoad' | translate }}</h4>
            </strong>
        </div>
        <div class="col-md-2">
            <button mat-icon-button class="close-button" (click)="close()">
                <mat-icon>close</mat-icon>
            </button>
        </div>
    </div>
</h2>

<div style="margin-left: 150px; margin-top: 30px;">
    <strong>
        <h6 style="font-weight: 600;">{{ 'Client.Template' | translate }}</h6>
    </strong>
</div>
<div class="upload-container">
    <mat-card class="download-card" (click)="downloadTemplate()">
        <div class="row">
            <div class="col-md-10">
                <p>{{ 'Client.TemplateBulkLoad' | translate }}</p>
                <p style="margin-top: -20px;">200 MB</p>
            </div>
            <div class="col-md-2" style="margin-top: 6px;">
                <mat-icon
                    style="overflow: visible; font-size: 25px; color: rgba(27, 27, 214, 0.699);">download</mat-icon>
            </div>
        </div>
    </mat-card>
</div>

<div style="margin-left: 150px;">
    <strong>
        <h6 style="font-weight: 600;">{{ 'Upload' | translate }}</h6>
    </strong>
</div>
<div class="upload-container">
    <mat-card class="upload-card" cdkDropList (cdkDropListDropped)="onFileDropped($event)"
        (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)" (drop)="onFileDropped($event)"
        [class.drop-zone-active]="isDragOver">
        <div class="upload-content" (click)="fileInput.click()">
            <mat-icon class="mb-5" style="overflow: visible; margin-right: 23px">cloud_upload</mat-icon>
            <p style="font-weight: 600;"><span style="color: rgba(0, 52, 163, 0.63);">{{ 'Client.ClickToAdd' | translate
                    }}</span> {{ 'Client.OrDragAndDropFilesHere' | translate }}</p>
            <p style="font-size: 12px; margin-top: -4px;">{{ 'Client.AcceptedFormat' | translate }} .XLSX</p>
            <input type="file" #fileInput (onChangeValidated)="onFileSelected($event)" hidden ValidationInputFile [allowedExtensions]="['xlsx']">

        </div>
    </mat-card>
    <div *ngIf="fileName" class="file-details">
        <p>{{ 'ModulesDynamic.DocumentName' | translate }}: {{ fileName }}</p>
    </div>
    <div *ngIf="errorMessage" class="error mb-4">
        {{ errorMessage }}
    </div>

    <div class="row mb-4">
        <div class="col-md-6 text-center mt-2">
            <button style="background-color: white; color: black; width: 180px;" mat-raised-button (click)="close()">
                {{ 'Cancel' | translate }}
            </button>
        </div>
        <div class="col-md-6 text-center mt-2">
            <button style="background-color: black; color: white; width: 180px;" mat-raised-button
                (click)="processFileClick()">
                {{ 'User.MasiveLoadModalBodySaveButtonText' | translate }}
            </button>
        </div>
    </div>

</div>