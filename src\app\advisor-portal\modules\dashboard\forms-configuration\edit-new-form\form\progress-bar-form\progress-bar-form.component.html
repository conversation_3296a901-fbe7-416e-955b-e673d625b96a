<div class="col-12 col-md-12">
  <div class="mb-2 d-flex justify-content-start align-items-center">
    <h3 style="margin: 0">
      {{ "FormConfiguration.ProgressBar.Title" | translate }}
    </h3>
    <mat-icon class="click ml-1" matTooltipPosition="right" matTooltip="{{ 'Tooltips.FormProgressBar' | translate }}">help_outline</mat-icon>
  </div>
  <app-table
    [displayedColumns]="estructTable"
    [data]="dataTableProgressBar"
    (iconClick)="controller($event)"
  ></app-table>
  <button
    class="mb-2"
    type="button"
    mat-raised-button
    color="primary"
    (click)="openModalCreateEditProgressBar()"
  >
    {{ "Add" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>
</div>

<!-- modal editar-crear paso de barra de progreso -->
<ng-template #editNewProgressBarModal>
  <app-modal2 [titleModal]="titelModal" (closeModal)="closeModal($event)">
    <ng-container body>
      <form [formGroup]="form">
        <div class="row mt-5">
          <div class="col-md-12 col-sm-12">
            <mat-slide-toggle class="mb-3" formControlName="bActive">
              {{ "FormConfiguration.ProgressBar.ActiveStepLabel" | translate }}
            </mat-slide-toggle>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{ "FormConfiguration.ProgressBar.StepNameLabel" | translate }}
              </mat-label>
              <input matInput formControlName="vName" />
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'vName', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field appearance="outline" class="w-100 mb-2">
              <mat-label>
                {{
                  "FormConfiguration.ProgressBar.StepNameDbLabel" | translate
                }}
              </mat-label>
              <input matInput formControlName="vNameDb" />
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'vNameDb', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ "FormConfiguration.ProgressBar.StepOrderLabel" | translate }}
              </mat-label>
              <mat-select formControlName="iOrder">
                <mat-option *ngFor="let order of orderList" [value]="order">
                  {{ order }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="utilsSvc.isControlHasError(form, 'iOrder', 'required')"
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      </form>
    </ng-container>

    <ng-container customButtonCenter>
      <button
        (click)="deleteStep()"
        type="button"
        mat-raised-button
        *ngIf="this.form.get('pkIIdProgressBar')?.value > 0"
        class="delete"
      >
        {{ "FormConfiguration.ProgressBar.DeleteButton" | translate }}
        <mat-icon class="delete" iconPositionEnd fontIcon="close"></mat-icon>
      </button>
    </ng-container>

    <ng-container customButtonRight>
      <button
        *ngIf="this.form.get('pkIIdProgressBar')?.value === 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="createStep()"
        [disabled]="!valid"
      >
        {{ "FormConfiguration.ProgressBar.AddStepButton" | translate }}
      </button>
      <button
        *ngIf="this.form.get('pkIIdProgressBar')?.value > 0"
        type="button"
        mat-raised-button
        color="primary"
        (click)="editStep()"
        [disabled]="!valid"
      >
        {{ "FormConfiguration.ProgressBar.SaveButton" | translate }}
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      </button>
    </ng-container>
  </app-modal2>
</ng-template>
