<div class="col-12 col-md-12 mt-3">
  <div class="row mb-2">
    <h3 class="col-md-12">{{ "Communications.Title" | translate }}</h3>
  </div>
  <span style="color: gray">{{ "Communications.TitleMessage" | translate }}</span>
</div>


<!-- datatable comunicaciones -->
<div class="row mt-2" style="padding-left: 1px;">
  <app-table
    [displayedColumns]="estructTableCommunications"
    [data]="dataTableCommunications"
    (iconClick)="controller($event)"
  ></app-table>
  <div *ngIf="dataTableCommunications?.length === 0" class="no-records-message">
    No existen registros.
  </div>
</div>
<!-- end datatable comunicaciones -->

<!-- boton añadir -->
<div class="row">
  <button
    class="mx-3 mt-2 col-2"
    type="button"
    (click)="openModalCreateEditElement()"
    mat-raised-button
    color="primary"
  >
  {{ "Communications.Add" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>

</div>
<!-- end boton añadir -->


<!-- modal editar-crear estados -->
<ng-template #createEditElementComunicationsModal>
  <app-modal2 [titleModal]="'Communications.TitleModal' | translate " (closeModal)="closeModalEvent($event)">
    <ng-container body>
      <form [formGroup]="form">
        <div class="row">

          <div class="cont-slide">
            <div class="mx-3">
              <mat-slide-toggle class="mb-3" formControlName="bEmailSend">
                {{ "Communications.ActivateCommunication" | translate }}  <!-- Activar comunicación -->
              </mat-slide-toggle>
            </div>

          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ "Communications.CurrentStatus" | translate }} <!-- Estado actual -->
              </mat-label>
              <mat-select formControlName="fkIIdStageByStateCurrent">
                <mat-option
                  *ngFor="let stageByState of stageByStateList"
                  [value]="stageByState.pkIIdStageByState"
                >
                  {{ stageByState.vState }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    form,
                    'fkIIdStageByStateCurrent',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                {{ "Communications.ChangeStatus" | translate }}  <!-- Cambio de estado a: -->
              </mat-label>
              <mat-select formControlName="fkIIdStageByState">
                <mat-option
                  *ngFor="let stageByStateChildren of stageByStateChildrenList"
                  [value]="stageByStateChildren.fkIIdStateChild"
                >
                  {{ stageByStateChildren.nameChildrenState }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    form,
                    'fkIIdStageByState',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="col-12 col-md-12">
            <mat-form-field class="w-100 mb-2" appearance="fill">
              <mat-label>
                Selección de plantilla
              </mat-label>
              <mat-select formControlName="fkIIdTemplate">
                <mat-option
                  *ngFor="let template of templateList"
                  [value]="template.pkIIdTemplate"
                >
                  {{ template.vName }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  utilsSvc.isControlHasError(
                    form,
                    'fkIIdTemplate',
                    'required'
                  )
                "
              >
                {{ "ThisFieldIsRequired" | translate }}
              </mat-error>
            </mat-form-field>
          </div>

        </div>
      </form>
    </ng-container>

    <ng-container customButtonRight>
      <button
        type="button"
        mat-raised-button
        color="primary"
        (click)="saveForm()"
        [disabled]="!validformCreateEditElement"
      >
        <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
        {{ "Save" | translate }}
      </button>
    </ng-container>
  </app-modal2>
</ng-template>

