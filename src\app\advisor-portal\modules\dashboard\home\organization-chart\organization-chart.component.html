<ng-container *ngIf="organizationChartObject.vImageBase64">
  <div class="cont-title mb-3">
    <h4 *ngIf="organizationChartObject.vName; else notName">
      {{ organizationChartObject.vName }}
    </h4>
    <ng-template #notName>
      <h4>{{ "HomeAdvisors.OrganizationChart.Title" | translate }}</h4>
    </ng-template>
  </div>

  <div class="cont-organization-chart">
    <button
      class="mb-2"
      mat-raised-button
      color="primary"
      (click)="openModal()"
    >
      {{ "HomeAdvisors.OrganizationChart.BtnOpenModal" | translate }}
    </button>
  </div>

  <!-- Modal para crear o editar un sectionFielValue -->
  <ng-template #openOrganizationChartModal>
    <app-modal2
      [titleModal]="titelModal"
      (closeModal)="eventCloseModal($event)"
    >
      <ng-container body>
        <div class="cont-img">
          <img [src]="imageSrc" alt="Imagen Organigrama" />
        </div>
      </ng-container>
    </app-modal2>
  </ng-template>
</ng-container>
