<form [formGroup]="form">

    <!-- Titulo del top -->
    <div class="row">
                            
        <mat-form-field appearance="outline" class="w-100 mb-2">
            <mat-label> 
                {{ 'TopTittle' | translate }} 
            </mat-label>
            <input
                (focusout)="emitModel()"
                matInput
                placeholder="{{ 'TopTittle' | translate }}"
                formControlName="vTopTittle"
                required
                type="text"
                PreventionSqlInjector
            />
            <mat-error
                *ngIf="_utilsService.isControlHasError(form, 'vTopTittle', 'required')"
            >
                {{ 'ThisFieldIsRequired' | translate }}
            </mat-error>
        </mat-form-field>

    </div>

    <!-- row cotizador 1 -->
    <div class="row">   
        <div class="col-1 mt-2">
            <h5>
                <span class="one">1</span>
            </h5>            
        </div>

        <mat-form-field appearance="outline" class="col-6 mb-2">
        <mat-label>  
            Top #1
        </mat-label>
        <input
            (focusout)="emitModel()"
            matInput
            placeholder="Top #1"
            formControlName="vQuote1"
            required
            type="text"
            PreventionSqlInjector
        />
        <mat-error
            *ngIf="
            _utilsService.isControlHasError(form, 'vQuote1', 'required')
            "
        >
            {{ 'ThisFieldIsRequired' | translate }}
        </mat-error>
        </mat-form-field>

        <div  *ngIf="!showTwo" class="col-1 mt-2">
            <h4>
                <a (click)="addRow(1)"><mat-icon iconPositionEnd fontIcon="add"></mat-icon></a>
            </h4>            
        </div>
    </div>
    
    <!-- row cotizador 2 -->
    <div *ngIf="showTwo" class="row">
        <div class="col-1 mt-2">
            <h5>
                <span class="two">2</span>
            </h5>            
        </div>

        <mat-form-field appearance="outline" class="col-6 mb-2">
        <mat-label>  
            Top #2
        </mat-label>
        <input
            (focusout)="emitModel()"
            matInput
            placeholder="Top #2"
            formControlName="vQuote2"
            required
            type="text"
            PreventionSqlInjector
        />
        <mat-error
            *ngIf="
            _utilsService.isControlHasError(form, 'vQuote2', 'required')
            "
        >
            {{ 'ThisFieldIsRequired' | translate }}
        </mat-error>
        </mat-form-field>

        <div *ngIf="!showThree" class="col-2 mt-2">
            <div class="row">
                <h4 class="col-6">
                    <a (click)="removeRow(2)"><mat-icon iconPositionEnd fontIcon="delete"></mat-icon></a>
                </h4>
                <h4 class="col-6">
                    <a (click)="addRow(2)"><mat-icon iconPositionEnd fontIcon="add"></mat-icon></a>
                </h4>  
            </div>                      
        </div>
    </div>

    <!-- row cotizador 3 -->
    <div *ngIf="showThree" class="row">
        
        <div class="col-1 mt-2">
            <h5>
                <span class="three">3</span>
            </h5>            
        </div>

        <mat-form-field appearance="outline" class="col-6 mb-2">
        <mat-label>  
            Top #3
        </mat-label>
        <input
            (focusout)="emitModel()"
            matInput
            placeholder="Top #3"
            formControlName="vQuote3"
            required
            type="text"
            PreventionSqlInjector
        />
        <mat-error
            *ngIf="
            _utilsService.isControlHasError(form, 'vQuote3', 'required')
            "
        >
            {{ 'ThisFieldIsRequired' | translate }}
        </mat-error>
        </mat-form-field>

        <div *ngIf="!showFour" class="col-2 mt-2">
            <div class="row">
                <h4 class="col-6">
                    <a (click)="removeRow(3)"><mat-icon iconPositionEnd fontIcon="delete"></mat-icon></a>
                </h4>
                <h4 class="col-6">
                    <a (click)="addRow(3)"><mat-icon iconPositionEnd fontIcon="add"></mat-icon></a>
                </h4>  
            </div>
                      
        </div>

    </div>

    <!-- row cotizador 4 -->
    <div *ngIf="showFour" class="row">
        
        <div class="col-1 mt-2">
            <h5>
                <span class="four">4</span>
            </h5>            
        </div>

        <mat-form-field appearance="outline" class="col-6 mb-2">
        <mat-label>  
            Top #4
        </mat-label>
        <input
            (focusout)="emitModel()"
            matInput
            placeholder="Top #4"
            formControlName="vQuote4"
            required
            type="text"
            PreventionSqlInjector
        />
        <mat-error
            *ngIf="
            _utilsService.isControlHasError(form, 'vQuote4', 'required')
            "
        >
            {{ 'ThisFieldIsRequired' | translate }}
        </mat-error>
        </mat-form-field>

        <div *ngIf="!showFive" class="col-2 mt-2">
            <div class="row">
                <h4 class="col-6">
                    <a (click)="removeRow(4)"><mat-icon iconPositionEnd fontIcon="delete"></mat-icon></a>
                </h4>
                <h4 class="col-6">
                    <a (click)="addRow(4)"><mat-icon iconPositionEnd fontIcon="add"></mat-icon></a>
                </h4> 
            </div>
                       
        </div>

    </div>

    <!-- row cotizador 5 -->
    <div *ngIf="showFive" class="row">

        <div class="col-1 mt-2">
            <h5>
                <span class="five">5</span>
            </h5>            
        </div>

        <mat-form-field appearance="outline" class="col-6 mb-2">
        <mat-label>  
            Top #5
        </mat-label>
        <input
            (focusout)="emitModel()"
            matInput
            placeholder="Top #5"
            formControlName="vQuote5"
            required
            type="text"
            PreventionSqlInjector
        />
        <mat-error
            *ngIf="
            _utilsService.isControlHasError(form, 'vQuote5', 'required')
            "
        >
            {{ 'ThisFieldIsRequired' | translate }}
        </mat-error>
        </mat-form-field>

        <div class="col-1 mt-2">
            <h4>
                <a (click)="removeRow(5)"><mat-icon iconPositionEnd fontIcon="delete"></mat-icon></a>
            </h4>            
        </div>

    </div>

</form>

