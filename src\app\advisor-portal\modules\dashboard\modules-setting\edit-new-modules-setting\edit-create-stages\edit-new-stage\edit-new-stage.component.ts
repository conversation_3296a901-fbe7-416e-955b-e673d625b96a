import { Component, ElementRef, EventEmitter, Input,  OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MessageService } from 'src/app/shared/services/message/message.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { RoleService } from 'src/app/shared/services/role/role.service';
import { catchError, of } from 'rxjs';
import { ResponseGlobalModel } from 'src/app/shared/models/response/response-global.mdel';
import { BodyTableModel } from 'src/app/shared/models/table/body-table.model';
import { IconEventClickModel } from 'src/app/shared/models/table/icon-event-click.model';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { Modal2Component } from 'src/app/shared/components/modal2/modal2.component';
import { MatDialog,MatDialogRef } from '@angular/material/dialog';
import { ChildrenStageModel, StageByStateModel, StageModel } from 'src/app/shared/models/module';
import { MatCardModule } from '@angular/material/card';
import { ModuleService } from 'src/app/shared/services/module/module.service';
import { EditNewStageByStateComponent } from './edit-new-stage-by-state/edit-new-stage-by-state.component';
import { CompanyCountryHistoryComponent } from 'src/app/shared/components/company-country-history/company-country-history.component';
import { FormsConfigurationHistoryComponent } from 'src/app/shared/components/forms-configuration-history/forms-configuration-history.component';
import { PreventionSqlInjectorDirective } from 'src/app/shared/directives/shared/prevention-sql-injector.directive';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-edit-new-stage',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,    
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    TranslateModule,
    MatSlideToggleModule,
    TableComponent,
    Modal2Component,
    MatCardModule,
    EditNewStageByStateComponent,
    CompanyCountryHistoryComponent,
    FormsConfigurationHistoryComponent,
    PreventionSqlInjectorDirective,
    MatTooltipModule
  ],
  templateUrl: './edit-new-stage.component.html',
  styleUrls: ['./edit-new-stage.component.scss']
})
export class EditNewStageComponent implements OnInit{

  //id menu y producto de entrada según correspondan
  @Input() idProductModuleIn: number = 0;
  //idStage en caso de se este editando una etapa
  @Input() idStageIn: number = 0;
  //variable en la que llega el modulo, subModulo, producto y propceso para miga de pan
  @Input() breadCrumHistoryIn: any = {};
  //resultado del formulario final
  @Output() resultOut = new EventEmitter<number>();
  //modal de crear esatdo
  @ViewChild('editNewStatusModal') editNewStatusModal?: TemplateRef<any>;
  currentModal: MatDialogRef<any> | null = null;

  arrayCompanyHistoryOut: any[] = [];

  //id de la etapa que se guarda
  idCreatedStage: number = 0;
  //boleano true en caso de que ya se halla crado la etapa.
  stageAlreadyCreated: boolean = false;
  //modelo de satge en caso de que se valla a actializar
  stageModel? : StageModel;

  //idStageByState en caso de que se valla a actualizar
  idStageByState: number = 0
  //titulo del modal de crear estado, nuevo/editar según el caso
  tittleModalText: string = '';
  //idEtapa que se envia al modal de estado
  idStageOut: number = 0;
  //valor del estado que se envía al modal para el caso de editar
  stageByStateModelOut?: StageByStateModel;
  //formulario del modal de estado es valido?
  stageByStateFormValid: boolean = false;
  //variable que guarda el valor del formulario del modal de estados
  stageByStateModel?: StageByStateModel;
 
  
  //lista etapas "padre" que se muestra en el html
  stageList: StageModel[] = [];
  //lista de dependencias en caso de estár activa
  parentStageList: ChildrenStageModel[] = [];;

  //valiables que validad si es una etapa dependiente o si tiene multiples estados. 
  isDependentStage: boolean = false;
  isMultipleStatus: boolean = false;
  allStatusCreated: boolean = false;
  multipleStatatesBlocked: boolean = true;

  //estructura de la tabla de estados
  estructTableStageByState: BodyTableModel[] = [
    {
      columnLabel: this._translateService.instant(
        'Stage.GeneralStatusCategory'
      ),
      columnValue: 'vCategory'
    },
    {
      columnLabel: this._translateService.instant(
        'Stage.AssignedStatus'
      ),
      columnValue: 'vState',
    },
    {
      columnLabel: this._translateService.instant(
        'Stage.IdentificationColor'
      ),
      columnValue: 'vColors',
    },
    {
      columnLabel: this._translateService.instant(
        'Stage.Visibility'
      ),
      columnValue: 'rolNames',
    },
    {
      columnLabel: this._translateService.instant(
        'Modify'
      ),
      columnValue: 'modify',
      columnIcon: 'edit',
    },
    {
      columnLabel: this._translateService.instant(
        'Delete'
      ),
      columnValue: 'delete',
      columnIcon: 'delete',
    },
  ]
  dataTableStageByState: StageByStateModel[] = [];

  //variables de formulario 
  form: FormGroup = new FormGroup({});
  dependenciesForm: FormGroup = new FormGroup({});

  constructor(
    private fb: FormBuilder,
    private _messageService: MessageService,    
    private  _translateService: TranslateService,
    private _roleService : RoleService,
    private _moduleService: ModuleService,
    public _utilsService: UtilsService,
    public _editNewStatusDialog: MatDialog
  ){}

  ngOnInit(): void {
    this.arrayCompanyHistoryOut.push(
      {
        title:this._translateService.instant('FormsConfigurationHistory.Module'),
        subtitle:this.breadCrumHistoryIn.module
      }
    );
    this.breadCrumHistoryIn.subModule?this.arrayCompanyHistoryOut.push(
      {
        title:this._translateService.instant('FormsConfigurationHistory.Submodule'),
        subtitle:this.breadCrumHistoryIn.subModule
      }
    ):''

    this.breadCrumHistoryIn.product?this.arrayCompanyHistoryOut.push(
      {
        title:this._translateService.instant('FormsConfigurationHistory.Product'),
        subtitle:this.breadCrumHistoryIn.product
      }
    ):''
    this.breadCrumHistoryIn.process?this.arrayCompanyHistoryOut.push(
      {
        title:this._translateService.instant('FormsConfigurationHistory.Process'),
        subtitle:this.breadCrumHistoryIn.process
      }
    ):''

    //this.arrayCompanyHistoryOut.push(arrayOut)
    this.initForm();
    this.getStagesByIdProductModule(this.idProductModuleIn);

    if(this.idStageIn!=0){
      this.stageAlreadyCreated = true;
      this.multipleStatatesBlocked = false;
      this.getStageById(this.idStageIn)
      this.getStageByStateList(this.idStageIn)
    }
  }

  initForm(){
    this.form = this.fb.group({
      pkIIdStage: [0],
      vNameStage: [null, [Validators.required]],
      dDateCreate:[new Date()],
      bActive: [true],
      bHasChildren:[false],
      bMultipleStates:[false],
      fkIdProductModule: this.idProductModuleIn
    });
  }

  initDependenciesForm(){
    this.dependenciesForm = this.fb.group({
      pkIIdChildrenStage: [0],
      fkIIdStageParent: [0],
      fkIIdStageChild: [this.idStageIn!=0?this.idStageIn : this.idCreatedStage],
      bActive: [true]
    });
  }

  controller(evt: IconEventClickModel) {
    if(evt.column == 'delete')
    {
      this._messageService
        .messageConfirmationAndNegation(
          '¿'+this._translateService.instant('Delete') + '?',
          '',
          'warning',
          this._translateService.instant('Confirm'),
          this._translateService.instant('Cancel')
        )
        .then((result) => {
          if (result) {
            this.deleteStageByState(evt.value.pkIIdStageByState)
            
          }
        });
    }
    else
    {
      this.openEditNewStatusDialog(evt.value, evt.value.pkIIdStageByState);
    }
  }  

  openEditNewStatusDialog(statusModel?: StageByStateModel, idStageByState: number = 0) {
    this.idStageByState = idStageByState;  
    this.idStageByState===0?
      this.tittleModalText = this._translateService.instant('Stage.NewStatus')
      :this.tittleModalText = this._translateService.instant('Stage.ModifyStatus')    
    this.stageByStateFormValid = false;
    this.idStageOut = this.idStageIn!=0? this.idStageIn : this.idCreatedStage;
    this.stageByStateModelOut = statusModel;
    const dialogRef = this._editNewStatusDialog.open(
      this.editNewStatusModal!,
      {
        disableClose: false,
        width: '50vw',
        maxHeight: '90vh'
      }
    );
    this.currentModal=dialogRef;
  }

  getStageByStateFormResult(event: StageByStateModel){
    if(event)
    {
      this.stageByStateFormValid = true;
      this.stageByStateModel = event; 
      this.idStageByState = event.pkIIdStageByState;
      if(event.pkIIdStageByState!=0){
        this.getStageByStateList(event.fkIIdStage)
        if(this.tittleModalText != this._translateService.instant('Stage.ModifyStatus')){
          this.closeModal()
        }
      }
    }    
  }

  dependentStageChange(ischecked : boolean){ 
    if(ischecked)
    {
      this.isDependentStage = ischecked;
      this.initDependenciesForm()
    }else{
      for(let item of this.parentStageList){
        this.deleteChildrenStage(item.pkIIdChildrenStage);
      }
    }
  }

  multipleStatusChange(ischecked: boolean){
    this.isMultipleStatus = ischecked;
    this.dataTableStageByState.length > 2?this.allStatusCreated = !ischecked: ''
  }

  selectDependenciesChange(idParentStage:number, checked:boolean){    
    let idChildrenStage: number=0;
    checked?(
      this.parentStageList.length==0?(
        this.createChildrenStages(idParentStage)
      ):(
        this.parentStageList.filter(p => p.fkIIdStageParent == idParentStage).length == 0 ? this.createChildrenStages(idParentStage):''
      )
    ):(
      idChildrenStage = this.parentStageList.filter(p => p.fkIIdStageParent == idParentStage)[0].pkIIdChildrenStage,
      this.deleteChildrenStage(idChildrenStage)
    )  
  }

  saveStageButtonClick(){
    this.form.valid ? (
        this.idStageIn==0?this.createStage():this.updateStage()
      ): this._messageService.messageInfo(
      this._translateService.instant('InvalidForm'),
      this._translateService.instant('PleaseCompleteAllTheInformationOnTheForm'),
    );
  }  

  //#region metodos que consumen las apis
    getStageById(idStage: number)
    {
      this._moduleService
        .getStageById(idStage)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('TheregetStageByIdWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
            
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.form.patchValue(resp.result);
              this.isMultipleStatus=this.form.value.bMultipleStates
              this.getChildrenStageById(idStage);
              this.arrayCompanyHistoryOut.push(
                {
                  title:this._translateService.instant('FormsConfigurationHistory.Stage'),
                  subtitle:this.form.value.vNameStage
                }
              )
            }
          }
        });
    }

    getStagesByIdProductModule(idProductModule: number){
      this._moduleService.getStageByIdProductModule(idProductModule)
        .pipe(
          catchError((error) => {
            console.log('No existen etapas asociadas');
            this.stageList = [];
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
            this.stageList = [];
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.stageList = resp.result.filter((r: any) => r.pkIIdStage!=(this.idStageIn!=0? this.idStageIn:this.idCreatedStage))
            }
          }
        });
    }

    //obtine la lista de etapas padre asignados a un idEtapa hijo
    getChildrenStageById(idStage: number) { 
      this._moduleService
        .getChildrenStageById(idStage)
        .pipe(          
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                'No existen dependencias para la etapa seleccionada',
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('No existen dependencias para la etapa seleccionada');
            this.isDependentStage = false;
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
              this.isDependentStage = false;
            } else {
              this.initDependenciesForm();
              this.isDependentStage = true
              this.parentStageList = resp.result;
              let selectedStages: number[] = []
              for(let item of resp.result) 
              {
                selectedStages.push(item.fkIIdStageParent)
              }              
              this.dependenciesForm.patchValue({fkIIdStageParent: selectedStages});              
            }
          }
        });
    }

    getStageByStateList(idStage: number) {
      this._moduleService
        .getStageByStateById(idStage)
        .pipe(
          catchError((error) => {
            console.log('No existen estados asociados a esta etapa.');
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
            this.dataTableStageByState = [];
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {      
              for(let item of resp.result){
                if(resp.result.filter((r:any) => r.vCategory == item.vCategory).length>1)
                {
                  this.multipleStatatesBlocked = true;
                  this.allStatusCreated = false;
                }
                else{
                  this.multipleStatatesBlocked = false;
                  if(resp.result.length > 2 && !this.isMultipleStatus){
                    this.allStatusCreated = true;
                    

                  }else
                  {
                    this.allStatusCreated = false;
                  }
                }
                item.rolNames = '';
                if(item.listRoleBusiness.length > 0){
                  for(let item2 of item.listRoleBusiness)
                  {
                    this._roleService
                      .getByRoleId(item2.fkIIdRoleBusiness) 
                      .pipe(
                        catchError((error) => {
                          if (error.error.error) {
                            this._messageService.messageWaring(
                              this._translateService.instant('ThereWasAError'),
                              error.error.message
                            );
                          }
                          return of([]);
                        })
                      )
                      .subscribe((resp2: ResponseGlobalModel | never[]) => {
                        if (resp.error){}
                        else{
                          if (Array.isArray(resp2)) {
                            console.log('El tipo de datos devueltos es un array vacío');
                          } else {
                              item.rolNames != ''? item.rolNames = item.rolNames + ", " + resp2.result.v_RoleName:item.rolNames = resp2.result.v_RoleName
                          }
                        }                        
                      });
                  }
                }else
                {
                  item.rolNames = '';
                }
              }
              this.dataTableStageByState = resp.result;
            }
          }
        });
    }
    
    createStage(){
      let model: StageModel = this.form.value;
      this.form.patchValue({bMultipleStates: this.isMultipleStatus});   
      this._moduleService
        .createStage(model)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
            this.idCreatedStage = 0;
            this.stageAlreadyCreated = false;
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.idCreatedStage = resp.result;
              this.stageAlreadyCreated = true;
              this.multipleStatatesBlocked = false;
              this.arrayCompanyHistoryOut.push(
                {
                  title:this._translateService.instant('FormsConfigurationHistory.Stage'),
                  subtitle:this.form.value.vNameStage
                }
              )
              this.resultOut.emit(this.idProductModuleIn);
              this._messageService.messageSuccess(
                this._translateService.instant('Saved'),
                resp.message
              );            
            }
          }
        });
    }
    
    createChildrenStages(idParentStage: number){
      let model: ChildrenStageModel = this.dependenciesForm.value;
      model.fkIIdStageParent = idParentStage;
      this._moduleService
        .createChildrenStages(model)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
            
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              //el proceso fue correcto pero no se muestra ninguna alerta por solicitud
            }
          }
        });
    }       

    updateStage(){
      this.form.patchValue({bMultipleStates: this.isMultipleStatus});
      let model: StageModel = this.form.value;
      this._moduleService
        .updateStage(model)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');            
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this.resultOut.emit(this.idProductModuleIn);
              this._messageService.messageSuccess(
                this._translateService.instant('Saved'),
                resp.message
              );            
            }
          }
        });
    }

    updateStageByState(){
      this._moduleService
        .updateStageByState(this.stageByStateModel!)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');            
          } else {
            if (resp.error) {
              this._messageService.messageError(
                resp.result
              );
            } else {    
              this.getStageByStateList(this.idStageIn!=0? this.idStageIn : this.idCreatedStage);
              this._messageService.messageSuccess(
                this._translateService.instant('Saved'),
                resp.message
              );    
            }
          }
        });
    }

    deleteChildrenStage(idChildrenStage: number)
    {
      this._moduleService
        .deleteChildrenStage(idChildrenStage)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.message
            );
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              //el proceso fue correcto pero no se muestra ninguna alerta por solicitud
            }
          }
        });
    }
    
    deleteStageByState(idStageByState: number)
    {
      this._moduleService
        .deleteStageByState(idStageByState)
        .pipe(
          catchError((error) => {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              this._translateService.instant('Stage.DependentStatusAlert'),
            );
            return of([]);
          })  
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            console.log('El tipo de datos devueltos es un array vacío.');
          } else {
            if (resp.error) {
              this._messageService.messageError(
                this._translateService.instant('ThereWasAError') + resp.message
              );
            } else {
              this._messageService.messageConfirmatio(
                this._translateService.instant('DeleteMessage'),
                '',
                'success',
                this._translateService.instant('Continue')
              );
              this.getStageByStateList(this.idStageIn!=0? this.idStageIn:this.idCreatedStage);
            }
          }
        });
    }
  //#endregion

  closeModal() {
    if (this.currentModal) {
      this.currentModal.close();
      this.currentModal = null; // Limpia la referencia después de cerrar el modal
    }
  }
  
}




