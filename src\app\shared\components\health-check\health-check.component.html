<div class="health-check-container">
  <div id="ssr-health-check" data-ssr-rendered="true">
      <h1>Application Health Status: <span class="health-status">{{ healthStatus }}</span></h1> 
       
      <div id="ssr-rendering-info"> 
          <p>Initial Render: <strong>{{ initialRenderPlatform }}</strong></p>
          <p>Current Render: <strong>{{ renderPlatform }}</strong></p>
          <p>SSR Working: <strong>{{ wasSSRRendered ? 'Yes' : 'No' }}</strong></p>
          <p>Timestamp: {{ timestamp }}</p>
          <p>Route: /healthcheck</p>
          <p *ngIf="isBrowser && hostname">Host: {{ hostname }}</p>

      </div>
      
      <div id="health-details">
          <h3>System Health Checks:</h3>
          <p>Server: <span class="status-indicator" [class.healthy]="healthData.server" [class.unhealthy]="!healthData.server">
              {{ healthData.server ? 'Healthy' : 'Unhealthy' }}</span>
          </p>
          <p>Server-Side Rendering: <span class="status-indicator" [class.healthy]="wasSSRRendered" [class.unhealthy]="!wasSSRRendered">
              {{ wasSSRRendered ? 'Working' : 'Not Working' }}</span>
          </p>
      </div>
  </div>
</div>
