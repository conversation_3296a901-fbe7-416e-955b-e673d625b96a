<div class="mt-8">
    <h3 class="mb-0">
        {{ "Reports.ReportsGenerated.Title" | translate }}
    </h3>
    <form [formGroup]="form" class="mt-2">
        <mat-form-field appearance="outline" class="col-4 p-1" *ngIf="listReportBd.length > 0">
            <mat-label>
                {{ "Reports.ReportsGenerated.SelectReport" | translate }}
            </mat-label>
            <mat-select (selectionChange)="onReportChange($event.value)" [required]="true" formControlName="fkIIdReport">
                <mat-option *ngFor="let item of listReportBd" [value]="item.pkGIdReport">
                    {{item.fkIIdInsuranceCompany ? ("Reports.Policy" | translate) : ("Reports.Module" | translate)}}_{{ item.vReportName }}
                </mat-option>
            </mat-select>
            <mat-error *ngIf="utilsService.isControlHasError(form, 'fkIIdReport', 'required')">
                {{ "ThisFieldIsRequired" | translate }}
            </mat-error>
        </mat-form-field>
    </form>
    <form [formGroup]="formFilters">
        <ng-container *ngIf="formFilters">
            <div class="row">
                <div class="col-4 p-1" *ngFor="let field of listAllFilters">
                    <app-field-generator [isQuote]="field.pkIIdField > 0" [field]="field" [defaultCatalog]="field.vDefaultCatalog" *ngIf="field.bIsLoaded != false"></app-field-generator>
                </div>
            </div>
        </ng-container>

        <!-- check filtro goups/rol/users -->
        <ng-container *ngIf="responseFilters.bIsGroupFilterActive || responseFilters.bIsRolFilterActive || responseFilters.bIsUserFilterActive">
            <form [formGroup]="formAdditionalFilters">
                <div class="row d-flex justify-content-space-around">
                    <div class="col-4 p-1" *ngIf="responseFilters.bIsGroupFilterActive">
                        <mat-form-field appearance="outline" class="w-100">
                        <mat-label>
                            {{ 'MyQuotation.AllQuotation.Group' | translate }}
                        </mat-label>
                        <mat-select formControlName="listGroupBussines" multiple>
                            <mat-option *ngFor="let item of groupTable" [value]="item.pkIIdGroup">
                            {{ item.vGroupName }}
                            </mat-option>
                        </mat-select>
                        </mat-form-field>
                    </div>

                    <div class="col-4 p-1" *ngIf="responseFilters.bIsRolFilterActive">
                        <mat-form-field appearance="outline" class="w-100">
                        <mat-label>
                            {{ 'Rol' | translate }}
                        </mat-label>
                        <mat-select formControlName="listRoleBusiness" multiple>
                            <mat-option *ngFor="let item of roleList" [value]="item.pkIIdRole">
                            {{ item.vRoleName }}
                            </mat-option>
                        </mat-select>
                        </mat-form-field>
                    </div>

                    <div class="col-4 p-1" *ngIf="responseFilters.bIsUserFilterActive">
                        <mat-form-field appearance="outline" class="w-100">
                        <mat-label>
                            {{ 'Usuarios' | translate }}
                        </mat-label>
                        <mat-select formControlName="listUserBussiens" multiple>
                            <mat-option *ngFor="let item of tempUsersList" [value]="item.pkIIdUser">
                            {{ item.vPersonName }}
                            </mat-option>
                        </mat-select>
                        </mat-form-field>
                    </div>
                </div>
            </form>
        </ng-container>
        <div class="row">
            <div class="col-md-12">
              <app-table [displayedColumns]="estructReportTable" [data]="dataReportTable"></app-table>
            </div>
          </div>

        <!-- Botones administrar reporte -->
        <div class="cont-btns">
            <div class="cont-btn-left mt-3">
                <button (click)="generateReport('')" [disabled]="formFilters.invalid" mat-raised-button [color]="colorBtnGenerate">
                    {{ "Generar Reporte" | translate }}  <mat-icon iconPositionEnd class="icon">dataset</mat-icon>
                </button>
                <button (click)="generateReport('CVS')" [disabled]="formFilters.invalid" mat-raised-button [color]="colorBtnGenerate" class="mx-2">
                    {{ "Reports.ReportsGenerated.GenerateCVS" | translate }} <mat-icon iconPositionEnd class="icon">chevron_right</mat-icon>
                </button>
                <button (click)="generateReport('XLSX')" [disabled]="formFilters.invalid" mat-raised-button [color]="colorBtnGenerate">
                    {{ "Reports.ReportsGenerated.GenerateXLSX" | translate }}  <mat-icon iconPositionEnd class="icon">chevron_right</mat-icon>
                </button>
            </div>
            <div class="cont-btn-right mt-3">
                <button (click)="onClickEditReport()" [disabled]="disabledBtnCrud" mat-raised-button [color]="colorEdit" class="mx-2">
                    {{ "Reports.ReportsGenerated.Edit" | translate }} <mat-icon iconPositionEnd class="">edit</mat-icon>
                </button>
                <button (click)="onClickDeleteReport()" [disabled]="disabledBtnCrud" mat-raised-button color="warn">
                    {{ "Reports.ReportsGenerated.DeleteReport" | translate }} <mat-icon iconPositionEnd>delete</mat-icon>
                </button>
            </div>
        </div>

    </form>
</div>