import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PageSeeDetailsIndividualPolicyComponent } from './page-see-details-individual-policy.component';

describe('PageSeeDetailsIndividualPolicyComponent', () => {
  let component: PageSeeDetailsIndividualPolicyComponent;
  let fixture: ComponentFixture<PageSeeDetailsIndividualPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ PageSeeDetailsIndividualPolicyComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PageSeeDetailsIndividualPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
