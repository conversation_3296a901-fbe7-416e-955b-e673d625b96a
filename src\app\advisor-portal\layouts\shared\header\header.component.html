<header [ngClass]="{'header-expanded': isNavbarExpanded}">
  <div class="language">
    <div class="dropdown">
      <!-- TRADUCCION -->
      <div class="row">
        <div class="col-3">
          <span>
            <img src="assets/img/layouts/world.svg" alt="" />
          </span>
        </div>
        <div class="col-8 click">
          <select
            id="select"
            class="form-control shadow-none"
            style="border-color: white"
            #selectedLang
            (change)="switchLang(selectedLang.value)"
          >
            <option
              *ngFor="let language of _translateService.getLangs()"
              [value]="language"
              [selected]="language === _translateService.currentLang"
            >
              {{ language.toUpperCase() }}
            </option>
          </select>
        </div>
      </div>
    </div>
  </div>

  <div class="user">
    <div class="country h7">{{ country }}</div>
    <div class="logo">
      <img [src]="imageSrc" class="logo" alt="Logo Empresa" />
    </div>
    <a
      id="list"
      class="profile dropdown-toggle click"
      data-bs-toggle="dropdown"
      aria-expanded="false"
    >
      <span class="ico">
        <img src="assets/img/layouts/perfil.svg" alt="Perfil" />
      </span>
      <ul class="dropdown-menu">
        <li>
          <a class="dropdown-item" (click)="goToProfile()">{{
            "Header.MyProfile" | translate
          }}</a>
          <a class="dropdown-item" (click)="changeCompany()">{{
            "Header.ChangeBusiness" | translate
          }}</a>
          <a class="dropdown-item" (click)="logout()">{{
            "Header.LogOut" | translate
          }}</a>
        </li>
      </ul>
      <div class="hover">
        <p class="p_small_sb">{{ profile }}</p>
        <p class="h7">{{ email }}</p>
      </div>
    </a>
  </div>
</header>
