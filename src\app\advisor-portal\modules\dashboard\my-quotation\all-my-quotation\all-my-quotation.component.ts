//Libraries
import { Component, OnInit, ViewChild, TemplateRef, inject } from '@angular/core';
import { MatNativeDateModule } from '@angular/material/core';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { PageEvent } from '@angular/material/paginator';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/store/app.reducers';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { Subscription, of, catchError, filter } from 'rxjs';
import { MatSelectModule } from '@angular/material/select';
import { MatDialog } from '@angular/material/dialog';
import {
  MatDatepickerInputEvent,
  MatDatepickerModule,
} from '@angular/material/datepicker';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule,FormArray, Validators, FormControl  } from '@angular/forms';
import { ActivatedRoute} from '@angular/router';

//Services
import { TransactionService } from 'src/app/shared/services/transaction/transaction.service';
import { UserService } from 'src/app/shared/services/user/user.service';
import { UtilsService } from 'src/app/shared/services/utils/utils.service';
import { MessageService } from 'src/app/shared/services/message/message.service';

//Models and Components
import {
  changeEdit,
  changeStageQuote,
  saveIdQuote,
} from 'src/app/store/actions/quote.actions';
import {
  BodyTableModel,
  IconEventClickModel,
} from 'src/app/shared/models/table';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import {
  MyQuotationModel,
  QuoteFilter,
  FiltersAvailable,
  QuoteStage,
} from 'src/app/shared/models/my-quotation/myQuotation.model';
import { ResponseGlobalModel } from 'src/app/shared/models/response';
import { ModalComponent } from 'src/app/shared/components/modal/modal.component';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import { SpinnerService } from 'src/app/shared/services/spinner/spinner.service';
import { WorkFlowService } from 'src/app/shared/services/work-flow/work-flow.service';
import { ProductService } from 'src/app/shared/services/product/product.service';
import { TaskTrayConfigProductApiProductModel } from 'src/app/shared/models/task-tray-config';
import { QuotationConfigListModel } from 'src/app/shared/models/work-flow/quotation-config-list.model';
import { DynamicFieldsModel } from 'src/app/shared/models/task';
import { LocalStorageService } from 'src/app/shared/services/local-storage/local-storage.service';

@Component({
  selector: 'app-all-my-quotation',
  standalone: true,
  templateUrl: './all-my-quotation.component.html',
  styleUrls: ['./all-my-quotation.component.scss'],
  imports: [
    ModalComponent,
    MatNativeDateModule,
    MatCheckboxModule,
    MatDatepickerModule,
    CommonModule,
    MatSelectModule,
    TableComponent,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatRadioModule,
    TranslateModule,
  ],
})
export class AllMyQuotationComponent implements OnInit {
  //--Variables--/
  myQuotationTable: MyQuotationModel[] = [];
  amountRows: number = 0;
  pageIndex: number = 0;
  pageSize: number = 100;
  currentPosition: number = 0;
  filtersByUser: FiltersAvailable = {};
  currentSortOrder: string = 'asc';
  transaction?: Subscription;

  @ViewChild('filterModal') filterModal?: TemplateRef<any>;

  formFilters: FormGroup = this._fb.group({
    idProduct: [null],
    startDate: [null],
    endDate: [null],
    active: [null],
    idStatus: [null],
    idUser: [null],
    businessCountry: [null],
    idUsers: this._fb.array([]),
    idGroups: [[]],
    keyword: [null],
    fkIdProduct: [null, [Validators.required]],
  });

  estructMyQuotationTable: BodyTableModel[] = [];

  isPortalClient: boolean = false;

  productsApiProduct: TaskTrayConfigProductApiProductModel[] = [];
  filterList: QuotationConfigListModel[] = [];
  isDisabled: boolean = true;
  dynamicFields: DynamicFieldsModel[] = [];
  valorProductoTemp: any = 0;

  private localStorageService = inject(LocalStorageService);

  constructor(
    private _transactionService: TransactionService,
    private _store: Store<AppState>,
    private _utilsSvc: UtilsService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    public modalDialog: MatDialog,
    private _fb: FormBuilder,
    private _settingService: SettingService,
    private _customRouter: CustomRouterService,
    private _activatedRoute: ActivatedRoute,
    private _spinnerService: SpinnerService,
    private _userService: UserService,
    private _workFlowService: WorkFlowService,
    private _productService: ProductService,
  ) { }

  async ngOnInit(): Promise<void> {
    await this.getIdUserSession();

    // esto de aqui luego se ordena en algun metodo para lo nuevo de campos dinamicos
    this.formFilters.get('fkIdProduct')?.valueChanges.subscribe({
      next: (fkIdProduct) => {
        if (fkIdProduct > 0) {
          this.getQuotationConfigList(
            fkIdProduct
          );
        }
      },
    });

  }

  // genera toda la estructura y data de la tabla
  generateSearch() {

    this.getDataDynamicFields();

    this.getMyQuotationByIdUser(
      this.formFilters.value,
      this.pageIndex,
      this.pageSize
    );
  }

  async getIdUserSession() {
    let userIdSession: number = 0;
    if (await this._settingService.getDataSettingInit()) {
      const data = await this._settingService.getDataSettingInit();
      userIdSession = data.idUser;
      this.formFilters.patchValue({
        idUser: userIdSession,
      });
      this.getBussinessFromUser();
    } else if (this._customRouter.getLoggedInClienPortal()){
      this.isPortalClient = true;
      this.formFilters.patchValue({
        idUser: await this._userService.getUserIdSesion()
      });
      this.getBussinessFromUser();
    }
  }

  async getBussinessFromUser() {
    if (
      (await this._settingService.getDataSettingInit()) ||
      this.isPortalClient
    ) {
      // Obtener los datos de configuración del localStorage
      const dataSetting = await this._settingService.getDataSettingInit();
      const pkIIdBusinessByCountry =
        dataSetting?.idBusinessByCountry ||
        this._customRouter.getIdbusinessByCountry();
      this.formFilters.patchValue({
        businessCountry: pkIIdBusinessByCountry,
      });
      
      await this.getMyQuotationFilters();

      const selectedProduct = this.localStorageService.getItem('selectedProduct');

      // Espera a que se complete la obtención de productos
      await this.getAllProductByBusiness(pkIIdBusinessByCountry); // Espera a que se complete

      if (selectedProduct) {
        // Verifica si el producto seleccionado existe en la lista de productos
        const productExists = this.productsApiProduct.some(product => product.pkIIdProduct === Number(selectedProduct));
        if (productExists) {
          this.formFilters.get('fkIdProduct')?.setValue(Number(selectedProduct));
        } else {
          console.warn('El producto seleccionado no existe en la lista de productos.');
        }
      }
      
      // Esto se ejecutará después de verificar el producto
      this.localStorageService.removeItem('selectedProduct');
    }
  }

  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.currentPosition = this.pageIndex * this.pageSize;

    // Asegúrate de que currentPosition no sea negativo
    if (this.currentPosition < 0) {
      this.currentPosition = 0;
    }

    this.getMyQuotationByIdUser(
      this.formFilters.value,
      this.currentPosition,
      this.pageSize,
      this.currentSortOrder
    );
  }

  // Método para el evento de clic en el botón de ordenar
  onSortClick() {
    // Cambiar el orden actual
    if (this.currentSortOrder === 'asc') {
      this.currentSortOrder = 'desc';
    } else {
      this.currentSortOrder = 'asc';
    }
    // Agrega lógica para ordenar ascendente o descendente aquí
    this.getMyQuotationByIdUser(
      this.formFilters.value,
      this.currentPosition,
      this.pageSize,
      this.currentSortOrder
    );
  }

  async getMyQuotationByIdUser(
    filter: QuoteFilter,
    from: number,
    pageSize: number,
    order?: string
  ) {
    
    filter.startDate = this.formatDate(this.formFilters.value.startDate);
    filter.endDate = this.formatDate(this.formFilters.value.endDate);
    if (this._customRouter.isClientPortal()) {

      if (filter.idUser != await this._userService.getUserIdSesion()) {
        this._customRouter.navigate(['dashboard/my-quotations/'+await this._userService.getUserIdSesion()])
        return;
      }else{
        filter.idUser = await this._userService.getUserIdSesion();
      }
    }


    filter.idProduct = this.formFilters.get('fkIdProduct')?.value;
    this.myQuotationTable = [];

    if (filter.idProduct == null){
      this._messageService.messageWaring(
        this._translateService.instant('Warning'),
        "Debe seleccionar un producto"
      );
      return;
    }

    this._spinnerService.showChanges(true);    

    // campos dinamicos
    let dynamicFieldsFilter = this.dynamicFields.filter(
      (obj) => obj.idValue !== null
    );

    let auxField: DynamicFieldsModel[] = [];

    dynamicFieldsFilter.forEach((element: DynamicFieldsModel) => {
      if (element.valueField) {
        auxField.push(element);
      } else {
        filter.jsonString = '';
      }
    });
    filter.jsonString = JSON.stringify(auxField);

    // ----------

    this.transaction = this._transactionService
      .getMyQuotes(filter, from, pageSize, order)
      .pipe(
        catchError((error) => {
          this._messageService.messageWaring(
            this._translateService.instant('Warning'),
            error.error.message
          );
          this._spinnerService.showChanges(false);
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        this._spinnerService.showChanges(false);

        if (Array.isArray(resp)) {
          console.log('El tipo de datos devueltos es un array vacío.');
        } else {
          if (resp.error) {
            this._messageService.messageError(
              this._translateService.instant('ThereWasAError') + resp.message
            );
          } else {

            // Transformación de datos antes de asignarlos a `myQuotationTable`
            const transformedData = this.flattenDynamicProperties(resp.result);
            
            this.myQuotationTable = transformedData.length > 0
                ? transformedData.sort((a, b) => b.pkIIdQuotes - a.pkIIdQuotes)
                : [];

            this.amountRows = resp.rowCount;

            // valido si existe marca key 
            const hasKeyFieldName = this.myQuotationTable.some(
              (quote) => quote.keyValue && quote.keyValue.trim() !== ''
            );

            // Si existe algún keyFieldName, mostramos la columna
            // this.definingTheTableStructure(hasKeyFieldName);
          }
        }
      });
  }

  async getMyQuotationFilters(): Promise<void> {
    this._spinnerService.showChanges(true);
    
    if (this._customRouter.isClientPortal()) {
        this.formFilters.patchValue({
            businessCountry: this._customRouter.getIdbusinessByCountry(),
        });
    }

    const userId = this._customRouter.isClientPortal()
        ? await this._userService.getUserIdSesion()
        : this.formFilters.get('idUser')?.value;

    // Retorna una nueva Promesa que se resolverá cuando termine la suscripción
    return new Promise((resolve) => {
        this.transaction = this._transactionService
            .getMyQuotesFilters(
                this.formFilters.get('businessCountry')?.value,
                userId
            )
            .pipe(
                catchError((error) => {
                    this._messageService.messageWaring(
                        this._translateService.instant('Warning'),
                        error.error.message
                    );
                    this._spinnerService.showChanges(false);
                    resolve(); // Resuelve la promesa en caso de error
                    return of([]); // Retorna un array vacío para manejar el flujo
                })
            )
            .subscribe((resp: ResponseGlobalModel | never[]) => {
                if (Array.isArray(resp)) {
                    console.log('El tipo de datos devueltos es un array vacío.');
                } else {
                    if (resp.error) {
                        this._messageService.messageError(
                            this._translateService.instant('ThereWasAError') + resp.message
                        );
                    } else {
                        this.filtersByUser = resp.result;
                    }
                }

                this._spinnerService.showChanges(false);
                resolve(); // Resuelve la promesa al finalizar la suscripción
            });
    });
  }


  downloadExcel() {
    this._transactionService
      .getQuoteByExcel(this.formFilters.value, 'asc')
      .subscribe(
        (response: any) => {
          const blob = new Blob([response], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'reporte.xlsx';
          link.click();
          window.URL.revokeObjectURL(url);
        },
        (error) => {
          console.error('Error al obtener el archivo Excel', error);
        }
      );
  }

  controller(event: IconEventClickModel) {
    
    this._activatedRoute.params.subscribe((p: any) => {
      if (p.idClient) {
        this.isPortalClient = true;
        this.formFilters.patchValue({
          idUser: p.idClient,
        });
      }
    });

    this.valorProductoTemp = this.formFilters.get('fkIdProduct')?.value;
    this.localStorageService.setItem('selectedProduct', this.valorProductoTemp);

    if (this.isPortalClient) {
      console.log("event --->", event);
      
      if (event.column == 'see') {
        switch (event.value.vStatus) {
        
          case 'Sin Emitir': {
            // this._customRouter.navigate([
            //   `dashboard/see-quotations/${event.value.fkIIdProduct}/1`,
            // ]);

            this._customRouter.navigate([
              `dashboard/quotation-management-tabs/${event.value.fkIIdProduct}/1`,
            ]);

            // this._customRouter.navigate([
            //   `dashboard/see-quotations/${event.value.fkIIdProduct}/1`,
            // ]);
            this._store.dispatch(changeEdit({ isEdit: false }));
  
            this._store.dispatch(
              saveIdQuote({ idQuote: event.value.pkIIdQuotes })
            );
            this._store.dispatch(
              changeStageQuote({ stage: QuoteStage.Emission })
            );
            break;
          }
          case 'Emitido': {
            // this._customRouter.navigate([
            //   `dashboard/see-quotations/${event.value.fkIIdProduct}/1`,
            // ]);
            this._customRouter.navigate([
              `dashboard/quotation-management-tabs/${event.value.fkIIdProduct}/1`,
            ]);

            this._store.dispatch(changeEdit({ isEdit: false }));
  
            this._store.dispatch(
              saveIdQuote({ idQuote: event.value.pkIIdQuotes })
            );
            this._store.dispatch(
              changeStageQuote({ stage: QuoteStage.Emission })
            );
            break;
          }
          case 'Emisión rechazada': {
            this._customRouter.navigate([
              `dashboard/quotation-management-tabs/${event.value.fkIIdProduct}/1`,
            ]);

            this._store.dispatch(changeEdit({ isEdit: false }));
  
            this._store.dispatch(
              saveIdQuote({ idQuote: event.value.pkIIdQuotes })
            );
            this._store.dispatch(
              changeStageQuote({ stage: QuoteStage.IssueFailed })
            );
            break;
          }
        }        
      }

      else if(event.column == 'edit'){

        if (event.value.vStatus === 'Emitido') {
          this._messageService.messageInfo('Atención', 'No puedes editar  una   cotización emitida');
          return;
        }
        switch (event.value.vStatus) {
          
          case 'Sin Emitir': {
            
            this._store.dispatch(changeEdit({ isEdit: true }));

            this._customRouter.navigate([
              `dashboard/edit-quotations-client/${event.value.fkIIdProduct}/1`,
            ]);

  
            this._store.dispatch(
              saveIdQuote({ idQuote: event.value.pkIIdQuotes })
            );
            this._store.dispatch(
              changeStageQuote({ stage: QuoteStage.Emission })
            );
            break;
          }
          case 'Emitido': {
            this._customRouter.navigate([
              `dashboard/edit-quotations-client/${event.value.fkIIdProduct}/1`,
            ]);
            this._store.dispatch(changeEdit({ isEdit: true }));
  
            this._store.dispatch(
              saveIdQuote({ idQuote: event.value.pkIIdQuotes })
            );
            this._store.dispatch(
              changeStageQuote({ stage: QuoteStage.Emission })
            );
            break;
          }
          case "Emitida":
            this._customRouter.navigate([
              `dashboard/quotation-management-tabs/${event.value.fkIIdProduct}/1`,
            ]);
            this._store.dispatch(changeEdit({ isEdit: true }));

            this._store.dispatch(
              saveIdQuote({ idQuote: event.value.pkIIdQuotes })
            );
            this._store.dispatch(
              changeStageQuote({ stage: QuoteStage.Pay })
            );
            break;
          case 'Emisión rechazada': {
            this._customRouter.navigate([
              `dashboard/edit-quotations-client/${event.value.fkIIdProduct}/1`,
            ]);
            this._store.dispatch(changeEdit({ isEdit: true }));
  
            this._store.dispatch(
              saveIdQuote({ idQuote: event.value.pkIIdQuotes })
            );
            this._store.dispatch(
              changeStageQuote({ stage: QuoteStage.IssueFailed })
            );
            break;
          }
        }  
      }
    } 
    else {
      switch (event.value.vStatus) {
        case 'Sin Emitir': {
          // this._customRouter.navigate([
          //   `dashboard/quotation/${event.value.fkIIdProduct}`,
          // ]);
          this._customRouter.navigate([
            `dashboard/quotation-management-tabs/${event.value.fkIIdProduct}`,
          ]);
          this._store.dispatch(changeEdit({ isEdit: false }));
          this._store.dispatch(changeStageQuote({ stage: QuoteStage.Emission }));
          this._store.dispatch(
            saveIdQuote({ idQuote: event.value.pkIIdQuotes })
          );
          break;
        }
        case 'Emitido': {
          // this._customRouter.navigate([
          //   `dashboard/quotation/${event.value.fkIIdProduct}`,
          // ]);
          this._customRouter.navigate([
            `dashboard/quotation-management-tabs/${event.value.fkIIdProduct}`,
          ]);
          
          this._store.dispatch(changeEdit({ isEdit: true }));

          this._store.dispatch(
            saveIdQuote({ idQuote: event.value.pkIIdQuotes })
          );
          this._store.dispatch(
            changeStageQuote({ stage: QuoteStage.Emission })
          );
          break;
        }
        case "Emitida":
          // Maybe is necessary this step, but in this step is mandatory wait for the answer 
          //of carrier.
          //For the moment this code is commented.
          this._customRouter.navigate([
            `dashboard/quotation-management-tabs/${event.value.fkIIdProduct}`,
          ]);
          this._store.dispatch(changeEdit({ isEdit: true }));

          this._store.dispatch(
            saveIdQuote({ idQuote: event.value.pkIIdQuotes })
          );
          this._store.dispatch(
            changeStageQuote({ stage: QuoteStage.Pay })
          );
          break;
        case "Emisión rechazada":
          // Maybe is necessary this step, but in this step is mandatory wait for the answer 
          //of carrier.
          //For the moment this code is commented.
          this._customRouter.navigate([
            `dashboard/quotation-management-tabs/${event.value.fkIIdProduct}`,
          ]);
          this._store.dispatch(changeEdit({ isEdit: true }));

          this._store.dispatch(
            saveIdQuote({ idQuote: event.value.pkIIdQuotes })
          );
          this._store.dispatch(
            changeStageQuote({ stage: QuoteStage.IssueFailed })
          );
          break;
      }
    }
  }

  // Controlador de eventos para formatear la fecha.
  onDateChange(event: MatDatepickerInputEvent<Date>, keyForm: string) {
    if (event.value) {
      // Formatear la fecha como YYYY-MM-DD
      const formattedDate = event.value.toISOString().split('T')[0];
      this.formFilters.patchValue({ keyForm: formattedDate });
    }
  }

  openModal() {
    for (let item of this.filterList) {
      this.formFilters.addControl(item.vFieldName, new FormControl(''));
    }
    this.modalDialog.open(this.filterModal!, {
      position: {
        right: '0', // Para que aparezca en la parte derecha
        top: '0', // Puedes ajustar la posición vertical si es necesario
      },
      width: '25vw', // Ancho del 25% de la ventana
      height: '100vh', // Altura completa
    });
  }

  toggleUserSelection(userId: number) {
    const index = this.idUsersFormArray.value.indexOf(userId);
    if (index === -1) {
      // El usuario no está en la lista, lo agregamos
      this.idUsersFormArray.push(this._fb.control(userId));
    } else {
      // El usuario ya está en la lista, lo quitamos
      this.idUsersFormArray.removeAt(index);
    }
  }

  isUserSelected(userId: number): boolean {
    const idUsersArray = this.formFilters.get('idUsers') as FormArray;
    return idUsersArray.value.includes(userId);
  }

  closeModal() {
    this.modalDialog.closeAll();
    // this.filterList = [];
  }

  ngOnDestroy(): void {
    this.transaction?.unsubscribe();
  }

  get idUsersFormArray(): FormArray {
    return this.formFilters.get('idUsers') as FormArray;
  }
  getSelectedUsers(): number[] {
    return this.idUsersFormArray.value;
  }

  resetSearch() {
    this.formFilters.reset();
    this.formFilters.patchValue({
      idUser: this.getIdUserSession(),
      businessCountry: this.getBussinessFromUser(),
    });
    this.generateSearch();
  }

  //Formatea el valor de la fecha, al formato YYYY-MM-DD
  formatDate(inputDate: string): string {
    if (!inputDate) {
      return '';
    }
    const date = new Date(inputDate);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  }

  // --------------  nueva actualizacion campos dinamicos

  async getAllProductByBusiness(fkIIdBusinessByCountry: number): Promise<void> {
    return new Promise((resolve) => {
      this._productService
        .getAllProductByBusiness(fkIIdBusinessByCountry)
        .pipe(
          catchError((error) => {
            if (error.error.error) {
              this._messageService.messageWaring(
                this._translateService.instant('ThereWasAError'),
                error.error.message
              );
            }
            return of([]);
          })
        )
        .subscribe((resp: ResponseGlobalModel | never[]) => {
          if (Array.isArray(resp)) {
            // Manejo de error
          } else {
            if (resp.result.length > 0) {
              this.productsApiProduct = resp.result.filter((product: { bActive: boolean; }) => product.bActive === true);
            }
          }
          resolve(); // Resuelve la promesa después de cargar los productos
        });
    });
  }
  
  //Obtiene la configuración por producto, para armar dinamicamente la tabla de cotizaciones.
  getQuotationConfigList(idProduct: number) {
    this.filterList = [];
    // this.dynamicFields = [];
    this.isDisabled = true;

    this._workFlowService
      .getQuotationConfigList(this.formFilters.get('businessCountry')?.value, idProduct, true)
      .pipe(
        catchError((error) => {
          if (error.error.error) {
            this._messageService.messageWaring(
              this._translateService.instant('ThereWasAError'),
              error.error.message
            );
          }
          return of([]);
        })
      )
      .subscribe((resp: ResponseGlobalModel | never[]) => {
        if (Array.isArray(resp)) {
        } else {
          this.generateTablestructureDynamically(resp.result);
          this.generateSearch();
          resp.result.forEach((element: QuotationConfigListModel) => {
            if (element.bIsFilter) {
              this.filterList.push(element);
            }
          });
          this.isDisabled = false;

        }
      });
  }

  //Función que construye dinamicamente, las columnas de la tabla en base a la configuración realizada por el usuario.
  generateTablestructureDynamically(data: QuotationConfigListModel[]) {

    this.estructMyQuotationTable = [];
    data.forEach((element: QuotationConfigListModel) => {
      let field: BodyTableModel = {
        columnLabel: element.vFieldName,
        columnValue: this.generateColumnValue(element),
      };

      if (this.estructMyQuotationTable.length < 16){
        if (element.vFieldAlias === 'bActive') {
          field.functionValue = (item: any) => this._utilsSvc.changeStatusValue(item)
        } else if (element.vFieldAlias === 'customerStatusValue') {
          field.functionValue = (item: any) => this._utilsSvc.changeStatusTaskValue(item)
        }
        this.estructMyQuotationTable.push(field);
      }

    });

    if (this.isPortalClient) {

      let seeColumn = {
        columnLabel: this._translateService.instant('TaskTray.ManagementHistory.Table.SeeDetail'),
        columnValue: 'see',
        columnIcon: 'find_in_page',
      };
      this.estructMyQuotationTable.push(seeColumn);

      let editColumn = {
        columnLabel: this._translateService.instant('Modify'),
        columnValue: 'edit',
        columnIcon: 'edit_square',
      };
      this.estructMyQuotationTable.push(editColumn);

    } else {
      let seeColumn = {
        columnLabel: this._translateService.instant('Action'),
        columnValue: 'see',
        columnIcon: 'search',
      };
      this.estructMyQuotationTable.push(seeColumn);
    }
   
  }

  //Función que genera el nombre de llave del valor que tomará el registro en la tabla.
  generateColumnValue(data: QuotationConfigListModel): string {
    let result: string = '';
    if (data.fkIIdField) {
      result = `${data.fkIIdField}_${data.vFieldName}`;
    } else {
      result = data.vFieldAlias;
    }
    return result;
  }

  // añadir campos dinamicos al objeto principal
  flattenDynamicProperties(data: any[]): any[] {
    return data.map(row => {
        // Crear una copia del objeto de fila
        const flatRow = { ...row };
        
        // Si existen `dynamicProperties`, añadimos cada uno a nivel superior
        if (row.dynamicProperties) {
            Object.entries(row.dynamicProperties).forEach(([key, value]) => {
                flatRow[key] = value;
            });
        }

        return flatRow;
    });
  }
  
  // cargar datos dinamicos para filtro
  getDataDynamicFields() {
    //Asignamos el valor del formulario en una variable temporal para poder procesar la información.
    let formValue = this.formFilters.value;

    //Guardamos los nombres y el id de los campos dinamicos.
    this.filterList.forEach((element: QuotationConfigListModel) => {
      let fieldDynamic: DynamicFieldsModel = {
        idValue: element.fkIIdField,
        vName: element.vFieldName,
        valueField: '',
      };
      this.dynamicFields.push(fieldDynamic);
    });

    //Verificamos si los campos dinamicos son estandares o adicionales.
    this.dynamicFields.forEach((element: DynamicFieldsModel) => {
      if (element.vName in formValue) {
        // element.valueField = this.formFilters.get(element.vName)?.value;
        element.valueField = formValue[element.vName];
      }
    });

  }




}
