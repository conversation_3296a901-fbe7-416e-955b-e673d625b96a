<div class="title">
  <h3 *ngIf="idCatalog == 0">
     {{"CatalogSetting.NewCatalog" | translate}}
  </h3>
  <h3 *ngIf="idCatalog != 0">
    {{"CatalogSetting.ModifyCatalog" | translate}}
 </h3>
</div>

<form [formGroup]="form">
  <div class="row mt-5">
    <div class="col-md-4 mb-2">
      <mat-form-field
        appearance="outline"
        class="select-look w-50 m-auto w-100"
      >
        <mat-label> {{ "CatalogSetting.CatalogName" | translate }} </mat-label>
        <input matInput formControlName="vName" />
        <mat-error
          *ngIf="_utilsSvc.isControlHasError(form, 'vName', 'required')"
        >
          {{ "ThisFieldIsRequired" | translate }}
        </mat-error>
      </mat-form-field>
    </div>
  </div>

  <div class="d-flex justify-content">
    <button
      *ngIf="this.form.get('pkIIdCatalog')?.value === 0"
      type="button"
      mat-raised-button
      color="primary"
      (click)="saveCatalog()"
      class="mx-1"
    >
      <mat-icon iconPositionEnd fontIcon="save"></mat-icon>
      {{ "CatalogSetting.SaveCatalog" | translate }}
    </button>
    <button
      *ngIf="this.form.get('pkIIdCatalog')?.value > 0"
      type="button"
      mat-raised-button
      color="primary"
      (click)="updateCatalog()"
      class="mx-1"
    >
      {{ "CatalogSetting.UpdateCatalog" | translate }}
      <mat-icon fontIcon="save"></mat-icon>
    </button>
    <button
      *ngIf="this.form.get('pkIIdCatalog')?.value > 0"
      type="button"
      mat-raised-button
      color="primary"
      (click)="deleteCatalog()"
      class="mx-1"
    >
      {{ "CatalogSetting.DeleteCatalog" | translate }}
      <mat-icon fontIcon="save"></mat-icon>
    </button>
  </div>
  <div class="mt-5">
    <app-table
      [displayedColumns]="estructTableField"
      [data]="fieldTable"
      (iconClick)="controller($event)"
    ></app-table>
  </div>

  <button
    class="mb-2"
    type="button"
    mat-raised-button
    color="primary"
    [disabled]="this.form.get('pkIIdCatalog')?.value == 0"
    (click)="addField()"
  >
    {{ "CatalogSetting.AddField" | translate }}
    <mat-icon iconPositionEnd fontIcon="add"></mat-icon>
  </button>

  <div class="d-flex justify-content-center">
    <div class="mt-3">
      <button class="mx-5" (click)="goBack()" type="button" mat-raised-button>
        {{ "Cancel" | translate }}
      </button>
    </div>
  </div>
</form>
