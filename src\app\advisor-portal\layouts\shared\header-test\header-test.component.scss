@import "/src/assets/styles/variables";

// header {
//   height: 100px;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   padding: 10px 32px;
//   width: 100%;

//   .language{
//     .dropdown-toggle {
//       border: none;
//       padding: 0;
//       height: 40px;
//       display: flex;
//       align-items: center;
//       justify-content: space-between;
//       img {
//         margin-right: 4px;
//       }
//       &::after {
//         content: "";
//         width: 24px;
//         height: 24px;
//         background: url("/assets/img/utils/angle_down_purple.svg") center no-repeat;
//         border: none;
//       }
//       &:focus {
//         outline: 0;
//         box-shadow: none;
//         border: none;
//       }
//       &:hover, &:focus-within, &:active, &:focus-visible, &:not(:focus){
//         background: none !important;
//         color: $color_1;
//       }
//     }
//   }
//   .user {
//     display: inline-flex;
//     align-items: center;
//     justify-content: end;
//     .country {
//       margin-right: 8px;
//       height: 55px;
//       display: inline-flex;
//       align-items: center;
//       padding-right: 10px;
//       border-right: 1px solid $border_1;
//     }
//     .profile {
//       height: 40px;
//       border-radius: 20px;
//       margin-left: 16px;
//       padding: 8px;
//       display: inline-flex;
//       justify-content: end;
//       align-items: center;
//       text-decoration: none;
//       .hover {
//         width: 0;
//         overflow: hidden;
//         height: 40px;
//         color: $color_2;
//         display: inline-flex;
//         flex-wrap: wrap;
//         justify-content: flex-end;
//         align-content: center;
//         align-items: center;
//         p {
//           color: $color_2 !important;
//           width: 100%;
//           text-align: right;
//           margin-bottom: 0;
//         }
//       }
//       &:hover {
//         background: $color_1;
//         flex-direction: row-reverse;
//         .ico {
//           margin-left: 8px;
//           img {
//             filter: brightness(100);
//           }
//         }
//         .hover {
//           width: auto;
//         }
//       }
//     }
//   }
// }

// @media (max-width: 766px) {
//   header{
//     visibility: hidden;
//   }
// }

header {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 32px;
  width: 100%;
  border-bottom: 1px solid gainsboro;
  .language {
    .dropdown{
      border-right: 1px, solid gainsboro;
    }
    .dropdown-toggle {
      border: none;
      padding: 0;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      img {
        margin-right: 4px;
      }
      &::after {
        content: "";
        width: 24px;
        height: 24px;
        background: url("/assets/img/utils/angle_down_purple.svg") center
          no-repeat;
        border: none;
      }
      &:focus {
        outline: 0;
        box-shadow: none;
        border: none;
      }
      &:hover,
      &:focus-within,
      &:active,
      &:focus-visible,
      &:not(:focus) {
        background: none !important;
        color: $color_1;
      }
    }
  }

}

@media (max-width: 766px) {
  header{
    visibility: hidden;
  }
}
