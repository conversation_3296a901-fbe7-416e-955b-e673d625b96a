.custom-tab-group {
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 16px;
}

.mat-tab-label {
    font-weight: 500;
    font-size: 16px;
    text-transform: none;
    /* Evitar mayúsculas automáticas */
    color: #333;
}

.mat-tab-label.mat-tab-label-active {
    color: #000;
    font-weight: bold;
    border-bottom: 2px solid #000;
    /* Línea activa */
}

.mat-tab-label:hover {
    color: #555;
}

.tab-content {
    padding: 16px;
    font-family: 'Arial', sans-serif;
}

.mat-tab-group {
    background: white;
}

.mat-tab-body-wrapper {
    padding-top: 10px;
}

.mat-tab-group {
    width: 100%;
}

::ng-deep .mat-mdc-tab-body-content{
    overflow-y: hidden !important;
}
