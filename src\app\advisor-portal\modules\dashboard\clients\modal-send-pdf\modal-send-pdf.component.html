<h2 mat-dialog-title>
    <button mat-icon-button class="close-button" (click)="close()">
      <mat-icon>close</mat-icon>
    </button>
  </h2>

  <mat-dialog-content>

    <div *ngIf="data.isSend" class="row">

        <div class="col-md-12 text-center">
            <strong><h5 style="font-weight: 800;">{{ 'Client.Product.VerifyTheRegisteredEmailAddress' | translate }}</h5></strong>
            <p>{{ 'Client.Product.WeWillSendTheFormToTheseAddresses' | translate }}</p>

            <p>* {{data.email}}</p>
        </div>

        <div class="col-md-12 text-center mt-2">
            <button style="background-color: black; color: white;" mat-raised-button (click)="applyFilters()">{{ 'Quotation.SendEmailQuote.SendButton' | translate }}</button>
        </div>

    </div>

    <div *ngIf="data.isConfirmation" class="row">
        <div class="col-md-12 text-center">
          <mat-icon class="large-icon text-center">check_circle</mat-icon>
          <strong>
            <h5 style="font-weight: 800;">
                {{ 'Client.Product.PolicyCoverSuccessfullySent' | translate }}
            </h5>
          </strong>
          <p>{{ 'Client.Product.ItWasSentToTheMail' | translate }}: {{data.email}}</p>
        </div>
        <div class="col-md-12 text-center mt-2">
          <button style="background-color: black; color: white;" mat-raised-button (click)="close()">
            {{ 'Continue' | translate }}
          </button>
        </div>
      </div>
      
      
  </mat-dialog-content>