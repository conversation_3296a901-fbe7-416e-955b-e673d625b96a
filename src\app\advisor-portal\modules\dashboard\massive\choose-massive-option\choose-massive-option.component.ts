import { Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChooseCountryAndCompanyComponent } from 'src/app/shared/components/choose-country-and-company/choose-country-and-company.component';
import { Subscription } from 'rxjs';
import { SettingService } from 'src/app/shared/services/setting/setting.service';
import { Router } from '@angular/router';
import { CustomRouterService } from 'src/app/shared/services/custom-router/custom-router.service';
import {
  LangChangeEvent,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { OptionListMassiveModel } from 'src/app/shared/models/massive';

@Component({
  selector: 'app-choose-massive-option',
  standalone: true,
  imports: [
    CommonModule,
    ChooseCountryAndCompanyComponent,
    TranslateModule,
    MatIconModule,
  ],
  templateUrl: './choose-massive-option.component.html',
  styleUrls: ['./choose-massive-option.component.scss'],
})
export class ChooseMassiveOptionComponent implements OnInit, OnDestroy {
  private _settingCountryAndCompanySubscription?: Subscription;
  idBusinessByCountry: number = 0;
  optionList: OptionListMassiveModel[] = [
    {
      name: this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.BulkCreationOfTasks'
      ),
      icon: 'new_label',
      id: 1,
      bActive: true,
    },
    {
      name: this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.MassCreationOfPolicies'
      ),
      icon: 'shield',
      id: 2,
      bActive: true,
    },
    {
      name: this._translateService.instant('RenewalPolicy.Title'),
      icon: 'cached',
      id: 3,
      bActive: true,
    },
    {
      name: this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.UploadingDocuments'
      ),
      icon: 'attach_file',
      id: 4,
      bActive: true,
    },
    {
      name: this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.StateManagement'
      ),
      icon: 'open_in_new',
      id: 5,
      bActive: true,
    },
    // {
    //   name: this._translateService.instant(
    //     'BulkUpload.Massives.MenuOptions.LoadingClients'
    //   ),
    //   icon: 'person_add',
    //   id: 6,
    //   bActive: false,
    // },
    {
      name: this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.LoadViewer'
      ),
      icon: 'upload_file',
      id: 6,
      bActive: true,
    },
  ];
  constructor(
    private _settingService: SettingService,
    private _router: Router,
    private _customeRouter: CustomRouterService,
    private _translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.getSettingCountryAndCompanySubscription();
    this._translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      //Traducción del array de opciones.
      this.optionList[0].name = this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.BulkCreationOfTasks'
      );
      this.optionList[1].name = this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.MassCreationOfPolicies'
      );
      this.optionList[2].name = this._translateService.instant(
        'RenewalPolicy.Title'
      );
      this.optionList[3].name = this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.UploadingDocuments'
      );
      this.optionList[4].name = this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.StateManagement'
      );
      this.optionList[5].name = this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.LoadingClients'
      );
      this.optionList[6].name = this._translateService.instant(
        'BulkUpload.Massives.MenuOptions.LoadViewer'
      );
    });
  }

  getSettingCountryAndCompanySubscription() {
    this._settingCountryAndCompanySubscription =
      this._settingService.currentSettingCountryAndCompany.subscribe(
        (response) => {
          if (this._router.url == response.currentModule) {
            if (!(Object.keys(response).length === 0)) {
              this.idBusinessByCountry =
                response.enterprise.pkIIdBusinessByCountry;
            }
          }
        }
      );
  }

  goToOption(optionId: number) {
    switch (optionId) {
      case 1:
        this._customeRouter.navigate([
          `dashboard/massive/mass-creation-tasks/${this.idBusinessByCountry}`,
        ]);
        break;
      case 2:
        this._customeRouter.navigate([
          `dashboard/massive/mass-creation-policy/${this.idBusinessByCountry}`,
        ]);
        break;
      case 3:
        this._customeRouter.navigate([
          `dashboard/massive/mass-renewal-policy/${this.idBusinessByCountry}`,
        ]);
        break;
      case 4:
        this._customeRouter.navigate([
          `dashboard/massive/mass-upload-documents/${this.idBusinessByCountry}`,
        ]);
        break;
      case 5:
        this._customeRouter.navigate([
          `dashboard/massive/mass-creation-state/${this.idBusinessByCountry}`,
        ]);
        break;
      case 6:
        this._customeRouter.navigate([
          `dashboard/massive/load-viewer/${this.idBusinessByCountry}`,
        ]);
        break;
      default:
        break;
    }
  }

  ngOnDestroy() {
    this._settingCountryAndCompanySubscription?.unsubscribe();
  }
}
